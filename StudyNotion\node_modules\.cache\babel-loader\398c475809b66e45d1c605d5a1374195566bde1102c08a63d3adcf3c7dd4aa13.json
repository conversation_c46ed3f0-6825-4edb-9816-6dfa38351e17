{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\temp\\\\StudyNotion\\\\src\\\\components\\\\core\\\\Dashboard\\\\AddCourse\\\\Upload.jsx\",\n  _s = $RefreshSig$();\nimport { useEffect, useRef, useState } from \"react\";\nimport { useDropzone } from \"react-dropzone\";\nimport { FiUploadCloud } from \"react-icons/fi\";\nimport { useSelector } from \"react-redux\";\nimport \"video-react/dist/video-react.css\";\nimport { Player } from \"video-react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function Upload(_ref) {\n  _s();\n  let {\n    name,\n    label,\n    register,\n    setValue,\n    errors,\n    video = false,\n    viewData = null,\n    editData = null\n  } = _ref;\n  const {\n    course\n  } = useSelector(state => state.course);\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [previewSource, setPreviewSource] = useState(viewData ? viewData : editData ? editData : \"\");\n  const inputRef = useRef(null);\n  const onDrop = acceptedFiles => {\n    const file = acceptedFiles[0];\n    if (file) {\n      previewFile(file);\n      setSelectedFile(file);\n    }\n  };\n  const {\n    getRootProps,\n    getInputProps,\n    isDragActive\n  } = useDropzone({\n    accept: !video ? {\n      \"image/*\": [\".jpeg\", \".jpg\", \".png\"]\n    } : {\n      \"video/*\": [\".mp4\"]\n    },\n    onDrop\n  });\n  const previewFile = file => {\n    // console.log(file)\n    const reader = new FileReader();\n    reader.readAsDataURL(file);\n    reader.onloadend = () => {\n      setPreviewSource(reader.result);\n    };\n  };\n  useEffect(() => {\n    register(name, {\n      required: true\n    });\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [register]);\n  useEffect(() => {\n    setValue(name, selectedFile);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [selectedFile, setValue]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col space-y-2\",\n    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n      className: \"text-sm text-richblack-5\",\n      htmlFor: name,\n      children: [label, \" \", !viewData && /*#__PURE__*/_jsxDEV(\"sup\", {\n        className: \"text-pink-200\",\n        children: \"*\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 31\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `${isDragActive ? \"bg-richblack-600\" : \"bg-richblack-700\"} flex min-h-[250px] cursor-pointer items-center justify-center rounded-md border-2 border-dotted border-richblack-500`,\n      children: previewSource ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex w-full flex-col p-6\",\n        children: [!video ? /*#__PURE__*/_jsxDEV(\"img\", {\n          src: previewSource,\n          alt: \"Preview\",\n          className: \"h-full w-full rounded-md object-cover\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(Player, {\n          aspectRatio: \"16:9\",\n          playsInline: true,\n          src: previewSource\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 15\n        }, this), !viewData && /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: () => {\n            setPreviewSource(\"\");\n            setSelectedFile(null);\n            setValue(name, null);\n          },\n          className: \"mt-3 text-richblack-400 underline\",\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex w-full flex-col items-center p-6\",\n        ...getRootProps(),\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          ...getInputProps(),\n          ref: inputRef\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid aspect-square w-14 place-items-center rounded-full bg-pure-greys-800\",\n          children: /*#__PURE__*/_jsxDEV(FiUploadCloud, {\n            className: \"text-2xl text-yellow-50\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-2 max-w-[200px] text-center text-sm text-richblack-200\",\n          children: [\"Drag and drop an \", !video ? \"image\" : \"video\", \", or click to\", \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-semibold text-yellow-50\",\n            children: \"Browse\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 15\n          }, this), \" a file\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"mt-10 flex list-disc justify-between space-x-12 text-center  text-xs text-richblack-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Aspect ratio 16:9\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Recommended size 1024x576\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 7\n    }, this), errors[name] && /*#__PURE__*/_jsxDEV(\"span\", {\n      className: \"ml-2 text-xs tracking-wide text-pink-200\",\n      children: [label, \" is required\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 61,\n    columnNumber: 5\n  }, this);\n}\n_s(Upload, \"6dX6hhprPnR+VJ0t5JKqFIDPTXY=\", false, function () {\n  return [useSelector, useDropzone];\n});\n_c = Upload;\nvar _c;\n$RefreshReg$(_c, \"Upload\");", "map": {"version": 3, "names": ["useEffect", "useRef", "useState", "useDropzone", "FiUploadCloud", "useSelector", "Player", "jsxDEV", "_jsxDEV", "Upload", "_ref", "_s", "name", "label", "register", "setValue", "errors", "video", "viewData", "editData", "course", "state", "selectedFile", "setSelectedFile", "previewSource", "setPreviewSource", "inputRef", "onDrop", "acceptedFiles", "file", "previewFile", "getRootProps", "getInputProps", "isDragActive", "accept", "reader", "FileReader", "readAsDataURL", "onloadend", "result", "required", "className", "children", "htmlFor", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "src", "alt", "aspectRatio", "playsInline", "type", "onClick", "ref", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/src/components/core/Dashboard/AddCourse/Upload.jsx"], "sourcesContent": ["import { useEffect, useRef, useState } from \"react\"\r\nimport { useDropzone } from \"react-dropzone\"\r\nimport { FiUploadCloud } from \"react-icons/fi\"\r\nimport { useSelector } from \"react-redux\"\r\n\r\nimport \"video-react/dist/video-react.css\"\r\nimport { Player } from \"video-react\"\r\n\r\nexport default function Upload({\r\n  name,\r\n  label,\r\n  register,\r\n  setValue,\r\n  errors,\r\n  video = false,\r\n  viewData = null,\r\n  editData = null,\r\n}) {\r\n  const { course } = useSelector((state) => state.course)\r\n  const [selectedFile, setSelectedFile] = useState(null)\r\n  const [previewSource, setPreviewSource] = useState(\r\n    viewData ? viewData : editData ? editData : \"\"\r\n  )\r\n  const inputRef = useRef(null)\r\n\r\n  const onDrop = (acceptedFiles) => {\r\n    const file = acceptedFiles[0]\r\n    if (file) {\r\n      previewFile(file)\r\n      setSelectedFile(file)\r\n    }\r\n  }\r\n\r\n  const { getRootProps, getInputProps, isDragActive } = useDropzone({\r\n    accept: !video\r\n      ? { \"image/*\": [\".jpeg\", \".jpg\", \".png\"] }\r\n      : { \"video/*\": [\".mp4\"] },\r\n    onDrop,\r\n  })\r\n\r\n  const previewFile = (file) => {\r\n    // console.log(file)\r\n    const reader = new FileReader()\r\n    reader.readAsDataURL(file)\r\n    reader.onloadend = () => {\r\n      setPreviewSource(reader.result)\r\n    }\r\n  }\r\n\r\n  useEffect(() => {\r\n    register(name, { required: true })\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [register])\r\n\r\n  useEffect(() => {\r\n    setValue(name, selectedFile)\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [selectedFile, setValue])\r\n\r\n  return (\r\n    <div className=\"flex flex-col space-y-2\">\r\n      <label className=\"text-sm text-richblack-5\" htmlFor={name}>\r\n        {label} {!viewData && <sup className=\"text-pink-200\">*</sup>}\r\n      </label>\r\n      <div\r\n        className={`${\r\n          isDragActive ? \"bg-richblack-600\" : \"bg-richblack-700\"\r\n        } flex min-h-[250px] cursor-pointer items-center justify-center rounded-md border-2 border-dotted border-richblack-500`}\r\n      >\r\n        {previewSource ? (\r\n          <div className=\"flex w-full flex-col p-6\">\r\n            {!video ? (\r\n              <img\r\n                src={previewSource}\r\n                alt=\"Preview\"\r\n                className=\"h-full w-full rounded-md object-cover\"\r\n              />\r\n            ) : (\r\n              <Player aspectRatio=\"16:9\" playsInline src={previewSource} />\r\n            )}\r\n            {!viewData && (\r\n              <button\r\n                type=\"button\"\r\n                onClick={() => {\r\n                  setPreviewSource(\"\")\r\n                  setSelectedFile(null)\r\n                  setValue(name, null)\r\n                }}\r\n                className=\"mt-3 text-richblack-400 underline\"\r\n              >\r\n                Cancel\r\n              </button>\r\n            )}\r\n          </div>\r\n        ) : (\r\n          <div\r\n            className=\"flex w-full flex-col items-center p-6\"\r\n            {...getRootProps()}\r\n          >\r\n            <input {...getInputProps()} ref={inputRef} />\r\n            <div className=\"grid aspect-square w-14 place-items-center rounded-full bg-pure-greys-800\">\r\n              <FiUploadCloud className=\"text-2xl text-yellow-50\" />\r\n            </div>\r\n            <p className=\"mt-2 max-w-[200px] text-center text-sm text-richblack-200\">\r\n              Drag and drop an {!video ? \"image\" : \"video\"}, or click to{\" \"}\r\n              <span className=\"font-semibold text-yellow-50\">Browse</span> a\r\n              file\r\n            </p>\r\n            <ul className=\"mt-10 flex list-disc justify-between space-x-12 text-center  text-xs text-richblack-200\">\r\n              <li>Aspect ratio 16:9</li>\r\n              <li>Recommended size 1024x576</li>\r\n            </ul>\r\n          </div>\r\n        )}\r\n      </div>\r\n      {errors[name] && (\r\n        <span className=\"ml-2 text-xs tracking-wide text-pink-200\">\r\n          {label} is required\r\n        </span>\r\n      )}\r\n    </div>\r\n  )\r\n}"], "mappings": ";;AAAA,SAASA,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACnD,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,aAAa,QAAQ,gBAAgB;AAC9C,SAASC,WAAW,QAAQ,aAAa;AAEzC,OAAO,kCAAkC;AACzC,SAASC,MAAM,QAAQ,aAAa;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEpC,eAAe,SAASC,MAAMA,CAAAC,IAAA,EAS3B;EAAAC,EAAA;EAAA,IAT4B;IAC7BC,IAAI;IACJC,KAAK;IACLC,QAAQ;IACRC,QAAQ;IACRC,MAAM;IACNC,KAAK,GAAG,KAAK;IACbC,QAAQ,GAAG,IAAI;IACfC,QAAQ,GAAG;EACb,CAAC,GAAAT,IAAA;EACC,MAAM;IAAEU;EAAO,CAAC,GAAGf,WAAW,CAAEgB,KAAK,IAAKA,KAAK,CAACD,MAAM,CAAC;EACvD,MAAM,CAACE,YAAY,EAAEC,eAAe,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACsB,aAAa,EAAEC,gBAAgB,CAAC,GAAGvB,QAAQ,CAChDgB,QAAQ,GAAGA,QAAQ,GAAGC,QAAQ,GAAGA,QAAQ,GAAG,EAAE,CAC/C;EACD,MAAMO,QAAQ,GAAGzB,MAAM,CAAC,IAAI,CAAC;EAE7B,MAAM0B,MAAM,GAAIC,aAAa,IAAK;IAChC,MAAMC,IAAI,GAAGD,aAAa,CAAC,CAAC,CAAC;IAC7B,IAAIC,IAAI,EAAE;MACRC,WAAW,CAACD,IAAI,CAAC;MACjBN,eAAe,CAACM,IAAI,CAAC;IACvB;EACF,CAAC;EAED,MAAM;IAAEE,YAAY;IAAEC,aAAa;IAAEC;EAAa,CAAC,GAAG9B,WAAW,CAAC;IAChE+B,MAAM,EAAE,CAACjB,KAAK,GACV;MAAE,SAAS,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM;IAAE,CAAC,GACxC;MAAE,SAAS,EAAE,CAAC,MAAM;IAAE,CAAC;IAC3BU;EACF,CAAC,CAAC;EAEF,MAAMG,WAAW,GAAID,IAAI,IAAK;IAC5B;IACA,MAAMM,MAAM,GAAG,IAAIC,UAAU,EAAE;IAC/BD,MAAM,CAACE,aAAa,CAACR,IAAI,CAAC;IAC1BM,MAAM,CAACG,SAAS,GAAG,MAAM;MACvBb,gBAAgB,CAACU,MAAM,CAACI,MAAM,CAAC;IACjC,CAAC;EACH,CAAC;EAEDvC,SAAS,CAAC,MAAM;IACdc,QAAQ,CAACF,IAAI,EAAE;MAAE4B,QAAQ,EAAE;IAAK,CAAC,CAAC;IAClC;EACF,CAAC,EAAE,CAAC1B,QAAQ,CAAC,CAAC;EAEdd,SAAS,CAAC,MAAM;IACde,QAAQ,CAACH,IAAI,EAAEU,YAAY,CAAC;IAC5B;EACF,CAAC,EAAE,CAACA,YAAY,EAAEP,QAAQ,CAAC,CAAC;EAE5B,oBACEP,OAAA;IAAKiC,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBACtClC,OAAA;MAAOiC,SAAS,EAAC,0BAA0B;MAACE,OAAO,EAAE/B,IAAK;MAAA8B,QAAA,GACvD7B,KAAK,EAAC,GAAC,EAAC,CAACK,QAAQ,iBAAIV,OAAA;QAAKiC,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAAM;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACtD,eACRvC,OAAA;MACEiC,SAAS,EAAG,GACVR,YAAY,GAAG,kBAAkB,GAAG,kBACrC,uHAAuH;MAAAS,QAAA,EAEvHlB,aAAa,gBACZhB,OAAA;QAAKiC,SAAS,EAAC,0BAA0B;QAAAC,QAAA,GACtC,CAACzB,KAAK,gBACLT,OAAA;UACEwC,GAAG,EAAExB,aAAc;UACnByB,GAAG,EAAC,SAAS;UACbR,SAAS,EAAC;QAAuC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACjD,gBAEFvC,OAAA,CAACF,MAAM;UAAC4C,WAAW,EAAC,MAAM;UAACC,WAAW;UAACH,GAAG,EAAExB;QAAc;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAC3D,EACA,CAAC7B,QAAQ,iBACRV,OAAA;UACE4C,IAAI,EAAC,QAAQ;UACbC,OAAO,EAAEA,CAAA,KAAM;YACb5B,gBAAgB,CAAC,EAAE,CAAC;YACpBF,eAAe,CAAC,IAAI,CAAC;YACrBR,QAAQ,CAACH,IAAI,EAAE,IAAI,CAAC;UACtB,CAAE;UACF6B,SAAS,EAAC,mCAAmC;UAAAC,QAAA,EAC9C;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACG,gBAENvC,OAAA;QACEiC,SAAS,EAAC,uCAAuC;QAAA,GAC7CV,YAAY,EAAE;QAAAW,QAAA,gBAElBlC,OAAA;UAAA,GAAWwB,aAAa,EAAE;UAAEsB,GAAG,EAAE5B;QAAS;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAG,eAC7CvC,OAAA;UAAKiC,SAAS,EAAC,2EAA2E;UAAAC,QAAA,eACxFlC,OAAA,CAACJ,aAAa;YAACqC,SAAS,EAAC;UAAyB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAG;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACjD,eACNvC,OAAA;UAAGiC,SAAS,EAAC,2DAA2D;UAAAC,QAAA,GAAC,mBACtD,EAAC,CAACzB,KAAK,GAAG,OAAO,GAAG,OAAO,EAAC,eAAa,EAAC,GAAG,eAC9DT,OAAA;YAAMiC,SAAS,EAAC,8BAA8B;YAAAC,QAAA,EAAC;UAAM;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAAO,WAE9D;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAI,eACJvC,OAAA;UAAIiC,SAAS,EAAC,yFAAyF;UAAAC,QAAA,gBACrGlC,OAAA;YAAAkC,QAAA,EAAI;UAAiB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAAK,eAC1BvC,OAAA;YAAAkC,QAAA,EAAI;UAAyB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAAK;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAC/B;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAER;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACG,EACL/B,MAAM,CAACJ,IAAI,CAAC,iBACXJ,OAAA;MAAMiC,SAAS,EAAC,0CAA0C;MAAAC,QAAA,GACvD7B,KAAK,EAAC,cACT;IAAA;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACD;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACG;AAEV;AAACpC,EAAA,CAlHuBF,MAAM;EAAA,QAUTJ,WAAW,EAewBF,WAAW;AAAA;AAAAoD,EAAA,GAzB3C9C,MAAM;AAAA,IAAA8C,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}