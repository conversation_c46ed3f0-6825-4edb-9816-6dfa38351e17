[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\index.js": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\App.js": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\reducer\\index.js": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\utils\\constants.js": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\pages\\Login.jsx": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\pages\\UpdatePassword.jsx": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\pages\\Signup.jsx": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\pages\\Home.jsx": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\pages\\Contact.jsx": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\pages\\VerifyEmail.jsx": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\pages\\Catalog.jsx": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\pages\\ForgotPassword.jsx": "12", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\pages\\CourseDetails.jsx": "13", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\pages\\About.jsx": "14", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\pages\\Dashboard.jsx": "15", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\pages\\Error.jsx": "16", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\pages\\ViewCourse.jsx": "17", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\common\\Navbar.jsx": "18", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Auth\\OpenRoute.jsx": "19", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Dashboard\\MyProfile.jsx": "20", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Auth\\PrivateRoute.jsx": "21", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Dashboard\\MyCourses.jsx": "22", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Dashboard\\EnrolledCourses.jsx": "23", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\ViewCourse\\VideoDetails.jsx": "24", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Dashboard\\InstructorDashboard\\Instructor.jsx": "25", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\slices\\profileSlice.js": "26", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\slices\\cartSlice.js": "27", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\slices\\courseSlice.js": "28", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\slices\\authSlice.js": "29", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\slices\\viewCourseSlice.js": "30", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Dashboard\\EditCourse\\index.js": "31", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Dashboard\\Settings\\index.jsx": "32", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Dashboard\\AddCourse\\index.jsx": "33", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Dashboard\\Cart\\index.jsx": "34", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\utils\\avgRating.js": "35", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\services\\formatDate.js": "36", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\services\\apiconnector.js": "37", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\services\\apis.js": "38", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\common\\ReviewSlider.jsx": "39", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\common\\Footer.jsx": "40", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\services\\operations\\authAPI.js": "41", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\services\\operations\\pageAndComponentData.js": "42", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\services\\operations\\courseDetailsAPI.js": "43", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\services\\operations\\studentFeaturesAPI.js": "44", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Auth\\Template.jsx": "45", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Course\\CourseDetailsCard.js": "46", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\HomePage\\InstructorSection.jsx": "47", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\HomePage\\Button.jsx": "48", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\common\\ConfirmationModal.jsx": "49", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\HomePage\\CodeBlocks.jsx": "50", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\common\\RatingStars.jsx": "51", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\HomePage\\ExploreMore.jsx": "52", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\utils\\dateFormatter.js": "53", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\ContactPage\\ContactForm.jsx": "54", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\ContactPage\\ContactDetails.jsx": "55", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\data\\navbar-links.js": "56", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\HomePage\\LearningLanguageSection.jsx": "57", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\HomePage\\TimelineSection.jsx": "58", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\HomePage\\HighlightText.jsx": "59", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Course\\CourseAccordionBar.jsx": "60", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Catalog\\CourseSlider.jsx": "61", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Catalog\\Course_Card.jsx": "62", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\ViewCourse\\CourseReviewModal.jsx": "63", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\ViewCourse\\VideoDetailsSidebar.jsx": "64", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\AboutPage\\Stats.jsx": "65", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\services\\operations\\profileAPI.js": "66", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Dashboard\\Sidebar.jsx": "67", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\AboutPage\\Quote.jsx": "68", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\AboutPage\\ContactFormSection.jsx": "69", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\AboutPage\\LearningGrid.jsx": "70", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\common\\IconBtn.jsx": "71", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Auth\\ProfileDropDown.jsx": "72", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Dashboard\\InstructorCourses\\CoursesTable.jsx": "73", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Dashboard\\InstructorDashboard\\InstructorChart.jsx": "74", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Dashboard\\AddCourse\\RenderSteps.jsx": "75", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Dashboard\\Settings\\EditProfile.jsx": "76", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Dashboard\\Settings\\UpdatePassword.jsx": "77", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Dashboard\\Settings\\ChangeProfilePicture.jsx": "78", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Dashboard\\Settings\\DeleteAccount.jsx": "79", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Dashboard\\Cart\\RenderTotalAmount.jsx": "80", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Dashboard\\Cart\\RenderCartCourses.jsx": "81", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\data\\homepage-explore.js": "82", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\data\\footer-links.js": "83", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\ContactPage\\ContactUsForm.jsx": "84", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Auth\\LoginForm.jsx": "85", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\HomePage\\CourseCard.jsx": "86", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Auth\\SignupForm.jsx": "87", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\data\\dashboard-links.js": "88", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\hooks\\useOnClickOutside.js": "89", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Course\\CourseSubSectionAccordion.jsx": "90", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\services\\operations\\SettingsAPI.js": "91", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Dashboard\\SidebarLink.jsx": "92", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Dashboard\\AddCourse\\CourseBuilder\\CourseBuilderForm.jsx": "93", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Dashboard\\AddCourse\\CourseInformation\\CourseInformationForm.jsx": "94", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\common\\Tab.jsx": "95", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Dashboard\\AddCourse\\PublishCourse\\index.jsx": "96", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Dashboard\\AddCourse\\CourseBuilder\\NestedView.jsx": "97", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Dashboard\\AddCourse\\Upload.jsx": "98", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Dashboard\\AddCourse\\CourseInformation\\RequirementField.jsx": "99", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Dashboard\\AddCourse\\CourseInformation\\ChipInput.jsx": "100", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Dashboard\\AddCourse\\CourseBuilder\\SubSectionModal.jsx": "101"}, {"size": 679, "mtime": 1753507468760, "results": "102", "hashOfConfig": "103"}, {"size": 4571, "mtime": 1753507468558, "results": "104", "hashOfConfig": "103"}, {"size": 523, "mtime": 1753507468782, "results": "105", "hashOfConfig": "103"}, {"size": 186, "mtime": 1753507468812, "results": "106", "hashOfConfig": "103"}, {"size": 401, "mtime": 1753507468773, "results": "107", "hashOfConfig": "103"}, {"size": 4392, "mtime": 1753507468776, "results": "108", "hashOfConfig": "103"}, {"size": 455, "mtime": 1753507468775, "results": "109", "hashOfConfig": "103"}, {"size": 7950, "mtime": 1753507468772, "results": "110", "hashOfConfig": "103"}, {"size": 1110, "mtime": 1753507468766, "results": "111", "hashOfConfig": "103"}, {"size": 3490, "mtime": 1753507468778, "results": "112", "hashOfConfig": "103"}, {"size": 5282, "mtime": 1753507468764, "results": "113", "hashOfConfig": "103"}, {"size": 2634, "mtime": 1753507468770, "results": "114", "hashOfConfig": "103"}, {"size": 10044, "mtime": 1753507468767, "results": "115", "hashOfConfig": "103"}, {"size": 6585, "mtime": 1753507468764, "results": "116", "hashOfConfig": "103"}, {"size": 849, "mtime": 1753507468769, "results": "117", "hashOfConfig": "103"}, {"size": 213, "mtime": 1753507468770, "results": "118", "hashOfConfig": "103"}, {"size": 1867, "mtime": 1753507468779, "results": "119", "hashOfConfig": "103"}, {"size": 6287, "mtime": 1753507468649, "results": "120", "hashOfConfig": "103"}, {"size": 396, "mtime": 1753507468663, "results": "121", "hashOfConfig": "103"}, {"size": 4511, "mtime": 1753507468717, "results": "122", "hashOfConfig": "103"}, {"size": 355, "mtime": 1753507468664, "results": "123", "hashOfConfig": "103"}, {"size": 1268, "mtime": 1753507468716, "results": "124", "hashOfConfig": "103"}, {"size": 3504, "mtime": 1753507468708, "results": "125", "hashOfConfig": "103"}, {"size": 8353, "mtime": 1753507468745, "results": "126", "hashOfConfig": "103"}, {"size": 5802, "mtime": 1753507468713, "results": "127", "hashOfConfig": "103"}, {"size": 588, "mtime": 1753507468806, "results": "128", "hashOfConfig": "103"}, {"size": 2434, "mtime": 1753507468802, "results": "129", "hashOfConfig": "103"}, {"size": 891, "mtime": 1753507468804, "results": "130", "hashOfConfig": "103"}, {"size": 668, "mtime": 1753507468801, "results": "131", "hashOfConfig": "103"}, {"size": 1050, "mtime": 1753507468807, "results": "132", "hashOfConfig": "103"}, {"size": 1620, "mtime": 1753507468706, "results": "133", "hashOfConfig": "103"}, {"size": 591, "mtime": 1753507468726, "results": "134", "hashOfConfig": "103"}, {"size": 1551, "mtime": 1753507468701, "results": "135", "hashOfConfig": "103"}, {"size": 849, "mtime": 1753507468704, "results": "136", "hashOfConfig": "103"}, {"size": 404, "mtime": 1753507468811, "results": "137", "hashOfConfig": "103"}, {"size": 511, "mtime": 1753507468787, "results": "138", "hashOfConfig": "103"}, {"size": 371, "mtime": 1753507468785, "results": "139", "hashOfConfig": "103"}, {"size": 2741, "mtime": 1753507468786, "results": "140", "hashOfConfig": "103"}, {"size": 3736, "mtime": 1753507468651, "results": "141", "hashOfConfig": "103"}, {"size": 6892, "mtime": 1755334986189, "results": "142", "hashOfConfig": "103"}, {"size": 4920, "mtime": 1753507468791, "results": "143", "hashOfConfig": "103"}, {"size": 770, "mtime": 1753507468795, "results": "144", "hashOfConfig": "103"}, {"size": 11960, "mtime": 1753507468793, "results": "145", "hashOfConfig": "103"}, {"size": 4182, "mtime": 1753507468798, "results": "146", "hashOfConfig": "103"}, {"size": 1834, "mtime": 1753507468667, "results": "147", "hashOfConfig": "103"}, {"size": 3905, "mtime": 1753507468675, "results": "148", "hashOfConfig": "103"}, {"size": 1505, "mtime": 1753507468737, "results": "149", "hashOfConfig": "103"}, {"size": 547, "mtime": 1753507468731, "results": "150", "hashOfConfig": "103"}, {"size": 1036, "mtime": 1753507468644, "results": "151", "hashOfConfig": "103"}, {"size": 2242, "mtime": 1753507468733, "results": "152", "hashOfConfig": "103"}, {"size": 1098, "mtime": 1753507468650, "results": "153", "hashOfConfig": "103"}, {"size": 2666, "mtime": 1753507468735, "results": "154", "hashOfConfig": "103"}, {"size": 179, "mtime": 1753507468813, "results": "155", "hashOfConfig": "103"}, {"size": 616, "mtime": 1753507468639, "results": "156", "hashOfConfig": "103"}, {"size": 1544, "mtime": 1753507468639, "results": "157", "hashOfConfig": "103"}, {"size": 257, "mtime": 1753507468755, "results": "158", "hashOfConfig": "103"}, {"size": 1855, "mtime": 1753507468739, "results": "159", "hashOfConfig": "103"}, {"size": 3341, "mtime": 1753507468741, "results": "160", "hashOfConfig": "103"}, {"size": 283, "mtime": 1753507468737, "results": "161", "hashOfConfig": "103"}, {"size": 2015, "mtime": 1753507468674, "results": "162", "hashOfConfig": "103"}, {"size": 1009, "mtime": 1753507468669, "results": "163", "hashOfConfig": "103"}, {"size": 1601, "mtime": 1753507468672, "results": "164", "hashOfConfig": "103"}, {"size": 4082, "mtime": 1753507468744, "results": "165", "hashOfConfig": "103"}, {"size": 5198, "mtime": 1753507468746, "results": "166", "hashOfConfig": "103"}, {"size": 1028, "mtime": 1753507468661, "results": "167", "hashOfConfig": "103"}, {"size": 2783, "mtime": 1753507468796, "results": "168", "hashOfConfig": "103"}, {"size": 2611, "mtime": 1753507468727, "results": "169", "hashOfConfig": "103"}, {"size": 833, "mtime": 1753507468659, "results": "170", "hashOfConfig": "103"}, {"size": 524, "mtime": 1753507468656, "results": "171", "hashOfConfig": "103"}, {"size": 3124, "mtime": 1753507468658, "results": "172", "hashOfConfig": "103"}, {"size": 718, "mtime": 1753507468647, "results": "173", "hashOfConfig": "103"}, {"size": 2174, "mtime": 1753507468666, "results": "174", "hashOfConfig": "103"}, {"size": 6769, "mtime": 1753507468709, "results": "175", "hashOfConfig": "103"}, {"size": 2765, "mtime": 1753507468715, "results": "176", "hashOfConfig": "103"}, {"size": 2672, "mtime": 1753507468699, "results": "177", "hashOfConfig": "103"}, {"size": 7237, "mtime": 1753507468723, "results": "178", "hashOfConfig": "103"}, {"size": 4138, "mtime": 1753507468725, "results": "179", "hashOfConfig": "103"}, {"size": 3121, "mtime": 1753507468720, "results": "180", "hashOfConfig": "103"}, {"size": 1659, "mtime": 1753507468721, "results": "181", "hashOfConfig": "103"}, {"size": 1103, "mtime": 1753507468704, "results": "182", "hashOfConfig": "103"}, {"size": 2535, "mtime": 1753507468703, "results": "183", "hashOfConfig": "103"}, {"size": 4859, "mtime": 1753507468754, "results": "184", "hashOfConfig": "103"}, {"size": 2193, "mtime": 1753507468752, "results": "185", "hashOfConfig": "103"}, {"size": 5625, "mtime": 1753507468641, "results": "186", "hashOfConfig": "103"}, {"size": 2942, "mtime": 1753507468662, "results": "187", "hashOfConfig": "103"}, {"size": 1550, "mtime": 1753507468733, "results": "188", "hashOfConfig": "103"}, {"size": 6987, "mtime": 1753507468667, "results": "189", "hashOfConfig": "103"}, {"size": 916, "mtime": 1753507468751, "results": "190", "hashOfConfig": "103"}, {"size": 1149, "mtime": 1753507468757, "results": "191", "hashOfConfig": "103"}, {"size": 543, "mtime": 1753507468677, "results": "192", "hashOfConfig": "103"}, {"size": 3670, "mtime": 1753507468790, "results": "193", "hashOfConfig": "103"}, {"size": 1185, "mtime": 1753507468729, "results": "194", "hashOfConfig": "103"}, {"size": 4757, "mtime": 1753507468683, "results": "195", "hashOfConfig": "103"}, {"size": 11394, "mtime": 1753507468690, "results": "196", "hashOfConfig": "103"}, {"size": 745, "mtime": 1753507468652, "results": "197", "hashOfConfig": "103"}, {"size": 3347, "mtime": 1753507468697, "results": "198", "hashOfConfig": "103"}, {"size": 7075, "mtime": 1753507468684, "results": "199", "hashOfConfig": "103"}, {"size": 3941, "mtime": 1753507468700, "results": "200", "hashOfConfig": "103"}, {"size": 2573, "mtime": 1753507468692, "results": "201", "hashOfConfig": "103"}, {"size": 3528, "mtime": 1753507468688, "results": "202", "hashOfConfig": "103"}, {"size": 7155, "mtime": 1753507468686, "results": "203", "hashOfConfig": "103"}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1arclm7", {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "447", "messages": "448", "suppressedMessages": "449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "450", "messages": "451", "suppressedMessages": "452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "453", "messages": "454", "suppressedMessages": "455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "456", "messages": "457", "suppressedMessages": "458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "459", "messages": "460", "suppressedMessages": "461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "462", "messages": "463", "suppressedMessages": "464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "465", "messages": "466", "suppressedMessages": "467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "468", "messages": "469", "suppressedMessages": "470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "471", "messages": "472", "suppressedMessages": "473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "474", "messages": "475", "suppressedMessages": "476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "477", "messages": "478", "suppressedMessages": "479", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "480", "messages": "481", "suppressedMessages": "482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "483", "messages": "484", "suppressedMessages": "485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "486", "messages": "487", "suppressedMessages": "488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "489", "messages": "490", "suppressedMessages": "491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "492", "messages": "493", "suppressedMessages": "494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "495", "messages": "496", "suppressedMessages": "497", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "498", "messages": "499", "suppressedMessages": "500", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "501", "messages": "502", "suppressedMessages": "503", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "504", "messages": "505", "suppressedMessages": "506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\App.js", ["507", "508"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\reducer\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\utils\\constants.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\pages\\Login.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\pages\\UpdatePassword.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\pages\\Signup.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\pages\\Home.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\pages\\Contact.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\pages\\VerifyEmail.jsx", [], ["509"], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\pages\\Catalog.jsx", ["510"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\pages\\ForgotPassword.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\pages\\CourseDetails.jsx", ["511", "512"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\pages\\About.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\pages\\Dashboard.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\pages\\Error.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\pages\\ViewCourse.jsx", [], ["513"], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\common\\Navbar.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Auth\\OpenRoute.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Dashboard\\MyProfile.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Auth\\PrivateRoute.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Dashboard\\MyCourses.jsx", [], ["514"], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Dashboard\\EnrolledCourses.jsx", ["515", "516"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\ViewCourse\\VideoDetails.jsx", ["517"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Dashboard\\InstructorDashboard\\Instructor.jsx", ["518"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\slices\\profileSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\slices\\cartSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\slices\\courseSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\slices\\authSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\slices\\viewCourseSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Dashboard\\EditCourse\\index.js", ["519"], ["520"], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Dashboard\\Settings\\index.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Dashboard\\AddCourse\\index.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Dashboard\\Cart\\index.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\utils\\avgRating.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\services\\formatDate.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\services\\apiconnector.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\services\\apis.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\common\\ReviewSlider.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\common\\Footer.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\services\\operations\\authAPI.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\services\\operations\\pageAndComponentData.js", ["521"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\services\\operations\\courseDetailsAPI.js", ["522"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\services\\operations\\studentFeaturesAPI.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Auth\\Template.jsx", ["523"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Course\\CourseDetailsCard.js", ["524"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\HomePage\\InstructorSection.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\HomePage\\Button.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\common\\ConfirmationModal.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\HomePage\\CodeBlocks.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\common\\RatingStars.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\HomePage\\ExploreMore.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\utils\\dateFormatter.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\ContactPage\\ContactForm.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\ContactPage\\ContactDetails.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\data\\navbar-links.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\HomePage\\LearningLanguageSection.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\HomePage\\TimelineSection.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\HomePage\\HighlightText.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Course\\CourseAccordionBar.jsx", ["525"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Catalog\\CourseSlider.jsx", ["526", "527", "528"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Catalog\\Course_Card.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\ViewCourse\\CourseReviewModal.jsx", [], ["529"], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\ViewCourse\\VideoDetailsSidebar.jsx", [], ["530"], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\AboutPage\\Stats.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\services\\operations\\profileAPI.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Dashboard\\Sidebar.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\AboutPage\\Quote.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\AboutPage\\ContactFormSection.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\AboutPage\\LearningGrid.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\common\\IconBtn.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Auth\\ProfileDropDown.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Dashboard\\InstructorCourses\\CoursesTable.jsx", ["531", "532", "533"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Dashboard\\InstructorDashboard\\InstructorChart.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Dashboard\\AddCourse\\RenderSteps.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Dashboard\\Settings\\EditProfile.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Dashboard\\Settings\\UpdatePassword.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Dashboard\\Settings\\ChangeProfilePicture.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Dashboard\\Settings\\DeleteAccount.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Dashboard\\Cart\\RenderTotalAmount.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Dashboard\\Cart\\RenderCartCourses.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\data\\homepage-explore.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\data\\footer-links.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\ContactPage\\ContactUsForm.jsx", ["534"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Auth\\LoginForm.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\HomePage\\CourseCard.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Auth\\SignupForm.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\data\\dashboard-links.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\hooks\\useOnClickOutside.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Course\\CourseSubSectionAccordion.jsx", ["535", "536", "537", "538"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\services\\operations\\SettingsAPI.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Dashboard\\SidebarLink.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Dashboard\\AddCourse\\CourseBuilder\\CourseBuilderForm.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Dashboard\\AddCourse\\CourseInformation\\CourseInformationForm.jsx", [], ["539"], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\common\\Tab.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Dashboard\\AddCourse\\PublishCourse\\index.jsx", ["540"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Dashboard\\AddCourse\\CourseBuilder\\NestedView.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Dashboard\\AddCourse\\Upload.jsx", ["541"], ["542", "543"], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Dashboard\\AddCourse\\CourseInformation\\RequirementField.jsx", [], ["544", "545"], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Dashboard\\AddCourse\\CourseInformation\\ChipInput.jsx", [], ["546", "547"], "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\src\\components\\core\\Dashboard\\AddCourse\\CourseBuilder\\SubSectionModal.jsx", ["548"], [], {"ruleId": "549", "severity": 1, "message": "550", "line": 34, "column": 9, "nodeType": "551", "messageId": "552", "endLine": 34, "endColumn": 17}, {"ruleId": "549", "severity": 1, "message": "553", "line": 35, "column": 9, "nodeType": "551", "messageId": "552", "endLine": 35, "endColumn": 17}, {"ruleId": "554", "severity": 1, "message": "555", "line": 22, "column": 6, "nodeType": "556", "endLine": 22, "endColumn": 8, "suggestions": "557", "suppressions": "558"}, {"ruleId": "559", "severity": 1, "message": "560", "line": 131, "column": 21, "nodeType": "561", "messageId": "562", "endLine": 131, "endColumn": 81}, {"ruleId": "563", "severity": 1, "message": "564", "line": 65, "column": 36, "nodeType": "565", "messageId": "566", "endLine": 65, "endColumn": 38}, {"ruleId": "549", "severity": 1, "message": "567", "line": 91, "column": 10, "nodeType": "551", "messageId": "552", "endLine": 91, "endColumn": 19}, {"ruleId": "554", "severity": 1, "message": "568", "line": 35, "column": 6, "nodeType": "556", "endLine": 35, "endColumn": 8, "suggestions": "569", "suppressions": "570"}, {"ruleId": "554", "severity": 1, "message": "571", "line": 24, "column": 6, "nodeType": "556", "endLine": 24, "endColumn": 8, "suggestions": "572", "suppressions": "573"}, {"ruleId": "549", "severity": 1, "message": "574", "line": 3, "column": 10, "nodeType": "551", "messageId": "552", "endLine": 3, "endColumn": 31}, {"ruleId": "554", "severity": 1, "message": "575", "line": 25, "column": 6, "nodeType": "556", "endLine": 25, "endColumn": 8, "suggestions": "576"}, {"ruleId": "554", "severity": 1, "message": "577", "line": 48, "column": 6, "nodeType": "556", "endLine": 48, "endColumn": 62, "suggestions": "578"}, {"ruleId": "554", "severity": 1, "message": "571", "line": 27, "column": 8, "nodeType": "556", "endLine": 27, "endColumn": 10, "suggestions": "579"}, {"ruleId": "549", "severity": 1, "message": "580", "line": 6, "column": 3, "nodeType": "551", "messageId": "552", "endLine": 6, "endColumn": 21}, {"ruleId": "554", "severity": 1, "message": "568", "line": 30, "column": 6, "nodeType": "556", "endLine": 30, "endColumn": 8, "suggestions": "581", "suppressions": "582"}, {"ruleId": "549", "severity": 1, "message": "583", "line": 1, "column": 8, "nodeType": "551", "messageId": "552", "endLine": 1, "endColumn": 13}, {"ruleId": "549", "severity": 1, "message": "584", "line": 3, "column": 10, "nodeType": "551", "messageId": "552", "endLine": 3, "endColumn": 33}, {"ruleId": "549", "severity": 1, "message": "585", "line": 1, "column": 10, "nodeType": "551", "messageId": "552", "endLine": 1, "endColumn": 18}, {"ruleId": "549", "severity": 1, "message": "586", "line": 22, "column": 10, "nodeType": "551", "messageId": "552", "endLine": 22, "endColumn": 18}, {"ruleId": "554", "severity": 1, "message": "587", "line": 13, "column": 6, "nodeType": "556", "endLine": 13, "endColumn": 16, "suggestions": "588"}, {"ruleId": "549", "severity": 1, "message": "589", "line": 7, "column": 10, "nodeType": "551", "messageId": "552", "endLine": 7, "endColumn": 18}, {"ruleId": "549", "severity": 1, "message": "590", "line": 7, "column": 28, "nodeType": "551", "messageId": "552", "endLine": 7, "endColumn": 38}, {"ruleId": "559", "severity": 1, "message": "560", "line": 29, "column": 15, "nodeType": "561", "messageId": "562", "endLine": 29, "endColumn": 67}, {"ruleId": "554", "severity": 1, "message": "591", "line": 26, "column": 6, "nodeType": "556", "endLine": 26, "endColumn": 8, "suggestions": "592", "suppressions": "593"}, {"ruleId": "554", "severity": 1, "message": "594", "line": 39, "column": 6, "nodeType": "556", "endLine": 39, "endColumn": 62, "suggestions": "595", "suppressions": "596"}, {"ruleId": "549", "severity": 1, "message": "597", "line": 4, "column": 10, "nodeType": "551", "messageId": "552", "endLine": 4, "endColumn": 19}, {"ruleId": "549", "severity": 1, "message": "598", "line": 4, "column": 21, "nodeType": "551", "messageId": "552", "endLine": 4, "endColumn": 34}, {"ruleId": "549", "severity": 1, "message": "550", "line": 22, "column": 9, "nodeType": "551", "messageId": "552", "endLine": 22, "endColumn": 17}, {"ruleId": "549", "severity": 1, "message": "599", "line": 21, "column": 13, "nodeType": "551", "messageId": "552", "endLine": 21, "endColumn": 16}, {"ruleId": "549", "severity": 1, "message": "600", "line": 1, "column": 17, "nodeType": "551", "messageId": "552", "endLine": 1, "endColumn": 26}, {"ruleId": "549", "severity": 1, "message": "601", "line": 1, "column": 28, "nodeType": "551", "messageId": "552", "endLine": 1, "endColumn": 34}, {"ruleId": "549", "severity": 1, "message": "602", "line": 1, "column": 36, "nodeType": "551", "messageId": "552", "endLine": 1, "endColumn": 44}, {"ruleId": "549", "severity": 1, "message": "603", "line": 2, "column": 10, "nodeType": "551", "messageId": "552", "endLine": 2, "endColumn": 23}, {"ruleId": "554", "severity": 1, "message": "604", "line": 60, "column": 6, "nodeType": "556", "endLine": 60, "endColumn": 8, "suggestions": "605", "suppressions": "606"}, {"ruleId": "554", "severity": 1, "message": "607", "line": 24, "column": 6, "nodeType": "556", "endLine": 24, "endColumn": 8, "suggestions": "608"}, {"ruleId": "549", "severity": 1, "message": "609", "line": 19, "column": 11, "nodeType": "551", "messageId": "552", "endLine": 19, "endColumn": 17}, {"ruleId": "554", "severity": 1, "message": "610", "line": 53, "column": 6, "nodeType": "556", "endLine": 53, "endColumn": 16, "suggestions": "611", "suppressions": "612"}, {"ruleId": "554", "severity": 1, "message": "610", "line": 58, "column": 6, "nodeType": "556", "endLine": 58, "endColumn": 30, "suggestions": "613", "suppressions": "614"}, {"ruleId": "554", "severity": 1, "message": "615", "line": 22, "column": 6, "nodeType": "556", "endLine": 22, "endColumn": 8, "suggestions": "616", "suppressions": "617"}, {"ruleId": "554", "severity": 1, "message": "618", "line": 27, "column": 6, "nodeType": "556", "endLine": 27, "endColumn": 24, "suggestions": "619", "suppressions": "620"}, {"ruleId": "554", "severity": 1, "message": "621", "line": 30, "column": 6, "nodeType": "556", "endLine": 30, "endColumn": 8, "suggestions": "622", "suppressions": "623"}, {"ruleId": "554", "severity": 1, "message": "618", "line": 35, "column": 6, "nodeType": "556", "endLine": 35, "endColumn": 13, "suggestions": "624", "suppressions": "625"}, {"ruleId": "554", "severity": 1, "message": "626", "line": 46, "column": 6, "nodeType": "556", "endLine": 46, "endColumn": 8, "suggestions": "627"}, "no-unused-vars", "'dispatch' is assigned a value but never used.", "Identifier", "unusedVar", "'navigate' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'navigate' and 'signupData'. Either include them or remove the dependency array.", "ArrayExpression", ["628"], ["629"], "react/jsx-pascal-case", "Imported JSX component Course_Card must be in PascalCase or SCREAMING_SNAKE_CASE", "JSXOpeningElement", "usePascalOrSnakeCase", "eqeqeq", "Expected '!==' and instead saw '!='.", "BinaryExpression", "unexpected", "'course_id' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'courseId', 'dispatch', and 'token'. Either include them or remove the dependency array.", ["630"], ["631"], "React Hook useEffect has a missing dependency: 'token'. Either include it or remove the dependency array.", ["632"], ["633"], "'BiDotsVerticalRounded' is defined but never used.", "React Hook useEffect has a missing dependency: 'getEnrolledCourses'. Either include it or remove the dependency array.", ["634"], "React Hook useEffect has missing dependencies: 'courseId', 'navigate', 'sectionId', and 'subSectionId'. Either include them or remove the dependency array.", ["635"], ["636"], "'fetchCourseDetails' is defined but never used.", ["637"], ["638"], "'React' is defined but never used.", "'updateCompletedLectures' is defined but never used.", "'FcGoogle' is defined but never used.", "'courseId' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'course._id'. Either include it or remove the dependency array. If 'setActive' needs the current value of 'course._id', you can also switch to useReducer instead of useState and read 'course._id' in the reducer.", ["639"], "'Autoplay' is defined but never used.", "'Navigation' is defined but never used.", "React Hook useEffect has a missing dependency: 'setValue'. Either include it or remove the dependency array.", ["640"], ["641"], "React Hook useEffect has missing dependencies: 'sectionId' and 'subSectionId'. Either include them or remove the dependency array.", ["642"], ["643"], "'setCourse' is defined but never used.", "'setEditCourse' is defined but never used.", "'res' is assigned a value but never used.", "'useEffect' is defined but never used.", "'useRef' is defined but never used.", "'useState' is defined but never used.", "'AiOutlineDown' is defined but never used.", "React Hook useEffect has missing dependencies: 'course.category', 'course.courseDescription', 'course.courseName', 'course.instructions', 'course.price', 'course.tag', 'course.thumbnail', 'course.whatYouWillLearn', 'editCourse', and 'setValue'. Either include them or remove the dependency array.", ["644"], ["645"], "React Hook useEffect has missing dependencies: 'course?.status' and 'setValue'. Either include them or remove the dependency array.", ["646"], "'course' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'name'. Either include it or remove the dependency array.", ["647"], ["648"], ["649"], ["650"], "React Hook useEffect has missing dependencies: 'course?.instructions', 'editCourse', 'name', and 'register'. Either include them or remove the dependency array. If 'register' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["651"], ["652"], "React Hook useEffect has missing dependencies: 'name' and 'setValue'. Either include them or remove the dependency array. If 'setValue' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["653"], ["654"], "React Hook useEffect has missing dependencies: 'course?.tag', 'editCourse', 'name', and 'register'. Either include them or remove the dependency array. If 'register' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["655"], ["656"], ["657"], ["658"], "React Hook useEffect has missing dependencies: 'edit', 'modalData.description', 'modalData.title', 'modalData.videoUrl', 'setValue', and 'view'. Either include them or remove the dependency array.", ["659"], {"desc": "660", "fix": "661"}, {"kind": "662", "justification": "663"}, {"desc": "664", "fix": "665"}, {"kind": "662", "justification": "663"}, {"desc": "666", "fix": "667"}, {"kind": "662", "justification": "663"}, {"desc": "668", "fix": "669"}, {"desc": "670", "fix": "671"}, {"desc": "666", "fix": "672"}, {"desc": "664", "fix": "673"}, {"kind": "662", "justification": "663"}, {"desc": "674", "fix": "675"}, {"desc": "676", "fix": "677"}, {"kind": "662", "justification": "663"}, {"desc": "678", "fix": "679"}, {"kind": "662", "justification": "663"}, {"desc": "680", "fix": "681"}, {"kind": "662", "justification": "663"}, {"desc": "682", "fix": "683"}, {"desc": "684", "fix": "685"}, {"kind": "662", "justification": "663"}, {"desc": "686", "fix": "687"}, {"kind": "662", "justification": "663"}, {"desc": "688", "fix": "689"}, {"kind": "662", "justification": "663"}, {"desc": "690", "fix": "691"}, {"kind": "662", "justification": "663"}, {"desc": "692", "fix": "693"}, {"kind": "662", "justification": "663"}, {"desc": "694", "fix": "695"}, {"kind": "662", "justification": "663"}, {"desc": "696", "fix": "697"}, "Update the dependencies array to be: [navigate, signupData]", {"range": "698", "text": "699"}, "directive", "", "Update the dependencies array to be: [courseId, dispatch, token]", {"range": "700", "text": "701"}, "Update the dependencies array to be: [token]", {"range": "702", "text": "703"}, "Update the dependencies array to be: [getEnrolledCourses]", {"range": "704", "text": "705"}, "Update the dependencies array to be: [courseSectionData, courseEntireData, location.pathname, courseId, sectionId, subSectionId, navigate]", {"range": "706", "text": "707"}, {"range": "708", "text": "703"}, {"range": "709", "text": "701"}, "Update the dependencies array to be: [course._id, isActive]", {"range": "710", "text": "711"}, "Update the dependencies array to be: [setValue]", {"range": "712", "text": "713"}, "Update the dependencies array to be: [courseSectionData, courseEntireData, location.pathname, sectionId, subSectionId]", {"range": "714", "text": "715"}, "Update the dependencies array to be: [course.category, course.courseDescription, course.courseName, course.instructions, course.price, course.tag, course.thumbnail, course.what<PERSON><PERSON><PERSON><PERSON><PERSON>, editCourse, setValue]", {"range": "716", "text": "717"}, "Update the dependencies array to be: [course?.status, setValue]", {"range": "718", "text": "719"}, "Update the dependencies array to be: [name, register]", {"range": "720", "text": "721"}, "Update the dependencies array to be: [name, selectedFile, setValue]", {"range": "722", "text": "723"}, "Update the dependencies array to be: [course?.instructions, editCourse, name, register]", {"range": "724", "text": "725"}, "Update the dependencies array to be: [name, requirementsList, setValue]", {"range": "726", "text": "727"}, "Update the dependencies array to be: [course?.tag, editCourse, name, register]", {"range": "728", "text": "729"}, "Update the dependencies array to be: [chips, name, setValue]", {"range": "730", "text": "731"}, "Update the dependencies array to be: [edit, modalData.description, modalData.title, modalData.videoUrl, setValue, view]", {"range": "732", "text": "733"}, [834, 836], "[navigate, signupData]", [1439, 1441], "[courseId, dispatch, token]", [822, 824], "[token]", [824, 826], "[getEnrolledCourses]", [1933, 1989], "[courseSectionData, courseEntireData, location.pathname, courseId, sectionId, subSectionId, navigate]", [1112, 1114], [1044, 1046], [434, 444], "[course._id, isActive]", [864, 866], "[setValue]", [1447, 1503], "[courseSectionData, courseEntireData, location.pathname, sectionId, subSectionId]", [2122, 2124], "[course.category, course.courseDescription, course.courseName, course.instructions, course.price, course.tag, course.thumbnail, course.what<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, editCourse, setValue]", [946, 948], "[course?.status, setValue]", [1390, 1400], "[name, register]", [1526, 1550], "[name, selected<PERSON><PERSON>, setValue]", [647, 649], "[course?.instructions, editCourse, name, register]", [779, 797], "[name, requirementsList, setValue]", [858, 860], "[course?.tag, editCourse, name, register]", [979, 986], "[chips, name, setValue]", [1299, 1301], "[edit, modalData.description, modalData.title, modalData.videoUrl, setValue, view]"]