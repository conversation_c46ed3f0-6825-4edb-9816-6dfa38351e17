{"ast": null, "code": "/**\n * This is the common logic for both the Node.js and web browser\n * implementations of `debug()`.\n */\n\nfunction setup(env) {\n  createDebug.debug = createDebug;\n  createDebug.default = createDebug;\n  createDebug.coerce = coerce;\n  createDebug.disable = disable;\n  createDebug.enable = enable;\n  createDebug.enabled = enabled;\n  createDebug.humanize = require('ms');\n  createDebug.destroy = destroy;\n  Object.keys(env).forEach(key => {\n    createDebug[key] = env[key];\n  });\n\n  /**\n  * The currently active debug mode names, and names to skip.\n  */\n\n  createDebug.names = [];\n  createDebug.skips = [];\n\n  /**\n  * Map of special \"%n\" handling functions, for the debug \"format\" argument.\n  *\n  * Valid key names are a single, lower or upper-case letter, i.e. \"n\" and \"N\".\n  */\n  createDebug.formatters = {};\n\n  /**\n  * Selects a color for a debug namespace\n  * @param {String} namespace The namespace string for the debug instance to be colored\n  * @return {Number|String} An ANSI color code for the given namespace\n  * @api private\n  */\n  function selectColor(namespace) {\n    let hash = 0;\n    for (let i = 0; i < namespace.length; i++) {\n      hash = (hash << 5) - hash + namespace.charCodeAt(i);\n      hash |= 0; // Convert to 32bit integer\n    }\n\n    return createDebug.colors[Math.abs(hash) % createDebug.colors.length];\n  }\n  createDebug.selectColor = selectColor;\n\n  /**\n  * Create a debugger with the given `namespace`.\n  *\n  * @param {String} namespace\n  * @return {Function}\n  * @api public\n  */\n  function createDebug(namespace) {\n    let prevTime;\n    let enableOverride = null;\n    let namespacesCache;\n    let enabledCache;\n    function debug() {\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      // Disabled?\n      if (!debug.enabled) {\n        return;\n      }\n      const self = debug;\n\n      // Set `diff` timestamp\n      const curr = Number(new Date());\n      const ms = curr - (prevTime || curr);\n      self.diff = ms;\n      self.prev = prevTime;\n      self.curr = curr;\n      prevTime = curr;\n      args[0] = createDebug.coerce(args[0]);\n      if (typeof args[0] !== 'string') {\n        // Anything else let's inspect with %O\n        args.unshift('%O');\n      }\n\n      // Apply any `formatters` transformations\n      let index = 0;\n      args[0] = args[0].replace(/%([a-zA-Z%])/g, (match, format) => {\n        // If we encounter an escaped % then don't increase the array index\n        if (match === '%%') {\n          return '%';\n        }\n        index++;\n        const formatter = createDebug.formatters[format];\n        if (typeof formatter === 'function') {\n          const val = args[index];\n          match = formatter.call(self, val);\n\n          // Now we need to remove `args[index]` since it's inlined in the `format`\n          args.splice(index, 1);\n          index--;\n        }\n        return match;\n      });\n\n      // Apply env-specific formatting (colors, etc.)\n      createDebug.formatArgs.call(self, args);\n      const logFn = self.log || createDebug.log;\n      logFn.apply(self, args);\n    }\n    debug.namespace = namespace;\n    debug.useColors = createDebug.useColors();\n    debug.color = createDebug.selectColor(namespace);\n    debug.extend = extend;\n    debug.destroy = createDebug.destroy; // XXX Temporary. Will be removed in the next major release.\n\n    Object.defineProperty(debug, 'enabled', {\n      enumerable: true,\n      configurable: false,\n      get: () => {\n        if (enableOverride !== null) {\n          return enableOverride;\n        }\n        if (namespacesCache !== createDebug.namespaces) {\n          namespacesCache = createDebug.namespaces;\n          enabledCache = createDebug.enabled(namespace);\n        }\n        return enabledCache;\n      },\n      set: v => {\n        enableOverride = v;\n      }\n    });\n\n    // Env-specific initialization logic for debug instances\n    if (typeof createDebug.init === 'function') {\n      createDebug.init(debug);\n    }\n    return debug;\n  }\n  function extend(namespace, delimiter) {\n    const newDebug = createDebug(this.namespace + (typeof delimiter === 'undefined' ? ':' : delimiter) + namespace);\n    newDebug.log = this.log;\n    return newDebug;\n  }\n\n  /**\n  * Enables a debug mode by namespaces. This can include modes\n  * separated by a colon and wildcards.\n  *\n  * @param {String} namespaces\n  * @api public\n  */\n  function enable(namespaces) {\n    createDebug.save(namespaces);\n    createDebug.namespaces = namespaces;\n    createDebug.names = [];\n    createDebug.skips = [];\n    let i;\n    const split = (typeof namespaces === 'string' ? namespaces : '').split(/[\\s,]+/);\n    const len = split.length;\n    for (i = 0; i < len; i++) {\n      if (!split[i]) {\n        // ignore empty strings\n        continue;\n      }\n      namespaces = split[i].replace(/\\*/g, '.*?');\n      if (namespaces[0] === '-') {\n        createDebug.skips.push(new RegExp('^' + namespaces.slice(1) + '$'));\n      } else {\n        createDebug.names.push(new RegExp('^' + namespaces + '$'));\n      }\n    }\n  }\n\n  /**\n  * Disable debug output.\n  *\n  * @return {String} namespaces\n  * @api public\n  */\n  function disable() {\n    const namespaces = [...createDebug.names.map(toNamespace), ...createDebug.skips.map(toNamespace).map(namespace => '-' + namespace)].join(',');\n    createDebug.enable('');\n    return namespaces;\n  }\n\n  /**\n  * Returns true if the given mode name is enabled, false otherwise.\n  *\n  * @param {String} name\n  * @return {Boolean}\n  * @api public\n  */\n  function enabled(name) {\n    if (name[name.length - 1] === '*') {\n      return true;\n    }\n    let i;\n    let len;\n    for (i = 0, len = createDebug.skips.length; i < len; i++) {\n      if (createDebug.skips[i].test(name)) {\n        return false;\n      }\n    }\n    for (i = 0, len = createDebug.names.length; i < len; i++) {\n      if (createDebug.names[i].test(name)) {\n        return true;\n      }\n    }\n    return false;\n  }\n\n  /**\n  * Convert regexp to namespace\n  *\n  * @param {RegExp} regxep\n  * @return {String} namespace\n  * @api private\n  */\n  function toNamespace(regexp) {\n    return regexp.toString().substring(2, regexp.toString().length - 2).replace(/\\.\\*\\?$/, '*');\n  }\n\n  /**\n  * Coerce `val`.\n  *\n  * @param {Mixed} val\n  * @return {Mixed}\n  * @api private\n  */\n  function coerce(val) {\n    if (val instanceof Error) {\n      return val.stack || val.message;\n    }\n    return val;\n  }\n\n  /**\n  * XXX DO NOT USE. This is a temporary stub function.\n  * XXX It WILL be removed in the next major release.\n  */\n  function destroy() {\n    console.warn('Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.');\n  }\n  createDebug.enable(createDebug.load());\n  return createDebug;\n}\nmodule.exports = setup;", "map": {"version": 3, "names": ["setup", "env", "createDebug", "debug", "default", "coerce", "disable", "enable", "enabled", "humanize", "require", "destroy", "Object", "keys", "for<PERSON>ach", "key", "names", "skips", "formatters", "selectColor", "namespace", "hash", "i", "length", "charCodeAt", "colors", "Math", "abs", "prevTime", "enableOverride", "namespacesCache", "enabledCache", "_len", "arguments", "args", "Array", "_key", "self", "curr", "Number", "Date", "ms", "diff", "prev", "unshift", "index", "replace", "match", "format", "formatter", "val", "call", "splice", "formatArgs", "logFn", "log", "apply", "useColors", "color", "extend", "defineProperty", "enumerable", "configurable", "get", "namespaces", "set", "v", "init", "delimiter", "newDebug", "save", "split", "len", "push", "RegExp", "slice", "map", "toNamespace", "join", "name", "test", "regexp", "toString", "substring", "Error", "stack", "message", "console", "warn", "load", "module", "exports"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/debug/src/common.js"], "sourcesContent": ["\n/**\n * This is the common logic for both the Node.js and web browser\n * implementations of `debug()`.\n */\n\nfunction setup(env) {\n\tcreateDebug.debug = createDebug;\n\tcreateDebug.default = createDebug;\n\tcreateDebug.coerce = coerce;\n\tcreateDebug.disable = disable;\n\tcreateDebug.enable = enable;\n\tcreateDebug.enabled = enabled;\n\tcreateDebug.humanize = require('ms');\n\tcreateDebug.destroy = destroy;\n\n\tObject.keys(env).forEach(key => {\n\t\tcreateDebug[key] = env[key];\n\t});\n\n\t/**\n\t* The currently active debug mode names, and names to skip.\n\t*/\n\n\tcreateDebug.names = [];\n\tcreateDebug.skips = [];\n\n\t/**\n\t* Map of special \"%n\" handling functions, for the debug \"format\" argument.\n\t*\n\t* Valid key names are a single, lower or upper-case letter, i.e. \"n\" and \"N\".\n\t*/\n\tcreateDebug.formatters = {};\n\n\t/**\n\t* Selects a color for a debug namespace\n\t* @param {String} namespace The namespace string for the debug instance to be colored\n\t* @return {Number|String} An ANSI color code for the given namespace\n\t* @api private\n\t*/\n\tfunction selectColor(namespace) {\n\t\tlet hash = 0;\n\n\t\tfor (let i = 0; i < namespace.length; i++) {\n\t\t\thash = ((hash << 5) - hash) + namespace.charCodeAt(i);\n\t\t\thash |= 0; // Convert to 32bit integer\n\t\t}\n\n\t\treturn createDebug.colors[Math.abs(hash) % createDebug.colors.length];\n\t}\n\tcreateDebug.selectColor = selectColor;\n\n\t/**\n\t* Create a debugger with the given `namespace`.\n\t*\n\t* @param {String} namespace\n\t* @return {Function}\n\t* @api public\n\t*/\n\tfunction createDebug(namespace) {\n\t\tlet prevTime;\n\t\tlet enableOverride = null;\n\t\tlet namespacesCache;\n\t\tlet enabledCache;\n\n\t\tfunction debug(...args) {\n\t\t\t// Disabled?\n\t\t\tif (!debug.enabled) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tconst self = debug;\n\n\t\t\t// Set `diff` timestamp\n\t\t\tconst curr = Number(new Date());\n\t\t\tconst ms = curr - (prevTime || curr);\n\t\t\tself.diff = ms;\n\t\t\tself.prev = prevTime;\n\t\t\tself.curr = curr;\n\t\t\tprevTime = curr;\n\n\t\t\targs[0] = createDebug.coerce(args[0]);\n\n\t\t\tif (typeof args[0] !== 'string') {\n\t\t\t\t// Anything else let's inspect with %O\n\t\t\t\targs.unshift('%O');\n\t\t\t}\n\n\t\t\t// Apply any `formatters` transformations\n\t\t\tlet index = 0;\n\t\t\targs[0] = args[0].replace(/%([a-zA-Z%])/g, (match, format) => {\n\t\t\t\t// If we encounter an escaped % then don't increase the array index\n\t\t\t\tif (match === '%%') {\n\t\t\t\t\treturn '%';\n\t\t\t\t}\n\t\t\t\tindex++;\n\t\t\t\tconst formatter = createDebug.formatters[format];\n\t\t\t\tif (typeof formatter === 'function') {\n\t\t\t\t\tconst val = args[index];\n\t\t\t\t\tmatch = formatter.call(self, val);\n\n\t\t\t\t\t// Now we need to remove `args[index]` since it's inlined in the `format`\n\t\t\t\t\targs.splice(index, 1);\n\t\t\t\t\tindex--;\n\t\t\t\t}\n\t\t\t\treturn match;\n\t\t\t});\n\n\t\t\t// Apply env-specific formatting (colors, etc.)\n\t\t\tcreateDebug.formatArgs.call(self, args);\n\n\t\t\tconst logFn = self.log || createDebug.log;\n\t\t\tlogFn.apply(self, args);\n\t\t}\n\n\t\tdebug.namespace = namespace;\n\t\tdebug.useColors = createDebug.useColors();\n\t\tdebug.color = createDebug.selectColor(namespace);\n\t\tdebug.extend = extend;\n\t\tdebug.destroy = createDebug.destroy; // XXX Temporary. Will be removed in the next major release.\n\n\t\tObject.defineProperty(debug, 'enabled', {\n\t\t\tenumerable: true,\n\t\t\tconfigurable: false,\n\t\t\tget: () => {\n\t\t\t\tif (enableOverride !== null) {\n\t\t\t\t\treturn enableOverride;\n\t\t\t\t}\n\t\t\t\tif (namespacesCache !== createDebug.namespaces) {\n\t\t\t\t\tnamespacesCache = createDebug.namespaces;\n\t\t\t\t\tenabledCache = createDebug.enabled(namespace);\n\t\t\t\t}\n\n\t\t\t\treturn enabledCache;\n\t\t\t},\n\t\t\tset: v => {\n\t\t\t\tenableOverride = v;\n\t\t\t}\n\t\t});\n\n\t\t// Env-specific initialization logic for debug instances\n\t\tif (typeof createDebug.init === 'function') {\n\t\t\tcreateDebug.init(debug);\n\t\t}\n\n\t\treturn debug;\n\t}\n\n\tfunction extend(namespace, delimiter) {\n\t\tconst newDebug = createDebug(this.namespace + (typeof delimiter === 'undefined' ? ':' : delimiter) + namespace);\n\t\tnewDebug.log = this.log;\n\t\treturn newDebug;\n\t}\n\n\t/**\n\t* Enables a debug mode by namespaces. This can include modes\n\t* separated by a colon and wildcards.\n\t*\n\t* @param {String} namespaces\n\t* @api public\n\t*/\n\tfunction enable(namespaces) {\n\t\tcreateDebug.save(namespaces);\n\t\tcreateDebug.namespaces = namespaces;\n\n\t\tcreateDebug.names = [];\n\t\tcreateDebug.skips = [];\n\n\t\tlet i;\n\t\tconst split = (typeof namespaces === 'string' ? namespaces : '').split(/[\\s,]+/);\n\t\tconst len = split.length;\n\n\t\tfor (i = 0; i < len; i++) {\n\t\t\tif (!split[i]) {\n\t\t\t\t// ignore empty strings\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tnamespaces = split[i].replace(/\\*/g, '.*?');\n\n\t\t\tif (namespaces[0] === '-') {\n\t\t\t\tcreateDebug.skips.push(new RegExp('^' + namespaces.slice(1) + '$'));\n\t\t\t} else {\n\t\t\t\tcreateDebug.names.push(new RegExp('^' + namespaces + '$'));\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t* Disable debug output.\n\t*\n\t* @return {String} namespaces\n\t* @api public\n\t*/\n\tfunction disable() {\n\t\tconst namespaces = [\n\t\t\t...createDebug.names.map(toNamespace),\n\t\t\t...createDebug.skips.map(toNamespace).map(namespace => '-' + namespace)\n\t\t].join(',');\n\t\tcreateDebug.enable('');\n\t\treturn namespaces;\n\t}\n\n\t/**\n\t* Returns true if the given mode name is enabled, false otherwise.\n\t*\n\t* @param {String} name\n\t* @return {Boolean}\n\t* @api public\n\t*/\n\tfunction enabled(name) {\n\t\tif (name[name.length - 1] === '*') {\n\t\t\treturn true;\n\t\t}\n\n\t\tlet i;\n\t\tlet len;\n\n\t\tfor (i = 0, len = createDebug.skips.length; i < len; i++) {\n\t\t\tif (createDebug.skips[i].test(name)) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t}\n\n\t\tfor (i = 0, len = createDebug.names.length; i < len; i++) {\n\t\t\tif (createDebug.names[i].test(name)) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t}\n\n\t\treturn false;\n\t}\n\n\t/**\n\t* Convert regexp to namespace\n\t*\n\t* @param {RegExp} regxep\n\t* @return {String} namespace\n\t* @api private\n\t*/\n\tfunction toNamespace(regexp) {\n\t\treturn regexp.toString()\n\t\t\t.substring(2, regexp.toString().length - 2)\n\t\t\t.replace(/\\.\\*\\?$/, '*');\n\t}\n\n\t/**\n\t* Coerce `val`.\n\t*\n\t* @param {Mixed} val\n\t* @return {Mixed}\n\t* @api private\n\t*/\n\tfunction coerce(val) {\n\t\tif (val instanceof Error) {\n\t\t\treturn val.stack || val.message;\n\t\t}\n\t\treturn val;\n\t}\n\n\t/**\n\t* XXX DO NOT USE. This is a temporary stub function.\n\t* XXX It WILL be removed in the next major release.\n\t*/\n\tfunction destroy() {\n\t\tconsole.warn('Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.');\n\t}\n\n\tcreateDebug.enable(createDebug.load());\n\n\treturn createDebug;\n}\n\nmodule.exports = setup;\n"], "mappings": "AACA;AACA;AACA;AACA;;AAEA,SAASA,KAAKA,CAACC,GAAG,EAAE;EACnBC,WAAW,CAACC,KAAK,GAAGD,WAAW;EAC/BA,WAAW,CAACE,OAAO,GAAGF,WAAW;EACjCA,WAAW,CAACG,MAAM,GAAGA,MAAM;EAC3BH,WAAW,CAACI,OAAO,GAAGA,OAAO;EAC7BJ,WAAW,CAACK,MAAM,GAAGA,MAAM;EAC3BL,WAAW,CAACM,OAAO,GAAGA,OAAO;EAC7BN,WAAW,CAACO,QAAQ,GAAGC,OAAO,CAAC,IAAI,CAAC;EACpCR,WAAW,CAACS,OAAO,GAAGA,OAAO;EAE7BC,MAAM,CAACC,IAAI,CAACZ,GAAG,CAAC,CAACa,OAAO,CAACC,GAAG,IAAI;IAC/Bb,WAAW,CAACa,GAAG,CAAC,GAAGd,GAAG,CAACc,GAAG,CAAC;EAC5B,CAAC,CAAC;;EAEF;AACD;AACA;;EAECb,WAAW,CAACc,KAAK,GAAG,EAAE;EACtBd,WAAW,CAACe,KAAK,GAAG,EAAE;;EAEtB;AACD;AACA;AACA;AACA;EACCf,WAAW,CAACgB,UAAU,GAAG,CAAC,CAAC;;EAE3B;AACD;AACA;AACA;AACA;AACA;EACC,SAASC,WAAWA,CAACC,SAAS,EAAE;IAC/B,IAAIC,IAAI,GAAG,CAAC;IAEZ,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,SAAS,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;MAC1CD,IAAI,GAAI,CAACA,IAAI,IAAI,CAAC,IAAIA,IAAI,GAAID,SAAS,CAACI,UAAU,CAACF,CAAC,CAAC;MACrDD,IAAI,IAAI,CAAC,CAAC,CAAC;IACZ;;IAEA,OAAOnB,WAAW,CAACuB,MAAM,CAACC,IAAI,CAACC,GAAG,CAACN,IAAI,CAAC,GAAGnB,WAAW,CAACuB,MAAM,CAACF,MAAM,CAAC;EACtE;EACArB,WAAW,CAACiB,WAAW,GAAGA,WAAW;;EAErC;AACD;AACA;AACA;AACA;AACA;AACA;EACC,SAASjB,WAAWA,CAACkB,SAAS,EAAE;IAC/B,IAAIQ,QAAQ;IACZ,IAAIC,cAAc,GAAG,IAAI;IACzB,IAAIC,eAAe;IACnB,IAAIC,YAAY;IAEhB,SAAS5B,KAAKA,CAAA,EAAU;MAAA,SAAA6B,IAAA,GAAAC,SAAA,CAAAV,MAAA,EAANW,IAAI,OAAAC,KAAA,CAAAH,IAAA,GAAAI,IAAA,MAAAA,IAAA,GAAAJ,IAAA,EAAAI,IAAA;QAAJF,IAAI,CAAAE,IAAA,IAAAH,SAAA,CAAAG,IAAA;MAAA;MACrB;MACA,IAAI,CAACjC,KAAK,CAACK,OAAO,EAAE;QACnB;MACD;MAEA,MAAM6B,IAAI,GAAGlC,KAAK;;MAElB;MACA,MAAMmC,IAAI,GAAGC,MAAM,CAAC,IAAIC,IAAI,EAAE,CAAC;MAC/B,MAAMC,EAAE,GAAGH,IAAI,IAAIV,QAAQ,IAAIU,IAAI,CAAC;MACpCD,IAAI,CAACK,IAAI,GAAGD,EAAE;MACdJ,IAAI,CAACM,IAAI,GAAGf,QAAQ;MACpBS,IAAI,CAACC,IAAI,GAAGA,IAAI;MAChBV,QAAQ,GAAGU,IAAI;MAEfJ,IAAI,CAAC,CAAC,CAAC,GAAGhC,WAAW,CAACG,MAAM,CAAC6B,IAAI,CAAC,CAAC,CAAC,CAAC;MAErC,IAAI,OAAOA,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;QAChC;QACAA,IAAI,CAACU,OAAO,CAAC,IAAI,CAAC;MACnB;;MAEA;MACA,IAAIC,KAAK,GAAG,CAAC;MACbX,IAAI,CAAC,CAAC,CAAC,GAAGA,IAAI,CAAC,CAAC,CAAC,CAACY,OAAO,CAAC,eAAe,EAAE,CAACC,KAAK,EAAEC,MAAM,KAAK;QAC7D;QACA,IAAID,KAAK,KAAK,IAAI,EAAE;UACnB,OAAO,GAAG;QACX;QACAF,KAAK,EAAE;QACP,MAAMI,SAAS,GAAG/C,WAAW,CAACgB,UAAU,CAAC8B,MAAM,CAAC;QAChD,IAAI,OAAOC,SAAS,KAAK,UAAU,EAAE;UACpC,MAAMC,GAAG,GAAGhB,IAAI,CAACW,KAAK,CAAC;UACvBE,KAAK,GAAGE,SAAS,CAACE,IAAI,CAACd,IAAI,EAAEa,GAAG,CAAC;;UAEjC;UACAhB,IAAI,CAACkB,MAAM,CAACP,KAAK,EAAE,CAAC,CAAC;UACrBA,KAAK,EAAE;QACR;QACA,OAAOE,KAAK;MACb,CAAC,CAAC;;MAEF;MACA7C,WAAW,CAACmD,UAAU,CAACF,IAAI,CAACd,IAAI,EAAEH,IAAI,CAAC;MAEvC,MAAMoB,KAAK,GAAGjB,IAAI,CAACkB,GAAG,IAAIrD,WAAW,CAACqD,GAAG;MACzCD,KAAK,CAACE,KAAK,CAACnB,IAAI,EAAEH,IAAI,CAAC;IACxB;IAEA/B,KAAK,CAACiB,SAAS,GAAGA,SAAS;IAC3BjB,KAAK,CAACsD,SAAS,GAAGvD,WAAW,CAACuD,SAAS,EAAE;IACzCtD,KAAK,CAACuD,KAAK,GAAGxD,WAAW,CAACiB,WAAW,CAACC,SAAS,CAAC;IAChDjB,KAAK,CAACwD,MAAM,GAAGA,MAAM;IACrBxD,KAAK,CAACQ,OAAO,GAAGT,WAAW,CAACS,OAAO,CAAC,CAAC;;IAErCC,MAAM,CAACgD,cAAc,CAACzD,KAAK,EAAE,SAAS,EAAE;MACvC0D,UAAU,EAAE,IAAI;MAChBC,YAAY,EAAE,KAAK;MACnBC,GAAG,EAAEA,CAAA,KAAM;QACV,IAAIlC,cAAc,KAAK,IAAI,EAAE;UAC5B,OAAOA,cAAc;QACtB;QACA,IAAIC,eAAe,KAAK5B,WAAW,CAAC8D,UAAU,EAAE;UAC/ClC,eAAe,GAAG5B,WAAW,CAAC8D,UAAU;UACxCjC,YAAY,GAAG7B,WAAW,CAACM,OAAO,CAACY,SAAS,CAAC;QAC9C;QAEA,OAAOW,YAAY;MACpB,CAAC;MACDkC,GAAG,EAAEC,CAAC,IAAI;QACTrC,cAAc,GAAGqC,CAAC;MACnB;IACD,CAAC,CAAC;;IAEF;IACA,IAAI,OAAOhE,WAAW,CAACiE,IAAI,KAAK,UAAU,EAAE;MAC3CjE,WAAW,CAACiE,IAAI,CAAChE,KAAK,CAAC;IACxB;IAEA,OAAOA,KAAK;EACb;EAEA,SAASwD,MAAMA,CAACvC,SAAS,EAAEgD,SAAS,EAAE;IACrC,MAAMC,QAAQ,GAAGnE,WAAW,CAAC,IAAI,CAACkB,SAAS,IAAI,OAAOgD,SAAS,KAAK,WAAW,GAAG,GAAG,GAAGA,SAAS,CAAC,GAAGhD,SAAS,CAAC;IAC/GiD,QAAQ,CAACd,GAAG,GAAG,IAAI,CAACA,GAAG;IACvB,OAAOc,QAAQ;EAChB;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;EACC,SAAS9D,MAAMA,CAACyD,UAAU,EAAE;IAC3B9D,WAAW,CAACoE,IAAI,CAACN,UAAU,CAAC;IAC5B9D,WAAW,CAAC8D,UAAU,GAAGA,UAAU;IAEnC9D,WAAW,CAACc,KAAK,GAAG,EAAE;IACtBd,WAAW,CAACe,KAAK,GAAG,EAAE;IAEtB,IAAIK,CAAC;IACL,MAAMiD,KAAK,GAAG,CAAC,OAAOP,UAAU,KAAK,QAAQ,GAAGA,UAAU,GAAG,EAAE,EAAEO,KAAK,CAAC,QAAQ,CAAC;IAChF,MAAMC,GAAG,GAAGD,KAAK,CAAChD,MAAM;IAExB,KAAKD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkD,GAAG,EAAElD,CAAC,EAAE,EAAE;MACzB,IAAI,CAACiD,KAAK,CAACjD,CAAC,CAAC,EAAE;QACd;QACA;MACD;MAEA0C,UAAU,GAAGO,KAAK,CAACjD,CAAC,CAAC,CAACwB,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC;MAE3C,IAAIkB,UAAU,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QAC1B9D,WAAW,CAACe,KAAK,CAACwD,IAAI,CAAC,IAAIC,MAAM,CAAC,GAAG,GAAGV,UAAU,CAACW,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;MACpE,CAAC,MAAM;QACNzE,WAAW,CAACc,KAAK,CAACyD,IAAI,CAAC,IAAIC,MAAM,CAAC,GAAG,GAAGV,UAAU,GAAG,GAAG,CAAC,CAAC;MAC3D;IACD;EACD;;EAEA;AACD;AACA;AACA;AACA;AACA;EACC,SAAS1D,OAAOA,CAAA,EAAG;IAClB,MAAM0D,UAAU,GAAG,CAClB,GAAG9D,WAAW,CAACc,KAAK,CAAC4D,GAAG,CAACC,WAAW,CAAC,EACrC,GAAG3E,WAAW,CAACe,KAAK,CAAC2D,GAAG,CAACC,WAAW,CAAC,CAACD,GAAG,CAACxD,SAAS,IAAI,GAAG,GAAGA,SAAS,CAAC,CACvE,CAAC0D,IAAI,CAAC,GAAG,CAAC;IACX5E,WAAW,CAACK,MAAM,CAAC,EAAE,CAAC;IACtB,OAAOyD,UAAU;EAClB;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;EACC,SAASxD,OAAOA,CAACuE,IAAI,EAAE;IACtB,IAAIA,IAAI,CAACA,IAAI,CAACxD,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;MAClC,OAAO,IAAI;IACZ;IAEA,IAAID,CAAC;IACL,IAAIkD,GAAG;IAEP,KAAKlD,CAAC,GAAG,CAAC,EAAEkD,GAAG,GAAGtE,WAAW,CAACe,KAAK,CAACM,MAAM,EAAED,CAAC,GAAGkD,GAAG,EAAElD,CAAC,EAAE,EAAE;MACzD,IAAIpB,WAAW,CAACe,KAAK,CAACK,CAAC,CAAC,CAAC0D,IAAI,CAACD,IAAI,CAAC,EAAE;QACpC,OAAO,KAAK;MACb;IACD;IAEA,KAAKzD,CAAC,GAAG,CAAC,EAAEkD,GAAG,GAAGtE,WAAW,CAACc,KAAK,CAACO,MAAM,EAAED,CAAC,GAAGkD,GAAG,EAAElD,CAAC,EAAE,EAAE;MACzD,IAAIpB,WAAW,CAACc,KAAK,CAACM,CAAC,CAAC,CAAC0D,IAAI,CAACD,IAAI,CAAC,EAAE;QACpC,OAAO,IAAI;MACZ;IACD;IAEA,OAAO,KAAK;EACb;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;EACC,SAASF,WAAWA,CAACI,MAAM,EAAE;IAC5B,OAAOA,MAAM,CAACC,QAAQ,EAAE,CACtBC,SAAS,CAAC,CAAC,EAAEF,MAAM,CAACC,QAAQ,EAAE,CAAC3D,MAAM,GAAG,CAAC,CAAC,CAC1CuB,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC;EAC1B;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;EACC,SAASzC,MAAMA,CAAC6C,GAAG,EAAE;IACpB,IAAIA,GAAG,YAAYkC,KAAK,EAAE;MACzB,OAAOlC,GAAG,CAACmC,KAAK,IAAInC,GAAG,CAACoC,OAAO;IAChC;IACA,OAAOpC,GAAG;EACX;;EAEA;AACD;AACA;AACA;EACC,SAASvC,OAAOA,CAAA,EAAG;IAClB4E,OAAO,CAACC,IAAI,CAAC,uIAAuI,CAAC;EACtJ;EAEAtF,WAAW,CAACK,MAAM,CAACL,WAAW,CAACuF,IAAI,EAAE,CAAC;EAEtC,OAAOvF,WAAW;AACnB;AAEAwF,MAAM,CAACC,OAAO,GAAG3F,KAAK"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}