{"ast": null, "code": "import transitionEmit from './transitionEmit.js';\nexport default function transitionStart() {\n  let runCallbacks = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;\n  let direction = arguments.length > 1 ? arguments[1] : undefined;\n  const swiper = this;\n  const {\n    params\n  } = swiper;\n  if (params.cssMode) return;\n  if (params.autoHeight) {\n    swiper.updateAutoHeight();\n  }\n  transitionEmit({\n    swiper,\n    runCallbacks,\n    direction,\n    step: 'Start'\n  });\n}", "map": {"version": 3, "names": ["transitionEmit", "transitionStart", "runCallbacks", "arguments", "length", "undefined", "direction", "swiper", "params", "cssMode", "autoHeight", "updateAutoHeight", "step"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/core/transition/transitionStart.js"], "sourcesContent": ["import transitionEmit from './transitionEmit.js';\nexport default function transitionStart(runCallbacks = true, direction) {\n  const swiper = this;\n  const {\n    params\n  } = swiper;\n  if (params.cssMode) return;\n  if (params.autoHeight) {\n    swiper.updateAutoHeight();\n  }\n  transitionEmit({\n    swiper,\n    runCallbacks,\n    direction,\n    step: 'Start'\n  });\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,qBAAqB;AAChD,eAAe,SAASC,eAAeA,CAAA,EAAiC;EAAA,IAAhCC,YAAY,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;EAAA,IAAEG,SAAS,GAAAH,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EACpE,MAAME,MAAM,GAAG,IAAI;EACnB,MAAM;IACJC;EACF,CAAC,GAAGD,MAAM;EACV,IAAIC,MAAM,CAACC,OAAO,EAAE;EACpB,IAAID,MAAM,CAACE,UAAU,EAAE;IACrBH,MAAM,CAACI,gBAAgB,EAAE;EAC3B;EACAX,cAAc,CAAC;IACbO,MAAM;IACNL,YAAY;IACZI,SAAS;IACTM,IAAI,EAAE;EACR,CAAC,CAAC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}