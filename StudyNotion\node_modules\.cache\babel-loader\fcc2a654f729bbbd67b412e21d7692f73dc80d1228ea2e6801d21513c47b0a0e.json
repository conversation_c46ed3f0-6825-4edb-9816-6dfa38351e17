{"ast": null, "code": "import { paramsList } from './params-list.js';\nimport { isObject } from './utils.js';\nfunction getChangedParams(swiperParams, oldParams, children, oldChildren, getKey) {\n  const keys = [];\n  if (!oldParams) return keys;\n  const addKey = key => {\n    if (keys.indexOf(key) < 0) keys.push(key);\n  };\n  if (children && oldChildren) {\n    const oldChildrenKeys = oldChildren.map(getKey);\n    const childrenKeys = children.map(getKey);\n    if (oldChildrenKeys.join('') !== childrenKeys.join('')) addKey('children');\n    if (oldChildren.length !== children.length) addKey('children');\n  }\n  const watchParams = paramsList.filter(key => key[0] === '_').map(key => key.replace(/_/, ''));\n  watchParams.forEach(key => {\n    if (key in swiperParams && key in oldParams) {\n      if (isObject(swiperParams[key]) && isObject(oldParams[key])) {\n        const newKeys = Object.keys(swiperParams[key]);\n        const oldKeys = Object.keys(oldParams[key]);\n        if (newKeys.length !== oldKeys.length) {\n          addKey(key);\n        } else {\n          newKeys.forEach(newKey => {\n            if (swiperParams[key][newKey] !== oldParams[key][newKey]) {\n              addKey(key);\n            }\n          });\n          oldKeys.forEach(oldKey => {\n            if (swiperParams[key][oldKey] !== oldParams[key][oldKey]) addKey(key);\n          });\n        }\n      } else if (swiperParams[key] !== oldParams[key]) {\n        addKey(key);\n      }\n    }\n  });\n  return keys;\n}\nexport { getChangedParams };", "map": {"version": 3, "names": ["paramsList", "isObject", "getChangedParams", "swiperParams", "oldParams", "children", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "keys", "<PERSON><PERSON><PERSON>", "key", "indexOf", "push", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "map", "children<PERSON>eys", "join", "length", "watchParams", "filter", "replace", "for<PERSON>ach", "newKeys", "Object", "oldKeys", "new<PERSON>ey", "<PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/components-shared/get-changed-params.js"], "sourcesContent": ["import { paramsList } from './params-list.js';\nimport { isObject } from './utils.js';\nfunction getChangedParams(swiperParams, oldParams, children, oldChildren, getKey) {\n  const keys = [];\n  if (!oldParams) return keys;\n  const addKey = key => {\n    if (keys.indexOf(key) < 0) keys.push(key);\n  };\n  if (children && oldChildren) {\n    const oldChildrenKeys = oldChildren.map(getKey);\n    const childrenKeys = children.map(getKey);\n    if (oldChildrenKeys.join('') !== childrenKeys.join('')) addKey('children');\n    if (oldChildren.length !== children.length) addKey('children');\n  }\n  const watchParams = paramsList.filter(key => key[0] === '_').map(key => key.replace(/_/, ''));\n  watchParams.forEach(key => {\n    if (key in swiperParams && key in oldParams) {\n      if (isObject(swiperParams[key]) && isObject(oldParams[key])) {\n        const newKeys = Object.keys(swiperParams[key]);\n        const oldKeys = Object.keys(oldParams[key]);\n        if (newKeys.length !== oldKeys.length) {\n          addKey(key);\n        } else {\n          newKeys.forEach(newKey => {\n            if (swiperParams[key][newKey] !== oldParams[key][newKey]) {\n              addKey(key);\n            }\n          });\n          oldKeys.forEach(oldKey => {\n            if (swiperParams[key][oldKey] !== oldParams[key][oldKey]) addKey(key);\n          });\n        }\n      } else if (swiperParams[key] !== oldParams[key]) {\n        addKey(key);\n      }\n    }\n  });\n  return keys;\n}\nexport { getChangedParams };"], "mappings": "AAAA,SAASA,UAAU,QAAQ,kBAAkB;AAC7C,SAASC,QAAQ,QAAQ,YAAY;AACrC,SAASC,gBAAgBA,CAACC,YAAY,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,MAAM,EAAE;EAChF,MAAMC,IAAI,GAAG,EAAE;EACf,IAAI,CAACJ,SAAS,EAAE,OAAOI,IAAI;EAC3B,MAAMC,MAAM,GAAGC,GAAG,IAAI;IACpB,IAAIF,IAAI,CAACG,OAAO,CAACD,GAAG,CAAC,GAAG,CAAC,EAAEF,IAAI,CAACI,IAAI,CAACF,GAAG,CAAC;EAC3C,CAAC;EACD,IAAIL,QAAQ,IAAIC,WAAW,EAAE;IAC3B,MAAMO,eAAe,GAAGP,WAAW,CAACQ,GAAG,CAACP,MAAM,CAAC;IAC/C,MAAMQ,YAAY,GAAGV,QAAQ,CAACS,GAAG,CAACP,MAAM,CAAC;IACzC,IAAIM,eAAe,CAACG,IAAI,CAAC,EAAE,CAAC,KAAKD,YAAY,CAACC,IAAI,CAAC,EAAE,CAAC,EAAEP,MAAM,CAAC,UAAU,CAAC;IAC1E,IAAIH,WAAW,CAACW,MAAM,KAAKZ,QAAQ,CAACY,MAAM,EAAER,MAAM,CAAC,UAAU,CAAC;EAChE;EACA,MAAMS,WAAW,GAAGlB,UAAU,CAACmB,MAAM,CAACT,GAAG,IAAIA,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAACI,GAAG,CAACJ,GAAG,IAAIA,GAAG,CAACU,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;EAC7FF,WAAW,CAACG,OAAO,CAACX,GAAG,IAAI;IACzB,IAAIA,GAAG,IAAIP,YAAY,IAAIO,GAAG,IAAIN,SAAS,EAAE;MAC3C,IAAIH,QAAQ,CAACE,YAAY,CAACO,GAAG,CAAC,CAAC,IAAIT,QAAQ,CAACG,SAAS,CAACM,GAAG,CAAC,CAAC,EAAE;QAC3D,MAAMY,OAAO,GAAGC,MAAM,CAACf,IAAI,CAACL,YAAY,CAACO,GAAG,CAAC,CAAC;QAC9C,MAAMc,OAAO,GAAGD,MAAM,CAACf,IAAI,CAACJ,SAAS,CAACM,GAAG,CAAC,CAAC;QAC3C,IAAIY,OAAO,CAACL,MAAM,KAAKO,OAAO,CAACP,MAAM,EAAE;UACrCR,MAAM,CAACC,GAAG,CAAC;QACb,CAAC,MAAM;UACLY,OAAO,CAACD,OAAO,CAACI,MAAM,IAAI;YACxB,IAAItB,YAAY,CAACO,GAAG,CAAC,CAACe,MAAM,CAAC,KAAKrB,SAAS,CAACM,GAAG,CAAC,CAACe,MAAM,CAAC,EAAE;cACxDhB,MAAM,CAACC,GAAG,CAAC;YACb;UACF,CAAC,CAAC;UACFc,OAAO,CAACH,OAAO,CAACK,MAAM,IAAI;YACxB,IAAIvB,YAAY,CAACO,GAAG,CAAC,CAACgB,MAAM,CAAC,KAAKtB,SAAS,CAACM,GAAG,CAAC,CAACgB,MAAM,CAAC,EAAEjB,MAAM,CAACC,GAAG,CAAC;UACvE,CAAC,CAAC;QACJ;MACF,CAAC,MAAM,IAAIP,YAAY,CAACO,GAAG,CAAC,KAAKN,SAAS,CAACM,GAAG,CAAC,EAAE;QAC/CD,MAAM,CAACC,GAAG,CAAC;MACb;IACF;EACF,CAAC,CAAC;EACF,OAAOF,IAAI;AACb;AACA,SAASN,gBAAgB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}