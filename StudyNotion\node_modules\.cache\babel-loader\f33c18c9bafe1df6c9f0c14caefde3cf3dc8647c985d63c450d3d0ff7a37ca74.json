{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar _classCallCheck2 = _interopRequireDefault(require(\"@babel/runtime/helpers/classCallCheck\"));\nvar _createClass2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createClass\"));\nvar _inherits2 = _interopRequireDefault(require(\"@babel/runtime/helpers/inherits\"));\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"@babel/runtime/helpers/possibleConstructorReturn\"));\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"@babel/runtime/helpers/getPrototypeOf\"));\nvar _react = _interopRequireDefault(require(\"react\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _allowed = _interopRequireDefault(require(\"../utils/allowed\"));\nfunction _createSuper(Derived) {\n  var hasNativeReflectConstruct = _isNativeReflectConstruct();\n  return function _createSuperInternal() {\n    var Super = (0, _getPrototypeOf2[\"default\"])(Derived),\n      result;\n    if (hasNativeReflectConstruct) {\n      var NewTarget = (0, _getPrototypeOf2[\"default\"])(this).constructor;\n      result = Reflect.construct(Super, arguments, NewTarget);\n    } else {\n      result = Super.apply(this, arguments);\n    }\n    return (0, _possibleConstructorReturn2[\"default\"])(this, result);\n  };\n}\nfunction _isNativeReflectConstruct() {\n  if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n  if (Reflect.construct.sham) return false;\n  if (typeof Proxy === \"function\") return true;\n  try {\n    Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\nvar TrInner = /*#__PURE__*/function (_React$Component) {\n  (0, _inherits2[\"default\"])(TrInner, _React$Component);\n  var _super = _createSuper(TrInner);\n  function TrInner(props) {\n    var _this;\n    (0, _classCallCheck2[\"default\"])(this, TrInner);\n    _this = _super.call(this, props);\n    var headers = props.headers;\n    if (headers && props.inHeader) {\n      _react[\"default\"].Children.map(props.children, function (child, i) {\n        if (child) {\n          headers[i] = child.props.children;\n        }\n      });\n    }\n    return _this;\n  }\n  (0, _createClass2[\"default\"])(TrInner, [{\n    key: \"render\",\n    value: function render() {\n      var children = this.props.children;\n      return /*#__PURE__*/_react[\"default\"].createElement(\"tr\", (0, _extends2[\"default\"])({\n        \"data-testid\": \"tr\"\n      }, (0, _allowed[\"default\"])(this.props)), children && _react[\"default\"].Children.map(children, function (child, i) {\n        return child && /*#__PURE__*/_react[\"default\"].cloneElement(child, {\n          // eslint-disable-next-line react/no-array-index-key\n          key: i,\n          columnKey: i\n        });\n      }));\n    }\n  }]);\n  return TrInner;\n}(_react[\"default\"].Component);\nTrInner.propTypes = {\n  children: _propTypes[\"default\"].node,\n  headers: _propTypes[\"default\"].shape({}),\n  inHeader: _propTypes[\"default\"].bool\n};\nTrInner.defaultProps = {\n  children: undefined,\n  headers: undefined,\n  inHeader: undefined\n};\nvar _default = TrInner;\nexports[\"default\"] = _default;", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "_extends2", "_classCallCheck2", "_createClass2", "_inherits2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_react", "_propTypes", "_allowed", "_createSuper", "Derived", "hasNativeReflectConstruct", "_isNativeReflectConstruct", "_createSuperInternal", "Super", "result", "<PERSON><PERSON><PERSON><PERSON>", "constructor", "Reflect", "construct", "arguments", "apply", "sham", "Proxy", "Boolean", "prototype", "valueOf", "call", "e", "TrInner", "_React$Component", "_super", "props", "_this", "headers", "inHeader", "Children", "map", "children", "child", "i", "key", "render", "createElement", "cloneElement", "column<PERSON>ey", "Component", "propTypes", "node", "shape", "bool", "defaultProps", "undefined", "_default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/react-super-responsive-table/dist/components/TrInner.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\n\nvar _classCallCheck2 = _interopRequireDefault(require(\"@babel/runtime/helpers/classCallCheck\"));\n\nvar _createClass2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createClass\"));\n\nvar _inherits2 = _interopRequireDefault(require(\"@babel/runtime/helpers/inherits\"));\n\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"@babel/runtime/helpers/possibleConstructorReturn\"));\n\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"@babel/runtime/helpers/getPrototypeOf\"));\n\nvar _react = _interopRequireDefault(require(\"react\"));\n\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\n\nvar _allowed = _interopRequireDefault(require(\"../utils/allowed\"));\n\nfunction _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = (0, _getPrototypeOf2[\"default\"])(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = (0, _getPrototypeOf2[\"default\"])(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return (0, _possibleConstructorReturn2[\"default\"])(this, result); }; }\n\nfunction _isNativeReflectConstruct() { if (typeof Reflect === \"undefined\" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === \"function\") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }\n\nvar TrInner = /*#__PURE__*/function (_React$Component) {\n  (0, _inherits2[\"default\"])(TrInner, _React$Component);\n\n  var _super = _createSuper(TrInner);\n\n  function TrInner(props) {\n    var _this;\n\n    (0, _classCallCheck2[\"default\"])(this, TrInner);\n    _this = _super.call(this, props);\n    var headers = props.headers;\n\n    if (headers && props.inHeader) {\n      _react[\"default\"].Children.map(props.children, function (child, i) {\n        if (child) {\n          headers[i] = child.props.children;\n        }\n      });\n    }\n\n    return _this;\n  }\n\n  (0, _createClass2[\"default\"])(TrInner, [{\n    key: \"render\",\n    value: function render() {\n      var children = this.props.children;\n      return /*#__PURE__*/_react[\"default\"].createElement(\"tr\", (0, _extends2[\"default\"])({\n        \"data-testid\": \"tr\"\n      }, (0, _allowed[\"default\"])(this.props)), children && _react[\"default\"].Children.map(children, function (child, i) {\n        return child && /*#__PURE__*/_react[\"default\"].cloneElement(child, {\n          // eslint-disable-next-line react/no-array-index-key\n          key: i,\n          columnKey: i\n        });\n      }));\n    }\n  }]);\n  return TrInner;\n}(_react[\"default\"].Component);\n\nTrInner.propTypes = {\n  children: _propTypes[\"default\"].node,\n  headers: _propTypes[\"default\"].shape({}),\n  inHeader: _propTypes[\"default\"].bool\n};\nTrInner.defaultProps = {\n  children: undefined,\n  headers: undefined,\n  inHeader: undefined\n};\nvar _default = TrInner;\nexports[\"default\"] = _default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC;AAEpFC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;AAE3B,IAAIE,SAAS,GAAGN,sBAAsB,CAACC,OAAO,CAAC,gCAAgC,CAAC,CAAC;AAEjF,IAAIM,gBAAgB,GAAGP,sBAAsB,CAACC,OAAO,CAAC,uCAAuC,CAAC,CAAC;AAE/F,IAAIO,aAAa,GAAGR,sBAAsB,CAACC,OAAO,CAAC,oCAAoC,CAAC,CAAC;AAEzF,IAAIQ,UAAU,GAAGT,sBAAsB,CAACC,OAAO,CAAC,iCAAiC,CAAC,CAAC;AAEnF,IAAIS,2BAA2B,GAAGV,sBAAsB,CAACC,OAAO,CAAC,kDAAkD,CAAC,CAAC;AAErH,IAAIU,gBAAgB,GAAGX,sBAAsB,CAACC,OAAO,CAAC,uCAAuC,CAAC,CAAC;AAE/F,IAAIW,MAAM,GAAGZ,sBAAsB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AAErD,IAAIY,UAAU,GAAGb,sBAAsB,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;AAE9D,IAAIa,QAAQ,GAAGd,sBAAsB,CAACC,OAAO,CAAC,kBAAkB,CAAC,CAAC;AAElE,SAASc,YAAYA,CAACC,OAAO,EAAE;EAAE,IAAIC,yBAAyB,GAAGC,yBAAyB,EAAE;EAAE,OAAO,SAASC,oBAAoBA,CAAA,EAAG;IAAE,IAAIC,KAAK,GAAG,CAAC,CAAC,EAAET,gBAAgB,CAAC,SAAS,CAAC,EAAEK,OAAO,CAAC;MAAEK,MAAM;IAAE,IAAIJ,yBAAyB,EAAE;MAAE,IAAIK,SAAS,GAAG,CAAC,CAAC,EAAEX,gBAAgB,CAAC,SAAS,CAAC,EAAE,IAAI,CAAC,CAACY,WAAW;MAAEF,MAAM,GAAGG,OAAO,CAACC,SAAS,CAACL,KAAK,EAAEM,SAAS,EAAEJ,SAAS,CAAC;IAAE,CAAC,MAAM;MAAED,MAAM,GAAGD,KAAK,CAACO,KAAK,CAAC,IAAI,EAAED,SAAS,CAAC;IAAE;IAAE,OAAO,CAAC,CAAC,EAAEhB,2BAA2B,CAAC,SAAS,CAAC,EAAE,IAAI,EAAEW,MAAM,CAAC;EAAE,CAAC;AAAE;AAE3d,SAASH,yBAAyBA,CAAA,EAAG;EAAE,IAAI,OAAOM,OAAO,KAAK,WAAW,IAAI,CAACA,OAAO,CAACC,SAAS,EAAE,OAAO,KAAK;EAAE,IAAID,OAAO,CAACC,SAAS,CAACG,IAAI,EAAE,OAAO,KAAK;EAAE,IAAI,OAAOC,KAAK,KAAK,UAAU,EAAE,OAAO,IAAI;EAAE,IAAI;IAAEC,OAAO,CAACC,SAAS,CAACC,OAAO,CAACC,IAAI,CAACT,OAAO,CAACC,SAAS,CAACK,OAAO,EAAE,EAAE,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;IAAE,OAAO,IAAI;EAAE,CAAC,CAAC,OAAOI,CAAC,EAAE;IAAE,OAAO,KAAK;EAAE;AAAE;AAExU,IAAIC,OAAO,GAAG,aAAa,UAAUC,gBAAgB,EAAE;EACrD,CAAC,CAAC,EAAE3B,UAAU,CAAC,SAAS,CAAC,EAAE0B,OAAO,EAAEC,gBAAgB,CAAC;EAErD,IAAIC,MAAM,GAAGtB,YAAY,CAACoB,OAAO,CAAC;EAElC,SAASA,OAAOA,CAACG,KAAK,EAAE;IACtB,IAAIC,KAAK;IAET,CAAC,CAAC,EAAEhC,gBAAgB,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE4B,OAAO,CAAC;IAC/CI,KAAK,GAAGF,MAAM,CAACJ,IAAI,CAAC,IAAI,EAAEK,KAAK,CAAC;IAChC,IAAIE,OAAO,GAAGF,KAAK,CAACE,OAAO;IAE3B,IAAIA,OAAO,IAAIF,KAAK,CAACG,QAAQ,EAAE;MAC7B7B,MAAM,CAAC,SAAS,CAAC,CAAC8B,QAAQ,CAACC,GAAG,CAACL,KAAK,CAACM,QAAQ,EAAE,UAAUC,KAAK,EAAEC,CAAC,EAAE;QACjE,IAAID,KAAK,EAAE;UACTL,OAAO,CAACM,CAAC,CAAC,GAAGD,KAAK,CAACP,KAAK,CAACM,QAAQ;QACnC;MACF,CAAC,CAAC;IACJ;IAEA,OAAOL,KAAK;EACd;EAEA,CAAC,CAAC,EAAE/B,aAAa,CAAC,SAAS,CAAC,EAAE2B,OAAO,EAAE,CAAC;IACtCY,GAAG,EAAE,QAAQ;IACb1C,KAAK,EAAE,SAAS2C,MAAMA,CAAA,EAAG;MACvB,IAAIJ,QAAQ,GAAG,IAAI,CAACN,KAAK,CAACM,QAAQ;MAClC,OAAO,aAAahC,MAAM,CAAC,SAAS,CAAC,CAACqC,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE3C,SAAS,CAAC,SAAS,CAAC,EAAE;QAClF,aAAa,EAAE;MACjB,CAAC,EAAE,CAAC,CAAC,EAAEQ,QAAQ,CAAC,SAAS,CAAC,EAAE,IAAI,CAACwB,KAAK,CAAC,CAAC,EAAEM,QAAQ,IAAIhC,MAAM,CAAC,SAAS,CAAC,CAAC8B,QAAQ,CAACC,GAAG,CAACC,QAAQ,EAAE,UAAUC,KAAK,EAAEC,CAAC,EAAE;QACjH,OAAOD,KAAK,IAAI,aAAajC,MAAM,CAAC,SAAS,CAAC,CAACsC,YAAY,CAACL,KAAK,EAAE;UACjE;UACAE,GAAG,EAAED,CAAC;UACNK,SAAS,EAAEL;QACb,CAAC,CAAC;MACJ,CAAC,CAAC,CAAC;IACL;EACF,CAAC,CAAC,CAAC;EACH,OAAOX,OAAO;AAChB,CAAC,CAACvB,MAAM,CAAC,SAAS,CAAC,CAACwC,SAAS,CAAC;AAE9BjB,OAAO,CAACkB,SAAS,GAAG;EAClBT,QAAQ,EAAE/B,UAAU,CAAC,SAAS,CAAC,CAACyC,IAAI;EACpCd,OAAO,EAAE3B,UAAU,CAAC,SAAS,CAAC,CAAC0C,KAAK,CAAC,CAAC,CAAC,CAAC;EACxCd,QAAQ,EAAE5B,UAAU,CAAC,SAAS,CAAC,CAAC2C;AAClC,CAAC;AACDrB,OAAO,CAACsB,YAAY,GAAG;EACrBb,QAAQ,EAAEc,SAAS;EACnBlB,OAAO,EAAEkB,SAAS;EAClBjB,QAAQ,EAAEiB;AACZ,CAAC;AACD,IAAIC,QAAQ,GAAGxB,OAAO;AACtB/B,OAAO,CAAC,SAAS,CAAC,GAAGuD,QAAQ"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}