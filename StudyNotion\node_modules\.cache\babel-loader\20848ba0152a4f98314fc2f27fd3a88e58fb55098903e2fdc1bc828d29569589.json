{"ast": null, "code": "export default function loopFix() {\n  let {\n    slideRealIndex,\n    slideTo = true,\n    direction,\n    setTranslate,\n    activeSlideIndex,\n    byController,\n    byMousewheel\n  } = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  const swiper = this;\n  if (!swiper.params.loop) return;\n  swiper.emit('beforeLoopFix');\n  const {\n    slides,\n    allowSlidePrev,\n    allowSlideNext,\n    slidesEl,\n    params\n  } = swiper;\n  swiper.allowSlidePrev = true;\n  swiper.allowSlideNext = true;\n  if (swiper.virtual && params.virtual.enabled) {\n    if (slideTo) {\n      if (!params.centeredSlides && swiper.snapIndex === 0) {\n        swiper.slideTo(swiper.virtual.slides.length, 0, false, true);\n      } else if (params.centeredSlides && swiper.snapIndex < params.slidesPerView) {\n        swiper.slideTo(swiper.virtual.slides.length + swiper.snapIndex, 0, false, true);\n      } else if (swiper.snapIndex === swiper.snapGrid.length - 1) {\n        swiper.slideTo(swiper.virtual.slidesBefore, 0, false, true);\n      }\n    }\n    swiper.allowSlidePrev = allowSlidePrev;\n    swiper.allowSlideNext = allowSlideNext;\n    swiper.emit('loopFix');\n    return;\n  }\n  const slidesPerView = params.slidesPerView === 'auto' ? swiper.slidesPerViewDynamic() : Math.ceil(parseFloat(params.slidesPerView, 10));\n  let loopedSlides = params.loopedSlides || slidesPerView;\n  if (loopedSlides % params.slidesPerGroup !== 0) {\n    loopedSlides += params.slidesPerGroup - loopedSlides % params.slidesPerGroup;\n  }\n  swiper.loopedSlides = loopedSlides;\n  const prependSlidesIndexes = [];\n  const appendSlidesIndexes = [];\n  let activeIndex = swiper.activeIndex;\n  if (typeof activeSlideIndex === 'undefined') {\n    activeSlideIndex = swiper.getSlideIndex(swiper.slides.filter(el => el.classList.contains(params.slideActiveClass))[0]);\n  } else {\n    activeIndex = activeSlideIndex;\n  }\n  const isNext = direction === 'next' || !direction;\n  const isPrev = direction === 'prev' || !direction;\n  let slidesPrepended = 0;\n  let slidesAppended = 0;\n  // prepend last slides before start\n  if (activeSlideIndex < loopedSlides) {\n    slidesPrepended = Math.max(loopedSlides - activeSlideIndex, params.slidesPerGroup);\n    for (let i = 0; i < loopedSlides - activeSlideIndex; i += 1) {\n      const index = i - Math.floor(i / slides.length) * slides.length;\n      prependSlidesIndexes.push(slides.length - index - 1);\n    }\n  } else if (activeSlideIndex /* + slidesPerView */ > swiper.slides.length - loopedSlides * 2) {\n    slidesAppended = Math.max(activeSlideIndex - (swiper.slides.length - loopedSlides * 2), params.slidesPerGroup);\n    for (let i = 0; i < slidesAppended; i += 1) {\n      const index = i - Math.floor(i / slides.length) * slides.length;\n      appendSlidesIndexes.push(index);\n    }\n  }\n  if (isPrev) {\n    prependSlidesIndexes.forEach(index => {\n      swiper.slides[index].swiperLoopMoveDOM = true;\n      slidesEl.prepend(swiper.slides[index]);\n      swiper.slides[index].swiperLoopMoveDOM = false;\n    });\n  }\n  if (isNext) {\n    appendSlidesIndexes.forEach(index => {\n      swiper.slides[index].swiperLoopMoveDOM = true;\n      slidesEl.append(swiper.slides[index]);\n      swiper.slides[index].swiperLoopMoveDOM = false;\n    });\n  }\n  swiper.recalcSlides();\n  if (params.slidesPerView === 'auto') {\n    swiper.updateSlides();\n  }\n  if (params.watchSlidesProgress) {\n    swiper.updateSlidesOffset();\n  }\n  if (slideTo) {\n    if (prependSlidesIndexes.length > 0 && isPrev) {\n      if (typeof slideRealIndex === 'undefined') {\n        const currentSlideTranslate = swiper.slidesGrid[activeIndex];\n        const newSlideTranslate = swiper.slidesGrid[activeIndex + slidesPrepended];\n        const diff = newSlideTranslate - currentSlideTranslate;\n        if (byMousewheel) {\n          swiper.setTranslate(swiper.translate - diff);\n        } else {\n          swiper.slideTo(activeIndex + slidesPrepended, 0, false, true);\n          if (setTranslate) {\n            swiper.touches[swiper.isHorizontal() ? 'startX' : 'startY'] += diff;\n          }\n        }\n      } else {\n        if (setTranslate) {\n          swiper.slideToLoop(slideRealIndex, 0, false, true);\n        }\n      }\n    } else if (appendSlidesIndexes.length > 0 && isNext) {\n      if (typeof slideRealIndex === 'undefined') {\n        const currentSlideTranslate = swiper.slidesGrid[activeIndex];\n        const newSlideTranslate = swiper.slidesGrid[activeIndex - slidesAppended];\n        const diff = newSlideTranslate - currentSlideTranslate;\n        if (byMousewheel) {\n          swiper.setTranslate(swiper.translate - diff);\n        } else {\n          swiper.slideTo(activeIndex - slidesAppended, 0, false, true);\n          if (setTranslate) {\n            swiper.touches[swiper.isHorizontal() ? 'startX' : 'startY'] += diff;\n          }\n        }\n      } else {\n        swiper.slideToLoop(slideRealIndex, 0, false, true);\n      }\n    }\n  }\n  swiper.allowSlidePrev = allowSlidePrev;\n  swiper.allowSlideNext = allowSlideNext;\n  if (swiper.controller && swiper.controller.control && !byController) {\n    const loopParams = {\n      slideRealIndex,\n      slideTo: false,\n      direction,\n      setTranslate,\n      activeSlideIndex,\n      byController: true\n    };\n    if (Array.isArray(swiper.controller.control)) {\n      swiper.controller.control.forEach(c => {\n        if (!c.destroyed && c.params.loop) c.loopFix(loopParams);\n      });\n    } else if (swiper.controller.control instanceof swiper.constructor && swiper.controller.control.params.loop) {\n      swiper.controller.control.loopFix(loopParams);\n    }\n  }\n  swiper.emit('loopFix');\n}", "map": {"version": 3, "names": ["loopFix", "slideRealIndex", "slideTo", "direction", "setTranslate", "activeSlideIndex", "byController", "byMousewheel", "arguments", "length", "undefined", "swiper", "params", "loop", "emit", "slides", "allowSlidePrev", "allowSlideNext", "slidesEl", "virtual", "enabled", "centeredSlides", "snapIndex", "<PERSON><PERSON><PERSON><PERSON>iew", "snapGrid", "slidesBefore", "slidesPerViewDynamic", "Math", "ceil", "parseFloat", "loopedSlides", "slidesPerGroup", "prependSlidesIndexes", "appendSlidesIndexes", "activeIndex", "getSlideIndex", "filter", "el", "classList", "contains", "slideActiveClass", "isNext", "isPrev", "slidesPrepended", "slidesAppended", "max", "i", "index", "floor", "push", "for<PERSON>ach", "swiperLoopMoveDOM", "prepend", "append", "recalcSlides", "updateSlides", "watchSlidesProgress", "updateSlidesOffset", "currentSlideTranslate", "slidesGrid", "newSlideTranslate", "diff", "translate", "touches", "isHorizontal", "slideToLoop", "controller", "control", "loopParams", "Array", "isArray", "c", "destroyed", "constructor"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/core/loop/loopFix.js"], "sourcesContent": ["export default function loopFix({\n  slideRealIndex,\n  slideTo = true,\n  direction,\n  setTranslate,\n  activeSlideIndex,\n  byC<PERSON><PERSON>er,\n  byMousewheel\n} = {}) {\n  const swiper = this;\n  if (!swiper.params.loop) return;\n  swiper.emit('beforeLoopFix');\n  const {\n    slides,\n    allowSlidePrev,\n    allowSlideNext,\n    slidesEl,\n    params\n  } = swiper;\n  swiper.allowSlidePrev = true;\n  swiper.allowSlideNext = true;\n  if (swiper.virtual && params.virtual.enabled) {\n    if (slideTo) {\n      if (!params.centeredSlides && swiper.snapIndex === 0) {\n        swiper.slideTo(swiper.virtual.slides.length, 0, false, true);\n      } else if (params.centeredSlides && swiper.snapIndex < params.slidesPerView) {\n        swiper.slideTo(swiper.virtual.slides.length + swiper.snapIndex, 0, false, true);\n      } else if (swiper.snapIndex === swiper.snapGrid.length - 1) {\n        swiper.slideTo(swiper.virtual.slidesBefore, 0, false, true);\n      }\n    }\n    swiper.allowSlidePrev = allowSlidePrev;\n    swiper.allowSlideNext = allowSlideNext;\n    swiper.emit('loopFix');\n    return;\n  }\n  const slidesPerView = params.slidesPerView === 'auto' ? swiper.slidesPerViewDynamic() : Math.ceil(parseFloat(params.slidesPerView, 10));\n  let loopedSlides = params.loopedSlides || slidesPerView;\n  if (loopedSlides % params.slidesPerGroup !== 0) {\n    loopedSlides += params.slidesPerGroup - loopedSlides % params.slidesPerGroup;\n  }\n  swiper.loopedSlides = loopedSlides;\n  const prependSlidesIndexes = [];\n  const appendSlidesIndexes = [];\n  let activeIndex = swiper.activeIndex;\n  if (typeof activeSlideIndex === 'undefined') {\n    activeSlideIndex = swiper.getSlideIndex(swiper.slides.filter(el => el.classList.contains(params.slideActiveClass))[0]);\n  } else {\n    activeIndex = activeSlideIndex;\n  }\n  const isNext = direction === 'next' || !direction;\n  const isPrev = direction === 'prev' || !direction;\n  let slidesPrepended = 0;\n  let slidesAppended = 0;\n  // prepend last slides before start\n  if (activeSlideIndex < loopedSlides) {\n    slidesPrepended = Math.max(loopedSlides - activeSlideIndex, params.slidesPerGroup);\n    for (let i = 0; i < loopedSlides - activeSlideIndex; i += 1) {\n      const index = i - Math.floor(i / slides.length) * slides.length;\n      prependSlidesIndexes.push(slides.length - index - 1);\n    }\n  } else if (activeSlideIndex /* + slidesPerView */ > swiper.slides.length - loopedSlides * 2) {\n    slidesAppended = Math.max(activeSlideIndex - (swiper.slides.length - loopedSlides * 2), params.slidesPerGroup);\n    for (let i = 0; i < slidesAppended; i += 1) {\n      const index = i - Math.floor(i / slides.length) * slides.length;\n      appendSlidesIndexes.push(index);\n    }\n  }\n  if (isPrev) {\n    prependSlidesIndexes.forEach(index => {\n      swiper.slides[index].swiperLoopMoveDOM = true;\n      slidesEl.prepend(swiper.slides[index]);\n      swiper.slides[index].swiperLoopMoveDOM = false;\n    });\n  }\n  if (isNext) {\n    appendSlidesIndexes.forEach(index => {\n      swiper.slides[index].swiperLoopMoveDOM = true;\n      slidesEl.append(swiper.slides[index]);\n      swiper.slides[index].swiperLoopMoveDOM = false;\n    });\n  }\n  swiper.recalcSlides();\n  if (params.slidesPerView === 'auto') {\n    swiper.updateSlides();\n  }\n  if (params.watchSlidesProgress) {\n    swiper.updateSlidesOffset();\n  }\n  if (slideTo) {\n    if (prependSlidesIndexes.length > 0 && isPrev) {\n      if (typeof slideRealIndex === 'undefined') {\n        const currentSlideTranslate = swiper.slidesGrid[activeIndex];\n        const newSlideTranslate = swiper.slidesGrid[activeIndex + slidesPrepended];\n        const diff = newSlideTranslate - currentSlideTranslate;\n        if (byMousewheel) {\n          swiper.setTranslate(swiper.translate - diff);\n        } else {\n          swiper.slideTo(activeIndex + slidesPrepended, 0, false, true);\n          if (setTranslate) {\n            swiper.touches[swiper.isHorizontal() ? 'startX' : 'startY'] += diff;\n          }\n        }\n      } else {\n        if (setTranslate) {\n          swiper.slideToLoop(slideRealIndex, 0, false, true);\n        }\n      }\n    } else if (appendSlidesIndexes.length > 0 && isNext) {\n      if (typeof slideRealIndex === 'undefined') {\n        const currentSlideTranslate = swiper.slidesGrid[activeIndex];\n        const newSlideTranslate = swiper.slidesGrid[activeIndex - slidesAppended];\n        const diff = newSlideTranslate - currentSlideTranslate;\n        if (byMousewheel) {\n          swiper.setTranslate(swiper.translate - diff);\n        } else {\n          swiper.slideTo(activeIndex - slidesAppended, 0, false, true);\n          if (setTranslate) {\n            swiper.touches[swiper.isHorizontal() ? 'startX' : 'startY'] += diff;\n          }\n        }\n      } else {\n        swiper.slideToLoop(slideRealIndex, 0, false, true);\n      }\n    }\n  }\n  swiper.allowSlidePrev = allowSlidePrev;\n  swiper.allowSlideNext = allowSlideNext;\n  if (swiper.controller && swiper.controller.control && !byController) {\n    const loopParams = {\n      slideRealIndex,\n      slideTo: false,\n      direction,\n      setTranslate,\n      activeSlideIndex,\n      byController: true\n    };\n    if (Array.isArray(swiper.controller.control)) {\n      swiper.controller.control.forEach(c => {\n        if (!c.destroyed && c.params.loop) c.loopFix(loopParams);\n      });\n    } else if (swiper.controller.control instanceof swiper.constructor && swiper.controller.control.params.loop) {\n      swiper.controller.control.loopFix(loopParams);\n    }\n  }\n  swiper.emit('loopFix');\n}"], "mappings": "AAAA,eAAe,SAASA,OAAOA,CAAA,EAQvB;EAAA,IARwB;IAC9BC,cAAc;IACdC,OAAO,GAAG,IAAI;IACdC,SAAS;IACTC,YAAY;IACZC,gBAAgB;IAChBC,YAAY;IACZC;EACF,CAAC,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EACJ,MAAMG,MAAM,GAAG,IAAI;EACnB,IAAI,CAACA,MAAM,CAACC,MAAM,CAACC,IAAI,EAAE;EACzBF,MAAM,CAACG,IAAI,CAAC,eAAe,CAAC;EAC5B,MAAM;IACJC,MAAM;IACNC,cAAc;IACdC,cAAc;IACdC,QAAQ;IACRN;EACF,CAAC,GAAGD,MAAM;EACVA,MAAM,CAACK,cAAc,GAAG,IAAI;EAC5BL,MAAM,CAACM,cAAc,GAAG,IAAI;EAC5B,IAAIN,MAAM,CAACQ,OAAO,IAAIP,MAAM,CAACO,OAAO,CAACC,OAAO,EAAE;IAC5C,IAAIlB,OAAO,EAAE;MACX,IAAI,CAACU,MAAM,CAACS,cAAc,IAAIV,MAAM,CAACW,SAAS,KAAK,CAAC,EAAE;QACpDX,MAAM,CAACT,OAAO,CAACS,MAAM,CAACQ,OAAO,CAACJ,MAAM,CAACN,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;MAC9D,CAAC,MAAM,IAAIG,MAAM,CAACS,cAAc,IAAIV,MAAM,CAACW,SAAS,GAAGV,MAAM,CAACW,aAAa,EAAE;QAC3EZ,MAAM,CAACT,OAAO,CAACS,MAAM,CAACQ,OAAO,CAACJ,MAAM,CAACN,MAAM,GAAGE,MAAM,CAACW,SAAS,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;MACjF,CAAC,MAAM,IAAIX,MAAM,CAACW,SAAS,KAAKX,MAAM,CAACa,QAAQ,CAACf,MAAM,GAAG,CAAC,EAAE;QAC1DE,MAAM,CAACT,OAAO,CAACS,MAAM,CAACQ,OAAO,CAACM,YAAY,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;MAC7D;IACF;IACAd,MAAM,CAACK,cAAc,GAAGA,cAAc;IACtCL,MAAM,CAACM,cAAc,GAAGA,cAAc;IACtCN,MAAM,CAACG,IAAI,CAAC,SAAS,CAAC;IACtB;EACF;EACA,MAAMS,aAAa,GAAGX,MAAM,CAACW,aAAa,KAAK,MAAM,GAAGZ,MAAM,CAACe,oBAAoB,EAAE,GAAGC,IAAI,CAACC,IAAI,CAACC,UAAU,CAACjB,MAAM,CAACW,aAAa,EAAE,EAAE,CAAC,CAAC;EACvI,IAAIO,YAAY,GAAGlB,MAAM,CAACkB,YAAY,IAAIP,aAAa;EACvD,IAAIO,YAAY,GAAGlB,MAAM,CAACmB,cAAc,KAAK,CAAC,EAAE;IAC9CD,YAAY,IAAIlB,MAAM,CAACmB,cAAc,GAAGD,YAAY,GAAGlB,MAAM,CAACmB,cAAc;EAC9E;EACApB,MAAM,CAACmB,YAAY,GAAGA,YAAY;EAClC,MAAME,oBAAoB,GAAG,EAAE;EAC/B,MAAMC,mBAAmB,GAAG,EAAE;EAC9B,IAAIC,WAAW,GAAGvB,MAAM,CAACuB,WAAW;EACpC,IAAI,OAAO7B,gBAAgB,KAAK,WAAW,EAAE;IAC3CA,gBAAgB,GAAGM,MAAM,CAACwB,aAAa,CAACxB,MAAM,CAACI,MAAM,CAACqB,MAAM,CAACC,EAAE,IAAIA,EAAE,CAACC,SAAS,CAACC,QAAQ,CAAC3B,MAAM,CAAC4B,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACxH,CAAC,MAAM;IACLN,WAAW,GAAG7B,gBAAgB;EAChC;EACA,MAAMoC,MAAM,GAAGtC,SAAS,KAAK,MAAM,IAAI,CAACA,SAAS;EACjD,MAAMuC,MAAM,GAAGvC,SAAS,KAAK,MAAM,IAAI,CAACA,SAAS;EACjD,IAAIwC,eAAe,GAAG,CAAC;EACvB,IAAIC,cAAc,GAAG,CAAC;EACtB;EACA,IAAIvC,gBAAgB,GAAGyB,YAAY,EAAE;IACnCa,eAAe,GAAGhB,IAAI,CAACkB,GAAG,CAACf,YAAY,GAAGzB,gBAAgB,EAAEO,MAAM,CAACmB,cAAc,CAAC;IAClF,KAAK,IAAIe,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGhB,YAAY,GAAGzB,gBAAgB,EAAEyC,CAAC,IAAI,CAAC,EAAE;MAC3D,MAAMC,KAAK,GAAGD,CAAC,GAAGnB,IAAI,CAACqB,KAAK,CAACF,CAAC,GAAG/B,MAAM,CAACN,MAAM,CAAC,GAAGM,MAAM,CAACN,MAAM;MAC/DuB,oBAAoB,CAACiB,IAAI,CAAClC,MAAM,CAACN,MAAM,GAAGsC,KAAK,GAAG,CAAC,CAAC;IACtD;EACF,CAAC,MAAM,IAAI1C,gBAAgB,CAAC,wBAAwBM,MAAM,CAACI,MAAM,CAACN,MAAM,GAAGqB,YAAY,GAAG,CAAC,EAAE;IAC3Fc,cAAc,GAAGjB,IAAI,CAACkB,GAAG,CAACxC,gBAAgB,IAAIM,MAAM,CAACI,MAAM,CAACN,MAAM,GAAGqB,YAAY,GAAG,CAAC,CAAC,EAAElB,MAAM,CAACmB,cAAc,CAAC;IAC9G,KAAK,IAAIe,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,cAAc,EAAEE,CAAC,IAAI,CAAC,EAAE;MAC1C,MAAMC,KAAK,GAAGD,CAAC,GAAGnB,IAAI,CAACqB,KAAK,CAACF,CAAC,GAAG/B,MAAM,CAACN,MAAM,CAAC,GAAGM,MAAM,CAACN,MAAM;MAC/DwB,mBAAmB,CAACgB,IAAI,CAACF,KAAK,CAAC;IACjC;EACF;EACA,IAAIL,MAAM,EAAE;IACVV,oBAAoB,CAACkB,OAAO,CAACH,KAAK,IAAI;MACpCpC,MAAM,CAACI,MAAM,CAACgC,KAAK,CAAC,CAACI,iBAAiB,GAAG,IAAI;MAC7CjC,QAAQ,CAACkC,OAAO,CAACzC,MAAM,CAACI,MAAM,CAACgC,KAAK,CAAC,CAAC;MACtCpC,MAAM,CAACI,MAAM,CAACgC,KAAK,CAAC,CAACI,iBAAiB,GAAG,KAAK;IAChD,CAAC,CAAC;EACJ;EACA,IAAIV,MAAM,EAAE;IACVR,mBAAmB,CAACiB,OAAO,CAACH,KAAK,IAAI;MACnCpC,MAAM,CAACI,MAAM,CAACgC,KAAK,CAAC,CAACI,iBAAiB,GAAG,IAAI;MAC7CjC,QAAQ,CAACmC,MAAM,CAAC1C,MAAM,CAACI,MAAM,CAACgC,KAAK,CAAC,CAAC;MACrCpC,MAAM,CAACI,MAAM,CAACgC,KAAK,CAAC,CAACI,iBAAiB,GAAG,KAAK;IAChD,CAAC,CAAC;EACJ;EACAxC,MAAM,CAAC2C,YAAY,EAAE;EACrB,IAAI1C,MAAM,CAACW,aAAa,KAAK,MAAM,EAAE;IACnCZ,MAAM,CAAC4C,YAAY,EAAE;EACvB;EACA,IAAI3C,MAAM,CAAC4C,mBAAmB,EAAE;IAC9B7C,MAAM,CAAC8C,kBAAkB,EAAE;EAC7B;EACA,IAAIvD,OAAO,EAAE;IACX,IAAI8B,oBAAoB,CAACvB,MAAM,GAAG,CAAC,IAAIiC,MAAM,EAAE;MAC7C,IAAI,OAAOzC,cAAc,KAAK,WAAW,EAAE;QACzC,MAAMyD,qBAAqB,GAAG/C,MAAM,CAACgD,UAAU,CAACzB,WAAW,CAAC;QAC5D,MAAM0B,iBAAiB,GAAGjD,MAAM,CAACgD,UAAU,CAACzB,WAAW,GAAGS,eAAe,CAAC;QAC1E,MAAMkB,IAAI,GAAGD,iBAAiB,GAAGF,qBAAqB;QACtD,IAAInD,YAAY,EAAE;UAChBI,MAAM,CAACP,YAAY,CAACO,MAAM,CAACmD,SAAS,GAAGD,IAAI,CAAC;QAC9C,CAAC,MAAM;UACLlD,MAAM,CAACT,OAAO,CAACgC,WAAW,GAAGS,eAAe,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;UAC7D,IAAIvC,YAAY,EAAE;YAChBO,MAAM,CAACoD,OAAO,CAACpD,MAAM,CAACqD,YAAY,EAAE,GAAG,QAAQ,GAAG,QAAQ,CAAC,IAAIH,IAAI;UACrE;QACF;MACF,CAAC,MAAM;QACL,IAAIzD,YAAY,EAAE;UAChBO,MAAM,CAACsD,WAAW,CAAChE,cAAc,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;QACpD;MACF;IACF,CAAC,MAAM,IAAIgC,mBAAmB,CAACxB,MAAM,GAAG,CAAC,IAAIgC,MAAM,EAAE;MACnD,IAAI,OAAOxC,cAAc,KAAK,WAAW,EAAE;QACzC,MAAMyD,qBAAqB,GAAG/C,MAAM,CAACgD,UAAU,CAACzB,WAAW,CAAC;QAC5D,MAAM0B,iBAAiB,GAAGjD,MAAM,CAACgD,UAAU,CAACzB,WAAW,GAAGU,cAAc,CAAC;QACzE,MAAMiB,IAAI,GAAGD,iBAAiB,GAAGF,qBAAqB;QACtD,IAAInD,YAAY,EAAE;UAChBI,MAAM,CAACP,YAAY,CAACO,MAAM,CAACmD,SAAS,GAAGD,IAAI,CAAC;QAC9C,CAAC,MAAM;UACLlD,MAAM,CAACT,OAAO,CAACgC,WAAW,GAAGU,cAAc,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;UAC5D,IAAIxC,YAAY,EAAE;YAChBO,MAAM,CAACoD,OAAO,CAACpD,MAAM,CAACqD,YAAY,EAAE,GAAG,QAAQ,GAAG,QAAQ,CAAC,IAAIH,IAAI;UACrE;QACF;MACF,CAAC,MAAM;QACLlD,MAAM,CAACsD,WAAW,CAAChE,cAAc,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;MACpD;IACF;EACF;EACAU,MAAM,CAACK,cAAc,GAAGA,cAAc;EACtCL,MAAM,CAACM,cAAc,GAAGA,cAAc;EACtC,IAAIN,MAAM,CAACuD,UAAU,IAAIvD,MAAM,CAACuD,UAAU,CAACC,OAAO,IAAI,CAAC7D,YAAY,EAAE;IACnE,MAAM8D,UAAU,GAAG;MACjBnE,cAAc;MACdC,OAAO,EAAE,KAAK;MACdC,SAAS;MACTC,YAAY;MACZC,gBAAgB;MAChBC,YAAY,EAAE;IAChB,CAAC;IACD,IAAI+D,KAAK,CAACC,OAAO,CAAC3D,MAAM,CAACuD,UAAU,CAACC,OAAO,CAAC,EAAE;MAC5CxD,MAAM,CAACuD,UAAU,CAACC,OAAO,CAACjB,OAAO,CAACqB,CAAC,IAAI;QACrC,IAAI,CAACA,CAAC,CAACC,SAAS,IAAID,CAAC,CAAC3D,MAAM,CAACC,IAAI,EAAE0D,CAAC,CAACvE,OAAO,CAACoE,UAAU,CAAC;MAC1D,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIzD,MAAM,CAACuD,UAAU,CAACC,OAAO,YAAYxD,MAAM,CAAC8D,WAAW,IAAI9D,MAAM,CAACuD,UAAU,CAACC,OAAO,CAACvD,MAAM,CAACC,IAAI,EAAE;MAC3GF,MAAM,CAACuD,UAAU,CAACC,OAAO,CAACnE,OAAO,CAACoE,UAAU,CAAC;IAC/C;EACF;EACAzD,MAAM,CAACG,IAAI,CAAC,SAAS,CAAC;AACxB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}