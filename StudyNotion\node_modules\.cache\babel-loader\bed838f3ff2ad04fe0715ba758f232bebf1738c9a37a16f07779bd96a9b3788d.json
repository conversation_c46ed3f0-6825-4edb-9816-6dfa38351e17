{"ast": null, "code": "export default function transitionEmit(_ref) {\n  let {\n    swiper,\n    runCallbacks,\n    direction,\n    step\n  } = _ref;\n  const {\n    activeIndex,\n    previousIndex\n  } = swiper;\n  let dir = direction;\n  if (!dir) {\n    if (activeIndex > previousIndex) dir = 'next';else if (activeIndex < previousIndex) dir = 'prev';else dir = 'reset';\n  }\n  swiper.emit(`transition${step}`);\n  if (runCallbacks && activeIndex !== previousIndex) {\n    if (dir === 'reset') {\n      swiper.emit(`slideResetTransition${step}`);\n      return;\n    }\n    swiper.emit(`slideChangeTransition${step}`);\n    if (dir === 'next') {\n      swiper.emit(`slideNextTransition${step}`);\n    } else {\n      swiper.emit(`slidePrevTransition${step}`);\n    }\n  }\n}", "map": {"version": 3, "names": ["transitionEmit", "_ref", "swiper", "runCallbacks", "direction", "step", "activeIndex", "previousIndex", "dir", "emit"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/core/transition/transitionEmit.js"], "sourcesContent": ["export default function transitionEmit({\n  swiper,\n  runCallbacks,\n  direction,\n  step\n}) {\n  const {\n    activeIndex,\n    previousIndex\n  } = swiper;\n  let dir = direction;\n  if (!dir) {\n    if (activeIndex > previousIndex) dir = 'next';else if (activeIndex < previousIndex) dir = 'prev';else dir = 'reset';\n  }\n  swiper.emit(`transition${step}`);\n  if (runCallbacks && activeIndex !== previousIndex) {\n    if (dir === 'reset') {\n      swiper.emit(`slideResetTransition${step}`);\n      return;\n    }\n    swiper.emit(`slideChangeTransition${step}`);\n    if (dir === 'next') {\n      swiper.emit(`slideNextTransition${step}`);\n    } else {\n      swiper.emit(`slidePrevTransition${step}`);\n    }\n  }\n}"], "mappings": "AAAA,eAAe,SAASA,cAAcA,CAAAC,IAAA,EAKnC;EAAA,IALoC;IACrCC,MAAM;IACNC,YAAY;IACZC,SAAS;IACTC;EACF,CAAC,GAAAJ,IAAA;EACC,MAAM;IACJK,WAAW;IACXC;EACF,CAAC,GAAGL,MAAM;EACV,IAAIM,GAAG,GAAGJ,SAAS;EACnB,IAAI,CAACI,GAAG,EAAE;IACR,IAAIF,WAAW,GAAGC,aAAa,EAAEC,GAAG,GAAG,MAAM,CAAC,KAAK,IAAIF,WAAW,GAAGC,aAAa,EAAEC,GAAG,GAAG,MAAM,CAAC,KAAKA,GAAG,GAAG,OAAO;EACrH;EACAN,MAAM,CAACO,IAAI,CAAE,aAAYJ,IAAK,EAAC,CAAC;EAChC,IAAIF,YAAY,IAAIG,WAAW,KAAKC,aAAa,EAAE;IACjD,IAAIC,GAAG,KAAK,OAAO,EAAE;MACnBN,MAAM,CAACO,IAAI,CAAE,uBAAsBJ,IAAK,EAAC,CAAC;MAC1C;IACF;IACAH,MAAM,CAACO,IAAI,CAAE,wBAAuBJ,IAAK,EAAC,CAAC;IAC3C,IAAIG,GAAG,KAAK,MAAM,EAAE;MAClBN,MAAM,CAACO,IAAI,CAAE,sBAAqBJ,IAAK,EAAC,CAAC;IAC3C,CAAC,MAAM;MACLH,MAAM,CAACO,IAAI,CAAE,sBAAqBJ,IAAK,EAAC,CAAC;IAC3C;EACF;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}