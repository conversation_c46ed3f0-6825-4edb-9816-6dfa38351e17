{"ast": null, "code": "import { getDocument } from 'ssr-window';\nimport { now } from '../../shared/utils.js';\nexport default function onTouchMove(event) {\n  const document = getDocument();\n  const swiper = this;\n  const data = swiper.touchEventsData;\n  const {\n    params,\n    touches,\n    rtlTranslate: rtl,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n  if (!params.simulateTouch && event.pointerType === 'mouse') return;\n  let e = event;\n  if (e.originalEvent) e = e.originalEvent;\n  if (!data.isTouched) {\n    if (data.startMoving && data.isScrolling) {\n      swiper.emit('touchMoveOpposite', e);\n    }\n    return;\n  }\n  const pointerIndex = data.evCache.findIndex(cachedEv => cachedEv.pointerId === e.pointerId);\n  if (pointerIndex >= 0) data.evCache[pointerIndex] = e;\n  const targetTouch = data.evCache.length > 1 ? data.evCache[0] : e;\n  const pageX = targetTouch.pageX;\n  const pageY = targetTouch.pageY;\n  if (e.preventedByNestedSwiper) {\n    touches.startX = pageX;\n    touches.startY = pageY;\n    return;\n  }\n  if (!swiper.allowTouchMove) {\n    if (!e.target.matches(data.focusableElements)) {\n      swiper.allowClick = false;\n    }\n    if (data.isTouched) {\n      Object.assign(touches, {\n        startX: pageX,\n        startY: pageY,\n        prevX: swiper.touches.currentX,\n        prevY: swiper.touches.currentY,\n        currentX: pageX,\n        currentY: pageY\n      });\n      data.touchStartTime = now();\n    }\n    return;\n  }\n  if (params.touchReleaseOnEdges && !params.loop) {\n    if (swiper.isVertical()) {\n      // Vertical\n      if (pageY < touches.startY && swiper.translate <= swiper.maxTranslate() || pageY > touches.startY && swiper.translate >= swiper.minTranslate()) {\n        data.isTouched = false;\n        data.isMoved = false;\n        return;\n      }\n    } else if (pageX < touches.startX && swiper.translate <= swiper.maxTranslate() || pageX > touches.startX && swiper.translate >= swiper.minTranslate()) {\n      return;\n    }\n  }\n  if (document.activeElement) {\n    if (e.target === document.activeElement && e.target.matches(data.focusableElements)) {\n      data.isMoved = true;\n      swiper.allowClick = false;\n      return;\n    }\n  }\n  if (data.allowTouchCallbacks) {\n    swiper.emit('touchMove', e);\n  }\n  if (e.targetTouches && e.targetTouches.length > 1) return;\n  touches.currentX = pageX;\n  touches.currentY = pageY;\n  const diffX = touches.currentX - touches.startX;\n  const diffY = touches.currentY - touches.startY;\n  if (swiper.params.threshold && Math.sqrt(diffX ** 2 + diffY ** 2) < swiper.params.threshold) return;\n  if (typeof data.isScrolling === 'undefined') {\n    let touchAngle;\n    if (swiper.isHorizontal() && touches.currentY === touches.startY || swiper.isVertical() && touches.currentX === touches.startX) {\n      data.isScrolling = false;\n    } else {\n      // eslint-disable-next-line\n      if (diffX * diffX + diffY * diffY >= 25) {\n        touchAngle = Math.atan2(Math.abs(diffY), Math.abs(diffX)) * 180 / Math.PI;\n        data.isScrolling = swiper.isHorizontal() ? touchAngle > params.touchAngle : 90 - touchAngle > params.touchAngle;\n      }\n    }\n  }\n  if (data.isScrolling) {\n    swiper.emit('touchMoveOpposite', e);\n  }\n  if (typeof data.startMoving === 'undefined') {\n    if (touches.currentX !== touches.startX || touches.currentY !== touches.startY) {\n      data.startMoving = true;\n    }\n  }\n  if (data.isScrolling || swiper.zoom && swiper.params.zoom && swiper.params.zoom.enabled && data.evCache.length > 1) {\n    data.isTouched = false;\n    return;\n  }\n  if (!data.startMoving) {\n    return;\n  }\n  swiper.allowClick = false;\n  if (!params.cssMode && e.cancelable) {\n    e.preventDefault();\n  }\n  if (params.touchMoveStopPropagation && !params.nested) {\n    e.stopPropagation();\n  }\n  let diff = swiper.isHorizontal() ? diffX : diffY;\n  let touchesDiff = swiper.isHorizontal() ? touches.currentX - touches.previousX : touches.currentY - touches.previousY;\n  if (params.oneWayMovement) {\n    diff = Math.abs(diff) * (rtl ? 1 : -1);\n    touchesDiff = Math.abs(touchesDiff) * (rtl ? 1 : -1);\n  }\n  touches.diff = diff;\n  diff *= params.touchRatio;\n  if (rtl) {\n    diff = -diff;\n    touchesDiff = -touchesDiff;\n  }\n  const prevTouchesDirection = swiper.touchesDirection;\n  swiper.swipeDirection = diff > 0 ? 'prev' : 'next';\n  swiper.touchesDirection = touchesDiff > 0 ? 'prev' : 'next';\n  const isLoop = swiper.params.loop && !params.cssMode;\n  if (!data.isMoved) {\n    if (isLoop) {\n      swiper.loopFix({\n        direction: swiper.swipeDirection\n      });\n    }\n    data.startTranslate = swiper.getTranslate();\n    swiper.setTransition(0);\n    if (swiper.animating) {\n      const evt = new window.CustomEvent('transitionend', {\n        bubbles: true,\n        cancelable: true\n      });\n      swiper.wrapperEl.dispatchEvent(evt);\n    }\n    data.allowMomentumBounce = false;\n    // Grab Cursor\n    if (params.grabCursor && (swiper.allowSlideNext === true || swiper.allowSlidePrev === true)) {\n      swiper.setGrabCursor(true);\n    }\n    swiper.emit('sliderFirstMove', e);\n  }\n  let loopFixed;\n  if (data.isMoved && prevTouchesDirection !== swiper.touchesDirection && isLoop && Math.abs(diff) >= 1) {\n    // need another loop fix\n    swiper.loopFix({\n      direction: swiper.swipeDirection,\n      setTranslate: true\n    });\n    loopFixed = true;\n  }\n  swiper.emit('sliderMove', e);\n  data.isMoved = true;\n  data.currentTranslate = diff + data.startTranslate;\n  let disableParentSwiper = true;\n  let resistanceRatio = params.resistanceRatio;\n  if (params.touchReleaseOnEdges) {\n    resistanceRatio = 0;\n  }\n  if (diff > 0) {\n    if (isLoop && !loopFixed && data.currentTranslate > (params.centeredSlides ? swiper.minTranslate() - swiper.size / 2 : swiper.minTranslate())) {\n      swiper.loopFix({\n        direction: 'prev',\n        setTranslate: true,\n        activeSlideIndex: 0\n      });\n    }\n    if (data.currentTranslate > swiper.minTranslate()) {\n      disableParentSwiper = false;\n      if (params.resistance) {\n        data.currentTranslate = swiper.minTranslate() - 1 + (-swiper.minTranslate() + data.startTranslate + diff) ** resistanceRatio;\n      }\n    }\n  } else if (diff < 0) {\n    if (isLoop && !loopFixed && data.currentTranslate < (params.centeredSlides ? swiper.maxTranslate() + swiper.size / 2 : swiper.maxTranslate())) {\n      swiper.loopFix({\n        direction: 'next',\n        setTranslate: true,\n        activeSlideIndex: swiper.slides.length - (params.slidesPerView === 'auto' ? swiper.slidesPerViewDynamic() : Math.ceil(parseFloat(params.slidesPerView, 10)))\n      });\n    }\n    if (data.currentTranslate < swiper.maxTranslate()) {\n      disableParentSwiper = false;\n      if (params.resistance) {\n        data.currentTranslate = swiper.maxTranslate() + 1 - (swiper.maxTranslate() - data.startTranslate - diff) ** resistanceRatio;\n      }\n    }\n  }\n  if (disableParentSwiper) {\n    e.preventedByNestedSwiper = true;\n  }\n\n  // Directions locks\n  if (!swiper.allowSlideNext && swiper.swipeDirection === 'next' && data.currentTranslate < data.startTranslate) {\n    data.currentTranslate = data.startTranslate;\n  }\n  if (!swiper.allowSlidePrev && swiper.swipeDirection === 'prev' && data.currentTranslate > data.startTranslate) {\n    data.currentTranslate = data.startTranslate;\n  }\n  if (!swiper.allowSlidePrev && !swiper.allowSlideNext) {\n    data.currentTranslate = data.startTranslate;\n  }\n\n  // Threshold\n  if (params.threshold > 0) {\n    if (Math.abs(diff) > params.threshold || data.allowThresholdMove) {\n      if (!data.allowThresholdMove) {\n        data.allowThresholdMove = true;\n        touches.startX = touches.currentX;\n        touches.startY = touches.currentY;\n        data.currentTranslate = data.startTranslate;\n        touches.diff = swiper.isHorizontal() ? touches.currentX - touches.startX : touches.currentY - touches.startY;\n        return;\n      }\n    } else {\n      data.currentTranslate = data.startTranslate;\n      return;\n    }\n  }\n  if (!params.followFinger || params.cssMode) return;\n\n  // Update active index in free mode\n  if (params.freeMode && params.freeMode.enabled && swiper.freeMode || params.watchSlidesProgress) {\n    swiper.updateActiveIndex();\n    swiper.updateSlidesClasses();\n  }\n  if (params.freeMode && params.freeMode.enabled && swiper.freeMode) {\n    swiper.freeMode.onTouchMove();\n  }\n  // Update progress\n  swiper.updateProgress(data.currentTranslate);\n  // Update translate\n  swiper.setTranslate(data.currentTranslate);\n}", "map": {"version": 3, "names": ["getDocument", "now", "onTouchMove", "event", "document", "swiper", "data", "touchEventsData", "params", "touches", "rtlTranslate", "rtl", "enabled", "simulate<PERSON>ouch", "pointerType", "e", "originalEvent", "isTouched", "startMoving", "isScrolling", "emit", "pointerIndex", "ev<PERSON><PERSON>", "findIndex", "cachedEv", "pointerId", "targetTouch", "length", "pageX", "pageY", "preventedByNestedSwiper", "startX", "startY", "allowTouchMove", "target", "matches", "focusableElements", "allowClick", "Object", "assign", "prevX", "currentX", "prevY", "currentY", "touchStartTime", "touchReleaseOnEdges", "loop", "isVertical", "translate", "maxTranslate", "minTranslate", "isMoved", "activeElement", "allowTouchCallbacks", "targetTouches", "diffX", "diffY", "threshold", "Math", "sqrt", "touchAngle", "isHorizontal", "atan2", "abs", "PI", "zoom", "cssMode", "cancelable", "preventDefault", "touchMoveStopPropagation", "nested", "stopPropagation", "diff", "touchesDiff", "previousX", "previousY", "oneWayMovement", "touchRatio", "prevTouchesDirection", "touchesDirection", "swipeDirection", "isLoop", "loopFix", "direction", "startTranslate", "getTranslate", "setTransition", "animating", "evt", "window", "CustomEvent", "bubbles", "wrapperEl", "dispatchEvent", "allowMomentumBounce", "grabCursor", "allowSlideNext", "allowSlidePrev", "setGrabCursor", "loopFixed", "setTranslate", "currentTranslate", "disableParentSwiper", "resistanceRatio", "centeredSlides", "size", "activeSlideIndex", "resistance", "slides", "<PERSON><PERSON><PERSON><PERSON>iew", "slidesPerViewDynamic", "ceil", "parseFloat", "allowThresholdMove", "follow<PERSON><PERSON>", "freeMode", "watchSlidesProgress", "updateActiveIndex", "updateSlidesClasses", "updateProgress"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/core/events/onTouchMove.js"], "sourcesContent": ["import { getDocument } from 'ssr-window';\nimport { now } from '../../shared/utils.js';\nexport default function onTouchMove(event) {\n  const document = getDocument();\n  const swiper = this;\n  const data = swiper.touchEventsData;\n  const {\n    params,\n    touches,\n    rtlTranslate: rtl,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n  if (!params.simulateTouch && event.pointerType === 'mouse') return;\n  let e = event;\n  if (e.originalEvent) e = e.originalEvent;\n  if (!data.isTouched) {\n    if (data.startMoving && data.isScrolling) {\n      swiper.emit('touchMoveOpposite', e);\n    }\n    return;\n  }\n  const pointerIndex = data.evCache.findIndex(cachedEv => cachedEv.pointerId === e.pointerId);\n  if (pointerIndex >= 0) data.evCache[pointerIndex] = e;\n  const targetTouch = data.evCache.length > 1 ? data.evCache[0] : e;\n  const pageX = targetTouch.pageX;\n  const pageY = targetTouch.pageY;\n  if (e.preventedByNestedSwiper) {\n    touches.startX = pageX;\n    touches.startY = pageY;\n    return;\n  }\n  if (!swiper.allowTouchMove) {\n    if (!e.target.matches(data.focusableElements)) {\n      swiper.allowClick = false;\n    }\n    if (data.isTouched) {\n      Object.assign(touches, {\n        startX: pageX,\n        startY: pageY,\n        prevX: swiper.touches.currentX,\n        prevY: swiper.touches.currentY,\n        currentX: pageX,\n        currentY: pageY\n      });\n      data.touchStartTime = now();\n    }\n    return;\n  }\n  if (params.touchReleaseOnEdges && !params.loop) {\n    if (swiper.isVertical()) {\n      // Vertical\n      if (pageY < touches.startY && swiper.translate <= swiper.maxTranslate() || pageY > touches.startY && swiper.translate >= swiper.minTranslate()) {\n        data.isTouched = false;\n        data.isMoved = false;\n        return;\n      }\n    } else if (pageX < touches.startX && swiper.translate <= swiper.maxTranslate() || pageX > touches.startX && swiper.translate >= swiper.minTranslate()) {\n      return;\n    }\n  }\n  if (document.activeElement) {\n    if (e.target === document.activeElement && e.target.matches(data.focusableElements)) {\n      data.isMoved = true;\n      swiper.allowClick = false;\n      return;\n    }\n  }\n  if (data.allowTouchCallbacks) {\n    swiper.emit('touchMove', e);\n  }\n  if (e.targetTouches && e.targetTouches.length > 1) return;\n  touches.currentX = pageX;\n  touches.currentY = pageY;\n  const diffX = touches.currentX - touches.startX;\n  const diffY = touches.currentY - touches.startY;\n  if (swiper.params.threshold && Math.sqrt(diffX ** 2 + diffY ** 2) < swiper.params.threshold) return;\n  if (typeof data.isScrolling === 'undefined') {\n    let touchAngle;\n    if (swiper.isHorizontal() && touches.currentY === touches.startY || swiper.isVertical() && touches.currentX === touches.startX) {\n      data.isScrolling = false;\n    } else {\n      // eslint-disable-next-line\n      if (diffX * diffX + diffY * diffY >= 25) {\n        touchAngle = Math.atan2(Math.abs(diffY), Math.abs(diffX)) * 180 / Math.PI;\n        data.isScrolling = swiper.isHorizontal() ? touchAngle > params.touchAngle : 90 - touchAngle > params.touchAngle;\n      }\n    }\n  }\n  if (data.isScrolling) {\n    swiper.emit('touchMoveOpposite', e);\n  }\n  if (typeof data.startMoving === 'undefined') {\n    if (touches.currentX !== touches.startX || touches.currentY !== touches.startY) {\n      data.startMoving = true;\n    }\n  }\n  if (data.isScrolling || swiper.zoom && swiper.params.zoom && swiper.params.zoom.enabled && data.evCache.length > 1) {\n    data.isTouched = false;\n    return;\n  }\n  if (!data.startMoving) {\n    return;\n  }\n  swiper.allowClick = false;\n  if (!params.cssMode && e.cancelable) {\n    e.preventDefault();\n  }\n  if (params.touchMoveStopPropagation && !params.nested) {\n    e.stopPropagation();\n  }\n  let diff = swiper.isHorizontal() ? diffX : diffY;\n  let touchesDiff = swiper.isHorizontal() ? touches.currentX - touches.previousX : touches.currentY - touches.previousY;\n  if (params.oneWayMovement) {\n    diff = Math.abs(diff) * (rtl ? 1 : -1);\n    touchesDiff = Math.abs(touchesDiff) * (rtl ? 1 : -1);\n  }\n  touches.diff = diff;\n  diff *= params.touchRatio;\n  if (rtl) {\n    diff = -diff;\n    touchesDiff = -touchesDiff;\n  }\n  const prevTouchesDirection = swiper.touchesDirection;\n  swiper.swipeDirection = diff > 0 ? 'prev' : 'next';\n  swiper.touchesDirection = touchesDiff > 0 ? 'prev' : 'next';\n  const isLoop = swiper.params.loop && !params.cssMode;\n  if (!data.isMoved) {\n    if (isLoop) {\n      swiper.loopFix({\n        direction: swiper.swipeDirection\n      });\n    }\n    data.startTranslate = swiper.getTranslate();\n    swiper.setTransition(0);\n    if (swiper.animating) {\n      const evt = new window.CustomEvent('transitionend', {\n        bubbles: true,\n        cancelable: true\n      });\n      swiper.wrapperEl.dispatchEvent(evt);\n    }\n    data.allowMomentumBounce = false;\n    // Grab Cursor\n    if (params.grabCursor && (swiper.allowSlideNext === true || swiper.allowSlidePrev === true)) {\n      swiper.setGrabCursor(true);\n    }\n    swiper.emit('sliderFirstMove', e);\n  }\n  let loopFixed;\n  if (data.isMoved && prevTouchesDirection !== swiper.touchesDirection && isLoop && Math.abs(diff) >= 1) {\n    // need another loop fix\n    swiper.loopFix({\n      direction: swiper.swipeDirection,\n      setTranslate: true\n    });\n    loopFixed = true;\n  }\n  swiper.emit('sliderMove', e);\n  data.isMoved = true;\n  data.currentTranslate = diff + data.startTranslate;\n  let disableParentSwiper = true;\n  let resistanceRatio = params.resistanceRatio;\n  if (params.touchReleaseOnEdges) {\n    resistanceRatio = 0;\n  }\n  if (diff > 0) {\n    if (isLoop && !loopFixed && data.currentTranslate > (params.centeredSlides ? swiper.minTranslate() - swiper.size / 2 : swiper.minTranslate())) {\n      swiper.loopFix({\n        direction: 'prev',\n        setTranslate: true,\n        activeSlideIndex: 0\n      });\n    }\n    if (data.currentTranslate > swiper.minTranslate()) {\n      disableParentSwiper = false;\n      if (params.resistance) {\n        data.currentTranslate = swiper.minTranslate() - 1 + (-swiper.minTranslate() + data.startTranslate + diff) ** resistanceRatio;\n      }\n    }\n  } else if (diff < 0) {\n    if (isLoop && !loopFixed && data.currentTranslate < (params.centeredSlides ? swiper.maxTranslate() + swiper.size / 2 : swiper.maxTranslate())) {\n      swiper.loopFix({\n        direction: 'next',\n        setTranslate: true,\n        activeSlideIndex: swiper.slides.length - (params.slidesPerView === 'auto' ? swiper.slidesPerViewDynamic() : Math.ceil(parseFloat(params.slidesPerView, 10)))\n      });\n    }\n    if (data.currentTranslate < swiper.maxTranslate()) {\n      disableParentSwiper = false;\n      if (params.resistance) {\n        data.currentTranslate = swiper.maxTranslate() + 1 - (swiper.maxTranslate() - data.startTranslate - diff) ** resistanceRatio;\n      }\n    }\n  }\n  if (disableParentSwiper) {\n    e.preventedByNestedSwiper = true;\n  }\n\n  // Directions locks\n  if (!swiper.allowSlideNext && swiper.swipeDirection === 'next' && data.currentTranslate < data.startTranslate) {\n    data.currentTranslate = data.startTranslate;\n  }\n  if (!swiper.allowSlidePrev && swiper.swipeDirection === 'prev' && data.currentTranslate > data.startTranslate) {\n    data.currentTranslate = data.startTranslate;\n  }\n  if (!swiper.allowSlidePrev && !swiper.allowSlideNext) {\n    data.currentTranslate = data.startTranslate;\n  }\n\n  // Threshold\n  if (params.threshold > 0) {\n    if (Math.abs(diff) > params.threshold || data.allowThresholdMove) {\n      if (!data.allowThresholdMove) {\n        data.allowThresholdMove = true;\n        touches.startX = touches.currentX;\n        touches.startY = touches.currentY;\n        data.currentTranslate = data.startTranslate;\n        touches.diff = swiper.isHorizontal() ? touches.currentX - touches.startX : touches.currentY - touches.startY;\n        return;\n      }\n    } else {\n      data.currentTranslate = data.startTranslate;\n      return;\n    }\n  }\n  if (!params.followFinger || params.cssMode) return;\n\n  // Update active index in free mode\n  if (params.freeMode && params.freeMode.enabled && swiper.freeMode || params.watchSlidesProgress) {\n    swiper.updateActiveIndex();\n    swiper.updateSlidesClasses();\n  }\n  if (params.freeMode && params.freeMode.enabled && swiper.freeMode) {\n    swiper.freeMode.onTouchMove();\n  }\n  // Update progress\n  swiper.updateProgress(data.currentTranslate);\n  // Update translate\n  swiper.setTranslate(data.currentTranslate);\n}"], "mappings": "AAAA,SAASA,WAAW,QAAQ,YAAY;AACxC,SAASC,GAAG,QAAQ,uBAAuB;AAC3C,eAAe,SAASC,WAAWA,CAACC,KAAK,EAAE;EACzC,MAAMC,QAAQ,GAAGJ,WAAW,EAAE;EAC9B,MAAMK,MAAM,GAAG,IAAI;EACnB,MAAMC,IAAI,GAAGD,MAAM,CAACE,eAAe;EACnC,MAAM;IACJC,MAAM;IACNC,OAAO;IACPC,YAAY,EAAEC,GAAG;IACjBC;EACF,CAAC,GAAGP,MAAM;EACV,IAAI,CAACO,OAAO,EAAE;EACd,IAAI,CAACJ,MAAM,CAACK,aAAa,IAAIV,KAAK,CAACW,WAAW,KAAK,OAAO,EAAE;EAC5D,IAAIC,CAAC,GAAGZ,KAAK;EACb,IAAIY,CAAC,CAACC,aAAa,EAAED,CAAC,GAAGA,CAAC,CAACC,aAAa;EACxC,IAAI,CAACV,IAAI,CAACW,SAAS,EAAE;IACnB,IAAIX,IAAI,CAACY,WAAW,IAAIZ,IAAI,CAACa,WAAW,EAAE;MACxCd,MAAM,CAACe,IAAI,CAAC,mBAAmB,EAAEL,CAAC,CAAC;IACrC;IACA;EACF;EACA,MAAMM,YAAY,GAAGf,IAAI,CAACgB,OAAO,CAACC,SAAS,CAACC,QAAQ,IAAIA,QAAQ,CAACC,SAAS,KAAKV,CAAC,CAACU,SAAS,CAAC;EAC3F,IAAIJ,YAAY,IAAI,CAAC,EAAEf,IAAI,CAACgB,OAAO,CAACD,YAAY,CAAC,GAAGN,CAAC;EACrD,MAAMW,WAAW,GAAGpB,IAAI,CAACgB,OAAO,CAACK,MAAM,GAAG,CAAC,GAAGrB,IAAI,CAACgB,OAAO,CAAC,CAAC,CAAC,GAAGP,CAAC;EACjE,MAAMa,KAAK,GAAGF,WAAW,CAACE,KAAK;EAC/B,MAAMC,KAAK,GAAGH,WAAW,CAACG,KAAK;EAC/B,IAAId,CAAC,CAACe,uBAAuB,EAAE;IAC7BrB,OAAO,CAACsB,MAAM,GAAGH,KAAK;IACtBnB,OAAO,CAACuB,MAAM,GAAGH,KAAK;IACtB;EACF;EACA,IAAI,CAACxB,MAAM,CAAC4B,cAAc,EAAE;IAC1B,IAAI,CAAClB,CAAC,CAACmB,MAAM,CAACC,OAAO,CAAC7B,IAAI,CAAC8B,iBAAiB,CAAC,EAAE;MAC7C/B,MAAM,CAACgC,UAAU,GAAG,KAAK;IAC3B;IACA,IAAI/B,IAAI,CAACW,SAAS,EAAE;MAClBqB,MAAM,CAACC,MAAM,CAAC9B,OAAO,EAAE;QACrBsB,MAAM,EAAEH,KAAK;QACbI,MAAM,EAAEH,KAAK;QACbW,KAAK,EAAEnC,MAAM,CAACI,OAAO,CAACgC,QAAQ;QAC9BC,KAAK,EAAErC,MAAM,CAACI,OAAO,CAACkC,QAAQ;QAC9BF,QAAQ,EAAEb,KAAK;QACfe,QAAQ,EAAEd;MACZ,CAAC,CAAC;MACFvB,IAAI,CAACsC,cAAc,GAAG3C,GAAG,EAAE;IAC7B;IACA;EACF;EACA,IAAIO,MAAM,CAACqC,mBAAmB,IAAI,CAACrC,MAAM,CAACsC,IAAI,EAAE;IAC9C,IAAIzC,MAAM,CAAC0C,UAAU,EAAE,EAAE;MACvB;MACA,IAAIlB,KAAK,GAAGpB,OAAO,CAACuB,MAAM,IAAI3B,MAAM,CAAC2C,SAAS,IAAI3C,MAAM,CAAC4C,YAAY,EAAE,IAAIpB,KAAK,GAAGpB,OAAO,CAACuB,MAAM,IAAI3B,MAAM,CAAC2C,SAAS,IAAI3C,MAAM,CAAC6C,YAAY,EAAE,EAAE;QAC9I5C,IAAI,CAACW,SAAS,GAAG,KAAK;QACtBX,IAAI,CAAC6C,OAAO,GAAG,KAAK;QACpB;MACF;IACF,CAAC,MAAM,IAAIvB,KAAK,GAAGnB,OAAO,CAACsB,MAAM,IAAI1B,MAAM,CAAC2C,SAAS,IAAI3C,MAAM,CAAC4C,YAAY,EAAE,IAAIrB,KAAK,GAAGnB,OAAO,CAACsB,MAAM,IAAI1B,MAAM,CAAC2C,SAAS,IAAI3C,MAAM,CAAC6C,YAAY,EAAE,EAAE;MACrJ;IACF;EACF;EACA,IAAI9C,QAAQ,CAACgD,aAAa,EAAE;IAC1B,IAAIrC,CAAC,CAACmB,MAAM,KAAK9B,QAAQ,CAACgD,aAAa,IAAIrC,CAAC,CAACmB,MAAM,CAACC,OAAO,CAAC7B,IAAI,CAAC8B,iBAAiB,CAAC,EAAE;MACnF9B,IAAI,CAAC6C,OAAO,GAAG,IAAI;MACnB9C,MAAM,CAACgC,UAAU,GAAG,KAAK;MACzB;IACF;EACF;EACA,IAAI/B,IAAI,CAAC+C,mBAAmB,EAAE;IAC5BhD,MAAM,CAACe,IAAI,CAAC,WAAW,EAAEL,CAAC,CAAC;EAC7B;EACA,IAAIA,CAAC,CAACuC,aAAa,IAAIvC,CAAC,CAACuC,aAAa,CAAC3B,MAAM,GAAG,CAAC,EAAE;EACnDlB,OAAO,CAACgC,QAAQ,GAAGb,KAAK;EACxBnB,OAAO,CAACkC,QAAQ,GAAGd,KAAK;EACxB,MAAM0B,KAAK,GAAG9C,OAAO,CAACgC,QAAQ,GAAGhC,OAAO,CAACsB,MAAM;EAC/C,MAAMyB,KAAK,GAAG/C,OAAO,CAACkC,QAAQ,GAAGlC,OAAO,CAACuB,MAAM;EAC/C,IAAI3B,MAAM,CAACG,MAAM,CAACiD,SAAS,IAAIC,IAAI,CAACC,IAAI,CAACJ,KAAK,IAAI,CAAC,GAAGC,KAAK,IAAI,CAAC,CAAC,GAAGnD,MAAM,CAACG,MAAM,CAACiD,SAAS,EAAE;EAC7F,IAAI,OAAOnD,IAAI,CAACa,WAAW,KAAK,WAAW,EAAE;IAC3C,IAAIyC,UAAU;IACd,IAAIvD,MAAM,CAACwD,YAAY,EAAE,IAAIpD,OAAO,CAACkC,QAAQ,KAAKlC,OAAO,CAACuB,MAAM,IAAI3B,MAAM,CAAC0C,UAAU,EAAE,IAAItC,OAAO,CAACgC,QAAQ,KAAKhC,OAAO,CAACsB,MAAM,EAAE;MAC9HzB,IAAI,CAACa,WAAW,GAAG,KAAK;IAC1B,CAAC,MAAM;MACL;MACA,IAAIoC,KAAK,GAAGA,KAAK,GAAGC,KAAK,GAAGA,KAAK,IAAI,EAAE,EAAE;QACvCI,UAAU,GAAGF,IAAI,CAACI,KAAK,CAACJ,IAAI,CAACK,GAAG,CAACP,KAAK,CAAC,EAAEE,IAAI,CAACK,GAAG,CAACR,KAAK,CAAC,CAAC,GAAG,GAAG,GAAGG,IAAI,CAACM,EAAE;QACzE1D,IAAI,CAACa,WAAW,GAAGd,MAAM,CAACwD,YAAY,EAAE,GAAGD,UAAU,GAAGpD,MAAM,CAACoD,UAAU,GAAG,EAAE,GAAGA,UAAU,GAAGpD,MAAM,CAACoD,UAAU;MACjH;IACF;EACF;EACA,IAAItD,IAAI,CAACa,WAAW,EAAE;IACpBd,MAAM,CAACe,IAAI,CAAC,mBAAmB,EAAEL,CAAC,CAAC;EACrC;EACA,IAAI,OAAOT,IAAI,CAACY,WAAW,KAAK,WAAW,EAAE;IAC3C,IAAIT,OAAO,CAACgC,QAAQ,KAAKhC,OAAO,CAACsB,MAAM,IAAItB,OAAO,CAACkC,QAAQ,KAAKlC,OAAO,CAACuB,MAAM,EAAE;MAC9E1B,IAAI,CAACY,WAAW,GAAG,IAAI;IACzB;EACF;EACA,IAAIZ,IAAI,CAACa,WAAW,IAAId,MAAM,CAAC4D,IAAI,IAAI5D,MAAM,CAACG,MAAM,CAACyD,IAAI,IAAI5D,MAAM,CAACG,MAAM,CAACyD,IAAI,CAACrD,OAAO,IAAIN,IAAI,CAACgB,OAAO,CAACK,MAAM,GAAG,CAAC,EAAE;IAClHrB,IAAI,CAACW,SAAS,GAAG,KAAK;IACtB;EACF;EACA,IAAI,CAACX,IAAI,CAACY,WAAW,EAAE;IACrB;EACF;EACAb,MAAM,CAACgC,UAAU,GAAG,KAAK;EACzB,IAAI,CAAC7B,MAAM,CAAC0D,OAAO,IAAInD,CAAC,CAACoD,UAAU,EAAE;IACnCpD,CAAC,CAACqD,cAAc,EAAE;EACpB;EACA,IAAI5D,MAAM,CAAC6D,wBAAwB,IAAI,CAAC7D,MAAM,CAAC8D,MAAM,EAAE;IACrDvD,CAAC,CAACwD,eAAe,EAAE;EACrB;EACA,IAAIC,IAAI,GAAGnE,MAAM,CAACwD,YAAY,EAAE,GAAGN,KAAK,GAAGC,KAAK;EAChD,IAAIiB,WAAW,GAAGpE,MAAM,CAACwD,YAAY,EAAE,GAAGpD,OAAO,CAACgC,QAAQ,GAAGhC,OAAO,CAACiE,SAAS,GAAGjE,OAAO,CAACkC,QAAQ,GAAGlC,OAAO,CAACkE,SAAS;EACrH,IAAInE,MAAM,CAACoE,cAAc,EAAE;IACzBJ,IAAI,GAAGd,IAAI,CAACK,GAAG,CAACS,IAAI,CAAC,IAAI7D,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IACtC8D,WAAW,GAAGf,IAAI,CAACK,GAAG,CAACU,WAAW,CAAC,IAAI9D,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;EACtD;EACAF,OAAO,CAAC+D,IAAI,GAAGA,IAAI;EACnBA,IAAI,IAAIhE,MAAM,CAACqE,UAAU;EACzB,IAAIlE,GAAG,EAAE;IACP6D,IAAI,GAAG,CAACA,IAAI;IACZC,WAAW,GAAG,CAACA,WAAW;EAC5B;EACA,MAAMK,oBAAoB,GAAGzE,MAAM,CAAC0E,gBAAgB;EACpD1E,MAAM,CAAC2E,cAAc,GAAGR,IAAI,GAAG,CAAC,GAAG,MAAM,GAAG,MAAM;EAClDnE,MAAM,CAAC0E,gBAAgB,GAAGN,WAAW,GAAG,CAAC,GAAG,MAAM,GAAG,MAAM;EAC3D,MAAMQ,MAAM,GAAG5E,MAAM,CAACG,MAAM,CAACsC,IAAI,IAAI,CAACtC,MAAM,CAAC0D,OAAO;EACpD,IAAI,CAAC5D,IAAI,CAAC6C,OAAO,EAAE;IACjB,IAAI8B,MAAM,EAAE;MACV5E,MAAM,CAAC6E,OAAO,CAAC;QACbC,SAAS,EAAE9E,MAAM,CAAC2E;MACpB,CAAC,CAAC;IACJ;IACA1E,IAAI,CAAC8E,cAAc,GAAG/E,MAAM,CAACgF,YAAY,EAAE;IAC3ChF,MAAM,CAACiF,aAAa,CAAC,CAAC,CAAC;IACvB,IAAIjF,MAAM,CAACkF,SAAS,EAAE;MACpB,MAAMC,GAAG,GAAG,IAAIC,MAAM,CAACC,WAAW,CAAC,eAAe,EAAE;QAClDC,OAAO,EAAE,IAAI;QACbxB,UAAU,EAAE;MACd,CAAC,CAAC;MACF9D,MAAM,CAACuF,SAAS,CAACC,aAAa,CAACL,GAAG,CAAC;IACrC;IACAlF,IAAI,CAACwF,mBAAmB,GAAG,KAAK;IAChC;IACA,IAAItF,MAAM,CAACuF,UAAU,KAAK1F,MAAM,CAAC2F,cAAc,KAAK,IAAI,IAAI3F,MAAM,CAAC4F,cAAc,KAAK,IAAI,CAAC,EAAE;MAC3F5F,MAAM,CAAC6F,aAAa,CAAC,IAAI,CAAC;IAC5B;IACA7F,MAAM,CAACe,IAAI,CAAC,iBAAiB,EAAEL,CAAC,CAAC;EACnC;EACA,IAAIoF,SAAS;EACb,IAAI7F,IAAI,CAAC6C,OAAO,IAAI2B,oBAAoB,KAAKzE,MAAM,CAAC0E,gBAAgB,IAAIE,MAAM,IAAIvB,IAAI,CAACK,GAAG,CAACS,IAAI,CAAC,IAAI,CAAC,EAAE;IACrG;IACAnE,MAAM,CAAC6E,OAAO,CAAC;MACbC,SAAS,EAAE9E,MAAM,CAAC2E,cAAc;MAChCoB,YAAY,EAAE;IAChB,CAAC,CAAC;IACFD,SAAS,GAAG,IAAI;EAClB;EACA9F,MAAM,CAACe,IAAI,CAAC,YAAY,EAAEL,CAAC,CAAC;EAC5BT,IAAI,CAAC6C,OAAO,GAAG,IAAI;EACnB7C,IAAI,CAAC+F,gBAAgB,GAAG7B,IAAI,GAAGlE,IAAI,CAAC8E,cAAc;EAClD,IAAIkB,mBAAmB,GAAG,IAAI;EAC9B,IAAIC,eAAe,GAAG/F,MAAM,CAAC+F,eAAe;EAC5C,IAAI/F,MAAM,CAACqC,mBAAmB,EAAE;IAC9B0D,eAAe,GAAG,CAAC;EACrB;EACA,IAAI/B,IAAI,GAAG,CAAC,EAAE;IACZ,IAAIS,MAAM,IAAI,CAACkB,SAAS,IAAI7F,IAAI,CAAC+F,gBAAgB,IAAI7F,MAAM,CAACgG,cAAc,GAAGnG,MAAM,CAAC6C,YAAY,EAAE,GAAG7C,MAAM,CAACoG,IAAI,GAAG,CAAC,GAAGpG,MAAM,CAAC6C,YAAY,EAAE,CAAC,EAAE;MAC7I7C,MAAM,CAAC6E,OAAO,CAAC;QACbC,SAAS,EAAE,MAAM;QACjBiB,YAAY,EAAE,IAAI;QAClBM,gBAAgB,EAAE;MACpB,CAAC,CAAC;IACJ;IACA,IAAIpG,IAAI,CAAC+F,gBAAgB,GAAGhG,MAAM,CAAC6C,YAAY,EAAE,EAAE;MACjDoD,mBAAmB,GAAG,KAAK;MAC3B,IAAI9F,MAAM,CAACmG,UAAU,EAAE;QACrBrG,IAAI,CAAC+F,gBAAgB,GAAGhG,MAAM,CAAC6C,YAAY,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC7C,MAAM,CAAC6C,YAAY,EAAE,GAAG5C,IAAI,CAAC8E,cAAc,GAAGZ,IAAI,KAAK+B,eAAe;MAC9H;IACF;EACF,CAAC,MAAM,IAAI/B,IAAI,GAAG,CAAC,EAAE;IACnB,IAAIS,MAAM,IAAI,CAACkB,SAAS,IAAI7F,IAAI,CAAC+F,gBAAgB,IAAI7F,MAAM,CAACgG,cAAc,GAAGnG,MAAM,CAAC4C,YAAY,EAAE,GAAG5C,MAAM,CAACoG,IAAI,GAAG,CAAC,GAAGpG,MAAM,CAAC4C,YAAY,EAAE,CAAC,EAAE;MAC7I5C,MAAM,CAAC6E,OAAO,CAAC;QACbC,SAAS,EAAE,MAAM;QACjBiB,YAAY,EAAE,IAAI;QAClBM,gBAAgB,EAAErG,MAAM,CAACuG,MAAM,CAACjF,MAAM,IAAInB,MAAM,CAACqG,aAAa,KAAK,MAAM,GAAGxG,MAAM,CAACyG,oBAAoB,EAAE,GAAGpD,IAAI,CAACqD,IAAI,CAACC,UAAU,CAACxG,MAAM,CAACqG,aAAa,EAAE,EAAE,CAAC,CAAC;MAC7J,CAAC,CAAC;IACJ;IACA,IAAIvG,IAAI,CAAC+F,gBAAgB,GAAGhG,MAAM,CAAC4C,YAAY,EAAE,EAAE;MACjDqD,mBAAmB,GAAG,KAAK;MAC3B,IAAI9F,MAAM,CAACmG,UAAU,EAAE;QACrBrG,IAAI,CAAC+F,gBAAgB,GAAGhG,MAAM,CAAC4C,YAAY,EAAE,GAAG,CAAC,GAAG,CAAC5C,MAAM,CAAC4C,YAAY,EAAE,GAAG3C,IAAI,CAAC8E,cAAc,GAAGZ,IAAI,KAAK+B,eAAe;MAC7H;IACF;EACF;EACA,IAAID,mBAAmB,EAAE;IACvBvF,CAAC,CAACe,uBAAuB,GAAG,IAAI;EAClC;;EAEA;EACA,IAAI,CAACzB,MAAM,CAAC2F,cAAc,IAAI3F,MAAM,CAAC2E,cAAc,KAAK,MAAM,IAAI1E,IAAI,CAAC+F,gBAAgB,GAAG/F,IAAI,CAAC8E,cAAc,EAAE;IAC7G9E,IAAI,CAAC+F,gBAAgB,GAAG/F,IAAI,CAAC8E,cAAc;EAC7C;EACA,IAAI,CAAC/E,MAAM,CAAC4F,cAAc,IAAI5F,MAAM,CAAC2E,cAAc,KAAK,MAAM,IAAI1E,IAAI,CAAC+F,gBAAgB,GAAG/F,IAAI,CAAC8E,cAAc,EAAE;IAC7G9E,IAAI,CAAC+F,gBAAgB,GAAG/F,IAAI,CAAC8E,cAAc;EAC7C;EACA,IAAI,CAAC/E,MAAM,CAAC4F,cAAc,IAAI,CAAC5F,MAAM,CAAC2F,cAAc,EAAE;IACpD1F,IAAI,CAAC+F,gBAAgB,GAAG/F,IAAI,CAAC8E,cAAc;EAC7C;;EAEA;EACA,IAAI5E,MAAM,CAACiD,SAAS,GAAG,CAAC,EAAE;IACxB,IAAIC,IAAI,CAACK,GAAG,CAACS,IAAI,CAAC,GAAGhE,MAAM,CAACiD,SAAS,IAAInD,IAAI,CAAC2G,kBAAkB,EAAE;MAChE,IAAI,CAAC3G,IAAI,CAAC2G,kBAAkB,EAAE;QAC5B3G,IAAI,CAAC2G,kBAAkB,GAAG,IAAI;QAC9BxG,OAAO,CAACsB,MAAM,GAAGtB,OAAO,CAACgC,QAAQ;QACjChC,OAAO,CAACuB,MAAM,GAAGvB,OAAO,CAACkC,QAAQ;QACjCrC,IAAI,CAAC+F,gBAAgB,GAAG/F,IAAI,CAAC8E,cAAc;QAC3C3E,OAAO,CAAC+D,IAAI,GAAGnE,MAAM,CAACwD,YAAY,EAAE,GAAGpD,OAAO,CAACgC,QAAQ,GAAGhC,OAAO,CAACsB,MAAM,GAAGtB,OAAO,CAACkC,QAAQ,GAAGlC,OAAO,CAACuB,MAAM;QAC5G;MACF;IACF,CAAC,MAAM;MACL1B,IAAI,CAAC+F,gBAAgB,GAAG/F,IAAI,CAAC8E,cAAc;MAC3C;IACF;EACF;EACA,IAAI,CAAC5E,MAAM,CAAC0G,YAAY,IAAI1G,MAAM,CAAC0D,OAAO,EAAE;;EAE5C;EACA,IAAI1D,MAAM,CAAC2G,QAAQ,IAAI3G,MAAM,CAAC2G,QAAQ,CAACvG,OAAO,IAAIP,MAAM,CAAC8G,QAAQ,IAAI3G,MAAM,CAAC4G,mBAAmB,EAAE;IAC/F/G,MAAM,CAACgH,iBAAiB,EAAE;IAC1BhH,MAAM,CAACiH,mBAAmB,EAAE;EAC9B;EACA,IAAI9G,MAAM,CAAC2G,QAAQ,IAAI3G,MAAM,CAAC2G,QAAQ,CAACvG,OAAO,IAAIP,MAAM,CAAC8G,QAAQ,EAAE;IACjE9G,MAAM,CAAC8G,QAAQ,CAACjH,WAAW,EAAE;EAC/B;EACA;EACAG,MAAM,CAACkH,cAAc,CAACjH,IAAI,CAAC+F,gBAAgB,CAAC;EAC5C;EACAhG,MAAM,CAAC+F,YAAY,CAAC9F,IAAI,CAAC+F,gBAAgB,CAAC;AAC5C"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}