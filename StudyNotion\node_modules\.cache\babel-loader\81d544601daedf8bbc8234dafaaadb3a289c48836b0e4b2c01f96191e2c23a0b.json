{"ast": null, "code": "// Note: types exposed from `index.d.ts`.\nexport { handlers as defaultHandlers } from './lib/handlers/index.js';\n// To do: next major: remove.\nexport { one, all } from './lib/state.js';\nexport { toHast } from './lib/index.js';", "map": {"version": 3, "names": ["handlers", "defaultHandlers", "one", "all", "toHast"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/mdast-util-to-hast/index.js"], "sourcesContent": ["// Note: types exposed from `index.d.ts`.\nexport {handlers as defaultHandlers} from './lib/handlers/index.js'\n// To do: next major: remove.\nexport {one, all} from './lib/state.js'\nexport {toHast} from './lib/index.js'\n"], "mappings": "AAAA;AACA,SAAQA,QAAQ,IAAIC,eAAe,QAAO,yBAAyB;AACnE;AACA,SAAQC,GAAG,EAAEC,GAAG,QAAO,gBAAgB;AACvC,SAAQC,MAAM,QAAO,gBAAgB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}