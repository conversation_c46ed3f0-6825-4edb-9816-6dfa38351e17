{"ast": null, "code": "/* eslint no-unused-vars: \"off\" */\nexport default function slideNext() {\n  let speed = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : this.params.speed;\n  let runCallbacks = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n  let internal = arguments.length > 2 ? arguments[2] : undefined;\n  const swiper = this;\n  const {\n    enabled,\n    params,\n    animating\n  } = swiper;\n  if (!enabled) return swiper;\n  let perGroup = params.slidesPerGroup;\n  if (params.slidesPerView === 'auto' && params.slidesPerGroup === 1 && params.slidesPerGroupAuto) {\n    perGroup = Math.max(swiper.slidesPerViewDynamic('current', true), 1);\n  }\n  const increment = swiper.activeIndex < params.slidesPerGroupSkip ? 1 : perGroup;\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  if (params.loop) {\n    if (animating && !isVirtual && params.loopPreventsSliding) return false;\n    swiper.loopFix({\n      direction: 'next'\n    });\n    // eslint-disable-next-line\n    swiper._clientLeft = swiper.wrapperEl.clientLeft;\n  }\n  if (params.rewind && swiper.isEnd) {\n    return swiper.slideTo(0, speed, runCallbacks, internal);\n  }\n  return swiper.slideTo(swiper.activeIndex + increment, speed, runCallbacks, internal);\n}", "map": {"version": 3, "names": ["slideNext", "speed", "arguments", "length", "undefined", "params", "runCallbacks", "internal", "swiper", "enabled", "animating", "perGroup", "slidesPerGroup", "<PERSON><PERSON><PERSON><PERSON>iew", "slidesPerGroupAuto", "Math", "max", "slidesPerViewDynamic", "increment", "activeIndex", "slidesPerGroupSkip", "isVirtual", "virtual", "loop", "loopPreventsSliding", "loopFix", "direction", "_clientLeft", "wrapperEl", "clientLeft", "rewind", "isEnd", "slideTo"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/core/slide/slideNext.js"], "sourcesContent": ["/* eslint no-unused-vars: \"off\" */\nexport default function slideNext(speed = this.params.speed, runCallbacks = true, internal) {\n  const swiper = this;\n  const {\n    enabled,\n    params,\n    animating\n  } = swiper;\n  if (!enabled) return swiper;\n  let perGroup = params.slidesPerGroup;\n  if (params.slidesPerView === 'auto' && params.slidesPerGroup === 1 && params.slidesPerGroupAuto) {\n    perGroup = Math.max(swiper.slidesPerViewDynamic('current', true), 1);\n  }\n  const increment = swiper.activeIndex < params.slidesPerGroupSkip ? 1 : perGroup;\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  if (params.loop) {\n    if (animating && !isVirtual && params.loopPreventsSliding) return false;\n    swiper.loopFix({\n      direction: 'next'\n    });\n    // eslint-disable-next-line\n    swiper._clientLeft = swiper.wrapperEl.clientLeft;\n  }\n  if (params.rewind && swiper.isEnd) {\n    return swiper.slideTo(0, speed, runCallbacks, internal);\n  }\n  return swiper.slideTo(swiper.activeIndex + increment, speed, runCallbacks, internal);\n}"], "mappings": "AAAA;AACA,eAAe,SAASA,SAASA,CAAA,EAA2D;EAAA,IAA1DC,KAAK,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI,CAACG,MAAM,CAACJ,KAAK;EAAA,IAAEK,YAAY,GAAAJ,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;EAAA,IAAEK,QAAQ,GAAAL,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EACxF,MAAMI,MAAM,GAAG,IAAI;EACnB,MAAM;IACJC,OAAO;IACPJ,MAAM;IACNK;EACF,CAAC,GAAGF,MAAM;EACV,IAAI,CAACC,OAAO,EAAE,OAAOD,MAAM;EAC3B,IAAIG,QAAQ,GAAGN,MAAM,CAACO,cAAc;EACpC,IAAIP,MAAM,CAACQ,aAAa,KAAK,MAAM,IAAIR,MAAM,CAACO,cAAc,KAAK,CAAC,IAAIP,MAAM,CAACS,kBAAkB,EAAE;IAC/FH,QAAQ,GAAGI,IAAI,CAACC,GAAG,CAACR,MAAM,CAACS,oBAAoB,CAAC,SAAS,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;EACtE;EACA,MAAMC,SAAS,GAAGV,MAAM,CAACW,WAAW,GAAGd,MAAM,CAACe,kBAAkB,GAAG,CAAC,GAAGT,QAAQ;EAC/E,MAAMU,SAAS,GAAGb,MAAM,CAACc,OAAO,IAAIjB,MAAM,CAACiB,OAAO,CAACb,OAAO;EAC1D,IAAIJ,MAAM,CAACkB,IAAI,EAAE;IACf,IAAIb,SAAS,IAAI,CAACW,SAAS,IAAIhB,MAAM,CAACmB,mBAAmB,EAAE,OAAO,KAAK;IACvEhB,MAAM,CAACiB,OAAO,CAAC;MACbC,SAAS,EAAE;IACb,CAAC,CAAC;IACF;IACAlB,MAAM,CAACmB,WAAW,GAAGnB,MAAM,CAACoB,SAAS,CAACC,UAAU;EAClD;EACA,IAAIxB,MAAM,CAACyB,MAAM,IAAItB,MAAM,CAACuB,KAAK,EAAE;IACjC,OAAOvB,MAAM,CAACwB,OAAO,CAAC,CAAC,EAAE/B,KAAK,EAAEK,YAAY,EAAEC,QAAQ,CAAC;EACzD;EACA,OAAOC,MAAM,CAACwB,OAAO,CAACxB,MAAM,CAACW,WAAW,GAAGD,SAAS,EAAEjB,KAAK,EAAEK,YAAY,EAAEC,QAAQ,CAAC;AACtF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}