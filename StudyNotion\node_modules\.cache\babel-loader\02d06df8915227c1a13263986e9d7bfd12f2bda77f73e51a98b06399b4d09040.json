{"ast": null, "code": "/**\n * SSR Window 4.0.2\n * Better handling for window object in SSR environment\n * https://github.com/nolimits4web/ssr-window\n *\n * Copyright 2021, <PERSON>\n *\n * Licensed under MIT\n *\n * Released on: December 13, 2021\n */\n/* eslint-disable no-param-reassign */\nfunction isObject(obj) {\n  return obj !== null && typeof obj === 'object' && 'constructor' in obj && obj.constructor === Object;\n}\nfunction extend() {\n  let target = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  let src = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  Object.keys(src).forEach(key => {\n    if (typeof target[key] === 'undefined') target[key] = src[key];else if (isObject(src[key]) && isObject(target[key]) && Object.keys(src[key]).length > 0) {\n      extend(target[key], src[key]);\n    }\n  });\n}\nconst ssrDocument = {\n  body: {},\n  addEventListener() {},\n  removeEventListener() {},\n  activeElement: {\n    blur() {},\n    nodeName: ''\n  },\n  querySelector() {\n    return null;\n  },\n  querySelectorAll() {\n    return [];\n  },\n  getElementById() {\n    return null;\n  },\n  createEvent() {\n    return {\n      initEvent() {}\n    };\n  },\n  createElement() {\n    return {\n      children: [],\n      childNodes: [],\n      style: {},\n      setAttribute() {},\n      getElementsByTagName() {\n        return [];\n      }\n    };\n  },\n  createElementNS() {\n    return {};\n  },\n  importNode() {\n    return null;\n  },\n  location: {\n    hash: '',\n    host: '',\n    hostname: '',\n    href: '',\n    origin: '',\n    pathname: '',\n    protocol: '',\n    search: ''\n  }\n};\nfunction getDocument() {\n  const doc = typeof document !== 'undefined' ? document : {};\n  extend(doc, ssrDocument);\n  return doc;\n}\nconst ssrWindow = {\n  document: ssrDocument,\n  navigator: {\n    userAgent: ''\n  },\n  location: {\n    hash: '',\n    host: '',\n    hostname: '',\n    href: '',\n    origin: '',\n    pathname: '',\n    protocol: '',\n    search: ''\n  },\n  history: {\n    replaceState() {},\n    pushState() {},\n    go() {},\n    back() {}\n  },\n  CustomEvent: function CustomEvent() {\n    return this;\n  },\n  addEventListener() {},\n  removeEventListener() {},\n  getComputedStyle() {\n    return {\n      getPropertyValue() {\n        return '';\n      }\n    };\n  },\n  Image() {},\n  Date() {},\n  screen: {},\n  setTimeout() {},\n  clearTimeout() {},\n  matchMedia() {\n    return {};\n  },\n  requestAnimationFrame(callback) {\n    if (typeof setTimeout === 'undefined') {\n      callback();\n      return null;\n    }\n    return setTimeout(callback, 0);\n  },\n  cancelAnimationFrame(id) {\n    if (typeof setTimeout === 'undefined') {\n      return;\n    }\n    clearTimeout(id);\n  }\n};\nfunction getWindow() {\n  const win = typeof window !== 'undefined' ? window : {};\n  extend(win, ssrWindow);\n  return win;\n}\nexport { extend, getDocument, getWindow, ssrDocument, ssrWindow };", "map": {"version": 3, "names": ["isObject", "obj", "constructor", "Object", "extend", "target", "arguments", "length", "undefined", "src", "keys", "for<PERSON>ach", "key", "ssrDocument", "body", "addEventListener", "removeEventListener", "activeElement", "blur", "nodeName", "querySelector", "querySelectorAll", "getElementById", "createEvent", "initEvent", "createElement", "children", "childNodes", "style", "setAttribute", "getElementsByTagName", "createElementNS", "importNode", "location", "hash", "host", "hostname", "href", "origin", "pathname", "protocol", "search", "getDocument", "doc", "document", "ssrWindow", "navigator", "userAgent", "history", "replaceState", "pushState", "go", "back", "CustomEvent", "getComputedStyle", "getPropertyValue", "Image", "Date", "screen", "setTimeout", "clearTimeout", "matchMedia", "requestAnimationFrame", "callback", "cancelAnimationFrame", "id", "getWindow", "win", "window"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/ssr-window/ssr-window.esm.js"], "sourcesContent": ["/**\n * SSR Window 4.0.2\n * Better handling for window object in SSR environment\n * https://github.com/nolimits4web/ssr-window\n *\n * Copyright 2021, <PERSON>\n *\n * Licensed under MIT\n *\n * Released on: December 13, 2021\n */\n/* eslint-disable no-param-reassign */\nfunction isObject(obj) {\n    return (obj !== null &&\n        typeof obj === 'object' &&\n        'constructor' in obj &&\n        obj.constructor === Object);\n}\nfunction extend(target = {}, src = {}) {\n    Object.keys(src).forEach((key) => {\n        if (typeof target[key] === 'undefined')\n            target[key] = src[key];\n        else if (isObject(src[key]) &&\n            isObject(target[key]) &&\n            Object.keys(src[key]).length > 0) {\n            extend(target[key], src[key]);\n        }\n    });\n}\n\nconst ssrDocument = {\n    body: {},\n    addEventListener() { },\n    removeEventListener() { },\n    activeElement: {\n        blur() { },\n        nodeName: '',\n    },\n    querySelector() {\n        return null;\n    },\n    querySelectorAll() {\n        return [];\n    },\n    getElementById() {\n        return null;\n    },\n    createEvent() {\n        return {\n            initEvent() { },\n        };\n    },\n    createElement() {\n        return {\n            children: [],\n            childNodes: [],\n            style: {},\n            setAttribute() { },\n            getElementsByTagName() {\n                return [];\n            },\n        };\n    },\n    createElementNS() {\n        return {};\n    },\n    importNode() {\n        return null;\n    },\n    location: {\n        hash: '',\n        host: '',\n        hostname: '',\n        href: '',\n        origin: '',\n        pathname: '',\n        protocol: '',\n        search: '',\n    },\n};\nfunction getDocument() {\n    const doc = typeof document !== 'undefined' ? document : {};\n    extend(doc, ssrDocument);\n    return doc;\n}\n\nconst ssrWindow = {\n    document: ssrDocument,\n    navigator: {\n        userAgent: '',\n    },\n    location: {\n        hash: '',\n        host: '',\n        hostname: '',\n        href: '',\n        origin: '',\n        pathname: '',\n        protocol: '',\n        search: '',\n    },\n    history: {\n        replaceState() { },\n        pushState() { },\n        go() { },\n        back() { },\n    },\n    CustomEvent: function CustomEvent() {\n        return this;\n    },\n    addEventListener() { },\n    removeEventListener() { },\n    getComputedStyle() {\n        return {\n            getPropertyValue() {\n                return '';\n            },\n        };\n    },\n    Image() { },\n    Date() { },\n    screen: {},\n    setTimeout() { },\n    clearTimeout() { },\n    matchMedia() {\n        return {};\n    },\n    requestAnimationFrame(callback) {\n        if (typeof setTimeout === 'undefined') {\n            callback();\n            return null;\n        }\n        return setTimeout(callback, 0);\n    },\n    cancelAnimationFrame(id) {\n        if (typeof setTimeout === 'undefined') {\n            return;\n        }\n        clearTimeout(id);\n    },\n};\nfunction getWindow() {\n    const win = typeof window !== 'undefined' ? window : {};\n    extend(win, ssrWindow);\n    return win;\n}\n\nexport { extend, getDocument, getWindow, ssrDocument, ssrWindow };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,QAAQA,CAACC,GAAG,EAAE;EACnB,OAAQA,GAAG,KAAK,IAAI,IAChB,OAAOA,GAAG,KAAK,QAAQ,IACvB,aAAa,IAAIA,GAAG,IACpBA,GAAG,CAACC,WAAW,KAAKC,MAAM;AAClC;AACA,SAASC,MAAMA,CAAA,EAAwB;EAAA,IAAvBC,MAAM,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAAA,IAAEG,GAAG,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EACjCH,MAAM,CAACO,IAAI,CAACD,GAAG,CAAC,CAACE,OAAO,CAAEC,GAAG,IAAK;IAC9B,IAAI,OAAOP,MAAM,CAACO,GAAG,CAAC,KAAK,WAAW,EAClCP,MAAM,CAACO,GAAG,CAAC,GAAGH,GAAG,CAACG,GAAG,CAAC,CAAC,KACtB,IAAIZ,QAAQ,CAACS,GAAG,CAACG,GAAG,CAAC,CAAC,IACvBZ,QAAQ,CAACK,MAAM,CAACO,GAAG,CAAC,CAAC,IACrBT,MAAM,CAACO,IAAI,CAACD,GAAG,CAACG,GAAG,CAAC,CAAC,CAACL,MAAM,GAAG,CAAC,EAAE;MAClCH,MAAM,CAACC,MAAM,CAACO,GAAG,CAAC,EAAEH,GAAG,CAACG,GAAG,CAAC,CAAC;IACjC;EACJ,CAAC,CAAC;AACN;AAEA,MAAMC,WAAW,GAAG;EAChBC,IAAI,EAAE,CAAC,CAAC;EACRC,gBAAgBA,CAAA,EAAG,CAAE,CAAC;EACtBC,mBAAmBA,CAAA,EAAG,CAAE,CAAC;EACzBC,aAAa,EAAE;IACXC,IAAIA,CAAA,EAAG,CAAE,CAAC;IACVC,QAAQ,EAAE;EACd,CAAC;EACDC,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI;EACf,CAAC;EACDC,gBAAgBA,CAAA,EAAG;IACf,OAAO,EAAE;EACb,CAAC;EACDC,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI;EACf,CAAC;EACDC,WAAWA,CAAA,EAAG;IACV,OAAO;MACHC,SAASA,CAAA,EAAG,CAAE;IAClB,CAAC;EACL,CAAC;EACDC,aAAaA,CAAA,EAAG;IACZ,OAAO;MACHC,QAAQ,EAAE,EAAE;MACZC,UAAU,EAAE,EAAE;MACdC,KAAK,EAAE,CAAC,CAAC;MACTC,YAAYA,CAAA,EAAG,CAAE,CAAC;MAClBC,oBAAoBA,CAAA,EAAG;QACnB,OAAO,EAAE;MACb;IACJ,CAAC;EACL,CAAC;EACDC,eAAeA,CAAA,EAAG;IACd,OAAO,CAAC,CAAC;EACb,CAAC;EACDC,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI;EACf,CAAC;EACDC,QAAQ,EAAE;IACNC,IAAI,EAAE,EAAE;IACRC,IAAI,EAAE,EAAE;IACRC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,MAAM,EAAE,EAAE;IACVC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,MAAM,EAAE;EACZ;AACJ,CAAC;AACD,SAASC,WAAWA,CAAA,EAAG;EACnB,MAAMC,GAAG,GAAG,OAAOC,QAAQ,KAAK,WAAW,GAAGA,QAAQ,GAAG,CAAC,CAAC;EAC3DxC,MAAM,CAACuC,GAAG,EAAE9B,WAAW,CAAC;EACxB,OAAO8B,GAAG;AACd;AAEA,MAAME,SAAS,GAAG;EACdD,QAAQ,EAAE/B,WAAW;EACrBiC,SAAS,EAAE;IACPC,SAAS,EAAE;EACf,CAAC;EACDd,QAAQ,EAAE;IACNC,IAAI,EAAE,EAAE;IACRC,IAAI,EAAE,EAAE;IACRC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,MAAM,EAAE,EAAE;IACVC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,MAAM,EAAE;EACZ,CAAC;EACDO,OAAO,EAAE;IACLC,YAAYA,CAAA,EAAG,CAAE,CAAC;IAClBC,SAASA,CAAA,EAAG,CAAE,CAAC;IACfC,EAAEA,CAAA,EAAG,CAAE,CAAC;IACRC,IAAIA,CAAA,EAAG,CAAE;EACb,CAAC;EACDC,WAAW,EAAE,SAASA,WAAWA,CAAA,EAAG;IAChC,OAAO,IAAI;EACf,CAAC;EACDtC,gBAAgBA,CAAA,EAAG,CAAE,CAAC;EACtBC,mBAAmBA,CAAA,EAAG,CAAE,CAAC;EACzBsC,gBAAgBA,CAAA,EAAG;IACf,OAAO;MACHC,gBAAgBA,CAAA,EAAG;QACf,OAAO,EAAE;MACb;IACJ,CAAC;EACL,CAAC;EACDC,KAAKA,CAAA,EAAG,CAAE,CAAC;EACXC,IAAIA,CAAA,EAAG,CAAE,CAAC;EACVC,MAAM,EAAE,CAAC,CAAC;EACVC,UAAUA,CAAA,EAAG,CAAE,CAAC;EAChBC,YAAYA,CAAA,EAAG,CAAE,CAAC;EAClBC,UAAUA,CAAA,EAAG;IACT,OAAO,CAAC,CAAC;EACb,CAAC;EACDC,qBAAqBA,CAACC,QAAQ,EAAE;IAC5B,IAAI,OAAOJ,UAAU,KAAK,WAAW,EAAE;MACnCI,QAAQ,EAAE;MACV,OAAO,IAAI;IACf;IACA,OAAOJ,UAAU,CAACI,QAAQ,EAAE,CAAC,CAAC;EAClC,CAAC;EACDC,oBAAoBA,CAACC,EAAE,EAAE;IACrB,IAAI,OAAON,UAAU,KAAK,WAAW,EAAE;MACnC;IACJ;IACAC,YAAY,CAACK,EAAE,CAAC;EACpB;AACJ,CAAC;AACD,SAASC,SAASA,CAAA,EAAG;EACjB,MAAMC,GAAG,GAAG,OAAOC,MAAM,KAAK,WAAW,GAAGA,MAAM,GAAG,CAAC,CAAC;EACvDhE,MAAM,CAAC+D,GAAG,EAAEtB,SAAS,CAAC;EACtB,OAAOsB,GAAG;AACd;AAEA,SAAS/D,MAAM,EAAEsC,WAAW,EAAEwB,SAAS,EAAErD,WAAW,EAAEgC,SAAS"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}