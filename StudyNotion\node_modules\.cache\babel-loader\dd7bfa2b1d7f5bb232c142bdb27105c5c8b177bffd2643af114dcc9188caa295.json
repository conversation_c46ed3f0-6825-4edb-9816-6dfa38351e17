{"ast": null, "code": "import { getWindow } from 'ssr-window';\nexport default function getBreakpoint(breakpoints) {\n  let base = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'window';\n  let containerEl = arguments.length > 2 ? arguments[2] : undefined;\n  if (!breakpoints || base === 'container' && !containerEl) return undefined;\n  let breakpoint = false;\n  const window = getWindow();\n  const currentHeight = base === 'window' ? window.innerHeight : containerEl.clientHeight;\n  const points = Object.keys(breakpoints).map(point => {\n    if (typeof point === 'string' && point.indexOf('@') === 0) {\n      const minRatio = parseFloat(point.substr(1));\n      const value = currentHeight * minRatio;\n      return {\n        value,\n        point\n      };\n    }\n    return {\n      value: point,\n      point\n    };\n  });\n  points.sort((a, b) => parseInt(a.value, 10) - parseInt(b.value, 10));\n  for (let i = 0; i < points.length; i += 1) {\n    const {\n      point,\n      value\n    } = points[i];\n    if (base === 'window') {\n      if (window.matchMedia(`(min-width: ${value}px)`).matches) {\n        breakpoint = point;\n      }\n    } else if (value <= containerEl.clientWidth) {\n      breakpoint = point;\n    }\n  }\n  return breakpoint || 'max';\n}", "map": {"version": 3, "names": ["getWindow", "getBreakpoint", "breakpoints", "base", "arguments", "length", "undefined", "containerEl", "breakpoint", "window", "currentHeight", "innerHeight", "clientHeight", "points", "Object", "keys", "map", "point", "indexOf", "minRatio", "parseFloat", "substr", "value", "sort", "a", "b", "parseInt", "i", "matchMedia", "matches", "clientWidth"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/core/breakpoints/getBreakpoint.js"], "sourcesContent": ["import { getWindow } from 'ssr-window';\nexport default function getBreakpoint(breakpoints, base = 'window', containerEl) {\n  if (!breakpoints || base === 'container' && !containerEl) return undefined;\n  let breakpoint = false;\n  const window = getWindow();\n  const currentHeight = base === 'window' ? window.innerHeight : containerEl.clientHeight;\n  const points = Object.keys(breakpoints).map(point => {\n    if (typeof point === 'string' && point.indexOf('@') === 0) {\n      const minRatio = parseFloat(point.substr(1));\n      const value = currentHeight * minRatio;\n      return {\n        value,\n        point\n      };\n    }\n    return {\n      value: point,\n      point\n    };\n  });\n  points.sort((a, b) => parseInt(a.value, 10) - parseInt(b.value, 10));\n  for (let i = 0; i < points.length; i += 1) {\n    const {\n      point,\n      value\n    } = points[i];\n    if (base === 'window') {\n      if (window.matchMedia(`(min-width: ${value}px)`).matches) {\n        breakpoint = point;\n      }\n    } else if (value <= containerEl.clientWidth) {\n      breakpoint = point;\n    }\n  }\n  return breakpoint || 'max';\n}"], "mappings": "AAAA,SAASA,SAAS,QAAQ,YAAY;AACtC,eAAe,SAASC,aAAaA,CAACC,WAAW,EAAgC;EAAA,IAA9BC,IAAI,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,QAAQ;EAAA,IAAEG,WAAW,GAAAH,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAC7E,IAAI,CAACJ,WAAW,IAAIC,IAAI,KAAK,WAAW,IAAI,CAACI,WAAW,EAAE,OAAOD,SAAS;EAC1E,IAAIE,UAAU,GAAG,KAAK;EACtB,MAAMC,MAAM,GAAGT,SAAS,EAAE;EAC1B,MAAMU,aAAa,GAAGP,IAAI,KAAK,QAAQ,GAAGM,MAAM,CAACE,WAAW,GAAGJ,WAAW,CAACK,YAAY;EACvF,MAAMC,MAAM,GAAGC,MAAM,CAACC,IAAI,CAACb,WAAW,CAAC,CAACc,GAAG,CAACC,KAAK,IAAI;IACnD,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;MACzD,MAAMC,QAAQ,GAAGC,UAAU,CAACH,KAAK,CAACI,MAAM,CAAC,CAAC,CAAC,CAAC;MAC5C,MAAMC,KAAK,GAAGZ,aAAa,GAAGS,QAAQ;MACtC,OAAO;QACLG,KAAK;QACLL;MACF,CAAC;IACH;IACA,OAAO;MACLK,KAAK,EAAEL,KAAK;MACZA;IACF,CAAC;EACH,CAAC,CAAC;EACFJ,MAAM,CAACU,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKC,QAAQ,CAACF,CAAC,CAACF,KAAK,EAAE,EAAE,CAAC,GAAGI,QAAQ,CAACD,CAAC,CAACH,KAAK,EAAE,EAAE,CAAC,CAAC;EACpE,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGd,MAAM,CAACR,MAAM,EAAEsB,CAAC,IAAI,CAAC,EAAE;IACzC,MAAM;MACJV,KAAK;MACLK;IACF,CAAC,GAAGT,MAAM,CAACc,CAAC,CAAC;IACb,IAAIxB,IAAI,KAAK,QAAQ,EAAE;MACrB,IAAIM,MAAM,CAACmB,UAAU,CAAE,eAAcN,KAAM,KAAI,CAAC,CAACO,OAAO,EAAE;QACxDrB,UAAU,GAAGS,KAAK;MACpB;IACF,CAAC,MAAM,IAAIK,KAAK,IAAIf,WAAW,CAACuB,WAAW,EAAE;MAC3CtB,UAAU,GAAGS,KAAK;IACpB;EACF;EACA,OAAOT,UAAU,IAAI,KAAK;AAC5B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}