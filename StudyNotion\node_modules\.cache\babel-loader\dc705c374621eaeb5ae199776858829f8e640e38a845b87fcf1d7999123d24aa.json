{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.findElPosition = findElPosition;\nexports.getPointerPosition = getPointerPosition;\nexports.blurNode = blurNode;\nexports.focusNode = focusNode;\nexports.hasClass = hasClass;\n\n/**\n * Offset Left\n * getBoundingClientRect technique from\n * John Resig http://ejohn.org/blog/getboundingclientrect-is-awesome/\n *\n * @function findElPosition\n * @param {ReactNodeRef} el React Node ref from which to get offset\n * @return {Object}\n */\nfunction findElPosition(el) {\n  var box;\n  if (el.getBoundingClientRect && el.parentNode) {\n    box = el.getBoundingClientRect();\n  }\n  if (!box) {\n    return {\n      left: 0,\n      top: 0\n    };\n  }\n  var _document = document,\n    body = _document.body,\n    docEl = _document.documentElement;\n  var clientLeft = docEl.clientLeft || body.clientLeft || 0;\n  var scrollLeft = window.pageXOffset || body.scrollLeft;\n  var left = box.left + scrollLeft - clientLeft;\n  var clientTop = docEl.clientTop || body.clientTop || 0;\n  var scrollTop = window.pageYOffset || body.scrollTop;\n  var top = box.top + scrollTop - clientTop; // Android sometimes returns slightly off decimal values, so need to round\n\n  return {\n    left: Math.round(left),\n    top: Math.round(top)\n  };\n}\n/**\n * Get pointer position in a React Node ref\n * Returns an object with x and y coordinates.\n * The base on the coordinates are the bottom left of the element.\n *\n * @function getPointerPosition\n * @param {ReactNodeRef} el React Node ref on which to get the pointer position on\n * @param {Event} event Event object\n * @return {Object} This object will have x and y coordinates corresponding to the mouse position\n */\n\nfunction getPointerPosition(el, event) {\n  var position = {};\n  var box = findElPosition(el);\n  var boxW = el.offsetWidth;\n  var boxH = el.offsetHeight;\n  var boxY = box.top;\n  var boxX = box.left;\n  var evtPageY = event.pageY;\n  var evtPageX = event.pageX;\n  if (event.changedTouches) {\n    evtPageX = event.changedTouches[0].pageX;\n    evtPageY = event.changedTouches[0].pageY;\n  }\n  position.y = Math.max(0, Math.min(1, (boxY - evtPageY + boxH) / boxH));\n  position.x = Math.max(0, Math.min(1, (evtPageX - boxX) / boxW));\n  return position;\n} // blur an element\n\nfunction blurNode(reactNode) {\n  if (reactNode && reactNode.blur) {\n    reactNode.blur();\n  }\n} // focus an element\n\nfunction focusNode(reactNode) {\n  if (reactNode && reactNode.focus) {\n    reactNode.focus();\n  }\n} // check if an element has a class name\n\nfunction hasClass(elm, cls) {\n  var classes = elm.className.split(' ');\n  for (var i = 0; i < classes.length; i++) {\n    if (classes[i].toLowerCase() === cls.toLowerCase()) {\n      return true;\n    }\n  }\n  return false;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "findElPosition", "getPointerPosition", "blurNode", "focusNode", "hasClass", "el", "box", "getBoundingClientRect", "parentNode", "left", "top", "_document", "document", "body", "docEl", "documentElement", "clientLeft", "scrollLeft", "window", "pageXOffset", "clientTop", "scrollTop", "pageYOffset", "Math", "round", "event", "position", "boxW", "offsetWidth", "boxH", "offsetHeight", "boxY", "boxX", "evtPageY", "pageY", "evtPageX", "pageX", "changedTouches", "y", "max", "min", "x", "reactNode", "blur", "focus", "elm", "cls", "classes", "className", "split", "i", "length", "toLowerCase"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/video-react/lib/utils/dom.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.findElPosition = findElPosition;\nexports.getPointerPosition = getPointerPosition;\nexports.blurNode = blurNode;\nexports.focusNode = focusNode;\nexports.hasClass = hasClass;\n\n/**\n * Offset Left\n * getBoundingClientRect technique from\n * John Resig http://ejohn.org/blog/getboundingclientrect-is-awesome/\n *\n * @function findElPosition\n * @param {ReactNodeRef} el React Node ref from which to get offset\n * @return {Object}\n */\nfunction findElPosition(el) {\n  var box;\n\n  if (el.getBoundingClientRect && el.parentNode) {\n    box = el.getBoundingClientRect();\n  }\n\n  if (!box) {\n    return {\n      left: 0,\n      top: 0\n    };\n  }\n\n  var _document = document,\n      body = _document.body,\n      docEl = _document.documentElement;\n  var clientLeft = docEl.clientLeft || body.clientLeft || 0;\n  var scrollLeft = window.pageXOffset || body.scrollLeft;\n  var left = box.left + scrollLeft - clientLeft;\n  var clientTop = docEl.clientTop || body.clientTop || 0;\n  var scrollTop = window.pageYOffset || body.scrollTop;\n  var top = box.top + scrollTop - clientTop; // Android sometimes returns slightly off decimal values, so need to round\n\n  return {\n    left: Math.round(left),\n    top: Math.round(top)\n  };\n}\n/**\n * Get pointer position in a React Node ref\n * Returns an object with x and y coordinates.\n * The base on the coordinates are the bottom left of the element.\n *\n * @function getPointerPosition\n * @param {ReactNodeRef} el React Node ref on which to get the pointer position on\n * @param {Event} event Event object\n * @return {Object} This object will have x and y coordinates corresponding to the mouse position\n */\n\n\nfunction getPointerPosition(el, event) {\n  var position = {};\n  var box = findElPosition(el);\n  var boxW = el.offsetWidth;\n  var boxH = el.offsetHeight;\n  var boxY = box.top;\n  var boxX = box.left;\n  var evtPageY = event.pageY;\n  var evtPageX = event.pageX;\n\n  if (event.changedTouches) {\n    evtPageX = event.changedTouches[0].pageX;\n    evtPageY = event.changedTouches[0].pageY;\n  }\n\n  position.y = Math.max(0, Math.min(1, (boxY - evtPageY + boxH) / boxH));\n  position.x = Math.max(0, Math.min(1, (evtPageX - boxX) / boxW));\n  return position;\n} // blur an element\n\n\nfunction blurNode(reactNode) {\n  if (reactNode && reactNode.blur) {\n    reactNode.blur();\n  }\n} // focus an element\n\n\nfunction focusNode(reactNode) {\n  if (reactNode && reactNode.focus) {\n    reactNode.focus();\n  }\n} // check if an element has a class name\n\n\nfunction hasClass(elm, cls) {\n  var classes = elm.className.split(' ');\n\n  for (var i = 0; i < classes.length; i++) {\n    if (classes[i].toLowerCase() === cls.toLowerCase()) {\n      return true;\n    }\n  }\n\n  return false;\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,cAAc,GAAGA,cAAc;AACvCF,OAAO,CAACG,kBAAkB,GAAGA,kBAAkB;AAC/CH,OAAO,CAACI,QAAQ,GAAGA,QAAQ;AAC3BJ,OAAO,CAACK,SAAS,GAAGA,SAAS;AAC7BL,OAAO,CAACM,QAAQ,GAAGA,QAAQ;;AAE3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASJ,cAAcA,CAACK,EAAE,EAAE;EAC1B,IAAIC,GAAG;EAEP,IAAID,EAAE,CAACE,qBAAqB,IAAIF,EAAE,CAACG,UAAU,EAAE;IAC7CF,GAAG,GAAGD,EAAE,CAACE,qBAAqB,EAAE;EAClC;EAEA,IAAI,CAACD,GAAG,EAAE;IACR,OAAO;MACLG,IAAI,EAAE,CAAC;MACPC,GAAG,EAAE;IACP,CAAC;EACH;EAEA,IAAIC,SAAS,GAAGC,QAAQ;IACpBC,IAAI,GAAGF,SAAS,CAACE,IAAI;IACrBC,KAAK,GAAGH,SAAS,CAACI,eAAe;EACrC,IAAIC,UAAU,GAAGF,KAAK,CAACE,UAAU,IAAIH,IAAI,CAACG,UAAU,IAAI,CAAC;EACzD,IAAIC,UAAU,GAAGC,MAAM,CAACC,WAAW,IAAIN,IAAI,CAACI,UAAU;EACtD,IAAIR,IAAI,GAAGH,GAAG,CAACG,IAAI,GAAGQ,UAAU,GAAGD,UAAU;EAC7C,IAAII,SAAS,GAAGN,KAAK,CAACM,SAAS,IAAIP,IAAI,CAACO,SAAS,IAAI,CAAC;EACtD,IAAIC,SAAS,GAAGH,MAAM,CAACI,WAAW,IAAIT,IAAI,CAACQ,SAAS;EACpD,IAAIX,GAAG,GAAGJ,GAAG,CAACI,GAAG,GAAGW,SAAS,GAAGD,SAAS,CAAC,CAAC;;EAE3C,OAAO;IACLX,IAAI,EAAEc,IAAI,CAACC,KAAK,CAACf,IAAI,CAAC;IACtBC,GAAG,EAAEa,IAAI,CAACC,KAAK,CAACd,GAAG;EACrB,CAAC;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA,SAAST,kBAAkBA,CAACI,EAAE,EAAEoB,KAAK,EAAE;EACrC,IAAIC,QAAQ,GAAG,CAAC,CAAC;EACjB,IAAIpB,GAAG,GAAGN,cAAc,CAACK,EAAE,CAAC;EAC5B,IAAIsB,IAAI,GAAGtB,EAAE,CAACuB,WAAW;EACzB,IAAIC,IAAI,GAAGxB,EAAE,CAACyB,YAAY;EAC1B,IAAIC,IAAI,GAAGzB,GAAG,CAACI,GAAG;EAClB,IAAIsB,IAAI,GAAG1B,GAAG,CAACG,IAAI;EACnB,IAAIwB,QAAQ,GAAGR,KAAK,CAACS,KAAK;EAC1B,IAAIC,QAAQ,GAAGV,KAAK,CAACW,KAAK;EAE1B,IAAIX,KAAK,CAACY,cAAc,EAAE;IACxBF,QAAQ,GAAGV,KAAK,CAACY,cAAc,CAAC,CAAC,CAAC,CAACD,KAAK;IACxCH,QAAQ,GAAGR,KAAK,CAACY,cAAc,CAAC,CAAC,CAAC,CAACH,KAAK;EAC1C;EAEAR,QAAQ,CAACY,CAAC,GAAGf,IAAI,CAACgB,GAAG,CAAC,CAAC,EAAEhB,IAAI,CAACiB,GAAG,CAAC,CAAC,EAAE,CAACT,IAAI,GAAGE,QAAQ,GAAGJ,IAAI,IAAIA,IAAI,CAAC,CAAC;EACtEH,QAAQ,CAACe,CAAC,GAAGlB,IAAI,CAACgB,GAAG,CAAC,CAAC,EAAEhB,IAAI,CAACiB,GAAG,CAAC,CAAC,EAAE,CAACL,QAAQ,GAAGH,IAAI,IAAIL,IAAI,CAAC,CAAC;EAC/D,OAAOD,QAAQ;AACjB,CAAC,CAAC;;AAGF,SAASxB,QAAQA,CAACwC,SAAS,EAAE;EAC3B,IAAIA,SAAS,IAAIA,SAAS,CAACC,IAAI,EAAE;IAC/BD,SAAS,CAACC,IAAI,EAAE;EAClB;AACF,CAAC,CAAC;;AAGF,SAASxC,SAASA,CAACuC,SAAS,EAAE;EAC5B,IAAIA,SAAS,IAAIA,SAAS,CAACE,KAAK,EAAE;IAChCF,SAAS,CAACE,KAAK,EAAE;EACnB;AACF,CAAC,CAAC;;AAGF,SAASxC,QAAQA,CAACyC,GAAG,EAAEC,GAAG,EAAE;EAC1B,IAAIC,OAAO,GAAGF,GAAG,CAACG,SAAS,CAACC,KAAK,CAAC,GAAG,CAAC;EAEtC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,OAAO,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;IACvC,IAAIH,OAAO,CAACG,CAAC,CAAC,CAACE,WAAW,EAAE,KAAKN,GAAG,CAACM,WAAW,EAAE,EAAE;MAClD,OAAO,IAAI;IACb;EACF;EAEA,OAAO,KAAK;AACd"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}