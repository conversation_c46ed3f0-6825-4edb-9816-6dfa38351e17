{"ast": null, "code": "import { __awaiter, __generator, __read, __spreadArray } from \"tslib\";\nimport { toFileWithPath } from './file';\nvar FILES_TO_IGNORE = [\n// Thumbnail cache files for macOS and Windows\n'.DS_Store', 'Thumbs.db' // Windows\n];\n/**\n * Convert a DragEvent's DataTrasfer object to a list of File objects\n * NOTE: If some of the items are folders,\n * everything will be flattened and placed in the same list but the paths will be kept as a {path} property.\n *\n * EXPERIMENTAL: A list of https://developer.mozilla.org/en-US/docs/Web/API/FileSystemHandle objects can also be passed as an arg\n * and a list of File objects will be returned.\n *\n * @param evt\n */\nexport function fromEvent(evt) {\n  return __awaiter(this, void 0, void 0, function () {\n    return __generator(this, function (_a) {\n      if (isObject(evt) && isDataTransfer(evt.dataTransfer)) {\n        return [2 /*return*/, getDataTransferFiles(evt.dataTransfer, evt.type)];\n      } else if (isChangeEvt(evt)) {\n        return [2 /*return*/, getInputFiles(evt)];\n      } else if (Array.isArray(evt) && evt.every(function (item) {\n        return 'getFile' in item && typeof item.getFile === 'function';\n      })) {\n        return [2 /*return*/, getFsHandleFiles(evt)];\n      }\n      return [2 /*return*/, []];\n    });\n  });\n}\nfunction isDataTransfer(value) {\n  return isObject(value);\n}\nfunction isChangeEvt(value) {\n  return isObject(value) && isObject(value.target);\n}\nfunction isObject(v) {\n  return typeof v === 'object' && v !== null;\n}\nfunction getInputFiles(evt) {\n  return fromList(evt.target.files).map(function (file) {\n    return toFileWithPath(file);\n  });\n}\n// Ee expect each handle to be https://developer.mozilla.org/en-US/docs/Web/API/FileSystemFileHandle\nfunction getFsHandleFiles(handles) {\n  return __awaiter(this, void 0, void 0, function () {\n    var files;\n    return __generator(this, function (_a) {\n      switch (_a.label) {\n        case 0:\n          return [4 /*yield*/, Promise.all(handles.map(function (h) {\n            return h.getFile();\n          }))];\n        case 1:\n          files = _a.sent();\n          return [2 /*return*/, files.map(function (file) {\n            return toFileWithPath(file);\n          })];\n      }\n    });\n  });\n}\nfunction getDataTransferFiles(dt, type) {\n  return __awaiter(this, void 0, void 0, function () {\n    var items, files;\n    return __generator(this, function (_a) {\n      switch (_a.label) {\n        case 0:\n          if (!dt.items) return [3 /*break*/, 2];\n          items = fromList(dt.items).filter(function (item) {\n            return item.kind === 'file';\n          });\n          // According to https://html.spec.whatwg.org/multipage/dnd.html#dndevents,\n          // only 'dragstart' and 'drop' has access to the data (source node)\n          if (type !== 'drop') {\n            return [2 /*return*/, items];\n          }\n          return [4 /*yield*/, Promise.all(items.map(toFilePromises))];\n        case 1:\n          files = _a.sent();\n          return [2 /*return*/, noIgnoredFiles(flatten(files))];\n        case 2:\n          return [2 /*return*/, noIgnoredFiles(fromList(dt.files).map(function (file) {\n            return toFileWithPath(file);\n          }))];\n      }\n    });\n  });\n}\nfunction noIgnoredFiles(files) {\n  return files.filter(function (file) {\n    return FILES_TO_IGNORE.indexOf(file.name) === -1;\n  });\n}\n// IE11 does not support Array.from()\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/from#Browser_compatibility\n// https://developer.mozilla.org/en-US/docs/Web/API/FileList\n// https://developer.mozilla.org/en-US/docs/Web/API/DataTransferItemList\nfunction fromList(items) {\n  if (items === null) {\n    return [];\n  }\n  var files = [];\n  // tslint:disable: prefer-for-of\n  for (var i = 0; i < items.length; i++) {\n    var file = items[i];\n    files.push(file);\n  }\n  return files;\n}\n// https://developer.mozilla.org/en-US/docs/Web/API/DataTransferItem\nfunction toFilePromises(item) {\n  if (typeof item.webkitGetAsEntry !== 'function') {\n    return fromDataTransferItem(item);\n  }\n  var entry = item.webkitGetAsEntry();\n  // Safari supports dropping an image node from a different window and can be retrieved using\n  // the DataTransferItem.getAsFile() API\n  // NOTE: FileSystemEntry.file() throws if trying to get the file\n  if (entry && entry.isDirectory) {\n    return fromDirEntry(entry);\n  }\n  return fromDataTransferItem(item);\n}\nfunction flatten(items) {\n  return items.reduce(function (acc, files) {\n    return __spreadArray(__spreadArray([], __read(acc), false), __read(Array.isArray(files) ? flatten(files) : [files]), false);\n  }, []);\n}\nfunction fromDataTransferItem(item) {\n  var file = item.getAsFile();\n  if (!file) {\n    return Promise.reject(\"\".concat(item, \" is not a File\"));\n  }\n  var fwp = toFileWithPath(file);\n  return Promise.resolve(fwp);\n}\n// https://developer.mozilla.org/en-US/docs/Web/API/FileSystemEntry\nfunction fromEntry(entry) {\n  return __awaiter(this, void 0, void 0, function () {\n    return __generator(this, function (_a) {\n      return [2 /*return*/, entry.isDirectory ? fromDirEntry(entry) : fromFileEntry(entry)];\n    });\n  });\n}\n// https://developer.mozilla.org/en-US/docs/Web/API/FileSystemDirectoryEntry\nfunction fromDirEntry(entry) {\n  var reader = entry.createReader();\n  return new Promise(function (resolve, reject) {\n    var entries = [];\n    function readEntries() {\n      var _this = this;\n      // https://developer.mozilla.org/en-US/docs/Web/API/FileSystemDirectoryEntry/createReader\n      // https://developer.mozilla.org/en-US/docs/Web/API/FileSystemDirectoryReader/readEntries\n      reader.readEntries(function (batch) {\n        return __awaiter(_this, void 0, void 0, function () {\n          var files, err_1, items;\n          return __generator(this, function (_a) {\n            switch (_a.label) {\n              case 0:\n                if (!!batch.length) return [3 /*break*/, 5];\n                _a.label = 1;\n              case 1:\n                _a.trys.push([1, 3,, 4]);\n                return [4 /*yield*/, Promise.all(entries)];\n              case 2:\n                files = _a.sent();\n                resolve(files);\n                return [3 /*break*/, 4];\n              case 3:\n                err_1 = _a.sent();\n                reject(err_1);\n                return [3 /*break*/, 4];\n              case 4:\n                return [3 /*break*/, 6];\n              case 5:\n                items = Promise.all(batch.map(fromEntry));\n                entries.push(items);\n                // Continue reading\n                readEntries();\n                _a.label = 6;\n              case 6:\n                return [2 /*return*/];\n            }\n          });\n        });\n      }, function (err) {\n        reject(err);\n      });\n    }\n    readEntries();\n  });\n}\n// https://developer.mozilla.org/en-US/docs/Web/API/FileSystemFileEntry\nfunction fromFileEntry(entry) {\n  return __awaiter(this, void 0, void 0, function () {\n    return __generator(this, function (_a) {\n      return [2 /*return*/, new Promise(function (resolve, reject) {\n        entry.file(function (file) {\n          var fwp = toFileWithPath(file, entry.fullPath);\n          resolve(fwp);\n        }, function (err) {\n          reject(err);\n        });\n      })];\n    });\n  });\n}", "map": {"version": 3, "names": ["toFileWithPath", "FILES_TO_IGNORE", "fromEvent", "evt", "isObject", "isDataTransfer", "dataTransfer", "getDataTransferFiles", "type", "isChangeEvt", "getInputFiles", "Array", "isArray", "every", "item", "getFile", "getFsHandleFiles", "value", "target", "v", "fromList", "files", "map", "file", "handles", "Promise", "all", "h", "_a", "sent", "dt", "items", "filter", "kind", "toFilePromises", "noIgnoredFiles", "flatten", "indexOf", "name", "i", "length", "push", "webkitGetAsEntry", "fromDataTransferItem", "entry", "isDirectory", "fromDirEntry", "reduce", "acc", "__spread<PERSON><PERSON>y", "__read", "getAsFile", "reject", "concat", "fwp", "resolve", "fromEntry", "fromFileEntry", "reader", "createReader", "entries", "readEntries", "_this", "batch", "__awaiter", "err_1", "err", "fullPath"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\file-selector\\src\\file-selector.ts"], "sourcesContent": ["import {FileWithPath, toFileWithPath} from './file';\n\n\nconst FILES_TO_IGNORE = [\n    // Thumbnail cache files for macOS and Windows\n    '.DS_Store', // macOs\n    'Thumbs.db'  // Windows\n];\n\n\n/**\n * Convert a DragEvent's DataTrasfer object to a list of File objects\n * NOTE: If some of the items are folders,\n * everything will be flattened and placed in the same list but the paths will be kept as a {path} property.\n *\n * EXPERIMENTAL: A list of https://developer.mozilla.org/en-US/docs/Web/API/FileSystemHandle objects can also be passed as an arg\n * and a list of File objects will be returned.\n *\n * @param evt\n */\nexport async function fromEvent(evt: Event | any): Promise<(FileWithPath | DataTransferItem)[]> {\n    if (isObject<DragEvent>(evt) && isDataTransfer(evt.dataTransfer)) {\n        return getDataTransferFiles(evt.dataTransfer, evt.type);\n    } else if (isChangeEvt(evt)) {\n        return getInputFiles(evt);\n    } else if (Array.isArray(evt) && evt.every(item => 'getFile' in item && typeof item.getFile === 'function')) {\n        return getFsHandleFiles(evt)\n    }\n    return [];\n}\n\nfunction isDataTransfer(value: any): value is DataTransfer {\n    return isObject(value);\n}\n\nfunction isChangeEvt(value: any): value is Event {\n    return isObject<Event>(value) && isObject(value.target);\n}\n\nfunction isObject<T>(v: any): v is T {\n    return typeof v === 'object' && v !== null\n}\n\nfunction getInputFiles(evt: Event) {\n    return fromList<FileWithPath>((evt.target as HTMLInputElement).files).map(file => toFileWithPath(file));\n}\n\n// Ee expect each handle to be https://developer.mozilla.org/en-US/docs/Web/API/FileSystemFileHandle\nasync function getFsHandleFiles(handles: any[]) {\n    const files = await Promise.all(handles.map(h => h.getFile()));\n    return files.map(file => toFileWithPath(file));\n}\n\n\nasync function getDataTransferFiles(dt: DataTransfer, type: string) {\n    // IE11 does not support dataTransfer.items\n    // See https://developer.mozilla.org/en-US/docs/Web/API/DataTransfer/items#Browser_compatibility\n    if (dt.items) {\n        const items = fromList<DataTransferItem>(dt.items)\n            .filter(item => item.kind === 'file');\n        // According to https://html.spec.whatwg.org/multipage/dnd.html#dndevents,\n        // only 'dragstart' and 'drop' has access to the data (source node)\n        if (type !== 'drop') {\n            return items;\n        }\n        const files = await Promise.all(items.map(toFilePromises));\n        return noIgnoredFiles(flatten<FileWithPath>(files));\n    }\n\n    return noIgnoredFiles(fromList<FileWithPath>(dt.files)\n        .map(file => toFileWithPath(file)));\n}\n\nfunction noIgnoredFiles(files: FileWithPath[]) {\n    return files.filter(file => FILES_TO_IGNORE.indexOf(file.name) === -1);\n}\n\n// IE11 does not support Array.from()\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/from#Browser_compatibility\n// https://developer.mozilla.org/en-US/docs/Web/API/FileList\n// https://developer.mozilla.org/en-US/docs/Web/API/DataTransferItemList\nfunction fromList<T>(items: DataTransferItemList | FileList | null): T[] {\n    if (items === null) {\n        return [];\n    }\n\n    const files = [];\n\n    // tslint:disable: prefer-for-of\n    for (let i = 0; i < items.length; i++) {\n        const file = items[i];\n        files.push(file);\n    }\n\n    return files as any;\n}\n\n// https://developer.mozilla.org/en-US/docs/Web/API/DataTransferItem\nfunction toFilePromises(item: DataTransferItem) {\n    if (typeof item.webkitGetAsEntry !== 'function') {\n        return fromDataTransferItem(item);\n    }\n\n    const entry = item.webkitGetAsEntry();\n\n    // Safari supports dropping an image node from a different window and can be retrieved using\n    // the DataTransferItem.getAsFile() API\n    // NOTE: FileSystemEntry.file() throws if trying to get the file\n    if (entry && entry.isDirectory) {\n        return fromDirEntry(entry) as any;\n    }\n\n    return fromDataTransferItem(item);\n}\n\nfunction flatten<T>(items: any[]): T[] {\n    return items.reduce((acc, files) => [\n        ...acc,\n        ...(Array.isArray(files) ? flatten(files) : [files])\n    ], []);\n}\n\nfunction fromDataTransferItem(item: DataTransferItem) {\n    const file = item.getAsFile();\n    if (!file) {\n        return Promise.reject(`${item} is not a File`);\n    }\n    const fwp = toFileWithPath(file);\n    return Promise.resolve(fwp);\n}\n\n// https://developer.mozilla.org/en-US/docs/Web/API/FileSystemEntry\nasync function fromEntry(entry: any) {\n    return entry.isDirectory ? fromDirEntry(entry) : fromFileEntry(entry);\n}\n\n// https://developer.mozilla.org/en-US/docs/Web/API/FileSystemDirectoryEntry\nfunction fromDirEntry(entry: any) {\n    const reader = entry.createReader();\n\n    return new Promise<FileArray[]>((resolve, reject) => {\n        const entries: Promise<FileValue[]>[] = [];\n\n        function readEntries() {\n            // https://developer.mozilla.org/en-US/docs/Web/API/FileSystemDirectoryEntry/createReader\n            // https://developer.mozilla.org/en-US/docs/Web/API/FileSystemDirectoryReader/readEntries\n            reader.readEntries(async (batch: any[]) => {\n                if (!batch.length) {\n                    // Done reading directory\n                    try {\n                        const files = await Promise.all(entries);\n                        resolve(files);\n                    } catch (err) {\n                        reject(err);\n                    }\n                } else {\n                    const items = Promise.all(batch.map(fromEntry));\n                    entries.push(items);\n\n                    // Continue reading\n                    readEntries();\n                }\n            }, (err: any) => {\n                reject(err);\n            });\n        }\n\n        readEntries();\n    });\n}\n\n// https://developer.mozilla.org/en-US/docs/Web/API/FileSystemFileEntry\nasync function fromFileEntry(entry: any) {\n    return new Promise<FileWithPath>((resolve, reject) => {\n        entry.file((file: FileWithPath) => {\n            const fwp = toFileWithPath(file, entry.fullPath);\n            resolve(fwp);\n        }, (err: any) => {\n            reject(err);\n        });\n    });\n}\n\n// Infinite type recursion\n// https://github.com/Microsoft/TypeScript/issues/3496#issuecomment-*********\ninterface FileArray extends Array<FileValue> {}\ntype FileValue = FileWithPath\n    | FileArray[];\n"], "mappings": ";AAAA,SAAsBA,cAAc,QAAO,QAAQ;AAGnD,IAAMC,eAAe,GAAG;AACpB;AACA,WAAW,EACX,WAAW,CAAE;AAAA,CAChB;AAGD;;;;;;;;;;AAUA,OAAM,SAAgBC,SAASA,CAACC,GAAgB;;;MAC5C,IAAIC,QAAQ,CAAYD,GAAG,CAAC,IAAIE,cAAc,CAACF,GAAG,CAACG,YAAY,CAAC,EAAE;QAC9D,sBAAOC,oBAAoB,CAACJ,GAAG,CAACG,YAAY,EAAEH,GAAG,CAACK,IAAI,CAAC;OAC1D,MAAM,IAAIC,WAAW,CAACN,GAAG,CAAC,EAAE;QACzB,sBAAOO,aAAa,CAACP,GAAG,CAAC;OAC5B,MAAM,IAAIQ,KAAK,CAACC,OAAO,CAACT,GAAG,CAAC,IAAIA,GAAG,CAACU,KAAK,CAAC,UAAAC,IAAI;QAAI,gBAAS,IAAIA,IAAI,IAAI,OAAOA,IAAI,CAACC,OAAO,KAAK,UAAU;MAAvD,CAAuD,CAAC,EAAE;QACzG,sBAAOC,gBAAgB,CAACb,GAAG,CAAC;;MAEhC,sBAAO,EAAE;;;;AAGb,SAASE,cAAcA,CAACY,KAAU;EAC9B,OAAOb,QAAQ,CAACa,KAAK,CAAC;AAC1B;AAEA,SAASR,WAAWA,CAACQ,KAAU;EAC3B,OAAOb,QAAQ,CAAQa,KAAK,CAAC,IAAIb,QAAQ,CAACa,KAAK,CAACC,MAAM,CAAC;AAC3D;AAEA,SAASd,QAAQA,CAAIe,CAAM;EACvB,OAAO,OAAOA,CAAC,KAAK,QAAQ,IAAIA,CAAC,KAAK,IAAI;AAC9C;AAEA,SAAST,aAAaA,CAACP,GAAU;EAC7B,OAAOiB,QAAQ,CAAgBjB,GAAG,CAACe,MAA2B,CAACG,KAAK,CAAC,CAACC,GAAG,CAAC,UAAAC,IAAI;IAAI,OAAAvB,cAAc,CAACuB,IAAI,CAAC;EAApB,CAAoB,CAAC;AAC3G;AAEA;AACA,SAAeP,gBAAgBA,CAACQ,OAAc;;;;;;UAC5B,qBAAMC,OAAO,CAACC,GAAG,CAACF,OAAO,CAACF,GAAG,CAAC,UAAAK,CAAC;YAAI,OAAAA,CAAC,CAACZ,OAAO,EAAE;UAAX,CAAW,CAAC,CAAC;;UAAxDM,KAAK,GAAGO,EAAA,CAAAC,IAAA,EAAgD;UAC9D,sBAAOR,KAAK,CAACC,GAAG,CAAC,UAAAC,IAAI;YAAI,OAAAvB,cAAc,CAACuB,IAAI,CAAC;UAApB,CAAoB,CAAC;MAAC;;;;AAInD,SAAehB,oBAAoBA,CAACuB,EAAgB,EAAEtB,IAAY;;;;;;eAG1DsB,EAAE,CAACC,KAAK,EAAR;UACMA,KAAK,GAAGX,QAAQ,CAAmBU,EAAE,CAACC,KAAK,CAAC,CAC7CC,MAAM,CAAC,UAAAlB,IAAI;YAAI,OAAAA,IAAI,CAACmB,IAAI,KAAK,MAAM;UAApB,CAAoB,CAAC;UACzC;UACA;UACA,IAAIzB,IAAI,KAAK,MAAM,EAAE;YACjB,sBAAOuB,KAAK;;UAEF,qBAAMN,OAAO,CAACC,GAAG,CAACK,KAAK,CAACT,GAAG,CAACY,cAAc,CAAC,CAAC;;UAApDb,KAAK,GAAGO,EAAA,CAAAC,IAAA,EAA4C;UAC1D,sBAAOM,cAAc,CAACC,OAAO,CAAef,KAAK,CAAC,CAAC;;UAGvD,sBAAOc,cAAc,CAACf,QAAQ,CAAeU,EAAE,CAACT,KAAK,CAAC,CACjDC,GAAG,CAAC,UAAAC,IAAI;YAAI,OAAAvB,cAAc,CAACuB,IAAI,CAAC;UAApB,CAAoB,CAAC,CAAC;MAAC;;;;AAG5C,SAASY,cAAcA,CAACd,KAAqB;EACzC,OAAOA,KAAK,CAACW,MAAM,CAAC,UAAAT,IAAI;IAAI,OAAAtB,eAAe,CAACoC,OAAO,CAACd,IAAI,CAACe,IAAI,CAAC,KAAK,CAAC,CAAC;EAAzC,CAAyC,CAAC;AAC1E;AAEA;AACA;AACA;AACA;AACA,SAASlB,QAAQA,CAAIW,KAA6C;EAC9D,IAAIA,KAAK,KAAK,IAAI,EAAE;IAChB,OAAO,EAAE;;EAGb,IAAMV,KAAK,GAAG,EAAE;EAEhB;EACA,KAAK,IAAIkB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,KAAK,CAACS,MAAM,EAAED,CAAC,EAAE,EAAE;IACnC,IAAMhB,IAAI,GAAGQ,KAAK,CAACQ,CAAC,CAAC;IACrBlB,KAAK,CAACoB,IAAI,CAAClB,IAAI,CAAC;;EAGpB,OAAOF,KAAY;AACvB;AAEA;AACA,SAASa,cAAcA,CAACpB,IAAsB;EAC1C,IAAI,OAAOA,IAAI,CAAC4B,gBAAgB,KAAK,UAAU,EAAE;IAC7C,OAAOC,oBAAoB,CAAC7B,IAAI,CAAC;;EAGrC,IAAM8B,KAAK,GAAG9B,IAAI,CAAC4B,gBAAgB,EAAE;EAErC;EACA;EACA;EACA,IAAIE,KAAK,IAAIA,KAAK,CAACC,WAAW,EAAE;IAC5B,OAAOC,YAAY,CAACF,KAAK,CAAQ;;EAGrC,OAAOD,oBAAoB,CAAC7B,IAAI,CAAC;AACrC;AAEA,SAASsB,OAAOA,CAAIL,KAAY;EAC5B,OAAOA,KAAK,CAACgB,MAAM,CAAC,UAACC,GAAG,EAAE3B,KAAK;IAAK,OAAA4B,aAAA,CAAAA,aAAA,KAAAC,MAAA,CAC7BF,GAAG,WAAAE,MAAA,CACFvC,KAAK,CAACC,OAAO,CAACS,KAAK,CAAC,GAAGe,OAAO,CAACf,KAAK,CAAC,GAAG,CAACA,KAAK,CAAC,CAAC;EAFpB,CAGnC,EAAE,EAAE,CAAC;AACV;AAEA,SAASsB,oBAAoBA,CAAC7B,IAAsB;EAChD,IAAMS,IAAI,GAAGT,IAAI,CAACqC,SAAS,EAAE;EAC7B,IAAI,CAAC5B,IAAI,EAAE;IACP,OAAOE,OAAO,CAAC2B,MAAM,CAAC,GAAAC,MAAA,CAAGvC,IAAI,mBAAgB,CAAC;;EAElD,IAAMwC,GAAG,GAAGtD,cAAc,CAACuB,IAAI,CAAC;EAChC,OAAOE,OAAO,CAAC8B,OAAO,CAACD,GAAG,CAAC;AAC/B;AAEA;AACA,SAAeE,SAASA,CAACZ,KAAU;;;MAC/B,sBAAOA,KAAK,CAACC,WAAW,GAAGC,YAAY,CAACF,KAAK,CAAC,GAAGa,aAAa,CAACb,KAAK,CAAC;;;;AAGzE;AACA,SAASE,YAAYA,CAACF,KAAU;EAC5B,IAAMc,MAAM,GAAGd,KAAK,CAACe,YAAY,EAAE;EAEnC,OAAO,IAAIlC,OAAO,CAAc,UAAC8B,OAAO,EAAEH,MAAM;IAC5C,IAAMQ,OAAO,GAA2B,EAAE;IAE1C,SAASC,WAAWA,CAAA;MAApB,IAAAC,KAAA;MACI;MACA;MACAJ,MAAM,CAACG,WAAW,CAAC,UAAOE,KAAY;QAAA,OAAAC,SAAA,CAAAF,KAAA;;;;;qBAC9B,CAACC,KAAK,CAACvB,MAAM,EAAb;;;;gBAGkB,qBAAMf,OAAO,CAACC,GAAG,CAACkC,OAAO,CAAC;;gBAAlCvC,KAAK,GAAGO,EAAA,CAAAC,IAAA,EAA0B;gBACxC0B,OAAO,CAAClC,KAAK,CAAC;;;;gBAEd+B,MAAM,CAACa,KAAG,CAAC;;;;;gBAGTlC,KAAK,GAAGN,OAAO,CAACC,GAAG,CAACqC,KAAK,CAACzC,GAAG,CAACkC,SAAS,CAAC,CAAC;gBAC/CI,OAAO,CAACnB,IAAI,CAACV,KAAK,CAAC;gBAEnB;gBACA8B,WAAW,EAAE;;;;;;;OAEpB,EAAE,UAACK,GAAQ;QACRd,MAAM,CAACc,GAAG,CAAC;MACf,CAAC,CAAC;IACN;IAEAL,WAAW,EAAE;EACjB,CAAC,CAAC;AACN;AAEA;AACA,SAAeJ,aAAaA,CAACb,KAAU;;;MACnC,sBAAO,IAAInB,OAAO,CAAe,UAAC8B,OAAO,EAAEH,MAAM;QAC7CR,KAAK,CAACrB,IAAI,CAAC,UAACA,IAAkB;UAC1B,IAAM+B,GAAG,GAAGtD,cAAc,CAACuB,IAAI,EAAEqB,KAAK,CAACuB,QAAQ,CAAC;UAChDZ,OAAO,CAACD,GAAG,CAAC;QAChB,CAAC,EAAE,UAACY,GAAQ;UACRd,MAAM,CAACc,GAAG,CAAC;QACf,CAAC,CAAC;MACN,CAAC,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}