{"ast": null, "code": "import { createElement, getSlideTransformEl } from './utils.js';\nexport default function createShadow(params, slideEl, side) {\n  const shadowClass = `swiper-slide-shadow${side ? `-${side}` : ''}`;\n  const shadowContainer = getSlideTransformEl(slideEl);\n  let shadowEl = shadowContainer.querySelector(`.${shadowClass}`);\n  if (!shadowEl) {\n    shadowEl = createElement('div', `swiper-slide-shadow${side ? `-${side}` : ''}`);\n    shadowContainer.append(shadowEl);\n  }\n  return shadowEl;\n}", "map": {"version": 3, "names": ["createElement", "getSlideTransformEl", "createShadow", "params", "slideEl", "side", "shadowClass", "shadow<PERSON><PERSON><PERSON>", "shadowEl", "querySelector", "append"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/shared/create-shadow.js"], "sourcesContent": ["import { createElement, getSlideTransformEl } from './utils.js';\nexport default function createShadow(params, slideEl, side) {\n  const shadowClass = `swiper-slide-shadow${side ? `-${side}` : ''}`;\n  const shadowContainer = getSlideTransformEl(slideEl);\n  let shadowEl = shadowContainer.querySelector(`.${shadowClass}`);\n  if (!shadowEl) {\n    shadowEl = createElement('div', `swiper-slide-shadow${side ? `-${side}` : ''}`);\n    shadowContainer.append(shadowEl);\n  }\n  return shadowEl;\n}"], "mappings": "AAAA,SAASA,aAAa,EAAEC,mBAAmB,QAAQ,YAAY;AAC/D,eAAe,SAASC,YAAYA,CAACC,MAAM,EAAEC,OAAO,EAAEC,IAAI,EAAE;EAC1D,MAAMC,WAAW,GAAI,sBAAqBD,IAAI,GAAI,IAAGA,IAAK,EAAC,GAAG,EAAG,EAAC;EAClE,MAAME,eAAe,GAAGN,mBAAmB,CAACG,OAAO,CAAC;EACpD,IAAII,QAAQ,GAAGD,eAAe,CAACE,aAAa,CAAE,IAAGH,WAAY,EAAC,CAAC;EAC/D,IAAI,CAACE,QAAQ,EAAE;IACbA,QAAQ,GAAGR,aAAa,CAAC,KAAK,EAAG,sBAAqBK,IAAI,GAAI,IAAGA,IAAK,EAAC,GAAG,EAAG,EAAC,CAAC;IAC/EE,eAAe,CAACG,MAAM,CAACF,QAAQ,CAAC;EAClC;EACA,OAAOA,QAAQ;AACjB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}