{"ast": null, "code": "import { getWindow } from 'ssr-window';\nimport { elementParents } from '../../../shared/utils.js';\nexport default function Observer(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on,\n    emit\n  } = _ref;\n  const observers = [];\n  const window = getWindow();\n  const attach = function (target) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    const ObserverFunc = window.MutationObserver || window.WebkitMutationObserver;\n    const observer = new ObserverFunc(mutations => {\n      // The observerUpdate event should only be triggered\n      // once despite the number of mutations.  Additional\n      // triggers are redundant and are very costly\n      if (swiper.__preventObserver__) return;\n      if (mutations.length === 1) {\n        emit('observerUpdate', mutations[0]);\n        return;\n      }\n      const observerUpdate = function observerUpdate() {\n        emit('observerUpdate', mutations[0]);\n      };\n      if (window.requestAnimationFrame) {\n        window.requestAnimationFrame(observerUpdate);\n      } else {\n        window.setTimeout(observerUpdate, 0);\n      }\n    });\n    observer.observe(target, {\n      attributes: typeof options.attributes === 'undefined' ? true : options.attributes,\n      childList: typeof options.childList === 'undefined' ? true : options.childList,\n      characterData: typeof options.characterData === 'undefined' ? true : options.characterData\n    });\n    observers.push(observer);\n  };\n  const init = () => {\n    if (!swiper.params.observer) return;\n    if (swiper.params.observeParents) {\n      const containerParents = elementParents(swiper.el);\n      for (let i = 0; i < containerParents.length; i += 1) {\n        attach(containerParents[i]);\n      }\n    }\n    // Observe container\n    attach(swiper.el, {\n      childList: swiper.params.observeSlideChildren\n    });\n\n    // Observe wrapper\n    attach(swiper.wrapperEl, {\n      attributes: false\n    });\n  };\n  const destroy = () => {\n    observers.forEach(observer => {\n      observer.disconnect();\n    });\n    observers.splice(0, observers.length);\n  };\n  extendParams({\n    observer: false,\n    observeParents: false,\n    observeSlideChildren: false\n  });\n  on('init', init);\n  on('destroy', destroy);\n}", "map": {"version": 3, "names": ["getWindow", "elementParents", "Observer", "_ref", "swiper", "extendParams", "on", "emit", "observers", "window", "attach", "target", "options", "arguments", "length", "undefined", "ObserverFunc", "MutationObserver", "WebkitMutationObserver", "observer", "mutations", "__preventObserver__", "observerUpdate", "requestAnimationFrame", "setTimeout", "observe", "attributes", "childList", "characterData", "push", "init", "params", "observeParents", "containerParents", "el", "i", "observeSlideChildren", "wrapperEl", "destroy", "for<PERSON>ach", "disconnect", "splice"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/core/modules/observer/observer.js"], "sourcesContent": ["import { getWindow } from 'ssr-window';\nimport { elementParents } from '../../../shared/utils.js';\nexport default function Observer({\n  swiper,\n  extendParams,\n  on,\n  emit\n}) {\n  const observers = [];\n  const window = getWindow();\n  const attach = (target, options = {}) => {\n    const ObserverFunc = window.MutationObserver || window.WebkitMutationObserver;\n    const observer = new ObserverFunc(mutations => {\n      // The observerUpdate event should only be triggered\n      // once despite the number of mutations.  Additional\n      // triggers are redundant and are very costly\n      if (swiper.__preventObserver__) return;\n      if (mutations.length === 1) {\n        emit('observerUpdate', mutations[0]);\n        return;\n      }\n      const observerUpdate = function observerUpdate() {\n        emit('observerUpdate', mutations[0]);\n      };\n      if (window.requestAnimationFrame) {\n        window.requestAnimationFrame(observerUpdate);\n      } else {\n        window.setTimeout(observerUpdate, 0);\n      }\n    });\n    observer.observe(target, {\n      attributes: typeof options.attributes === 'undefined' ? true : options.attributes,\n      childList: typeof options.childList === 'undefined' ? true : options.childList,\n      characterData: typeof options.characterData === 'undefined' ? true : options.characterData\n    });\n    observers.push(observer);\n  };\n  const init = () => {\n    if (!swiper.params.observer) return;\n    if (swiper.params.observeParents) {\n      const containerParents = elementParents(swiper.el);\n      for (let i = 0; i < containerParents.length; i += 1) {\n        attach(containerParents[i]);\n      }\n    }\n    // Observe container\n    attach(swiper.el, {\n      childList: swiper.params.observeSlideChildren\n    });\n\n    // Observe wrapper\n    attach(swiper.wrapperEl, {\n      attributes: false\n    });\n  };\n  const destroy = () => {\n    observers.forEach(observer => {\n      observer.disconnect();\n    });\n    observers.splice(0, observers.length);\n  };\n  extendParams({\n    observer: false,\n    observeParents: false,\n    observeSlideChildren: false\n  });\n  on('init', init);\n  on('destroy', destroy);\n}"], "mappings": "AAAA,SAASA,SAAS,QAAQ,YAAY;AACtC,SAASC,cAAc,QAAQ,0BAA0B;AACzD,eAAe,SAASC,QAAQA,CAAAC,IAAA,EAK7B;EAAA,IAL8B;IAC/BC,MAAM;IACNC,YAAY;IACZC,EAAE;IACFC;EACF,CAAC,GAAAJ,IAAA;EACC,MAAMK,SAAS,GAAG,EAAE;EACpB,MAAMC,MAAM,GAAGT,SAAS,EAAE;EAC1B,MAAMU,MAAM,GAAG,SAAAA,CAACC,MAAM,EAAmB;IAAA,IAAjBC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IAClC,MAAMG,YAAY,GAAGP,MAAM,CAACQ,gBAAgB,IAAIR,MAAM,CAACS,sBAAsB;IAC7E,MAAMC,QAAQ,GAAG,IAAIH,YAAY,CAACI,SAAS,IAAI;MAC7C;MACA;MACA;MACA,IAAIhB,MAAM,CAACiB,mBAAmB,EAAE;MAChC,IAAID,SAAS,CAACN,MAAM,KAAK,CAAC,EAAE;QAC1BP,IAAI,CAAC,gBAAgB,EAAEa,SAAS,CAAC,CAAC,CAAC,CAAC;QACpC;MACF;MACA,MAAME,cAAc,GAAG,SAASA,cAAcA,CAAA,EAAG;QAC/Cf,IAAI,CAAC,gBAAgB,EAAEa,SAAS,CAAC,CAAC,CAAC,CAAC;MACtC,CAAC;MACD,IAAIX,MAAM,CAACc,qBAAqB,EAAE;QAChCd,MAAM,CAACc,qBAAqB,CAACD,cAAc,CAAC;MAC9C,CAAC,MAAM;QACLb,MAAM,CAACe,UAAU,CAACF,cAAc,EAAE,CAAC,CAAC;MACtC;IACF,CAAC,CAAC;IACFH,QAAQ,CAACM,OAAO,CAACd,MAAM,EAAE;MACvBe,UAAU,EAAE,OAAOd,OAAO,CAACc,UAAU,KAAK,WAAW,GAAG,IAAI,GAAGd,OAAO,CAACc,UAAU;MACjFC,SAAS,EAAE,OAAOf,OAAO,CAACe,SAAS,KAAK,WAAW,GAAG,IAAI,GAAGf,OAAO,CAACe,SAAS;MAC9EC,aAAa,EAAE,OAAOhB,OAAO,CAACgB,aAAa,KAAK,WAAW,GAAG,IAAI,GAAGhB,OAAO,CAACgB;IAC/E,CAAC,CAAC;IACFpB,SAAS,CAACqB,IAAI,CAACV,QAAQ,CAAC;EAC1B,CAAC;EACD,MAAMW,IAAI,GAAGA,CAAA,KAAM;IACjB,IAAI,CAAC1B,MAAM,CAAC2B,MAAM,CAACZ,QAAQ,EAAE;IAC7B,IAAIf,MAAM,CAAC2B,MAAM,CAACC,cAAc,EAAE;MAChC,MAAMC,gBAAgB,GAAGhC,cAAc,CAACG,MAAM,CAAC8B,EAAE,CAAC;MAClD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,gBAAgB,CAACnB,MAAM,EAAEqB,CAAC,IAAI,CAAC,EAAE;QACnDzB,MAAM,CAACuB,gBAAgB,CAACE,CAAC,CAAC,CAAC;MAC7B;IACF;IACA;IACAzB,MAAM,CAACN,MAAM,CAAC8B,EAAE,EAAE;MAChBP,SAAS,EAAEvB,MAAM,CAAC2B,MAAM,CAACK;IAC3B,CAAC,CAAC;;IAEF;IACA1B,MAAM,CAACN,MAAM,CAACiC,SAAS,EAAE;MACvBX,UAAU,EAAE;IACd,CAAC,CAAC;EACJ,CAAC;EACD,MAAMY,OAAO,GAAGA,CAAA,KAAM;IACpB9B,SAAS,CAAC+B,OAAO,CAACpB,QAAQ,IAAI;MAC5BA,QAAQ,CAACqB,UAAU,EAAE;IACvB,CAAC,CAAC;IACFhC,SAAS,CAACiC,MAAM,CAAC,CAAC,EAAEjC,SAAS,CAACM,MAAM,CAAC;EACvC,CAAC;EACDT,YAAY,CAAC;IACXc,QAAQ,EAAE,KAAK;IACfa,cAAc,EAAE,KAAK;IACrBI,oBAAoB,EAAE;EACxB,CAAC,CAAC;EACF9B,EAAE,CAAC,MAAM,EAAEwB,IAAI,CAAC;EAChBxB,EAAE,CAAC,SAAS,EAAEgC,OAAO,CAAC;AACxB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}