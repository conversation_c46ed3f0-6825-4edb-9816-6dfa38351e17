{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _react = _interopRequireDefault(require(\"react\"));\nvar _classnames = _interopRequireDefault(require(\"classnames\"));\nvar _utils = require(\"../../utils\");\nfunction MouseTimeDisplay(_ref) {\n  var duration = _ref.duration,\n    mouseTime = _ref.mouseTime,\n    className = _ref.className,\n    text = _ref.text;\n  if (!mouseTime.time) {\n    return null;\n  }\n  var time = text || (0, _utils.formatTime)(mouseTime.time, duration);\n  return _react[\"default\"].createElement(\"div\", {\n    className: (0, _classnames[\"default\"])('video-react-mouse-display', className),\n    style: {\n      left: \"\".concat(mouseTime.position, \"px\")\n    },\n    \"data-current-time\": time\n  });\n}\nMouseTimeDisplay.propTypes = {\n  duration: _propTypes[\"default\"].number,\n  mouseTime: _propTypes[\"default\"].object,\n  className: _propTypes[\"default\"].string\n};\nMouseTimeDisplay.displayName = 'MouseTimeDisplay';\nvar _default = MouseTimeDisplay;\nexports[\"default\"] = _default;", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "_propTypes", "_react", "_classnames", "_utils", "MouseTimeDisplay", "_ref", "duration", "mouseTime", "className", "text", "time", "formatTime", "createElement", "style", "left", "concat", "position", "propTypes", "number", "object", "string", "displayName", "_default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/video-react/lib/components/control-bar/MouseTimeDisplay.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\n\nvar _react = _interopRequireDefault(require(\"react\"));\n\nvar _classnames = _interopRequireDefault(require(\"classnames\"));\n\nvar _utils = require(\"../../utils\");\n\nfunction MouseTimeDisplay(_ref) {\n  var duration = _ref.duration,\n      mouseTime = _ref.mouseTime,\n      className = _ref.className,\n      text = _ref.text;\n\n  if (!mouseTime.time) {\n    return null;\n  }\n\n  var time = text || (0, _utils.formatTime)(mouseTime.time, duration);\n  return _react[\"default\"].createElement(\"div\", {\n    className: (0, _classnames[\"default\"])('video-react-mouse-display', className),\n    style: {\n      left: \"\".concat(mouseTime.position, \"px\")\n    },\n    \"data-current-time\": time\n  });\n}\n\nMouseTimeDisplay.propTypes = {\n  duration: _propTypes[\"default\"].number,\n  mouseTime: _propTypes[\"default\"].object,\n  className: _propTypes[\"default\"].string\n};\nMouseTimeDisplay.displayName = 'MouseTimeDisplay';\nvar _default = MouseTimeDisplay;\nexports[\"default\"] = _default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC;AAEpFC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;AAE3B,IAAIE,UAAU,GAAGN,sBAAsB,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;AAE9D,IAAIM,MAAM,GAAGP,sBAAsB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AAErD,IAAIO,WAAW,GAAGR,sBAAsB,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;AAE/D,IAAIQ,MAAM,GAAGR,OAAO,CAAC,aAAa,CAAC;AAEnC,SAASS,gBAAgBA,CAACC,IAAI,EAAE;EAC9B,IAAIC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;IACxBC,SAAS,GAAGF,IAAI,CAACE,SAAS;IAC1BC,SAAS,GAAGH,IAAI,CAACG,SAAS;IAC1BC,IAAI,GAAGJ,IAAI,CAACI,IAAI;EAEpB,IAAI,CAACF,SAAS,CAACG,IAAI,EAAE;IACnB,OAAO,IAAI;EACb;EAEA,IAAIA,IAAI,GAAGD,IAAI,IAAI,CAAC,CAAC,EAAEN,MAAM,CAACQ,UAAU,EAAEJ,SAAS,CAACG,IAAI,EAAEJ,QAAQ,CAAC;EACnE,OAAOL,MAAM,CAAC,SAAS,CAAC,CAACW,aAAa,CAAC,KAAK,EAAE;IAC5CJ,SAAS,EAAE,CAAC,CAAC,EAAEN,WAAW,CAAC,SAAS,CAAC,EAAE,2BAA2B,EAAEM,SAAS,CAAC;IAC9EK,KAAK,EAAE;MACLC,IAAI,EAAE,EAAE,CAACC,MAAM,CAACR,SAAS,CAACS,QAAQ,EAAE,IAAI;IAC1C,CAAC;IACD,mBAAmB,EAAEN;EACvB,CAAC,CAAC;AACJ;AAEAN,gBAAgB,CAACa,SAAS,GAAG;EAC3BX,QAAQ,EAAEN,UAAU,CAAC,SAAS,CAAC,CAACkB,MAAM;EACtCX,SAAS,EAAEP,UAAU,CAAC,SAAS,CAAC,CAACmB,MAAM;EACvCX,SAAS,EAAER,UAAU,CAAC,SAAS,CAAC,CAACoB;AACnC,CAAC;AACDhB,gBAAgB,CAACiB,WAAW,GAAG,kBAAkB;AACjD,IAAIC,QAAQ,GAAGlB,gBAAgB;AAC/BN,OAAO,CAAC,SAAS,CAAC,GAAGwB,QAAQ"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}