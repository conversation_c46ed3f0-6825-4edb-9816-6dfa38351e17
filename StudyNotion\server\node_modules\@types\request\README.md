# Installation
> `npm install --save @types/request`

# Summary
This package contains type definitions for request (https://github.com/request/request).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/request.

### Additional Details
 * Last updated: Sat, 01 Jan 2022 14:01:38 GMT
 * Dependencies: [@types/caseless](https://npmjs.com/package/@types/caseless), [@types/form-data](https://npmjs.com/package/@types/form-data), [@types/tough-cookie](https://npmjs.com/package/@types/tough-cookie), [@types/node](https://npmjs.com/package/@types/node)
 * Global values: none

# Credits
These definitions were written by [<PERSON>](https://github.com/soywiz), [bonnici](https://github.com/bonnici), [<PERSON>](https://github.com/Bartvds), [<PERSON>](https://github.com/j<PERSON><PERSON>en), [<PERSON>](https://github.com/ccurrens), [<PERSON>](https://github.com/lookfirst), [<PERSON>. <PERSON>](https://github.com/mastermatt), [<PERSON> <PERSON>lla](https://github.com/josecolella), and [Marek Urbanowicz](https://github.com/murbanowicz).
