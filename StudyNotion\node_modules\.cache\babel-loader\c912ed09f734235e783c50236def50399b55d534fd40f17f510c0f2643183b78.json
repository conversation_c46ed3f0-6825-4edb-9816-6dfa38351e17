{"ast": null, "code": "/**\n * @typedef {import('micromark-util-types').Code} Code\n * @typedef {import('micromark-util-types').Effects} Effects\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').TokenType} TokenType\n */\n\nimport { factorySpace } from 'micromark-factory-space';\nimport { markdownLineEnding } from 'micromark-util-character';\nimport { codes } from 'micromark-util-symbol/codes.js';\nimport { constants } from 'micromark-util-symbol/constants.js';\nimport { types } from 'micromark-util-symbol/types.js';\n\n/**\n * Parse titles.\n *\n * ###### Examples\n *\n * ```markdown\n * \"a\"\n * 'b'\n * (c)\n * \"a\n * b\"\n * 'a\n *     b'\n * (a\\)b)\n * ```\n *\n * @param {Effects} effects\n *   Context.\n * @param {State} ok\n *   State switched to when successful.\n * @param {State} nok\n *   State switched to when unsuccessful.\n * @param {TokenType} type\n *   Type of the whole title (`\"a\"`, `'b'`, `(c)`).\n * @param {TokenType} markerType\n *   Type for the markers (`\"`, `'`, `(`, and `)`).\n * @param {TokenType} stringType\n *   Type for the value (`a`).\n * @returns {State}\n *   Start state.\n */\n// eslint-disable-next-line max-params\nexport function factoryTitle(effects, ok, nok, type, markerType, stringType) {\n  /** @type {NonNullable<Code>} */\n  let marker;\n  return start;\n\n  /**\n   * Start of title.\n   *\n   * ```markdown\n   * > | \"a\"\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    if (code === codes.quotationMark || code === codes.apostrophe || code === codes.leftParenthesis) {\n      effects.enter(type);\n      effects.enter(markerType);\n      effects.consume(code);\n      effects.exit(markerType);\n      marker = code === codes.leftParenthesis ? codes.rightParenthesis : code;\n      return begin;\n    }\n    return nok(code);\n  }\n\n  /**\n   * After opening marker.\n   *\n   * This is also used at the closing marker.\n   *\n   * ```markdown\n   * > | \"a\"\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function begin(code) {\n    if (code === marker) {\n      effects.enter(markerType);\n      effects.consume(code);\n      effects.exit(markerType);\n      effects.exit(type);\n      return ok;\n    }\n    effects.enter(stringType);\n    return atBreak(code);\n  }\n\n  /**\n   * At something, before something else.\n   *\n   * ```markdown\n   * > | \"a\"\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function atBreak(code) {\n    if (code === marker) {\n      effects.exit(stringType);\n      return begin(marker);\n    }\n    if (code === codes.eof) {\n      return nok(code);\n    }\n\n    // Note: blank lines can’t exist in content.\n    if (markdownLineEnding(code)) {\n      // To do: use `space_or_tab_eol_with_options`, connect.\n      effects.enter(types.lineEnding);\n      effects.consume(code);\n      effects.exit(types.lineEnding);\n      return factorySpace(effects, atBreak, types.linePrefix);\n    }\n    effects.enter(types.chunkString, {\n      contentType: constants.contentTypeString\n    });\n    return inside(code);\n  }\n\n  /**\n   *\n   *\n   * @type {State}\n   */\n  function inside(code) {\n    if (code === marker || code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.chunkString);\n      return atBreak(code);\n    }\n    effects.consume(code);\n    return code === codes.backslash ? escape : inside;\n  }\n\n  /**\n   * After `\\`, at a special character.\n   *\n   * ```markdown\n   * > | \"a\\*b\"\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function escape(code) {\n    if (code === marker || code === codes.backslash) {\n      effects.consume(code);\n      return inside;\n    }\n    return inside(code);\n  }\n}", "map": {"version": 3, "names": ["factorySpace", "markdownLineEnding", "codes", "constants", "types", "factoryTitle", "effects", "ok", "nok", "type", "markerType", "stringType", "marker", "start", "code", "quotationMark", "apostrophe", "leftParenthesis", "enter", "consume", "exit", "rightParenthesis", "begin", "atBreak", "eof", "lineEnding", "linePrefix", "chunkString", "contentType", "contentTypeString", "inside", "backslash", "escape"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/micromark-factory-title/dev/index.js"], "sourcesContent": ["/**\n * @typedef {import('micromark-util-types').Code} Code\n * @typedef {import('micromark-util-types').Effects} Effects\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').TokenType} TokenType\n */\n\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownLineEnding} from 'micromark-util-character'\nimport {codes} from 'micromark-util-symbol/codes.js'\nimport {constants} from 'micromark-util-symbol/constants.js'\nimport {types} from 'micromark-util-symbol/types.js'\n\n/**\n * Parse titles.\n *\n * ###### Examples\n *\n * ```markdown\n * \"a\"\n * 'b'\n * (c)\n * \"a\n * b\"\n * 'a\n *     b'\n * (a\\)b)\n * ```\n *\n * @param {Effects} effects\n *   Context.\n * @param {State} ok\n *   State switched to when successful.\n * @param {State} nok\n *   State switched to when unsuccessful.\n * @param {TokenType} type\n *   Type of the whole title (`\"a\"`, `'b'`, `(c)`).\n * @param {TokenType} markerType\n *   Type for the markers (`\"`, `'`, `(`, and `)`).\n * @param {TokenType} stringType\n *   Type for the value (`a`).\n * @returns {State}\n *   Start state.\n */\n// eslint-disable-next-line max-params\nexport function factoryTitle(effects, ok, nok, type, markerType, stringType) {\n  /** @type {NonNullable<Code>} */\n  let marker\n\n  return start\n\n  /**\n   * Start of title.\n   *\n   * ```markdown\n   * > | \"a\"\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    if (\n      code === codes.quotationMark ||\n      code === codes.apostrophe ||\n      code === codes.leftParenthesis\n    ) {\n      effects.enter(type)\n      effects.enter(markerType)\n      effects.consume(code)\n      effects.exit(markerType)\n      marker = code === codes.leftParenthesis ? codes.rightParenthesis : code\n      return begin\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After opening marker.\n   *\n   * This is also used at the closing marker.\n   *\n   * ```markdown\n   * > | \"a\"\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function begin(code) {\n    if (code === marker) {\n      effects.enter(markerType)\n      effects.consume(code)\n      effects.exit(markerType)\n      effects.exit(type)\n      return ok\n    }\n\n    effects.enter(stringType)\n    return atBreak(code)\n  }\n\n  /**\n   * At something, before something else.\n   *\n   * ```markdown\n   * > | \"a\"\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function atBreak(code) {\n    if (code === marker) {\n      effects.exit(stringType)\n      return begin(marker)\n    }\n\n    if (code === codes.eof) {\n      return nok(code)\n    }\n\n    // Note: blank lines can’t exist in content.\n    if (markdownLineEnding(code)) {\n      // To do: use `space_or_tab_eol_with_options`, connect.\n      effects.enter(types.lineEnding)\n      effects.consume(code)\n      effects.exit(types.lineEnding)\n      return factorySpace(effects, atBreak, types.linePrefix)\n    }\n\n    effects.enter(types.chunkString, {contentType: constants.contentTypeString})\n    return inside(code)\n  }\n\n  /**\n   *\n   *\n   * @type {State}\n   */\n  function inside(code) {\n    if (code === marker || code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.chunkString)\n      return atBreak(code)\n    }\n\n    effects.consume(code)\n    return code === codes.backslash ? escape : inside\n  }\n\n  /**\n   * After `\\`, at a special character.\n   *\n   * ```markdown\n   * > | \"a\\*b\"\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function escape(code) {\n    if (code === marker || code === codes.backslash) {\n      effects.consume(code)\n      return inside\n    }\n\n    return inside(code)\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,YAAY,QAAO,yBAAyB;AACpD,SAAQC,kBAAkB,QAAO,0BAA0B;AAC3D,SAAQC,KAAK,QAAO,gCAAgC;AACpD,SAAQC,SAAS,QAAO,oCAAoC;AAC5D,SAAQC,KAAK,QAAO,gCAAgC;;AAEpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,YAAYA,CAACC,OAAO,EAAEC,EAAE,EAAEC,GAAG,EAAEC,IAAI,EAAEC,UAAU,EAAEC,UAAU,EAAE;EAC3E;EACA,IAAIC,MAAM;EAEV,OAAOC,KAAK;;EAEZ;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,KAAKA,CAACC,IAAI,EAAE;IACnB,IACEA,IAAI,KAAKZ,KAAK,CAACa,aAAa,IAC5BD,IAAI,KAAKZ,KAAK,CAACc,UAAU,IACzBF,IAAI,KAAKZ,KAAK,CAACe,eAAe,EAC9B;MACAX,OAAO,CAACY,KAAK,CAACT,IAAI,CAAC;MACnBH,OAAO,CAACY,KAAK,CAACR,UAAU,CAAC;MACzBJ,OAAO,CAACa,OAAO,CAACL,IAAI,CAAC;MACrBR,OAAO,CAACc,IAAI,CAACV,UAAU,CAAC;MACxBE,MAAM,GAAGE,IAAI,KAAKZ,KAAK,CAACe,eAAe,GAAGf,KAAK,CAACmB,gBAAgB,GAAGP,IAAI;MACvE,OAAOQ,KAAK;IACd;IAEA,OAAOd,GAAG,CAACM,IAAI,CAAC;EAClB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASQ,KAAKA,CAACR,IAAI,EAAE;IACnB,IAAIA,IAAI,KAAKF,MAAM,EAAE;MACnBN,OAAO,CAACY,KAAK,CAACR,UAAU,CAAC;MACzBJ,OAAO,CAACa,OAAO,CAACL,IAAI,CAAC;MACrBR,OAAO,CAACc,IAAI,CAACV,UAAU,CAAC;MACxBJ,OAAO,CAACc,IAAI,CAACX,IAAI,CAAC;MAClB,OAAOF,EAAE;IACX;IAEAD,OAAO,CAACY,KAAK,CAACP,UAAU,CAAC;IACzB,OAAOY,OAAO,CAACT,IAAI,CAAC;EACtB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASS,OAAOA,CAACT,IAAI,EAAE;IACrB,IAAIA,IAAI,KAAKF,MAAM,EAAE;MACnBN,OAAO,CAACc,IAAI,CAACT,UAAU,CAAC;MACxB,OAAOW,KAAK,CAACV,MAAM,CAAC;IACtB;IAEA,IAAIE,IAAI,KAAKZ,KAAK,CAACsB,GAAG,EAAE;MACtB,OAAOhB,GAAG,CAACM,IAAI,CAAC;IAClB;;IAEA;IACA,IAAIb,kBAAkB,CAACa,IAAI,CAAC,EAAE;MAC5B;MACAR,OAAO,CAACY,KAAK,CAACd,KAAK,CAACqB,UAAU,CAAC;MAC/BnB,OAAO,CAACa,OAAO,CAACL,IAAI,CAAC;MACrBR,OAAO,CAACc,IAAI,CAAChB,KAAK,CAACqB,UAAU,CAAC;MAC9B,OAAOzB,YAAY,CAACM,OAAO,EAAEiB,OAAO,EAAEnB,KAAK,CAACsB,UAAU,CAAC;IACzD;IAEApB,OAAO,CAACY,KAAK,CAACd,KAAK,CAACuB,WAAW,EAAE;MAACC,WAAW,EAAEzB,SAAS,CAAC0B;IAAiB,CAAC,CAAC;IAC5E,OAAOC,MAAM,CAAChB,IAAI,CAAC;EACrB;;EAEA;AACF;AACA;AACA;AACA;EACE,SAASgB,MAAMA,CAAChB,IAAI,EAAE;IACpB,IAAIA,IAAI,KAAKF,MAAM,IAAIE,IAAI,KAAKZ,KAAK,CAACsB,GAAG,IAAIvB,kBAAkB,CAACa,IAAI,CAAC,EAAE;MACrER,OAAO,CAACc,IAAI,CAAChB,KAAK,CAACuB,WAAW,CAAC;MAC/B,OAAOJ,OAAO,CAACT,IAAI,CAAC;IACtB;IAEAR,OAAO,CAACa,OAAO,CAACL,IAAI,CAAC;IACrB,OAAOA,IAAI,KAAKZ,KAAK,CAAC6B,SAAS,GAAGC,MAAM,GAAGF,MAAM;EACnD;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASE,MAAMA,CAAClB,IAAI,EAAE;IACpB,IAAIA,IAAI,KAAKF,MAAM,IAAIE,IAAI,KAAKZ,KAAK,CAAC6B,SAAS,EAAE;MAC/CzB,OAAO,CAACa,OAAO,CAACL,IAAI,CAAC;MACrB,OAAOgB,MAAM;IACf;IAEA,OAAOA,MAAM,CAAChB,IAAI,CAAC;EACrB;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}