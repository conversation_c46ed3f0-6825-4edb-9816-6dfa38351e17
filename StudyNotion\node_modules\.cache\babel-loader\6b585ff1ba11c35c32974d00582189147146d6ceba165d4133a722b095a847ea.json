{"ast": null, "code": "import setGrabCursor from './setGrabCursor.js';\nimport unsetGrabCursor from './unsetGrabCursor.js';\nexport default {\n  setGrabCursor,\n  unsetGrabCursor\n};", "map": {"version": 3, "names": ["setGrabCursor", "unsetGrabCursor"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/core/grab-cursor/index.js"], "sourcesContent": ["import setGrabCursor from './setGrabCursor.js';\nimport unsetGrabCursor from './unsetGrabCursor.js';\nexport default {\n  setGrabCursor,\n  unsetGrabCursor\n};"], "mappings": "AAAA,OAAOA,aAAa,MAAM,oBAAoB;AAC9C,OAAOC,eAAe,MAAM,sBAAsB;AAClD,eAAe;EACbD,aAAa;EACbC;AACF,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}