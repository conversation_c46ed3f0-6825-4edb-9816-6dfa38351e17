{"ast": null, "code": "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').Content} Content\n * @typedef {import('mdast').ListItem} ListItem\n * @typedef {import('mdast').Parent} Parent\n * @typedef {import('mdast').Root} Root\n * @typedef {import('../state.js').State} State\n */\n\n/**\n * @typedef {Root | Content} Nodes\n * @typedef {Extract<Nodes, Parent>} Parents\n */\n\n/**\n * Turn an mdast `listItem` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {ListItem} node\n *   mdast node.\n * @param {Parents | null | undefined} parent\n *   Parent of `node`.\n * @returns {Element}\n *   hast node.\n */\nexport function listItem(state, node, parent) {\n  const results = state.all(node);\n  const loose = parent ? listLoose(parent) : listItem<PERSON>oose(node);\n  /** @type {Properties} */\n  const properties = {};\n  /** @type {Array<ElementContent>} */\n  const children = [];\n  if (typeof node.checked === 'boolean') {\n    const head = results[0];\n    /** @type {Element} */\n    let paragraph;\n    if (head && head.type === 'element' && head.tagName === 'p') {\n      paragraph = head;\n    } else {\n      paragraph = {\n        type: 'element',\n        tagName: 'p',\n        properties: {},\n        children: []\n      };\n      results.unshift(paragraph);\n    }\n    if (paragraph.children.length > 0) {\n      paragraph.children.unshift({\n        type: 'text',\n        value: ' '\n      });\n    }\n    paragraph.children.unshift({\n      type: 'element',\n      tagName: 'input',\n      properties: {\n        type: 'checkbox',\n        checked: node.checked,\n        disabled: true\n      },\n      children: []\n    });\n\n    // According to github-markdown-css, this class hides bullet.\n    // See: <https://github.com/sindresorhus/github-markdown-css>.\n    properties.className = ['task-list-item'];\n  }\n  let index = -1;\n  while (++index < results.length) {\n    const child = results[index];\n\n    // Add eols before nodes, except if this is a loose, first paragraph.\n    if (loose || index !== 0 || child.type !== 'element' || child.tagName !== 'p') {\n      children.push({\n        type: 'text',\n        value: '\\n'\n      });\n    }\n    if (child.type === 'element' && child.tagName === 'p' && !loose) {\n      children.push(...child.children);\n    } else {\n      children.push(child);\n    }\n  }\n  const tail = results[results.length - 1];\n\n  // Add a final eol.\n  if (tail && (loose || tail.type !== 'element' || tail.tagName !== 'p')) {\n    children.push({\n      type: 'text',\n      value: '\\n'\n    });\n  }\n\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'li',\n    properties,\n    children\n  };\n  state.patch(node, result);\n  return state.applyData(node, result);\n}\n\n/**\n * @param {Parents} node\n * @return {Boolean}\n */\nfunction listLoose(node) {\n  let loose = false;\n  if (node.type === 'list') {\n    loose = node.spread || false;\n    const children = node.children;\n    let index = -1;\n    while (!loose && ++index < children.length) {\n      loose = listItemLoose(children[index]);\n    }\n  }\n  return loose;\n}\n\n/**\n * @param {ListItem} node\n * @return {Boolean}\n */\nfunction listItemLoose(node) {\n  const spread = node.spread;\n  return spread === undefined || spread === null ? node.children.length > 1 : spread;\n}", "map": {"version": 3, "names": ["listItem", "state", "node", "parent", "results", "all", "loose", "listLoose", "listItemLoose", "properties", "children", "checked", "head", "paragraph", "type", "tagName", "unshift", "length", "value", "disabled", "className", "index", "child", "push", "tail", "result", "patch", "applyData", "spread", "undefined"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/mdast-util-to-hast/lib/handlers/list-item.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').Content} Content\n * @typedef {import('mdast').ListItem} ListItem\n * @typedef {import('mdast').Parent} Parent\n * @typedef {import('mdast').Root} Root\n * @typedef {import('../state.js').State} State\n */\n\n/**\n * @typedef {Root | Content} Nodes\n * @typedef {Extract<Nodes, Parent>} Parents\n */\n\n/**\n * Turn an mdast `listItem` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {ListItem} node\n *   mdast node.\n * @param {Parents | null | undefined} parent\n *   Parent of `node`.\n * @returns {Element}\n *   hast node.\n */\nexport function listItem(state, node, parent) {\n  const results = state.all(node)\n  const loose = parent ? listLoose(parent) : listItem<PERSON>oose(node)\n  /** @type {Properties} */\n  const properties = {}\n  /** @type {Array<ElementContent>} */\n  const children = []\n\n  if (typeof node.checked === 'boolean') {\n    const head = results[0]\n    /** @type {Element} */\n    let paragraph\n\n    if (head && head.type === 'element' && head.tagName === 'p') {\n      paragraph = head\n    } else {\n      paragraph = {type: 'element', tagName: 'p', properties: {}, children: []}\n      results.unshift(paragraph)\n    }\n\n    if (paragraph.children.length > 0) {\n      paragraph.children.unshift({type: 'text', value: ' '})\n    }\n\n    paragraph.children.unshift({\n      type: 'element',\n      tagName: 'input',\n      properties: {type: 'checkbox', checked: node.checked, disabled: true},\n      children: []\n    })\n\n    // According to github-markdown-css, this class hides bullet.\n    // See: <https://github.com/sindresorhus/github-markdown-css>.\n    properties.className = ['task-list-item']\n  }\n\n  let index = -1\n\n  while (++index < results.length) {\n    const child = results[index]\n\n    // Add eols before nodes, except if this is a loose, first paragraph.\n    if (\n      loose ||\n      index !== 0 ||\n      child.type !== 'element' ||\n      child.tagName !== 'p'\n    ) {\n      children.push({type: 'text', value: '\\n'})\n    }\n\n    if (child.type === 'element' && child.tagName === 'p' && !loose) {\n      children.push(...child.children)\n    } else {\n      children.push(child)\n    }\n  }\n\n  const tail = results[results.length - 1]\n\n  // Add a final eol.\n  if (tail && (loose || tail.type !== 'element' || tail.tagName !== 'p')) {\n    children.push({type: 'text', value: '\\n'})\n  }\n\n  /** @type {Element} */\n  const result = {type: 'element', tagName: 'li', properties, children}\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n\n/**\n * @param {Parents} node\n * @return {Boolean}\n */\nfunction listLoose(node) {\n  let loose = false\n  if (node.type === 'list') {\n    loose = node.spread || false\n    const children = node.children\n    let index = -1\n\n    while (!loose && ++index < children.length) {\n      loose = listItemLoose(children[index])\n    }\n  }\n\n  return loose\n}\n\n/**\n * @param {ListItem} node\n * @return {Boolean}\n */\nfunction listItemLoose(node) {\n  const spread = node.spread\n\n  return spread === undefined || spread === null\n    ? node.children.length > 1\n    : spread\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,QAAQA,CAACC,KAAK,EAAEC,IAAI,EAAEC,MAAM,EAAE;EAC5C,MAAMC,OAAO,GAAGH,KAAK,CAACI,GAAG,CAACH,IAAI,CAAC;EAC/B,MAAMI,KAAK,GAAGH,MAAM,GAAGI,SAAS,CAACJ,MAAM,CAAC,GAAGK,aAAa,CAACN,IAAI,CAAC;EAC9D;EACA,MAAMO,UAAU,GAAG,CAAC,CAAC;EACrB;EACA,MAAMC,QAAQ,GAAG,EAAE;EAEnB,IAAI,OAAOR,IAAI,CAACS,OAAO,KAAK,SAAS,EAAE;IACrC,MAAMC,IAAI,GAAGR,OAAO,CAAC,CAAC,CAAC;IACvB;IACA,IAAIS,SAAS;IAEb,IAAID,IAAI,IAAIA,IAAI,CAACE,IAAI,KAAK,SAAS,IAAIF,IAAI,CAACG,OAAO,KAAK,GAAG,EAAE;MAC3DF,SAAS,GAAGD,IAAI;IAClB,CAAC,MAAM;MACLC,SAAS,GAAG;QAACC,IAAI,EAAE,SAAS;QAAEC,OAAO,EAAE,GAAG;QAAEN,UAAU,EAAE,CAAC,CAAC;QAAEC,QAAQ,EAAE;MAAE,CAAC;MACzEN,OAAO,CAACY,OAAO,CAACH,SAAS,CAAC;IAC5B;IAEA,IAAIA,SAAS,CAACH,QAAQ,CAACO,MAAM,GAAG,CAAC,EAAE;MACjCJ,SAAS,CAACH,QAAQ,CAACM,OAAO,CAAC;QAACF,IAAI,EAAE,MAAM;QAAEI,KAAK,EAAE;MAAG,CAAC,CAAC;IACxD;IAEAL,SAAS,CAACH,QAAQ,CAACM,OAAO,CAAC;MACzBF,IAAI,EAAE,SAAS;MACfC,OAAO,EAAE,OAAO;MAChBN,UAAU,EAAE;QAACK,IAAI,EAAE,UAAU;QAAEH,OAAO,EAAET,IAAI,CAACS,OAAO;QAAEQ,QAAQ,EAAE;MAAI,CAAC;MACrET,QAAQ,EAAE;IACZ,CAAC,CAAC;;IAEF;IACA;IACAD,UAAU,CAACW,SAAS,GAAG,CAAC,gBAAgB,CAAC;EAC3C;EAEA,IAAIC,KAAK,GAAG,CAAC,CAAC;EAEd,OAAO,EAAEA,KAAK,GAAGjB,OAAO,CAACa,MAAM,EAAE;IAC/B,MAAMK,KAAK,GAAGlB,OAAO,CAACiB,KAAK,CAAC;;IAE5B;IACA,IACEf,KAAK,IACLe,KAAK,KAAK,CAAC,IACXC,KAAK,CAACR,IAAI,KAAK,SAAS,IACxBQ,KAAK,CAACP,OAAO,KAAK,GAAG,EACrB;MACAL,QAAQ,CAACa,IAAI,CAAC;QAACT,IAAI,EAAE,MAAM;QAAEI,KAAK,EAAE;MAAI,CAAC,CAAC;IAC5C;IAEA,IAAII,KAAK,CAACR,IAAI,KAAK,SAAS,IAAIQ,KAAK,CAACP,OAAO,KAAK,GAAG,IAAI,CAACT,KAAK,EAAE;MAC/DI,QAAQ,CAACa,IAAI,CAAC,GAAGD,KAAK,CAACZ,QAAQ,CAAC;IAClC,CAAC,MAAM;MACLA,QAAQ,CAACa,IAAI,CAACD,KAAK,CAAC;IACtB;EACF;EAEA,MAAME,IAAI,GAAGpB,OAAO,CAACA,OAAO,CAACa,MAAM,GAAG,CAAC,CAAC;;EAExC;EACA,IAAIO,IAAI,KAAKlB,KAAK,IAAIkB,IAAI,CAACV,IAAI,KAAK,SAAS,IAAIU,IAAI,CAACT,OAAO,KAAK,GAAG,CAAC,EAAE;IACtEL,QAAQ,CAACa,IAAI,CAAC;MAACT,IAAI,EAAE,MAAM;MAAEI,KAAK,EAAE;IAAI,CAAC,CAAC;EAC5C;;EAEA;EACA,MAAMO,MAAM,GAAG;IAACX,IAAI,EAAE,SAAS;IAAEC,OAAO,EAAE,IAAI;IAAEN,UAAU;IAAEC;EAAQ,CAAC;EACrET,KAAK,CAACyB,KAAK,CAACxB,IAAI,EAAEuB,MAAM,CAAC;EACzB,OAAOxB,KAAK,CAAC0B,SAAS,CAACzB,IAAI,EAAEuB,MAAM,CAAC;AACtC;;AAEA;AACA;AACA;AACA;AACA,SAASlB,SAASA,CAACL,IAAI,EAAE;EACvB,IAAII,KAAK,GAAG,KAAK;EACjB,IAAIJ,IAAI,CAACY,IAAI,KAAK,MAAM,EAAE;IACxBR,KAAK,GAAGJ,IAAI,CAAC0B,MAAM,IAAI,KAAK;IAC5B,MAAMlB,QAAQ,GAAGR,IAAI,CAACQ,QAAQ;IAC9B,IAAIW,KAAK,GAAG,CAAC,CAAC;IAEd,OAAO,CAACf,KAAK,IAAI,EAAEe,KAAK,GAAGX,QAAQ,CAACO,MAAM,EAAE;MAC1CX,KAAK,GAAGE,aAAa,CAACE,QAAQ,CAACW,KAAK,CAAC,CAAC;IACxC;EACF;EAEA,OAAOf,KAAK;AACd;;AAEA;AACA;AACA;AACA;AACA,SAASE,aAAaA,CAACN,IAAI,EAAE;EAC3B,MAAM0B,MAAM,GAAG1B,IAAI,CAAC0B,MAAM;EAE1B,OAAOA,MAAM,KAAKC,SAAS,IAAID,MAAM,KAAK,IAAI,GAC1C1B,IAAI,CAACQ,QAAQ,CAACO,MAAM,GAAG,CAAC,GACxBW,MAAM;AACZ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}