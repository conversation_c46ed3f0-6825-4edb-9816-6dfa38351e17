{"ast": null, "code": "export default function setTranslate(translate, byController) {\n  const swiper = this;\n  const {\n    rtlTranslate: rtl,\n    params,\n    wrapperEl,\n    progress\n  } = swiper;\n  let x = 0;\n  let y = 0;\n  const z = 0;\n  if (swiper.isHorizontal()) {\n    x = rtl ? -translate : translate;\n  } else {\n    y = translate;\n  }\n  if (params.roundLengths) {\n    x = Math.floor(x);\n    y = Math.floor(y);\n  }\n  swiper.previousTranslate = swiper.translate;\n  swiper.translate = swiper.isHorizontal() ? x : y;\n  if (params.cssMode) {\n    wrapperEl[swiper.isHorizontal() ? 'scrollLeft' : 'scrollTop'] = swiper.isHorizontal() ? -x : -y;\n  } else if (!params.virtualTranslate) {\n    if (swiper.isHorizontal()) {\n      x -= swiper.cssOverflowAdjustment();\n    } else {\n      y -= swiper.cssOverflowAdjustment();\n    }\n    wrapperEl.style.transform = `translate3d(${x}px, ${y}px, ${z}px)`;\n  }\n\n  // Check if we need to update progress\n  let newProgress;\n  const translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n  if (translatesDiff === 0) {\n    newProgress = 0;\n  } else {\n    newProgress = (translate - swiper.minTranslate()) / translatesDiff;\n  }\n  if (newProgress !== progress) {\n    swiper.updateProgress(translate);\n  }\n  swiper.emit('setTranslate', swiper.translate, byController);\n}", "map": {"version": 3, "names": ["setTranslate", "translate", "byController", "swiper", "rtlTranslate", "rtl", "params", "wrapperEl", "progress", "x", "y", "z", "isHorizontal", "roundLengths", "Math", "floor", "previousTranslate", "cssMode", "virtualTranslate", "cssOverflowAdjustment", "style", "transform", "newProgress", "translatesDiff", "maxTranslate", "minTranslate", "updateProgress", "emit"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/core/translate/setTranslate.js"], "sourcesContent": ["export default function setTranslate(translate, byController) {\n  const swiper = this;\n  const {\n    rtlTranslate: rtl,\n    params,\n    wrapperEl,\n    progress\n  } = swiper;\n  let x = 0;\n  let y = 0;\n  const z = 0;\n  if (swiper.isHorizontal()) {\n    x = rtl ? -translate : translate;\n  } else {\n    y = translate;\n  }\n  if (params.roundLengths) {\n    x = Math.floor(x);\n    y = Math.floor(y);\n  }\n  swiper.previousTranslate = swiper.translate;\n  swiper.translate = swiper.isHorizontal() ? x : y;\n  if (params.cssMode) {\n    wrapperEl[swiper.isHorizontal() ? 'scrollLeft' : 'scrollTop'] = swiper.isHorizontal() ? -x : -y;\n  } else if (!params.virtualTranslate) {\n    if (swiper.isHorizontal()) {\n      x -= swiper.cssOverflowAdjustment();\n    } else {\n      y -= swiper.cssOverflowAdjustment();\n    }\n    wrapperEl.style.transform = `translate3d(${x}px, ${y}px, ${z}px)`;\n  }\n\n  // Check if we need to update progress\n  let newProgress;\n  const translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n  if (translatesDiff === 0) {\n    newProgress = 0;\n  } else {\n    newProgress = (translate - swiper.minTranslate()) / translatesDiff;\n  }\n  if (newProgress !== progress) {\n    swiper.updateProgress(translate);\n  }\n  swiper.emit('setTranslate', swiper.translate, byController);\n}"], "mappings": "AAAA,eAAe,SAASA,YAAYA,CAACC,SAAS,EAAEC,YAAY,EAAE;EAC5D,MAAMC,MAAM,GAAG,IAAI;EACnB,MAAM;IACJC,YAAY,EAAEC,GAAG;IACjBC,MAAM;IACNC,SAAS;IACTC;EACF,CAAC,GAAGL,MAAM;EACV,IAAIM,CAAC,GAAG,CAAC;EACT,IAAIC,CAAC,GAAG,CAAC;EACT,MAAMC,CAAC,GAAG,CAAC;EACX,IAAIR,MAAM,CAACS,YAAY,EAAE,EAAE;IACzBH,CAAC,GAAGJ,GAAG,GAAG,CAACJ,SAAS,GAAGA,SAAS;EAClC,CAAC,MAAM;IACLS,CAAC,GAAGT,SAAS;EACf;EACA,IAAIK,MAAM,CAACO,YAAY,EAAE;IACvBJ,CAAC,GAAGK,IAAI,CAACC,KAAK,CAACN,CAAC,CAAC;IACjBC,CAAC,GAAGI,IAAI,CAACC,KAAK,CAACL,CAAC,CAAC;EACnB;EACAP,MAAM,CAACa,iBAAiB,GAAGb,MAAM,CAACF,SAAS;EAC3CE,MAAM,CAACF,SAAS,GAAGE,MAAM,CAACS,YAAY,EAAE,GAAGH,CAAC,GAAGC,CAAC;EAChD,IAAIJ,MAAM,CAACW,OAAO,EAAE;IAClBV,SAAS,CAACJ,MAAM,CAACS,YAAY,EAAE,GAAG,YAAY,GAAG,WAAW,CAAC,GAAGT,MAAM,CAACS,YAAY,EAAE,GAAG,CAACH,CAAC,GAAG,CAACC,CAAC;EACjG,CAAC,MAAM,IAAI,CAACJ,MAAM,CAACY,gBAAgB,EAAE;IACnC,IAAIf,MAAM,CAACS,YAAY,EAAE,EAAE;MACzBH,CAAC,IAAIN,MAAM,CAACgB,qBAAqB,EAAE;IACrC,CAAC,MAAM;MACLT,CAAC,IAAIP,MAAM,CAACgB,qBAAqB,EAAE;IACrC;IACAZ,SAAS,CAACa,KAAK,CAACC,SAAS,GAAI,eAAcZ,CAAE,OAAMC,CAAE,OAAMC,CAAE,KAAI;EACnE;;EAEA;EACA,IAAIW,WAAW;EACf,MAAMC,cAAc,GAAGpB,MAAM,CAACqB,YAAY,EAAE,GAAGrB,MAAM,CAACsB,YAAY,EAAE;EACpE,IAAIF,cAAc,KAAK,CAAC,EAAE;IACxBD,WAAW,GAAG,CAAC;EACjB,CAAC,MAAM;IACLA,WAAW,GAAG,CAACrB,SAAS,GAAGE,MAAM,CAACsB,YAAY,EAAE,IAAIF,cAAc;EACpE;EACA,IAAID,WAAW,KAAKd,QAAQ,EAAE;IAC5BL,MAAM,CAACuB,cAAc,CAACzB,SAAS,CAAC;EAClC;EACAE,MAAM,CAACwB,IAAI,CAAC,cAAc,EAAExB,MAAM,CAACF,SAAS,EAAEC,YAAY,CAAC;AAC7D"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}