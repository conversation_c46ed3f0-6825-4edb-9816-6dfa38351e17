{"ast": null, "code": "import { elementChildren } from '../../shared/utils.js';\nexport default function loopCreate(slideRealIndex) {\n  const swiper = this;\n  const {\n    params,\n    slidesEl\n  } = swiper;\n  if (!params.loop || swiper.virtual && swiper.params.virtual.enabled) return;\n  const slides = elementChildren(slidesEl, `.${params.slideClass}, swiper-slide`);\n  slides.forEach((el, index) => {\n    el.setAttribute('data-swiper-slide-index', index);\n  });\n  swiper.loopFix({\n    slideRealIndex,\n    direction: params.centeredSlides ? undefined : 'next'\n  });\n}", "map": {"version": 3, "names": ["elementChildren", "loopCreate", "slideRealIndex", "swiper", "params", "slidesEl", "loop", "virtual", "enabled", "slides", "slideClass", "for<PERSON>ach", "el", "index", "setAttribute", "loopFix", "direction", "centeredSlides", "undefined"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/core/loop/loopCreate.js"], "sourcesContent": ["import { elementChildren } from '../../shared/utils.js';\nexport default function loopCreate(slideRealIndex) {\n  const swiper = this;\n  const {\n    params,\n    slidesEl\n  } = swiper;\n  if (!params.loop || swiper.virtual && swiper.params.virtual.enabled) return;\n  const slides = elementChildren(slidesEl, `.${params.slideClass}, swiper-slide`);\n  slides.forEach((el, index) => {\n    el.setAttribute('data-swiper-slide-index', index);\n  });\n  swiper.loopFix({\n    slideRealIndex,\n    direction: params.centeredSlides ? undefined : 'next'\n  });\n}"], "mappings": "AAAA,SAASA,eAAe,QAAQ,uBAAuB;AACvD,eAAe,SAASC,UAAUA,CAACC,cAAc,EAAE;EACjD,MAAMC,MAAM,GAAG,IAAI;EACnB,MAAM;IACJC,MAAM;IACNC;EACF,CAAC,GAAGF,MAAM;EACV,IAAI,CAACC,MAAM,CAACE,IAAI,IAAIH,MAAM,CAACI,OAAO,IAAIJ,MAAM,CAACC,MAAM,CAACG,OAAO,CAACC,OAAO,EAAE;EACrE,MAAMC,MAAM,GAAGT,eAAe,CAACK,QAAQ,EAAG,IAAGD,MAAM,CAACM,UAAW,gBAAe,CAAC;EAC/ED,MAAM,CAACE,OAAO,CAAC,CAACC,EAAE,EAAEC,KAAK,KAAK;IAC5BD,EAAE,CAACE,YAAY,CAAC,yBAAyB,EAAED,KAAK,CAAC;EACnD,CAAC,CAAC;EACFV,MAAM,CAACY,OAAO,CAAC;IACbb,cAAc;IACdc,SAAS,EAAEZ,MAAM,CAACa,cAAc,GAAGC,SAAS,GAAG;EACjD,CAAC,CAAC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}