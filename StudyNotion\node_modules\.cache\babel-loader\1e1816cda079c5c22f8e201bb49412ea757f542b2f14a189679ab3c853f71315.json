{"ast": null, "code": "/* eslint no-underscore-dangle: \"off\" */\n/* eslint no-use-before-define: \"off\" */\nimport { getDocument } from 'ssr-window';\nexport default function Autoplay(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on,\n    emit,\n    params\n  } = _ref;\n  swiper.autoplay = {\n    running: false,\n    paused: false,\n    timeLeft: 0\n  };\n  extendParams({\n    autoplay: {\n      enabled: false,\n      delay: 3000,\n      waitForTransition: true,\n      disableOnInteraction: true,\n      stopOnLastSlide: false,\n      reverseDirection: false,\n      pauseOnMouseEnter: false\n    }\n  });\n  let timeout;\n  let raf;\n  let autoplayDelayTotal = params && params.autoplay ? params.autoplay.delay : 3000;\n  let autoplayDelayCurrent = params && params.autoplay ? params.autoplay.delay : 3000;\n  let autoplayTimeLeft;\n  let autoplayStartTime = new Date().getTime;\n  let wasPaused;\n  let isTouched;\n  let pausedByTouch;\n  let touchStartTimeout;\n  let slideChanged;\n  let pausedByInteraction;\n  function onTransitionEnd(e) {\n    if (!swiper || swiper.destroyed || !swiper.wrapperEl) return;\n    if (e.target !== swiper.wrapperEl) return;\n    swiper.wrapperEl.removeEventListener('transitionend', onTransitionEnd);\n    resume();\n  }\n  const calcTimeLeft = () => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    if (swiper.autoplay.paused) {\n      wasPaused = true;\n    } else if (wasPaused) {\n      autoplayDelayCurrent = autoplayTimeLeft;\n      wasPaused = false;\n    }\n    const timeLeft = swiper.autoplay.paused ? autoplayTimeLeft : autoplayStartTime + autoplayDelayCurrent - new Date().getTime();\n    swiper.autoplay.timeLeft = timeLeft;\n    emit('autoplayTimeLeft', timeLeft, timeLeft / autoplayDelayTotal);\n    raf = requestAnimationFrame(() => {\n      calcTimeLeft();\n    });\n  };\n  const getSlideDelay = () => {\n    let activeSlideEl;\n    if (swiper.virtual && swiper.params.virtual.enabled) {\n      activeSlideEl = swiper.slides.filter(slideEl => slideEl.classList.contains('swiper-slide-active'))[0];\n    } else {\n      activeSlideEl = swiper.slides[swiper.activeIndex];\n    }\n    if (!activeSlideEl) return undefined;\n    const currentSlideDelay = parseInt(activeSlideEl.getAttribute('data-swiper-autoplay'), 10);\n    return currentSlideDelay;\n  };\n  const run = delayForce => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    cancelAnimationFrame(raf);\n    calcTimeLeft();\n    let delay = typeof delayForce === 'undefined' ? swiper.params.autoplay.delay : delayForce;\n    autoplayDelayTotal = swiper.params.autoplay.delay;\n    autoplayDelayCurrent = swiper.params.autoplay.delay;\n    const currentSlideDelay = getSlideDelay();\n    if (!Number.isNaN(currentSlideDelay) && currentSlideDelay > 0 && typeof delayForce === 'undefined') {\n      delay = currentSlideDelay;\n      autoplayDelayTotal = currentSlideDelay;\n      autoplayDelayCurrent = currentSlideDelay;\n    }\n    autoplayTimeLeft = delay;\n    const speed = swiper.params.speed;\n    const proceed = () => {\n      if (!swiper || swiper.destroyed) return;\n      if (swiper.params.autoplay.reverseDirection) {\n        if (!swiper.isBeginning || swiper.params.loop || swiper.params.rewind) {\n          swiper.slidePrev(speed, true, true);\n          emit('autoplay');\n        } else if (!swiper.params.autoplay.stopOnLastSlide) {\n          swiper.slideTo(swiper.slides.length - 1, speed, true, true);\n          emit('autoplay');\n        }\n      } else {\n        if (!swiper.isEnd || swiper.params.loop || swiper.params.rewind) {\n          swiper.slideNext(speed, true, true);\n          emit('autoplay');\n        } else if (!swiper.params.autoplay.stopOnLastSlide) {\n          swiper.slideTo(0, speed, true, true);\n          emit('autoplay');\n        }\n      }\n      if (swiper.params.cssMode) {\n        autoplayStartTime = new Date().getTime();\n        requestAnimationFrame(() => {\n          run();\n        });\n      }\n    };\n    if (delay > 0) {\n      clearTimeout(timeout);\n      timeout = setTimeout(() => {\n        proceed();\n      }, delay);\n    } else {\n      requestAnimationFrame(() => {\n        proceed();\n      });\n    }\n\n    // eslint-disable-next-line\n    return delay;\n  };\n  const start = () => {\n    swiper.autoplay.running = true;\n    run();\n    emit('autoplayStart');\n  };\n  const stop = () => {\n    swiper.autoplay.running = false;\n    clearTimeout(timeout);\n    cancelAnimationFrame(raf);\n    emit('autoplayStop');\n  };\n  const pause = (internal, reset) => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    clearTimeout(timeout);\n    if (!internal) {\n      pausedByInteraction = true;\n    }\n    const proceed = () => {\n      emit('autoplayPause');\n      if (swiper.params.autoplay.waitForTransition) {\n        swiper.wrapperEl.addEventListener('transitionend', onTransitionEnd);\n      } else {\n        resume();\n      }\n    };\n    swiper.autoplay.paused = true;\n    if (reset) {\n      if (slideChanged) {\n        autoplayTimeLeft = swiper.params.autoplay.delay;\n      }\n      slideChanged = false;\n      proceed();\n      return;\n    }\n    const delay = autoplayTimeLeft || swiper.params.autoplay.delay;\n    autoplayTimeLeft = delay - (new Date().getTime() - autoplayStartTime);\n    if (swiper.isEnd && autoplayTimeLeft < 0 && !swiper.params.loop) return;\n    if (autoplayTimeLeft < 0) autoplayTimeLeft = 0;\n    proceed();\n  };\n  const resume = () => {\n    if (swiper.isEnd && autoplayTimeLeft < 0 && !swiper.params.loop || swiper.destroyed || !swiper.autoplay.running) return;\n    autoplayStartTime = new Date().getTime();\n    if (pausedByInteraction) {\n      pausedByInteraction = false;\n      run(autoplayTimeLeft);\n    } else {\n      run();\n    }\n    swiper.autoplay.paused = false;\n    emit('autoplayResume');\n  };\n  const onVisibilityChange = () => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    const document = getDocument();\n    if (document.visibilityState === 'hidden') {\n      pausedByInteraction = true;\n      pause(true);\n    }\n    if (document.visibilityState === 'visible') {\n      resume();\n    }\n  };\n  const onPointerEnter = e => {\n    if (e.pointerType !== 'mouse') return;\n    pausedByInteraction = true;\n    pause(true);\n  };\n  const onPointerLeave = e => {\n    if (e.pointerType !== 'mouse') return;\n    if (swiper.autoplay.paused) {\n      resume();\n    }\n  };\n  const attachMouseEvents = () => {\n    if (swiper.params.autoplay.pauseOnMouseEnter) {\n      swiper.el.addEventListener('pointerenter', onPointerEnter);\n      swiper.el.addEventListener('pointerleave', onPointerLeave);\n    }\n  };\n  const detachMouseEvents = () => {\n    swiper.el.removeEventListener('pointerenter', onPointerEnter);\n    swiper.el.removeEventListener('pointerleave', onPointerLeave);\n  };\n  const attachDocumentEvents = () => {\n    const document = getDocument();\n    document.addEventListener('visibilitychange', onVisibilityChange);\n  };\n  const detachDocumentEvents = () => {\n    const document = getDocument();\n    document.removeEventListener('visibilitychange', onVisibilityChange);\n  };\n  on('init', () => {\n    if (swiper.params.autoplay.enabled) {\n      attachMouseEvents();\n      attachDocumentEvents();\n      autoplayStartTime = new Date().getTime();\n      start();\n    }\n  });\n  on('destroy', () => {\n    detachMouseEvents();\n    detachDocumentEvents();\n    if (swiper.autoplay.running) {\n      stop();\n    }\n  });\n  on('beforeTransitionStart', (_s, speed, internal) => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    if (internal || !swiper.params.autoplay.disableOnInteraction) {\n      pause(true, true);\n    } else {\n      stop();\n    }\n  });\n  on('sliderFirstMove', () => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    if (swiper.params.autoplay.disableOnInteraction) {\n      stop();\n      return;\n    }\n    isTouched = true;\n    pausedByTouch = false;\n    pausedByInteraction = false;\n    touchStartTimeout = setTimeout(() => {\n      pausedByInteraction = true;\n      pausedByTouch = true;\n      pause(true);\n    }, 200);\n  });\n  on('touchEnd', () => {\n    if (swiper.destroyed || !swiper.autoplay.running || !isTouched) return;\n    clearTimeout(touchStartTimeout);\n    clearTimeout(timeout);\n    if (swiper.params.autoplay.disableOnInteraction) {\n      pausedByTouch = false;\n      isTouched = false;\n      return;\n    }\n    if (pausedByTouch && swiper.params.cssMode) resume();\n    pausedByTouch = false;\n    isTouched = false;\n  });\n  on('slideChange', () => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    slideChanged = true;\n  });\n  Object.assign(swiper.autoplay, {\n    start,\n    stop,\n    pause,\n    resume\n  });\n}", "map": {"version": 3, "names": ["getDocument", "Autoplay", "_ref", "swiper", "extendParams", "on", "emit", "params", "autoplay", "running", "paused", "timeLeft", "enabled", "delay", "waitForTransition", "disableOnInteraction", "stopOnLastSlide", "reverseDirection", "pauseOnMouseEnter", "timeout", "raf", "autoplayDelayTotal", "autoplayDelayCurrent", "autoplayTimeLeft", "autoplayStartTime", "Date", "getTime", "wasPaused", "isTouched", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "touchStartTimeout", "slideChanged", "pausedByInteraction", "onTransitionEnd", "e", "destroyed", "wrapperEl", "target", "removeEventListener", "resume", "calcTimeLeft", "requestAnimationFrame", "getSlideDelay", "activeSlideEl", "virtual", "slides", "filter", "slideEl", "classList", "contains", "activeIndex", "undefined", "currentSlideDelay", "parseInt", "getAttribute", "run", "delayForce", "cancelAnimationFrame", "Number", "isNaN", "speed", "proceed", "isBeginning", "loop", "rewind", "slidePrev", "slideTo", "length", "isEnd", "slideNext", "cssMode", "clearTimeout", "setTimeout", "start", "stop", "pause", "internal", "reset", "addEventListener", "onVisibilityChange", "document", "visibilityState", "onPointerEnter", "pointerType", "onPointerLeave", "attachMouseEvents", "el", "detachMouseEvents", "attachDocumentEvents", "detachDocumentEvents", "_s", "Object", "assign"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/modules/autoplay/autoplay.js"], "sourcesContent": ["/* eslint no-underscore-dangle: \"off\" */\n/* eslint no-use-before-define: \"off\" */\nimport { getDocument } from 'ssr-window';\nexport default function Autoplay({\n  swiper,\n  extendParams,\n  on,\n  emit,\n  params\n}) {\n  swiper.autoplay = {\n    running: false,\n    paused: false,\n    timeLeft: 0\n  };\n  extendParams({\n    autoplay: {\n      enabled: false,\n      delay: 3000,\n      waitForTransition: true,\n      disableOnInteraction: true,\n      stopOnLastSlide: false,\n      reverseDirection: false,\n      pauseOnMouseEnter: false\n    }\n  });\n  let timeout;\n  let raf;\n  let autoplayDelayTotal = params && params.autoplay ? params.autoplay.delay : 3000;\n  let autoplayDelayCurrent = params && params.autoplay ? params.autoplay.delay : 3000;\n  let autoplayTimeLeft;\n  let autoplayStartTime = new Date().getTime;\n  let wasPaused;\n  let isTouched;\n  let pausedByTouch;\n  let touchStartTimeout;\n  let slideChanged;\n  let pausedByInteraction;\n  function onTransitionEnd(e) {\n    if (!swiper || swiper.destroyed || !swiper.wrapperEl) return;\n    if (e.target !== swiper.wrapperEl) return;\n    swiper.wrapperEl.removeEventListener('transitionend', onTransitionEnd);\n    resume();\n  }\n  const calcTimeLeft = () => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    if (swiper.autoplay.paused) {\n      wasPaused = true;\n    } else if (wasPaused) {\n      autoplayDelayCurrent = autoplayTimeLeft;\n      wasPaused = false;\n    }\n    const timeLeft = swiper.autoplay.paused ? autoplayTimeLeft : autoplayStartTime + autoplayDelayCurrent - new Date().getTime();\n    swiper.autoplay.timeLeft = timeLeft;\n    emit('autoplayTimeLeft', timeLeft, timeLeft / autoplayDelayTotal);\n    raf = requestAnimationFrame(() => {\n      calcTimeLeft();\n    });\n  };\n  const getSlideDelay = () => {\n    let activeSlideEl;\n    if (swiper.virtual && swiper.params.virtual.enabled) {\n      activeSlideEl = swiper.slides.filter(slideEl => slideEl.classList.contains('swiper-slide-active'))[0];\n    } else {\n      activeSlideEl = swiper.slides[swiper.activeIndex];\n    }\n    if (!activeSlideEl) return undefined;\n    const currentSlideDelay = parseInt(activeSlideEl.getAttribute('data-swiper-autoplay'), 10);\n    return currentSlideDelay;\n  };\n  const run = delayForce => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    cancelAnimationFrame(raf);\n    calcTimeLeft();\n    let delay = typeof delayForce === 'undefined' ? swiper.params.autoplay.delay : delayForce;\n    autoplayDelayTotal = swiper.params.autoplay.delay;\n    autoplayDelayCurrent = swiper.params.autoplay.delay;\n    const currentSlideDelay = getSlideDelay();\n    if (!Number.isNaN(currentSlideDelay) && currentSlideDelay > 0 && typeof delayForce === 'undefined') {\n      delay = currentSlideDelay;\n      autoplayDelayTotal = currentSlideDelay;\n      autoplayDelayCurrent = currentSlideDelay;\n    }\n    autoplayTimeLeft = delay;\n    const speed = swiper.params.speed;\n    const proceed = () => {\n      if (!swiper || swiper.destroyed) return;\n      if (swiper.params.autoplay.reverseDirection) {\n        if (!swiper.isBeginning || swiper.params.loop || swiper.params.rewind) {\n          swiper.slidePrev(speed, true, true);\n          emit('autoplay');\n        } else if (!swiper.params.autoplay.stopOnLastSlide) {\n          swiper.slideTo(swiper.slides.length - 1, speed, true, true);\n          emit('autoplay');\n        }\n      } else {\n        if (!swiper.isEnd || swiper.params.loop || swiper.params.rewind) {\n          swiper.slideNext(speed, true, true);\n          emit('autoplay');\n        } else if (!swiper.params.autoplay.stopOnLastSlide) {\n          swiper.slideTo(0, speed, true, true);\n          emit('autoplay');\n        }\n      }\n      if (swiper.params.cssMode) {\n        autoplayStartTime = new Date().getTime();\n        requestAnimationFrame(() => {\n          run();\n        });\n      }\n    };\n    if (delay > 0) {\n      clearTimeout(timeout);\n      timeout = setTimeout(() => {\n        proceed();\n      }, delay);\n    } else {\n      requestAnimationFrame(() => {\n        proceed();\n      });\n    }\n\n    // eslint-disable-next-line\n    return delay;\n  };\n  const start = () => {\n    swiper.autoplay.running = true;\n    run();\n    emit('autoplayStart');\n  };\n  const stop = () => {\n    swiper.autoplay.running = false;\n    clearTimeout(timeout);\n    cancelAnimationFrame(raf);\n    emit('autoplayStop');\n  };\n  const pause = (internal, reset) => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    clearTimeout(timeout);\n    if (!internal) {\n      pausedByInteraction = true;\n    }\n    const proceed = () => {\n      emit('autoplayPause');\n      if (swiper.params.autoplay.waitForTransition) {\n        swiper.wrapperEl.addEventListener('transitionend', onTransitionEnd);\n      } else {\n        resume();\n      }\n    };\n    swiper.autoplay.paused = true;\n    if (reset) {\n      if (slideChanged) {\n        autoplayTimeLeft = swiper.params.autoplay.delay;\n      }\n      slideChanged = false;\n      proceed();\n      return;\n    }\n    const delay = autoplayTimeLeft || swiper.params.autoplay.delay;\n    autoplayTimeLeft = delay - (new Date().getTime() - autoplayStartTime);\n    if (swiper.isEnd && autoplayTimeLeft < 0 && !swiper.params.loop) return;\n    if (autoplayTimeLeft < 0) autoplayTimeLeft = 0;\n    proceed();\n  };\n  const resume = () => {\n    if (swiper.isEnd && autoplayTimeLeft < 0 && !swiper.params.loop || swiper.destroyed || !swiper.autoplay.running) return;\n    autoplayStartTime = new Date().getTime();\n    if (pausedByInteraction) {\n      pausedByInteraction = false;\n      run(autoplayTimeLeft);\n    } else {\n      run();\n    }\n    swiper.autoplay.paused = false;\n    emit('autoplayResume');\n  };\n  const onVisibilityChange = () => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    const document = getDocument();\n    if (document.visibilityState === 'hidden') {\n      pausedByInteraction = true;\n      pause(true);\n    }\n    if (document.visibilityState === 'visible') {\n      resume();\n    }\n  };\n  const onPointerEnter = e => {\n    if (e.pointerType !== 'mouse') return;\n    pausedByInteraction = true;\n    pause(true);\n  };\n  const onPointerLeave = e => {\n    if (e.pointerType !== 'mouse') return;\n    if (swiper.autoplay.paused) {\n      resume();\n    }\n  };\n  const attachMouseEvents = () => {\n    if (swiper.params.autoplay.pauseOnMouseEnter) {\n      swiper.el.addEventListener('pointerenter', onPointerEnter);\n      swiper.el.addEventListener('pointerleave', onPointerLeave);\n    }\n  };\n  const detachMouseEvents = () => {\n    swiper.el.removeEventListener('pointerenter', onPointerEnter);\n    swiper.el.removeEventListener('pointerleave', onPointerLeave);\n  };\n  const attachDocumentEvents = () => {\n    const document = getDocument();\n    document.addEventListener('visibilitychange', onVisibilityChange);\n  };\n  const detachDocumentEvents = () => {\n    const document = getDocument();\n    document.removeEventListener('visibilitychange', onVisibilityChange);\n  };\n  on('init', () => {\n    if (swiper.params.autoplay.enabled) {\n      attachMouseEvents();\n      attachDocumentEvents();\n      autoplayStartTime = new Date().getTime();\n      start();\n    }\n  });\n  on('destroy', () => {\n    detachMouseEvents();\n    detachDocumentEvents();\n    if (swiper.autoplay.running) {\n      stop();\n    }\n  });\n  on('beforeTransitionStart', (_s, speed, internal) => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    if (internal || !swiper.params.autoplay.disableOnInteraction) {\n      pause(true, true);\n    } else {\n      stop();\n    }\n  });\n  on('sliderFirstMove', () => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    if (swiper.params.autoplay.disableOnInteraction) {\n      stop();\n      return;\n    }\n    isTouched = true;\n    pausedByTouch = false;\n    pausedByInteraction = false;\n    touchStartTimeout = setTimeout(() => {\n      pausedByInteraction = true;\n      pausedByTouch = true;\n      pause(true);\n    }, 200);\n  });\n  on('touchEnd', () => {\n    if (swiper.destroyed || !swiper.autoplay.running || !isTouched) return;\n    clearTimeout(touchStartTimeout);\n    clearTimeout(timeout);\n    if (swiper.params.autoplay.disableOnInteraction) {\n      pausedByTouch = false;\n      isTouched = false;\n      return;\n    }\n    if (pausedByTouch && swiper.params.cssMode) resume();\n    pausedByTouch = false;\n    isTouched = false;\n  });\n  on('slideChange', () => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    slideChanged = true;\n  });\n  Object.assign(swiper.autoplay, {\n    start,\n    stop,\n    pause,\n    resume\n  });\n}"], "mappings": "AAAA;AACA;AACA,SAASA,WAAW,QAAQ,YAAY;AACxC,eAAe,SAASC,QAAQA,CAAAC,IAAA,EAM7B;EAAA,IAN8B;IAC/BC,MAAM;IACNC,YAAY;IACZC,EAAE;IACFC,IAAI;IACJC;EACF,CAAC,GAAAL,IAAA;EACCC,MAAM,CAACK,QAAQ,GAAG;IAChBC,OAAO,EAAE,KAAK;IACdC,MAAM,EAAE,KAAK;IACbC,QAAQ,EAAE;EACZ,CAAC;EACDP,YAAY,CAAC;IACXI,QAAQ,EAAE;MACRI,OAAO,EAAE,KAAK;MACdC,KAAK,EAAE,IAAI;MACXC,iBAAiB,EAAE,IAAI;MACvBC,oBAAoB,EAAE,IAAI;MAC1BC,eAAe,EAAE,KAAK;MACtBC,gBAAgB,EAAE,KAAK;MACvBC,iBAAiB,EAAE;IACrB;EACF,CAAC,CAAC;EACF,IAAIC,OAAO;EACX,IAAIC,GAAG;EACP,IAAIC,kBAAkB,GAAGd,MAAM,IAAIA,MAAM,CAACC,QAAQ,GAAGD,MAAM,CAACC,QAAQ,CAACK,KAAK,GAAG,IAAI;EACjF,IAAIS,oBAAoB,GAAGf,MAAM,IAAIA,MAAM,CAACC,QAAQ,GAAGD,MAAM,CAACC,QAAQ,CAACK,KAAK,GAAG,IAAI;EACnF,IAAIU,gBAAgB;EACpB,IAAIC,iBAAiB,GAAG,IAAIC,IAAI,EAAE,CAACC,OAAO;EAC1C,IAAIC,SAAS;EACb,IAAIC,SAAS;EACb,IAAIC,aAAa;EACjB,IAAIC,iBAAiB;EACrB,IAAIC,YAAY;EAChB,IAAIC,mBAAmB;EACvB,SAASC,eAAeA,CAACC,CAAC,EAAE;IAC1B,IAAI,CAAC/B,MAAM,IAAIA,MAAM,CAACgC,SAAS,IAAI,CAAChC,MAAM,CAACiC,SAAS,EAAE;IACtD,IAAIF,CAAC,CAACG,MAAM,KAAKlC,MAAM,CAACiC,SAAS,EAAE;IACnCjC,MAAM,CAACiC,SAAS,CAACE,mBAAmB,CAAC,eAAe,EAAEL,eAAe,CAAC;IACtEM,MAAM,EAAE;EACV;EACA,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIrC,MAAM,CAACgC,SAAS,IAAI,CAAChC,MAAM,CAACK,QAAQ,CAACC,OAAO,EAAE;IAClD,IAAIN,MAAM,CAACK,QAAQ,CAACE,MAAM,EAAE;MAC1BiB,SAAS,GAAG,IAAI;IAClB,CAAC,MAAM,IAAIA,SAAS,EAAE;MACpBL,oBAAoB,GAAGC,gBAAgB;MACvCI,SAAS,GAAG,KAAK;IACnB;IACA,MAAMhB,QAAQ,GAAGR,MAAM,CAACK,QAAQ,CAACE,MAAM,GAAGa,gBAAgB,GAAGC,iBAAiB,GAAGF,oBAAoB,GAAG,IAAIG,IAAI,EAAE,CAACC,OAAO,EAAE;IAC5HvB,MAAM,CAACK,QAAQ,CAACG,QAAQ,GAAGA,QAAQ;IACnCL,IAAI,CAAC,kBAAkB,EAAEK,QAAQ,EAAEA,QAAQ,GAAGU,kBAAkB,CAAC;IACjED,GAAG,GAAGqB,qBAAqB,CAAC,MAAM;MAChCD,YAAY,EAAE;IAChB,CAAC,CAAC;EACJ,CAAC;EACD,MAAME,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAIC,aAAa;IACjB,IAAIxC,MAAM,CAACyC,OAAO,IAAIzC,MAAM,CAACI,MAAM,CAACqC,OAAO,CAAChC,OAAO,EAAE;MACnD+B,aAAa,GAAGxC,MAAM,CAAC0C,MAAM,CAACC,MAAM,CAACC,OAAO,IAAIA,OAAO,CAACC,SAAS,CAACC,QAAQ,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC;IACvG,CAAC,MAAM;MACLN,aAAa,GAAGxC,MAAM,CAAC0C,MAAM,CAAC1C,MAAM,CAAC+C,WAAW,CAAC;IACnD;IACA,IAAI,CAACP,aAAa,EAAE,OAAOQ,SAAS;IACpC,MAAMC,iBAAiB,GAAGC,QAAQ,CAACV,aAAa,CAACW,YAAY,CAAC,sBAAsB,CAAC,EAAE,EAAE,CAAC;IAC1F,OAAOF,iBAAiB;EAC1B,CAAC;EACD,MAAMG,GAAG,GAAGC,UAAU,IAAI;IACxB,IAAIrD,MAAM,CAACgC,SAAS,IAAI,CAAChC,MAAM,CAACK,QAAQ,CAACC,OAAO,EAAE;IAClDgD,oBAAoB,CAACrC,GAAG,CAAC;IACzBoB,YAAY,EAAE;IACd,IAAI3B,KAAK,GAAG,OAAO2C,UAAU,KAAK,WAAW,GAAGrD,MAAM,CAACI,MAAM,CAACC,QAAQ,CAACK,KAAK,GAAG2C,UAAU;IACzFnC,kBAAkB,GAAGlB,MAAM,CAACI,MAAM,CAACC,QAAQ,CAACK,KAAK;IACjDS,oBAAoB,GAAGnB,MAAM,CAACI,MAAM,CAACC,QAAQ,CAACK,KAAK;IACnD,MAAMuC,iBAAiB,GAAGV,aAAa,EAAE;IACzC,IAAI,CAACgB,MAAM,CAACC,KAAK,CAACP,iBAAiB,CAAC,IAAIA,iBAAiB,GAAG,CAAC,IAAI,OAAOI,UAAU,KAAK,WAAW,EAAE;MAClG3C,KAAK,GAAGuC,iBAAiB;MACzB/B,kBAAkB,GAAG+B,iBAAiB;MACtC9B,oBAAoB,GAAG8B,iBAAiB;IAC1C;IACA7B,gBAAgB,GAAGV,KAAK;IACxB,MAAM+C,KAAK,GAAGzD,MAAM,CAACI,MAAM,CAACqD,KAAK;IACjC,MAAMC,OAAO,GAAGA,CAAA,KAAM;MACpB,IAAI,CAAC1D,MAAM,IAAIA,MAAM,CAACgC,SAAS,EAAE;MACjC,IAAIhC,MAAM,CAACI,MAAM,CAACC,QAAQ,CAACS,gBAAgB,EAAE;QAC3C,IAAI,CAACd,MAAM,CAAC2D,WAAW,IAAI3D,MAAM,CAACI,MAAM,CAACwD,IAAI,IAAI5D,MAAM,CAACI,MAAM,CAACyD,MAAM,EAAE;UACrE7D,MAAM,CAAC8D,SAAS,CAACL,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC;UACnCtD,IAAI,CAAC,UAAU,CAAC;QAClB,CAAC,MAAM,IAAI,CAACH,MAAM,CAACI,MAAM,CAACC,QAAQ,CAACQ,eAAe,EAAE;UAClDb,MAAM,CAAC+D,OAAO,CAAC/D,MAAM,CAAC0C,MAAM,CAACsB,MAAM,GAAG,CAAC,EAAEP,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC;UAC3DtD,IAAI,CAAC,UAAU,CAAC;QAClB;MACF,CAAC,MAAM;QACL,IAAI,CAACH,MAAM,CAACiE,KAAK,IAAIjE,MAAM,CAACI,MAAM,CAACwD,IAAI,IAAI5D,MAAM,CAACI,MAAM,CAACyD,MAAM,EAAE;UAC/D7D,MAAM,CAACkE,SAAS,CAACT,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC;UACnCtD,IAAI,CAAC,UAAU,CAAC;QAClB,CAAC,MAAM,IAAI,CAACH,MAAM,CAACI,MAAM,CAACC,QAAQ,CAACQ,eAAe,EAAE;UAClDb,MAAM,CAAC+D,OAAO,CAAC,CAAC,EAAEN,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC;UACpCtD,IAAI,CAAC,UAAU,CAAC;QAClB;MACF;MACA,IAAIH,MAAM,CAACI,MAAM,CAAC+D,OAAO,EAAE;QACzB9C,iBAAiB,GAAG,IAAIC,IAAI,EAAE,CAACC,OAAO,EAAE;QACxCe,qBAAqB,CAAC,MAAM;UAC1Bc,GAAG,EAAE;QACP,CAAC,CAAC;MACJ;IACF,CAAC;IACD,IAAI1C,KAAK,GAAG,CAAC,EAAE;MACb0D,YAAY,CAACpD,OAAO,CAAC;MACrBA,OAAO,GAAGqD,UAAU,CAAC,MAAM;QACzBX,OAAO,EAAE;MACX,CAAC,EAAEhD,KAAK,CAAC;IACX,CAAC,MAAM;MACL4B,qBAAqB,CAAC,MAAM;QAC1BoB,OAAO,EAAE;MACX,CAAC,CAAC;IACJ;;IAEA;IACA,OAAOhD,KAAK;EACd,CAAC;EACD,MAAM4D,KAAK,GAAGA,CAAA,KAAM;IAClBtE,MAAM,CAACK,QAAQ,CAACC,OAAO,GAAG,IAAI;IAC9B8C,GAAG,EAAE;IACLjD,IAAI,CAAC,eAAe,CAAC;EACvB,CAAC;EACD,MAAMoE,IAAI,GAAGA,CAAA,KAAM;IACjBvE,MAAM,CAACK,QAAQ,CAACC,OAAO,GAAG,KAAK;IAC/B8D,YAAY,CAACpD,OAAO,CAAC;IACrBsC,oBAAoB,CAACrC,GAAG,CAAC;IACzBd,IAAI,CAAC,cAAc,CAAC;EACtB,CAAC;EACD,MAAMqE,KAAK,GAAGA,CAACC,QAAQ,EAAEC,KAAK,KAAK;IACjC,IAAI1E,MAAM,CAACgC,SAAS,IAAI,CAAChC,MAAM,CAACK,QAAQ,CAACC,OAAO,EAAE;IAClD8D,YAAY,CAACpD,OAAO,CAAC;IACrB,IAAI,CAACyD,QAAQ,EAAE;MACb5C,mBAAmB,GAAG,IAAI;IAC5B;IACA,MAAM6B,OAAO,GAAGA,CAAA,KAAM;MACpBvD,IAAI,CAAC,eAAe,CAAC;MACrB,IAAIH,MAAM,CAACI,MAAM,CAACC,QAAQ,CAACM,iBAAiB,EAAE;QAC5CX,MAAM,CAACiC,SAAS,CAAC0C,gBAAgB,CAAC,eAAe,EAAE7C,eAAe,CAAC;MACrE,CAAC,MAAM;QACLM,MAAM,EAAE;MACV;IACF,CAAC;IACDpC,MAAM,CAACK,QAAQ,CAACE,MAAM,GAAG,IAAI;IAC7B,IAAImE,KAAK,EAAE;MACT,IAAI9C,YAAY,EAAE;QAChBR,gBAAgB,GAAGpB,MAAM,CAACI,MAAM,CAACC,QAAQ,CAACK,KAAK;MACjD;MACAkB,YAAY,GAAG,KAAK;MACpB8B,OAAO,EAAE;MACT;IACF;IACA,MAAMhD,KAAK,GAAGU,gBAAgB,IAAIpB,MAAM,CAACI,MAAM,CAACC,QAAQ,CAACK,KAAK;IAC9DU,gBAAgB,GAAGV,KAAK,IAAI,IAAIY,IAAI,EAAE,CAACC,OAAO,EAAE,GAAGF,iBAAiB,CAAC;IACrE,IAAIrB,MAAM,CAACiE,KAAK,IAAI7C,gBAAgB,GAAG,CAAC,IAAI,CAACpB,MAAM,CAACI,MAAM,CAACwD,IAAI,EAAE;IACjE,IAAIxC,gBAAgB,GAAG,CAAC,EAAEA,gBAAgB,GAAG,CAAC;IAC9CsC,OAAO,EAAE;EACX,CAAC;EACD,MAAMtB,MAAM,GAAGA,CAAA,KAAM;IACnB,IAAIpC,MAAM,CAACiE,KAAK,IAAI7C,gBAAgB,GAAG,CAAC,IAAI,CAACpB,MAAM,CAACI,MAAM,CAACwD,IAAI,IAAI5D,MAAM,CAACgC,SAAS,IAAI,CAAChC,MAAM,CAACK,QAAQ,CAACC,OAAO,EAAE;IACjHe,iBAAiB,GAAG,IAAIC,IAAI,EAAE,CAACC,OAAO,EAAE;IACxC,IAAIM,mBAAmB,EAAE;MACvBA,mBAAmB,GAAG,KAAK;MAC3BuB,GAAG,CAAChC,gBAAgB,CAAC;IACvB,CAAC,MAAM;MACLgC,GAAG,EAAE;IACP;IACApD,MAAM,CAACK,QAAQ,CAACE,MAAM,GAAG,KAAK;IAC9BJ,IAAI,CAAC,gBAAgB,CAAC;EACxB,CAAC;EACD,MAAMyE,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAI5E,MAAM,CAACgC,SAAS,IAAI,CAAChC,MAAM,CAACK,QAAQ,CAACC,OAAO,EAAE;IAClD,MAAMuE,QAAQ,GAAGhF,WAAW,EAAE;IAC9B,IAAIgF,QAAQ,CAACC,eAAe,KAAK,QAAQ,EAAE;MACzCjD,mBAAmB,GAAG,IAAI;MAC1B2C,KAAK,CAAC,IAAI,CAAC;IACb;IACA,IAAIK,QAAQ,CAACC,eAAe,KAAK,SAAS,EAAE;MAC1C1C,MAAM,EAAE;IACV;EACF,CAAC;EACD,MAAM2C,cAAc,GAAGhD,CAAC,IAAI;IAC1B,IAAIA,CAAC,CAACiD,WAAW,KAAK,OAAO,EAAE;IAC/BnD,mBAAmB,GAAG,IAAI;IAC1B2C,KAAK,CAAC,IAAI,CAAC;EACb,CAAC;EACD,MAAMS,cAAc,GAAGlD,CAAC,IAAI;IAC1B,IAAIA,CAAC,CAACiD,WAAW,KAAK,OAAO,EAAE;IAC/B,IAAIhF,MAAM,CAACK,QAAQ,CAACE,MAAM,EAAE;MAC1B6B,MAAM,EAAE;IACV;EACF,CAAC;EACD,MAAM8C,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAIlF,MAAM,CAACI,MAAM,CAACC,QAAQ,CAACU,iBAAiB,EAAE;MAC5Cf,MAAM,CAACmF,EAAE,CAACR,gBAAgB,CAAC,cAAc,EAAEI,cAAc,CAAC;MAC1D/E,MAAM,CAACmF,EAAE,CAACR,gBAAgB,CAAC,cAAc,EAAEM,cAAc,CAAC;IAC5D;EACF,CAAC;EACD,MAAMG,iBAAiB,GAAGA,CAAA,KAAM;IAC9BpF,MAAM,CAACmF,EAAE,CAAChD,mBAAmB,CAAC,cAAc,EAAE4C,cAAc,CAAC;IAC7D/E,MAAM,CAACmF,EAAE,CAAChD,mBAAmB,CAAC,cAAc,EAAE8C,cAAc,CAAC;EAC/D,CAAC;EACD,MAAMI,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAMR,QAAQ,GAAGhF,WAAW,EAAE;IAC9BgF,QAAQ,CAACF,gBAAgB,CAAC,kBAAkB,EAAEC,kBAAkB,CAAC;EACnE,CAAC;EACD,MAAMU,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAMT,QAAQ,GAAGhF,WAAW,EAAE;IAC9BgF,QAAQ,CAAC1C,mBAAmB,CAAC,kBAAkB,EAAEyC,kBAAkB,CAAC;EACtE,CAAC;EACD1E,EAAE,CAAC,MAAM,EAAE,MAAM;IACf,IAAIF,MAAM,CAACI,MAAM,CAACC,QAAQ,CAACI,OAAO,EAAE;MAClCyE,iBAAiB,EAAE;MACnBG,oBAAoB,EAAE;MACtBhE,iBAAiB,GAAG,IAAIC,IAAI,EAAE,CAACC,OAAO,EAAE;MACxC+C,KAAK,EAAE;IACT;EACF,CAAC,CAAC;EACFpE,EAAE,CAAC,SAAS,EAAE,MAAM;IAClBkF,iBAAiB,EAAE;IACnBE,oBAAoB,EAAE;IACtB,IAAItF,MAAM,CAACK,QAAQ,CAACC,OAAO,EAAE;MAC3BiE,IAAI,EAAE;IACR;EACF,CAAC,CAAC;EACFrE,EAAE,CAAC,uBAAuB,EAAE,CAACqF,EAAE,EAAE9B,KAAK,EAAEgB,QAAQ,KAAK;IACnD,IAAIzE,MAAM,CAACgC,SAAS,IAAI,CAAChC,MAAM,CAACK,QAAQ,CAACC,OAAO,EAAE;IAClD,IAAImE,QAAQ,IAAI,CAACzE,MAAM,CAACI,MAAM,CAACC,QAAQ,CAACO,oBAAoB,EAAE;MAC5D4D,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC;IACnB,CAAC,MAAM;MACLD,IAAI,EAAE;IACR;EACF,CAAC,CAAC;EACFrE,EAAE,CAAC,iBAAiB,EAAE,MAAM;IAC1B,IAAIF,MAAM,CAACgC,SAAS,IAAI,CAAChC,MAAM,CAACK,QAAQ,CAACC,OAAO,EAAE;IAClD,IAAIN,MAAM,CAACI,MAAM,CAACC,QAAQ,CAACO,oBAAoB,EAAE;MAC/C2D,IAAI,EAAE;MACN;IACF;IACA9C,SAAS,GAAG,IAAI;IAChBC,aAAa,GAAG,KAAK;IACrBG,mBAAmB,GAAG,KAAK;IAC3BF,iBAAiB,GAAG0C,UAAU,CAAC,MAAM;MACnCxC,mBAAmB,GAAG,IAAI;MAC1BH,aAAa,GAAG,IAAI;MACpB8C,KAAK,CAAC,IAAI,CAAC;IACb,CAAC,EAAE,GAAG,CAAC;EACT,CAAC,CAAC;EACFtE,EAAE,CAAC,UAAU,EAAE,MAAM;IACnB,IAAIF,MAAM,CAACgC,SAAS,IAAI,CAAChC,MAAM,CAACK,QAAQ,CAACC,OAAO,IAAI,CAACmB,SAAS,EAAE;IAChE2C,YAAY,CAACzC,iBAAiB,CAAC;IAC/ByC,YAAY,CAACpD,OAAO,CAAC;IACrB,IAAIhB,MAAM,CAACI,MAAM,CAACC,QAAQ,CAACO,oBAAoB,EAAE;MAC/Cc,aAAa,GAAG,KAAK;MACrBD,SAAS,GAAG,KAAK;MACjB;IACF;IACA,IAAIC,aAAa,IAAI1B,MAAM,CAACI,MAAM,CAAC+D,OAAO,EAAE/B,MAAM,EAAE;IACpDV,aAAa,GAAG,KAAK;IACrBD,SAAS,GAAG,KAAK;EACnB,CAAC,CAAC;EACFvB,EAAE,CAAC,aAAa,EAAE,MAAM;IACtB,IAAIF,MAAM,CAACgC,SAAS,IAAI,CAAChC,MAAM,CAACK,QAAQ,CAACC,OAAO,EAAE;IAClDsB,YAAY,GAAG,IAAI;EACrB,CAAC,CAAC;EACF4D,MAAM,CAACC,MAAM,CAACzF,MAAM,CAACK,QAAQ,EAAE;IAC7BiE,KAAK;IACLC,IAAI;IACJC,KAAK;IACLpC;EACF,CAAC,CAAC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}