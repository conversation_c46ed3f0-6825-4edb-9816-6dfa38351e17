{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\");\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nvar _classCallCheck2 = _interopRequireDefault(require(\"@babel/runtime/helpers/classCallCheck\"));\nvar _createClass2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createClass\"));\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"@babel/runtime/helpers/possibleConstructorReturn\"));\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"@babel/runtime/helpers/getPrototypeOf\"));\nvar _assertThisInitialized2 = _interopRequireDefault(require(\"@babel/runtime/helpers/assertThisInitialized\"));\nvar _inherits2 = _interopRequireDefault(require(\"@babel/runtime/helpers/inherits\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _react = _interopRequireWildcard(require(\"react\"));\nvar _classnames = _interopRequireDefault(require(\"classnames\"));\nvar _MenuButton = _interopRequireDefault(require(\"../menu/MenuButton\"));\nvar propTypes = {\n  player: _propTypes[\"default\"].object,\n  actions: _propTypes[\"default\"].object,\n  className: _propTypes[\"default\"].string,\n  offMenuText: _propTypes[\"default\"].string,\n  showOffMenu: _propTypes[\"default\"].bool,\n  kinds: _propTypes[\"default\"].array\n};\nvar defaultProps = {\n  offMenuText: 'Off',\n  showOffMenu: true,\n  kinds: ['captions', 'subtitles'] // `kind`s of TextTrack to look for to associate it with this menu.\n};\n\nvar ClosedCaptionButton = /*#__PURE__*/\nfunction (_Component) {\n  (0, _inherits2[\"default\"])(ClosedCaptionButton, _Component);\n  function ClosedCaptionButton(props, context) {\n    var _this;\n    (0, _classCallCheck2[\"default\"])(this, ClosedCaptionButton);\n    _this = (0, _possibleConstructorReturn2[\"default\"])(this, (0, _getPrototypeOf2[\"default\"])(ClosedCaptionButton).call(this, props, context));\n    _this.getTextTrackItems = _this.getTextTrackItems.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.updateState = _this.updateState.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleSelectItem = _this.handleSelectItem.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.state = _this.getTextTrackItems();\n    return _this;\n  }\n  (0, _createClass2[\"default\"])(ClosedCaptionButton, [{\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate() {\n      this.updateState();\n    }\n  }, {\n    key: \"getTextTrackItems\",\n    value: function getTextTrackItems() {\n      var _this$props = this.props,\n        kinds = _this$props.kinds,\n        player = _this$props.player,\n        offMenuText = _this$props.offMenuText,\n        showOffMenu = _this$props.showOffMenu;\n      var textTracks = player.textTracks,\n        activeTextTrack = player.activeTextTrack;\n      var textTrackItems = {\n        items: [],\n        selectedIndex: 0\n      };\n      var tracks = Array.from(textTracks || []);\n      if (tracks.length === 0) {\n        return textTrackItems;\n      }\n      if (showOffMenu) {\n        textTrackItems.items.push({\n          label: offMenuText || 'Off',\n          value: null\n        });\n      }\n      tracks.forEach(function (textTrack) {\n        // ignore invalid text track kind\n        if (kinds.length && !kinds.includes(textTrack.kind)) {\n          return;\n        }\n        textTrackItems.items.push({\n          label: textTrack.label,\n          value: textTrack.language\n        });\n      });\n      textTrackItems.selectedIndex = textTrackItems.items.findIndex(function (item) {\n        return activeTextTrack && activeTextTrack.language === item.value;\n      });\n      if (textTrackItems.selectedIndex === -1) {\n        textTrackItems.selectedIndex = 0;\n      }\n      return textTrackItems;\n    }\n  }, {\n    key: \"updateState\",\n    value: function updateState() {\n      var textTrackItems = this.getTextTrackItems();\n      if (textTrackItems.selectedIndex !== this.state.selectedIndex || !this.textTrackItemsAreEqual(textTrackItems.items, this.state.items)) {\n        this.setState(textTrackItems);\n      }\n    }\n  }, {\n    key: \"textTrackItemsAreEqual\",\n    value: function textTrackItemsAreEqual(items1, items2) {\n      if (items1.length !== items2.length) {\n        return false;\n      }\n      for (var i = 0; i < items1.length; i++) {\n        if (!items2[i] || items1[i].label !== items2[i].label || items1[i].value !== items2[i].value) {\n          return false;\n        }\n      }\n      return true;\n    }\n  }, {\n    key: \"handleSelectItem\",\n    value: function handleSelectItem(index) {\n      var _this$props2 = this.props,\n        player = _this$props2.player,\n        actions = _this$props2.actions,\n        showOffMenu = _this$props2.showOffMenu;\n      var textTracks = player.textTracks; // For the 'subtitles-off' button, the first condition will never match\n      // so all subtitles will be turned off\n\n      Array.from(textTracks).forEach(function (textTrack, i) {\n        // if it shows the `Off` menu, the first item is `Off`\n        if (index === (showOffMenu ? i + 1 : i)) {\n          textTrack.mode = 'showing';\n          actions.activateTextTrack(textTrack);\n        } else {\n          textTrack.mode = 'hidden';\n        }\n      });\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$state = this.state,\n        items = _this$state.items,\n        selectedIndex = _this$state.selectedIndex;\n      return _react[\"default\"].createElement(_MenuButton[\"default\"], {\n        className: (0, _classnames[\"default\"])('video-react-closed-caption', this.props.className),\n        onSelectItem: this.handleSelectItem,\n        items: items,\n        selectedIndex: selectedIndex\n      }, _react[\"default\"].createElement(\"span\", {\n        className: \"video-react-control-text\"\n      }, \"Closed Caption\"));\n    }\n  }]);\n  return ClosedCaptionButton;\n}(_react.Component);\nClosedCaptionButton.propTypes = propTypes;\nClosedCaptionButton.defaultProps = defaultProps;\nClosedCaptionButton.displayName = 'ClosedCaptionButton';\nvar _default = ClosedCaptionButton;\nexports[\"default\"] = _default;", "map": {"version": 3, "names": ["_interopRequireWildcard", "require", "_interopRequireDefault", "Object", "defineProperty", "exports", "value", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_assertThisInitialized2", "_inherits2", "_propTypes", "_react", "_classnames", "_MenuButton", "propTypes", "player", "object", "actions", "className", "string", "offMenuText", "showOffMenu", "bool", "kinds", "array", "defaultProps", "ClosedCaptionButton", "_Component", "props", "context", "_this", "call", "getTextTrackItems", "bind", "updateState", "handleSelectItem", "state", "key", "componentDidUpdate", "_this$props", "textTracks", "activeTextTrack", "textTrackItems", "items", "selectedIndex", "tracks", "Array", "from", "length", "push", "label", "for<PERSON>ach", "textTrack", "includes", "kind", "language", "findIndex", "item", "textTrackItemsAreEqual", "setState", "items1", "items2", "i", "index", "_this$props2", "mode", "activateTextTrack", "render", "_this$state", "createElement", "onSelectItem", "Component", "displayName", "_default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/video-react/lib/components/control-bar/ClosedCaptionButton.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\");\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _classCallCheck2 = _interopRequireDefault(require(\"@babel/runtime/helpers/classCallCheck\"));\n\nvar _createClass2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createClass\"));\n\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"@babel/runtime/helpers/possibleConstructorReturn\"));\n\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"@babel/runtime/helpers/getPrototypeOf\"));\n\nvar _assertThisInitialized2 = _interopRequireDefault(require(\"@babel/runtime/helpers/assertThisInitialized\"));\n\nvar _inherits2 = _interopRequireDefault(require(\"@babel/runtime/helpers/inherits\"));\n\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\n\nvar _react = _interopRequireWildcard(require(\"react\"));\n\nvar _classnames = _interopRequireDefault(require(\"classnames\"));\n\nvar _MenuButton = _interopRequireDefault(require(\"../menu/MenuButton\"));\n\nvar propTypes = {\n  player: _propTypes[\"default\"].object,\n  actions: _propTypes[\"default\"].object,\n  className: _propTypes[\"default\"].string,\n  offMenuText: _propTypes[\"default\"].string,\n  showOffMenu: _propTypes[\"default\"].bool,\n  kinds: _propTypes[\"default\"].array\n};\nvar defaultProps = {\n  offMenuText: 'Off',\n  showOffMenu: true,\n  kinds: ['captions', 'subtitles'] // `kind`s of TextTrack to look for to associate it with this menu.\n\n};\n\nvar ClosedCaptionButton =\n/*#__PURE__*/\nfunction (_Component) {\n  (0, _inherits2[\"default\"])(ClosedCaptionButton, _Component);\n\n  function ClosedCaptionButton(props, context) {\n    var _this;\n\n    (0, _classCallCheck2[\"default\"])(this, ClosedCaptionButton);\n    _this = (0, _possibleConstructorReturn2[\"default\"])(this, (0, _getPrototypeOf2[\"default\"])(ClosedCaptionButton).call(this, props, context));\n    _this.getTextTrackItems = _this.getTextTrackItems.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.updateState = _this.updateState.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleSelectItem = _this.handleSelectItem.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.state = _this.getTextTrackItems();\n    return _this;\n  }\n\n  (0, _createClass2[\"default\"])(ClosedCaptionButton, [{\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate() {\n      this.updateState();\n    }\n  }, {\n    key: \"getTextTrackItems\",\n    value: function getTextTrackItems() {\n      var _this$props = this.props,\n          kinds = _this$props.kinds,\n          player = _this$props.player,\n          offMenuText = _this$props.offMenuText,\n          showOffMenu = _this$props.showOffMenu;\n      var textTracks = player.textTracks,\n          activeTextTrack = player.activeTextTrack;\n      var textTrackItems = {\n        items: [],\n        selectedIndex: 0\n      };\n      var tracks = Array.from(textTracks || []);\n\n      if (tracks.length === 0) {\n        return textTrackItems;\n      }\n\n      if (showOffMenu) {\n        textTrackItems.items.push({\n          label: offMenuText || 'Off',\n          value: null\n        });\n      }\n\n      tracks.forEach(function (textTrack) {\n        // ignore invalid text track kind\n        if (kinds.length && !kinds.includes(textTrack.kind)) {\n          return;\n        }\n\n        textTrackItems.items.push({\n          label: textTrack.label,\n          value: textTrack.language\n        });\n      });\n      textTrackItems.selectedIndex = textTrackItems.items.findIndex(function (item) {\n        return activeTextTrack && activeTextTrack.language === item.value;\n      });\n\n      if (textTrackItems.selectedIndex === -1) {\n        textTrackItems.selectedIndex = 0;\n      }\n\n      return textTrackItems;\n    }\n  }, {\n    key: \"updateState\",\n    value: function updateState() {\n      var textTrackItems = this.getTextTrackItems();\n\n      if (textTrackItems.selectedIndex !== this.state.selectedIndex || !this.textTrackItemsAreEqual(textTrackItems.items, this.state.items)) {\n        this.setState(textTrackItems);\n      }\n    }\n  }, {\n    key: \"textTrackItemsAreEqual\",\n    value: function textTrackItemsAreEqual(items1, items2) {\n      if (items1.length !== items2.length) {\n        return false;\n      }\n\n      for (var i = 0; i < items1.length; i++) {\n        if (!items2[i] || items1[i].label !== items2[i].label || items1[i].value !== items2[i].value) {\n          return false;\n        }\n      }\n\n      return true;\n    }\n  }, {\n    key: \"handleSelectItem\",\n    value: function handleSelectItem(index) {\n      var _this$props2 = this.props,\n          player = _this$props2.player,\n          actions = _this$props2.actions,\n          showOffMenu = _this$props2.showOffMenu;\n      var textTracks = player.textTracks; // For the 'subtitles-off' button, the first condition will never match\n      // so all subtitles will be turned off\n\n      Array.from(textTracks).forEach(function (textTrack, i) {\n        // if it shows the `Off` menu, the first item is `Off`\n        if (index === (showOffMenu ? i + 1 : i)) {\n          textTrack.mode = 'showing';\n          actions.activateTextTrack(textTrack);\n        } else {\n          textTrack.mode = 'hidden';\n        }\n      });\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$state = this.state,\n          items = _this$state.items,\n          selectedIndex = _this$state.selectedIndex;\n      return _react[\"default\"].createElement(_MenuButton[\"default\"], {\n        className: (0, _classnames[\"default\"])('video-react-closed-caption', this.props.className),\n        onSelectItem: this.handleSelectItem,\n        items: items,\n        selectedIndex: selectedIndex\n      }, _react[\"default\"].createElement(\"span\", {\n        className: \"video-react-control-text\"\n      }, \"Closed Caption\"));\n    }\n  }]);\n  return ClosedCaptionButton;\n}(_react.Component);\n\nClosedCaptionButton.propTypes = propTypes;\nClosedCaptionButton.defaultProps = defaultProps;\nClosedCaptionButton.displayName = 'ClosedCaptionButton';\nvar _default = ClosedCaptionButton;\nexports[\"default\"] = _default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,uBAAuB,GAAGC,OAAO,CAAC,+CAA+C,CAAC;AAEtF,IAAIC,sBAAsB,GAAGD,OAAO,CAAC,8CAA8C,CAAC;AAEpFE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;AAE3B,IAAIE,gBAAgB,GAAGL,sBAAsB,CAACD,OAAO,CAAC,uCAAuC,CAAC,CAAC;AAE/F,IAAIO,aAAa,GAAGN,sBAAsB,CAACD,OAAO,CAAC,oCAAoC,CAAC,CAAC;AAEzF,IAAIQ,2BAA2B,GAAGP,sBAAsB,CAACD,OAAO,CAAC,kDAAkD,CAAC,CAAC;AAErH,IAAIS,gBAAgB,GAAGR,sBAAsB,CAACD,OAAO,CAAC,uCAAuC,CAAC,CAAC;AAE/F,IAAIU,uBAAuB,GAAGT,sBAAsB,CAACD,OAAO,CAAC,8CAA8C,CAAC,CAAC;AAE7G,IAAIW,UAAU,GAAGV,sBAAsB,CAACD,OAAO,CAAC,iCAAiC,CAAC,CAAC;AAEnF,IAAIY,UAAU,GAAGX,sBAAsB,CAACD,OAAO,CAAC,YAAY,CAAC,CAAC;AAE9D,IAAIa,MAAM,GAAGd,uBAAuB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AAEtD,IAAIc,WAAW,GAAGb,sBAAsB,CAACD,OAAO,CAAC,YAAY,CAAC,CAAC;AAE/D,IAAIe,WAAW,GAAGd,sBAAsB,CAACD,OAAO,CAAC,oBAAoB,CAAC,CAAC;AAEvE,IAAIgB,SAAS,GAAG;EACdC,MAAM,EAAEL,UAAU,CAAC,SAAS,CAAC,CAACM,MAAM;EACpCC,OAAO,EAAEP,UAAU,CAAC,SAAS,CAAC,CAACM,MAAM;EACrCE,SAAS,EAAER,UAAU,CAAC,SAAS,CAAC,CAACS,MAAM;EACvCC,WAAW,EAAEV,UAAU,CAAC,SAAS,CAAC,CAACS,MAAM;EACzCE,WAAW,EAAEX,UAAU,CAAC,SAAS,CAAC,CAACY,IAAI;EACvCC,KAAK,EAAEb,UAAU,CAAC,SAAS,CAAC,CAACc;AAC/B,CAAC;AACD,IAAIC,YAAY,GAAG;EACjBL,WAAW,EAAE,KAAK;EAClBC,WAAW,EAAE,IAAI;EACjBE,KAAK,EAAE,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;AAEnC,CAAC;;AAED,IAAIG,mBAAmB,GACvB;AACA,UAAUC,UAAU,EAAE;EACpB,CAAC,CAAC,EAAElB,UAAU,CAAC,SAAS,CAAC,EAAEiB,mBAAmB,EAAEC,UAAU,CAAC;EAE3D,SAASD,mBAAmBA,CAACE,KAAK,EAAEC,OAAO,EAAE;IAC3C,IAAIC,KAAK;IAET,CAAC,CAAC,EAAE1B,gBAAgB,CAAC,SAAS,CAAC,EAAE,IAAI,EAAEsB,mBAAmB,CAAC;IAC3DI,KAAK,GAAG,CAAC,CAAC,EAAExB,2BAA2B,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAEC,gBAAgB,CAAC,SAAS,CAAC,EAAEmB,mBAAmB,CAAC,CAACK,IAAI,CAAC,IAAI,EAAEH,KAAK,EAAEC,OAAO,CAAC,CAAC;IAC3IC,KAAK,CAACE,iBAAiB,GAAGF,KAAK,CAACE,iBAAiB,CAACC,IAAI,CAAC,CAAC,CAAC,EAAEzB,uBAAuB,CAAC,SAAS,CAAC,EAAEsB,KAAK,CAAC,CAAC;IACtGA,KAAK,CAACI,WAAW,GAAGJ,KAAK,CAACI,WAAW,CAACD,IAAI,CAAC,CAAC,CAAC,EAAEzB,uBAAuB,CAAC,SAAS,CAAC,EAAEsB,KAAK,CAAC,CAAC;IAC1FA,KAAK,CAACK,gBAAgB,GAAGL,KAAK,CAACK,gBAAgB,CAACF,IAAI,CAAC,CAAC,CAAC,EAAEzB,uBAAuB,CAAC,SAAS,CAAC,EAAEsB,KAAK,CAAC,CAAC;IACpGA,KAAK,CAACM,KAAK,GAAGN,KAAK,CAACE,iBAAiB,EAAE;IACvC,OAAOF,KAAK;EACd;EAEA,CAAC,CAAC,EAAEzB,aAAa,CAAC,SAAS,CAAC,EAAEqB,mBAAmB,EAAE,CAAC;IAClDW,GAAG,EAAE,oBAAoB;IACzBlC,KAAK,EAAE,SAASmC,kBAAkBA,CAAA,EAAG;MACnC,IAAI,CAACJ,WAAW,EAAE;IACpB;EACF,CAAC,EAAE;IACDG,GAAG,EAAE,mBAAmB;IACxBlC,KAAK,EAAE,SAAS6B,iBAAiBA,CAAA,EAAG;MAClC,IAAIO,WAAW,GAAG,IAAI,CAACX,KAAK;QACxBL,KAAK,GAAGgB,WAAW,CAAChB,KAAK;QACzBR,MAAM,GAAGwB,WAAW,CAACxB,MAAM;QAC3BK,WAAW,GAAGmB,WAAW,CAACnB,WAAW;QACrCC,WAAW,GAAGkB,WAAW,CAAClB,WAAW;MACzC,IAAImB,UAAU,GAAGzB,MAAM,CAACyB,UAAU;QAC9BC,eAAe,GAAG1B,MAAM,CAAC0B,eAAe;MAC5C,IAAIC,cAAc,GAAG;QACnBC,KAAK,EAAE,EAAE;QACTC,aAAa,EAAE;MACjB,CAAC;MACD,IAAIC,MAAM,GAAGC,KAAK,CAACC,IAAI,CAACP,UAAU,IAAI,EAAE,CAAC;MAEzC,IAAIK,MAAM,CAACG,MAAM,KAAK,CAAC,EAAE;QACvB,OAAON,cAAc;MACvB;MAEA,IAAIrB,WAAW,EAAE;QACfqB,cAAc,CAACC,KAAK,CAACM,IAAI,CAAC;UACxBC,KAAK,EAAE9B,WAAW,IAAI,KAAK;UAC3BjB,KAAK,EAAE;QACT,CAAC,CAAC;MACJ;MAEA0C,MAAM,CAACM,OAAO,CAAC,UAAUC,SAAS,EAAE;QAClC;QACA,IAAI7B,KAAK,CAACyB,MAAM,IAAI,CAACzB,KAAK,CAAC8B,QAAQ,CAACD,SAAS,CAACE,IAAI,CAAC,EAAE;UACnD;QACF;QAEAZ,cAAc,CAACC,KAAK,CAACM,IAAI,CAAC;UACxBC,KAAK,EAAEE,SAAS,CAACF,KAAK;UACtB/C,KAAK,EAAEiD,SAAS,CAACG;QACnB,CAAC,CAAC;MACJ,CAAC,CAAC;MACFb,cAAc,CAACE,aAAa,GAAGF,cAAc,CAACC,KAAK,CAACa,SAAS,CAAC,UAAUC,IAAI,EAAE;QAC5E,OAAOhB,eAAe,IAAIA,eAAe,CAACc,QAAQ,KAAKE,IAAI,CAACtD,KAAK;MACnE,CAAC,CAAC;MAEF,IAAIuC,cAAc,CAACE,aAAa,KAAK,CAAC,CAAC,EAAE;QACvCF,cAAc,CAACE,aAAa,GAAG,CAAC;MAClC;MAEA,OAAOF,cAAc;IACvB;EACF,CAAC,EAAE;IACDL,GAAG,EAAE,aAAa;IAClBlC,KAAK,EAAE,SAAS+B,WAAWA,CAAA,EAAG;MAC5B,IAAIQ,cAAc,GAAG,IAAI,CAACV,iBAAiB,EAAE;MAE7C,IAAIU,cAAc,CAACE,aAAa,KAAK,IAAI,CAACR,KAAK,CAACQ,aAAa,IAAI,CAAC,IAAI,CAACc,sBAAsB,CAAChB,cAAc,CAACC,KAAK,EAAE,IAAI,CAACP,KAAK,CAACO,KAAK,CAAC,EAAE;QACrI,IAAI,CAACgB,QAAQ,CAACjB,cAAc,CAAC;MAC/B;IACF;EACF,CAAC,EAAE;IACDL,GAAG,EAAE,wBAAwB;IAC7BlC,KAAK,EAAE,SAASuD,sBAAsBA,CAACE,MAAM,EAAEC,MAAM,EAAE;MACrD,IAAID,MAAM,CAACZ,MAAM,KAAKa,MAAM,CAACb,MAAM,EAAE;QACnC,OAAO,KAAK;MACd;MAEA,KAAK,IAAIc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,MAAM,CAACZ,MAAM,EAAEc,CAAC,EAAE,EAAE;QACtC,IAAI,CAACD,MAAM,CAACC,CAAC,CAAC,IAAIF,MAAM,CAACE,CAAC,CAAC,CAACZ,KAAK,KAAKW,MAAM,CAACC,CAAC,CAAC,CAACZ,KAAK,IAAIU,MAAM,CAACE,CAAC,CAAC,CAAC3D,KAAK,KAAK0D,MAAM,CAACC,CAAC,CAAC,CAAC3D,KAAK,EAAE;UAC5F,OAAO,KAAK;QACd;MACF;MAEA,OAAO,IAAI;IACb;EACF,CAAC,EAAE;IACDkC,GAAG,EAAE,kBAAkB;IACvBlC,KAAK,EAAE,SAASgC,gBAAgBA,CAAC4B,KAAK,EAAE;MACtC,IAAIC,YAAY,GAAG,IAAI,CAACpC,KAAK;QACzBb,MAAM,GAAGiD,YAAY,CAACjD,MAAM;QAC5BE,OAAO,GAAG+C,YAAY,CAAC/C,OAAO;QAC9BI,WAAW,GAAG2C,YAAY,CAAC3C,WAAW;MAC1C,IAAImB,UAAU,GAAGzB,MAAM,CAACyB,UAAU,CAAC,CAAC;MACpC;;MAEAM,KAAK,CAACC,IAAI,CAACP,UAAU,CAAC,CAACW,OAAO,CAAC,UAAUC,SAAS,EAAEU,CAAC,EAAE;QACrD;QACA,IAAIC,KAAK,MAAM1C,WAAW,GAAGyC,CAAC,GAAG,CAAC,GAAGA,CAAC,CAAC,EAAE;UACvCV,SAAS,CAACa,IAAI,GAAG,SAAS;UAC1BhD,OAAO,CAACiD,iBAAiB,CAACd,SAAS,CAAC;QACtC,CAAC,MAAM;UACLA,SAAS,CAACa,IAAI,GAAG,QAAQ;QAC3B;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACD5B,GAAG,EAAE,QAAQ;IACblC,KAAK,EAAE,SAASgE,MAAMA,CAAA,EAAG;MACvB,IAAIC,WAAW,GAAG,IAAI,CAAChC,KAAK;QACxBO,KAAK,GAAGyB,WAAW,CAACzB,KAAK;QACzBC,aAAa,GAAGwB,WAAW,CAACxB,aAAa;MAC7C,OAAOjC,MAAM,CAAC,SAAS,CAAC,CAAC0D,aAAa,CAACxD,WAAW,CAAC,SAAS,CAAC,EAAE;QAC7DK,SAAS,EAAE,CAAC,CAAC,EAAEN,WAAW,CAAC,SAAS,CAAC,EAAE,4BAA4B,EAAE,IAAI,CAACgB,KAAK,CAACV,SAAS,CAAC;QAC1FoD,YAAY,EAAE,IAAI,CAACnC,gBAAgB;QACnCQ,KAAK,EAAEA,KAAK;QACZC,aAAa,EAAEA;MACjB,CAAC,EAAEjC,MAAM,CAAC,SAAS,CAAC,CAAC0D,aAAa,CAAC,MAAM,EAAE;QACzCnD,SAAS,EAAE;MACb,CAAC,EAAE,gBAAgB,CAAC,CAAC;IACvB;EACF,CAAC,CAAC,CAAC;EACH,OAAOQ,mBAAmB;AAC5B,CAAC,CAACf,MAAM,CAAC4D,SAAS,CAAC;AAEnB7C,mBAAmB,CAACZ,SAAS,GAAGA,SAAS;AACzCY,mBAAmB,CAACD,YAAY,GAAGA,YAAY;AAC/CC,mBAAmB,CAAC8C,WAAW,GAAG,qBAAqB;AACvD,IAAIC,QAAQ,GAAG/C,mBAAmB;AAClCxB,OAAO,CAAC,SAAS,CAAC,GAAGuE,QAAQ"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}