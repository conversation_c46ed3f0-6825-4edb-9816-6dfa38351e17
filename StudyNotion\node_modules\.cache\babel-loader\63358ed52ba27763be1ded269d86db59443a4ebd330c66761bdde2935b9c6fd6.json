{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.IS_IOS = exports.IS_IPOD = exports.IS_IPHONE = exports.IS_IPAD = void 0;\nvar USER_AGENT = typeof window !== 'undefined' && window.navigator ? window.navigator.userAgent : ''; // const webkitVersionMap = (/AppleWebKit\\/([\\d.]+)/i).exec(USER_AGENT);\n// const appleWebkitVersion = webkitVersionMap ? parseFloat(webkitVersionMap.pop()) : null;\n\n/*\n * Device is an iPhone\n *\n * @type {Boolean}\n * @constant\n * @private\n */\n\nvar IS_IPAD = /iPad/i.test(USER_AGENT); // The Facebook app's UIWebView identifies as both an iPhone and iPad, so\n// to identify iPhones, we need to exclude iPads.\n// http://artsy.github.io/blog/2012/10/18/the-perils-of-ios-user-agent-sniffing/\n\nexports.IS_IPAD = IS_IPAD;\nvar IS_IPHONE = /iPhone/i.test(USER_AGENT) && !IS_IPAD;\nexports.IS_IPHONE = IS_IPHONE;\nvar IS_IPOD = /iPod/i.test(USER_AGENT);\nexports.IS_IPOD = IS_IPOD;\nvar IS_IOS = IS_IPHONE || IS_IPAD || IS_IPOD;\nexports.IS_IOS = IS_IOS;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "IS_IOS", "IS_IPOD", "IS_IPHONE", "IS_IPAD", "USER_AGENT", "window", "navigator", "userAgent", "test"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/video-react/lib/utils/browser.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.IS_IOS = exports.IS_IPOD = exports.IS_IPHONE = exports.IS_IPAD = void 0;\nvar USER_AGENT = typeof window !== 'undefined' && window.navigator ? window.navigator.userAgent : ''; // const webkitVersionMap = (/AppleWebKit\\/([\\d.]+)/i).exec(USER_AGENT);\n// const appleWebkitVersion = webkitVersionMap ? parseFloat(webkitVersionMap.pop()) : null;\n\n/*\n * Device is an iPhone\n *\n * @type {Boolean}\n * @constant\n * @private\n */\n\nvar IS_IPAD = /iPad/i.test(USER_AGENT); // The Facebook app's UIWebView identifies as both an iPhone and iPad, so\n// to identify iPhones, we need to exclude iPads.\n// http://artsy.github.io/blog/2012/10/18/the-perils-of-ios-user-agent-sniffing/\n\nexports.IS_IPAD = IS_IPAD;\nvar IS_IPHONE = /iPhone/i.test(USER_AGENT) && !IS_IPAD;\nexports.IS_IPHONE = IS_IPHONE;\nvar IS_IPOD = /iPod/i.test(USER_AGENT);\nexports.IS_IPOD = IS_IPOD;\nvar IS_IOS = IS_IPHONE || IS_IPAD || IS_IPOD;\nexports.IS_IOS = IS_IOS;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,MAAM,GAAGF,OAAO,CAACG,OAAO,GAAGH,OAAO,CAACI,SAAS,GAAGJ,OAAO,CAACK,OAAO,GAAG,KAAK,CAAC;AAC/E,IAAIC,UAAU,GAAG,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,SAAS,GAAGD,MAAM,CAACC,SAAS,CAACC,SAAS,GAAG,EAAE,CAAC,CAAC;AACtG;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIJ,OAAO,GAAG,OAAO,CAACK,IAAI,CAACJ,UAAU,CAAC,CAAC,CAAC;AACxC;AACA;;AAEAN,OAAO,CAACK,OAAO,GAAGA,OAAO;AACzB,IAAID,SAAS,GAAG,SAAS,CAACM,IAAI,CAACJ,UAAU,CAAC,IAAI,CAACD,OAAO;AACtDL,OAAO,CAACI,SAAS,GAAGA,SAAS;AAC7B,IAAID,OAAO,GAAG,OAAO,CAACO,IAAI,CAACJ,UAAU,CAAC;AACtCN,OAAO,CAACG,OAAO,GAAGA,OAAO;AACzB,IAAID,MAAM,GAAGE,SAAS,IAAIC,OAAO,IAAIF,OAAO;AAC5CH,OAAO,CAACE,MAAM,GAAGA,MAAM"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}