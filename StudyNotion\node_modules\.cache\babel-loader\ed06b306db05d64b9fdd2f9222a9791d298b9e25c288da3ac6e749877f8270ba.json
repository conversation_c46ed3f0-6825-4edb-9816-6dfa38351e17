{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = LoadProgressBar;\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _react = _interopRequireDefault(require(\"react\"));\nvar _classnames = _interopRequireDefault(require(\"classnames\"));\nvar propTypes = {\n  duration: _propTypes[\"default\"].number,\n  buffered: _propTypes[\"default\"].object,\n  className: _propTypes[\"default\"].string\n}; // Shows load progress\n\nfunction LoadProgressBar(_ref) {\n  var buffered = _ref.buffered,\n    duration = _ref.duration,\n    className = _ref.className;\n  if (!buffered || !buffered.length) {\n    return null;\n  }\n  var bufferedEnd = buffered.end(buffered.length - 1);\n  var style = {};\n  if (bufferedEnd > duration) {\n    bufferedEnd = duration;\n  } // get the percent width of a time compared to the total end\n\n  function percentify(time, end) {\n    var percent = time / end || 0; // no NaN\n\n    return \"\".concat((percent >= 1 ? 1 : percent) * 100, \"%\");\n  } // the width of the progress bar\n\n  style.width = percentify(bufferedEnd, duration);\n  var parts = []; // add child elements to represent the individual buffered time ranges\n\n  for (var i = 0; i < buffered.length; i++) {\n    var start = buffered.start(i);\n    var end = buffered.end(i); // set the percent based on the width of the progress bar (bufferedEnd)\n\n    var part = _react[\"default\"].createElement(\"div\", {\n      style: {\n        left: percentify(start, bufferedEnd),\n        width: percentify(end - start, bufferedEnd)\n      },\n      key: \"part-\".concat(i)\n    });\n    parts.push(part);\n  }\n  if (parts.length === 0) {\n    parts = null;\n  }\n  return _react[\"default\"].createElement(\"div\", {\n    style: style,\n    className: (0, _classnames[\"default\"])('video-react-load-progress', className)\n  }, _react[\"default\"].createElement(\"span\", {\n    className: \"video-react-control-text\"\n  }, \"Loaded: 0%\"), parts);\n}\nLoadProgressBar.propTypes = propTypes;\nLoadProgressBar.displayName = 'LoadProgressBar';", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "LoadProgressBar", "_propTypes", "_react", "_classnames", "propTypes", "duration", "number", "buffered", "object", "className", "string", "_ref", "length", "bufferedEnd", "end", "style", "percentify", "time", "percent", "concat", "width", "parts", "i", "start", "part", "createElement", "left", "key", "push", "displayName"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/video-react/lib/components/control-bar/LoadProgressBar.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = LoadProgressBar;\n\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\n\nvar _react = _interopRequireDefault(require(\"react\"));\n\nvar _classnames = _interopRequireDefault(require(\"classnames\"));\n\nvar propTypes = {\n  duration: _propTypes[\"default\"].number,\n  buffered: _propTypes[\"default\"].object,\n  className: _propTypes[\"default\"].string\n}; // Shows load progress\n\nfunction LoadProgressBar(_ref) {\n  var buffered = _ref.buffered,\n      duration = _ref.duration,\n      className = _ref.className;\n\n  if (!buffered || !buffered.length) {\n    return null;\n  }\n\n  var bufferedEnd = buffered.end(buffered.length - 1);\n  var style = {};\n\n  if (bufferedEnd > duration) {\n    bufferedEnd = duration;\n  } // get the percent width of a time compared to the total end\n\n\n  function percentify(time, end) {\n    var percent = time / end || 0; // no NaN\n\n    return \"\".concat((percent >= 1 ? 1 : percent) * 100, \"%\");\n  } // the width of the progress bar\n\n\n  style.width = percentify(bufferedEnd, duration);\n  var parts = []; // add child elements to represent the individual buffered time ranges\n\n  for (var i = 0; i < buffered.length; i++) {\n    var start = buffered.start(i);\n    var end = buffered.end(i); // set the percent based on the width of the progress bar (bufferedEnd)\n\n    var part = _react[\"default\"].createElement(\"div\", {\n      style: {\n        left: percentify(start, bufferedEnd),\n        width: percentify(end - start, bufferedEnd)\n      },\n      key: \"part-\".concat(i)\n    });\n\n    parts.push(part);\n  }\n\n  if (parts.length === 0) {\n    parts = null;\n  }\n\n  return _react[\"default\"].createElement(\"div\", {\n    style: style,\n    className: (0, _classnames[\"default\"])('video-react-load-progress', className)\n  }, _react[\"default\"].createElement(\"span\", {\n    className: \"video-react-control-text\"\n  }, \"Loaded: 0%\"), parts);\n}\n\nLoadProgressBar.propTypes = propTypes;\nLoadProgressBar.displayName = 'LoadProgressBar';"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC;AAEpFC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAAC,SAAS,CAAC,GAAGE,eAAe;AAEpC,IAAIC,UAAU,GAAGP,sBAAsB,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;AAE9D,IAAIO,MAAM,GAAGR,sBAAsB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AAErD,IAAIQ,WAAW,GAAGT,sBAAsB,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;AAE/D,IAAIS,SAAS,GAAG;EACdC,QAAQ,EAAEJ,UAAU,CAAC,SAAS,CAAC,CAACK,MAAM;EACtCC,QAAQ,EAAEN,UAAU,CAAC,SAAS,CAAC,CAACO,MAAM;EACtCC,SAAS,EAAER,UAAU,CAAC,SAAS,CAAC,CAACS;AACnC,CAAC,CAAC,CAAC;;AAEH,SAASV,eAAeA,CAACW,IAAI,EAAE;EAC7B,IAAIJ,QAAQ,GAAGI,IAAI,CAACJ,QAAQ;IACxBF,QAAQ,GAAGM,IAAI,CAACN,QAAQ;IACxBI,SAAS,GAAGE,IAAI,CAACF,SAAS;EAE9B,IAAI,CAACF,QAAQ,IAAI,CAACA,QAAQ,CAACK,MAAM,EAAE;IACjC,OAAO,IAAI;EACb;EAEA,IAAIC,WAAW,GAAGN,QAAQ,CAACO,GAAG,CAACP,QAAQ,CAACK,MAAM,GAAG,CAAC,CAAC;EACnD,IAAIG,KAAK,GAAG,CAAC,CAAC;EAEd,IAAIF,WAAW,GAAGR,QAAQ,EAAE;IAC1BQ,WAAW,GAAGR,QAAQ;EACxB,CAAC,CAAC;;EAGF,SAASW,UAAUA,CAACC,IAAI,EAAEH,GAAG,EAAE;IAC7B,IAAII,OAAO,GAAGD,IAAI,GAAGH,GAAG,IAAI,CAAC,CAAC,CAAC;;IAE/B,OAAO,EAAE,CAACK,MAAM,CAAC,CAACD,OAAO,IAAI,CAAC,GAAG,CAAC,GAAGA,OAAO,IAAI,GAAG,EAAE,GAAG,CAAC;EAC3D,CAAC,CAAC;;EAGFH,KAAK,CAACK,KAAK,GAAGJ,UAAU,CAACH,WAAW,EAAER,QAAQ,CAAC;EAC/C,IAAIgB,KAAK,GAAG,EAAE,CAAC,CAAC;;EAEhB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGf,QAAQ,CAACK,MAAM,EAAEU,CAAC,EAAE,EAAE;IACxC,IAAIC,KAAK,GAAGhB,QAAQ,CAACgB,KAAK,CAACD,CAAC,CAAC;IAC7B,IAAIR,GAAG,GAAGP,QAAQ,CAACO,GAAG,CAACQ,CAAC,CAAC,CAAC,CAAC;;IAE3B,IAAIE,IAAI,GAAGtB,MAAM,CAAC,SAAS,CAAC,CAACuB,aAAa,CAAC,KAAK,EAAE;MAChDV,KAAK,EAAE;QACLW,IAAI,EAAEV,UAAU,CAACO,KAAK,EAAEV,WAAW,CAAC;QACpCO,KAAK,EAAEJ,UAAU,CAACF,GAAG,GAAGS,KAAK,EAAEV,WAAW;MAC5C,CAAC;MACDc,GAAG,EAAE,OAAO,CAACR,MAAM,CAACG,CAAC;IACvB,CAAC,CAAC;IAEFD,KAAK,CAACO,IAAI,CAACJ,IAAI,CAAC;EAClB;EAEA,IAAIH,KAAK,CAACT,MAAM,KAAK,CAAC,EAAE;IACtBS,KAAK,GAAG,IAAI;EACd;EAEA,OAAOnB,MAAM,CAAC,SAAS,CAAC,CAACuB,aAAa,CAAC,KAAK,EAAE;IAC5CV,KAAK,EAAEA,KAAK;IACZN,SAAS,EAAE,CAAC,CAAC,EAAEN,WAAW,CAAC,SAAS,CAAC,EAAE,2BAA2B,EAAEM,SAAS;EAC/E,CAAC,EAAEP,MAAM,CAAC,SAAS,CAAC,CAACuB,aAAa,CAAC,MAAM,EAAE;IACzChB,SAAS,EAAE;EACb,CAAC,EAAE,YAAY,CAAC,EAAEY,KAAK,CAAC;AAC1B;AAEArB,eAAe,CAACI,SAAS,GAAGA,SAAS;AACrCJ,eAAe,CAAC6B,WAAW,GAAG,iBAAiB"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}