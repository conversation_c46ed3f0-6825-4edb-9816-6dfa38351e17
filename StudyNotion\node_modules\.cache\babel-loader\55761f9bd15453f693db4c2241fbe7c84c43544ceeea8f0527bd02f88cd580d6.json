{"ast": null, "code": "export default function addSlide(index, slides) {\n  const swiper = this;\n  const {\n    params,\n    activeIndex,\n    slidesEl\n  } = swiper;\n  let activeIndexBuffer = activeIndex;\n  if (params.loop) {\n    activeIndexBuffer -= swiper.loopedSlides;\n    swiper.loopDestroy();\n    swiper.recalcSlides();\n  }\n  const baseLength = swiper.slides.length;\n  if (index <= 0) {\n    swiper.prependSlide(slides);\n    return;\n  }\n  if (index >= baseLength) {\n    swiper.appendSlide(slides);\n    return;\n  }\n  let newActiveIndex = activeIndexBuffer > index ? activeIndexBuffer + 1 : activeIndexBuffer;\n  const slidesBuffer = [];\n  for (let i = baseLength - 1; i >= index; i -= 1) {\n    const currentSlide = swiper.slides[i];\n    currentSlide.remove();\n    slidesBuffer.unshift(currentSlide);\n  }\n  if (typeof slides === 'object' && 'length' in slides) {\n    for (let i = 0; i < slides.length; i += 1) {\n      if (slides[i]) slidesEl.append(slides[i]);\n    }\n    newActiveIndex = activeIndexBuffer > index ? activeIndexBuffer + slides.length : activeIndexBuffer;\n  } else {\n    slidesEl.append(slides);\n  }\n  for (let i = 0; i < slidesBuffer.length; i += 1) {\n    slidesEl.append(slidesBuffer[i]);\n  }\n  swiper.recalcSlides();\n  if (params.loop) {\n    swiper.loopCreate();\n  }\n  if (!params.observer || swiper.isElement) {\n    swiper.update();\n  }\n  if (params.loop) {\n    swiper.slideTo(newActiveIndex + swiper.loopedSlides, 0, false);\n  } else {\n    swiper.slideTo(newActiveIndex, 0, false);\n  }\n}", "map": {"version": 3, "names": ["addSlide", "index", "slides", "swiper", "params", "activeIndex", "slidesEl", "activeIndexBuffer", "loop", "loopedSlides", "loop<PERSON><PERSON><PERSON>", "recalcSlides", "baseLength", "length", "prependSlide", "appendSlide", "newActiveIndex", "slidesBuffer", "i", "currentSlide", "remove", "unshift", "append", "loopCreate", "observer", "isElement", "update", "slideTo"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/modules/manipulation/methods/addSlide.js"], "sourcesContent": ["export default function addSlide(index, slides) {\n  const swiper = this;\n  const {\n    params,\n    activeIndex,\n    slidesEl\n  } = swiper;\n  let activeIndexBuffer = activeIndex;\n  if (params.loop) {\n    activeIndexBuffer -= swiper.loopedSlides;\n    swiper.loopDestroy();\n    swiper.recalcSlides();\n  }\n  const baseLength = swiper.slides.length;\n  if (index <= 0) {\n    swiper.prependSlide(slides);\n    return;\n  }\n  if (index >= baseLength) {\n    swiper.appendSlide(slides);\n    return;\n  }\n  let newActiveIndex = activeIndexBuffer > index ? activeIndexBuffer + 1 : activeIndexBuffer;\n  const slidesBuffer = [];\n  for (let i = baseLength - 1; i >= index; i -= 1) {\n    const currentSlide = swiper.slides[i];\n    currentSlide.remove();\n    slidesBuffer.unshift(currentSlide);\n  }\n  if (typeof slides === 'object' && 'length' in slides) {\n    for (let i = 0; i < slides.length; i += 1) {\n      if (slides[i]) slidesEl.append(slides[i]);\n    }\n    newActiveIndex = activeIndexBuffer > index ? activeIndexBuffer + slides.length : activeIndexBuffer;\n  } else {\n    slidesEl.append(slides);\n  }\n  for (let i = 0; i < slidesBuffer.length; i += 1) {\n    slidesEl.append(slidesBuffer[i]);\n  }\n  swiper.recalcSlides();\n  if (params.loop) {\n    swiper.loopCreate();\n  }\n  if (!params.observer || swiper.isElement) {\n    swiper.update();\n  }\n  if (params.loop) {\n    swiper.slideTo(newActiveIndex + swiper.loopedSlides, 0, false);\n  } else {\n    swiper.slideTo(newActiveIndex, 0, false);\n  }\n}"], "mappings": "AAAA,eAAe,SAASA,QAAQA,CAACC,KAAK,EAAEC,MAAM,EAAE;EAC9C,MAAMC,MAAM,GAAG,IAAI;EACnB,MAAM;IACJC,MAAM;IACNC,WAAW;IACXC;EACF,CAAC,GAAGH,MAAM;EACV,IAAII,iBAAiB,GAAGF,WAAW;EACnC,IAAID,MAAM,CAACI,IAAI,EAAE;IACfD,iBAAiB,IAAIJ,MAAM,CAACM,YAAY;IACxCN,MAAM,CAACO,WAAW,EAAE;IACpBP,MAAM,CAACQ,YAAY,EAAE;EACvB;EACA,MAAMC,UAAU,GAAGT,MAAM,CAACD,MAAM,CAACW,MAAM;EACvC,IAAIZ,KAAK,IAAI,CAAC,EAAE;IACdE,MAAM,CAACW,YAAY,CAACZ,MAAM,CAAC;IAC3B;EACF;EACA,IAAID,KAAK,IAAIW,UAAU,EAAE;IACvBT,MAAM,CAACY,WAAW,CAACb,MAAM,CAAC;IAC1B;EACF;EACA,IAAIc,cAAc,GAAGT,iBAAiB,GAAGN,KAAK,GAAGM,iBAAiB,GAAG,CAAC,GAAGA,iBAAiB;EAC1F,MAAMU,YAAY,GAAG,EAAE;EACvB,KAAK,IAAIC,CAAC,GAAGN,UAAU,GAAG,CAAC,EAAEM,CAAC,IAAIjB,KAAK,EAAEiB,CAAC,IAAI,CAAC,EAAE;IAC/C,MAAMC,YAAY,GAAGhB,MAAM,CAACD,MAAM,CAACgB,CAAC,CAAC;IACrCC,YAAY,CAACC,MAAM,EAAE;IACrBH,YAAY,CAACI,OAAO,CAACF,YAAY,CAAC;EACpC;EACA,IAAI,OAAOjB,MAAM,KAAK,QAAQ,IAAI,QAAQ,IAAIA,MAAM,EAAE;IACpD,KAAK,IAAIgB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGhB,MAAM,CAACW,MAAM,EAAEK,CAAC,IAAI,CAAC,EAAE;MACzC,IAAIhB,MAAM,CAACgB,CAAC,CAAC,EAAEZ,QAAQ,CAACgB,MAAM,CAACpB,MAAM,CAACgB,CAAC,CAAC,CAAC;IAC3C;IACAF,cAAc,GAAGT,iBAAiB,GAAGN,KAAK,GAAGM,iBAAiB,GAAGL,MAAM,CAACW,MAAM,GAAGN,iBAAiB;EACpG,CAAC,MAAM;IACLD,QAAQ,CAACgB,MAAM,CAACpB,MAAM,CAAC;EACzB;EACA,KAAK,IAAIgB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,YAAY,CAACJ,MAAM,EAAEK,CAAC,IAAI,CAAC,EAAE;IAC/CZ,QAAQ,CAACgB,MAAM,CAACL,YAAY,CAACC,CAAC,CAAC,CAAC;EAClC;EACAf,MAAM,CAACQ,YAAY,EAAE;EACrB,IAAIP,MAAM,CAACI,IAAI,EAAE;IACfL,MAAM,CAACoB,UAAU,EAAE;EACrB;EACA,IAAI,CAACnB,MAAM,CAACoB,QAAQ,IAAIrB,MAAM,CAACsB,SAAS,EAAE;IACxCtB,MAAM,CAACuB,MAAM,EAAE;EACjB;EACA,IAAItB,MAAM,CAACI,IAAI,EAAE;IACfL,MAAM,CAACwB,OAAO,CAACX,cAAc,GAAGb,MAAM,CAACM,YAAY,EAAE,CAAC,EAAE,KAAK,CAAC;EAChE,CAAC,MAAM;IACLN,MAAM,CAACwB,OAAO,CAACX,cAAc,EAAE,CAAC,EAAE,KAAK,CAAC;EAC1C;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}