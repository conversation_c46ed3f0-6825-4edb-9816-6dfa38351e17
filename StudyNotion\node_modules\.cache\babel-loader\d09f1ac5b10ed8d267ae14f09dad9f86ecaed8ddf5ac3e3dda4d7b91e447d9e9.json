{"ast": null, "code": "import { elementChildren, nextTick } from '../../shared/utils.js';\nexport default function slideToClickedSlide() {\n  const swiper = this;\n  const {\n    params,\n    slidesEl\n  } = swiper;\n  const slidesPerView = params.slidesPerView === 'auto' ? swiper.slidesPerViewDynamic() : params.slidesPerView;\n  let slideToIndex = swiper.clickedIndex;\n  let realIndex;\n  const slideSelector = swiper.isElement ? `swiper-slide` : `.${params.slideClass}`;\n  if (params.loop) {\n    if (swiper.animating) return;\n    realIndex = parseInt(swiper.clickedSlide.getAttribute('data-swiper-slide-index'), 10);\n    if (params.centeredSlides) {\n      if (slideToIndex < swiper.loopedSlides - slidesPerView / 2 || slideToIndex > swiper.slides.length - swiper.loopedSlides + slidesPerView / 2) {\n        swiper.loopFix();\n        slideToIndex = swiper.getSlideIndex(elementChildren(slidesEl, `${slideSelector}[data-swiper-slide-index=\"${realIndex}\"]`)[0]);\n        nextTick(() => {\n          swiper.slideTo(slideToIndex);\n        });\n      } else {\n        swiper.slideTo(slideToIndex);\n      }\n    } else if (slideToIndex > swiper.slides.length - slidesPerView) {\n      swiper.loopFix();\n      slideToIndex = swiper.getSlideIndex(elementChildren(slidesEl, `${slideSelector}[data-swiper-slide-index=\"${realIndex}\"]`)[0]);\n      nextTick(() => {\n        swiper.slideTo(slideToIndex);\n      });\n    } else {\n      swiper.slideTo(slideToIndex);\n    }\n  } else {\n    swiper.slideTo(slideToIndex);\n  }\n}", "map": {"version": 3, "names": ["elementChildren", "nextTick", "slideToClickedSlide", "swiper", "params", "slidesEl", "<PERSON><PERSON><PERSON><PERSON>iew", "slidesPerViewDynamic", "slideToIndex", "clickedIndex", "realIndex", "slideSelector", "isElement", "slideClass", "loop", "animating", "parseInt", "clickedSlide", "getAttribute", "centeredSlides", "loopedSlides", "slides", "length", "loopFix", "getSlideIndex", "slideTo"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/core/slide/slideToClickedSlide.js"], "sourcesContent": ["import { elementChildren, nextTick } from '../../shared/utils.js';\nexport default function slideToClickedSlide() {\n  const swiper = this;\n  const {\n    params,\n    slidesEl\n  } = swiper;\n  const slidesPerView = params.slidesPerView === 'auto' ? swiper.slidesPerViewDynamic() : params.slidesPerView;\n  let slideToIndex = swiper.clickedIndex;\n  let realIndex;\n  const slideSelector = swiper.isElement ? `swiper-slide` : `.${params.slideClass}`;\n  if (params.loop) {\n    if (swiper.animating) return;\n    realIndex = parseInt(swiper.clickedSlide.getAttribute('data-swiper-slide-index'), 10);\n    if (params.centeredSlides) {\n      if (slideToIndex < swiper.loopedSlides - slidesPerView / 2 || slideToIndex > swiper.slides.length - swiper.loopedSlides + slidesPerView / 2) {\n        swiper.loopFix();\n        slideToIndex = swiper.getSlideIndex(elementChildren(slidesEl, `${slideSelector}[data-swiper-slide-index=\"${realIndex}\"]`)[0]);\n        nextTick(() => {\n          swiper.slideTo(slideToIndex);\n        });\n      } else {\n        swiper.slideTo(slideToIndex);\n      }\n    } else if (slideToIndex > swiper.slides.length - slidesPerView) {\n      swiper.loopFix();\n      slideToIndex = swiper.getSlideIndex(elementChildren(slidesEl, `${slideSelector}[data-swiper-slide-index=\"${realIndex}\"]`)[0]);\n      nextTick(() => {\n        swiper.slideTo(slideToIndex);\n      });\n    } else {\n      swiper.slideTo(slideToIndex);\n    }\n  } else {\n    swiper.slideTo(slideToIndex);\n  }\n}"], "mappings": "AAAA,SAASA,eAAe,EAAEC,QAAQ,QAAQ,uBAAuB;AACjE,eAAe,SAASC,mBAAmBA,CAAA,EAAG;EAC5C,MAAMC,MAAM,GAAG,IAAI;EACnB,MAAM;IACJC,MAAM;IACNC;EACF,CAAC,GAAGF,MAAM;EACV,MAAMG,aAAa,GAAGF,MAAM,CAACE,aAAa,KAAK,MAAM,GAAGH,MAAM,CAACI,oBAAoB,EAAE,GAAGH,MAAM,CAACE,aAAa;EAC5G,IAAIE,YAAY,GAAGL,MAAM,CAACM,YAAY;EACtC,IAAIC,SAAS;EACb,MAAMC,aAAa,GAAGR,MAAM,CAACS,SAAS,GAAI,cAAa,GAAI,IAAGR,MAAM,CAACS,UAAW,EAAC;EACjF,IAAIT,MAAM,CAACU,IAAI,EAAE;IACf,IAAIX,MAAM,CAACY,SAAS,EAAE;IACtBL,SAAS,GAAGM,QAAQ,CAACb,MAAM,CAACc,YAAY,CAACC,YAAY,CAAC,yBAAyB,CAAC,EAAE,EAAE,CAAC;IACrF,IAAId,MAAM,CAACe,cAAc,EAAE;MACzB,IAAIX,YAAY,GAAGL,MAAM,CAACiB,YAAY,GAAGd,aAAa,GAAG,CAAC,IAAIE,YAAY,GAAGL,MAAM,CAACkB,MAAM,CAACC,MAAM,GAAGnB,MAAM,CAACiB,YAAY,GAAGd,aAAa,GAAG,CAAC,EAAE;QAC3IH,MAAM,CAACoB,OAAO,EAAE;QAChBf,YAAY,GAAGL,MAAM,CAACqB,aAAa,CAACxB,eAAe,CAACK,QAAQ,EAAG,GAAEM,aAAc,6BAA4BD,SAAU,IAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7HT,QAAQ,CAAC,MAAM;UACbE,MAAM,CAACsB,OAAO,CAACjB,YAAY,CAAC;QAC9B,CAAC,CAAC;MACJ,CAAC,MAAM;QACLL,MAAM,CAACsB,OAAO,CAACjB,YAAY,CAAC;MAC9B;IACF,CAAC,MAAM,IAAIA,YAAY,GAAGL,MAAM,CAACkB,MAAM,CAACC,MAAM,GAAGhB,aAAa,EAAE;MAC9DH,MAAM,CAACoB,OAAO,EAAE;MAChBf,YAAY,GAAGL,MAAM,CAACqB,aAAa,CAACxB,eAAe,CAACK,QAAQ,EAAG,GAAEM,aAAc,6BAA4BD,SAAU,IAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7HT,QAAQ,CAAC,MAAM;QACbE,MAAM,CAACsB,OAAO,CAACjB,YAAY,CAAC;MAC9B,CAAC,CAAC;IACJ,CAAC,MAAM;MACLL,MAAM,CAACsB,OAAO,CAACjB,YAAY,CAAC;IAC9B;EACF,CAAC,MAAM;IACLL,MAAM,CAACsB,OAAO,CAACjB,YAAY,CAAC;EAC9B;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}