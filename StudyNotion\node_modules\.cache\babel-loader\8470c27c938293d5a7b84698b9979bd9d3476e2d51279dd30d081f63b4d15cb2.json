{"ast": null, "code": "import createShadow from '../../shared/create-shadow.js';\nimport effectInit from '../../shared/effect-init.js';\nimport effectTarget from '../../shared/effect-target.js';\nimport effectVirtualTransitionEnd from '../../shared/effect-virtual-transition-end.js';\nimport { getSlideTransformEl } from '../../shared/utils.js';\nexport default function EffectCreative(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on\n  } = _ref;\n  extendParams({\n    creativeEffect: {\n      limitProgress: 1,\n      shadowPerProgress: false,\n      progressMultiplier: 1,\n      perspective: true,\n      prev: {\n        translate: [0, 0, 0],\n        rotate: [0, 0, 0],\n        opacity: 1,\n        scale: 1\n      },\n      next: {\n        translate: [0, 0, 0],\n        rotate: [0, 0, 0],\n        opacity: 1,\n        scale: 1\n      }\n    }\n  });\n  const getTranslateValue = value => {\n    if (typeof value === 'string') return value;\n    return `${value}px`;\n  };\n  const setTranslate = () => {\n    const {\n      slides,\n      wrapperEl,\n      slidesSizesGrid\n    } = swiper;\n    const params = swiper.params.creativeEffect;\n    const {\n      progressMultiplier: multiplier\n    } = params;\n    const isCenteredSlides = swiper.params.centeredSlides;\n    if (isCenteredSlides) {\n      const margin = slidesSizesGrid[0] / 2 - swiper.params.slidesOffsetBefore || 0;\n      wrapperEl.style.transform = `translateX(calc(50% - ${margin}px))`;\n    }\n    for (let i = 0; i < slides.length; i += 1) {\n      const slideEl = slides[i];\n      const slideProgress = slideEl.progress;\n      const progress = Math.min(Math.max(slideEl.progress, -params.limitProgress), params.limitProgress);\n      let originalProgress = progress;\n      if (!isCenteredSlides) {\n        originalProgress = Math.min(Math.max(slideEl.originalProgress, -params.limitProgress), params.limitProgress);\n      }\n      const offset = slideEl.swiperSlideOffset;\n      const t = [swiper.params.cssMode ? -offset - swiper.translate : -offset, 0, 0];\n      const r = [0, 0, 0];\n      let custom = false;\n      if (!swiper.isHorizontal()) {\n        t[1] = t[0];\n        t[0] = 0;\n      }\n      let data = {\n        translate: [0, 0, 0],\n        rotate: [0, 0, 0],\n        scale: 1,\n        opacity: 1\n      };\n      if (progress < 0) {\n        data = params.next;\n        custom = true;\n      } else if (progress > 0) {\n        data = params.prev;\n        custom = true;\n      }\n      // set translate\n      t.forEach((value, index) => {\n        t[index] = `calc(${value}px + (${getTranslateValue(data.translate[index])} * ${Math.abs(progress * multiplier)}))`;\n      });\n      // set rotates\n      r.forEach((value, index) => {\n        r[index] = data.rotate[index] * Math.abs(progress * multiplier);\n      });\n      slideEl.style.zIndex = -Math.abs(Math.round(slideProgress)) + slides.length;\n      const translateString = t.join(', ');\n      const rotateString = `rotateX(${r[0]}deg) rotateY(${r[1]}deg) rotateZ(${r[2]}deg)`;\n      const scaleString = originalProgress < 0 ? `scale(${1 + (1 - data.scale) * originalProgress * multiplier})` : `scale(${1 - (1 - data.scale) * originalProgress * multiplier})`;\n      const opacityString = originalProgress < 0 ? 1 + (1 - data.opacity) * originalProgress * multiplier : 1 - (1 - data.opacity) * originalProgress * multiplier;\n      const transform = `translate3d(${translateString}) ${rotateString} ${scaleString}`;\n\n      // Set shadows\n      if (custom && data.shadow || !custom) {\n        let shadowEl = slideEl.querySelector('.swiper-slide-shadow');\n        if (!shadowEl && data.shadow) {\n          shadowEl = createShadow(params, slideEl);\n        }\n        if (shadowEl) {\n          const shadowOpacity = params.shadowPerProgress ? progress * (1 / params.limitProgress) : progress;\n          shadowEl.style.opacity = Math.min(Math.max(Math.abs(shadowOpacity), 0), 1);\n        }\n      }\n      const targetEl = effectTarget(params, slideEl);\n      targetEl.style.transform = transform;\n      targetEl.style.opacity = opacityString;\n      if (data.origin) {\n        targetEl.style.transformOrigin = data.origin;\n      }\n    }\n  };\n  const setTransition = duration => {\n    const transformElements = swiper.slides.map(slideEl => getSlideTransformEl(slideEl));\n    transformElements.forEach(el => {\n      el.style.transitionDuration = `${duration}ms`;\n      el.querySelectorAll('.swiper-slide-shadow').forEach(shadowEl => {\n        shadowEl.style.transitionDuration = `${duration}ms`;\n      });\n    });\n    effectVirtualTransitionEnd({\n      swiper,\n      duration,\n      transformElements,\n      allSlides: true\n    });\n  };\n  effectInit({\n    effect: 'creative',\n    swiper,\n    on,\n    setTranslate,\n    setTransition,\n    perspective: () => swiper.params.creativeEffect.perspective,\n    overwriteParams: () => ({\n      watchSlidesProgress: true,\n      virtualTranslate: !swiper.params.cssMode\n    })\n  });\n}", "map": {"version": 3, "names": ["createShadow", "effectInit", "effect<PERSON>arget", "effectVirtualTransitionEnd", "getSlideTransformEl", "EffectCreative", "_ref", "swiper", "extendParams", "on", "creativeEffect", "limitProgress", "shadowPerProgress", "progressMultiplier", "perspective", "prev", "translate", "rotate", "opacity", "scale", "next", "getTranslateValue", "value", "setTranslate", "slides", "wrapperEl", "slidesSizesGrid", "params", "multiplier", "isCenteredSlides", "centeredSlides", "margin", "slidesOffsetBefore", "style", "transform", "i", "length", "slideEl", "slideProgress", "progress", "Math", "min", "max", "originalProgress", "offset", "swiperSlideOffset", "t", "cssMode", "r", "custom", "isHorizontal", "data", "for<PERSON>ach", "index", "abs", "zIndex", "round", "translateString", "join", "rotateString", "scaleString", "opacityString", "shadow", "shadowEl", "querySelector", "shadowOpacity", "targetEl", "origin", "transform<PERSON><PERSON>in", "setTransition", "duration", "transformElements", "map", "el", "transitionDuration", "querySelectorAll", "allSlides", "effect", "overwriteParams", "watchSlidesProgress", "virtualTranslate"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/modules/effect-creative/effect-creative.js"], "sourcesContent": ["import createShadow from '../../shared/create-shadow.js';\nimport effectInit from '../../shared/effect-init.js';\nimport effectTarget from '../../shared/effect-target.js';\nimport effectVirtualTransitionEnd from '../../shared/effect-virtual-transition-end.js';\nimport { getSlideTransformEl } from '../../shared/utils.js';\nexport default function EffectCreative({\n  swiper,\n  extendParams,\n  on\n}) {\n  extendParams({\n    creativeEffect: {\n      limitProgress: 1,\n      shadowPerProgress: false,\n      progressMultiplier: 1,\n      perspective: true,\n      prev: {\n        translate: [0, 0, 0],\n        rotate: [0, 0, 0],\n        opacity: 1,\n        scale: 1\n      },\n      next: {\n        translate: [0, 0, 0],\n        rotate: [0, 0, 0],\n        opacity: 1,\n        scale: 1\n      }\n    }\n  });\n  const getTranslateValue = value => {\n    if (typeof value === 'string') return value;\n    return `${value}px`;\n  };\n  const setTranslate = () => {\n    const {\n      slides,\n      wrapperEl,\n      slidesSizesGrid\n    } = swiper;\n    const params = swiper.params.creativeEffect;\n    const {\n      progressMultiplier: multiplier\n    } = params;\n    const isCenteredSlides = swiper.params.centeredSlides;\n    if (isCenteredSlides) {\n      const margin = slidesSizesGrid[0] / 2 - swiper.params.slidesOffsetBefore || 0;\n      wrapperEl.style.transform = `translateX(calc(50% - ${margin}px))`;\n    }\n    for (let i = 0; i < slides.length; i += 1) {\n      const slideEl = slides[i];\n      const slideProgress = slideEl.progress;\n      const progress = Math.min(Math.max(slideEl.progress, -params.limitProgress), params.limitProgress);\n      let originalProgress = progress;\n      if (!isCenteredSlides) {\n        originalProgress = Math.min(Math.max(slideEl.originalProgress, -params.limitProgress), params.limitProgress);\n      }\n      const offset = slideEl.swiperSlideOffset;\n      const t = [swiper.params.cssMode ? -offset - swiper.translate : -offset, 0, 0];\n      const r = [0, 0, 0];\n      let custom = false;\n      if (!swiper.isHorizontal()) {\n        t[1] = t[0];\n        t[0] = 0;\n      }\n      let data = {\n        translate: [0, 0, 0],\n        rotate: [0, 0, 0],\n        scale: 1,\n        opacity: 1\n      };\n      if (progress < 0) {\n        data = params.next;\n        custom = true;\n      } else if (progress > 0) {\n        data = params.prev;\n        custom = true;\n      }\n      // set translate\n      t.forEach((value, index) => {\n        t[index] = `calc(${value}px + (${getTranslateValue(data.translate[index])} * ${Math.abs(progress * multiplier)}))`;\n      });\n      // set rotates\n      r.forEach((value, index) => {\n        r[index] = data.rotate[index] * Math.abs(progress * multiplier);\n      });\n      slideEl.style.zIndex = -Math.abs(Math.round(slideProgress)) + slides.length;\n      const translateString = t.join(', ');\n      const rotateString = `rotateX(${r[0]}deg) rotateY(${r[1]}deg) rotateZ(${r[2]}deg)`;\n      const scaleString = originalProgress < 0 ? `scale(${1 + (1 - data.scale) * originalProgress * multiplier})` : `scale(${1 - (1 - data.scale) * originalProgress * multiplier})`;\n      const opacityString = originalProgress < 0 ? 1 + (1 - data.opacity) * originalProgress * multiplier : 1 - (1 - data.opacity) * originalProgress * multiplier;\n      const transform = `translate3d(${translateString}) ${rotateString} ${scaleString}`;\n\n      // Set shadows\n      if (custom && data.shadow || !custom) {\n        let shadowEl = slideEl.querySelector('.swiper-slide-shadow');\n        if (!shadowEl && data.shadow) {\n          shadowEl = createShadow(params, slideEl);\n        }\n        if (shadowEl) {\n          const shadowOpacity = params.shadowPerProgress ? progress * (1 / params.limitProgress) : progress;\n          shadowEl.style.opacity = Math.min(Math.max(Math.abs(shadowOpacity), 0), 1);\n        }\n      }\n      const targetEl = effectTarget(params, slideEl);\n      targetEl.style.transform = transform;\n      targetEl.style.opacity = opacityString;\n      if (data.origin) {\n        targetEl.style.transformOrigin = data.origin;\n      }\n    }\n  };\n  const setTransition = duration => {\n    const transformElements = swiper.slides.map(slideEl => getSlideTransformEl(slideEl));\n    transformElements.forEach(el => {\n      el.style.transitionDuration = `${duration}ms`;\n      el.querySelectorAll('.swiper-slide-shadow').forEach(shadowEl => {\n        shadowEl.style.transitionDuration = `${duration}ms`;\n      });\n    });\n    effectVirtualTransitionEnd({\n      swiper,\n      duration,\n      transformElements,\n      allSlides: true\n    });\n  };\n  effectInit({\n    effect: 'creative',\n    swiper,\n    on,\n    setTranslate,\n    setTransition,\n    perspective: () => swiper.params.creativeEffect.perspective,\n    overwriteParams: () => ({\n      watchSlidesProgress: true,\n      virtualTranslate: !swiper.params.cssMode\n    })\n  });\n}"], "mappings": "AAAA,OAAOA,YAAY,MAAM,+BAA+B;AACxD,OAAOC,UAAU,MAAM,6BAA6B;AACpD,OAAOC,YAAY,MAAM,+BAA+B;AACxD,OAAOC,0BAA0B,MAAM,+CAA+C;AACtF,SAASC,mBAAmB,QAAQ,uBAAuB;AAC3D,eAAe,SAASC,cAAcA,CAAAC,IAAA,EAInC;EAAA,IAJoC;IACrCC,MAAM;IACNC,YAAY;IACZC;EACF,CAAC,GAAAH,IAAA;EACCE,YAAY,CAAC;IACXE,cAAc,EAAE;MACdC,aAAa,EAAE,CAAC;MAChBC,iBAAiB,EAAE,KAAK;MACxBC,kBAAkB,EAAE,CAAC;MACrBC,WAAW,EAAE,IAAI;MACjBC,IAAI,EAAE;QACJC,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACpBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACjBC,OAAO,EAAE,CAAC;QACVC,KAAK,EAAE;MACT,CAAC;MACDC,IAAI,EAAE;QACJJ,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACpBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACjBC,OAAO,EAAE,CAAC;QACVC,KAAK,EAAE;MACT;IACF;EACF,CAAC,CAAC;EACF,MAAME,iBAAiB,GAAGC,KAAK,IAAI;IACjC,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE,OAAOA,KAAK;IAC3C,OAAQ,GAAEA,KAAM,IAAG;EACrB,CAAC;EACD,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAM;MACJC,MAAM;MACNC,SAAS;MACTC;IACF,CAAC,GAAGnB,MAAM;IACV,MAAMoB,MAAM,GAAGpB,MAAM,CAACoB,MAAM,CAACjB,cAAc;IAC3C,MAAM;MACJG,kBAAkB,EAAEe;IACtB,CAAC,GAAGD,MAAM;IACV,MAAME,gBAAgB,GAAGtB,MAAM,CAACoB,MAAM,CAACG,cAAc;IACrD,IAAID,gBAAgB,EAAE;MACpB,MAAME,MAAM,GAAGL,eAAe,CAAC,CAAC,CAAC,GAAG,CAAC,GAAGnB,MAAM,CAACoB,MAAM,CAACK,kBAAkB,IAAI,CAAC;MAC7EP,SAAS,CAACQ,KAAK,CAACC,SAAS,GAAI,yBAAwBH,MAAO,MAAK;IACnE;IACA,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,MAAM,CAACY,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;MACzC,MAAME,OAAO,GAAGb,MAAM,CAACW,CAAC,CAAC;MACzB,MAAMG,aAAa,GAAGD,OAAO,CAACE,QAAQ;MACtC,MAAMA,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACL,OAAO,CAACE,QAAQ,EAAE,CAACZ,MAAM,CAAChB,aAAa,CAAC,EAAEgB,MAAM,CAAChB,aAAa,CAAC;MAClG,IAAIgC,gBAAgB,GAAGJ,QAAQ;MAC/B,IAAI,CAACV,gBAAgB,EAAE;QACrBc,gBAAgB,GAAGH,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACL,OAAO,CAACM,gBAAgB,EAAE,CAAChB,MAAM,CAAChB,aAAa,CAAC,EAAEgB,MAAM,CAAChB,aAAa,CAAC;MAC9G;MACA,MAAMiC,MAAM,GAAGP,OAAO,CAACQ,iBAAiB;MACxC,MAAMC,CAAC,GAAG,CAACvC,MAAM,CAACoB,MAAM,CAACoB,OAAO,GAAG,CAACH,MAAM,GAAGrC,MAAM,CAACS,SAAS,GAAG,CAAC4B,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;MAC9E,MAAMI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACnB,IAAIC,MAAM,GAAG,KAAK;MAClB,IAAI,CAAC1C,MAAM,CAAC2C,YAAY,EAAE,EAAE;QAC1BJ,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC;QACXA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;MACV;MACA,IAAIK,IAAI,GAAG;QACTnC,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACpBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACjBE,KAAK,EAAE,CAAC;QACRD,OAAO,EAAE;MACX,CAAC;MACD,IAAIqB,QAAQ,GAAG,CAAC,EAAE;QAChBY,IAAI,GAAGxB,MAAM,CAACP,IAAI;QAClB6B,MAAM,GAAG,IAAI;MACf,CAAC,MAAM,IAAIV,QAAQ,GAAG,CAAC,EAAE;QACvBY,IAAI,GAAGxB,MAAM,CAACZ,IAAI;QAClBkC,MAAM,GAAG,IAAI;MACf;MACA;MACAH,CAAC,CAACM,OAAO,CAAC,CAAC9B,KAAK,EAAE+B,KAAK,KAAK;QAC1BP,CAAC,CAACO,KAAK,CAAC,GAAI,QAAO/B,KAAM,SAAQD,iBAAiB,CAAC8B,IAAI,CAACnC,SAAS,CAACqC,KAAK,CAAC,CAAE,MAAKb,IAAI,CAACc,GAAG,CAACf,QAAQ,GAAGX,UAAU,CAAE,IAAG;MACpH,CAAC,CAAC;MACF;MACAoB,CAAC,CAACI,OAAO,CAAC,CAAC9B,KAAK,EAAE+B,KAAK,KAAK;QAC1BL,CAAC,CAACK,KAAK,CAAC,GAAGF,IAAI,CAAClC,MAAM,CAACoC,KAAK,CAAC,GAAGb,IAAI,CAACc,GAAG,CAACf,QAAQ,GAAGX,UAAU,CAAC;MACjE,CAAC,CAAC;MACFS,OAAO,CAACJ,KAAK,CAACsB,MAAM,GAAG,CAACf,IAAI,CAACc,GAAG,CAACd,IAAI,CAACgB,KAAK,CAAClB,aAAa,CAAC,CAAC,GAAGd,MAAM,CAACY,MAAM;MAC3E,MAAMqB,eAAe,GAAGX,CAAC,CAACY,IAAI,CAAC,IAAI,CAAC;MACpC,MAAMC,YAAY,GAAI,WAAUX,CAAC,CAAC,CAAC,CAAE,gBAAeA,CAAC,CAAC,CAAC,CAAE,gBAAeA,CAAC,CAAC,CAAC,CAAE,MAAK;MAClF,MAAMY,WAAW,GAAGjB,gBAAgB,GAAG,CAAC,GAAI,SAAQ,CAAC,GAAG,CAAC,CAAC,GAAGQ,IAAI,CAAChC,KAAK,IAAIwB,gBAAgB,GAAGf,UAAW,GAAE,GAAI,SAAQ,CAAC,GAAG,CAAC,CAAC,GAAGuB,IAAI,CAAChC,KAAK,IAAIwB,gBAAgB,GAAGf,UAAW,GAAE;MAC9K,MAAMiC,aAAa,GAAGlB,gBAAgB,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAGQ,IAAI,CAACjC,OAAO,IAAIyB,gBAAgB,GAAGf,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC,GAAGuB,IAAI,CAACjC,OAAO,IAAIyB,gBAAgB,GAAGf,UAAU;MAC5J,MAAMM,SAAS,GAAI,eAAcuB,eAAgB,KAAIE,YAAa,IAAGC,WAAY,EAAC;;MAElF;MACA,IAAIX,MAAM,IAAIE,IAAI,CAACW,MAAM,IAAI,CAACb,MAAM,EAAE;QACpC,IAAIc,QAAQ,GAAG1B,OAAO,CAAC2B,aAAa,CAAC,sBAAsB,CAAC;QAC5D,IAAI,CAACD,QAAQ,IAAIZ,IAAI,CAACW,MAAM,EAAE;UAC5BC,QAAQ,GAAG/D,YAAY,CAAC2B,MAAM,EAAEU,OAAO,CAAC;QAC1C;QACA,IAAI0B,QAAQ,EAAE;UACZ,MAAME,aAAa,GAAGtC,MAAM,CAACf,iBAAiB,GAAG2B,QAAQ,IAAI,CAAC,GAAGZ,MAAM,CAAChB,aAAa,CAAC,GAAG4B,QAAQ;UACjGwB,QAAQ,CAAC9B,KAAK,CAACf,OAAO,GAAGsB,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACF,IAAI,CAACc,GAAG,CAACW,aAAa,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;QAC5E;MACF;MACA,MAAMC,QAAQ,GAAGhE,YAAY,CAACyB,MAAM,EAAEU,OAAO,CAAC;MAC9C6B,QAAQ,CAACjC,KAAK,CAACC,SAAS,GAAGA,SAAS;MACpCgC,QAAQ,CAACjC,KAAK,CAACf,OAAO,GAAG2C,aAAa;MACtC,IAAIV,IAAI,CAACgB,MAAM,EAAE;QACfD,QAAQ,CAACjC,KAAK,CAACmC,eAAe,GAAGjB,IAAI,CAACgB,MAAM;MAC9C;IACF;EACF,CAAC;EACD,MAAME,aAAa,GAAGC,QAAQ,IAAI;IAChC,MAAMC,iBAAiB,GAAGhE,MAAM,CAACiB,MAAM,CAACgD,GAAG,CAACnC,OAAO,IAAIjC,mBAAmB,CAACiC,OAAO,CAAC,CAAC;IACpFkC,iBAAiB,CAACnB,OAAO,CAACqB,EAAE,IAAI;MAC9BA,EAAE,CAACxC,KAAK,CAACyC,kBAAkB,GAAI,GAAEJ,QAAS,IAAG;MAC7CG,EAAE,CAACE,gBAAgB,CAAC,sBAAsB,CAAC,CAACvB,OAAO,CAACW,QAAQ,IAAI;QAC9DA,QAAQ,CAAC9B,KAAK,CAACyC,kBAAkB,GAAI,GAAEJ,QAAS,IAAG;MACrD,CAAC,CAAC;IACJ,CAAC,CAAC;IACFnE,0BAA0B,CAAC;MACzBI,MAAM;MACN+D,QAAQ;MACRC,iBAAiB;MACjBK,SAAS,EAAE;IACb,CAAC,CAAC;EACJ,CAAC;EACD3E,UAAU,CAAC;IACT4E,MAAM,EAAE,UAAU;IAClBtE,MAAM;IACNE,EAAE;IACFc,YAAY;IACZ8C,aAAa;IACbvD,WAAW,EAAEA,CAAA,KAAMP,MAAM,CAACoB,MAAM,CAACjB,cAAc,CAACI,WAAW;IAC3DgE,eAAe,EAAEA,CAAA,MAAO;MACtBC,mBAAmB,EAAE,IAAI;MACzBC,gBAAgB,EAAE,CAACzE,MAAM,CAACoB,MAAM,CAACoB;IACnC,CAAC;EACH,CAAC,CAAC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}