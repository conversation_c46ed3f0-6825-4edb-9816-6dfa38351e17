{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nvar _slicedToArray = function () {\n  function sliceIterator(arr, i) {\n    var _arr = [];\n    var _n = true;\n    var _d = false;\n    var _e = undefined;\n    try {\n      for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) {\n        _arr.push(_s.value);\n        if (i && _arr.length === i) break;\n      }\n    } catch (err) {\n      _d = true;\n      _e = err;\n    } finally {\n      try {\n        if (!_n && _i[\"return\"]) _i[\"return\"]();\n      } finally {\n        if (_d) throw _e;\n      }\n    }\n    return _arr;\n  }\n  return function (arr, i) {\n    if (Array.isArray(arr)) {\n      return arr;\n    } else if (Symbol.iterator in Object(arr)) {\n      return sliceIterator(arr, i);\n    } else {\n      throw new TypeError(\"Invalid attempt to destructure non-iterable instance\");\n    }\n  };\n}();\nexports.default = useConfig;\nvar _react = require('react');\nfunction useConfig(config) {\n  var _useState = (0, _react.useState)(config.count),\n    _useState2 = _slicedToArray(_useState, 2),\n    count = _useState2[0],\n    setCount = _useState2[1];\n  var _useState3 = (0, _react.useState)(config.size),\n    _useState4 = _slicedToArray(_useState3, 2),\n    size = _useState4[0],\n    setSize = _useState4[1];\n  var _useState5 = (0, _react.useState)(config.char),\n    _useState6 = _slicedToArray(_useState5, 2),\n    char = _useState6[0],\n    setChar = _useState6[1];\n  var _useState7 = (0, _react.useState)(config.color),\n    _useState8 = _slicedToArray(_useState7, 2),\n    color = _useState8[0],\n    setColor = _useState8[1];\n  var _useState9 = (0, _react.useState)(config.activeColor),\n    _useState10 = _slicedToArray(_useState9, 2),\n    activeColor = _useState10[0],\n    setActiveColor = _useState10[1];\n  var _useState11 = (0, _react.useState)(config.isHalf),\n    _useState12 = _slicedToArray(_useState11, 2),\n    isHalf = _useState12[0],\n    setIsHalf = _useState12[1];\n  var _useState13 = (0, _react.useState)(config.edit),\n    _useState14 = _slicedToArray(_useState13, 2),\n    edit = _useState14[0],\n    setEdit = _useState14[1];\n  var _useState15 = (0, _react.useState)(config.emptyIcon),\n    _useState16 = _slicedToArray(_useState15, 2),\n    emptyIcon = _useState16[0],\n    setEmptyIcon = _useState16[1];\n  var _useState17 = (0, _react.useState)(config.halfIcon),\n    _useState18 = _slicedToArray(_useState17, 2),\n    halfIcon = _useState18[0],\n    setHalfIcon = _useState18[1];\n  var _useState19 = (0, _react.useState)(config.filledIcon),\n    _useState20 = _slicedToArray(_useState19, 2),\n    filledIcon = _useState20[0],\n    setFilledIcon = _useState20[1];\n  var _useState21 = (0, _react.useState)(config.a11y),\n    _useState22 = _slicedToArray(_useState21, 2),\n    a11y = _useState22[0],\n    setA11y = _useState22[1];\n  var configObj = {\n    count: count,\n    size: size,\n    char: char,\n    color: color,\n    activeColor: activeColor,\n    isHalf: isHalf,\n    edit: edit,\n    emptyIcon: emptyIcon,\n    halfIcon: halfIcon,\n    filledIcon: filledIcon,\n    a11y: a11y\n  };\n  function setConfig(config) {\n    setCount(config.count);\n    setSize(config.size);\n    setChar(config.char);\n    setColor(config.color);\n    setActiveColor(config.activeColor);\n    setIsHalf(config.isHalf);\n    setEdit(config.edit);\n    setEmptyIcon(config.emptyIcon);\n    setHalfIcon(config.halfIcon);\n    setFilledIcon(config.filledIcon);\n    setA11y(config.a11y);\n  }\n  return [configObj, setConfig];\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "_slicedToArray", "sliceIterator", "arr", "i", "_arr", "_n", "_d", "_e", "undefined", "_i", "Symbol", "iterator", "_s", "next", "done", "push", "length", "err", "Array", "isArray", "TypeError", "default", "useConfig", "_react", "require", "config", "_useState", "useState", "count", "_useState2", "setCount", "_useState3", "size", "_useState4", "setSize", "_useState5", "char", "_useState6", "setChar", "_useState7", "color", "_useState8", "setColor", "_useState9", "activeColor", "_useState10", "setActiveColor", "_useState11", "is<PERSON>alf", "_useState12", "setIsHalf", "_useState13", "edit", "_useState14", "setEdit", "_useState15", "emptyIcon", "_useState16", "setEmptyIcon", "_useState17", "halfIcon", "_useState18", "setHalfIcon", "_useState19", "filledIcon", "_useState20", "setFilledIcon", "_useState21", "a11y", "_useState22", "setA11y", "config<PERSON><PERSON><PERSON>", "setConfig"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/react-rating-stars-component/dist/hooks/useConfig.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\n\nvar _slicedToArray = function () { function sliceIterator(arr, i) { var _arr = []; var _n = true; var _d = false; var _e = undefined; try { for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"]) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; } return function (arr, i) { if (Array.isArray(arr)) { return arr; } else if (Symbol.iterator in Object(arr)) { return sliceIterator(arr, i); } else { throw new TypeError(\"Invalid attempt to destructure non-iterable instance\"); } }; }();\n\nexports.default = useConfig;\n\nvar _react = require('react');\n\nfunction useConfig(config) {\n    var _useState = (0, _react.useState)(config.count),\n        _useState2 = _slicedToArray(_useState, 2),\n        count = _useState2[0],\n        setCount = _useState2[1];\n\n    var _useState3 = (0, _react.useState)(config.size),\n        _useState4 = _slicedToArray(_useState3, 2),\n        size = _useState4[0],\n        setSize = _useState4[1];\n\n    var _useState5 = (0, _react.useState)(config.char),\n        _useState6 = _slicedToArray(_useState5, 2),\n        char = _useState6[0],\n        setChar = _useState6[1];\n\n    var _useState7 = (0, _react.useState)(config.color),\n        _useState8 = _slicedToArray(_useState7, 2),\n        color = _useState8[0],\n        setColor = _useState8[1];\n\n    var _useState9 = (0, _react.useState)(config.activeColor),\n        _useState10 = _slicedToArray(_useState9, 2),\n        activeColor = _useState10[0],\n        setActiveColor = _useState10[1];\n\n    var _useState11 = (0, _react.useState)(config.isHalf),\n        _useState12 = _slicedToArray(_useState11, 2),\n        isHalf = _useState12[0],\n        setIsHalf = _useState12[1];\n\n    var _useState13 = (0, _react.useState)(config.edit),\n        _useState14 = _slicedToArray(_useState13, 2),\n        edit = _useState14[0],\n        setEdit = _useState14[1];\n\n    var _useState15 = (0, _react.useState)(config.emptyIcon),\n        _useState16 = _slicedToArray(_useState15, 2),\n        emptyIcon = _useState16[0],\n        setEmptyIcon = _useState16[1];\n\n    var _useState17 = (0, _react.useState)(config.halfIcon),\n        _useState18 = _slicedToArray(_useState17, 2),\n        halfIcon = _useState18[0],\n        setHalfIcon = _useState18[1];\n\n    var _useState19 = (0, _react.useState)(config.filledIcon),\n        _useState20 = _slicedToArray(_useState19, 2),\n        filledIcon = _useState20[0],\n        setFilledIcon = _useState20[1];\n\n    var _useState21 = (0, _react.useState)(config.a11y),\n        _useState22 = _slicedToArray(_useState21, 2),\n        a11y = _useState22[0],\n        setA11y = _useState22[1];\n\n    var configObj = {\n        count: count,\n        size: size,\n        char: char,\n        color: color,\n        activeColor: activeColor,\n        isHalf: isHalf,\n        edit: edit,\n        emptyIcon: emptyIcon,\n        halfIcon: halfIcon,\n        filledIcon: filledIcon,\n        a11y: a11y\n    };\n\n    function setConfig(config) {\n        setCount(config.count);\n        setSize(config.size);\n        setChar(config.char);\n        setColor(config.color);\n        setActiveColor(config.activeColor);\n        setIsHalf(config.isHalf);\n        setEdit(config.edit);\n        setEmptyIcon(config.emptyIcon);\n        setHalfIcon(config.halfIcon);\n        setFilledIcon(config.filledIcon);\n        setA11y(config.a11y);\n    }\n\n    return [configObj, setConfig];\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EACzCC,KAAK,EAAE;AACX,CAAC,CAAC;AAEF,IAAIC,cAAc,GAAG,YAAY;EAAE,SAASC,aAAaA,CAACC,GAAG,EAAEC,CAAC,EAAE;IAAE,IAAIC,IAAI,GAAG,EAAE;IAAE,IAAIC,EAAE,GAAG,IAAI;IAAE,IAAIC,EAAE,GAAG,KAAK;IAAE,IAAIC,EAAE,GAAGC,SAAS;IAAE,IAAI;MAAE,KAAK,IAAIC,EAAE,GAAGP,GAAG,CAACQ,MAAM,CAACC,QAAQ,CAAC,EAAE,EAAEC,EAAE,EAAE,EAAEP,EAAE,GAAG,CAACO,EAAE,GAAGH,EAAE,CAACI,IAAI,EAAE,EAAEC,IAAI,CAAC,EAAET,EAAE,GAAG,IAAI,EAAE;QAAED,IAAI,CAACW,IAAI,CAACH,EAAE,CAACb,KAAK,CAAC;QAAE,IAAII,CAAC,IAAIC,IAAI,CAACY,MAAM,KAAKb,CAAC,EAAE;MAAO;IAAE,CAAC,CAAC,OAAOc,GAAG,EAAE;MAAEX,EAAE,GAAG,IAAI;MAAEC,EAAE,GAAGU,GAAG;IAAE,CAAC,SAAS;MAAE,IAAI;QAAE,IAAI,CAACZ,EAAE,IAAII,EAAE,CAAC,QAAQ,CAAC,EAAEA,EAAE,CAAC,QAAQ,CAAC,EAAE;MAAE,CAAC,SAAS;QAAE,IAAIH,EAAE,EAAE,MAAMC,EAAE;MAAE;IAAE;IAAE,OAAOH,IAAI;EAAE;EAAE,OAAO,UAAUF,GAAG,EAAEC,CAAC,EAAE;IAAE,IAAIe,KAAK,CAACC,OAAO,CAACjB,GAAG,CAAC,EAAE;MAAE,OAAOA,GAAG;IAAE,CAAC,MAAM,IAAIQ,MAAM,CAACC,QAAQ,IAAIf,MAAM,CAACM,GAAG,CAAC,EAAE;MAAE,OAAOD,aAAa,CAACC,GAAG,EAAEC,CAAC,CAAC;IAAE,CAAC,MAAM;MAAE,MAAM,IAAIiB,SAAS,CAAC,sDAAsD,CAAC;IAAE;EAAE,CAAC;AAAE,CAAC,EAAE;AAEvpBtB,OAAO,CAACuB,OAAO,GAAGC,SAAS;AAE3B,IAAIC,MAAM,GAAGC,OAAO,CAAC,OAAO,CAAC;AAE7B,SAASF,SAASA,CAACG,MAAM,EAAE;EACvB,IAAIC,SAAS,GAAG,CAAC,CAAC,EAAEH,MAAM,CAACI,QAAQ,EAAEF,MAAM,CAACG,KAAK,CAAC;IAC9CC,UAAU,GAAG7B,cAAc,CAAC0B,SAAS,EAAE,CAAC,CAAC;IACzCE,KAAK,GAAGC,UAAU,CAAC,CAAC,CAAC;IACrBC,QAAQ,GAAGD,UAAU,CAAC,CAAC,CAAC;EAE5B,IAAIE,UAAU,GAAG,CAAC,CAAC,EAAER,MAAM,CAACI,QAAQ,EAAEF,MAAM,CAACO,IAAI,CAAC;IAC9CC,UAAU,GAAGjC,cAAc,CAAC+B,UAAU,EAAE,CAAC,CAAC;IAC1CC,IAAI,GAAGC,UAAU,CAAC,CAAC,CAAC;IACpBC,OAAO,GAAGD,UAAU,CAAC,CAAC,CAAC;EAE3B,IAAIE,UAAU,GAAG,CAAC,CAAC,EAAEZ,MAAM,CAACI,QAAQ,EAAEF,MAAM,CAACW,IAAI,CAAC;IAC9CC,UAAU,GAAGrC,cAAc,CAACmC,UAAU,EAAE,CAAC,CAAC;IAC1CC,IAAI,GAAGC,UAAU,CAAC,CAAC,CAAC;IACpBC,OAAO,GAAGD,UAAU,CAAC,CAAC,CAAC;EAE3B,IAAIE,UAAU,GAAG,CAAC,CAAC,EAAEhB,MAAM,CAACI,QAAQ,EAAEF,MAAM,CAACe,KAAK,CAAC;IAC/CC,UAAU,GAAGzC,cAAc,CAACuC,UAAU,EAAE,CAAC,CAAC;IAC1CC,KAAK,GAAGC,UAAU,CAAC,CAAC,CAAC;IACrBC,QAAQ,GAAGD,UAAU,CAAC,CAAC,CAAC;EAE5B,IAAIE,UAAU,GAAG,CAAC,CAAC,EAAEpB,MAAM,CAACI,QAAQ,EAAEF,MAAM,CAACmB,WAAW,CAAC;IACrDC,WAAW,GAAG7C,cAAc,CAAC2C,UAAU,EAAE,CAAC,CAAC;IAC3CC,WAAW,GAAGC,WAAW,CAAC,CAAC,CAAC;IAC5BC,cAAc,GAAGD,WAAW,CAAC,CAAC,CAAC;EAEnC,IAAIE,WAAW,GAAG,CAAC,CAAC,EAAExB,MAAM,CAACI,QAAQ,EAAEF,MAAM,CAACuB,MAAM,CAAC;IACjDC,WAAW,GAAGjD,cAAc,CAAC+C,WAAW,EAAE,CAAC,CAAC;IAC5CC,MAAM,GAAGC,WAAW,CAAC,CAAC,CAAC;IACvBC,SAAS,GAAGD,WAAW,CAAC,CAAC,CAAC;EAE9B,IAAIE,WAAW,GAAG,CAAC,CAAC,EAAE5B,MAAM,CAACI,QAAQ,EAAEF,MAAM,CAAC2B,IAAI,CAAC;IAC/CC,WAAW,GAAGrD,cAAc,CAACmD,WAAW,EAAE,CAAC,CAAC;IAC5CC,IAAI,GAAGC,WAAW,CAAC,CAAC,CAAC;IACrBC,OAAO,GAAGD,WAAW,CAAC,CAAC,CAAC;EAE5B,IAAIE,WAAW,GAAG,CAAC,CAAC,EAAEhC,MAAM,CAACI,QAAQ,EAAEF,MAAM,CAAC+B,SAAS,CAAC;IACpDC,WAAW,GAAGzD,cAAc,CAACuD,WAAW,EAAE,CAAC,CAAC;IAC5CC,SAAS,GAAGC,WAAW,CAAC,CAAC,CAAC;IAC1BC,YAAY,GAAGD,WAAW,CAAC,CAAC,CAAC;EAEjC,IAAIE,WAAW,GAAG,CAAC,CAAC,EAAEpC,MAAM,CAACI,QAAQ,EAAEF,MAAM,CAACmC,QAAQ,CAAC;IACnDC,WAAW,GAAG7D,cAAc,CAAC2D,WAAW,EAAE,CAAC,CAAC;IAC5CC,QAAQ,GAAGC,WAAW,CAAC,CAAC,CAAC;IACzBC,WAAW,GAAGD,WAAW,CAAC,CAAC,CAAC;EAEhC,IAAIE,WAAW,GAAG,CAAC,CAAC,EAAExC,MAAM,CAACI,QAAQ,EAAEF,MAAM,CAACuC,UAAU,CAAC;IACrDC,WAAW,GAAGjE,cAAc,CAAC+D,WAAW,EAAE,CAAC,CAAC;IAC5CC,UAAU,GAAGC,WAAW,CAAC,CAAC,CAAC;IAC3BC,aAAa,GAAGD,WAAW,CAAC,CAAC,CAAC;EAElC,IAAIE,WAAW,GAAG,CAAC,CAAC,EAAE5C,MAAM,CAACI,QAAQ,EAAEF,MAAM,CAAC2C,IAAI,CAAC;IAC/CC,WAAW,GAAGrE,cAAc,CAACmE,WAAW,EAAE,CAAC,CAAC;IAC5CC,IAAI,GAAGC,WAAW,CAAC,CAAC,CAAC;IACrBC,OAAO,GAAGD,WAAW,CAAC,CAAC,CAAC;EAE5B,IAAIE,SAAS,GAAG;IACZ3C,KAAK,EAAEA,KAAK;IACZI,IAAI,EAAEA,IAAI;IACVI,IAAI,EAAEA,IAAI;IACVI,KAAK,EAAEA,KAAK;IACZI,WAAW,EAAEA,WAAW;IACxBI,MAAM,EAAEA,MAAM;IACdI,IAAI,EAAEA,IAAI;IACVI,SAAS,EAAEA,SAAS;IACpBI,QAAQ,EAAEA,QAAQ;IAClBI,UAAU,EAAEA,UAAU;IACtBI,IAAI,EAAEA;EACV,CAAC;EAED,SAASI,SAASA,CAAC/C,MAAM,EAAE;IACvBK,QAAQ,CAACL,MAAM,CAACG,KAAK,CAAC;IACtBM,OAAO,CAACT,MAAM,CAACO,IAAI,CAAC;IACpBM,OAAO,CAACb,MAAM,CAACW,IAAI,CAAC;IACpBM,QAAQ,CAACjB,MAAM,CAACe,KAAK,CAAC;IACtBM,cAAc,CAACrB,MAAM,CAACmB,WAAW,CAAC;IAClCM,SAAS,CAACzB,MAAM,CAACuB,MAAM,CAAC;IACxBM,OAAO,CAAC7B,MAAM,CAAC2B,IAAI,CAAC;IACpBM,YAAY,CAACjC,MAAM,CAAC+B,SAAS,CAAC;IAC9BM,WAAW,CAACrC,MAAM,CAACmC,QAAQ,CAAC;IAC5BM,aAAa,CAACzC,MAAM,CAACuC,UAAU,CAAC;IAChCM,OAAO,CAAC7C,MAAM,CAAC2C,IAAI,CAAC;EACxB;EAEA,OAAO,CAACG,SAAS,EAAEC,SAAS,CAAC;AACjC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}