{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _react = _interopRequireDefault(require(\"react\"));\nvar _classnames = _interopRequireDefault(require(\"classnames\"));\nvar propTypes = {\n  poster: _propTypes[\"default\"].string,\n  player: _propTypes[\"default\"].object,\n  actions: _propTypes[\"default\"].object,\n  className: _propTypes[\"default\"].string\n};\nfunction PosterImage(_ref) {\n  var poster = _ref.poster,\n    player = _ref.player,\n    actions = _ref.actions,\n    className = _ref.className;\n  if (!poster || player.hasStarted) {\n    return null;\n  }\n  return _react[\"default\"].createElement(\"div\", {\n    className: (0, _classnames[\"default\"])('video-react-poster', className),\n    style: {\n      backgroundImage: \"url(\\\"\".concat(poster, \"\\\")\")\n    },\n    onClick: function onClick() {\n      if (player.paused) {\n        actions.play();\n      }\n    }\n  });\n}\nPosterImage.propTypes = propTypes;\nPosterImage.displayName = 'PosterImage';\nvar _default = PosterImage;\nexports[\"default\"] = _default;", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "_propTypes", "_react", "_classnames", "propTypes", "poster", "string", "player", "object", "actions", "className", "PosterImage", "_ref", "hasStarted", "createElement", "style", "backgroundImage", "concat", "onClick", "paused", "play", "displayName", "_default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/video-react/lib/components/PosterImage.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\n\nvar _react = _interopRequireDefault(require(\"react\"));\n\nvar _classnames = _interopRequireDefault(require(\"classnames\"));\n\nvar propTypes = {\n  poster: _propTypes[\"default\"].string,\n  player: _propTypes[\"default\"].object,\n  actions: _propTypes[\"default\"].object,\n  className: _propTypes[\"default\"].string\n};\n\nfunction PosterImage(_ref) {\n  var poster = _ref.poster,\n      player = _ref.player,\n      actions = _ref.actions,\n      className = _ref.className;\n\n  if (!poster || player.hasStarted) {\n    return null;\n  }\n\n  return _react[\"default\"].createElement(\"div\", {\n    className: (0, _classnames[\"default\"])('video-react-poster', className),\n    style: {\n      backgroundImage: \"url(\\\"\".concat(poster, \"\\\")\")\n    },\n    onClick: function onClick() {\n      if (player.paused) {\n        actions.play();\n      }\n    }\n  });\n}\n\nPosterImage.propTypes = propTypes;\nPosterImage.displayName = 'PosterImage';\nvar _default = PosterImage;\nexports[\"default\"] = _default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC;AAEpFC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;AAE3B,IAAIE,UAAU,GAAGN,sBAAsB,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;AAE9D,IAAIM,MAAM,GAAGP,sBAAsB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AAErD,IAAIO,WAAW,GAAGR,sBAAsB,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;AAE/D,IAAIQ,SAAS,GAAG;EACdC,MAAM,EAAEJ,UAAU,CAAC,SAAS,CAAC,CAACK,MAAM;EACpCC,MAAM,EAAEN,UAAU,CAAC,SAAS,CAAC,CAACO,MAAM;EACpCC,OAAO,EAAER,UAAU,CAAC,SAAS,CAAC,CAACO,MAAM;EACrCE,SAAS,EAAET,UAAU,CAAC,SAAS,CAAC,CAACK;AACnC,CAAC;AAED,SAASK,WAAWA,CAACC,IAAI,EAAE;EACzB,IAAIP,MAAM,GAAGO,IAAI,CAACP,MAAM;IACpBE,MAAM,GAAGK,IAAI,CAACL,MAAM;IACpBE,OAAO,GAAGG,IAAI,CAACH,OAAO;IACtBC,SAAS,GAAGE,IAAI,CAACF,SAAS;EAE9B,IAAI,CAACL,MAAM,IAAIE,MAAM,CAACM,UAAU,EAAE;IAChC,OAAO,IAAI;EACb;EAEA,OAAOX,MAAM,CAAC,SAAS,CAAC,CAACY,aAAa,CAAC,KAAK,EAAE;IAC5CJ,SAAS,EAAE,CAAC,CAAC,EAAEP,WAAW,CAAC,SAAS,CAAC,EAAE,oBAAoB,EAAEO,SAAS,CAAC;IACvEK,KAAK,EAAE;MACLC,eAAe,EAAE,QAAQ,CAACC,MAAM,CAACZ,MAAM,EAAE,KAAK;IAChD,CAAC;IACDa,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG;MAC1B,IAAIX,MAAM,CAACY,MAAM,EAAE;QACjBV,OAAO,CAACW,IAAI,EAAE;MAChB;IACF;EACF,CAAC,CAAC;AACJ;AAEAT,WAAW,CAACP,SAAS,GAAGA,SAAS;AACjCO,WAAW,CAACU,WAAW,GAAG,aAAa;AACvC,IAAIC,QAAQ,GAAGX,WAAW;AAC1BZ,OAAO,CAAC,SAAS,CAAC,GAAGuB,QAAQ"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}