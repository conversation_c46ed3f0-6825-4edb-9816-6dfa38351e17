{"ast": null, "code": "import { needsNavigation, needsPagination, needsScrollbar } from './utils.js';\nfunction mountSwiper(_ref, swiperParams) {\n  let {\n    el,\n    nextEl,\n    prevEl,\n    paginationEl,\n    scrollbarEl,\n    swiper\n  } = _ref;\n  if (needsNavigation(swiperParams) && nextEl && prevEl) {\n    swiper.params.navigation.nextEl = nextEl;\n    swiper.originalParams.navigation.nextEl = nextEl;\n    swiper.params.navigation.prevEl = prevEl;\n    swiper.originalParams.navigation.prevEl = prevEl;\n  }\n  if (needsPagination(swiperParams) && paginationEl) {\n    swiper.params.pagination.el = paginationEl;\n    swiper.originalParams.pagination.el = paginationEl;\n  }\n  if (needsScrollbar(swiperParams) && scrollbarEl) {\n    swiper.params.scrollbar.el = scrollbarEl;\n    swiper.originalParams.scrollbar.el = scrollbarEl;\n  }\n  swiper.init(el);\n}\nexport { mountSwiper };", "map": {"version": 3, "names": ["needsNavigation", "needsPagination", "needsScrollbar", "mountSwiper", "_ref", "swiperParams", "el", "nextEl", "prevEl", "paginationEl", "scrollbarEl", "swiper", "params", "navigation", "originalParams", "pagination", "scrollbar", "init"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/components-shared/mount-swiper.js"], "sourcesContent": ["import { needsNavigation, needsPagination, needsScrollbar } from './utils.js';\nfunction mountSwiper({\n  el,\n  nextEl,\n  prevEl,\n  paginationEl,\n  scrollbarEl,\n  swiper\n}, swiperParams) {\n  if (needsNavigation(swiperParams) && nextEl && prevEl) {\n    swiper.params.navigation.nextEl = nextEl;\n    swiper.originalParams.navigation.nextEl = nextEl;\n    swiper.params.navigation.prevEl = prevEl;\n    swiper.originalParams.navigation.prevEl = prevEl;\n  }\n  if (needsPagination(swiperParams) && paginationEl) {\n    swiper.params.pagination.el = paginationEl;\n    swiper.originalParams.pagination.el = paginationEl;\n  }\n  if (needsScrollbar(swiperParams) && scrollbarEl) {\n    swiper.params.scrollbar.el = scrollbarEl;\n    swiper.originalParams.scrollbar.el = scrollbarEl;\n  }\n  swiper.init(el);\n}\nexport { mountSwiper };"], "mappings": "AAAA,SAASA,eAAe,EAAEC,eAAe,EAAEC,cAAc,QAAQ,YAAY;AAC7E,SAASC,WAAWA,CAAAC,IAAA,EAOjBC,YAAY,EAAE;EAAA,IAPI;IACnBC,EAAE;IACFC,MAAM;IACNC,MAAM;IACNC,YAAY;IACZC,WAAW;IACXC;EACF,CAAC,GAAAP,IAAA;EACC,IAAIJ,eAAe,CAACK,YAAY,CAAC,IAAIE,MAAM,IAAIC,MAAM,EAAE;IACrDG,MAAM,CAACC,MAAM,CAACC,UAAU,CAACN,MAAM,GAAGA,MAAM;IACxCI,MAAM,CAACG,cAAc,CAACD,UAAU,CAACN,MAAM,GAAGA,MAAM;IAChDI,MAAM,CAACC,MAAM,CAACC,UAAU,CAACL,MAAM,GAAGA,MAAM;IACxCG,MAAM,CAACG,cAAc,CAACD,UAAU,CAACL,MAAM,GAAGA,MAAM;EAClD;EACA,IAAIP,eAAe,CAACI,YAAY,CAAC,IAAII,YAAY,EAAE;IACjDE,MAAM,CAACC,MAAM,CAACG,UAAU,CAACT,EAAE,GAAGG,YAAY;IAC1CE,MAAM,CAACG,cAAc,CAACC,UAAU,CAACT,EAAE,GAAGG,YAAY;EACpD;EACA,IAAIP,cAAc,CAACG,YAAY,CAAC,IAAIK,WAAW,EAAE;IAC/CC,MAAM,CAACC,MAAM,CAACI,SAAS,CAACV,EAAE,GAAGI,WAAW;IACxCC,MAAM,CAACG,cAAc,CAACE,SAAS,CAACV,EAAE,GAAGI,WAAW;EAClD;EACAC,MAAM,CAACM,IAAI,CAACX,EAAE,CAAC;AACjB;AACA,SAASH,WAAW"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}