{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\temp\\\\StudyNotion\\\\src\\\\components\\\\core\\\\Dashboard\\\\SidebarLink.jsx\",\n  _s = $RefreshSig$();\nimport * as Icons from \"react-icons/vsc\";\nimport { useDispatch } from \"react-redux\";\nimport { NavLink, matchPath, useLocation } from \"react-router-dom\";\nimport { resetCourseState } from \"../../../slices/courseSlice\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function SidebarLink(_ref) {\n  _s();\n  let {\n    link,\n    iconName\n  } = _ref;\n  const Icon = Icons[iconName];\n  const location = useLocation();\n  const dispatch = useDispatch();\n  const matchRoute = route => {\n    return matchPath({\n      path: route\n    }, location.pathname);\n  };\n  return /*#__PURE__*/_jsxDEV(NavLink, {\n    to: link.path,\n    onClick: () => dispatch(resetCourseState()),\n    className: `relative px-8 py-2 text-sm font-medium ${matchRoute(link.path) ? \"bg-yellow-800 text-yellow-50\" : \"bg-opacity-0 text-richblack-300\"} transition-all duration-200`,\n    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n      className: `absolute left-0 top-0 h-full w-[0.15rem] bg-yellow-50 ${matchRoute(link.path) ? \"opacity-100\" : \"opacity-0\"}`\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center gap-x-2\",\n      children: [/*#__PURE__*/_jsxDEV(Icon, {\n        className: \"text-lg\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: link.name\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 5\n  }, this);\n}\n_s(SidebarLink, \"MQggm+TQsuvWCz9fxLEdTr94DHM=\", false, function () {\n  return [useLocation, useDispatch];\n});\n_c = SidebarLink;\nvar _c;\n$RefreshReg$(_c, \"SidebarLink\");", "map": {"version": 3, "names": ["Icons", "useDispatch", "NavLink", "matchPath", "useLocation", "resetCourseState", "jsxDEV", "_jsxDEV", "SidebarLink", "_ref", "_s", "link", "iconName", "Icon", "location", "dispatch", "matchRoute", "route", "path", "pathname", "to", "onClick", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/src/components/core/Dashboard/SidebarLink.jsx"], "sourcesContent": ["import * as Icons from \"react-icons/vsc\"\r\nimport { useDispatch } from \"react-redux\"\r\nimport { NavLink, matchPath, useLocation } from \"react-router-dom\"\r\n\r\nimport { resetCourseState } from \"../../../slices/courseSlice\"\r\n\r\nexport default function SidebarLink({ link, iconName }) {\r\n  const Icon = Icons[iconName]\r\n  const location = useLocation()\r\n  const dispatch = useDispatch()\r\n\r\n  const matchRoute = (route) => {\r\n    return matchPath({ path: route }, location.pathname)\r\n  }\r\n\r\n  return (\r\n    <NavLink\r\n      to={link.path}\r\n      onClick={() => dispatch(resetCourseState())}\r\n      className={`relative px-8 py-2 text-sm font-medium ${\r\n        matchRoute(link.path)\r\n          ? \"bg-yellow-800 text-yellow-50\"\r\n          : \"bg-opacity-0 text-richblack-300\"\r\n      } transition-all duration-200`}\r\n    >\r\n      <span\r\n        className={`absolute left-0 top-0 h-full w-[0.15rem] bg-yellow-50 ${\r\n          matchRoute(link.path) ? \"opacity-100\" : \"opacity-0\"\r\n        }`}\r\n      ></span>\r\n      <div className=\"flex items-center gap-x-2\">\r\n        {/* Icon Goes Here */}\r\n        <Icon className=\"text-lg\" />\r\n        <span>{link.name}</span>\r\n      </div>\r\n    </NavLink>\r\n  )\r\n}"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,iBAAiB;AACxC,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,OAAO,EAAEC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AAElE,SAASC,gBAAgB,QAAQ,6BAA6B;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAE9D,eAAe,SAASC,WAAWA,CAAAC,IAAA,EAAqB;EAAAC,EAAA;EAAA,IAApB;IAAEC,IAAI;IAAEC;EAAS,CAAC,GAAAH,IAAA;EACpD,MAAMI,IAAI,GAAGb,KAAK,CAACY,QAAQ,CAAC;EAC5B,MAAME,QAAQ,GAAGV,WAAW,EAAE;EAC9B,MAAMW,QAAQ,GAAGd,WAAW,EAAE;EAE9B,MAAMe,UAAU,GAAIC,KAAK,IAAK;IAC5B,OAAOd,SAAS,CAAC;MAAEe,IAAI,EAAED;IAAM,CAAC,EAAEH,QAAQ,CAACK,QAAQ,CAAC;EACtD,CAAC;EAED,oBACEZ,OAAA,CAACL,OAAO;IACNkB,EAAE,EAAET,IAAI,CAACO,IAAK;IACdG,OAAO,EAAEA,CAAA,KAAMN,QAAQ,CAACV,gBAAgB,EAAE,CAAE;IAC5CiB,SAAS,EAAG,0CACVN,UAAU,CAACL,IAAI,CAACO,IAAI,CAAC,GACjB,8BAA8B,GAC9B,iCACL,8BAA8B;IAAAK,QAAA,gBAE/BhB,OAAA;MACEe,SAAS,EAAG,yDACVN,UAAU,CAACL,IAAI,CAACO,IAAI,CAAC,GAAG,aAAa,GAAG,WACzC;IAAE;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACG,eACRpB,OAAA;MAAKe,SAAS,EAAC,2BAA2B;MAAAC,QAAA,gBAExChB,OAAA,CAACM,IAAI;QAACS,SAAS,EAAC;MAAS;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAAG,eAC5BpB,OAAA;QAAAgB,QAAA,EAAOZ,IAAI,CAACiB;MAAI;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAAQ;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACpB;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACE;AAEd;AAACjB,EAAA,CA/BuBF,WAAW;EAAA,QAEhBJ,WAAW,EACXH,WAAW;AAAA;AAAA4B,EAAA,GAHNrB,WAAW;AAAA,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}