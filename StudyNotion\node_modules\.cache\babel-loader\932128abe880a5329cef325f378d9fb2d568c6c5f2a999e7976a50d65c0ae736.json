{"ast": null, "code": "/* eslint no-unused-vars: \"off\" */\nexport default function slidePrev() {\n  let speed = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : this.params.speed;\n  let runCallbacks = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n  let internal = arguments.length > 2 ? arguments[2] : undefined;\n  const swiper = this;\n  const {\n    params,\n    snapGrid,\n    slidesGrid,\n    rtlTranslate,\n    enabled,\n    animating\n  } = swiper;\n  if (!enabled) return swiper;\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  if (params.loop) {\n    if (animating && !isVirtual && params.loopPreventsSliding) return false;\n    swiper.loopFix({\n      direction: 'prev'\n    });\n    // eslint-disable-next-line\n    swiper._clientLeft = swiper.wrapperEl.clientLeft;\n  }\n  const translate = rtlTranslate ? swiper.translate : -swiper.translate;\n  function normalize(val) {\n    if (val < 0) return -Math.floor(Math.abs(val));\n    return Math.floor(val);\n  }\n  const normalizedTranslate = normalize(translate);\n  const normalizedSnapGrid = snapGrid.map(val => normalize(val));\n  let prevSnap = snapGrid[normalizedSnapGrid.indexOf(normalizedTranslate) - 1];\n  if (typeof prevSnap === 'undefined' && params.cssMode) {\n    let prevSnapIndex;\n    snapGrid.forEach((snap, snapIndex) => {\n      if (normalizedTranslate >= snap) {\n        // prevSnap = snap;\n        prevSnapIndex = snapIndex;\n      }\n    });\n    if (typeof prevSnapIndex !== 'undefined') {\n      prevSnap = snapGrid[prevSnapIndex > 0 ? prevSnapIndex - 1 : prevSnapIndex];\n    }\n  }\n  let prevIndex = 0;\n  if (typeof prevSnap !== 'undefined') {\n    prevIndex = slidesGrid.indexOf(prevSnap);\n    if (prevIndex < 0) prevIndex = swiper.activeIndex - 1;\n    if (params.slidesPerView === 'auto' && params.slidesPerGroup === 1 && params.slidesPerGroupAuto) {\n      prevIndex = prevIndex - swiper.slidesPerViewDynamic('previous', true) + 1;\n      prevIndex = Math.max(prevIndex, 0);\n    }\n  }\n  if (params.rewind && swiper.isBeginning) {\n    const lastIndex = swiper.params.virtual && swiper.params.virtual.enabled && swiper.virtual ? swiper.virtual.slides.length - 1 : swiper.slides.length - 1;\n    return swiper.slideTo(lastIndex, speed, runCallbacks, internal);\n  }\n  return swiper.slideTo(prevIndex, speed, runCallbacks, internal);\n}", "map": {"version": 3, "names": ["slidePrev", "speed", "arguments", "length", "undefined", "params", "runCallbacks", "internal", "swiper", "snapGrid", "slidesGrid", "rtlTranslate", "enabled", "animating", "isVirtual", "virtual", "loop", "loopPreventsSliding", "loopFix", "direction", "_clientLeft", "wrapperEl", "clientLeft", "translate", "normalize", "val", "Math", "floor", "abs", "normalizedTranslate", "normalizedSnapGrid", "map", "prevSnap", "indexOf", "cssMode", "prevSnapIndex", "for<PERSON>ach", "snap", "snapIndex", "prevIndex", "activeIndex", "<PERSON><PERSON><PERSON><PERSON>iew", "slidesPerGroup", "slidesPerGroupAuto", "slidesPerViewDynamic", "max", "rewind", "isBeginning", "lastIndex", "slides", "slideTo"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/core/slide/slidePrev.js"], "sourcesContent": ["/* eslint no-unused-vars: \"off\" */\nexport default function slidePrev(speed = this.params.speed, runCallbacks = true, internal) {\n  const swiper = this;\n  const {\n    params,\n    snapGrid,\n    slidesGrid,\n    rtlTranslate,\n    enabled,\n    animating\n  } = swiper;\n  if (!enabled) return swiper;\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  if (params.loop) {\n    if (animating && !isVirtual && params.loopPreventsSliding) return false;\n    swiper.loopFix({\n      direction: 'prev'\n    });\n    // eslint-disable-next-line\n    swiper._clientLeft = swiper.wrapperEl.clientLeft;\n  }\n  const translate = rtlTranslate ? swiper.translate : -swiper.translate;\n  function normalize(val) {\n    if (val < 0) return -Math.floor(Math.abs(val));\n    return Math.floor(val);\n  }\n  const normalizedTranslate = normalize(translate);\n  const normalizedSnapGrid = snapGrid.map(val => normalize(val));\n  let prevSnap = snapGrid[normalizedSnapGrid.indexOf(normalizedTranslate) - 1];\n  if (typeof prevSnap === 'undefined' && params.cssMode) {\n    let prevSnapIndex;\n    snapGrid.forEach((snap, snapIndex) => {\n      if (normalizedTranslate >= snap) {\n        // prevSnap = snap;\n        prevSnapIndex = snapIndex;\n      }\n    });\n    if (typeof prevSnapIndex !== 'undefined') {\n      prevSnap = snapGrid[prevSnapIndex > 0 ? prevSnapIndex - 1 : prevSnapIndex];\n    }\n  }\n  let prevIndex = 0;\n  if (typeof prevSnap !== 'undefined') {\n    prevIndex = slidesGrid.indexOf(prevSnap);\n    if (prevIndex < 0) prevIndex = swiper.activeIndex - 1;\n    if (params.slidesPerView === 'auto' && params.slidesPerGroup === 1 && params.slidesPerGroupAuto) {\n      prevIndex = prevIndex - swiper.slidesPerViewDynamic('previous', true) + 1;\n      prevIndex = Math.max(prevIndex, 0);\n    }\n  }\n  if (params.rewind && swiper.isBeginning) {\n    const lastIndex = swiper.params.virtual && swiper.params.virtual.enabled && swiper.virtual ? swiper.virtual.slides.length - 1 : swiper.slides.length - 1;\n    return swiper.slideTo(lastIndex, speed, runCallbacks, internal);\n  }\n  return swiper.slideTo(prevIndex, speed, runCallbacks, internal);\n}"], "mappings": "AAAA;AACA,eAAe,SAASA,SAASA,CAAA,EAA2D;EAAA,IAA1DC,KAAK,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI,CAACG,MAAM,CAACJ,KAAK;EAAA,IAAEK,YAAY,GAAAJ,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;EAAA,IAAEK,QAAQ,GAAAL,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EACxF,MAAMI,MAAM,GAAG,IAAI;EACnB,MAAM;IACJH,MAAM;IACNI,QAAQ;IACRC,UAAU;IACVC,YAAY;IACZC,OAAO;IACPC;EACF,CAAC,GAAGL,MAAM;EACV,IAAI,CAACI,OAAO,EAAE,OAAOJ,MAAM;EAC3B,MAAMM,SAAS,GAAGN,MAAM,CAACO,OAAO,IAAIV,MAAM,CAACU,OAAO,CAACH,OAAO;EAC1D,IAAIP,MAAM,CAACW,IAAI,EAAE;IACf,IAAIH,SAAS,IAAI,CAACC,SAAS,IAAIT,MAAM,CAACY,mBAAmB,EAAE,OAAO,KAAK;IACvET,MAAM,CAACU,OAAO,CAAC;MACbC,SAAS,EAAE;IACb,CAAC,CAAC;IACF;IACAX,MAAM,CAACY,WAAW,GAAGZ,MAAM,CAACa,SAAS,CAACC,UAAU;EAClD;EACA,MAAMC,SAAS,GAAGZ,YAAY,GAAGH,MAAM,CAACe,SAAS,GAAG,CAACf,MAAM,CAACe,SAAS;EACrE,SAASC,SAASA,CAACC,GAAG,EAAE;IACtB,IAAIA,GAAG,GAAG,CAAC,EAAE,OAAO,CAACC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACH,GAAG,CAAC,CAAC;IAC9C,OAAOC,IAAI,CAACC,KAAK,CAACF,GAAG,CAAC;EACxB;EACA,MAAMI,mBAAmB,GAAGL,SAAS,CAACD,SAAS,CAAC;EAChD,MAAMO,kBAAkB,GAAGrB,QAAQ,CAACsB,GAAG,CAACN,GAAG,IAAID,SAAS,CAACC,GAAG,CAAC,CAAC;EAC9D,IAAIO,QAAQ,GAAGvB,QAAQ,CAACqB,kBAAkB,CAACG,OAAO,CAACJ,mBAAmB,CAAC,GAAG,CAAC,CAAC;EAC5E,IAAI,OAAOG,QAAQ,KAAK,WAAW,IAAI3B,MAAM,CAAC6B,OAAO,EAAE;IACrD,IAAIC,aAAa;IACjB1B,QAAQ,CAAC2B,OAAO,CAAC,CAACC,IAAI,EAAEC,SAAS,KAAK;MACpC,IAAIT,mBAAmB,IAAIQ,IAAI,EAAE;QAC/B;QACAF,aAAa,GAAGG,SAAS;MAC3B;IACF,CAAC,CAAC;IACF,IAAI,OAAOH,aAAa,KAAK,WAAW,EAAE;MACxCH,QAAQ,GAAGvB,QAAQ,CAAC0B,aAAa,GAAG,CAAC,GAAGA,aAAa,GAAG,CAAC,GAAGA,aAAa,CAAC;IAC5E;EACF;EACA,IAAII,SAAS,GAAG,CAAC;EACjB,IAAI,OAAOP,QAAQ,KAAK,WAAW,EAAE;IACnCO,SAAS,GAAG7B,UAAU,CAACuB,OAAO,CAACD,QAAQ,CAAC;IACxC,IAAIO,SAAS,GAAG,CAAC,EAAEA,SAAS,GAAG/B,MAAM,CAACgC,WAAW,GAAG,CAAC;IACrD,IAAInC,MAAM,CAACoC,aAAa,KAAK,MAAM,IAAIpC,MAAM,CAACqC,cAAc,KAAK,CAAC,IAAIrC,MAAM,CAACsC,kBAAkB,EAAE;MAC/FJ,SAAS,GAAGA,SAAS,GAAG/B,MAAM,CAACoC,oBAAoB,CAAC,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC;MACzEL,SAAS,GAAGb,IAAI,CAACmB,GAAG,CAACN,SAAS,EAAE,CAAC,CAAC;IACpC;EACF;EACA,IAAIlC,MAAM,CAACyC,MAAM,IAAItC,MAAM,CAACuC,WAAW,EAAE;IACvC,MAAMC,SAAS,GAAGxC,MAAM,CAACH,MAAM,CAACU,OAAO,IAAIP,MAAM,CAACH,MAAM,CAACU,OAAO,CAACH,OAAO,IAAIJ,MAAM,CAACO,OAAO,GAAGP,MAAM,CAACO,OAAO,CAACkC,MAAM,CAAC9C,MAAM,GAAG,CAAC,GAAGK,MAAM,CAACyC,MAAM,CAAC9C,MAAM,GAAG,CAAC;IACxJ,OAAOK,MAAM,CAAC0C,OAAO,CAACF,SAAS,EAAE/C,KAAK,EAAEK,YAAY,EAAEC,QAAQ,CAAC;EACjE;EACA,OAAOC,MAAM,CAAC0C,OAAO,CAACX,SAAS,EAAEtC,KAAK,EAAEK,YAAY,EAAEC,QAAQ,CAAC;AACjE"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}