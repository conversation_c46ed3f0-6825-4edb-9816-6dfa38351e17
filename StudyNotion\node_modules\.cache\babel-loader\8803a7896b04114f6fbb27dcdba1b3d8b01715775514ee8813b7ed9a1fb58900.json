{"ast": null, "code": "import utils from '../utils.js';\nimport httpAdapter from './http.js';\nimport xhrAdapter from './xhr.js';\nimport AxiosError from \"../core/AxiosError.js\";\nconst knownAdapters = {\n  http: httpAdapter,\n  xhr: xhrAdapter\n};\nutils.forEach(knownAdapters, (fn, value) => {\n  if (fn) {\n    try {\n      Object.defineProperty(fn, 'name', {\n        value\n      });\n    } catch (e) {\n      // eslint-disable-next-line no-empty\n    }\n    Object.defineProperty(fn, 'adapterName', {\n      value\n    });\n  }\n});\nexport default {\n  getAdapter: adapters => {\n    adapters = utils.isArray(adapters) ? adapters : [adapters];\n    const {\n      length\n    } = adapters;\n    let nameOrAdapter;\n    let adapter;\n    for (let i = 0; i < length; i++) {\n      nameOrAdapter = adapters[i];\n      if (adapter = utils.isString(nameOrAdapter) ? knownAdapters[nameOrAdapter.toLowerCase()] : nameOrAdapter) {\n        break;\n      }\n    }\n    if (!adapter) {\n      if (adapter === false) {\n        throw new AxiosError(`Adapter ${nameOrAdapter} is not supported by the environment`, 'ERR_NOT_SUPPORT');\n      }\n      throw new Error(utils.hasOwnProp(knownAdapters, nameOrAdapter) ? `Adapter '${nameOrAdapter}' is not available in the build` : `Unknown adapter '${nameOrAdapter}'`);\n    }\n    if (!utils.isFunction(adapter)) {\n      throw new TypeError('adapter is not a function');\n    }\n    return adapter;\n  },\n  adapters: knownAdapters\n};", "map": {"version": 3, "names": ["utils", "httpAdapter", "xhrAdapter", "AxiosError", "knownAdapters", "http", "xhr", "for<PERSON>ach", "fn", "value", "Object", "defineProperty", "e", "getAdapter", "adapters", "isArray", "length", "nameOrAdapter", "adapter", "i", "isString", "toLowerCase", "Error", "hasOwnProp", "isFunction", "TypeError"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/axios/lib/adapters/adapters.js"], "sourcesContent": ["import utils from '../utils.js';\nimport httpAdapter from './http.js';\nimport xhrAdapter from './xhr.js';\nimport AxiosError from \"../core/AxiosError.js\";\n\nconst knownAdapters = {\n  http: httpAdapter,\n  xhr: xhrAdapter\n}\n\nutils.forEach(knownAdapters, (fn, value) => {\n  if(fn) {\n    try {\n      Object.defineProperty(fn, 'name', {value});\n    } catch (e) {\n      // eslint-disable-next-line no-empty\n    }\n    Object.defineProperty(fn, 'adapterName', {value});\n  }\n});\n\nexport default {\n  getAdapter: (adapters) => {\n    adapters = utils.isArray(adapters) ? adapters : [adapters];\n\n    const {length} = adapters;\n    let nameOrAdapter;\n    let adapter;\n\n    for (let i = 0; i < length; i++) {\n      nameOrAdapter = adapters[i];\n      if((adapter = utils.isString(nameOrAdapter) ? knownAdapters[nameOrAdapter.toLowerCase()] : nameOrAdapter)) {\n        break;\n      }\n    }\n\n    if (!adapter) {\n      if (adapter === false) {\n        throw new AxiosError(\n          `Adapter ${nameOrAdapter} is not supported by the environment`,\n          'ERR_NOT_SUPPORT'\n        );\n      }\n\n      throw new Error(\n        utils.hasOwnProp(knownAdapters, nameOrAdapter) ?\n          `Adapter '${nameOrAdapter}' is not available in the build` :\n          `Unknown adapter '${nameOrAdapter}'`\n      );\n    }\n\n    if (!utils.isFunction(adapter)) {\n      throw new TypeError('adapter is not a function');\n    }\n\n    return adapter;\n  },\n  adapters: knownAdapters\n}\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,aAAa;AAC/B,OAAOC,WAAW,MAAM,WAAW;AACnC,OAAOC,UAAU,MAAM,UAAU;AACjC,OAAOC,UAAU,MAAM,uBAAuB;AAE9C,MAAMC,aAAa,GAAG;EACpBC,IAAI,EAAEJ,WAAW;EACjBK,GAAG,EAAEJ;AACP,CAAC;AAEDF,KAAK,CAACO,OAAO,CAACH,aAAa,EAAE,CAACI,EAAE,EAAEC,KAAK,KAAK;EAC1C,IAAGD,EAAE,EAAE;IACL,IAAI;MACFE,MAAM,CAACC,cAAc,CAACH,EAAE,EAAE,MAAM,EAAE;QAACC;MAAK,CAAC,CAAC;IAC5C,CAAC,CAAC,OAAOG,CAAC,EAAE;MACV;IAAA;IAEFF,MAAM,CAACC,cAAc,CAACH,EAAE,EAAE,aAAa,EAAE;MAACC;IAAK,CAAC,CAAC;EACnD;AACF,CAAC,CAAC;AAEF,eAAe;EACbI,UAAU,EAAGC,QAAQ,IAAK;IACxBA,QAAQ,GAAGd,KAAK,CAACe,OAAO,CAACD,QAAQ,CAAC,GAAGA,QAAQ,GAAG,CAACA,QAAQ,CAAC;IAE1D,MAAM;MAACE;IAAM,CAAC,GAAGF,QAAQ;IACzB,IAAIG,aAAa;IACjB,IAAIC,OAAO;IAEX,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,MAAM,EAAEG,CAAC,EAAE,EAAE;MAC/BF,aAAa,GAAGH,QAAQ,CAACK,CAAC,CAAC;MAC3B,IAAID,OAAO,GAAGlB,KAAK,CAACoB,QAAQ,CAACH,aAAa,CAAC,GAAGb,aAAa,CAACa,aAAa,CAACI,WAAW,EAAE,CAAC,GAAGJ,aAAa,EAAG;QACzG;MACF;IACF;IAEA,IAAI,CAACC,OAAO,EAAE;MACZ,IAAIA,OAAO,KAAK,KAAK,EAAE;QACrB,MAAM,IAAIf,UAAU,CACjB,WAAUc,aAAc,sCAAqC,EAC9D,iBAAiB,CAClB;MACH;MAEA,MAAM,IAAIK,KAAK,CACbtB,KAAK,CAACuB,UAAU,CAACnB,aAAa,EAAEa,aAAa,CAAC,GAC3C,YAAWA,aAAc,iCAAgC,GACzD,oBAAmBA,aAAc,GAAE,CACvC;IACH;IAEA,IAAI,CAACjB,KAAK,CAACwB,UAAU,CAACN,OAAO,CAAC,EAAE;MAC9B,MAAM,IAAIO,SAAS,CAAC,2BAA2B,CAAC;IAClD;IAEA,OAAOP,OAAO;EAChB,CAAC;EACDJ,QAAQ,EAAEV;AACZ,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}