{"ast": null, "code": "import { now, nextTick } from '../../shared/utils.js';\nexport default function onTouchEnd(event) {\n  const swiper = this;\n  const data = swiper.touchEventsData;\n  const pointerIndex = data.evCache.findIndex(cachedEv => cachedEv.pointerId === event.pointerId);\n  if (pointerIndex >= 0) {\n    data.evCache.splice(pointerIndex, 1);\n  }\n  if (['pointercancel', 'pointerout', 'pointerleave'].includes(event.type)) {\n    const proceed = event.type === 'pointercancel' && (swiper.browser.isSafari || swiper.browser.isWebView);\n    if (!proceed) {\n      return;\n    }\n  }\n  const {\n    params,\n    touches,\n    rtlTranslate: rtl,\n    slidesGrid,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n  if (!params.simulateTouch && event.pointerType === 'mouse') return;\n  let e = event;\n  if (e.originalEvent) e = e.originalEvent;\n  if (data.allowTouchCallbacks) {\n    swiper.emit('touchEnd', e);\n  }\n  data.allowTouchCallbacks = false;\n  if (!data.isTouched) {\n    if (data.isMoved && params.grabCursor) {\n      swiper.setGrabCursor(false);\n    }\n    data.isMoved = false;\n    data.startMoving = false;\n    return;\n  }\n  // Return Grab Cursor\n  if (params.grabCursor && data.isMoved && data.isTouched && (swiper.allowSlideNext === true || swiper.allowSlidePrev === true)) {\n    swiper.setGrabCursor(false);\n  }\n\n  // Time diff\n  const touchEndTime = now();\n  const timeDiff = touchEndTime - data.touchStartTime;\n\n  // Tap, doubleTap, Click\n  if (swiper.allowClick) {\n    const pathTree = e.path || e.composedPath && e.composedPath();\n    swiper.updateClickedSlide(pathTree && pathTree[0] || e.target);\n    swiper.emit('tap click', e);\n    if (timeDiff < 300 && touchEndTime - data.lastClickTime < 300) {\n      swiper.emit('doubleTap doubleClick', e);\n    }\n  }\n  data.lastClickTime = now();\n  nextTick(() => {\n    if (!swiper.destroyed) swiper.allowClick = true;\n  });\n  if (!data.isTouched || !data.isMoved || !swiper.swipeDirection || touches.diff === 0 || data.currentTranslate === data.startTranslate) {\n    data.isTouched = false;\n    data.isMoved = false;\n    data.startMoving = false;\n    return;\n  }\n  data.isTouched = false;\n  data.isMoved = false;\n  data.startMoving = false;\n  let currentPos;\n  if (params.followFinger) {\n    currentPos = rtl ? swiper.translate : -swiper.translate;\n  } else {\n    currentPos = -data.currentTranslate;\n  }\n  if (params.cssMode) {\n    return;\n  }\n  if (params.freeMode && params.freeMode.enabled) {\n    swiper.freeMode.onTouchEnd({\n      currentPos\n    });\n    return;\n  }\n\n  // Find current slide\n  let stopIndex = 0;\n  let groupSize = swiper.slidesSizesGrid[0];\n  for (let i = 0; i < slidesGrid.length; i += i < params.slidesPerGroupSkip ? 1 : params.slidesPerGroup) {\n    const increment = i < params.slidesPerGroupSkip - 1 ? 1 : params.slidesPerGroup;\n    if (typeof slidesGrid[i + increment] !== 'undefined') {\n      if (currentPos >= slidesGrid[i] && currentPos < slidesGrid[i + increment]) {\n        stopIndex = i;\n        groupSize = slidesGrid[i + increment] - slidesGrid[i];\n      }\n    } else if (currentPos >= slidesGrid[i]) {\n      stopIndex = i;\n      groupSize = slidesGrid[slidesGrid.length - 1] - slidesGrid[slidesGrid.length - 2];\n    }\n  }\n  let rewindFirstIndex = null;\n  let rewindLastIndex = null;\n  if (params.rewind) {\n    if (swiper.isBeginning) {\n      rewindLastIndex = params.virtual && params.virtual.enabled && swiper.virtual ? swiper.virtual.slides.length - 1 : swiper.slides.length - 1;\n    } else if (swiper.isEnd) {\n      rewindFirstIndex = 0;\n    }\n  }\n  // Find current slide size\n  const ratio = (currentPos - slidesGrid[stopIndex]) / groupSize;\n  const increment = stopIndex < params.slidesPerGroupSkip - 1 ? 1 : params.slidesPerGroup;\n  if (timeDiff > params.longSwipesMs) {\n    // Long touches\n    if (!params.longSwipes) {\n      swiper.slideTo(swiper.activeIndex);\n      return;\n    }\n    if (swiper.swipeDirection === 'next') {\n      if (ratio >= params.longSwipesRatio) swiper.slideTo(params.rewind && swiper.isEnd ? rewindFirstIndex : stopIndex + increment);else swiper.slideTo(stopIndex);\n    }\n    if (swiper.swipeDirection === 'prev') {\n      if (ratio > 1 - params.longSwipesRatio) {\n        swiper.slideTo(stopIndex + increment);\n      } else if (rewindLastIndex !== null && ratio < 0 && Math.abs(ratio) > params.longSwipesRatio) {\n        swiper.slideTo(rewindLastIndex);\n      } else {\n        swiper.slideTo(stopIndex);\n      }\n    }\n  } else {\n    // Short swipes\n    if (!params.shortSwipes) {\n      swiper.slideTo(swiper.activeIndex);\n      return;\n    }\n    const isNavButtonTarget = swiper.navigation && (e.target === swiper.navigation.nextEl || e.target === swiper.navigation.prevEl);\n    if (!isNavButtonTarget) {\n      if (swiper.swipeDirection === 'next') {\n        swiper.slideTo(rewindFirstIndex !== null ? rewindFirstIndex : stopIndex + increment);\n      }\n      if (swiper.swipeDirection === 'prev') {\n        swiper.slideTo(rewindLastIndex !== null ? rewindLastIndex : stopIndex);\n      }\n    } else if (e.target === swiper.navigation.nextEl) {\n      swiper.slideTo(stopIndex + increment);\n    } else {\n      swiper.slideTo(stopIndex);\n    }\n  }\n}", "map": {"version": 3, "names": ["now", "nextTick", "onTouchEnd", "event", "swiper", "data", "touchEventsData", "pointerIndex", "ev<PERSON><PERSON>", "findIndex", "cachedEv", "pointerId", "splice", "includes", "type", "proceed", "browser", "<PERSON><PERSON><PERSON><PERSON>", "isWebView", "params", "touches", "rtlTranslate", "rtl", "slidesGrid", "enabled", "simulate<PERSON>ouch", "pointerType", "e", "originalEvent", "allowTouchCallbacks", "emit", "isTouched", "isMoved", "grabCursor", "setGrabCursor", "startMoving", "allowSlideNext", "allowSlidePrev", "touchEndTime", "timeDiff", "touchStartTime", "allowClick", "pathTree", "path", "<PERSON><PERSON><PERSON>", "updateClickedSlide", "target", "lastClickTime", "destroyed", "swipeDirection", "diff", "currentTranslate", "startTranslate", "currentPos", "follow<PERSON><PERSON>", "translate", "cssMode", "freeMode", "stopIndex", "groupSize", "slidesSizesGrid", "i", "length", "slidesPerGroupSkip", "slidesPerGroup", "increment", "rewindFirstIndex", "rewindLastIndex", "rewind", "isBeginning", "virtual", "slides", "isEnd", "ratio", "longSwipesMs", "longSwipes", "slideTo", "activeIndex", "longSwipesRatio", "Math", "abs", "shortSwipes", "isNavButtonTarget", "navigation", "nextEl", "prevEl"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/core/events/onTouchEnd.js"], "sourcesContent": ["import { now, nextTick } from '../../shared/utils.js';\nexport default function onTouchEnd(event) {\n  const swiper = this;\n  const data = swiper.touchEventsData;\n  const pointerIndex = data.evCache.findIndex(cachedEv => cachedEv.pointerId === event.pointerId);\n  if (pointerIndex >= 0) {\n    data.evCache.splice(pointerIndex, 1);\n  }\n  if (['pointercancel', 'pointerout', 'pointerleave'].includes(event.type)) {\n    const proceed = event.type === 'pointercancel' && (swiper.browser.isSafari || swiper.browser.isWebView);\n    if (!proceed) {\n      return;\n    }\n  }\n  const {\n    params,\n    touches,\n    rtlTranslate: rtl,\n    slidesGrid,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n  if (!params.simulateTouch && event.pointerType === 'mouse') return;\n  let e = event;\n  if (e.originalEvent) e = e.originalEvent;\n  if (data.allowTouchCallbacks) {\n    swiper.emit('touchEnd', e);\n  }\n  data.allowTouchCallbacks = false;\n  if (!data.isTouched) {\n    if (data.isMoved && params.grabCursor) {\n      swiper.setGrabCursor(false);\n    }\n    data.isMoved = false;\n    data.startMoving = false;\n    return;\n  }\n  // Return Grab Cursor\n  if (params.grabCursor && data.isMoved && data.isTouched && (swiper.allowSlideNext === true || swiper.allowSlidePrev === true)) {\n    swiper.setGrabCursor(false);\n  }\n\n  // Time diff\n  const touchEndTime = now();\n  const timeDiff = touchEndTime - data.touchStartTime;\n\n  // Tap, doubleTap, Click\n  if (swiper.allowClick) {\n    const pathTree = e.path || e.composedPath && e.composedPath();\n    swiper.updateClickedSlide(pathTree && pathTree[0] || e.target);\n    swiper.emit('tap click', e);\n    if (timeDiff < 300 && touchEndTime - data.lastClickTime < 300) {\n      swiper.emit('doubleTap doubleClick', e);\n    }\n  }\n  data.lastClickTime = now();\n  nextTick(() => {\n    if (!swiper.destroyed) swiper.allowClick = true;\n  });\n  if (!data.isTouched || !data.isMoved || !swiper.swipeDirection || touches.diff === 0 || data.currentTranslate === data.startTranslate) {\n    data.isTouched = false;\n    data.isMoved = false;\n    data.startMoving = false;\n    return;\n  }\n  data.isTouched = false;\n  data.isMoved = false;\n  data.startMoving = false;\n  let currentPos;\n  if (params.followFinger) {\n    currentPos = rtl ? swiper.translate : -swiper.translate;\n  } else {\n    currentPos = -data.currentTranslate;\n  }\n  if (params.cssMode) {\n    return;\n  }\n  if (params.freeMode && params.freeMode.enabled) {\n    swiper.freeMode.onTouchEnd({\n      currentPos\n    });\n    return;\n  }\n\n  // Find current slide\n  let stopIndex = 0;\n  let groupSize = swiper.slidesSizesGrid[0];\n  for (let i = 0; i < slidesGrid.length; i += i < params.slidesPerGroupSkip ? 1 : params.slidesPerGroup) {\n    const increment = i < params.slidesPerGroupSkip - 1 ? 1 : params.slidesPerGroup;\n    if (typeof slidesGrid[i + increment] !== 'undefined') {\n      if (currentPos >= slidesGrid[i] && currentPos < slidesGrid[i + increment]) {\n        stopIndex = i;\n        groupSize = slidesGrid[i + increment] - slidesGrid[i];\n      }\n    } else if (currentPos >= slidesGrid[i]) {\n      stopIndex = i;\n      groupSize = slidesGrid[slidesGrid.length - 1] - slidesGrid[slidesGrid.length - 2];\n    }\n  }\n  let rewindFirstIndex = null;\n  let rewindLastIndex = null;\n  if (params.rewind) {\n    if (swiper.isBeginning) {\n      rewindLastIndex = params.virtual && params.virtual.enabled && swiper.virtual ? swiper.virtual.slides.length - 1 : swiper.slides.length - 1;\n    } else if (swiper.isEnd) {\n      rewindFirstIndex = 0;\n    }\n  }\n  // Find current slide size\n  const ratio = (currentPos - slidesGrid[stopIndex]) / groupSize;\n  const increment = stopIndex < params.slidesPerGroupSkip - 1 ? 1 : params.slidesPerGroup;\n  if (timeDiff > params.longSwipesMs) {\n    // Long touches\n    if (!params.longSwipes) {\n      swiper.slideTo(swiper.activeIndex);\n      return;\n    }\n    if (swiper.swipeDirection === 'next') {\n      if (ratio >= params.longSwipesRatio) swiper.slideTo(params.rewind && swiper.isEnd ? rewindFirstIndex : stopIndex + increment);else swiper.slideTo(stopIndex);\n    }\n    if (swiper.swipeDirection === 'prev') {\n      if (ratio > 1 - params.longSwipesRatio) {\n        swiper.slideTo(stopIndex + increment);\n      } else if (rewindLastIndex !== null && ratio < 0 && Math.abs(ratio) > params.longSwipesRatio) {\n        swiper.slideTo(rewindLastIndex);\n      } else {\n        swiper.slideTo(stopIndex);\n      }\n    }\n  } else {\n    // Short swipes\n    if (!params.shortSwipes) {\n      swiper.slideTo(swiper.activeIndex);\n      return;\n    }\n    const isNavButtonTarget = swiper.navigation && (e.target === swiper.navigation.nextEl || e.target === swiper.navigation.prevEl);\n    if (!isNavButtonTarget) {\n      if (swiper.swipeDirection === 'next') {\n        swiper.slideTo(rewindFirstIndex !== null ? rewindFirstIndex : stopIndex + increment);\n      }\n      if (swiper.swipeDirection === 'prev') {\n        swiper.slideTo(rewindLastIndex !== null ? rewindLastIndex : stopIndex);\n      }\n    } else if (e.target === swiper.navigation.nextEl) {\n      swiper.slideTo(stopIndex + increment);\n    } else {\n      swiper.slideTo(stopIndex);\n    }\n  }\n}"], "mappings": "AAAA,SAASA,GAAG,EAAEC,QAAQ,QAAQ,uBAAuB;AACrD,eAAe,SAASC,UAAUA,CAACC,KAAK,EAAE;EACxC,MAAMC,MAAM,GAAG,IAAI;EACnB,MAAMC,IAAI,GAAGD,MAAM,CAACE,eAAe;EACnC,MAAMC,YAAY,GAAGF,IAAI,CAACG,OAAO,CAACC,SAAS,CAACC,QAAQ,IAAIA,QAAQ,CAACC,SAAS,KAAKR,KAAK,CAACQ,SAAS,CAAC;EAC/F,IAAIJ,YAAY,IAAI,CAAC,EAAE;IACrBF,IAAI,CAACG,OAAO,CAACI,MAAM,CAACL,YAAY,EAAE,CAAC,CAAC;EACtC;EACA,IAAI,CAAC,eAAe,EAAE,YAAY,EAAE,cAAc,CAAC,CAACM,QAAQ,CAACV,KAAK,CAACW,IAAI,CAAC,EAAE;IACxE,MAAMC,OAAO,GAAGZ,KAAK,CAACW,IAAI,KAAK,eAAe,KAAKV,MAAM,CAACY,OAAO,CAACC,QAAQ,IAAIb,MAAM,CAACY,OAAO,CAACE,SAAS,CAAC;IACvG,IAAI,CAACH,OAAO,EAAE;MACZ;IACF;EACF;EACA,MAAM;IACJI,MAAM;IACNC,OAAO;IACPC,YAAY,EAAEC,GAAG;IACjBC,UAAU;IACVC;EACF,CAAC,GAAGpB,MAAM;EACV,IAAI,CAACoB,OAAO,EAAE;EACd,IAAI,CAACL,MAAM,CAACM,aAAa,IAAItB,KAAK,CAACuB,WAAW,KAAK,OAAO,EAAE;EAC5D,IAAIC,CAAC,GAAGxB,KAAK;EACb,IAAIwB,CAAC,CAACC,aAAa,EAAED,CAAC,GAAGA,CAAC,CAACC,aAAa;EACxC,IAAIvB,IAAI,CAACwB,mBAAmB,EAAE;IAC5BzB,MAAM,CAAC0B,IAAI,CAAC,UAAU,EAAEH,CAAC,CAAC;EAC5B;EACAtB,IAAI,CAACwB,mBAAmB,GAAG,KAAK;EAChC,IAAI,CAACxB,IAAI,CAAC0B,SAAS,EAAE;IACnB,IAAI1B,IAAI,CAAC2B,OAAO,IAAIb,MAAM,CAACc,UAAU,EAAE;MACrC7B,MAAM,CAAC8B,aAAa,CAAC,KAAK,CAAC;IAC7B;IACA7B,IAAI,CAAC2B,OAAO,GAAG,KAAK;IACpB3B,IAAI,CAAC8B,WAAW,GAAG,KAAK;IACxB;EACF;EACA;EACA,IAAIhB,MAAM,CAACc,UAAU,IAAI5B,IAAI,CAAC2B,OAAO,IAAI3B,IAAI,CAAC0B,SAAS,KAAK3B,MAAM,CAACgC,cAAc,KAAK,IAAI,IAAIhC,MAAM,CAACiC,cAAc,KAAK,IAAI,CAAC,EAAE;IAC7HjC,MAAM,CAAC8B,aAAa,CAAC,KAAK,CAAC;EAC7B;;EAEA;EACA,MAAMI,YAAY,GAAGtC,GAAG,EAAE;EAC1B,MAAMuC,QAAQ,GAAGD,YAAY,GAAGjC,IAAI,CAACmC,cAAc;;EAEnD;EACA,IAAIpC,MAAM,CAACqC,UAAU,EAAE;IACrB,MAAMC,QAAQ,GAAGf,CAAC,CAACgB,IAAI,IAAIhB,CAAC,CAACiB,YAAY,IAAIjB,CAAC,CAACiB,YAAY,EAAE;IAC7DxC,MAAM,CAACyC,kBAAkB,CAACH,QAAQ,IAAIA,QAAQ,CAAC,CAAC,CAAC,IAAIf,CAAC,CAACmB,MAAM,CAAC;IAC9D1C,MAAM,CAAC0B,IAAI,CAAC,WAAW,EAAEH,CAAC,CAAC;IAC3B,IAAIY,QAAQ,GAAG,GAAG,IAAID,YAAY,GAAGjC,IAAI,CAAC0C,aAAa,GAAG,GAAG,EAAE;MAC7D3C,MAAM,CAAC0B,IAAI,CAAC,uBAAuB,EAAEH,CAAC,CAAC;IACzC;EACF;EACAtB,IAAI,CAAC0C,aAAa,GAAG/C,GAAG,EAAE;EAC1BC,QAAQ,CAAC,MAAM;IACb,IAAI,CAACG,MAAM,CAAC4C,SAAS,EAAE5C,MAAM,CAACqC,UAAU,GAAG,IAAI;EACjD,CAAC,CAAC;EACF,IAAI,CAACpC,IAAI,CAAC0B,SAAS,IAAI,CAAC1B,IAAI,CAAC2B,OAAO,IAAI,CAAC5B,MAAM,CAAC6C,cAAc,IAAI7B,OAAO,CAAC8B,IAAI,KAAK,CAAC,IAAI7C,IAAI,CAAC8C,gBAAgB,KAAK9C,IAAI,CAAC+C,cAAc,EAAE;IACrI/C,IAAI,CAAC0B,SAAS,GAAG,KAAK;IACtB1B,IAAI,CAAC2B,OAAO,GAAG,KAAK;IACpB3B,IAAI,CAAC8B,WAAW,GAAG,KAAK;IACxB;EACF;EACA9B,IAAI,CAAC0B,SAAS,GAAG,KAAK;EACtB1B,IAAI,CAAC2B,OAAO,GAAG,KAAK;EACpB3B,IAAI,CAAC8B,WAAW,GAAG,KAAK;EACxB,IAAIkB,UAAU;EACd,IAAIlC,MAAM,CAACmC,YAAY,EAAE;IACvBD,UAAU,GAAG/B,GAAG,GAAGlB,MAAM,CAACmD,SAAS,GAAG,CAACnD,MAAM,CAACmD,SAAS;EACzD,CAAC,MAAM;IACLF,UAAU,GAAG,CAAChD,IAAI,CAAC8C,gBAAgB;EACrC;EACA,IAAIhC,MAAM,CAACqC,OAAO,EAAE;IAClB;EACF;EACA,IAAIrC,MAAM,CAACsC,QAAQ,IAAItC,MAAM,CAACsC,QAAQ,CAACjC,OAAO,EAAE;IAC9CpB,MAAM,CAACqD,QAAQ,CAACvD,UAAU,CAAC;MACzBmD;IACF,CAAC,CAAC;IACF;EACF;;EAEA;EACA,IAAIK,SAAS,GAAG,CAAC;EACjB,IAAIC,SAAS,GAAGvD,MAAM,CAACwD,eAAe,CAAC,CAAC,CAAC;EACzC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtC,UAAU,CAACuC,MAAM,EAAED,CAAC,IAAIA,CAAC,GAAG1C,MAAM,CAAC4C,kBAAkB,GAAG,CAAC,GAAG5C,MAAM,CAAC6C,cAAc,EAAE;IACrG,MAAMC,SAAS,GAAGJ,CAAC,GAAG1C,MAAM,CAAC4C,kBAAkB,GAAG,CAAC,GAAG,CAAC,GAAG5C,MAAM,CAAC6C,cAAc;IAC/E,IAAI,OAAOzC,UAAU,CAACsC,CAAC,GAAGI,SAAS,CAAC,KAAK,WAAW,EAAE;MACpD,IAAIZ,UAAU,IAAI9B,UAAU,CAACsC,CAAC,CAAC,IAAIR,UAAU,GAAG9B,UAAU,CAACsC,CAAC,GAAGI,SAAS,CAAC,EAAE;QACzEP,SAAS,GAAGG,CAAC;QACbF,SAAS,GAAGpC,UAAU,CAACsC,CAAC,GAAGI,SAAS,CAAC,GAAG1C,UAAU,CAACsC,CAAC,CAAC;MACvD;IACF,CAAC,MAAM,IAAIR,UAAU,IAAI9B,UAAU,CAACsC,CAAC,CAAC,EAAE;MACtCH,SAAS,GAAGG,CAAC;MACbF,SAAS,GAAGpC,UAAU,CAACA,UAAU,CAACuC,MAAM,GAAG,CAAC,CAAC,GAAGvC,UAAU,CAACA,UAAU,CAACuC,MAAM,GAAG,CAAC,CAAC;IACnF;EACF;EACA,IAAII,gBAAgB,GAAG,IAAI;EAC3B,IAAIC,eAAe,GAAG,IAAI;EAC1B,IAAIhD,MAAM,CAACiD,MAAM,EAAE;IACjB,IAAIhE,MAAM,CAACiE,WAAW,EAAE;MACtBF,eAAe,GAAGhD,MAAM,CAACmD,OAAO,IAAInD,MAAM,CAACmD,OAAO,CAAC9C,OAAO,IAAIpB,MAAM,CAACkE,OAAO,GAAGlE,MAAM,CAACkE,OAAO,CAACC,MAAM,CAACT,MAAM,GAAG,CAAC,GAAG1D,MAAM,CAACmE,MAAM,CAACT,MAAM,GAAG,CAAC;IAC5I,CAAC,MAAM,IAAI1D,MAAM,CAACoE,KAAK,EAAE;MACvBN,gBAAgB,GAAG,CAAC;IACtB;EACF;EACA;EACA,MAAMO,KAAK,GAAG,CAACpB,UAAU,GAAG9B,UAAU,CAACmC,SAAS,CAAC,IAAIC,SAAS;EAC9D,MAAMM,SAAS,GAAGP,SAAS,GAAGvC,MAAM,CAAC4C,kBAAkB,GAAG,CAAC,GAAG,CAAC,GAAG5C,MAAM,CAAC6C,cAAc;EACvF,IAAIzB,QAAQ,GAAGpB,MAAM,CAACuD,YAAY,EAAE;IAClC;IACA,IAAI,CAACvD,MAAM,CAACwD,UAAU,EAAE;MACtBvE,MAAM,CAACwE,OAAO,CAACxE,MAAM,CAACyE,WAAW,CAAC;MAClC;IACF;IACA,IAAIzE,MAAM,CAAC6C,cAAc,KAAK,MAAM,EAAE;MACpC,IAAIwB,KAAK,IAAItD,MAAM,CAAC2D,eAAe,EAAE1E,MAAM,CAACwE,OAAO,CAACzD,MAAM,CAACiD,MAAM,IAAIhE,MAAM,CAACoE,KAAK,GAAGN,gBAAgB,GAAGR,SAAS,GAAGO,SAAS,CAAC,CAAC,KAAK7D,MAAM,CAACwE,OAAO,CAAClB,SAAS,CAAC;IAC9J;IACA,IAAItD,MAAM,CAAC6C,cAAc,KAAK,MAAM,EAAE;MACpC,IAAIwB,KAAK,GAAG,CAAC,GAAGtD,MAAM,CAAC2D,eAAe,EAAE;QACtC1E,MAAM,CAACwE,OAAO,CAAClB,SAAS,GAAGO,SAAS,CAAC;MACvC,CAAC,MAAM,IAAIE,eAAe,KAAK,IAAI,IAAIM,KAAK,GAAG,CAAC,IAAIM,IAAI,CAACC,GAAG,CAACP,KAAK,CAAC,GAAGtD,MAAM,CAAC2D,eAAe,EAAE;QAC5F1E,MAAM,CAACwE,OAAO,CAACT,eAAe,CAAC;MACjC,CAAC,MAAM;QACL/D,MAAM,CAACwE,OAAO,CAAClB,SAAS,CAAC;MAC3B;IACF;EACF,CAAC,MAAM;IACL;IACA,IAAI,CAACvC,MAAM,CAAC8D,WAAW,EAAE;MACvB7E,MAAM,CAACwE,OAAO,CAACxE,MAAM,CAACyE,WAAW,CAAC;MAClC;IACF;IACA,MAAMK,iBAAiB,GAAG9E,MAAM,CAAC+E,UAAU,KAAKxD,CAAC,CAACmB,MAAM,KAAK1C,MAAM,CAAC+E,UAAU,CAACC,MAAM,IAAIzD,CAAC,CAACmB,MAAM,KAAK1C,MAAM,CAAC+E,UAAU,CAACE,MAAM,CAAC;IAC/H,IAAI,CAACH,iBAAiB,EAAE;MACtB,IAAI9E,MAAM,CAAC6C,cAAc,KAAK,MAAM,EAAE;QACpC7C,MAAM,CAACwE,OAAO,CAACV,gBAAgB,KAAK,IAAI,GAAGA,gBAAgB,GAAGR,SAAS,GAAGO,SAAS,CAAC;MACtF;MACA,IAAI7D,MAAM,CAAC6C,cAAc,KAAK,MAAM,EAAE;QACpC7C,MAAM,CAACwE,OAAO,CAACT,eAAe,KAAK,IAAI,GAAGA,eAAe,GAAGT,SAAS,CAAC;MACxE;IACF,CAAC,MAAM,IAAI/B,CAAC,CAACmB,MAAM,KAAK1C,MAAM,CAAC+E,UAAU,CAACC,MAAM,EAAE;MAChDhF,MAAM,CAACwE,OAAO,CAAClB,SAAS,GAAGO,SAAS,CAAC;IACvC,CAAC,MAAM;MACL7D,MAAM,CAACwE,OAAO,CAAClB,SAAS,CAAC;IAC3B;EACF;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}