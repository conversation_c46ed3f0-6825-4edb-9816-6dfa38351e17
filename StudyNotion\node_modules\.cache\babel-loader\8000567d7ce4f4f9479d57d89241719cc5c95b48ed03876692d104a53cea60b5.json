{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = _default;\nexports.operationReducer = exports.playerReducer = void 0;\nvar _player = _interopRequireDefault(require(\"./player\"));\nvar _operation = _interopRequireDefault(require(\"./operation\"));\nfunction _default() {\n  var state = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  var action = arguments.length > 1 ? arguments[1] : undefined;\n  return {\n    player: (0, _player[\"default\"])(state.player, action),\n    operation: (0, _operation[\"default\"])(state.operation, action)\n  };\n}\nvar playerReducer = _player[\"default\"];\nexports.playerReducer = playerReducer;\nvar operationReducer = _operation[\"default\"];\nexports.operationReducer = operationReducer;", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "_default", "operationReducer", "player<PERSON>ed<PERSON><PERSON>", "_player", "_operation", "state", "arguments", "length", "undefined", "action", "player", "operation"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/video-react/lib/reducers/index.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = _default;\nexports.operationReducer = exports.playerReducer = void 0;\n\nvar _player = _interopRequireDefault(require(\"./player\"));\n\nvar _operation = _interopRequireDefault(require(\"./operation\"));\n\nfunction _default() {\n  var state = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  var action = arguments.length > 1 ? arguments[1] : undefined;\n  return {\n    player: (0, _player[\"default\"])(state.player, action),\n    operation: (0, _operation[\"default\"])(state.operation, action)\n  };\n}\n\nvar playerReducer = _player[\"default\"];\nexports.playerReducer = playerReducer;\nvar operationReducer = _operation[\"default\"];\nexports.operationReducer = operationReducer;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC;AAEpFC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAAC,SAAS,CAAC,GAAGE,QAAQ;AAC7BF,OAAO,CAACG,gBAAgB,GAAGH,OAAO,CAACI,aAAa,GAAG,KAAK,CAAC;AAEzD,IAAIC,OAAO,GAAGT,sBAAsB,CAACC,OAAO,CAAC,UAAU,CAAC,CAAC;AAEzD,IAAIS,UAAU,GAAGV,sBAAsB,CAACC,OAAO,CAAC,aAAa,CAAC,CAAC;AAE/D,SAASK,QAAQA,CAAA,EAAG;EAClB,IAAIK,KAAK,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EAClF,IAAIG,MAAM,GAAGH,SAAS,CAACC,MAAM,GAAG,CAAC,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAGE,SAAS;EAC5D,OAAO;IACLE,MAAM,EAAE,CAAC,CAAC,EAAEP,OAAO,CAAC,SAAS,CAAC,EAAEE,KAAK,CAACK,MAAM,EAAED,MAAM,CAAC;IACrDE,SAAS,EAAE,CAAC,CAAC,EAAEP,UAAU,CAAC,SAAS,CAAC,EAAEC,KAAK,CAACM,SAAS,EAAEF,MAAM;EAC/D,CAAC;AACH;AAEA,IAAIP,aAAa,GAAGC,OAAO,CAAC,SAAS,CAAC;AACtCL,OAAO,CAACI,aAAa,GAAGA,aAAa;AACrC,IAAID,gBAAgB,GAAGG,UAAU,CAAC,SAAS,CAAC;AAC5CN,OAAO,CAACG,gBAAgB,GAAGA,gBAAgB"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}