{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\temp\\\\StudyNotion\\\\src\\\\components\\\\core\\\\Dashboard\\\\AddCourse\\\\CourseInformation\\\\RequirementField.jsx\",\n  _s = $RefreshSig$();\nimport { useEffect, useState } from \"react\";\nimport { useSelector } from \"react-redux\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function RequirementsField(_ref) {\n  _s();\n  let {\n    name,\n    label,\n    register,\n    setValue,\n    errors,\n    getValues\n  } = _ref;\n  const {\n    editCourse,\n    course\n  } = useSelector(state => state.course);\n  const [requirement, setRequirement] = useState(\"\");\n  const [requirementsList, setRequirementsList] = useState([]);\n  useEffect(() => {\n    if (editCourse) {\n      setRequirementsList(course === null || course === void 0 ? void 0 : course.instructions);\n    }\n    register(name, {\n      required: true,\n      validate: value => value.length > 0\n    });\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  useEffect(() => {\n    setValue(name, requirementsList);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [requirementsList]);\n  const handleAddRequirement = () => {\n    if (requirement) {\n      setRequirementsList([...requirementsList, requirement]);\n      setRequirement(\"\");\n    }\n  };\n  const handleRemoveRequirement = index => {\n    const updatedRequirements = [...requirementsList];\n    updatedRequirements.splice(index, 1);\n    setRequirementsList(updatedRequirements);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col space-y-2\",\n    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n      className: \"text-sm text-richblack-5\",\n      htmlFor: name,\n      children: [label, \" \", /*#__PURE__*/_jsxDEV(\"sup\", {\n        className: \"text-pink-200\",\n        children: \"*\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col items-start space-y-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        id: name,\n        value: requirement,\n        onChange: e => setRequirement(e.target.value),\n        className: \"form-style w-full\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"button\",\n        onClick: handleAddRequirement,\n        className: \"font-semibold text-yellow-50\",\n        children: \"Add\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 7\n    }, this), requirementsList.length > 0 && /*#__PURE__*/_jsxDEV(\"ul\", {\n      className: \"mt-2 list-inside list-disc\",\n      children: requirementsList.map((requirement, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"flex items-center text-richblack-5\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: requirement\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          className: \"ml-2 text-xs text-pure-greys-300 \",\n          onClick: () => handleRemoveRequirement(index),\n          children: \"clear\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 15\n        }, this)]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 9\n    }, this), errors[name] && /*#__PURE__*/_jsxDEV(\"span\", {\n      className: \"ml-2 text-xs tracking-wide text-pink-200\",\n      children: [label, \" is required\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 43,\n    columnNumber: 5\n  }, this);\n}\n_s(RequirementsField, \"1A0w7spK+9NuPHFfK9hnxGi6HoU=\", false, function () {\n  return [useSelector];\n});\n_c = RequirementsField;\nvar _c;\n$RefreshReg$(_c, \"RequirementsField\");", "map": {"version": 3, "names": ["useEffect", "useState", "useSelector", "jsxDEV", "_jsxDEV", "RequirementsField", "_ref", "_s", "name", "label", "register", "setValue", "errors", "getV<PERSON>ues", "editCourse", "course", "state", "requirement", "setRequirement", "requirementsList", "setRequirementsList", "instructions", "required", "validate", "value", "length", "handleAddRequirement", "handleRemoveRequirement", "index", "updatedRequirements", "splice", "className", "children", "htmlFor", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "id", "onChange", "e", "target", "onClick", "map", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/src/components/core/Dashboard/AddCourse/CourseInformation/RequirementField.jsx"], "sourcesContent": ["import { useEffect, useState } from \"react\"\r\nimport { useSelector } from \"react-redux\"\r\n\r\nexport default function RequirementsField({\r\n  name,\r\n  label,\r\n  register,\r\n  setValue,\r\n  errors,\r\n  getValues,\r\n}) {\r\n  const { editCourse, course } = useSelector((state) => state.course)\r\n  const [requirement, setRequirement] = useState(\"\")\r\n  const [requirementsList, setRequirementsList] = useState([])\r\n\r\n  useEffect(() => {\r\n    if (editCourse) {\r\n      setRequirementsList(course?.instructions)\r\n    }\r\n    register(name, { required: true, validate: (value) => value.length > 0 })\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [])\r\n\r\n  useEffect(() => {\r\n    setValue(name, requirementsList)\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [requirementsList])\r\n\r\n  const handleAddRequirement = () => {\r\n    if (requirement) {\r\n      setRequirementsList([...requirementsList, requirement])\r\n      setRequirement(\"\")\r\n    }\r\n  }\r\n\r\n  const handleRemoveRequirement = (index) => {\r\n    const updatedRequirements = [...requirementsList]\r\n    updatedRequirements.splice(index, 1)\r\n    setRequirementsList(updatedRequirements)\r\n  }\r\n\r\n  return (\r\n    <div className=\"flex flex-col space-y-2\">\r\n      <label className=\"text-sm text-richblack-5\" htmlFor={name}>\r\n        {label} <sup className=\"text-pink-200\">*</sup>\r\n      </label>\r\n      <div className=\"flex flex-col items-start space-y-2\">\r\n        <input\r\n          type=\"text\"\r\n          id={name}\r\n          value={requirement}\r\n          onChange={(e) => setRequirement(e.target.value)}\r\n          className=\"form-style w-full\"\r\n        />\r\n        <button\r\n          type=\"button\"\r\n          onClick={handleAddRequirement}\r\n          className=\"font-semibold text-yellow-50\"\r\n        >\r\n          Add\r\n        </button>\r\n      </div>\r\n      {requirementsList.length > 0 && (\r\n        <ul className=\"mt-2 list-inside list-disc\">\r\n          {requirementsList.map((requirement, index) => (\r\n            <li key={index} className=\"flex items-center text-richblack-5\">\r\n              <span>{requirement}</span>\r\n              <button\r\n                type=\"button\"\r\n                className=\"ml-2 text-xs text-pure-greys-300 \"\r\n                onClick={() => handleRemoveRequirement(index)}\r\n              >\r\n                clear\r\n              </button>\r\n            </li>\r\n          ))}\r\n        </ul>\r\n      )}\r\n      {errors[name] && (\r\n        <span className=\"ml-2 text-xs tracking-wide text-pink-200\">\r\n          {label} is required\r\n        </span>\r\n      )}\r\n    </div>\r\n  )\r\n}"], "mappings": ";;AAAA,SAASA,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,SAASC,WAAW,QAAQ,aAAa;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEzC,eAAe,SAASC,iBAAiBA,CAAAC,IAAA,EAOtC;EAAAC,EAAA;EAAA,IAPuC;IACxCC,IAAI;IACJC,KAAK;IACLC,QAAQ;IACRC,QAAQ;IACRC,MAAM;IACNC;EACF,CAAC,GAAAP,IAAA;EACC,MAAM;IAAEQ,UAAU;IAAEC;EAAO,CAAC,GAAGb,WAAW,CAAEc,KAAK,IAAKA,KAAK,CAACD,MAAM,CAAC;EACnE,MAAM,CAACE,WAAW,EAAEC,cAAc,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACkB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAE5DD,SAAS,CAAC,MAAM;IACd,IAAIc,UAAU,EAAE;MACdM,mBAAmB,CAACL,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEM,YAAY,CAAC;IAC3C;IACAX,QAAQ,CAACF,IAAI,EAAE;MAAEc,QAAQ,EAAE,IAAI;MAAEC,QAAQ,EAAGC,KAAK,IAAKA,KAAK,CAACC,MAAM,GAAG;IAAE,CAAC,CAAC;IACzE;EACF,CAAC,EAAE,EAAE,CAAC;EAENzB,SAAS,CAAC,MAAM;IACdW,QAAQ,CAACH,IAAI,EAAEW,gBAAgB,CAAC;IAChC;EACF,CAAC,EAAE,CAACA,gBAAgB,CAAC,CAAC;EAEtB,MAAMO,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAIT,WAAW,EAAE;MACfG,mBAAmB,CAAC,CAAC,GAAGD,gBAAgB,EAAEF,WAAW,CAAC,CAAC;MACvDC,cAAc,CAAC,EAAE,CAAC;IACpB;EACF,CAAC;EAED,MAAMS,uBAAuB,GAAIC,KAAK,IAAK;IACzC,MAAMC,mBAAmB,GAAG,CAAC,GAAGV,gBAAgB,CAAC;IACjDU,mBAAmB,CAACC,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;IACpCR,mBAAmB,CAACS,mBAAmB,CAAC;EAC1C,CAAC;EAED,oBACEzB,OAAA;IAAK2B,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBACtC5B,OAAA;MAAO2B,SAAS,EAAC,0BAA0B;MAACE,OAAO,EAAEzB,IAAK;MAAAwB,QAAA,GACvDvB,KAAK,EAAC,GAAC,eAAAL,OAAA;QAAK2B,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAAM;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACxC,eACRjC,OAAA;MAAK2B,SAAS,EAAC,qCAAqC;MAAAC,QAAA,gBAClD5B,OAAA;QACEkC,IAAI,EAAC,MAAM;QACXC,EAAE,EAAE/B,IAAK;QACTgB,KAAK,EAAEP,WAAY;QACnBuB,QAAQ,EAAGC,CAAC,IAAKvB,cAAc,CAACuB,CAAC,CAACC,MAAM,CAAClB,KAAK,CAAE;QAChDO,SAAS,EAAC;MAAmB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAC7B,eACFjC,OAAA;QACEkC,IAAI,EAAC,QAAQ;QACbK,OAAO,EAAEjB,oBAAqB;QAC9BK,SAAS,EAAC,8BAA8B;QAAAC,QAAA,EACzC;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAAS;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACL,EACLlB,gBAAgB,CAACM,MAAM,GAAG,CAAC,iBAC1BrB,OAAA;MAAI2B,SAAS,EAAC,4BAA4B;MAAAC,QAAA,EACvCb,gBAAgB,CAACyB,GAAG,CAAC,CAAC3B,WAAW,EAAEW,KAAK,kBACvCxB,OAAA;QAAgB2B,SAAS,EAAC,oCAAoC;QAAAC,QAAA,gBAC5D5B,OAAA;UAAA4B,QAAA,EAAOf;QAAW;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAQ,eAC1BjC,OAAA;UACEkC,IAAI,EAAC,QAAQ;UACbP,SAAS,EAAC,mCAAmC;UAC7CY,OAAO,EAAEA,CAAA,KAAMhB,uBAAuB,CAACC,KAAK,CAAE;UAAAI,QAAA,EAC/C;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAS;MAAA,GARFT,KAAK;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAUf;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAEL,EACAzB,MAAM,CAACJ,IAAI,CAAC,iBACXJ,OAAA;MAAM2B,SAAS,EAAC,0CAA0C;MAAAC,QAAA,GACvDvB,KAAK,EAAC,cACT;IAAA;MAAAyB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACD;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACG;AAEV;AAAC9B,EAAA,CAlFuBF,iBAAiB;EAAA,QAQRH,WAAW;AAAA;AAAA2C,EAAA,GARpBxC,iBAAiB;AAAA,IAAAwC,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}