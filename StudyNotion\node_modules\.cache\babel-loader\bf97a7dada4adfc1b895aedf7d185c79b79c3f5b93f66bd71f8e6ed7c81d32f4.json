{"ast": null, "code": "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Blockquote} Blockquote\n * @typedef {import('../state.js').State} State\n */\n\n/**\n * Turn an mdast `blockquote` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Blockquote} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function blockquote(state, node) {\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'blockquote',\n    properties: {},\n    children: state.wrap(state.all(node), true)\n  };\n  state.patch(node, result);\n  return state.applyData(node, result);\n}", "map": {"version": 3, "names": ["blockquote", "state", "node", "result", "type", "tagName", "properties", "children", "wrap", "all", "patch", "applyData"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/mdast-util-to-hast/lib/handlers/blockquote.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Blockquote} Blockquote\n * @typedef {import('../state.js').State} State\n */\n\n/**\n * Turn an mdast `blockquote` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Blockquote} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function blockquote(state, node) {\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'blockquote',\n    properties: {},\n    children: state.wrap(state.all(node), true)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,UAAUA,CAACC,KAAK,EAAEC,IAAI,EAAE;EACtC;EACA,MAAMC,MAAM,GAAG;IACbC,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,YAAY;IACrBC,UAAU,EAAE,CAAC,CAAC;IACdC,QAAQ,EAAEN,KAAK,CAACO,IAAI,CAACP,KAAK,CAACQ,GAAG,CAACP,IAAI,CAAC,EAAE,IAAI;EAC5C,CAAC;EACDD,KAAK,CAACS,KAAK,CAACR,IAAI,EAAEC,MAAM,CAAC;EACzB,OAAOF,KAAK,CAACU,SAAS,CAACT,IAAI,EAAEC,MAAM,CAAC;AACtC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}