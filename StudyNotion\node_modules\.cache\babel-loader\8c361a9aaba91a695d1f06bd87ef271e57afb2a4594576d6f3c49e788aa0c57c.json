{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\");\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar _objectSpread2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectSpread\"));\nvar _classCallCheck2 = _interopRequireDefault(require(\"@babel/runtime/helpers/classCallCheck\"));\nvar _createClass2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createClass\"));\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"@babel/runtime/helpers/possibleConstructorReturn\"));\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"@babel/runtime/helpers/getPrototypeOf\"));\nvar _assertThisInitialized2 = _interopRequireDefault(require(\"@babel/runtime/helpers/assertThisInitialized\"));\nvar _inherits2 = _interopRequireDefault(require(\"@babel/runtime/helpers/inherits\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _react = _interopRequireWildcard(require(\"react\"));\nvar _classnames = _interopRequireDefault(require(\"classnames\"));\nvar propTypes = {\n  tagName: _propTypes[\"default\"].string,\n  onClick: _propTypes[\"default\"].func.isRequired,\n  onFocus: _propTypes[\"default\"].func,\n  onBlur: _propTypes[\"default\"].func,\n  className: _propTypes[\"default\"].string\n};\nvar defaultProps = {\n  tagName: 'div'\n};\nvar ClickableComponent = /*#__PURE__*/\nfunction (_Component) {\n  (0, _inherits2[\"default\"])(ClickableComponent, _Component);\n  function ClickableComponent(props, context) {\n    var _this;\n    (0, _classCallCheck2[\"default\"])(this, ClickableComponent);\n    _this = (0, _possibleConstructorReturn2[\"default\"])(this, (0, _getPrototypeOf2[\"default\"])(ClickableComponent).call(this, props, context));\n    _this.handleClick = _this.handleClick.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleFocus = _this.handleFocus.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleBlur = _this.handleBlur.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleKeypress = _this.handleKeypress.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    return _this;\n  }\n  (0, _createClass2[\"default\"])(ClickableComponent, [{\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount(e) {\n      this.handleBlur(e);\n    }\n  }, {\n    key: \"handleKeypress\",\n    value: function handleKeypress(event) {\n      // Support Space (32) or Enter (13) key operation to fire a click event\n      if (event.which === 32 || event.which === 13) {\n        event.preventDefault();\n        this.handleClick(event);\n      }\n    }\n  }, {\n    key: \"handleClick\",\n    value: function handleClick(event) {\n      var onClick = this.props.onClick;\n      onClick(event);\n    }\n  }, {\n    key: \"handleFocus\",\n    value: function handleFocus(e) {\n      document.addEventListener('keydown', this.handleKeypress);\n      if (this.props.onFocus) {\n        this.props.onFocus(e);\n      }\n    }\n  }, {\n    key: \"handleBlur\",\n    value: function handleBlur(e) {\n      document.removeEventListener('keydown', this.handleKeypress);\n      if (this.props.onBlur) {\n        this.props.onBlur(e);\n      }\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var Tag = this.props.tagName;\n      var props = (0, _objectSpread2[\"default\"])({}, this.props);\n      delete props.tagName;\n      delete props.className;\n      return _react[\"default\"].createElement(Tag, (0, _extends2[\"default\"])({\n        className: (0, _classnames[\"default\"])(this.props.className),\n        role: \"button\",\n        tabIndex: \"0\",\n        onClick: this.handleClick,\n        onFocus: this.handleFocus,\n        onBlur: this.handleBlur\n      }, props));\n    }\n  }]);\n  return ClickableComponent;\n}(_react.Component);\nexports[\"default\"] = ClickableComponent;\nClickableComponent.propTypes = propTypes;\nClickableComponent.defaultProps = defaultProps;\nClickableComponent.displayName = 'ClickableComponent';", "map": {"version": 3, "names": ["_interopRequireWildcard", "require", "_interopRequireDefault", "Object", "defineProperty", "exports", "value", "_extends2", "_objectSpread2", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_assertThisInitialized2", "_inherits2", "_propTypes", "_react", "_classnames", "propTypes", "tagName", "string", "onClick", "func", "isRequired", "onFocus", "onBlur", "className", "defaultProps", "ClickableComponent", "_Component", "props", "context", "_this", "call", "handleClick", "bind", "handleFocus", "handleBlur", "handleKeypress", "key", "componentWillUnmount", "e", "event", "which", "preventDefault", "document", "addEventListener", "removeEventListener", "render", "Tag", "createElement", "role", "tabIndex", "Component", "displayName"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/video-react/lib/components/ClickableComponent.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\");\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\n\nvar _objectSpread2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectSpread\"));\n\nvar _classCallCheck2 = _interopRequireDefault(require(\"@babel/runtime/helpers/classCallCheck\"));\n\nvar _createClass2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createClass\"));\n\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"@babel/runtime/helpers/possibleConstructorReturn\"));\n\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"@babel/runtime/helpers/getPrototypeOf\"));\n\nvar _assertThisInitialized2 = _interopRequireDefault(require(\"@babel/runtime/helpers/assertThisInitialized\"));\n\nvar _inherits2 = _interopRequireDefault(require(\"@babel/runtime/helpers/inherits\"));\n\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\n\nvar _react = _interopRequireWildcard(require(\"react\"));\n\nvar _classnames = _interopRequireDefault(require(\"classnames\"));\n\nvar propTypes = {\n  tagName: _propTypes[\"default\"].string,\n  onClick: _propTypes[\"default\"].func.isRequired,\n  onFocus: _propTypes[\"default\"].func,\n  onBlur: _propTypes[\"default\"].func,\n  className: _propTypes[\"default\"].string\n};\nvar defaultProps = {\n  tagName: 'div'\n};\n\nvar ClickableComponent =\n/*#__PURE__*/\nfunction (_Component) {\n  (0, _inherits2[\"default\"])(ClickableComponent, _Component);\n\n  function ClickableComponent(props, context) {\n    var _this;\n\n    (0, _classCallCheck2[\"default\"])(this, ClickableComponent);\n    _this = (0, _possibleConstructorReturn2[\"default\"])(this, (0, _getPrototypeOf2[\"default\"])(ClickableComponent).call(this, props, context));\n    _this.handleClick = _this.handleClick.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleFocus = _this.handleFocus.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleBlur = _this.handleBlur.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleKeypress = _this.handleKeypress.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    return _this;\n  }\n\n  (0, _createClass2[\"default\"])(ClickableComponent, [{\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount(e) {\n      this.handleBlur(e);\n    }\n  }, {\n    key: \"handleKeypress\",\n    value: function handleKeypress(event) {\n      // Support Space (32) or Enter (13) key operation to fire a click event\n      if (event.which === 32 || event.which === 13) {\n        event.preventDefault();\n        this.handleClick(event);\n      }\n    }\n  }, {\n    key: \"handleClick\",\n    value: function handleClick(event) {\n      var onClick = this.props.onClick;\n      onClick(event);\n    }\n  }, {\n    key: \"handleFocus\",\n    value: function handleFocus(e) {\n      document.addEventListener('keydown', this.handleKeypress);\n\n      if (this.props.onFocus) {\n        this.props.onFocus(e);\n      }\n    }\n  }, {\n    key: \"handleBlur\",\n    value: function handleBlur(e) {\n      document.removeEventListener('keydown', this.handleKeypress);\n\n      if (this.props.onBlur) {\n        this.props.onBlur(e);\n      }\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var Tag = this.props.tagName;\n      var props = (0, _objectSpread2[\"default\"])({}, this.props);\n      delete props.tagName;\n      delete props.className;\n      return _react[\"default\"].createElement(Tag, (0, _extends2[\"default\"])({\n        className: (0, _classnames[\"default\"])(this.props.className),\n        role: \"button\",\n        tabIndex: \"0\",\n        onClick: this.handleClick,\n        onFocus: this.handleFocus,\n        onBlur: this.handleBlur\n      }, props));\n    }\n  }]);\n  return ClickableComponent;\n}(_react.Component);\n\nexports[\"default\"] = ClickableComponent;\nClickableComponent.propTypes = propTypes;\nClickableComponent.defaultProps = defaultProps;\nClickableComponent.displayName = 'ClickableComponent';"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,uBAAuB,GAAGC,OAAO,CAAC,+CAA+C,CAAC;AAEtF,IAAIC,sBAAsB,GAAGD,OAAO,CAAC,8CAA8C,CAAC;AAEpFE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;AAE3B,IAAIE,SAAS,GAAGL,sBAAsB,CAACD,OAAO,CAAC,gCAAgC,CAAC,CAAC;AAEjF,IAAIO,cAAc,GAAGN,sBAAsB,CAACD,OAAO,CAAC,qCAAqC,CAAC,CAAC;AAE3F,IAAIQ,gBAAgB,GAAGP,sBAAsB,CAACD,OAAO,CAAC,uCAAuC,CAAC,CAAC;AAE/F,IAAIS,aAAa,GAAGR,sBAAsB,CAACD,OAAO,CAAC,oCAAoC,CAAC,CAAC;AAEzF,IAAIU,2BAA2B,GAAGT,sBAAsB,CAACD,OAAO,CAAC,kDAAkD,CAAC,CAAC;AAErH,IAAIW,gBAAgB,GAAGV,sBAAsB,CAACD,OAAO,CAAC,uCAAuC,CAAC,CAAC;AAE/F,IAAIY,uBAAuB,GAAGX,sBAAsB,CAACD,OAAO,CAAC,8CAA8C,CAAC,CAAC;AAE7G,IAAIa,UAAU,GAAGZ,sBAAsB,CAACD,OAAO,CAAC,iCAAiC,CAAC,CAAC;AAEnF,IAAIc,UAAU,GAAGb,sBAAsB,CAACD,OAAO,CAAC,YAAY,CAAC,CAAC;AAE9D,IAAIe,MAAM,GAAGhB,uBAAuB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AAEtD,IAAIgB,WAAW,GAAGf,sBAAsB,CAACD,OAAO,CAAC,YAAY,CAAC,CAAC;AAE/D,IAAIiB,SAAS,GAAG;EACdC,OAAO,EAAEJ,UAAU,CAAC,SAAS,CAAC,CAACK,MAAM;EACrCC,OAAO,EAAEN,UAAU,CAAC,SAAS,CAAC,CAACO,IAAI,CAACC,UAAU;EAC9CC,OAAO,EAAET,UAAU,CAAC,SAAS,CAAC,CAACO,IAAI;EACnCG,MAAM,EAAEV,UAAU,CAAC,SAAS,CAAC,CAACO,IAAI;EAClCI,SAAS,EAAEX,UAAU,CAAC,SAAS,CAAC,CAACK;AACnC,CAAC;AACD,IAAIO,YAAY,GAAG;EACjBR,OAAO,EAAE;AACX,CAAC;AAED,IAAIS,kBAAkB,GACtB;AACA,UAAUC,UAAU,EAAE;EACpB,CAAC,CAAC,EAAEf,UAAU,CAAC,SAAS,CAAC,EAAEc,kBAAkB,EAAEC,UAAU,CAAC;EAE1D,SAASD,kBAAkBA,CAACE,KAAK,EAAEC,OAAO,EAAE;IAC1C,IAAIC,KAAK;IAET,CAAC,CAAC,EAAEvB,gBAAgB,CAAC,SAAS,CAAC,EAAE,IAAI,EAAEmB,kBAAkB,CAAC;IAC1DI,KAAK,GAAG,CAAC,CAAC,EAAErB,2BAA2B,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAEC,gBAAgB,CAAC,SAAS,CAAC,EAAEgB,kBAAkB,CAAC,CAACK,IAAI,CAAC,IAAI,EAAEH,KAAK,EAAEC,OAAO,CAAC,CAAC;IAC1IC,KAAK,CAACE,WAAW,GAAGF,KAAK,CAACE,WAAW,CAACC,IAAI,CAAC,CAAC,CAAC,EAAEtB,uBAAuB,CAAC,SAAS,CAAC,EAAEmB,KAAK,CAAC,CAAC;IAC1FA,KAAK,CAACI,WAAW,GAAGJ,KAAK,CAACI,WAAW,CAACD,IAAI,CAAC,CAAC,CAAC,EAAEtB,uBAAuB,CAAC,SAAS,CAAC,EAAEmB,KAAK,CAAC,CAAC;IAC1FA,KAAK,CAACK,UAAU,GAAGL,KAAK,CAACK,UAAU,CAACF,IAAI,CAAC,CAAC,CAAC,EAAEtB,uBAAuB,CAAC,SAAS,CAAC,EAAEmB,KAAK,CAAC,CAAC;IACxFA,KAAK,CAACM,cAAc,GAAGN,KAAK,CAACM,cAAc,CAACH,IAAI,CAAC,CAAC,CAAC,EAAEtB,uBAAuB,CAAC,SAAS,CAAC,EAAEmB,KAAK,CAAC,CAAC;IAChG,OAAOA,KAAK;EACd;EAEA,CAAC,CAAC,EAAEtB,aAAa,CAAC,SAAS,CAAC,EAAEkB,kBAAkB,EAAE,CAAC;IACjDW,GAAG,EAAE,sBAAsB;IAC3BjC,KAAK,EAAE,SAASkC,oBAAoBA,CAACC,CAAC,EAAE;MACtC,IAAI,CAACJ,UAAU,CAACI,CAAC,CAAC;IACpB;EACF,CAAC,EAAE;IACDF,GAAG,EAAE,gBAAgB;IACrBjC,KAAK,EAAE,SAASgC,cAAcA,CAACI,KAAK,EAAE;MACpC;MACA,IAAIA,KAAK,CAACC,KAAK,KAAK,EAAE,IAAID,KAAK,CAACC,KAAK,KAAK,EAAE,EAAE;QAC5CD,KAAK,CAACE,cAAc,EAAE;QACtB,IAAI,CAACV,WAAW,CAACQ,KAAK,CAAC;MACzB;IACF;EACF,CAAC,EAAE;IACDH,GAAG,EAAE,aAAa;IAClBjC,KAAK,EAAE,SAAS4B,WAAWA,CAACQ,KAAK,EAAE;MACjC,IAAIrB,OAAO,GAAG,IAAI,CAACS,KAAK,CAACT,OAAO;MAChCA,OAAO,CAACqB,KAAK,CAAC;IAChB;EACF,CAAC,EAAE;IACDH,GAAG,EAAE,aAAa;IAClBjC,KAAK,EAAE,SAAS8B,WAAWA,CAACK,CAAC,EAAE;MAC7BI,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAACR,cAAc,CAAC;MAEzD,IAAI,IAAI,CAACR,KAAK,CAACN,OAAO,EAAE;QACtB,IAAI,CAACM,KAAK,CAACN,OAAO,CAACiB,CAAC,CAAC;MACvB;IACF;EACF,CAAC,EAAE;IACDF,GAAG,EAAE,YAAY;IACjBjC,KAAK,EAAE,SAAS+B,UAAUA,CAACI,CAAC,EAAE;MAC5BI,QAAQ,CAACE,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAACT,cAAc,CAAC;MAE5D,IAAI,IAAI,CAACR,KAAK,CAACL,MAAM,EAAE;QACrB,IAAI,CAACK,KAAK,CAACL,MAAM,CAACgB,CAAC,CAAC;MACtB;IACF;EACF,CAAC,EAAE;IACDF,GAAG,EAAE,QAAQ;IACbjC,KAAK,EAAE,SAAS0C,MAAMA,CAAA,EAAG;MACvB,IAAIC,GAAG,GAAG,IAAI,CAACnB,KAAK,CAACX,OAAO;MAC5B,IAAIW,KAAK,GAAG,CAAC,CAAC,EAAEtB,cAAc,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAACsB,KAAK,CAAC;MAC1D,OAAOA,KAAK,CAACX,OAAO;MACpB,OAAOW,KAAK,CAACJ,SAAS;MACtB,OAAOV,MAAM,CAAC,SAAS,CAAC,CAACkC,aAAa,CAACD,GAAG,EAAE,CAAC,CAAC,EAAE1C,SAAS,CAAC,SAAS,CAAC,EAAE;QACpEmB,SAAS,EAAE,CAAC,CAAC,EAAET,WAAW,CAAC,SAAS,CAAC,EAAE,IAAI,CAACa,KAAK,CAACJ,SAAS,CAAC;QAC5DyB,IAAI,EAAE,QAAQ;QACdC,QAAQ,EAAE,GAAG;QACb/B,OAAO,EAAE,IAAI,CAACa,WAAW;QACzBV,OAAO,EAAE,IAAI,CAACY,WAAW;QACzBX,MAAM,EAAE,IAAI,CAACY;MACf,CAAC,EAAEP,KAAK,CAAC,CAAC;IACZ;EACF,CAAC,CAAC,CAAC;EACH,OAAOF,kBAAkB;AAC3B,CAAC,CAACZ,MAAM,CAACqC,SAAS,CAAC;AAEnBhD,OAAO,CAAC,SAAS,CAAC,GAAGuB,kBAAkB;AACvCA,kBAAkB,CAACV,SAAS,GAAGA,SAAS;AACxCU,kBAAkB,CAACD,YAAY,GAAGA,YAAY;AAC9CC,kBAAkB,CAAC0B,WAAW,GAAG,oBAAoB"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}