{"ast": null, "code": "import * as React from 'react';\nimport React__default from 'react';\nvar isCheckBoxInput = element => element.type === 'checkbox';\nvar isDateObject = value => value instanceof Date;\nvar isNullOrUndefined = value => value == null;\nconst isObjectType = value => typeof value === 'object';\nvar isObject = value => !isNullOrUndefined(value) && !Array.isArray(value) && isObjectType(value) && !isDateObject(value);\nvar getEventValue = event => isObject(event) && event.target ? isCheckBoxInput(event.target) ? event.target.checked : event.target.value : event;\nvar getNodeParentName = name => name.substring(0, name.search(/\\.\\d+(\\.|$)/)) || name;\nvar isNameInFieldArray = (names, name) => names.has(getNodeParentName(name));\nvar isPlainObject = tempObject => {\n  const prototypeCopy = tempObject.constructor && tempObject.constructor.prototype;\n  return isObject(prototypeCopy) && prototypeCopy.hasOwnProperty('isPrototypeOf');\n};\nvar isWeb = typeof window !== 'undefined' && typeof window.HTMLElement !== 'undefined' && typeof document !== 'undefined';\nfunction cloneObject(data) {\n  let copy;\n  const isArray = Array.isArray(data);\n  if (data instanceof Date) {\n    copy = new Date(data);\n  } else if (data instanceof Set) {\n    copy = new Set(data);\n  } else if (!(isWeb && (data instanceof Blob || data instanceof FileList)) && (isArray || isObject(data))) {\n    copy = isArray ? [] : {};\n    if (!isArray && !isPlainObject(data)) {\n      copy = data;\n    } else {\n      for (const key in data) {\n        if (data.hasOwnProperty(key)) {\n          copy[key] = cloneObject(data[key]);\n        }\n      }\n    }\n  } else {\n    return data;\n  }\n  return copy;\n}\nvar compact = value => Array.isArray(value) ? value.filter(Boolean) : [];\nvar isUndefined = val => val === undefined;\nvar get = (obj, path, defaultValue) => {\n  if (!path || !isObject(obj)) {\n    return defaultValue;\n  }\n  const result = compact(path.split(/[,[\\].]+?/)).reduce((result, key) => isNullOrUndefined(result) ? result : result[key], obj);\n  return isUndefined(result) || result === obj ? isUndefined(obj[path]) ? defaultValue : obj[path] : result;\n};\nconst EVENTS = {\n  BLUR: 'blur',\n  FOCUS_OUT: 'focusout',\n  CHANGE: 'change'\n};\nconst VALIDATION_MODE = {\n  onBlur: 'onBlur',\n  onChange: 'onChange',\n  onSubmit: 'onSubmit',\n  onTouched: 'onTouched',\n  all: 'all'\n};\nconst INPUT_VALIDATION_RULES = {\n  max: 'max',\n  min: 'min',\n  maxLength: 'maxLength',\n  minLength: 'minLength',\n  pattern: 'pattern',\n  required: 'required',\n  validate: 'validate'\n};\nconst HookFormContext = React__default.createContext(null);\n/**\n * This custom hook allows you to access the form context. useFormContext is intended to be used in deeply nested structures, where it would become inconvenient to pass the context as a prop. To be used with {@link FormProvider}.\n *\n * @remarks\n * [API](https://react-hook-form.com/api/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @returns return all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nconst useFormContext = () => React__default.useContext(HookFormContext);\n/**\n * A provider component that propagates the `useForm` methods to all children components via [React Context](https://reactjs.org/docs/context.html) API. To be used with {@link useFormContext}.\n *\n * @remarks\n * [API](https://react-hook-form.com/api/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @param props - all useFrom methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nconst FormProvider = props => {\n  const {\n    children,\n    ...data\n  } = props;\n  return React__default.createElement(HookFormContext.Provider, {\n    value: data\n  }, children);\n};\nvar getProxyFormState = function (formState, control, localProxyFormState) {\n  let isRoot = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : true;\n  const result = {\n    defaultValues: control._defaultValues\n  };\n  for (const key in formState) {\n    Object.defineProperty(result, key, {\n      get: () => {\n        const _key = key;\n        if (control._proxyFormState[_key] !== VALIDATION_MODE.all) {\n          control._proxyFormState[_key] = !isRoot || VALIDATION_MODE.all;\n        }\n        localProxyFormState && (localProxyFormState[_key] = true);\n        return formState[_key];\n      }\n    });\n  }\n  return result;\n};\nvar isEmptyObject = value => isObject(value) && !Object.keys(value).length;\nvar shouldRenderFormState = (formStateData, _proxyFormState, updateFormState, isRoot) => {\n  updateFormState(formStateData);\n  const {\n    name,\n    ...formState\n  } = formStateData;\n  return isEmptyObject(formState) || Object.keys(formState).length >= Object.keys(_proxyFormState).length || Object.keys(formState).find(key => _proxyFormState[key] === (!isRoot || VALIDATION_MODE.all));\n};\nvar convertToArrayPayload = value => Array.isArray(value) ? value : [value];\nvar shouldSubscribeByName = (name, signalName, exact) => exact && signalName ? name === signalName : !name || !signalName || name === signalName || convertToArrayPayload(name).some(currentName => currentName && (currentName.startsWith(signalName) || signalName.startsWith(currentName)));\nfunction useSubscribe(props) {\n  const _props = React__default.useRef(props);\n  _props.current = props;\n  React__default.useEffect(() => {\n    const subscription = !props.disabled && _props.current.subject && _props.current.subject.subscribe({\n      next: _props.current.next\n    });\n    return () => {\n      subscription && subscription.unsubscribe();\n    };\n  }, [props.disabled]);\n}\n\n/**\n * This custom hook allows you to subscribe to each form state, and isolate the re-render at the custom hook level. It has its scope in terms of form state subscription, so it would not affect other useFormState and useForm. Using this hook can reduce the re-render impact on large and complex form application.\n *\n * @remarks\n * [API](https://react-hook-form.com/api/useformstate) • [Demo](https://codesandbox.io/s/useformstate-75xly)\n *\n * @param props - include options on specify fields to subscribe. {@link UseFormStateReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, control } = useForm({\n *     defaultValues: {\n *     firstName: \"firstName\"\n *   }});\n *   const { dirtyFields } = useFormState({\n *     control\n *   });\n *   const onSubmit = (data) => console.log(data);\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input {...register(\"firstName\")} placeholder=\"First Name\" />\n *       {dirtyFields.firstName && <p>Field is dirty.</p>}\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nfunction useFormState(props) {\n  const methods = useFormContext();\n  const {\n    control = methods.control,\n    disabled,\n    name,\n    exact\n  } = props || {};\n  const [formState, updateFormState] = React__default.useState(control._formState);\n  const _mounted = React__default.useRef(true);\n  const _localProxyFormState = React__default.useRef({\n    isDirty: false,\n    isLoading: false,\n    dirtyFields: false,\n    touchedFields: false,\n    isValidating: false,\n    isValid: false,\n    errors: false\n  });\n  const _name = React__default.useRef(name);\n  _name.current = name;\n  useSubscribe({\n    disabled,\n    next: value => _mounted.current && shouldSubscribeByName(_name.current, value.name, exact) && shouldRenderFormState(value, _localProxyFormState.current, control._updateFormState) && updateFormState({\n      ...control._formState,\n      ...value\n    }),\n    subject: control._subjects.state\n  });\n  React__default.useEffect(() => {\n    _mounted.current = true;\n    _localProxyFormState.current.isValid && control._updateValid(true);\n    return () => {\n      _mounted.current = false;\n    };\n  }, [control]);\n  return getProxyFormState(formState, control, _localProxyFormState.current, false);\n}\nvar isString = value => typeof value === 'string';\nvar generateWatchOutput = (names, _names, formValues, isGlobal, defaultValue) => {\n  if (isString(names)) {\n    isGlobal && _names.watch.add(names);\n    return get(formValues, names, defaultValue);\n  }\n  if (Array.isArray(names)) {\n    return names.map(fieldName => (isGlobal && _names.watch.add(fieldName), get(formValues, fieldName)));\n  }\n  isGlobal && (_names.watchAll = true);\n  return formValues;\n};\n\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/api/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @example\n * ```tsx\n * const { watch } = useForm();\n * const values = useWatch({\n *   name: \"fieldName\"\n *   control,\n * })\n * ```\n */\nfunction useWatch(props) {\n  const methods = useFormContext();\n  const {\n    control = methods.control,\n    name,\n    defaultValue,\n    disabled,\n    exact\n  } = props || {};\n  const _name = React__default.useRef(name);\n  _name.current = name;\n  useSubscribe({\n    disabled,\n    subject: control._subjects.values,\n    next: formState => {\n      if (shouldSubscribeByName(_name.current, formState.name, exact)) {\n        updateValue(cloneObject(generateWatchOutput(_name.current, control._names, formState.values || control._formValues, false, defaultValue)));\n      }\n    }\n  });\n  const [value, updateValue] = React__default.useState(control._getWatch(name, defaultValue));\n  React__default.useEffect(() => control._removeUnmounted());\n  return value;\n}\nvar isKey = value => /^\\w*$/.test(value);\nvar stringToPath = input => compact(input.replace(/[\"|']|\\]/g, '').split(/\\.|\\[/));\nfunction set(object, path, value) {\n  let index = -1;\n  const tempPath = isKey(path) ? [path] : stringToPath(path);\n  const length = tempPath.length;\n  const lastIndex = length - 1;\n  while (++index < length) {\n    const key = tempPath[index];\n    let newValue = value;\n    if (index !== lastIndex) {\n      const objValue = object[key];\n      newValue = isObject(objValue) || Array.isArray(objValue) ? objValue : !isNaN(+tempPath[index + 1]) ? [] : {};\n    }\n    object[key] = newValue;\n    object = object[key];\n  }\n  return object;\n}\n\n/**\n * Custom hook to work with controlled component, this function provide you with both form and field level state. Re-render is isolated at the hook level.\n *\n * @remarks\n * [API](https://react-hook-form.com/api/usecontroller) • [Demo](https://codesandbox.io/s/usecontroller-0o8px)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns field properties, field and form state. {@link UseControllerReturn}\n *\n * @example\n * ```tsx\n * function Input(props) {\n *   const { field, fieldState, formState } = useController(props);\n *   return (\n *     <div>\n *       <input {...field} placeholder={props.name} />\n *       <p>{fieldState.isTouched && \"Touched\"}</p>\n *       <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *     </div>\n *   );\n * }\n * ```\n */\nfunction useController(props) {\n  const methods = useFormContext();\n  const {\n    name,\n    control = methods.control,\n    shouldUnregister\n  } = props;\n  const isArrayField = isNameInFieldArray(control._names.array, name);\n  const value = useWatch({\n    control,\n    name,\n    defaultValue: get(control._formValues, name, get(control._defaultValues, name, props.defaultValue)),\n    exact: true\n  });\n  const formState = useFormState({\n    control,\n    name\n  });\n  const _registerProps = React__default.useRef(control.register(name, {\n    ...props.rules,\n    value\n  }));\n  _registerProps.current = control.register(name, props.rules);\n  React__default.useEffect(() => {\n    const _shouldUnregisterField = control._options.shouldUnregister || shouldUnregister;\n    const updateMounted = (name, value) => {\n      const field = get(control._fields, name);\n      if (field) {\n        field._f.mount = value;\n      }\n    };\n    updateMounted(name, true);\n    if (_shouldUnregisterField) {\n      const value = cloneObject(get(control._options.defaultValues, name));\n      set(control._defaultValues, name, value);\n      if (isUndefined(get(control._formValues, name))) {\n        set(control._formValues, name, value);\n      }\n    }\n    return () => {\n      (isArrayField ? _shouldUnregisterField && !control._state.action : _shouldUnregisterField) ? control.unregister(name) : updateMounted(name, false);\n    };\n  }, [name, control, isArrayField, shouldUnregister]);\n  return {\n    field: {\n      name,\n      value,\n      onChange: React__default.useCallback(event => _registerProps.current.onChange({\n        target: {\n          value: getEventValue(event),\n          name: name\n        },\n        type: EVENTS.CHANGE\n      }), [name]),\n      onBlur: React__default.useCallback(() => _registerProps.current.onBlur({\n        target: {\n          value: get(control._formValues, name),\n          name: name\n        },\n        type: EVENTS.BLUR\n      }), [name, control]),\n      ref: elm => {\n        const field = get(control._fields, name);\n        if (field && elm) {\n          field._f.ref = {\n            focus: () => elm.focus(),\n            select: () => elm.select(),\n            setCustomValidity: message => elm.setCustomValidity(message),\n            reportValidity: () => elm.reportValidity()\n          };\n        }\n      }\n    },\n    formState,\n    fieldState: Object.defineProperties({}, {\n      invalid: {\n        enumerable: true,\n        get: () => !!get(formState.errors, name)\n      },\n      isDirty: {\n        enumerable: true,\n        get: () => !!get(formState.dirtyFields, name)\n      },\n      isTouched: {\n        enumerable: true,\n        get: () => !!get(formState.touchedFields, name)\n      },\n      error: {\n        enumerable: true,\n        get: () => get(formState.errors, name)\n      }\n    })\n  };\n}\n\n/**\n * Component based on `useController` hook to work with controlled component.\n *\n * @remarks\n * [API](https://react-hook-form.com/api/usecontroller/controller) • [Demo](https://codesandbox.io/s/react-hook-form-v6-controller-ts-jwyzw) • [Video](https://www.youtube.com/watch?v=N2UNk_UCVyA)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns provide field handler functions, field and form state.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control } = useForm<FormValues>({\n *     defaultValues: {\n *       test: \"\"\n *     }\n *   });\n *\n *   return (\n *     <form>\n *       <Controller\n *         control={control}\n *         name=\"test\"\n *         render={({ field: { onChange, onBlur, value, ref }, formState, fieldState }) => (\n *           <>\n *             <input\n *               onChange={onChange} // send value to hook form\n *               onBlur={onBlur} // notify when input is touched\n *               value={value} // return updated value\n *               ref={ref} // set ref for focus management\n *             />\n *             <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *             <p>{fieldState.isTouched ? \"touched\" : \"\"}</p>\n *           </>\n *         )}\n *       />\n *     </form>\n *   );\n * }\n * ```\n */\nconst Controller = props => props.render(useController(props));\nconst POST_REQUEST = 'post';\n/**\n * Form component to manage submission.\n *\n * @param props - to setup submission detail. {@link FormProps}\n *\n * @returns form component or headless render prop.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control, formState: { errors } } = useForm();\n *\n *   return (\n *     <Form action=\"/api\" control={control}>\n *       <input {...register(\"name\")} />\n *       <p>{errors?.root?.server && 'Server error'}</p>\n *       <button>Submit</button>\n *     </Form>\n *   );\n * }\n * ```\n */\nfunction Form(props) {\n  const methods = useFormContext();\n  const [mounted, setMounted] = React.useState(false);\n  const {\n    control = methods.control,\n    onSubmit,\n    children,\n    action,\n    method = POST_REQUEST,\n    headers,\n    encType,\n    onError,\n    render,\n    onSuccess,\n    validateStatus,\n    ...rest\n  } = props;\n  const submit = async event => {\n    let hasError = false;\n    let type = '';\n    await control.handleSubmit(async data => {\n      const formData = new FormData();\n      let formDataJson = '';\n      try {\n        formDataJson = JSON.stringify(data);\n      } catch (_a) {}\n      for (const name of control._names.mount) {\n        formData.append(name, get(data, name));\n      }\n      if (onSubmit) {\n        onSubmit({\n          data,\n          event,\n          method,\n          formData,\n          formDataJson\n        });\n      }\n      if (action) {\n        try {\n          const shouldStringifySubmissionData = [headers && headers['Content-Type'], encType].some(value => value && value.includes('json'));\n          const response = await fetch(action, {\n            method,\n            headers: {\n              ...headers,\n              ...(encType ? {\n                'Content-Type': encType\n              } : {})\n            },\n            body: shouldStringifySubmissionData ? formDataJson : formData\n          });\n          if (response && (validateStatus ? !validateStatus(response.status) : response.status < 200 || response.status >= 300)) {\n            hasError = true;\n            onError && onError({\n              response\n            });\n            type = String(response.status);\n          } else {\n            onSuccess && onSuccess({\n              response\n            });\n          }\n        } catch (error) {\n          hasError = true;\n          onError && onError({\n            error\n          });\n        }\n      }\n    })(event);\n    if (hasError && props.control) {\n      props.control._subjects.state.next({\n        isSubmitSuccessful: false\n      });\n      props.control.setError('root.server', {\n        type\n      });\n    }\n  };\n  React.useEffect(() => {\n    setMounted(true);\n  }, []);\n  return render ? React.createElement(React.Fragment, null, render({\n    submit\n  })) : React.createElement(\"form\", {\n    noValidate: mounted,\n    action: action,\n    method: method,\n    encType: encType,\n    onSubmit: submit,\n    ...rest\n  }, children);\n}\nvar appendErrors = (name, validateAllFieldCriteria, errors, type, message) => validateAllFieldCriteria ? {\n  ...errors[name],\n  types: {\n    ...(errors[name] && errors[name].types ? errors[name].types : {}),\n    [type]: message || true\n  }\n} : {};\nconst focusFieldBy = (fields, callback, fieldsNames) => {\n  for (const key of fieldsNames || Object.keys(fields)) {\n    const field = get(fields, key);\n    if (field) {\n      const {\n        _f,\n        ...currentField\n      } = field;\n      if (_f && callback(_f.name)) {\n        if (_f.ref.focus) {\n          _f.ref.focus();\n          break;\n        } else if (_f.refs && _f.refs[0].focus) {\n          _f.refs[0].focus();\n          break;\n        }\n      } else if (isObject(currentField)) {\n        focusFieldBy(currentField, callback);\n      }\n    }\n  }\n};\nvar generateId = () => {\n  const d = typeof performance === 'undefined' ? Date.now() : performance.now() * 1000;\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, c => {\n    const r = (Math.random() * 16 + d) % 16 | 0;\n    return (c == 'x' ? r : r & 0x3 | 0x8).toString(16);\n  });\n};\nvar getFocusFieldName = function (name, index) {\n  let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  return options.shouldFocus || isUndefined(options.shouldFocus) ? options.focusName || `${name}.${isUndefined(options.focusIndex) ? index : options.focusIndex}.` : '';\n};\nvar getValidationModes = mode => ({\n  isOnSubmit: !mode || mode === VALIDATION_MODE.onSubmit,\n  isOnBlur: mode === VALIDATION_MODE.onBlur,\n  isOnChange: mode === VALIDATION_MODE.onChange,\n  isOnAll: mode === VALIDATION_MODE.all,\n  isOnTouch: mode === VALIDATION_MODE.onTouched\n});\nvar isWatched = (name, _names, isBlurEvent) => !isBlurEvent && (_names.watchAll || _names.watch.has(name) || [..._names.watch].some(watchName => name.startsWith(watchName) && /^\\.\\w+/.test(name.slice(watchName.length))));\nvar updateFieldArrayRootError = (errors, error, name) => {\n  const fieldArrayErrors = compact(get(errors, name));\n  set(fieldArrayErrors, 'root', error[name]);\n  set(errors, name, fieldArrayErrors);\n  return errors;\n};\nvar isBoolean = value => typeof value === 'boolean';\nvar isFileInput = element => element.type === 'file';\nvar isFunction = value => typeof value === 'function';\nvar isHTMLElement = value => {\n  if (!isWeb) {\n    return false;\n  }\n  const owner = value ? value.ownerDocument : 0;\n  return value instanceof (owner && owner.defaultView ? owner.defaultView.HTMLElement : HTMLElement);\n};\nvar isMessage = value => isString(value);\nvar isRadioInput = element => element.type === 'radio';\nvar isRegex = value => value instanceof RegExp;\nconst defaultResult = {\n  value: false,\n  isValid: false\n};\nconst validResult = {\n  value: true,\n  isValid: true\n};\nvar getCheckboxValue = options => {\n  if (Array.isArray(options)) {\n    if (options.length > 1) {\n      const values = options.filter(option => option && option.checked && !option.disabled).map(option => option.value);\n      return {\n        value: values,\n        isValid: !!values.length\n      };\n    }\n    return options[0].checked && !options[0].disabled ?\n    // @ts-expect-error expected to work in the browser\n    options[0].attributes && !isUndefined(options[0].attributes.value) ? isUndefined(options[0].value) || options[0].value === '' ? validResult : {\n      value: options[0].value,\n      isValid: true\n    } : validResult : defaultResult;\n  }\n  return defaultResult;\n};\nconst defaultReturn = {\n  isValid: false,\n  value: null\n};\nvar getRadioValue = options => Array.isArray(options) ? options.reduce((previous, option) => option && option.checked && !option.disabled ? {\n  isValid: true,\n  value: option.value\n} : previous, defaultReturn) : defaultReturn;\nfunction getValidateError(result, ref) {\n  let type = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'validate';\n  if (isMessage(result) || Array.isArray(result) && result.every(isMessage) || isBoolean(result) && !result) {\n    return {\n      type,\n      message: isMessage(result) ? result : '',\n      ref\n    };\n  }\n}\nvar getValueAndMessage = validationData => isObject(validationData) && !isRegex(validationData) ? validationData : {\n  value: validationData,\n  message: ''\n};\nvar validateField = async (field, formValues, validateAllFieldCriteria, shouldUseNativeValidation, isFieldArray) => {\n  const {\n    ref,\n    refs,\n    required,\n    maxLength,\n    minLength,\n    min,\n    max,\n    pattern,\n    validate,\n    name,\n    valueAsNumber,\n    mount,\n    disabled\n  } = field._f;\n  const inputValue = get(formValues, name);\n  if (!mount || disabled) {\n    return {};\n  }\n  const inputRef = refs ? refs[0] : ref;\n  const setCustomValidity = message => {\n    if (shouldUseNativeValidation && inputRef.reportValidity) {\n      inputRef.setCustomValidity(isBoolean(message) ? '' : message || '');\n      inputRef.reportValidity();\n    }\n  };\n  const error = {};\n  const isRadio = isRadioInput(ref);\n  const isCheckBox = isCheckBoxInput(ref);\n  const isRadioOrCheckbox = isRadio || isCheckBox;\n  const isEmpty = (valueAsNumber || isFileInput(ref)) && isUndefined(ref.value) && isUndefined(inputValue) || isHTMLElement(ref) && ref.value === '' || inputValue === '' || Array.isArray(inputValue) && !inputValue.length;\n  const appendErrorsCurry = appendErrors.bind(null, name, validateAllFieldCriteria, error);\n  const getMinMaxMessage = function (exceedMax, maxLengthMessage, minLengthMessage) {\n    let maxType = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : INPUT_VALIDATION_RULES.maxLength;\n    let minType = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : INPUT_VALIDATION_RULES.minLength;\n    const message = exceedMax ? maxLengthMessage : minLengthMessage;\n    error[name] = {\n      type: exceedMax ? maxType : minType,\n      message,\n      ref,\n      ...appendErrorsCurry(exceedMax ? maxType : minType, message)\n    };\n  };\n  if (isFieldArray ? !Array.isArray(inputValue) || !inputValue.length : required && (!isRadioOrCheckbox && (isEmpty || isNullOrUndefined(inputValue)) || isBoolean(inputValue) && !inputValue || isCheckBox && !getCheckboxValue(refs).isValid || isRadio && !getRadioValue(refs).isValid)) {\n    const {\n      value,\n      message\n    } = isMessage(required) ? {\n      value: !!required,\n      message: required\n    } : getValueAndMessage(required);\n    if (value) {\n      error[name] = {\n        type: INPUT_VALIDATION_RULES.required,\n        message,\n        ref: inputRef,\n        ...appendErrorsCurry(INPUT_VALIDATION_RULES.required, message)\n      };\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(message);\n        return error;\n      }\n    }\n  }\n  if (!isEmpty && (!isNullOrUndefined(min) || !isNullOrUndefined(max))) {\n    let exceedMax;\n    let exceedMin;\n    const maxOutput = getValueAndMessage(max);\n    const minOutput = getValueAndMessage(min);\n    if (!isNullOrUndefined(inputValue) && !isNaN(inputValue)) {\n      const valueNumber = ref.valueAsNumber || (inputValue ? +inputValue : inputValue);\n      if (!isNullOrUndefined(maxOutput.value)) {\n        exceedMax = valueNumber > maxOutput.value;\n      }\n      if (!isNullOrUndefined(minOutput.value)) {\n        exceedMin = valueNumber < minOutput.value;\n      }\n    } else {\n      const valueDate = ref.valueAsDate || new Date(inputValue);\n      const convertTimeToDate = time => new Date(new Date().toDateString() + ' ' + time);\n      const isTime = ref.type == 'time';\n      const isWeek = ref.type == 'week';\n      if (isString(maxOutput.value) && inputValue) {\n        exceedMax = isTime ? convertTimeToDate(inputValue) > convertTimeToDate(maxOutput.value) : isWeek ? inputValue > maxOutput.value : valueDate > new Date(maxOutput.value);\n      }\n      if (isString(minOutput.value) && inputValue) {\n        exceedMin = isTime ? convertTimeToDate(inputValue) < convertTimeToDate(minOutput.value) : isWeek ? inputValue < minOutput.value : valueDate < new Date(minOutput.value);\n      }\n    }\n    if (exceedMax || exceedMin) {\n      getMinMaxMessage(!!exceedMax, maxOutput.message, minOutput.message, INPUT_VALIDATION_RULES.max, INPUT_VALIDATION_RULES.min);\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(error[name].message);\n        return error;\n      }\n    }\n  }\n  if ((maxLength || minLength) && !isEmpty && (isString(inputValue) || isFieldArray && Array.isArray(inputValue))) {\n    const maxLengthOutput = getValueAndMessage(maxLength);\n    const minLengthOutput = getValueAndMessage(minLength);\n    const exceedMax = !isNullOrUndefined(maxLengthOutput.value) && inputValue.length > +maxLengthOutput.value;\n    const exceedMin = !isNullOrUndefined(minLengthOutput.value) && inputValue.length < +minLengthOutput.value;\n    if (exceedMax || exceedMin) {\n      getMinMaxMessage(exceedMax, maxLengthOutput.message, minLengthOutput.message);\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(error[name].message);\n        return error;\n      }\n    }\n  }\n  if (pattern && !isEmpty && isString(inputValue)) {\n    const {\n      value: patternValue,\n      message\n    } = getValueAndMessage(pattern);\n    if (isRegex(patternValue) && !inputValue.match(patternValue)) {\n      error[name] = {\n        type: INPUT_VALIDATION_RULES.pattern,\n        message,\n        ref,\n        ...appendErrorsCurry(INPUT_VALIDATION_RULES.pattern, message)\n      };\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(message);\n        return error;\n      }\n    }\n  }\n  if (validate) {\n    if (isFunction(validate)) {\n      const result = await validate(inputValue, formValues);\n      const validateError = getValidateError(result, inputRef);\n      if (validateError) {\n        error[name] = {\n          ...validateError,\n          ...appendErrorsCurry(INPUT_VALIDATION_RULES.validate, validateError.message)\n        };\n        if (!validateAllFieldCriteria) {\n          setCustomValidity(validateError.message);\n          return error;\n        }\n      }\n    } else if (isObject(validate)) {\n      let validationResult = {};\n      for (const key in validate) {\n        if (!isEmptyObject(validationResult) && !validateAllFieldCriteria) {\n          break;\n        }\n        const validateError = getValidateError(await validate[key](inputValue, formValues), inputRef, key);\n        if (validateError) {\n          validationResult = {\n            ...validateError,\n            ...appendErrorsCurry(key, validateError.message)\n          };\n          setCustomValidity(validateError.message);\n          if (validateAllFieldCriteria) {\n            error[name] = validationResult;\n          }\n        }\n      }\n      if (!isEmptyObject(validationResult)) {\n        error[name] = {\n          ref: inputRef,\n          ...validationResult\n        };\n        if (!validateAllFieldCriteria) {\n          return error;\n        }\n      }\n    }\n  }\n  setCustomValidity(true);\n  return error;\n};\nfunction append(data, value) {\n  return [...data, ...convertToArrayPayload(value)];\n}\nvar fillEmptyArray = value => Array.isArray(value) ? value.map(() => undefined) : undefined;\nfunction insert(data, index, value) {\n  return [...data.slice(0, index), ...convertToArrayPayload(value), ...data.slice(index)];\n}\nvar moveArrayAt = (data, from, to) => {\n  if (!Array.isArray(data)) {\n    return [];\n  }\n  if (isUndefined(data[to])) {\n    data[to] = undefined;\n  }\n  data.splice(to, 0, data.splice(from, 1)[0]);\n  return data;\n};\nfunction prepend(data, value) {\n  return [...convertToArrayPayload(value), ...convertToArrayPayload(data)];\n}\nfunction removeAtIndexes(data, indexes) {\n  let i = 0;\n  const temp = [...data];\n  for (const index of indexes) {\n    temp.splice(index - i, 1);\n    i++;\n  }\n  return compact(temp).length ? temp : [];\n}\nvar removeArrayAt = (data, index) => isUndefined(index) ? [] : removeAtIndexes(data, convertToArrayPayload(index).sort((a, b) => a - b));\nvar swapArrayAt = (data, indexA, indexB) => {\n  data[indexA] = [data[indexB], data[indexB] = data[indexA]][0];\n};\nfunction baseGet(object, updatePath) {\n  const length = updatePath.slice(0, -1).length;\n  let index = 0;\n  while (index < length) {\n    object = isUndefined(object) ? index++ : object[updatePath[index++]];\n  }\n  return object;\n}\nfunction isEmptyArray(obj) {\n  for (const key in obj) {\n    if (obj.hasOwnProperty(key) && !isUndefined(obj[key])) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction unset(object, path) {\n  const paths = Array.isArray(path) ? path : isKey(path) ? [path] : stringToPath(path);\n  const childObject = paths.length === 1 ? object : baseGet(object, paths);\n  const index = paths.length - 1;\n  const key = paths[index];\n  if (childObject) {\n    delete childObject[key];\n  }\n  if (index !== 0 && (isObject(childObject) && isEmptyObject(childObject) || Array.isArray(childObject) && isEmptyArray(childObject))) {\n    unset(object, paths.slice(0, -1));\n  }\n  return object;\n}\nvar updateAt = (fieldValues, index, value) => {\n  fieldValues[index] = value;\n  return fieldValues;\n};\n\n/**\n * A custom hook that exposes convenient methods to perform operations with a list of dynamic inputs that need to be appended, updated, removed etc. • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn) • [Video](https://youtu.be/4MrbfGSFY2A)\n *\n * @remarks\n * [API](https://react-hook-form.com/api/usefieldarray) • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn)\n *\n * @param props - useFieldArray props\n *\n * @returns methods - functions to manipulate with the Field Arrays (dynamic inputs) {@link UseFieldArrayReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, control, handleSubmit, reset, trigger, setError } = useForm({\n *     defaultValues: {\n *       test: []\n *     }\n *   });\n *   const { fields, append } = useFieldArray({\n *     control,\n *     name: \"test\"\n *   });\n *\n *   return (\n *     <form onSubmit={handleSubmit(data => console.log(data))}>\n *       {fields.map((item, index) => (\n *          <input key={item.id} {...register(`test.${index}.firstName`)}  />\n *       ))}\n *       <button type=\"button\" onClick={() => append({ firstName: \"bill\" })}>\n *         append\n *       </button>\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nfunction useFieldArray(props) {\n  const methods = useFormContext();\n  const {\n    control = methods.control,\n    name,\n    keyName = 'id',\n    shouldUnregister\n  } = props;\n  const [fields, setFields] = React__default.useState(control._getFieldArray(name));\n  const ids = React__default.useRef(control._getFieldArray(name).map(generateId));\n  const _fieldIds = React__default.useRef(fields);\n  const _name = React__default.useRef(name);\n  const _actioned = React__default.useRef(false);\n  _name.current = name;\n  _fieldIds.current = fields;\n  control._names.array.add(name);\n  props.rules && control.register(name, props.rules);\n  useSubscribe({\n    next: _ref => {\n      let {\n        values,\n        name: fieldArrayName\n      } = _ref;\n      if (fieldArrayName === _name.current || !fieldArrayName) {\n        const fieldValues = get(values, _name.current);\n        if (Array.isArray(fieldValues)) {\n          setFields(fieldValues);\n          ids.current = fieldValues.map(generateId);\n        }\n      }\n    },\n    subject: control._subjects.array\n  });\n  const updateValues = React__default.useCallback(updatedFieldArrayValues => {\n    _actioned.current = true;\n    control._updateFieldArray(name, updatedFieldArrayValues);\n  }, [control, name]);\n  const append$1 = (value, options) => {\n    const appendValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = append(control._getFieldArray(name), appendValue);\n    control._names.focus = getFocusFieldName(name, updatedFieldArrayValues.length - 1, options);\n    ids.current = append(ids.current, appendValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._updateFieldArray(name, updatedFieldArrayValues, append, {\n      argA: fillEmptyArray(value)\n    });\n  };\n  const prepend$1 = (value, options) => {\n    const prependValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = prepend(control._getFieldArray(name), prependValue);\n    control._names.focus = getFocusFieldName(name, 0, options);\n    ids.current = prepend(ids.current, prependValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._updateFieldArray(name, updatedFieldArrayValues, prepend, {\n      argA: fillEmptyArray(value)\n    });\n  };\n  const remove = index => {\n    const updatedFieldArrayValues = removeArrayAt(control._getFieldArray(name), index);\n    ids.current = removeArrayAt(ids.current, index);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._updateFieldArray(name, updatedFieldArrayValues, removeArrayAt, {\n      argA: index\n    });\n  };\n  const insert$1 = (index, value, options) => {\n    const insertValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = insert(control._getFieldArray(name), index, insertValue);\n    control._names.focus = getFocusFieldName(name, index, options);\n    ids.current = insert(ids.current, index, insertValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._updateFieldArray(name, updatedFieldArrayValues, insert, {\n      argA: index,\n      argB: fillEmptyArray(value)\n    });\n  };\n  const swap = (indexA, indexB) => {\n    const updatedFieldArrayValues = control._getFieldArray(name);\n    swapArrayAt(updatedFieldArrayValues, indexA, indexB);\n    swapArrayAt(ids.current, indexA, indexB);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._updateFieldArray(name, updatedFieldArrayValues, swapArrayAt, {\n      argA: indexA,\n      argB: indexB\n    }, false);\n  };\n  const move = (from, to) => {\n    const updatedFieldArrayValues = control._getFieldArray(name);\n    moveArrayAt(updatedFieldArrayValues, from, to);\n    moveArrayAt(ids.current, from, to);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._updateFieldArray(name, updatedFieldArrayValues, moveArrayAt, {\n      argA: from,\n      argB: to\n    }, false);\n  };\n  const update = (index, value) => {\n    const updateValue = cloneObject(value);\n    const updatedFieldArrayValues = updateAt(control._getFieldArray(name), index, updateValue);\n    ids.current = [...updatedFieldArrayValues].map((item, i) => !item || i === index ? generateId() : ids.current[i]);\n    updateValues(updatedFieldArrayValues);\n    setFields([...updatedFieldArrayValues]);\n    control._updateFieldArray(name, updatedFieldArrayValues, updateAt, {\n      argA: index,\n      argB: updateValue\n    }, true, false);\n  };\n  const replace = value => {\n    const updatedFieldArrayValues = convertToArrayPayload(cloneObject(value));\n    ids.current = updatedFieldArrayValues.map(generateId);\n    updateValues([...updatedFieldArrayValues]);\n    setFields([...updatedFieldArrayValues]);\n    control._updateFieldArray(name, [...updatedFieldArrayValues], data => data, {}, true, false);\n  };\n  React__default.useEffect(() => {\n    control._state.action = false;\n    isWatched(name, control._names) && control._subjects.state.next({\n      ...control._formState\n    });\n    if (_actioned.current && (!getValidationModes(control._options.mode).isOnSubmit || control._formState.isSubmitted)) {\n      if (control._options.resolver) {\n        control._executeSchema([name]).then(result => {\n          const error = get(result.errors, name);\n          const existingError = get(control._formState.errors, name);\n          if (existingError ? !error && existingError.type || error && (existingError.type !== error.type || existingError.message !== error.message) : error && error.type) {\n            error ? set(control._formState.errors, name, error) : unset(control._formState.errors, name);\n            control._subjects.state.next({\n              errors: control._formState.errors\n            });\n          }\n        });\n      } else {\n        const field = get(control._fields, name);\n        if (field && field._f) {\n          validateField(field, control._formValues, control._options.criteriaMode === VALIDATION_MODE.all, control._options.shouldUseNativeValidation, true).then(error => !isEmptyObject(error) && control._subjects.state.next({\n            errors: updateFieldArrayRootError(control._formState.errors, error, name)\n          }));\n        }\n      }\n    }\n    control._subjects.values.next({\n      name,\n      values: {\n        ...control._formValues\n      }\n    });\n    control._names.focus && focusFieldBy(control._fields, key => !!key && key.startsWith(control._names.focus || ''));\n    control._names.focus = '';\n    control._updateValid();\n  }, [fields, name, control]);\n  React__default.useEffect(() => {\n    !get(control._formValues, name) && control._updateFieldArray(name);\n    return () => {\n      (control._options.shouldUnregister || shouldUnregister) && control.unregister(name);\n    };\n  }, [name, control, keyName, shouldUnregister]);\n  return {\n    swap: React__default.useCallback(swap, [updateValues, name, control]),\n    move: React__default.useCallback(move, [updateValues, name, control]),\n    prepend: React__default.useCallback(prepend$1, [updateValues, name, control]),\n    append: React__default.useCallback(append$1, [updateValues, name, control]),\n    remove: React__default.useCallback(remove, [updateValues, name, control]),\n    insert: React__default.useCallback(insert$1, [updateValues, name, control]),\n    update: React__default.useCallback(update, [updateValues, name, control]),\n    replace: React__default.useCallback(replace, [updateValues, name, control]),\n    fields: React__default.useMemo(() => fields.map((field, index) => ({\n      ...field,\n      [keyName]: ids.current[index] || generateId()\n    })), [fields, keyName])\n  };\n}\nfunction createSubject() {\n  let _observers = [];\n  const next = value => {\n    for (const observer of _observers) {\n      observer.next && observer.next(value);\n    }\n  };\n  const subscribe = observer => {\n    _observers.push(observer);\n    return {\n      unsubscribe: () => {\n        _observers = _observers.filter(o => o !== observer);\n      }\n    };\n  };\n  const unsubscribe = () => {\n    _observers = [];\n  };\n  return {\n    get observers() {\n      return _observers;\n    },\n    next,\n    subscribe,\n    unsubscribe\n  };\n}\nvar isPrimitive = value => isNullOrUndefined(value) || !isObjectType(value);\nfunction deepEqual(object1, object2) {\n  if (isPrimitive(object1) || isPrimitive(object2)) {\n    return object1 === object2;\n  }\n  if (isDateObject(object1) && isDateObject(object2)) {\n    return object1.getTime() === object2.getTime();\n  }\n  const keys1 = Object.keys(object1);\n  const keys2 = Object.keys(object2);\n  if (keys1.length !== keys2.length) {\n    return false;\n  }\n  for (const key of keys1) {\n    const val1 = object1[key];\n    if (!keys2.includes(key)) {\n      return false;\n    }\n    if (key !== 'ref') {\n      const val2 = object2[key];\n      if (isDateObject(val1) && isDateObject(val2) || isObject(val1) && isObject(val2) || Array.isArray(val1) && Array.isArray(val2) ? !deepEqual(val1, val2) : val1 !== val2) {\n        return false;\n      }\n    }\n  }\n  return true;\n}\nvar isMultipleSelect = element => element.type === `select-multiple`;\nvar isRadioOrCheckbox = ref => isRadioInput(ref) || isCheckBoxInput(ref);\nvar live = ref => isHTMLElement(ref) && ref.isConnected;\nvar objectHasFunction = data => {\n  for (const key in data) {\n    if (isFunction(data[key])) {\n      return true;\n    }\n  }\n  return false;\n};\nfunction markFieldsDirty(data) {\n  let fields = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  const isParentNodeArray = Array.isArray(data);\n  if (isObject(data) || isParentNodeArray) {\n    for (const key in data) {\n      if (Array.isArray(data[key]) || isObject(data[key]) && !objectHasFunction(data[key])) {\n        fields[key] = Array.isArray(data[key]) ? [] : {};\n        markFieldsDirty(data[key], fields[key]);\n      } else if (!isNullOrUndefined(data[key])) {\n        fields[key] = true;\n      }\n    }\n  }\n  return fields;\n}\nfunction getDirtyFieldsFromDefaultValues(data, formValues, dirtyFieldsFromValues) {\n  const isParentNodeArray = Array.isArray(data);\n  if (isObject(data) || isParentNodeArray) {\n    for (const key in data) {\n      if (Array.isArray(data[key]) || isObject(data[key]) && !objectHasFunction(data[key])) {\n        if (isUndefined(formValues) || isPrimitive(dirtyFieldsFromValues[key])) {\n          dirtyFieldsFromValues[key] = Array.isArray(data[key]) ? markFieldsDirty(data[key], []) : {\n            ...markFieldsDirty(data[key])\n          };\n        } else {\n          getDirtyFieldsFromDefaultValues(data[key], isNullOrUndefined(formValues) ? {} : formValues[key], dirtyFieldsFromValues[key]);\n        }\n      } else {\n        dirtyFieldsFromValues[key] = !deepEqual(data[key], formValues[key]);\n      }\n    }\n  }\n  return dirtyFieldsFromValues;\n}\nvar getDirtyFields = (defaultValues, formValues) => getDirtyFieldsFromDefaultValues(defaultValues, formValues, markFieldsDirty(formValues));\nvar getFieldValueAs = (value, _ref2) => {\n  let {\n    valueAsNumber,\n    valueAsDate,\n    setValueAs\n  } = _ref2;\n  return isUndefined(value) ? value : valueAsNumber ? value === '' ? NaN : value ? +value : value : valueAsDate && isString(value) ? new Date(value) : setValueAs ? setValueAs(value) : value;\n};\nfunction getFieldValue(_f) {\n  const ref = _f.ref;\n  if (_f.refs ? _f.refs.every(ref => ref.disabled) : ref.disabled) {\n    return;\n  }\n  if (isFileInput(ref)) {\n    return ref.files;\n  }\n  if (isRadioInput(ref)) {\n    return getRadioValue(_f.refs).value;\n  }\n  if (isMultipleSelect(ref)) {\n    return [...ref.selectedOptions].map(_ref3 => {\n      let {\n        value\n      } = _ref3;\n      return value;\n    });\n  }\n  if (isCheckBoxInput(ref)) {\n    return getCheckboxValue(_f.refs).value;\n  }\n  return getFieldValueAs(isUndefined(ref.value) ? _f.ref.value : ref.value, _f);\n}\nvar getResolverOptions = (fieldsNames, _fields, criteriaMode, shouldUseNativeValidation) => {\n  const fields = {};\n  for (const name of fieldsNames) {\n    const field = get(_fields, name);\n    field && set(fields, name, field._f);\n  }\n  return {\n    criteriaMode,\n    names: [...fieldsNames],\n    fields,\n    shouldUseNativeValidation\n  };\n};\nvar getRuleValue = rule => isUndefined(rule) ? rule : isRegex(rule) ? rule.source : isObject(rule) ? isRegex(rule.value) ? rule.value.source : rule.value : rule;\nvar hasValidation = options => options.mount && (options.required || options.min || options.max || options.maxLength || options.minLength || options.pattern || options.validate);\nfunction schemaErrorLookup(errors, _fields, name) {\n  const error = get(errors, name);\n  if (error || isKey(name)) {\n    return {\n      error,\n      name\n    };\n  }\n  const names = name.split('.');\n  while (names.length) {\n    const fieldName = names.join('.');\n    const field = get(_fields, fieldName);\n    const foundError = get(errors, fieldName);\n    if (field && !Array.isArray(field) && name !== fieldName) {\n      return {\n        name\n      };\n    }\n    if (foundError && foundError.type) {\n      return {\n        name: fieldName,\n        error: foundError\n      };\n    }\n    names.pop();\n  }\n  return {\n    name\n  };\n}\nvar skipValidation = (isBlurEvent, isTouched, isSubmitted, reValidateMode, mode) => {\n  if (mode.isOnAll) {\n    return false;\n  } else if (!isSubmitted && mode.isOnTouch) {\n    return !(isTouched || isBlurEvent);\n  } else if (isSubmitted ? reValidateMode.isOnBlur : mode.isOnBlur) {\n    return !isBlurEvent;\n  } else if (isSubmitted ? reValidateMode.isOnChange : mode.isOnChange) {\n    return isBlurEvent;\n  }\n  return true;\n};\nvar unsetEmptyArray = (ref, name) => !compact(get(ref, name)).length && unset(ref, name);\nconst defaultOptions = {\n  mode: VALIDATION_MODE.onSubmit,\n  reValidateMode: VALIDATION_MODE.onChange,\n  shouldFocusError: true\n};\nfunction createFormControl() {\n  let props = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  let flushRootRender = arguments.length > 1 ? arguments[1] : undefined;\n  let _options = {\n    ...defaultOptions,\n    ...props\n  };\n  let _formState = {\n    submitCount: 0,\n    isDirty: false,\n    isLoading: isFunction(_options.defaultValues),\n    isValidating: false,\n    isSubmitted: false,\n    isSubmitting: false,\n    isSubmitSuccessful: false,\n    isValid: false,\n    touchedFields: {},\n    dirtyFields: {},\n    errors: {}\n  };\n  let _fields = {};\n  let _defaultValues = isObject(_options.defaultValues) || isObject(_options.values) ? cloneObject(_options.defaultValues || _options.values) || {} : {};\n  let _formValues = _options.shouldUnregister ? {} : cloneObject(_defaultValues);\n  let _state = {\n    action: false,\n    mount: false,\n    watch: false\n  };\n  let _names = {\n    mount: new Set(),\n    unMount: new Set(),\n    array: new Set(),\n    watch: new Set()\n  };\n  let delayErrorCallback;\n  let timer = 0;\n  const _proxyFormState = {\n    isDirty: false,\n    dirtyFields: false,\n    touchedFields: false,\n    isValidating: false,\n    isValid: false,\n    errors: false\n  };\n  const _subjects = {\n    values: createSubject(),\n    array: createSubject(),\n    state: createSubject()\n  };\n  const shouldCaptureDirtyFields = props.resetOptions && props.resetOptions.keepDirtyValues;\n  const validationModeBeforeSubmit = getValidationModes(_options.mode);\n  const validationModeAfterSubmit = getValidationModes(_options.reValidateMode);\n  const shouldDisplayAllAssociatedErrors = _options.criteriaMode === VALIDATION_MODE.all;\n  const debounce = callback => wait => {\n    clearTimeout(timer);\n    timer = setTimeout(callback, wait);\n  };\n  const _updateValid = async shouldUpdateValid => {\n    if (_proxyFormState.isValid || shouldUpdateValid) {\n      const isValid = _options.resolver ? isEmptyObject((await _executeSchema()).errors) : await executeBuiltInValidation(_fields, true);\n      if (isValid !== _formState.isValid) {\n        _subjects.state.next({\n          isValid\n        });\n      }\n    }\n  };\n  const _updateIsValidating = value => _proxyFormState.isValidating && _subjects.state.next({\n    isValidating: value\n  });\n  const _updateFieldArray = function (name) {\n    let values = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];\n    let method = arguments.length > 2 ? arguments[2] : undefined;\n    let args = arguments.length > 3 ? arguments[3] : undefined;\n    let shouldSetValues = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : true;\n    let shouldUpdateFieldsAndState = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : true;\n    if (args && method) {\n      _state.action = true;\n      if (shouldUpdateFieldsAndState && Array.isArray(get(_fields, name))) {\n        const fieldValues = method(get(_fields, name), args.argA, args.argB);\n        shouldSetValues && set(_fields, name, fieldValues);\n      }\n      if (shouldUpdateFieldsAndState && Array.isArray(get(_formState.errors, name))) {\n        const errors = method(get(_formState.errors, name), args.argA, args.argB);\n        shouldSetValues && set(_formState.errors, name, errors);\n        unsetEmptyArray(_formState.errors, name);\n      }\n      if (_proxyFormState.touchedFields && shouldUpdateFieldsAndState && Array.isArray(get(_formState.touchedFields, name))) {\n        const touchedFields = method(get(_formState.touchedFields, name), args.argA, args.argB);\n        shouldSetValues && set(_formState.touchedFields, name, touchedFields);\n      }\n      if (_proxyFormState.dirtyFields) {\n        _formState.dirtyFields = getDirtyFields(_defaultValues, _formValues);\n      }\n      _subjects.state.next({\n        name,\n        isDirty: _getDirty(name, values),\n        dirtyFields: _formState.dirtyFields,\n        errors: _formState.errors,\n        isValid: _formState.isValid\n      });\n    } else {\n      set(_formValues, name, values);\n    }\n  };\n  const updateErrors = (name, error) => {\n    set(_formState.errors, name, error);\n    _subjects.state.next({\n      errors: _formState.errors\n    });\n  };\n  const updateValidAndValue = (name, shouldSkipSetValueAs, value, ref) => {\n    const field = get(_fields, name);\n    if (field) {\n      const defaultValue = get(_formValues, name, isUndefined(value) ? get(_defaultValues, name) : value);\n      isUndefined(defaultValue) || ref && ref.defaultChecked || shouldSkipSetValueAs ? set(_formValues, name, shouldSkipSetValueAs ? defaultValue : getFieldValue(field._f)) : setFieldValue(name, defaultValue);\n      _state.mount && _updateValid();\n    }\n  };\n  const updateTouchAndDirty = (name, fieldValue, isBlurEvent, shouldDirty, shouldRender) => {\n    let shouldUpdateField = false;\n    let isPreviousDirty = false;\n    const output = {\n      name\n    };\n    if (!isBlurEvent || shouldDirty) {\n      if (_proxyFormState.isDirty) {\n        isPreviousDirty = _formState.isDirty;\n        _formState.isDirty = output.isDirty = _getDirty();\n        shouldUpdateField = isPreviousDirty !== output.isDirty;\n      }\n      const isCurrentFieldPristine = deepEqual(get(_defaultValues, name), fieldValue);\n      isPreviousDirty = get(_formState.dirtyFields, name);\n      isCurrentFieldPristine ? unset(_formState.dirtyFields, name) : set(_formState.dirtyFields, name, true);\n      output.dirtyFields = _formState.dirtyFields;\n      shouldUpdateField = shouldUpdateField || _proxyFormState.dirtyFields && isPreviousDirty !== !isCurrentFieldPristine;\n    }\n    if (isBlurEvent) {\n      const isPreviousFieldTouched = get(_formState.touchedFields, name);\n      if (!isPreviousFieldTouched) {\n        set(_formState.touchedFields, name, isBlurEvent);\n        output.touchedFields = _formState.touchedFields;\n        shouldUpdateField = shouldUpdateField || _proxyFormState.touchedFields && isPreviousFieldTouched !== isBlurEvent;\n      }\n    }\n    shouldUpdateField && shouldRender && _subjects.state.next(output);\n    return shouldUpdateField ? output : {};\n  };\n  const shouldRenderByError = (name, isValid, error, fieldState) => {\n    const previousFieldError = get(_formState.errors, name);\n    const shouldUpdateValid = _proxyFormState.isValid && isBoolean(isValid) && _formState.isValid !== isValid;\n    if (props.delayError && error) {\n      delayErrorCallback = debounce(() => updateErrors(name, error));\n      delayErrorCallback(props.delayError);\n    } else {\n      clearTimeout(timer);\n      delayErrorCallback = null;\n      error ? set(_formState.errors, name, error) : unset(_formState.errors, name);\n    }\n    if ((error ? !deepEqual(previousFieldError, error) : previousFieldError) || !isEmptyObject(fieldState) || shouldUpdateValid) {\n      const updatedFormState = {\n        ...fieldState,\n        ...(shouldUpdateValid && isBoolean(isValid) ? {\n          isValid\n        } : {}),\n        errors: _formState.errors,\n        name\n      };\n      _formState = {\n        ..._formState,\n        ...updatedFormState\n      };\n      _subjects.state.next(updatedFormState);\n    }\n    _updateIsValidating(false);\n  };\n  const _executeSchema = async name => _options.resolver(_formValues, _options.context, getResolverOptions(name || _names.mount, _fields, _options.criteriaMode, _options.shouldUseNativeValidation));\n  const executeSchemaAndUpdateState = async names => {\n    const {\n      errors\n    } = await _executeSchema();\n    if (names) {\n      for (const name of names) {\n        const error = get(errors, name);\n        error ? set(_formState.errors, name, error) : unset(_formState.errors, name);\n      }\n    } else {\n      _formState.errors = errors;\n    }\n    return errors;\n  };\n  const executeBuiltInValidation = async function (fields, shouldOnlyCheckValid) {\n    let context = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {\n      valid: true\n    };\n    for (const name in fields) {\n      const field = fields[name];\n      if (field) {\n        const {\n          _f,\n          ...fieldValue\n        } = field;\n        if (_f) {\n          const isFieldArrayRoot = _names.array.has(_f.name);\n          const fieldError = await validateField(field, _formValues, shouldDisplayAllAssociatedErrors, _options.shouldUseNativeValidation && !shouldOnlyCheckValid, isFieldArrayRoot);\n          if (fieldError[_f.name]) {\n            context.valid = false;\n            if (shouldOnlyCheckValid) {\n              break;\n            }\n          }\n          !shouldOnlyCheckValid && (get(fieldError, _f.name) ? isFieldArrayRoot ? updateFieldArrayRootError(_formState.errors, fieldError, _f.name) : set(_formState.errors, _f.name, fieldError[_f.name]) : unset(_formState.errors, _f.name));\n        }\n        fieldValue && (await executeBuiltInValidation(fieldValue, shouldOnlyCheckValid, context));\n      }\n    }\n    return context.valid;\n  };\n  const _removeUnmounted = () => {\n    for (const name of _names.unMount) {\n      const field = get(_fields, name);\n      field && (field._f.refs ? field._f.refs.every(ref => !live(ref)) : !live(field._f.ref)) && unregister(name);\n    }\n    _names.unMount = new Set();\n  };\n  const _getDirty = (name, data) => (name && data && set(_formValues, name, data), !deepEqual(getValues(), _defaultValues));\n  const _getWatch = (names, defaultValue, isGlobal) => generateWatchOutput(names, _names, {\n    ...(_state.mount ? _formValues : isUndefined(defaultValue) ? _defaultValues : isString(names) ? {\n      [names]: defaultValue\n    } : defaultValue)\n  }, isGlobal, defaultValue);\n  const _getFieldArray = name => compact(get(_state.mount ? _formValues : _defaultValues, name, props.shouldUnregister ? get(_defaultValues, name, []) : []));\n  const setFieldValue = function (name, value) {\n    let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    const field = get(_fields, name);\n    let fieldValue = value;\n    if (field) {\n      const fieldReference = field._f;\n      if (fieldReference) {\n        !fieldReference.disabled && set(_formValues, name, getFieldValueAs(value, fieldReference));\n        fieldValue = isHTMLElement(fieldReference.ref) && isNullOrUndefined(value) ? '' : value;\n        if (isMultipleSelect(fieldReference.ref)) {\n          [...fieldReference.ref.options].forEach(optionRef => optionRef.selected = fieldValue.includes(optionRef.value));\n        } else if (fieldReference.refs) {\n          if (isCheckBoxInput(fieldReference.ref)) {\n            fieldReference.refs.length > 1 ? fieldReference.refs.forEach(checkboxRef => (!checkboxRef.defaultChecked || !checkboxRef.disabled) && (checkboxRef.checked = Array.isArray(fieldValue) ? !!fieldValue.find(data => data === checkboxRef.value) : fieldValue === checkboxRef.value)) : fieldReference.refs[0] && (fieldReference.refs[0].checked = !!fieldValue);\n          } else {\n            fieldReference.refs.forEach(radioRef => radioRef.checked = radioRef.value === fieldValue);\n          }\n        } else if (isFileInput(fieldReference.ref)) {\n          fieldReference.ref.value = '';\n        } else {\n          fieldReference.ref.value = fieldValue;\n          if (!fieldReference.ref.type) {\n            _subjects.values.next({\n              name,\n              values: {\n                ..._formValues\n              }\n            });\n          }\n        }\n      }\n    }\n    (options.shouldDirty || options.shouldTouch) && updateTouchAndDirty(name, fieldValue, options.shouldTouch, options.shouldDirty, true);\n    options.shouldValidate && trigger(name);\n  };\n  const setValues = (name, value, options) => {\n    for (const fieldKey in value) {\n      const fieldValue = value[fieldKey];\n      const fieldName = `${name}.${fieldKey}`;\n      const field = get(_fields, fieldName);\n      (_names.array.has(name) || !isPrimitive(fieldValue) || field && !field._f) && !isDateObject(fieldValue) ? setValues(fieldName, fieldValue, options) : setFieldValue(fieldName, fieldValue, options);\n    }\n  };\n  const setValue = function (name, value) {\n    let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    const field = get(_fields, name);\n    const isFieldArray = _names.array.has(name);\n    const cloneValue = cloneObject(value);\n    set(_formValues, name, cloneValue);\n    if (isFieldArray) {\n      _subjects.array.next({\n        name,\n        values: {\n          ..._formValues\n        }\n      });\n      if ((_proxyFormState.isDirty || _proxyFormState.dirtyFields) && options.shouldDirty) {\n        _subjects.state.next({\n          name,\n          dirtyFields: getDirtyFields(_defaultValues, _formValues),\n          isDirty: _getDirty(name, cloneValue)\n        });\n      }\n    } else {\n      field && !field._f && !isNullOrUndefined(cloneValue) ? setValues(name, cloneValue, options) : setFieldValue(name, cloneValue, options);\n    }\n    isWatched(name, _names) && _subjects.state.next({\n      ..._formState\n    });\n    _subjects.values.next({\n      name,\n      values: {\n        ..._formValues\n      }\n    });\n    !_state.mount && flushRootRender();\n  };\n  const onChange = async event => {\n    const target = event.target;\n    let name = target.name;\n    let isFieldValueUpdated = true;\n    const field = get(_fields, name);\n    const getCurrentFieldValue = () => target.type ? getFieldValue(field._f) : getEventValue(event);\n    if (field) {\n      let error;\n      let isValid;\n      const fieldValue = getCurrentFieldValue();\n      const isBlurEvent = event.type === EVENTS.BLUR || event.type === EVENTS.FOCUS_OUT;\n      const shouldSkipValidation = !hasValidation(field._f) && !_options.resolver && !get(_formState.errors, name) && !field._f.deps || skipValidation(isBlurEvent, get(_formState.touchedFields, name), _formState.isSubmitted, validationModeAfterSubmit, validationModeBeforeSubmit);\n      const watched = isWatched(name, _names, isBlurEvent);\n      set(_formValues, name, fieldValue);\n      if (isBlurEvent) {\n        field._f.onBlur && field._f.onBlur(event);\n        delayErrorCallback && delayErrorCallback(0);\n      } else if (field._f.onChange) {\n        field._f.onChange(event);\n      }\n      const fieldState = updateTouchAndDirty(name, fieldValue, isBlurEvent, false);\n      const shouldRender = !isEmptyObject(fieldState) || watched;\n      !isBlurEvent && _subjects.values.next({\n        name,\n        type: event.type,\n        values: {\n          ..._formValues\n        }\n      });\n      if (shouldSkipValidation) {\n        _proxyFormState.isValid && _updateValid();\n        return shouldRender && _subjects.state.next({\n          name,\n          ...(watched ? {} : fieldState)\n        });\n      }\n      !isBlurEvent && watched && _subjects.state.next({\n        ..._formState\n      });\n      _updateIsValidating(true);\n      if (_options.resolver) {\n        const {\n          errors\n        } = await _executeSchema([name]);\n        const previousErrorLookupResult = schemaErrorLookup(_formState.errors, _fields, name);\n        const errorLookupResult = schemaErrorLookup(errors, _fields, previousErrorLookupResult.name || name);\n        error = errorLookupResult.error;\n        name = errorLookupResult.name;\n        isValid = isEmptyObject(errors);\n      } else {\n        error = (await validateField(field, _formValues, shouldDisplayAllAssociatedErrors, _options.shouldUseNativeValidation))[name];\n        isFieldValueUpdated = isNaN(fieldValue) || fieldValue === get(_formValues, name, fieldValue);\n        if (isFieldValueUpdated) {\n          if (error) {\n            isValid = false;\n          } else if (_proxyFormState.isValid) {\n            isValid = await executeBuiltInValidation(_fields, true);\n          }\n        }\n      }\n      if (isFieldValueUpdated) {\n        field._f.deps && trigger(field._f.deps);\n        shouldRenderByError(name, isValid, error, fieldState);\n      }\n    }\n  };\n  const trigger = async function (name) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    let isValid;\n    let validationResult;\n    const fieldNames = convertToArrayPayload(name);\n    _updateIsValidating(true);\n    if (_options.resolver) {\n      const errors = await executeSchemaAndUpdateState(isUndefined(name) ? name : fieldNames);\n      isValid = isEmptyObject(errors);\n      validationResult = name ? !fieldNames.some(name => get(errors, name)) : isValid;\n    } else if (name) {\n      validationResult = (await Promise.all(fieldNames.map(async fieldName => {\n        const field = get(_fields, fieldName);\n        return await executeBuiltInValidation(field && field._f ? {\n          [fieldName]: field\n        } : field);\n      }))).every(Boolean);\n      !(!validationResult && !_formState.isValid) && _updateValid();\n    } else {\n      validationResult = isValid = await executeBuiltInValidation(_fields);\n    }\n    _subjects.state.next({\n      ...(!isString(name) || _proxyFormState.isValid && isValid !== _formState.isValid ? {} : {\n        name\n      }),\n      ...(_options.resolver || !name ? {\n        isValid\n      } : {}),\n      errors: _formState.errors,\n      isValidating: false\n    });\n    options.shouldFocus && !validationResult && focusFieldBy(_fields, key => key && get(_formState.errors, key), name ? fieldNames : _names.mount);\n    return validationResult;\n  };\n  const getValues = fieldNames => {\n    const values = {\n      ..._defaultValues,\n      ...(_state.mount ? _formValues : {})\n    };\n    return isUndefined(fieldNames) ? values : isString(fieldNames) ? get(values, fieldNames) : fieldNames.map(name => get(values, name));\n  };\n  const getFieldState = (name, formState) => ({\n    invalid: !!get((formState || _formState).errors, name),\n    isDirty: !!get((formState || _formState).dirtyFields, name),\n    isTouched: !!get((formState || _formState).touchedFields, name),\n    error: get((formState || _formState).errors, name)\n  });\n  const clearErrors = name => {\n    name && convertToArrayPayload(name).forEach(inputName => unset(_formState.errors, inputName));\n    _subjects.state.next({\n      errors: name ? _formState.errors : {}\n    });\n  };\n  const setError = (name, error, options) => {\n    const ref = (get(_fields, name, {\n      _f: {}\n    })._f || {}).ref;\n    set(_formState.errors, name, {\n      ...error,\n      ref\n    });\n    _subjects.state.next({\n      name,\n      errors: _formState.errors,\n      isValid: false\n    });\n    options && options.shouldFocus && ref && ref.focus && ref.focus();\n  };\n  const watch = (name, defaultValue) => isFunction(name) ? _subjects.values.subscribe({\n    next: payload => name(_getWatch(undefined, defaultValue), payload)\n  }) : _getWatch(name, defaultValue, true);\n  const unregister = function (name) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    for (const fieldName of name ? convertToArrayPayload(name) : _names.mount) {\n      _names.mount.delete(fieldName);\n      _names.array.delete(fieldName);\n      if (!options.keepValue) {\n        unset(_fields, fieldName);\n        unset(_formValues, fieldName);\n      }\n      !options.keepError && unset(_formState.errors, fieldName);\n      !options.keepDirty && unset(_formState.dirtyFields, fieldName);\n      !options.keepTouched && unset(_formState.touchedFields, fieldName);\n      !_options.shouldUnregister && !options.keepDefaultValue && unset(_defaultValues, fieldName);\n    }\n    _subjects.values.next({\n      values: {\n        ..._formValues\n      }\n    });\n    _subjects.state.next({\n      ..._formState,\n      ...(!options.keepDirty ? {} : {\n        isDirty: _getDirty()\n      })\n    });\n    !options.keepIsValid && _updateValid();\n  };\n  const register = function (name) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    let field = get(_fields, name);\n    const disabledIsDefined = isBoolean(options.disabled);\n    set(_fields, name, {\n      ...(field || {}),\n      _f: {\n        ...(field && field._f ? field._f : {\n          ref: {\n            name\n          }\n        }),\n        name,\n        mount: true,\n        ...options\n      }\n    });\n    _names.mount.add(name);\n    field ? disabledIsDefined && set(_formValues, name, options.disabled ? undefined : get(_formValues, name, getFieldValue(field._f))) : updateValidAndValue(name, true, options.value);\n    return {\n      ...(disabledIsDefined ? {\n        disabled: options.disabled\n      } : {}),\n      ...(_options.progressive ? {\n        required: !!options.required,\n        min: getRuleValue(options.min),\n        max: getRuleValue(options.max),\n        minLength: getRuleValue(options.minLength),\n        maxLength: getRuleValue(options.maxLength),\n        pattern: getRuleValue(options.pattern)\n      } : {}),\n      name,\n      onChange,\n      onBlur: onChange,\n      ref: ref => {\n        if (ref) {\n          register(name, options);\n          field = get(_fields, name);\n          const fieldRef = isUndefined(ref.value) ? ref.querySelectorAll ? ref.querySelectorAll('input,select,textarea')[0] || ref : ref : ref;\n          const radioOrCheckbox = isRadioOrCheckbox(fieldRef);\n          const refs = field._f.refs || [];\n          if (radioOrCheckbox ? refs.find(option => option === fieldRef) : fieldRef === field._f.ref) {\n            return;\n          }\n          set(_fields, name, {\n            _f: {\n              ...field._f,\n              ...(radioOrCheckbox ? {\n                refs: [...refs.filter(live), fieldRef, ...(Array.isArray(get(_defaultValues, name)) ? [{}] : [])],\n                ref: {\n                  type: fieldRef.type,\n                  name\n                }\n              } : {\n                ref: fieldRef\n              })\n            }\n          });\n          updateValidAndValue(name, false, undefined, fieldRef);\n        } else {\n          field = get(_fields, name, {});\n          if (field._f) {\n            field._f.mount = false;\n          }\n          (_options.shouldUnregister || options.shouldUnregister) && !(isNameInFieldArray(_names.array, name) && _state.action) && _names.unMount.add(name);\n        }\n      }\n    };\n  };\n  const _focusError = () => _options.shouldFocusError && focusFieldBy(_fields, key => key && get(_formState.errors, key), _names.mount);\n  const handleSubmit = (onValid, onInvalid) => async e => {\n    if (e) {\n      e.preventDefault && e.preventDefault();\n      e.persist && e.persist();\n    }\n    let fieldValues = cloneObject(_formValues);\n    _subjects.state.next({\n      isSubmitting: true\n    });\n    if (_options.resolver) {\n      const {\n        errors,\n        values\n      } = await _executeSchema();\n      _formState.errors = errors;\n      fieldValues = values;\n    } else {\n      await executeBuiltInValidation(_fields);\n    }\n    unset(_formState.errors, 'root');\n    if (isEmptyObject(_formState.errors)) {\n      _subjects.state.next({\n        errors: {}\n      });\n      await onValid(fieldValues, e);\n    } else {\n      if (onInvalid) {\n        await onInvalid({\n          ..._formState.errors\n        }, e);\n      }\n      _focusError();\n      setTimeout(_focusError);\n    }\n    _subjects.state.next({\n      isSubmitted: true,\n      isSubmitting: false,\n      isSubmitSuccessful: isEmptyObject(_formState.errors),\n      submitCount: _formState.submitCount + 1,\n      errors: _formState.errors\n    });\n  };\n  const resetField = function (name) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    if (get(_fields, name)) {\n      if (isUndefined(options.defaultValue)) {\n        setValue(name, get(_defaultValues, name));\n      } else {\n        setValue(name, options.defaultValue);\n        set(_defaultValues, name, options.defaultValue);\n      }\n      if (!options.keepTouched) {\n        unset(_formState.touchedFields, name);\n      }\n      if (!options.keepDirty) {\n        unset(_formState.dirtyFields, name);\n        _formState.isDirty = options.defaultValue ? _getDirty(name, get(_defaultValues, name)) : _getDirty();\n      }\n      if (!options.keepError) {\n        unset(_formState.errors, name);\n        _proxyFormState.isValid && _updateValid();\n      }\n      _subjects.state.next({\n        ..._formState\n      });\n    }\n  };\n  const _reset = function (formValues) {\n    let keepStateOptions = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    const updatedValues = formValues || _defaultValues;\n    const cloneUpdatedValues = cloneObject(updatedValues);\n    const values = formValues && !isEmptyObject(formValues) ? cloneUpdatedValues : _defaultValues;\n    if (!keepStateOptions.keepDefaultValues) {\n      _defaultValues = updatedValues;\n    }\n    if (!keepStateOptions.keepValues) {\n      if (keepStateOptions.keepDirtyValues || shouldCaptureDirtyFields) {\n        for (const fieldName of _names.mount) {\n          get(_formState.dirtyFields, fieldName) ? set(values, fieldName, get(_formValues, fieldName)) : setValue(fieldName, get(values, fieldName));\n        }\n      } else {\n        if (isWeb && isUndefined(formValues)) {\n          for (const name of _names.mount) {\n            const field = get(_fields, name);\n            if (field && field._f) {\n              const fieldReference = Array.isArray(field._f.refs) ? field._f.refs[0] : field._f.ref;\n              if (isHTMLElement(fieldReference)) {\n                const form = fieldReference.closest('form');\n                if (form) {\n                  form.reset();\n                  break;\n                }\n              }\n            }\n          }\n        }\n        _fields = {};\n      }\n      _formValues = props.shouldUnregister ? keepStateOptions.keepDefaultValues ? cloneObject(_defaultValues) : {} : cloneObject(values);\n      _subjects.array.next({\n        values: {\n          ...values\n        }\n      });\n      _subjects.values.next({\n        values: {\n          ...values\n        }\n      });\n    }\n    _names = {\n      mount: new Set(),\n      unMount: new Set(),\n      array: new Set(),\n      watch: new Set(),\n      watchAll: false,\n      focus: ''\n    };\n    !_state.mount && flushRootRender();\n    _state.mount = !_proxyFormState.isValid || !!keepStateOptions.keepIsValid;\n    _state.watch = !!props.shouldUnregister;\n    _subjects.state.next({\n      submitCount: keepStateOptions.keepSubmitCount ? _formState.submitCount : 0,\n      isDirty: keepStateOptions.keepDirty ? _formState.isDirty : !!(keepStateOptions.keepDefaultValues && !deepEqual(formValues, _defaultValues)),\n      isSubmitted: keepStateOptions.keepIsSubmitted ? _formState.isSubmitted : false,\n      dirtyFields: keepStateOptions.keepDirtyValues ? _formState.dirtyFields : keepStateOptions.keepDefaultValues && formValues ? getDirtyFields(_defaultValues, formValues) : {},\n      touchedFields: keepStateOptions.keepTouched ? _formState.touchedFields : {},\n      errors: keepStateOptions.keepErrors ? _formState.errors : {},\n      isSubmitting: false,\n      isSubmitSuccessful: false\n    });\n  };\n  const reset = (formValues, keepStateOptions) => _reset(isFunction(formValues) ? formValues(_formValues) : formValues, keepStateOptions);\n  const setFocus = function (name) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    const field = get(_fields, name);\n    const fieldReference = field && field._f;\n    if (fieldReference) {\n      const fieldRef = fieldReference.refs ? fieldReference.refs[0] : fieldReference.ref;\n      if (fieldRef.focus) {\n        fieldRef.focus();\n        options.shouldSelect && fieldRef.select();\n      }\n    }\n  };\n  const _updateFormState = updatedFormState => {\n    _formState = {\n      ..._formState,\n      ...updatedFormState\n    };\n  };\n  const _resetDefaultValues = () => isFunction(_options.defaultValues) && _options.defaultValues().then(values => {\n    reset(values, _options.resetOptions);\n    _subjects.state.next({\n      isLoading: false\n    });\n  });\n  return {\n    control: {\n      register,\n      unregister,\n      getFieldState,\n      handleSubmit,\n      setError,\n      _executeSchema,\n      _getWatch,\n      _getDirty,\n      _updateValid,\n      _removeUnmounted,\n      _updateFieldArray,\n      _getFieldArray,\n      _reset,\n      _resetDefaultValues,\n      _updateFormState,\n      _subjects,\n      _proxyFormState,\n      get _fields() {\n        return _fields;\n      },\n      get _formValues() {\n        return _formValues;\n      },\n      get _state() {\n        return _state;\n      },\n      set _state(value) {\n        _state = value;\n      },\n      get _defaultValues() {\n        return _defaultValues;\n      },\n      get _names() {\n        return _names;\n      },\n      set _names(value) {\n        _names = value;\n      },\n      get _formState() {\n        return _formState;\n      },\n      set _formState(value) {\n        _formState = value;\n      },\n      get _options() {\n        return _options;\n      },\n      set _options(value) {\n        _options = {\n          ..._options,\n          ...value\n        };\n      }\n    },\n    trigger,\n    register,\n    handleSubmit,\n    watch,\n    setValue,\n    getValues,\n    reset,\n    resetField,\n    clearErrors,\n    unregister,\n    setError,\n    setFocus,\n    getFieldState\n  };\n}\n\n/**\n * Custom hook to manage the entire form.\n *\n * @remarks\n * [API](https://react-hook-form.com/api/useform) • [Demo](https://codesandbox.io/s/react-hook-form-get-started-ts-5ksmm) • [Video](https://www.youtube.com/watch?v=RkXv4AXXC_4)\n *\n * @param props - form configuration and validation parameters.\n *\n * @returns methods - individual functions to manage the form state. {@link UseFormReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, watch, formState: { errors } } = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   console.log(watch(\"example\"));\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input defaultValue=\"test\" {...register(\"example\")} />\n *       <input {...register(\"exampleRequired\", { required: true })} />\n *       {errors.exampleRequired && <span>This field is required</span>}\n *       <button>Submit</button>\n *     </form>\n *   );\n * }\n * ```\n */\nfunction useForm() {\n  let props = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  const _formControl = React__default.useRef();\n  const [formState, updateFormState] = React__default.useState({\n    isDirty: false,\n    isValidating: false,\n    isLoading: isFunction(props.defaultValues),\n    isSubmitted: false,\n    isSubmitting: false,\n    isSubmitSuccessful: false,\n    isValid: false,\n    submitCount: 0,\n    dirtyFields: {},\n    touchedFields: {},\n    errors: {},\n    defaultValues: isFunction(props.defaultValues) ? undefined : props.defaultValues\n  });\n  if (!_formControl.current) {\n    _formControl.current = {\n      ...createFormControl(props, () => updateFormState(formState => ({\n        ...formState\n      }))),\n      formState\n    };\n  }\n  const control = _formControl.current.control;\n  control._options = props;\n  useSubscribe({\n    subject: control._subjects.state,\n    next: value => {\n      if (shouldRenderFormState(value, control._proxyFormState, control._updateFormState, true)) {\n        updateFormState({\n          ...control._formState\n        });\n      }\n    }\n  });\n  React__default.useEffect(() => {\n    if (props.values && !deepEqual(props.values, control._defaultValues)) {\n      control._reset(props.values, control._options.resetOptions);\n    } else {\n      control._resetDefaultValues();\n    }\n  }, [props.values, control]);\n  React__default.useEffect(() => {\n    if (!control._state.mount) {\n      control._updateValid();\n      control._state.mount = true;\n    }\n    if (control._state.watch) {\n      control._state.watch = false;\n      control._subjects.state.next({\n        ...control._formState\n      });\n    }\n    control._removeUnmounted();\n  });\n  _formControl.current.formState = getProxyFormState(formState, control);\n  return _formControl.current;\n}\nexport { Controller, Form, FormProvider, appendErrors, get, set, useController, useFieldArray, useForm, useFormContext, useFormState, useWatch };", "map": {"version": 3, "names": ["isCheckBoxInput", "element", "type", "isDateObject", "value", "Date", "isNullOrUndefined", "isObjectType", "isObject", "Array", "isArray", "getEventValue", "event", "target", "checked", "getNodeParentName", "name", "substring", "search", "isNameInFieldArray", "names", "has", "isPlainObject", "tempObject", "prototypeCopy", "constructor", "prototype", "hasOwnProperty", "isWeb", "window", "HTMLElement", "document", "cloneObject", "data", "copy", "Set", "Blob", "FileList", "key", "compact", "filter", "Boolean", "isUndefined", "val", "undefined", "get", "obj", "path", "defaultValue", "result", "split", "reduce", "EVENTS", "BLUR", "FOCUS_OUT", "CHANGE", "VALIDATION_MODE", "onBlur", "onChange", "onSubmit", "onTouched", "all", "INPUT_VALIDATION_RULES", "max", "min", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "pattern", "required", "validate", "HookFormContext", "React__default", "createContext", "useFormContext", "useContext", "FormProvider", "props", "children", "createElement", "Provider", "getProxyFormState", "formState", "control", "localProxyFormState", "isRoot", "arguments", "length", "defaultValues", "_defaultValues", "Object", "defineProperty", "_key", "_proxyFormState", "isEmptyObject", "keys", "shouldRenderFormState", "formStateData", "updateFormState", "find", "convertToArrayPayload", "shouldSubscribeByName", "signalName", "exact", "some", "currentName", "startsWith", "useSubscribe", "_props", "useRef", "current", "useEffect", "subscription", "disabled", "subject", "subscribe", "next", "unsubscribe", "useFormState", "methods", "useState", "_formState", "_mounted", "_localProxyFormState", "isDirty", "isLoading", "dirtyFields", "touchedFields", "isValidating", "<PERSON><PERSON><PERSON><PERSON>", "errors", "_name", "_updateFormState", "_subjects", "state", "_updateValid", "isString", "generateWatchOutput", "_names", "formValues", "isGlobal", "watch", "add", "map", "fieldName", "watchAll", "useWatch", "values", "updateValue", "_formValues", "_getWatch", "_removeUnmounted", "is<PERSON>ey", "test", "stringToPath", "input", "replace", "set", "object", "index", "temp<PERSON>ath", "lastIndex", "newValue", "objValue", "isNaN", "useController", "shouldUnregister", "isArrayField", "array", "_registerProps", "register", "rules", "_shouldUnregisterField", "_options", "updateMounted", "field", "_fields", "_f", "mount", "_state", "action", "unregister", "useCallback", "ref", "elm", "focus", "select", "setCustomValidity", "message", "reportValidity", "fieldState", "defineProperties", "invalid", "enumerable", "isTouched", "error", "Controller", "render", "POST_REQUEST", "Form", "mounted", "setMounted", "React", "method", "headers", "encType", "onError", "onSuccess", "validateStatus", "rest", "submit", "<PERSON><PERSON><PERSON><PERSON>", "handleSubmit", "formData", "FormData", "formDataJson", "JSON", "stringify", "_a", "append", "shouldStringifySubmissionData", "includes", "response", "fetch", "body", "status", "String", "isSubmitSuccessful", "setError", "Fragment", "noValidate", "appendErrors", "validateAllFieldCriteria", "types", "focusFieldBy", "fields", "callback", "fieldsNames", "current<PERSON><PERSON>", "refs", "generateId", "d", "performance", "now", "c", "r", "Math", "random", "toString", "getFocusFieldName", "options", "shouldFocus", "focusName", "focusIndex", "getValidationModes", "mode", "isOnSubmit", "isOnBlur", "isOnChange", "isOnAll", "isOnTouch", "isWatched", "isBlurEvent", "watchName", "slice", "updateFieldArrayRootError", "fieldArrayErrors", "isBoolean", "isFileInput", "isFunction", "isHTMLElement", "owner", "ownerDocument", "defaultView", "isMessage", "isRadioInput", "isRegex", "RegExp", "defaultResult", "validResult", "getCheckboxValue", "option", "attributes", "defaultReturn", "getRadioValue", "previous", "getValidateError", "every", "getValueAndMessage", "validationData", "validateField", "shouldUseNativeValidation", "isFieldArray", "valueAsNumber", "inputValue", "inputRef", "isRadio", "isCheckBox", "isRadioOrCheckbox", "isEmpty", "appendErrors<PERSON><PERSON><PERSON>", "bind", "getMinMaxMessage", "exceedMax", "maxLengthMessage", "minLengthMessage", "maxType", "minType", "exceedMin", "maxOutput", "minOutput", "valueNumber", "valueDate", "valueAsDate", "convertTimeToDate", "time", "toDateString", "isTime", "isWeek", "maxLengthOutput", "minLengthOutput", "patternValue", "match", "validateError", "validationResult", "fillEmptyArray", "insert", "moveArrayAt", "from", "to", "splice", "prepend", "removeAtIndexes", "indexes", "i", "temp", "removeArrayAt", "sort", "a", "b", "swapArrayAt", "indexA", "indexB", "baseGet", "updatePath", "isEmptyArray", "unset", "paths", "childObject", "updateAt", "field<PERSON><PERSON><PERSON>", "useFieldArray", "keyName", "setFields", "_getFieldArray", "ids", "_fieldIds", "_actioned", "_ref", "fieldArrayName", "updateValues", "updatedFieldArrayValues", "_updateFieldArray", "append$1", "appendValue", "argA", "prepend$1", "prependValue", "remove", "insert$1", "insertValue", "argB", "swap", "move", "update", "item", "isSubmitted", "resolver", "_executeSchema", "then", "existingError", "criteriaMode", "useMemo", "createSubject", "_observers", "observer", "push", "o", "observers", "isPrimitive", "deepEqual", "object1", "object2", "getTime", "keys1", "keys2", "val1", "val2", "isMultipleSelect", "live", "isConnected", "objectHasFunction", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isParentNodeArray", "getDirtyFieldsFromDefaultValues", "dirtyField<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getDirty<PERSON>ields", "getFieldValueAs", "_ref2", "setValueAs", "NaN", "getFieldValue", "files", "selectedOptions", "_ref3", "getResolverOptions", "getRuleValue", "rule", "source", "hasValidation", "schemaErrorLookup", "join", "found<PERSON><PERSON>r", "pop", "skipValidation", "reValidateMode", "unsetEmptyArray", "defaultOptions", "shouldFocusError", "createFormControl", "flushRootRender", "submitCount", "isSubmitting", "unMount", "delayError<PERSON><PERSON><PERSON>", "timer", "should<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>s", "resetOptions", "keepDirtyV<PERSON>ues", "validationModeBeforeSubmit", "validationModeAfterSubmit", "shouldDisplayAllAssociatedErrors", "debounce", "wait", "clearTimeout", "setTimeout", "shouldUpdateValid", "executeBuiltInValidation", "_updateIsValidating", "args", "shouldSetValues", "shouldUpdateFieldsAndState", "_getDirty", "updateErrors", "updateValidAndValue", "shouldSkipSetValueAs", "defaultChecked", "setFieldValue", "updateTouchAndDirty", "fieldValue", "should<PERSON>irty", "shouldRender", "shouldUpdateField", "is<PERSON>revious<PERSON><PERSON>y", "output", "isCurrentFieldPristine", "isPreviousFieldTouched", "shouldRenderByError", "previousFieldError", "delayError", "updatedFormState", "context", "executeSchemaAndUpdateState", "should<PERSON>nly<PERSON><PERSON><PERSON><PERSON>d", "valid", "isFieldArrayRoot", "fieldError", "getV<PERSON>ues", "fieldReference", "for<PERSON>ach", "optionRef", "selected", "checkboxRef", "radioRef", "shouldTouch", "shouldValidate", "trigger", "set<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "setValue", "cloneValue", "isFieldValueUpdated", "getCurrentFieldValue", "shouldSkipValidation", "deps", "watched", "previousErrorLookupResult", "errorLookupResult", "fieldNames", "Promise", "getFieldState", "clearErrors", "inputName", "payload", "delete", "keepValue", "keepError", "keep<PERSON>irty", "keepTouched", "keepDefaultValue", "keepIsValid", "disabledIsDefined", "progressive", "fieldRef", "querySelectorAll", "radioOrCheckbox", "_focusError", "onValid", "onInvalid", "e", "preventDefault", "persist", "reset<PERSON>ield", "_reset", "keepStateOptions", "updatedValues", "cloneUpdatedValues", "keepDefaultValues", "keepV<PERSON>ues", "form", "closest", "reset", "keepSubmitCount", "keepIsSubmitted", "keepErrors", "setFocus", "shouldSelect", "_resetDefaultValues", "useForm", "_formControl"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\utils\\isCheckBoxInput.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\utils\\isDateObject.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\utils\\isNullOrUndefined.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\utils\\isObject.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\logic\\getEventValue.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\logic\\getNodeParentName.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\logic\\isNameInFieldArray.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\utils\\isPlainObject.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\utils\\isWeb.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\utils\\cloneObject.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\utils\\compact.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\utils\\isUndefined.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\utils\\get.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\constants.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\useFormContext.tsx", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\logic\\getProxyFormState.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\utils\\isEmptyObject.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\logic\\shouldRenderFormState.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\utils\\convertToArrayPayload.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\logic\\shouldSubscribeByName.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\useSubscribe.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\useFormState.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\utils\\isString.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\logic\\generateWatchOutput.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\useWatch.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\utils\\isKey.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\utils\\stringToPath.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\utils\\set.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\useController.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\controller.tsx", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\form.tsx", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\logic\\appendErrors.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\logic\\focusFieldBy.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\logic\\generateId.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\logic\\getFocusFieldName.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\logic\\getValidationModes.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\logic\\isWatched.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\logic\\updateFieldArrayRootError.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\utils\\isBoolean.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\utils\\isFileInput.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\utils\\isFunction.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\utils\\isHTMLElement.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\utils\\isMessage.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\utils\\isRadioInput.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\utils\\isRegex.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\logic\\getCheckboxValue.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\logic\\getRadioValue.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\logic\\getValidateError.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\logic\\getValueAndMessage.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\logic\\validateField.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\utils\\append.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\utils\\fillEmptyArray.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\utils\\insert.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\utils\\move.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\utils\\prepend.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\utils\\remove.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\utils\\swap.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\utils\\unset.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\utils\\update.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\useFieldArray.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\utils\\createSubject.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\utils\\isPrimitive.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\utils\\deepEqual.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\utils\\isMultipleSelect.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\utils\\isRadioOrCheckbox.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\utils\\live.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\utils\\objectHasFunction.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\logic\\getDirtyFields.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\logic\\getFieldValueAs.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\logic\\getFieldValue.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\logic\\getResolverOptions.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\logic\\getRuleValue.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\logic\\hasValidation.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\logic\\schemaErrorLookup.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\logic\\skipValidation.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\logic\\unsetEmptyArray.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\logic\\createFormControl.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\react-hook-form\\src\\useForm.ts"], "sourcesContent": ["import { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLInputElement =>\n  element.type === 'checkbox';\n", "export default (value: unknown): value is Date => value instanceof Date;\n", "export default (value: unknown): value is null | undefined => value == null;\n", "import isDateObject from './isDateObject';\nimport isNullOrUndefined from './isNullOrUndefined';\n\nexport const isObjectType = (value: unknown) => typeof value === 'object';\n\nexport default <T extends object>(value: unknown): value is T =>\n  !isNullOrUndefined(value) &&\n  !Array.isArray(value) &&\n  isObjectType(value) &&\n  !isDateObject(value);\n", "import isCheckBoxInput from '../utils/isCheckBoxInput';\nimport isObject from '../utils/isObject';\n\ntype Event = { target: any };\n\nexport default (event: unknown) =>\n  isObject(event) && (event as Event).target\n    ? isCheckBoxInput((event as Event).target)\n      ? (event as Event).target.checked\n      : (event as Event).target.value\n    : event;\n", "export default (name: string) =>\n  name.substring(0, name.search(/\\.\\d+(\\.|$)/)) || name;\n", "import { InternalFieldName } from '../types';\n\nimport getNodeParentName from './getNodeParentName';\n\nexport default (names: Set<InternalFieldName>, name: InternalFieldName) =>\n  names.has(getNodeParentName(name));\n", "import isObject from './isObject';\n\nexport default (tempObject: object) => {\n  const prototypeCopy =\n    tempObject.constructor && tempObject.constructor.prototype;\n\n  return (\n    isObject(prototypeCopy) && prototypeCopy.hasOwnProperty('isPrototypeOf')\n  );\n};\n", "export default typeof window !== 'undefined' &&\n  typeof window.HTMLElement !== 'undefined' &&\n  typeof document !== 'undefined';\n", "import isObject from './isObject';\nimport isPlainObject from './isPlainObject';\nimport isWeb from './isWeb';\n\nexport default function cloneObject<T>(data: T): T {\n  let copy: any;\n  const isArray = Array.isArray(data);\n\n  if (data instanceof Date) {\n    copy = new Date(data);\n  } else if (data instanceof Set) {\n    copy = new Set(data);\n  } else if (\n    !(isWeb && (data instanceof Blob || data instanceof FileList)) &&\n    (isArray || isObject(data))\n  ) {\n    copy = isArray ? [] : {};\n\n    if (!isArray && !isPlainObject(data)) {\n      copy = data;\n    } else {\n      for (const key in data) {\n        if (data.hasOwnProperty(key)) {\n          copy[key] = cloneObject(data[key]);\n        }\n      }\n    }\n  } else {\n    return data;\n  }\n\n  return copy;\n}\n", "export default <TValue>(value: TValue[]) =>\n  Array.isArray(value) ? value.filter(Boolean) : [];\n", "export default (val: unknown): val is undefined => val === undefined;\n", "import compact from './compact';\nimport isNullOrUndefined from './isNullOrUndefined';\nimport isObject from './isObject';\nimport isUndefined from './isUndefined';\n\nexport default <T>(obj: T, path?: string, defaultValue?: unknown): any => {\n  if (!path || !isObject(obj)) {\n    return defaultValue;\n  }\n\n  const result = compact(path.split(/[,[\\].]+?/)).reduce(\n    (result, key) =>\n      isNullOrUndefined(result) ? result : result[key as keyof {}],\n    obj,\n  );\n\n  return isUndefined(result) || result === obj\n    ? isUndefined(obj[path as keyof T])\n      ? defaultValue\n      : obj[path as keyof T]\n    : result;\n};\n", "import { ValidationMode } from './types';\n\nexport const EVENTS = {\n  BLUR: 'blur',\n  FOCUS_OUT: 'focusout',\n  CHANGE: 'change',\n};\n\nexport const VALIDATION_MODE: ValidationMode = {\n  onBlur: 'onBlur',\n  onChange: 'onChange',\n  onSubmit: 'onSubmit',\n  onTouched: 'onTouched',\n  all: 'all',\n};\n\nexport const INPUT_VALIDATION_RULES = {\n  max: 'max',\n  min: 'min',\n  maxLength: 'maxLength',\n  minLength: 'minLength',\n  pattern: 'pattern',\n  required: 'required',\n  validate: 'validate',\n};\n", "import React from 'react';\n\nimport { FieldValues, FormProviderProps, UseFormReturn } from './types';\n\nconst HookFormContext = React.createContext<UseFormReturn | null>(null);\n\n/**\n * This custom hook allows you to access the form context. useFormContext is intended to be used in deeply nested structures, where it would become inconvenient to pass the context as a prop. To be used with {@link FormProvider}.\n *\n * @remarks\n * [API](https://react-hook-form.com/api/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @returns return all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nexport const useFormContext = <\n  TFieldValues extends FieldValues,\n  TransformedValues extends FieldValues | undefined = undefined,\n>(): UseFormReturn<TFieldValues> =>\n  React.useContext(HookFormContext) as UseFormReturn<\n    TFieldValues,\n    TransformedValues\n  >;\n\n/**\n * A provider component that propagates the `useForm` methods to all children components via [React Context](https://reactjs.org/docs/context.html) API. To be used with {@link useFormContext}.\n *\n * @remarks\n * [API](https://react-hook-form.com/api/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @param props - all useFrom methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nexport const FormProvider = <\n  TFieldValues extends FieldValues,\n  TContext = any,\n  TTransformedValues extends FieldValues | undefined = undefined,\n>(\n  props: FormProviderProps<TFieldValues, TContext, TTransformedValues>,\n) => {\n  const { children, ...data } = props;\n  return (\n    <HookFormContext.Provider value={data as unknown as UseFormReturn}>\n      {children}\n    </HookFormContext.Provider>\n  );\n};\n", "import { VALIDATION_MODE } from '../constants';\nimport { Control, FieldValues, FormState, ReadFormState } from '../types';\n\nexport default <TFieldValues extends FieldValues, TContext = any>(\n  formState: FormState<TFieldValues>,\n  control: Control<TFieldValues, TContext>,\n  localProxyFormState?: ReadFormState,\n  isRoot = true,\n) => {\n  const result = {\n    defaultValues: control._defaultValues,\n  } as typeof formState;\n\n  for (const key in formState) {\n    Object.defineProperty(result, key, {\n      get: () => {\n        const _key = key as keyof FormState<TFieldValues> & keyof ReadFormState;\n\n        if (control._proxyFormState[_key] !== VALIDATION_MODE.all) {\n          control._proxyFormState[_key] = !isRoot || VALIDATION_MODE.all;\n        }\n\n        localProxyFormState && (localProxyFormState[_key] = true);\n        return formState[_key];\n      },\n    });\n  }\n\n  return result;\n};\n", "import { EmptyObject } from '../types';\n\nimport isObject from './isObject';\n\nexport default (value: unknown): value is EmptyObject =>\n  isObject(value) && !Object.keys(value).length;\n", "import { VALIDATION_MODE } from '../constants';\nimport {\n  Control,\n  FieldValues,\n  FormState,\n  InternalFieldName,\n  ReadFormState,\n} from '../types';\nimport isEmptyObject from '../utils/isEmptyObject';\n\nexport default <T extends FieldValues, K extends ReadFormState>(\n  formStateData: Partial<FormState<T>> & { name?: InternalFieldName },\n  _proxyFormState: K,\n  updateFormState: Control<T>['_updateFormState'],\n  isRoot?: boolean,\n) => {\n  updateFormState(formStateData);\n  const { name, ...formState } = formStateData;\n\n  return (\n    isEmptyObject(formState) ||\n    Object.keys(formState).length >= Object.keys(_proxyFormState).length ||\n    Object.keys(formState).find(\n      (key) =>\n        _proxyFormState[key as keyof ReadFormState] ===\n        (!isRoot || VALIDATION_MODE.all),\n    )\n  );\n};\n", "export default <T>(value: T) => (Array.isArray(value) ? value : [value]);\n", "import convertToArrayPayload from '../utils/convertToArrayPayload';\n\nexport default <T extends string | string[] | undefined>(\n  name?: T,\n  signalName?: string,\n  exact?: boolean,\n) =>\n  exact && signalName\n    ? name === signalName\n    : !name ||\n      !signalName ||\n      name === signalName ||\n      convertToArrayPayload(name).some(\n        (currentName) =>\n          currentName &&\n          (currentName.startsWith(signalName) ||\n            signalName.startsWith(currentName)),\n      );\n", "import React from 'react';\n\nimport { Subject } from './utils/createSubject';\n\ntype Props<T> = {\n  disabled?: boolean;\n  subject: Subject<T>;\n  next: (value: T) => void;\n};\n\nexport function useSubscribe<T>(props: Props<T>) {\n  const _props = React.useRef(props);\n  _props.current = props;\n\n  React.useEffect(() => {\n    const subscription =\n      !props.disabled &&\n      _props.current.subject &&\n      _props.current.subject.subscribe({\n        next: _props.current.next,\n      });\n\n    return () => {\n      subscription && subscription.unsubscribe();\n    };\n  }, [props.disabled]);\n}\n", "import React from 'react';\n\nimport getProxyFormState from './logic/getProxyFormState';\nimport shouldRenderFormState from './logic/shouldRenderFormState';\nimport shouldSubscribeByName from './logic/shouldSubscribeByName';\nimport {\n  FieldValues,\n  FormState,\n  InternalFieldName,\n  UseFormStateProps,\n  UseFormStateReturn,\n} from './types';\nimport { useFormContext } from './useFormContext';\nimport { useSubscribe } from './useSubscribe';\n\n/**\n * This custom hook allows you to subscribe to each form state, and isolate the re-render at the custom hook level. It has its scope in terms of form state subscription, so it would not affect other useFormState and useForm. Using this hook can reduce the re-render impact on large and complex form application.\n *\n * @remarks\n * [API](https://react-hook-form.com/api/useformstate) • [Demo](https://codesandbox.io/s/useformstate-75xly)\n *\n * @param props - include options on specify fields to subscribe. {@link UseFormStateReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, control } = useForm({\n *     defaultValues: {\n *     firstName: \"firstName\"\n *   }});\n *   const { dirtyFields } = useFormState({\n *     control\n *   });\n *   const onSubmit = (data) => console.log(data);\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input {...register(\"firstName\")} placeholder=\"First Name\" />\n *       {dirtyFields.firstName && <p>Field is dirty.</p>}\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nfunction useFormState<TFieldValues extends FieldValues = FieldValues>(\n  props?: UseFormStateProps<TFieldValues>,\n): UseFormStateReturn<TFieldValues> {\n  const methods = useFormContext<TFieldValues>();\n  const { control = methods.control, disabled, name, exact } = props || {};\n  const [formState, updateFormState] = React.useState(control._formState);\n  const _mounted = React.useRef(true);\n  const _localProxyFormState = React.useRef({\n    isDirty: false,\n    isLoading: false,\n    dirtyFields: false,\n    touchedFields: false,\n    isValidating: false,\n    isValid: false,\n    errors: false,\n  });\n  const _name = React.useRef(name);\n\n  _name.current = name;\n\n  useSubscribe({\n    disabled,\n    next: (\n      value: Partial<FormState<TFieldValues>> & { name?: InternalFieldName },\n    ) =>\n      _mounted.current &&\n      shouldSubscribeByName(\n        _name.current as InternalFieldName,\n        value.name,\n        exact,\n      ) &&\n      shouldRenderFormState(\n        value,\n        _localProxyFormState.current,\n        control._updateFormState,\n      ) &&\n      updateFormState({\n        ...control._formState,\n        ...value,\n      }),\n    subject: control._subjects.state,\n  });\n\n  React.useEffect(() => {\n    _mounted.current = true;\n    _localProxyFormState.current.isValid && control._updateValid(true);\n\n    return () => {\n      _mounted.current = false;\n    };\n  }, [control]);\n\n  return getProxyFormState(\n    formState,\n    control,\n    _localProxyFormState.current,\n    false,\n  );\n}\n\nexport { useFormState };\n", "export default (value: unknown): value is string => typeof value === 'string';\n", "import { DeepPartial, FieldValues, Names } from '../types';\nimport get from '../utils/get';\nimport isString from '../utils/isString';\n\nexport default <T>(\n  names: string | string[] | undefined,\n  _names: Names,\n  formValues?: FieldValues,\n  isGlobal?: boolean,\n  defaultValue?: DeepPartial<T> | unknown,\n) => {\n  if (isString(names)) {\n    isGlobal && _names.watch.add(names);\n    return get(formValues, names, defaultValue);\n  }\n\n  if (Array.isArray(names)) {\n    return names.map(\n      (fieldName) => (\n        isGlobal && _names.watch.add(fieldName), get(formValues, fieldName)\n      ),\n    );\n  }\n\n  isGlobal && (_names.watchAll = true);\n\n  return formValues;\n};\n", "import React from 'react';\n\nimport generateWatchOutput from './logic/generateWatchOutput';\nimport shouldSubscribeByName from './logic/shouldSubscribeByName';\nimport cloneObject from './utils/cloneObject';\nimport {\n  Control,\n  DeepPartialSkipArrayKey,\n  FieldPath,\n  FieldPathValue,\n  FieldPathValues,\n  FieldValues,\n  InternalFieldName,\n  UseWatchProps,\n} from './types';\nimport { useFormContext } from './useFormContext';\nimport { useSubscribe } from './useSubscribe';\n\n/**\n * Subscribe to the entire form values change and re-render at the hook level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/api/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @param props - defaultValue, disable subscription and match exact name.\n *\n * @example\n * ```tsx\n * const { watch } = useForm();\n * const values = useWatch({\n *   control,\n *   defaultValue: {\n *     name: \"data\"\n *   },\n *   exact: false,\n * })\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n>(props: {\n  defaultValue?: DeepPartialSkipArrayKey<TFieldValues>;\n  control?: Control<TFieldValues>;\n  disabled?: boolean;\n  exact?: boolean;\n}): DeepPartialSkipArrayKey<TFieldValues>;\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/api/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @param props - defaultValue, disable subscription and match exact name.\n *\n * @example\n * ```tsx\n * const { watch } = useForm();\n * const values = useWatch({\n *   control,\n *   name: \"fieldA\",\n *   defaultValue: \"default value\",\n *   exact: false,\n * })\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n  TFieldName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n>(props: {\n  name: TFieldName;\n  defaultValue?: FieldPathValue<TFieldValues, TFieldName>;\n  control?: Control<TFieldValues>;\n  disabled?: boolean;\n  exact?: boolean;\n}): FieldPathValue<TFieldValues, TFieldName>;\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/api/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @param props - defaultValue, disable subscription and match exact name.\n *\n * @example\n * ```tsx\n * const { watch } = useForm();\n * const values = useWatch({\n *   control,\n *   name: [\"fieldA\", \"fieldB\"],\n *   defaultValue: {\n *     fieldA: \"data\",\n *     fieldB: \"data\"\n *   },\n *   exact: false,\n * })\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n  TFieldNames extends readonly FieldPath<TFieldValues>[] = readonly FieldPath<TFieldValues>[],\n>(props: {\n  name: readonly [...TFieldNames];\n  defaultValue?: DeepPartialSkipArrayKey<TFieldValues>;\n  control?: Control<TFieldValues>;\n  disabled?: boolean;\n  exact?: boolean;\n}): FieldPathValues<TFieldValues, TFieldNames>;\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/api/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @example\n * ```tsx\n * // can skip passing down the control into useWatch if the form is wrapped with the FormProvider\n * const values = useWatch()\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n>(): DeepPartialSkipArrayKey<TFieldValues>;\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/api/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @example\n * ```tsx\n * const { watch } = useForm();\n * const values = useWatch({\n *   name: \"fieldName\"\n *   control,\n * })\n * ```\n */\nexport function useWatch<TFieldValues extends FieldValues>(\n  props?: UseWatchProps<TFieldValues>,\n) {\n  const methods = useFormContext();\n  const {\n    control = methods.control,\n    name,\n    defaultValue,\n    disabled,\n    exact,\n  } = props || {};\n  const _name = React.useRef(name);\n\n  _name.current = name;\n\n  useSubscribe({\n    disabled,\n    subject: control._subjects.values,\n    next: (formState: { name?: InternalFieldName; values?: FieldValues }) => {\n      if (\n        shouldSubscribeByName(\n          _name.current as InternalFieldName,\n          formState.name,\n          exact,\n        )\n      ) {\n        updateValue(\n          cloneObject(\n            generateWatchOutput(\n              _name.current as InternalFieldName | InternalFieldName[],\n              control._names,\n              formState.values || control._formValues,\n              false,\n              defaultValue,\n            ),\n          ),\n        );\n      }\n    },\n  });\n\n  const [value, updateValue] = React.useState(\n    control._getWatch(\n      name as InternalFieldName,\n      defaultValue as DeepPartialSkipArrayKey<TFieldValues>,\n    ),\n  );\n\n  React.useEffect(() => control._removeUnmounted());\n\n  return value;\n}\n", "export default (value: string) => /^\\w*$/.test(value);\n", "import compact from './compact';\n\nexport default (input: string): string[] =>\n  compact(input.replace(/[\"|']|\\]/g, '').split(/\\.|\\[/));\n", "import { FieldValues } from '../types';\n\nimport isKey from './isKey';\nimport isObject from './isObject';\nimport stringToPath from './stringToPath';\n\nexport default function set(\n  object: FieldValues,\n  path: string,\n  value?: unknown,\n) {\n  let index = -1;\n  const tempPath = isKey(path) ? [path] : stringToPath(path);\n  const length = tempPath.length;\n  const lastIndex = length - 1;\n\n  while (++index < length) {\n    const key = tempPath[index];\n    let newValue = value;\n\n    if (index !== lastIndex) {\n      const objValue = object[key];\n      newValue =\n        isObject(objValue) || Array.isArray(objValue)\n          ? objValue\n          : !isNaN(+tempPath[index + 1])\n          ? []\n          : {};\n    }\n    object[key] = newValue;\n    object = object[key];\n  }\n  return object;\n}\n", "import React from 'react';\n\nimport getEventValue from './logic/getEventValue';\nimport isNameInFieldArray from './logic/isNameInFieldArray';\nimport cloneObject from './utils/cloneObject';\nimport get from './utils/get';\nimport isUndefined from './utils/isUndefined';\nimport { EVENTS } from './constants';\nimport {\n  ControllerFieldState,\n  Field,\n  FieldPath,\n  FieldPathValue,\n  FieldValues,\n  InternalFieldName,\n  UseControllerProps,\n  UseControllerReturn,\n} from './types';\nimport { useFormContext } from './useFormContext';\nimport { useFormState } from './useFormState';\nimport { useWatch } from './useWatch';\nimport { set } from './utils';\n\n/**\n * Custom hook to work with controlled component, this function provide you with both form and field level state. Re-render is isolated at the hook level.\n *\n * @remarks\n * [API](https://react-hook-form.com/api/usecontroller) • [Demo](https://codesandbox.io/s/usecontroller-0o8px)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns field properties, field and form state. {@link UseControllerReturn}\n *\n * @example\n * ```tsx\n * function Input(props) {\n *   const { field, fieldState, formState } = useController(props);\n *   return (\n *     <div>\n *       <input {...field} placeholder={props.name} />\n *       <p>{fieldState.isTouched && \"Touched\"}</p>\n *       <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *     </div>\n *   );\n * }\n * ```\n */\nexport function useController<\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n>(\n  props: UseControllerProps<TFieldValues, TName>,\n): UseControllerReturn<TFieldValues, TName> {\n  const methods = useFormContext<TFieldValues>();\n  const { name, control = methods.control, shouldUnregister } = props;\n  const isArrayField = isNameInFieldArray(control._names.array, name);\n  const value = useWatch({\n    control,\n    name,\n    defaultValue: get(\n      control._formValues,\n      name,\n      get(control._defaultValues, name, props.defaultValue),\n    ),\n    exact: true,\n  }) as FieldPathValue<TFieldValues, TName>;\n  const formState = useFormState({\n    control,\n    name,\n  });\n\n  const _registerProps = React.useRef(\n    control.register(name, {\n      ...props.rules,\n      value,\n    }),\n  );\n\n  _registerProps.current = control.register(name, props.rules);\n\n  React.useEffect(() => {\n    const _shouldUnregisterField =\n      control._options.shouldUnregister || shouldUnregister;\n\n    const updateMounted = (name: InternalFieldName, value: boolean) => {\n      const field: Field = get(control._fields, name);\n\n      if (field) {\n        field._f.mount = value;\n      }\n    };\n\n    updateMounted(name, true);\n\n    if (_shouldUnregisterField) {\n      const value = cloneObject(get(control._options.defaultValues, name));\n      set(control._defaultValues, name, value);\n      if (isUndefined(get(control._formValues, name))) {\n        set(control._formValues, name, value);\n      }\n    }\n\n    return () => {\n      (\n        isArrayField\n          ? _shouldUnregisterField && !control._state.action\n          : _shouldUnregisterField\n      )\n        ? control.unregister(name)\n        : updateMounted(name, false);\n    };\n  }, [name, control, isArrayField, shouldUnregister]);\n\n  return {\n    field: {\n      name,\n      value,\n      onChange: React.useCallback(\n        (event) =>\n          _registerProps.current.onChange({\n            target: {\n              value: getEventValue(event),\n              name: name as InternalFieldName,\n            },\n            type: EVENTS.CHANGE,\n          }),\n        [name],\n      ),\n      onBlur: React.useCallback(\n        () =>\n          _registerProps.current.onBlur({\n            target: {\n              value: get(control._formValues, name),\n              name: name as InternalFieldName,\n            },\n            type: EVENTS.BLUR,\n          }),\n        [name, control],\n      ),\n      ref: (elm) => {\n        const field = get(control._fields, name);\n\n        if (field && elm) {\n          field._f.ref = {\n            focus: () => elm.focus(),\n            select: () => elm.select(),\n            setCustomValidity: (message: string) =>\n              elm.setCustomValidity(message),\n            reportValidity: () => elm.reportValidity(),\n          };\n        }\n      },\n    },\n    formState,\n    fieldState: Object.defineProperties(\n      {},\n      {\n        invalid: {\n          enumerable: true,\n          get: () => !!get(formState.errors, name),\n        },\n        isDirty: {\n          enumerable: true,\n          get: () => !!get(formState.dirtyFields, name),\n        },\n        isTouched: {\n          enumerable: true,\n          get: () => !!get(formState.touchedFields, name),\n        },\n        error: {\n          enumerable: true,\n          get: () => get(formState.errors, name),\n        },\n      },\n    ) as ControllerFieldState,\n  };\n}\n", "import { ControllerProps, FieldPath, FieldValues } from './types';\nimport { useController } from './useController';\n\n/**\n * Component based on `useController` hook to work with controlled component.\n *\n * @remarks\n * [API](https://react-hook-form.com/api/usecontroller/controller) • [Demo](https://codesandbox.io/s/react-hook-form-v6-controller-ts-jwyzw) • [Video](https://www.youtube.com/watch?v=N2UNk_UCVyA)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns provide field handler functions, field and form state.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control } = useForm<FormValues>({\n *     defaultValues: {\n *       test: \"\"\n *     }\n *   });\n *\n *   return (\n *     <form>\n *       <Controller\n *         control={control}\n *         name=\"test\"\n *         render={({ field: { onChange, onBlur, value, ref }, formState, fieldState }) => (\n *           <>\n *             <input\n *               onChange={onChange} // send value to hook form\n *               onBlur={onBlur} // notify when input is touched\n *               value={value} // return updated value\n *               ref={ref} // set ref for focus management\n *             />\n *             <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *             <p>{fieldState.isTouched ? \"touched\" : \"\"}</p>\n *           </>\n *         )}\n *       />\n *     </form>\n *   );\n * }\n * ```\n */\nconst Controller = <\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n>(\n  props: ControllerProps<TFieldValues, TName>,\n) => props.render(useController<TFieldValues, TName>(props));\n\nexport { Controller };\n", "import * as React from 'react';\n\nimport get from './utils/get';\nimport { Control, FieldValues, FormSubmitHandler } from './types';\nimport { useFormContext } from './useFormContext';\n\nexport type FormProps<\n  TFieldValues extends FieldValues,\n  TTransformedValues extends FieldValues | undefined = undefined,\n> = Omit<React.FormHTMLAttributes<HTMLFormElement>, 'onError'> &\n  Partial<{\n    control: Control<TFieldValues>;\n    headers: Record<string, string>;\n    validateStatus: (status: number) => boolean;\n    onError: ({\n      response,\n      error,\n    }:\n      | {\n          response: Response;\n          error?: undefined;\n        }\n      | {\n          response?: undefined;\n          error: unknown;\n        }) => void;\n    onSuccess: ({ response }: { response: Response }) => void;\n    onSubmit: TTransformedValues extends FieldValues\n      ? FormSubmitHandler<TTransformedValues>\n      : FormSubmitHandler<TFieldValues>;\n    method: 'post' | 'put' | 'delete';\n    children: React.ReactNode | React.ReactNode[];\n    render: (props: {\n      submit: (e?: React.FormEvent) => void;\n    }) => React.ReactNode | React.ReactNode[];\n    encType:\n      | 'application/x-www-form-urlencoded'\n      | 'multipart/form-data'\n      | 'text/plain'\n      | 'application/json';\n  }>;\n\nconst POST_REQUEST = 'post';\n\n/**\n * Form component to manage submission.\n *\n * @param props - to setup submission detail. {@link FormProps}\n *\n * @returns form component or headless render prop.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control, formState: { errors } } = useForm();\n *\n *   return (\n *     <Form action=\"/api\" control={control}>\n *       <input {...register(\"name\")} />\n *       <p>{errors?.root?.server && 'Server error'}</p>\n *       <button>Submit</button>\n *     </Form>\n *   );\n * }\n * ```\n */\nexport function Form<\n  T extends FieldValues,\n  U extends FieldValues | undefined = undefined,\n>(props: FormProps<T, U>) {\n  const methods = useFormContext<T>();\n  const [mounted, setMounted] = React.useState(false);\n  const {\n    control = methods.control,\n    onSubmit,\n    children,\n    action,\n    method = POST_REQUEST,\n    headers,\n    encType,\n    onError,\n    render,\n    onSuccess,\n    validateStatus,\n    ...rest\n  } = props;\n\n  const submit = async (event?: React.BaseSyntheticEvent) => {\n    let hasError = false;\n    let type = '';\n\n    await control.handleSubmit(async (data) => {\n      const formData = new FormData();\n      let formDataJson = '';\n\n      try {\n        formDataJson = JSON.stringify(data);\n      } catch {}\n\n      for (const name of control._names.mount) {\n        formData.append(name, get(data, name));\n      }\n\n      if (onSubmit) {\n        onSubmit({\n          data,\n          event,\n          method,\n          formData,\n          formDataJson,\n        });\n      }\n\n      if (action) {\n        try {\n          const shouldStringifySubmissionData = [\n            headers && headers['Content-Type'],\n            encType,\n          ].some((value) => value && value.includes('json'));\n\n          const response = await fetch(action, {\n            method,\n            headers: {\n              ...headers,\n              ...(encType ? { 'Content-Type': encType } : {}),\n            },\n            body: shouldStringifySubmissionData ? formDataJson : formData,\n          });\n\n          if (\n            response &&\n            (validateStatus\n              ? !validateStatus(response.status)\n              : response.status < 200 || response.status >= 300)\n          ) {\n            hasError = true;\n            onError && onError({ response });\n            type = String(response.status);\n          } else {\n            onSuccess && onSuccess({ response });\n          }\n        } catch (error: unknown) {\n          hasError = true;\n          onError && onError({ error });\n        }\n      }\n    })(event);\n\n    if (hasError && props.control) {\n      props.control._subjects.state.next({\n        isSubmitSuccessful: false,\n      });\n      props.control.setError('root.server', {\n        type,\n      });\n    }\n  };\n\n  React.useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  return render ? (\n    <>\n      {render({\n        submit,\n      })}\n    </>\n  ) : (\n    <form\n      noValidate={mounted}\n      action={action}\n      method={method}\n      encType={encType}\n      onSubmit={submit}\n      {...rest}\n    >\n      {children}\n    </form>\n  );\n}\n", "import {\n  InternalFieldErrors,\n  InternalFieldName,\n  ValidateResult,\n} from '../types';\n\nexport default (\n  name: InternalFieldName,\n  validateAllFieldCriteria: boolean,\n  errors: InternalFieldErrors,\n  type: string,\n  message: ValidateResult,\n) =>\n  validateAllFieldCriteria\n    ? {\n        ...errors[name],\n        types: {\n          ...(errors[name] && errors[name]!.types ? errors[name]!.types : {}),\n          [type]: message || true,\n        },\n      }\n    : {};\n", "import { FieldRefs, InternalFieldName } from '../types';\nimport { get } from '../utils';\nimport isObject from '../utils/isObject';\n\nconst focusFieldBy = (\n  fields: FieldRefs,\n  callback: (name?: string) => boolean,\n  fieldsNames?: Set<InternalFieldName> | InternalFieldName[],\n) => {\n  for (const key of fieldsNames || Object.keys(fields)) {\n    const field = get(fields, key);\n\n    if (field) {\n      const { _f, ...currentField } = field;\n\n      if (_f && callback(_f.name)) {\n        if (_f.ref.focus) {\n          _f.ref.focus();\n          break;\n        } else if (_f.refs && _f.refs[0].focus) {\n          _f.refs[0].focus();\n          break;\n        }\n      } else if (isObject(currentField)) {\n        focusFieldBy(currentField, callback);\n      }\n    }\n  }\n};\n\nexport default focusFieldBy;\n", "export default () => {\n  const d =\n    typeof performance === 'undefined' ? Date.now() : performance.now() * 1000;\n\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\n    const r = (Math.random() * 16 + d) % 16 | 0;\n\n    return (c == 'x' ? r : (r & 0x3) | 0x8).toString(16);\n  });\n};\n", "import { FieldArrayMethodProps, InternalFieldName } from '../types';\nimport isUndefined from '../utils/isUndefined';\n\nexport default (\n  name: InternalFieldName,\n  index: number,\n  options: FieldArrayMethodProps = {},\n): string =>\n  options.shouldFocus || isUndefined(options.shouldFocus)\n    ? options.focusName ||\n      `${name}.${isUndefined(options.focusIndex) ? index : options.focusIndex}.`\n    : '';\n", "import { VALIDATION_MODE } from '../constants';\nimport { Mode } from '../types';\n\nexport default (\n  mode?: Mode,\n): {\n  isOnSubmit: boolean;\n  isOnBlur: boolean;\n  isOnChange: boolean;\n  isOnAll: boolean;\n  isOnTouch: boolean;\n} => ({\n  isOnSubmit: !mode || mode === VALIDATION_MODE.onSubmit,\n  isOnBlur: mode === VALIDATION_MODE.onBlur,\n  isOnChange: mode === VALIDATION_MODE.onChange,\n  isOnAll: mode === VALIDATION_MODE.all,\n  isOnTouch: mode === VALIDATION_MODE.onTouched,\n});\n", "import { InternalFieldName, Names } from '../types';\n\nexport default (\n  name: InternalFieldName,\n  _names: Names,\n  isBlurEvent?: boolean,\n) =>\n  !isBlurEvent &&\n  (_names.watchAll ||\n    _names.watch.has(name) ||\n    [..._names.watch].some(\n      (watchName) =>\n        name.startsWith(watchName) &&\n        /^\\.\\w+/.test(name.slice(watchName.length)),\n    ));\n", "import {\n  FieldError,\n  FieldErrors,\n  FieldValues,\n  InternalFieldName,\n} from '../types';\nimport compact from '../utils/compact';\nimport get from '../utils/get';\nimport set from '../utils/set';\n\nexport default <T extends FieldValues = FieldValues>(\n  errors: FieldErrors<T>,\n  error: Partial<Record<string, FieldError>>,\n  name: InternalFieldName,\n): FieldErrors<T> => {\n  const fieldArrayErrors = compact(get(errors, name));\n  set(fieldArrayErrors, 'root', error[name]);\n  set(errors, name, fieldArrayErrors);\n  return errors;\n};\n", "export default (value: unknown): value is boolean => typeof value === 'boolean';\n", "import { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLInputElement =>\n  element.type === 'file';\n", "export default (value: unknown): value is Function =>\n  typeof value === 'function';\n", "import isWeb from './isWeb';\n\nexport default (value: unknown): value is HTMLElement => {\n  if (!isWeb) {\n    return false;\n  }\n\n  const owner = value ? ((value as HTMLElement).ownerDocument as Document) : 0;\n  return (\n    value instanceof\n    (owner && owner.defaultView ? owner.defaultView.HTMLElement : HTMLElement)\n  );\n};\n", "import { Message } from '../types';\nimport isString from '../utils/isString';\n\nexport default (value: unknown): value is Message => isString(value);\n", "import { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLInputElement =>\n  element.type === 'radio';\n", "export default (value: unknown): value is RegExp => value instanceof RegExp;\n", "import isUndefined from '../utils/isUndefined';\n\ntype CheckboxFieldResult = {\n  isValid: boolean;\n  value: string | string[] | boolean | undefined;\n};\n\nconst defaultResult: CheckboxFieldResult = {\n  value: false,\n  isValid: false,\n};\n\nconst validResult = { value: true, isValid: true };\n\nexport default (options?: HTMLInputElement[]): CheckboxFieldResult => {\n  if (Array.isArray(options)) {\n    if (options.length > 1) {\n      const values = options\n        .filter((option) => option && option.checked && !option.disabled)\n        .map((option) => option.value);\n      return { value: values, isValid: !!values.length };\n    }\n\n    return options[0].checked && !options[0].disabled\n      ? // @ts-expect-error expected to work in the browser\n        options[0].attributes && !isUndefined(options[0].attributes.value)\n        ? isUndefined(options[0].value) || options[0].value === ''\n          ? validResult\n          : { value: options[0].value, isValid: true }\n        : validResult\n      : defaultResult;\n  }\n\n  return defaultResult;\n};\n", "type RadioFieldResult = {\n  isValid: boolean;\n  value: number | string | null;\n};\n\nconst defaultReturn: RadioFieldResult = {\n  isValid: false,\n  value: null,\n};\n\nexport default (options?: HTMLInputElement[]): RadioFieldResult =>\n  Array.isArray(options)\n    ? options.reduce(\n        (previous, option): RadioFieldResult =>\n          option && option.checked && !option.disabled\n            ? {\n                isValid: true,\n                value: option.value,\n              }\n            : previous,\n        defaultReturn,\n      )\n    : defaultReturn;\n", "import { FieldError, Ref, ValidateResult } from '../types';\nimport isBoolean from '../utils/isBoolean';\nimport isMessage from '../utils/isMessage';\n\nexport default function getValidateError(\n  result: ValidateResult,\n  ref: Ref,\n  type = 'validate',\n): FieldError | void {\n  if (\n    isMessage(result) ||\n    (Array.isArray(result) && result.every(isMessage)) ||\n    (isBoolean(result) && !result)\n  ) {\n    return {\n      type,\n      message: isMessage(result) ? result : '',\n      ref,\n    };\n  }\n}\n", "import { ValidationRule } from '../types';\nimport isObject from '../utils/isObject';\nimport isRegex from '../utils/isRegex';\n\nexport default (validationData?: ValidationRule) =>\n  isObject(validationData) && !isRegex(validationData)\n    ? validationData\n    : {\n        value: validationData,\n        message: '',\n      };\n", "import { INPUT_VALIDATION_RULES } from '../constants';\nimport {\n  Field,\n  FieldError,\n  FieldValues,\n  InternalFieldErrors,\n  Message,\n  NativeFieldValue,\n} from '../types';\nimport get from '../utils/get';\nimport isBoolean from '../utils/isBoolean';\nimport isCheckBoxInput from '../utils/isCheckBoxInput';\nimport isEmptyObject from '../utils/isEmptyObject';\nimport isFileInput from '../utils/isFileInput';\nimport isFunction from '../utils/isFunction';\nimport isHTMLElement from '../utils/isHTMLElement';\nimport isMessage from '../utils/isMessage';\nimport isNullOrUndefined from '../utils/isNullOrUndefined';\nimport isObject from '../utils/isObject';\nimport isRadioInput from '../utils/isRadioInput';\nimport isRegex from '../utils/isRegex';\nimport isString from '../utils/isString';\nimport isUndefined from '../utils/isUndefined';\n\nimport appendErrors from './appendErrors';\nimport getCheckboxValue from './getCheckboxValue';\nimport getRadioValue from './getRadioValue';\nimport getValidateError from './getValidateError';\nimport getValueAndMessage from './getValueAndMessage';\n\nexport default async <T extends FieldValues>(\n  field: Field,\n  formValues: T,\n  validateAllFieldCriteria: boolean,\n  shouldUseNativeValidation?: boolean,\n  isFieldArray?: boolean,\n): Promise<InternalFieldErrors> => {\n  const {\n    ref,\n    refs,\n    required,\n    maxLength,\n    minLength,\n    min,\n    max,\n    pattern,\n    validate,\n    name,\n    valueAsNumber,\n    mount,\n    disabled,\n  } = field._f;\n  const inputValue: NativeFieldValue = get(formValues, name);\n  if (!mount || disabled) {\n    return {};\n  }\n  const inputRef: HTMLInputElement = refs ? refs[0] : (ref as HTMLInputElement);\n  const setCustomValidity = (message?: string | boolean) => {\n    if (shouldUseNativeValidation && inputRef.reportValidity) {\n      inputRef.setCustomValidity(isBoolean(message) ? '' : message || '');\n      inputRef.reportValidity();\n    }\n  };\n  const error: InternalFieldErrors = {};\n  const isRadio = isRadioInput(ref);\n  const isCheckBox = isCheckBoxInput(ref);\n  const isRadioOrCheckbox = isRadio || isCheckBox;\n  const isEmpty =\n    ((valueAsNumber || isFileInput(ref)) &&\n      isUndefined(ref.value) &&\n      isUndefined(inputValue)) ||\n    (isHTMLElement(ref) && ref.value === '') ||\n    inputValue === '' ||\n    (Array.isArray(inputValue) && !inputValue.length);\n  const appendErrorsCurry = appendErrors.bind(\n    null,\n    name,\n    validateAllFieldCriteria,\n    error,\n  );\n  const getMinMaxMessage = (\n    exceedMax: boolean,\n    maxLengthMessage: Message,\n    minLengthMessage: Message,\n    maxType = INPUT_VALIDATION_RULES.maxLength,\n    minType = INPUT_VALIDATION_RULES.minLength,\n  ) => {\n    const message = exceedMax ? maxLengthMessage : minLengthMessage;\n    error[name] = {\n      type: exceedMax ? maxType : minType,\n      message,\n      ref,\n      ...appendErrorsCurry(exceedMax ? maxType : minType, message),\n    };\n  };\n\n  if (\n    isFieldArray\n      ? !Array.isArray(inputValue) || !inputValue.length\n      : required &&\n        ((!isRadioOrCheckbox && (isEmpty || isNullOrUndefined(inputValue))) ||\n          (isBoolean(inputValue) && !inputValue) ||\n          (isCheckBox && !getCheckboxValue(refs).isValid) ||\n          (isRadio && !getRadioValue(refs).isValid))\n  ) {\n    const { value, message } = isMessage(required)\n      ? { value: !!required, message: required }\n      : getValueAndMessage(required);\n\n    if (value) {\n      error[name] = {\n        type: INPUT_VALIDATION_RULES.required,\n        message,\n        ref: inputRef,\n        ...appendErrorsCurry(INPUT_VALIDATION_RULES.required, message),\n      };\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(message);\n        return error;\n      }\n    }\n  }\n\n  if (!isEmpty && (!isNullOrUndefined(min) || !isNullOrUndefined(max))) {\n    let exceedMax;\n    let exceedMin;\n    const maxOutput = getValueAndMessage(max);\n    const minOutput = getValueAndMessage(min);\n\n    if (!isNullOrUndefined(inputValue) && !isNaN(inputValue as number)) {\n      const valueNumber =\n        (ref as HTMLInputElement).valueAsNumber ||\n        (inputValue ? +inputValue : inputValue);\n      if (!isNullOrUndefined(maxOutput.value)) {\n        exceedMax = valueNumber > maxOutput.value;\n      }\n      if (!isNullOrUndefined(minOutput.value)) {\n        exceedMin = valueNumber < minOutput.value;\n      }\n    } else {\n      const valueDate =\n        (ref as HTMLInputElement).valueAsDate || new Date(inputValue as string);\n      const convertTimeToDate = (time: unknown) =>\n        new Date(new Date().toDateString() + ' ' + time);\n      const isTime = ref.type == 'time';\n      const isWeek = ref.type == 'week';\n\n      if (isString(maxOutput.value) && inputValue) {\n        exceedMax = isTime\n          ? convertTimeToDate(inputValue) > convertTimeToDate(maxOutput.value)\n          : isWeek\n          ? inputValue > maxOutput.value\n          : valueDate > new Date(maxOutput.value);\n      }\n\n      if (isString(minOutput.value) && inputValue) {\n        exceedMin = isTime\n          ? convertTimeToDate(inputValue) < convertTimeToDate(minOutput.value)\n          : isWeek\n          ? inputValue < minOutput.value\n          : valueDate < new Date(minOutput.value);\n      }\n    }\n\n    if (exceedMax || exceedMin) {\n      getMinMaxMessage(\n        !!exceedMax,\n        maxOutput.message,\n        minOutput.message,\n        INPUT_VALIDATION_RULES.max,\n        INPUT_VALIDATION_RULES.min,\n      );\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(error[name]!.message);\n        return error;\n      }\n    }\n  }\n\n  if (\n    (maxLength || minLength) &&\n    !isEmpty &&\n    (isString(inputValue) || (isFieldArray && Array.isArray(inputValue)))\n  ) {\n    const maxLengthOutput = getValueAndMessage(maxLength);\n    const minLengthOutput = getValueAndMessage(minLength);\n    const exceedMax =\n      !isNullOrUndefined(maxLengthOutput.value) &&\n      inputValue.length > +maxLengthOutput.value;\n    const exceedMin =\n      !isNullOrUndefined(minLengthOutput.value) &&\n      inputValue.length < +minLengthOutput.value;\n\n    if (exceedMax || exceedMin) {\n      getMinMaxMessage(\n        exceedMax,\n        maxLengthOutput.message,\n        minLengthOutput.message,\n      );\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(error[name]!.message);\n        return error;\n      }\n    }\n  }\n\n  if (pattern && !isEmpty && isString(inputValue)) {\n    const { value: patternValue, message } = getValueAndMessage(pattern);\n\n    if (isRegex(patternValue) && !inputValue.match(patternValue)) {\n      error[name] = {\n        type: INPUT_VALIDATION_RULES.pattern,\n        message,\n        ref,\n        ...appendErrorsCurry(INPUT_VALIDATION_RULES.pattern, message),\n      };\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(message);\n        return error;\n      }\n    }\n  }\n\n  if (validate) {\n    if (isFunction(validate)) {\n      const result = await validate(inputValue, formValues);\n      const validateError = getValidateError(result, inputRef);\n\n      if (validateError) {\n        error[name] = {\n          ...validateError,\n          ...appendErrorsCurry(\n            INPUT_VALIDATION_RULES.validate,\n            validateError.message,\n          ),\n        };\n        if (!validateAllFieldCriteria) {\n          setCustomValidity(validateError.message);\n          return error;\n        }\n      }\n    } else if (isObject(validate)) {\n      let validationResult = {} as FieldError;\n\n      for (const key in validate) {\n        if (!isEmptyObject(validationResult) && !validateAllFieldCriteria) {\n          break;\n        }\n\n        const validateError = getValidateError(\n          await validate[key](inputValue, formValues),\n          inputRef,\n          key,\n        );\n\n        if (validateError) {\n          validationResult = {\n            ...validateError,\n            ...appendErrorsCurry(key, validateError.message),\n          };\n\n          setCustomValidity(validateError.message);\n\n          if (validateAllFieldCriteria) {\n            error[name] = validationResult;\n          }\n        }\n      }\n\n      if (!isEmptyObject(validationResult)) {\n        error[name] = {\n          ref: inputRef,\n          ...validationResult,\n        };\n        if (!validateAllFieldCriteria) {\n          return error;\n        }\n      }\n    }\n  }\n\n  setCustomValidity(true);\n  return error;\n};\n", "import convertToArrayPayload from './convertToArrayPayload';\n\nexport default function append<T>(data: T[], value: T | T[]): T[] {\n  return [...data, ...convertToArrayPayload(value)];\n}\n", "export default <T>(value: T | T[]): undefined[] | undefined =>\n  Array.isArray(value) ? value.map(() => undefined) : undefined;\n", "import convertToArrayPayload from './convertToArrayPayload';\n\nexport default function insert<T>(data: T[], index: number): (T | undefined)[];\nexport default function insert<T>(\n  data: T[],\n  index: number,\n  value: T | T[],\n): T[];\nexport default function insert<T>(\n  data: T[],\n  index: number,\n  value?: T | T[],\n): (T | undefined)[] {\n  return [\n    ...data.slice(0, index),\n    ...convertToArrayPayload(value),\n    ...data.slice(index),\n  ];\n}\n", "import isUndefined from './isUndefined';\n\nexport default <T>(\n  data: (T | undefined)[],\n  from: number,\n  to: number,\n): (T | undefined)[] => {\n  if (!Array.isArray(data)) {\n    return [];\n  }\n\n  if (isUndefined(data[to])) {\n    data[to] = undefined;\n  }\n  data.splice(to, 0, data.splice(from, 1)[0]);\n\n  return data;\n};\n", "import convertToArrayPayload from './convertToArrayPayload';\n\nexport default function prepend<T>(data: T[], value: T | T[]): T[] {\n  return [...convertToArrayPayload(value), ...convertToArrayPayload(data)];\n}\n", "import compact from './compact';\nimport convertToArrayPayload from './convertToArrayPayload';\nimport isUndefined from './isUndefined';\n\nfunction removeAtIndexes<T>(data: T[], indexes: number[]): T[] {\n  let i = 0;\n  const temp = [...data];\n\n  for (const index of indexes) {\n    temp.splice(index - i, 1);\n    i++;\n  }\n\n  return compact(temp).length ? temp : [];\n}\n\nexport default <T>(data: T[], index?: number | number[]): T[] =>\n  isUndefined(index)\n    ? []\n    : removeAtIndexes(\n        data,\n        (convertToArrayPayload(index) as number[]).sort((a, b) => a - b),\n      );\n", "export default <T>(data: T[], indexA: number, indexB: number): void => {\n  data[indexA] = [data[indexB], (data[indexB] = data[indexA])][0];\n};\n", "import isEmptyObject from './isEmptyObject';\nimport isKey from './isKey';\nimport isObject from './isObject';\nimport isUndefined from './isUndefined';\nimport stringToPath from './stringToPath';\n\nfunction baseGet(object: any, updatePath: (string | number)[]) {\n  const length = updatePath.slice(0, -1).length;\n  let index = 0;\n\n  while (index < length) {\n    object = isUndefined(object) ? index++ : object[updatePath[index++]];\n  }\n\n  return object;\n}\n\nfunction isEmptyArray(obj: unknown[]) {\n  for (const key in obj) {\n    if (obj.hasOwnProperty(key) && !isUndefined(obj[key])) {\n      return false;\n    }\n  }\n  return true;\n}\n\nexport default function unset(object: any, path: string | (string | number)[]) {\n  const paths = Array.isArray(path)\n    ? path\n    : isKey(path)\n    ? [path]\n    : stringToPath(path);\n\n  const childObject = paths.length === 1 ? object : baseGet(object, paths);\n\n  const index = paths.length - 1;\n  const key = paths[index];\n\n  if (childObject) {\n    delete childObject[key];\n  }\n\n  if (\n    index !== 0 &&\n    ((isObject(childObject) && isEmptyObject(childObject)) ||\n      (Array.isArray(childObject) && isEmptyArray(childObject)))\n  ) {\n    unset(object, paths.slice(0, -1));\n  }\n\n  return object;\n}\n", "export default <T>(fieldValues: T[], index: number, value: T) => {\n  fieldValues[index] = value;\n  return fieldValues;\n};\n", "import React from 'react';\n\nimport focusFieldBy from './logic/focusFieldBy';\nimport generateId from './logic/generateId';\nimport getFocusFieldName from './logic/getFocusFieldName';\nimport getValidationModes from './logic/getValidationModes';\nimport isWatched from './logic/isWatched';\nimport updateFieldArrayRootError from './logic/updateFieldArrayRootError';\nimport validateField from './logic/validateField';\nimport appendAt from './utils/append';\nimport cloneObject from './utils/cloneObject';\nimport convertToArrayPayload from './utils/convertToArrayPayload';\nimport fillEmptyArray from './utils/fillEmptyArray';\nimport get from './utils/get';\nimport insertAt from './utils/insert';\nimport isEmptyObject from './utils/isEmptyObject';\nimport moveArrayAt from './utils/move';\nimport prependAt from './utils/prepend';\nimport removeArrayAt from './utils/remove';\nimport set from './utils/set';\nimport swapArrayAt from './utils/swap';\nimport unset from './utils/unset';\nimport updateAt from './utils/update';\nimport { VALIDATION_MODE } from './constants';\nimport {\n  Control,\n  Field,\n  FieldArray,\n  FieldArrayMethodProps,\n  FieldArrayPath,\n  FieldArrayWithId,\n  FieldErrors,\n  FieldPath,\n  FieldValues,\n  FormState,\n  InternalFieldName,\n  RegisterOptions,\n  UseFieldArrayProps,\n  UseFieldArrayReturn,\n} from './types';\nimport { useFormContext } from './useFormContext';\nimport { useSubscribe } from './useSubscribe';\n\n/**\n * A custom hook that exposes convenient methods to perform operations with a list of dynamic inputs that need to be appended, updated, removed etc. • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn) • [Video](https://youtu.be/4MrbfGSFY2A)\n *\n * @remarks\n * [API](https://react-hook-form.com/api/usefieldarray) • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn)\n *\n * @param props - useFieldArray props\n *\n * @returns methods - functions to manipulate with the Field Arrays (dynamic inputs) {@link UseFieldArrayReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, control, handleSubmit, reset, trigger, setError } = useForm({\n *     defaultValues: {\n *       test: []\n *     }\n *   });\n *   const { fields, append } = useFieldArray({\n *     control,\n *     name: \"test\"\n *   });\n *\n *   return (\n *     <form onSubmit={handleSubmit(data => console.log(data))}>\n *       {fields.map((item, index) => (\n *          <input key={item.id} {...register(`test.${index}.firstName`)}  />\n *       ))}\n *       <button type=\"button\" onClick={() => append({ firstName: \"bill\" })}>\n *         append\n *       </button>\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nexport function useFieldArray<\n  TFieldValues extends FieldValues = FieldValues,\n  TFieldArrayName extends FieldArrayPath<TFieldValues> = FieldArrayPath<TFieldValues>,\n  TKeyName extends string = 'id',\n>(\n  props: UseFieldArrayProps<TFieldValues, TFieldArrayName, TKeyName>,\n): UseFieldArrayReturn<TFieldValues, TFieldArrayName, TKeyName> {\n  const methods = useFormContext();\n  const {\n    control = methods.control,\n    name,\n    keyName = 'id',\n    shouldUnregister,\n  } = props;\n  const [fields, setFields] = React.useState(control._getFieldArray(name));\n  const ids = React.useRef<string[]>(\n    control._getFieldArray(name).map(generateId),\n  );\n  const _fieldIds = React.useRef(fields);\n  const _name = React.useRef(name);\n  const _actioned = React.useRef(false);\n\n  _name.current = name;\n  _fieldIds.current = fields;\n  control._names.array.add(name);\n\n  props.rules &&\n    (control as Control<TFieldValues>).register(\n      name as FieldPath<TFieldValues>,\n      props.rules as RegisterOptions<TFieldValues>,\n    );\n\n  useSubscribe({\n    next: ({\n      values,\n      name: fieldArrayName,\n    }: {\n      values?: FieldValues;\n      name?: InternalFieldName;\n    }) => {\n      if (fieldArrayName === _name.current || !fieldArrayName) {\n        const fieldValues = get(values, _name.current);\n        if (Array.isArray(fieldValues)) {\n          setFields(fieldValues);\n          ids.current = fieldValues.map(generateId);\n        }\n      }\n    },\n    subject: control._subjects.array,\n  });\n\n  const updateValues = React.useCallback(\n    <\n      T extends Partial<\n        FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>\n      >[],\n    >(\n      updatedFieldArrayValues: T,\n    ) => {\n      _actioned.current = true;\n      control._updateFieldArray(name, updatedFieldArrayValues);\n    },\n    [control, name],\n  );\n\n  const append = (\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n    options?: FieldArrayMethodProps,\n  ) => {\n    const appendValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = appendAt(\n      control._getFieldArray(name),\n      appendValue,\n    );\n    control._names.focus = getFocusFieldName(\n      name,\n      updatedFieldArrayValues.length - 1,\n      options,\n    );\n    ids.current = appendAt(ids.current, appendValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._updateFieldArray(name, updatedFieldArrayValues, appendAt, {\n      argA: fillEmptyArray(value),\n    });\n  };\n\n  const prepend = (\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n    options?: FieldArrayMethodProps,\n  ) => {\n    const prependValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = prependAt(\n      control._getFieldArray(name),\n      prependValue,\n    );\n    control._names.focus = getFocusFieldName(name, 0, options);\n    ids.current = prependAt(ids.current, prependValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._updateFieldArray(name, updatedFieldArrayValues, prependAt, {\n      argA: fillEmptyArray(value),\n    });\n  };\n\n  const remove = (index?: number | number[]) => {\n    const updatedFieldArrayValues: Partial<\n      FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>\n    >[] = removeArrayAt(control._getFieldArray(name), index);\n    ids.current = removeArrayAt(ids.current, index);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._updateFieldArray(name, updatedFieldArrayValues, removeArrayAt, {\n      argA: index,\n    });\n  };\n\n  const insert = (\n    index: number,\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n    options?: FieldArrayMethodProps,\n  ) => {\n    const insertValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = insertAt(\n      control._getFieldArray(name),\n      index,\n      insertValue,\n    );\n    control._names.focus = getFocusFieldName(name, index, options);\n    ids.current = insertAt(ids.current, index, insertValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._updateFieldArray(name, updatedFieldArrayValues, insertAt, {\n      argA: index,\n      argB: fillEmptyArray(value),\n    });\n  };\n\n  const swap = (indexA: number, indexB: number) => {\n    const updatedFieldArrayValues = control._getFieldArray(name);\n    swapArrayAt(updatedFieldArrayValues, indexA, indexB);\n    swapArrayAt(ids.current, indexA, indexB);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._updateFieldArray(\n      name,\n      updatedFieldArrayValues,\n      swapArrayAt,\n      {\n        argA: indexA,\n        argB: indexB,\n      },\n      false,\n    );\n  };\n\n  const move = (from: number, to: number) => {\n    const updatedFieldArrayValues = control._getFieldArray(name);\n    moveArrayAt(updatedFieldArrayValues, from, to);\n    moveArrayAt(ids.current, from, to);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._updateFieldArray(\n      name,\n      updatedFieldArrayValues,\n      moveArrayAt,\n      {\n        argA: from,\n        argB: to,\n      },\n      false,\n    );\n  };\n\n  const update = (\n    index: number,\n    value: FieldArray<TFieldValues, TFieldArrayName>,\n  ) => {\n    const updateValue = cloneObject(value);\n    const updatedFieldArrayValues = updateAt(\n      control._getFieldArray<\n        FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>\n      >(name),\n      index,\n      updateValue as FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>,\n    );\n    ids.current = [...updatedFieldArrayValues].map((item, i) =>\n      !item || i === index ? generateId() : ids.current[i],\n    );\n    updateValues(updatedFieldArrayValues);\n    setFields([...updatedFieldArrayValues]);\n    control._updateFieldArray(\n      name,\n      updatedFieldArrayValues,\n      updateAt,\n      {\n        argA: index,\n        argB: updateValue,\n      },\n      true,\n      false,\n    );\n  };\n\n  const replace = (\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n  ) => {\n    const updatedFieldArrayValues = convertToArrayPayload(cloneObject(value));\n    ids.current = updatedFieldArrayValues.map(generateId);\n    updateValues([...updatedFieldArrayValues]);\n    setFields([...updatedFieldArrayValues]);\n    control._updateFieldArray(\n      name,\n      [...updatedFieldArrayValues],\n      <T>(data: T): T => data,\n      {},\n      true,\n      false,\n    );\n  };\n\n  React.useEffect(() => {\n    control._state.action = false;\n\n    isWatched(name, control._names) &&\n      control._subjects.state.next({\n        ...control._formState,\n      } as FormState<TFieldValues>);\n\n    if (\n      _actioned.current &&\n      (!getValidationModes(control._options.mode).isOnSubmit ||\n        control._formState.isSubmitted)\n    ) {\n      if (control._options.resolver) {\n        control._executeSchema([name]).then((result) => {\n          const error = get(result.errors, name);\n          const existingError = get(control._formState.errors, name);\n\n          if (\n            existingError\n              ? (!error && existingError.type) ||\n                (error &&\n                  (existingError.type !== error.type ||\n                    existingError.message !== error.message))\n              : error && error.type\n          ) {\n            error\n              ? set(control._formState.errors, name, error)\n              : unset(control._formState.errors, name);\n            control._subjects.state.next({\n              errors: control._formState.errors as FieldErrors<TFieldValues>,\n            });\n          }\n        });\n      } else {\n        const field: Field = get(control._fields, name);\n        if (field && field._f) {\n          validateField(\n            field,\n            control._formValues,\n            control._options.criteriaMode === VALIDATION_MODE.all,\n            control._options.shouldUseNativeValidation,\n            true,\n          ).then(\n            (error) =>\n              !isEmptyObject(error) &&\n              control._subjects.state.next({\n                errors: updateFieldArrayRootError(\n                  control._formState.errors as FieldErrors<TFieldValues>,\n                  error,\n                  name,\n                ) as FieldErrors<TFieldValues>,\n              }),\n          );\n        }\n      }\n    }\n\n    control._subjects.values.next({\n      name,\n      values: { ...control._formValues },\n    });\n\n    control._names.focus &&\n      focusFieldBy(\n        control._fields,\n        (key) => !!key && key.startsWith(control._names.focus || ''),\n      );\n\n    control._names.focus = '';\n\n    control._updateValid();\n  }, [fields, name, control]);\n\n  React.useEffect(() => {\n    !get(control._formValues, name) && control._updateFieldArray(name);\n\n    return () => {\n      (control._options.shouldUnregister || shouldUnregister) &&\n        control.unregister(name as FieldPath<TFieldValues>);\n    };\n  }, [name, control, keyName, shouldUnregister]);\n\n  return {\n    swap: React.useCallback(swap, [updateValues, name, control]),\n    move: React.useCallback(move, [updateValues, name, control]),\n    prepend: React.useCallback(prepend, [updateValues, name, control]),\n    append: React.useCallback(append, [updateValues, name, control]),\n    remove: React.useCallback(remove, [updateValues, name, control]),\n    insert: React.useCallback(insert, [updateValues, name, control]),\n    update: React.useCallback(update, [updateValues, name, control]),\n    replace: React.useCallback(replace, [updateValues, name, control]),\n    fields: React.useMemo(\n      () =>\n        fields.map((field, index) => ({\n          ...field,\n          [keyName]: ids.current[index] || generateId(),\n        })) as FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>[],\n      [fields, keyName],\n    ),\n  };\n}\n", "import { Noop } from '../types';\n\nexport type Observer<T> = {\n  next: (value: T) => void;\n};\n\nexport type Subscription = {\n  unsubscribe: Noop;\n};\n\nexport type Subject<T> = {\n  readonly observers: Observer<T>[];\n  subscribe: (value: Observer<T>) => Subscription;\n  unsubscribe: Noop;\n} & Observer<T>;\n\nexport default function createSubject<T>(): Subject<T> {\n  let _observers: Observer<T>[] = [];\n\n  const next = (value: T) => {\n    for (const observer of _observers) {\n      observer.next && observer.next(value);\n    }\n  };\n\n  const subscribe = (observer: Observer<T>): Subscription => {\n    _observers.push(observer);\n    return {\n      unsubscribe: () => {\n        _observers = _observers.filter((o) => o !== observer);\n      },\n    };\n  };\n\n  const unsubscribe = () => {\n    _observers = [];\n  };\n\n  return {\n    get observers() {\n      return _observers;\n    },\n    next,\n    subscribe,\n    unsubscribe,\n  };\n}\n", "import { Primitive } from '../types';\n\nimport isNullOrUndefined from './isNullOrUndefined';\nimport { isObjectType } from './isObject';\n\nexport default (value: unknown): value is Primitive =>\n  isNullOrUndefined(value) || !isObjectType(value);\n", "import isObject from '../utils/isObject';\n\nimport isDateObject from './isDateObject';\nimport isPrimitive from './isPrimitive';\n\nexport default function deepEqual(object1: any, object2: any) {\n  if (isPrimitive(object1) || isPrimitive(object2)) {\n    return object1 === object2;\n  }\n\n  if (isDateObject(object1) && isDateObject(object2)) {\n    return object1.getTime() === object2.getTime();\n  }\n\n  const keys1 = Object.keys(object1);\n  const keys2 = Object.keys(object2);\n\n  if (keys1.length !== keys2.length) {\n    return false;\n  }\n\n  for (const key of keys1) {\n    const val1 = object1[key];\n\n    if (!keys2.includes(key)) {\n      return false;\n    }\n\n    if (key !== 'ref') {\n      const val2 = object2[key];\n\n      if (\n        (isDateObject(val1) && isDateObject(val2)) ||\n        (isObject(val1) && isObject(val2)) ||\n        (Array.isArray(val1) && Array.isArray(val2))\n          ? !deepEqual(val1, val2)\n          : val1 !== val2\n      ) {\n        return false;\n      }\n    }\n  }\n\n  return true;\n}\n", "import { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLSelectElement =>\n  element.type === `select-multiple`;\n", "import { FieldElement } from '../types';\n\nimport isCheckBoxInput from './isCheckBoxInput';\nimport isRadioInput from './isRadioInput';\n\nexport default (ref: FieldElement): ref is HTMLInputElement =>\n  isRadioInput(ref) || isCheckBoxInput(ref);\n", "import { Ref } from '../types';\n\nimport isHTMLElement from './isHTMLElement';\n\nexport default (ref: Ref) => isHTMLElement(ref) && ref.isConnected;\n", "import isFunction from './isFunction';\n\nexport default <T>(data: T): boolean => {\n  for (const key in data) {\n    if (isFunction(data[key])) {\n      return true;\n    }\n  }\n  return false;\n};\n", "import deepEqual from '../utils/deepEqual';\nimport isNullOrUndefined from '../utils/isNullOrUndefined';\nimport isObject from '../utils/isObject';\nimport isPrimitive from '../utils/isPrimitive';\nimport isUndefined from '../utils/isUndefined';\nimport objectHasFunction from '../utils/objectHasFunction';\n\nfunction markFieldsDirty<U>(data: U, fields: Record<string, any> = {}) {\n  const isParentNodeArray = Array.isArray(data);\n\n  if (isObject(data) || isParentNodeArray) {\n    for (const key in data) {\n      if (\n        Array.isArray(data[key]) ||\n        (isObject(data[key]) && !objectHasFunction(data[key]))\n      ) {\n        fields[key] = Array.isArray(data[key]) ? [] : {};\n        markFieldsDirty(data[key], fields[key]);\n      } else if (!isNullOrUndefined(data[key])) {\n        fields[key] = true;\n      }\n    }\n  }\n\n  return fields;\n}\n\nfunction getDirtyFieldsFromDefaultValues<T>(\n  data: T,\n  formValues: T,\n  dirtyFieldsFromValues: any,\n) {\n  const isParentNodeArray = Array.isArray(data);\n\n  if (isObject(data) || isParentNodeArray) {\n    for (const key in data) {\n      if (\n        Array.isArray(data[key]) ||\n        (isObject(data[key]) && !objectHasFunction(data[key]))\n      ) {\n        if (\n          isUndefined(formValues) ||\n          isPrimitive(dirtyFieldsFromValues[key])\n        ) {\n          dirtyFieldsFromValues[key] = Array.isArray(data[key])\n            ? markFieldsDirty(data[key], [])\n            : { ...markFieldsDirty(data[key]) };\n        } else {\n          getDirtyFieldsFromDefaultValues(\n            data[key],\n            isNullOrUndefined(formValues) ? {} : formValues[key],\n            dirtyFieldsFromValues[key],\n          );\n        }\n      } else {\n        dirtyFieldsFromValues[key] = !deepEqual(data[key], formValues[key]);\n      }\n    }\n  }\n\n  return dirtyFieldsFromValues;\n}\n\nexport default <T>(defaultValues: T, formValues: T) =>\n  getDirtyFieldsFromDefaultValues(\n    defaultValues,\n    formValues,\n    markFieldsDirty(formValues),\n  );\n", "import { Field, NativeFieldValue } from '../types';\nimport isString from '../utils/isString';\nimport isUndefined from '../utils/isUndefined';\n\nexport default <T extends NativeFieldValue>(\n  value: T,\n  { valueAsNumber, valueAsDate, setValueAs }: Field['_f'],\n) =>\n  isUndefined(value)\n    ? value\n    : valueAsNumber\n    ? value === ''\n      ? NaN\n      : value\n      ? +value\n      : value\n    : valueAsDate && isString(value)\n    ? new Date(value)\n    : setValueAs\n    ? setValueAs(value)\n    : value;\n", "import { Field } from '../types';\nimport isCheckBox from '../utils/isCheckBoxInput';\nimport isFileInput from '../utils/isFileInput';\nimport isMultipleSelect from '../utils/isMultipleSelect';\nimport isRadioInput from '../utils/isRadioInput';\nimport isUndefined from '../utils/isUndefined';\n\nimport getCheckboxValue from './getCheckboxValue';\nimport getFieldValueAs from './getFieldValueAs';\nimport getRadioValue from './getRadioValue';\n\nexport default function getFieldValue(_f: Field['_f']) {\n  const ref = _f.ref;\n\n  if (_f.refs ? _f.refs.every((ref) => ref.disabled) : ref.disabled) {\n    return;\n  }\n\n  if (isFileInput(ref)) {\n    return ref.files;\n  }\n\n  if (isRadioInput(ref)) {\n    return getRadioValue(_f.refs).value;\n  }\n\n  if (isMultipleSelect(ref)) {\n    return [...ref.selectedOptions].map(({ value }) => value);\n  }\n\n  if (isCheckBox(ref)) {\n    return getCheckboxValue(_f.refs).value;\n  }\n\n  return getFieldValueAs(isUndefined(ref.value) ? _f.ref.value : ref.value, _f);\n}\n", "import {\n  CriteriaMode,\n  Field,\n  FieldName,\n  FieldRefs,\n  FieldValues,\n  InternalFieldName,\n} from '../types';\nimport { get } from '../utils';\nimport set from '../utils/set';\n\nexport default <TFieldValues extends FieldValues>(\n  fieldsNames: Set<InternalFieldName> | InternalFieldName[],\n  _fields: FieldRefs,\n  criteriaMode?: CriteriaMode,\n  shouldUseNativeValidation?: boolean | undefined,\n) => {\n  const fields: Record<InternalFieldName, Field['_f']> = {};\n\n  for (const name of fieldsNames) {\n    const field: Field = get(_fields, name);\n\n    field && set(fields, name, field._f);\n  }\n\n  return {\n    criteriaMode,\n    names: [...fieldsNames] as FieldName<TFieldValues>[],\n    fields,\n    shouldUseNativeValidation,\n  };\n};\n", "import {\n  ValidationRule,\n  ValidationValue,\n  ValidationValueMessage,\n} from '../types';\nimport isObject from '../utils/isObject';\nimport isRegex from '../utils/isRegex';\nimport isUndefined from '../utils/isUndefined';\n\nexport default <T extends ValidationValue>(\n  rule?: ValidationRule<T> | ValidationValueMessage<T>,\n) =>\n  isUndefined(rule)\n    ? rule\n    : isRegex(rule)\n    ? rule.source\n    : isObject(rule)\n    ? isRegex(rule.value)\n      ? rule.value.source\n      : rule.value\n    : rule;\n", "import { Field } from '../types';\n\nexport default (options: Field['_f']) =>\n  options.mount &&\n  (options.required ||\n    options.min ||\n    options.max ||\n    options.maxLength ||\n    options.minLength ||\n    options.pattern ||\n    options.validate);\n", "import { FieldError, FieldErrors, FieldValues } from '../types';\nimport get from '../utils/get';\nimport isKey from '../utils/isKey';\n\nexport default function schemaErrorLookup<T extends FieldValues = FieldValues>(\n  errors: FieldErrors<T>,\n  _fields: FieldValues,\n  name: string,\n): {\n  error?: FieldError;\n  name: string;\n} {\n  const error = get(errors, name);\n\n  if (error || isKey(name)) {\n    return {\n      error,\n      name,\n    };\n  }\n\n  const names = name.split('.');\n\n  while (names.length) {\n    const fieldName = names.join('.');\n    const field = get(_fields, fieldName);\n    const foundError = get(errors, fieldName);\n\n    if (field && !Array.isArray(field) && name !== fieldName) {\n      return { name };\n    }\n\n    if (foundError && foundError.type) {\n      return {\n        name: fieldName,\n        error: foundError,\n      };\n    }\n\n    names.pop();\n  }\n\n  return {\n    name,\n  };\n}\n", "export default (\n  isBlurEvent: boolean,\n  isTouched: boolean,\n  isSubmitted: boolean,\n  reValidateMode: {\n    isOnBlur: boolean;\n    isOnChange: boolean;\n  },\n  mode: Partial<{\n    isOnSubmit: boolean;\n    isOnBlur: boolean;\n    isOnChange: boolean;\n    isOnTouch: boolean;\n    isOnAll: boolean;\n  }>,\n) => {\n  if (mode.isOnAll) {\n    return false;\n  } else if (!isSubmitted && mode.isOnTouch) {\n    return !(isTouched || isBlurEvent);\n  } else if (isSubmitted ? reValidateMode.isOnBlur : mode.isOnBlur) {\n    return !isBlurEvent;\n  } else if (isSubmitted ? reValidateMode.isOnChange : mode.isOnChange) {\n    return isBlurEvent;\n  }\n  return true;\n};\n", "import compact from '../utils/compact';\nimport get from '../utils/get';\nimport unset from '../utils/unset';\n\nexport default <T>(ref: T, name: string) =>\n  !compact(get(ref, name)).length && unset(ref, name);\n", "import { EVENTS, VALIDATION_MODE } from '../constants';\nimport {\n  BatchFieldArrayUpdate,\n  ChangeHandler,\n  DeepPartial,\n  DelayCallback,\n  EventType,\n  Field,\n  FieldError,\n  FieldNamesMarkedBoolean,\n  FieldPath,\n  FieldRefs,\n  FieldValues,\n  FormState,\n  GetIsDirty,\n  InternalFieldName,\n  Names,\n  Path,\n  PathValue,\n  Ref,\n  SetFieldValue,\n  SetValueConfig,\n  Subjects,\n  UseFormClearErrors,\n  UseFormGetFieldState,\n  UseFormGetValues,\n  UseFormHandleSubmit,\n  UseFormProps,\n  UseFormRegister,\n  UseFormReset,\n  UseFormResetField,\n  UseFormReturn,\n  UseFormSetError,\n  UseFormSetFocus,\n  UseFormSetValue,\n  UseFormTrigger,\n  UseFormUnregister,\n  UseFormWatch,\n  WatchInternal,\n  WatchObserver,\n} from '../types';\nimport cloneObject from '../utils/cloneObject';\nimport compact from '../utils/compact';\nimport convertToArrayPayload from '../utils/convertToArrayPayload';\nimport createSubject from '../utils/createSubject';\nimport deepEqual from '../utils/deepEqual';\nimport get from '../utils/get';\nimport isBoolean from '../utils/isBoolean';\nimport isCheckBoxInput from '../utils/isCheckBoxInput';\nimport isDateObject from '../utils/isDateObject';\nimport isEmptyObject from '../utils/isEmptyObject';\nimport isFileInput from '../utils/isFileInput';\nimport isFunction from '../utils/isFunction';\nimport isHTMLElement from '../utils/isHTMLElement';\nimport isMultipleSelect from '../utils/isMultipleSelect';\nimport isNullOrUndefined from '../utils/isNullOrUndefined';\nimport isObject from '../utils/isObject';\nimport isPrimitive from '../utils/isPrimitive';\nimport isRadioOrCheckbox from '../utils/isRadioOrCheckbox';\nimport isString from '../utils/isString';\nimport isUndefined from '../utils/isUndefined';\nimport isWeb from '../utils/isWeb';\nimport live from '../utils/live';\nimport set from '../utils/set';\nimport unset from '../utils/unset';\n\nimport focusFieldBy from './focusFieldBy';\nimport generateWatchOutput from './generateWatchOutput';\nimport getDirtyFields from './getDirtyFields';\nimport getEventValue from './getEventValue';\nimport getFieldValue from './getFieldValue';\nimport getFieldValueAs from './getFieldValueAs';\nimport getResolverOptions from './getResolverOptions';\nimport getRuleValue from './getRuleValue';\nimport getValidationModes from './getValidationModes';\nimport hasValidation from './hasValidation';\nimport isNameInFieldArray from './isNameInFieldArray';\nimport isWatched from './isWatched';\nimport schemaErrorLookup from './schemaErrorLookup';\nimport skipValidation from './skipValidation';\nimport unsetEmptyArray from './unsetEmptyArray';\nimport updateFieldArrayRootError from './updateFieldArrayRootError';\nimport validateField from './validateField';\n\nconst defaultOptions = {\n  mode: VALIDATION_MODE.onSubmit,\n  reValidateMode: VALIDATION_MODE.onChange,\n  shouldFocusError: true,\n} as const;\n\nexport function createFormControl<\n  TFieldValues extends FieldValues = FieldValues,\n  TContext = any,\n>(\n  props: UseFormProps<TFieldValues, TContext> = {},\n  flushRootRender: () => void,\n): Omit<UseFormReturn<TFieldValues, TContext>, 'formState'> {\n  let _options = {\n    ...defaultOptions,\n    ...props,\n  };\n  let _formState: FormState<TFieldValues> = {\n    submitCount: 0,\n    isDirty: false,\n    isLoading: isFunction(_options.defaultValues),\n    isValidating: false,\n    isSubmitted: false,\n    isSubmitting: false,\n    isSubmitSuccessful: false,\n    isValid: false,\n    touchedFields: {},\n    dirtyFields: {},\n    errors: {},\n  };\n  let _fields = {};\n  let _defaultValues =\n    isObject(_options.defaultValues) || isObject(_options.values)\n      ? cloneObject(_options.defaultValues || _options.values) || {}\n      : {};\n  let _formValues = _options.shouldUnregister\n    ? {}\n    : cloneObject(_defaultValues);\n  let _state = {\n    action: false,\n    mount: false,\n    watch: false,\n  };\n  let _names: Names = {\n    mount: new Set(),\n    unMount: new Set(),\n    array: new Set(),\n    watch: new Set(),\n  };\n  let delayErrorCallback: DelayCallback | null;\n  let timer = 0;\n  const _proxyFormState = {\n    isDirty: false,\n    dirtyFields: false,\n    touchedFields: false,\n    isValidating: false,\n    isValid: false,\n    errors: false,\n  };\n  const _subjects: Subjects<TFieldValues> = {\n    values: createSubject(),\n    array: createSubject(),\n    state: createSubject(),\n  };\n  const shouldCaptureDirtyFields =\n    props.resetOptions && props.resetOptions.keepDirtyValues;\n  const validationModeBeforeSubmit = getValidationModes(_options.mode);\n  const validationModeAfterSubmit = getValidationModes(_options.reValidateMode);\n  const shouldDisplayAllAssociatedErrors =\n    _options.criteriaMode === VALIDATION_MODE.all;\n\n  const debounce =\n    <T extends Function>(callback: T) =>\n    (wait: number) => {\n      clearTimeout(timer);\n      timer = setTimeout(callback, wait);\n    };\n\n  const _updateValid = async (shouldUpdateValid?: boolean) => {\n    if (_proxyFormState.isValid || shouldUpdateValid) {\n      const isValid = _options.resolver\n        ? isEmptyObject((await _executeSchema()).errors)\n        : await executeBuiltInValidation(_fields, true);\n\n      if (isValid !== _formState.isValid) {\n        _subjects.state.next({\n          isValid,\n        });\n      }\n    }\n  };\n\n  const _updateIsValidating = (value: boolean) =>\n    _proxyFormState.isValidating &&\n    _subjects.state.next({\n      isValidating: value,\n    });\n\n  const _updateFieldArray: BatchFieldArrayUpdate = (\n    name,\n    values = [],\n    method,\n    args,\n    shouldSetValues = true,\n    shouldUpdateFieldsAndState = true,\n  ) => {\n    if (args && method) {\n      _state.action = true;\n      if (shouldUpdateFieldsAndState && Array.isArray(get(_fields, name))) {\n        const fieldValues = method(get(_fields, name), args.argA, args.argB);\n        shouldSetValues && set(_fields, name, fieldValues);\n      }\n\n      if (\n        shouldUpdateFieldsAndState &&\n        Array.isArray(get(_formState.errors, name))\n      ) {\n        const errors = method(\n          get(_formState.errors, name),\n          args.argA,\n          args.argB,\n        );\n        shouldSetValues && set(_formState.errors, name, errors);\n        unsetEmptyArray(_formState.errors, name);\n      }\n\n      if (\n        _proxyFormState.touchedFields &&\n        shouldUpdateFieldsAndState &&\n        Array.isArray(get(_formState.touchedFields, name))\n      ) {\n        const touchedFields = method(\n          get(_formState.touchedFields, name),\n          args.argA,\n          args.argB,\n        );\n        shouldSetValues && set(_formState.touchedFields, name, touchedFields);\n      }\n\n      if (_proxyFormState.dirtyFields) {\n        _formState.dirtyFields = getDirtyFields(_defaultValues, _formValues);\n      }\n\n      _subjects.state.next({\n        name,\n        isDirty: _getDirty(name, values),\n        dirtyFields: _formState.dirtyFields,\n        errors: _formState.errors,\n        isValid: _formState.isValid,\n      });\n    } else {\n      set(_formValues, name, values);\n    }\n  };\n\n  const updateErrors = (name: InternalFieldName, error: FieldError) => {\n    set(_formState.errors, name, error);\n    _subjects.state.next({\n      errors: _formState.errors,\n    });\n  };\n\n  const updateValidAndValue = (\n    name: InternalFieldName,\n    shouldSkipSetValueAs: boolean,\n    value?: unknown,\n    ref?: Ref,\n  ) => {\n    const field: Field = get(_fields, name);\n\n    if (field) {\n      const defaultValue = get(\n        _formValues,\n        name,\n        isUndefined(value) ? get(_defaultValues, name) : value,\n      );\n\n      isUndefined(defaultValue) ||\n      (ref && (ref as HTMLInputElement).defaultChecked) ||\n      shouldSkipSetValueAs\n        ? set(\n            _formValues,\n            name,\n            shouldSkipSetValueAs ? defaultValue : getFieldValue(field._f),\n          )\n        : setFieldValue(name, defaultValue);\n\n      _state.mount && _updateValid();\n    }\n  };\n\n  const updateTouchAndDirty = (\n    name: InternalFieldName,\n    fieldValue: unknown,\n    isBlurEvent?: boolean,\n    shouldDirty?: boolean,\n    shouldRender?: boolean,\n  ): Partial<\n    Pick<FormState<TFieldValues>, 'dirtyFields' | 'isDirty' | 'touchedFields'>\n  > => {\n    let shouldUpdateField = false;\n    let isPreviousDirty = false;\n    const output: Partial<FormState<TFieldValues>> & { name: string } = {\n      name,\n    };\n\n    if (!isBlurEvent || shouldDirty) {\n      if (_proxyFormState.isDirty) {\n        isPreviousDirty = _formState.isDirty;\n        _formState.isDirty = output.isDirty = _getDirty();\n        shouldUpdateField = isPreviousDirty !== output.isDirty;\n      }\n\n      const isCurrentFieldPristine = deepEqual(\n        get(_defaultValues, name),\n        fieldValue,\n      );\n\n      isPreviousDirty = get(_formState.dirtyFields, name);\n      isCurrentFieldPristine\n        ? unset(_formState.dirtyFields, name)\n        : set(_formState.dirtyFields, name, true);\n      output.dirtyFields = _formState.dirtyFields;\n      shouldUpdateField =\n        shouldUpdateField ||\n        (_proxyFormState.dirtyFields &&\n          isPreviousDirty !== !isCurrentFieldPristine);\n    }\n\n    if (isBlurEvent) {\n      const isPreviousFieldTouched = get(_formState.touchedFields, name);\n\n      if (!isPreviousFieldTouched) {\n        set(_formState.touchedFields, name, isBlurEvent);\n        output.touchedFields = _formState.touchedFields;\n        shouldUpdateField =\n          shouldUpdateField ||\n          (_proxyFormState.touchedFields &&\n            isPreviousFieldTouched !== isBlurEvent);\n      }\n    }\n\n    shouldUpdateField && shouldRender && _subjects.state.next(output);\n\n    return shouldUpdateField ? output : {};\n  };\n\n  const shouldRenderByError = (\n    name: InternalFieldName,\n    isValid?: boolean,\n    error?: FieldError,\n    fieldState?: {\n      dirty?: FieldNamesMarkedBoolean<TFieldValues>;\n      isDirty?: boolean;\n      touched?: FieldNamesMarkedBoolean<TFieldValues>;\n    },\n  ) => {\n    const previousFieldError = get(_formState.errors, name);\n    const shouldUpdateValid =\n      _proxyFormState.isValid &&\n      isBoolean(isValid) &&\n      _formState.isValid !== isValid;\n\n    if (props.delayError && error) {\n      delayErrorCallback = debounce(() => updateErrors(name, error));\n      delayErrorCallback(props.delayError);\n    } else {\n      clearTimeout(timer);\n      delayErrorCallback = null;\n      error\n        ? set(_formState.errors, name, error)\n        : unset(_formState.errors, name);\n    }\n\n    if (\n      (error ? !deepEqual(previousFieldError, error) : previousFieldError) ||\n      !isEmptyObject(fieldState) ||\n      shouldUpdateValid\n    ) {\n      const updatedFormState = {\n        ...fieldState,\n        ...(shouldUpdateValid && isBoolean(isValid) ? { isValid } : {}),\n        errors: _formState.errors,\n        name,\n      };\n\n      _formState = {\n        ..._formState,\n        ...updatedFormState,\n      };\n\n      _subjects.state.next(updatedFormState);\n    }\n\n    _updateIsValidating(false);\n  };\n\n  const _executeSchema = async (name?: InternalFieldName[]) =>\n    _options.resolver!(\n      _formValues as TFieldValues,\n      _options.context,\n      getResolverOptions(\n        name || _names.mount,\n        _fields,\n        _options.criteriaMode,\n        _options.shouldUseNativeValidation,\n      ),\n    );\n\n  const executeSchemaAndUpdateState = async (names?: InternalFieldName[]) => {\n    const { errors } = await _executeSchema();\n\n    if (names) {\n      for (const name of names) {\n        const error = get(errors, name);\n        error\n          ? set(_formState.errors, name, error)\n          : unset(_formState.errors, name);\n      }\n    } else {\n      _formState.errors = errors;\n    }\n\n    return errors;\n  };\n\n  const executeBuiltInValidation = async (\n    fields: FieldRefs,\n    shouldOnlyCheckValid?: boolean,\n    context: {\n      valid: boolean;\n    } = {\n      valid: true,\n    },\n  ) => {\n    for (const name in fields) {\n      const field = fields[name];\n\n      if (field) {\n        const { _f, ...fieldValue } = field;\n\n        if (_f) {\n          const isFieldArrayRoot = _names.array.has(_f.name);\n          const fieldError = await validateField(\n            field,\n            _formValues,\n            shouldDisplayAllAssociatedErrors,\n            _options.shouldUseNativeValidation && !shouldOnlyCheckValid,\n            isFieldArrayRoot,\n          );\n\n          if (fieldError[_f.name]) {\n            context.valid = false;\n            if (shouldOnlyCheckValid) {\n              break;\n            }\n          }\n\n          !shouldOnlyCheckValid &&\n            (get(fieldError, _f.name)\n              ? isFieldArrayRoot\n                ? updateFieldArrayRootError(\n                    _formState.errors,\n                    fieldError,\n                    _f.name,\n                  )\n                : set(_formState.errors, _f.name, fieldError[_f.name])\n              : unset(_formState.errors, _f.name));\n        }\n\n        fieldValue &&\n          (await executeBuiltInValidation(\n            fieldValue,\n            shouldOnlyCheckValid,\n            context,\n          ));\n      }\n    }\n\n    return context.valid;\n  };\n\n  const _removeUnmounted = () => {\n    for (const name of _names.unMount) {\n      const field: Field = get(_fields, name);\n\n      field &&\n        (field._f.refs\n          ? field._f.refs.every((ref) => !live(ref))\n          : !live(field._f.ref)) &&\n        unregister(name as FieldPath<TFieldValues>);\n    }\n\n    _names.unMount = new Set();\n  };\n\n  const _getDirty: GetIsDirty = (name, data) => (\n    name && data && set(_formValues, name, data),\n    !deepEqual(getValues(), _defaultValues)\n  );\n\n  const _getWatch: WatchInternal<TFieldValues> = (\n    names,\n    defaultValue,\n    isGlobal,\n  ) =>\n    generateWatchOutput(\n      names,\n      _names,\n      {\n        ...(_state.mount\n          ? _formValues\n          : isUndefined(defaultValue)\n          ? _defaultValues\n          : isString(names)\n          ? { [names]: defaultValue }\n          : defaultValue),\n      },\n      isGlobal,\n      defaultValue,\n    );\n\n  const _getFieldArray = <TFieldArrayValues>(\n    name: InternalFieldName,\n  ): Partial<TFieldArrayValues>[] =>\n    compact(\n      get(\n        _state.mount ? _formValues : _defaultValues,\n        name,\n        props.shouldUnregister ? get(_defaultValues, name, []) : [],\n      ),\n    );\n\n  const setFieldValue = (\n    name: InternalFieldName,\n    value: SetFieldValue<TFieldValues>,\n    options: SetValueConfig = {},\n  ) => {\n    const field: Field = get(_fields, name);\n    let fieldValue: unknown = value;\n\n    if (field) {\n      const fieldReference = field._f;\n\n      if (fieldReference) {\n        !fieldReference.disabled &&\n          set(_formValues, name, getFieldValueAs(value, fieldReference));\n\n        fieldValue =\n          isHTMLElement(fieldReference.ref) && isNullOrUndefined(value)\n            ? ''\n            : value;\n\n        if (isMultipleSelect(fieldReference.ref)) {\n          [...fieldReference.ref.options].forEach(\n            (optionRef) =>\n              (optionRef.selected = (\n                fieldValue as InternalFieldName[]\n              ).includes(optionRef.value)),\n          );\n        } else if (fieldReference.refs) {\n          if (isCheckBoxInput(fieldReference.ref)) {\n            fieldReference.refs.length > 1\n              ? fieldReference.refs.forEach(\n                  (checkboxRef) =>\n                    (!checkboxRef.defaultChecked || !checkboxRef.disabled) &&\n                    (checkboxRef.checked = Array.isArray(fieldValue)\n                      ? !!(fieldValue as []).find(\n                          (data: string) => data === checkboxRef.value,\n                        )\n                      : fieldValue === checkboxRef.value),\n                )\n              : fieldReference.refs[0] &&\n                (fieldReference.refs[0].checked = !!fieldValue);\n          } else {\n            fieldReference.refs.forEach(\n              (radioRef: HTMLInputElement) =>\n                (radioRef.checked = radioRef.value === fieldValue),\n            );\n          }\n        } else if (isFileInput(fieldReference.ref)) {\n          fieldReference.ref.value = '';\n        } else {\n          fieldReference.ref.value = fieldValue;\n\n          if (!fieldReference.ref.type) {\n            _subjects.values.next({\n              name,\n              values: { ..._formValues },\n            });\n          }\n        }\n      }\n    }\n\n    (options.shouldDirty || options.shouldTouch) &&\n      updateTouchAndDirty(\n        name,\n        fieldValue,\n        options.shouldTouch,\n        options.shouldDirty,\n        true,\n      );\n\n    options.shouldValidate && trigger(name as Path<TFieldValues>);\n  };\n\n  const setValues = <\n    T extends InternalFieldName,\n    K extends SetFieldValue<TFieldValues>,\n    U extends SetValueConfig,\n  >(\n    name: T,\n    value: K,\n    options: U,\n  ) => {\n    for (const fieldKey in value) {\n      const fieldValue = value[fieldKey];\n      const fieldName = `${name}.${fieldKey}`;\n      const field = get(_fields, fieldName);\n\n      (_names.array.has(name) ||\n        !isPrimitive(fieldValue) ||\n        (field && !field._f)) &&\n      !isDateObject(fieldValue)\n        ? setValues(fieldName, fieldValue, options)\n        : setFieldValue(fieldName, fieldValue, options);\n    }\n  };\n\n  const setValue: UseFormSetValue<TFieldValues> = (\n    name,\n    value,\n    options = {},\n  ) => {\n    const field = get(_fields, name);\n    const isFieldArray = _names.array.has(name);\n    const cloneValue = cloneObject(value);\n\n    set(_formValues, name, cloneValue);\n\n    if (isFieldArray) {\n      _subjects.array.next({\n        name,\n        values: { ..._formValues },\n      });\n\n      if (\n        (_proxyFormState.isDirty || _proxyFormState.dirtyFields) &&\n        options.shouldDirty\n      ) {\n        _subjects.state.next({\n          name,\n          dirtyFields: getDirtyFields(_defaultValues, _formValues),\n          isDirty: _getDirty(name, cloneValue),\n        });\n      }\n    } else {\n      field && !field._f && !isNullOrUndefined(cloneValue)\n        ? setValues(name, cloneValue, options)\n        : setFieldValue(name, cloneValue, options);\n    }\n\n    isWatched(name, _names) && _subjects.state.next({ ..._formState });\n    _subjects.values.next({\n      name,\n      values: { ..._formValues },\n    });\n    !_state.mount && flushRootRender();\n  };\n\n  const onChange: ChangeHandler = async (event) => {\n    const target = event.target;\n    let name = target.name;\n    let isFieldValueUpdated = true;\n    const field: Field = get(_fields, name);\n    const getCurrentFieldValue = () =>\n      target.type ? getFieldValue(field._f) : getEventValue(event);\n\n    if (field) {\n      let error;\n      let isValid;\n      const fieldValue = getCurrentFieldValue();\n      const isBlurEvent =\n        event.type === EVENTS.BLUR || event.type === EVENTS.FOCUS_OUT;\n      const shouldSkipValidation =\n        (!hasValidation(field._f) &&\n          !_options.resolver &&\n          !get(_formState.errors, name) &&\n          !field._f.deps) ||\n        skipValidation(\n          isBlurEvent,\n          get(_formState.touchedFields, name),\n          _formState.isSubmitted,\n          validationModeAfterSubmit,\n          validationModeBeforeSubmit,\n        );\n      const watched = isWatched(name, _names, isBlurEvent);\n\n      set(_formValues, name, fieldValue);\n\n      if (isBlurEvent) {\n        field._f.onBlur && field._f.onBlur(event);\n        delayErrorCallback && delayErrorCallback(0);\n      } else if (field._f.onChange) {\n        field._f.onChange(event);\n      }\n\n      const fieldState = updateTouchAndDirty(\n        name,\n        fieldValue,\n        isBlurEvent,\n        false,\n      );\n\n      const shouldRender = !isEmptyObject(fieldState) || watched;\n\n      !isBlurEvent &&\n        _subjects.values.next({\n          name,\n          type: event.type,\n          values: { ..._formValues },\n        });\n\n      if (shouldSkipValidation) {\n        _proxyFormState.isValid && _updateValid();\n\n        return (\n          shouldRender &&\n          _subjects.state.next({ name, ...(watched ? {} : fieldState) })\n        );\n      }\n\n      !isBlurEvent && watched && _subjects.state.next({ ..._formState });\n\n      _updateIsValidating(true);\n\n      if (_options.resolver) {\n        const { errors } = await _executeSchema([name]);\n        const previousErrorLookupResult = schemaErrorLookup(\n          _formState.errors,\n          _fields,\n          name,\n        );\n        const errorLookupResult = schemaErrorLookup(\n          errors,\n          _fields,\n          previousErrorLookupResult.name || name,\n        );\n\n        error = errorLookupResult.error;\n        name = errorLookupResult.name;\n\n        isValid = isEmptyObject(errors);\n      } else {\n        error = (\n          await validateField(\n            field,\n            _formValues,\n            shouldDisplayAllAssociatedErrors,\n            _options.shouldUseNativeValidation,\n          )\n        )[name];\n\n        isFieldValueUpdated =\n          isNaN(fieldValue) ||\n          fieldValue === get(_formValues, name, fieldValue);\n\n        if (isFieldValueUpdated) {\n          if (error) {\n            isValid = false;\n          } else if (_proxyFormState.isValid) {\n            isValid = await executeBuiltInValidation(_fields, true);\n          }\n        }\n      }\n\n      if (isFieldValueUpdated) {\n        field._f.deps &&\n          trigger(\n            field._f.deps as\n              | FieldPath<TFieldValues>\n              | FieldPath<TFieldValues>[],\n          );\n        shouldRenderByError(name, isValid, error, fieldState);\n      }\n    }\n  };\n\n  const trigger: UseFormTrigger<TFieldValues> = async (name, options = {}) => {\n    let isValid;\n    let validationResult;\n    const fieldNames = convertToArrayPayload(name) as InternalFieldName[];\n\n    _updateIsValidating(true);\n\n    if (_options.resolver) {\n      const errors = await executeSchemaAndUpdateState(\n        isUndefined(name) ? name : fieldNames,\n      );\n\n      isValid = isEmptyObject(errors);\n      validationResult = name\n        ? !fieldNames.some((name) => get(errors, name))\n        : isValid;\n    } else if (name) {\n      validationResult = (\n        await Promise.all(\n          fieldNames.map(async (fieldName) => {\n            const field = get(_fields, fieldName);\n            return await executeBuiltInValidation(\n              field && field._f ? { [fieldName]: field } : field,\n            );\n          }),\n        )\n      ).every(Boolean);\n      !(!validationResult && !_formState.isValid) && _updateValid();\n    } else {\n      validationResult = isValid = await executeBuiltInValidation(_fields);\n    }\n\n    _subjects.state.next({\n      ...(!isString(name) ||\n      (_proxyFormState.isValid && isValid !== _formState.isValid)\n        ? {}\n        : { name }),\n      ...(_options.resolver || !name ? { isValid } : {}),\n      errors: _formState.errors,\n      isValidating: false,\n    });\n\n    options.shouldFocus &&\n      !validationResult &&\n      focusFieldBy(\n        _fields,\n        (key) => key && get(_formState.errors, key),\n        name ? fieldNames : _names.mount,\n      );\n\n    return validationResult;\n  };\n\n  const getValues: UseFormGetValues<TFieldValues> = (\n    fieldNames?:\n      | FieldPath<TFieldValues>\n      | ReadonlyArray<FieldPath<TFieldValues>>,\n  ) => {\n    const values = {\n      ..._defaultValues,\n      ...(_state.mount ? _formValues : {}),\n    };\n\n    return isUndefined(fieldNames)\n      ? values\n      : isString(fieldNames)\n      ? get(values, fieldNames)\n      : fieldNames.map((name) => get(values, name));\n  };\n\n  const getFieldState: UseFormGetFieldState<TFieldValues> = (\n    name,\n    formState,\n  ) => ({\n    invalid: !!get((formState || _formState).errors, name),\n    isDirty: !!get((formState || _formState).dirtyFields, name),\n    isTouched: !!get((formState || _formState).touchedFields, name),\n    error: get((formState || _formState).errors, name),\n  });\n\n  const clearErrors: UseFormClearErrors<TFieldValues> = (name) => {\n    name &&\n      convertToArrayPayload(name).forEach((inputName) =>\n        unset(_formState.errors, inputName),\n      );\n\n    _subjects.state.next({\n      errors: name ? _formState.errors : {},\n    });\n  };\n\n  const setError: UseFormSetError<TFieldValues> = (name, error, options) => {\n    const ref = (get(_fields, name, { _f: {} })._f || {}).ref;\n\n    set(_formState.errors, name, {\n      ...error,\n      ref,\n    });\n\n    _subjects.state.next({\n      name,\n      errors: _formState.errors,\n      isValid: false,\n    });\n\n    options && options.shouldFocus && ref && ref.focus && ref.focus();\n  };\n\n  const watch: UseFormWatch<TFieldValues> = (\n    name?:\n      | FieldPath<TFieldValues>\n      | ReadonlyArray<FieldPath<TFieldValues>>\n      | WatchObserver<TFieldValues>,\n    defaultValue?: DeepPartial<TFieldValues>,\n  ) =>\n    isFunction(name)\n      ? _subjects.values.subscribe({\n          next: (payload) =>\n            name(\n              _getWatch(undefined, defaultValue),\n              payload as {\n                name?: FieldPath<TFieldValues>;\n                type?: EventType;\n                value?: unknown;\n              },\n            ),\n        })\n      : _getWatch(\n          name as InternalFieldName | InternalFieldName[],\n          defaultValue,\n          true,\n        );\n\n  const unregister: UseFormUnregister<TFieldValues> = (name, options = {}) => {\n    for (const fieldName of name ? convertToArrayPayload(name) : _names.mount) {\n      _names.mount.delete(fieldName);\n      _names.array.delete(fieldName);\n\n      if (!options.keepValue) {\n        unset(_fields, fieldName);\n        unset(_formValues, fieldName);\n      }\n\n      !options.keepError && unset(_formState.errors, fieldName);\n      !options.keepDirty && unset(_formState.dirtyFields, fieldName);\n      !options.keepTouched && unset(_formState.touchedFields, fieldName);\n      !_options.shouldUnregister &&\n        !options.keepDefaultValue &&\n        unset(_defaultValues, fieldName);\n    }\n\n    _subjects.values.next({\n      values: { ..._formValues },\n    });\n\n    _subjects.state.next({\n      ..._formState,\n      ...(!options.keepDirty ? {} : { isDirty: _getDirty() }),\n    });\n\n    !options.keepIsValid && _updateValid();\n  };\n\n  const register: UseFormRegister<TFieldValues> = (name, options = {}) => {\n    let field = get(_fields, name);\n    const disabledIsDefined = isBoolean(options.disabled);\n\n    set(_fields, name, {\n      ...(field || {}),\n      _f: {\n        ...(field && field._f ? field._f : { ref: { name } }),\n        name,\n        mount: true,\n        ...options,\n      },\n    });\n    _names.mount.add(name);\n\n    field\n      ? disabledIsDefined &&\n        set(\n          _formValues,\n          name,\n          options.disabled\n            ? undefined\n            : get(_formValues, name, getFieldValue(field._f)),\n        )\n      : updateValidAndValue(name, true, options.value);\n\n    return {\n      ...(disabledIsDefined ? { disabled: options.disabled } : {}),\n      ...(_options.progressive\n        ? {\n            required: !!options.required,\n            min: getRuleValue(options.min),\n            max: getRuleValue(options.max),\n            minLength: getRuleValue<number>(options.minLength) as number,\n            maxLength: getRuleValue(options.maxLength) as number,\n            pattern: getRuleValue(options.pattern) as string,\n          }\n        : {}),\n      name,\n      onChange,\n      onBlur: onChange,\n      ref: (ref: HTMLInputElement | null): void => {\n        if (ref) {\n          register(name, options);\n          field = get(_fields, name);\n\n          const fieldRef = isUndefined(ref.value)\n            ? ref.querySelectorAll\n              ? (ref.querySelectorAll('input,select,textarea')[0] as Ref) || ref\n              : ref\n            : ref;\n          const radioOrCheckbox = isRadioOrCheckbox(fieldRef);\n          const refs = field._f.refs || [];\n\n          if (\n            radioOrCheckbox\n              ? refs.find((option: Ref) => option === fieldRef)\n              : fieldRef === field._f.ref\n          ) {\n            return;\n          }\n\n          set(_fields, name, {\n            _f: {\n              ...field._f,\n              ...(radioOrCheckbox\n                ? {\n                    refs: [\n                      ...refs.filter(live),\n                      fieldRef,\n                      ...(Array.isArray(get(_defaultValues, name)) ? [{}] : []),\n                    ],\n                    ref: { type: fieldRef.type, name },\n                  }\n                : { ref: fieldRef }),\n            },\n          });\n\n          updateValidAndValue(name, false, undefined, fieldRef);\n        } else {\n          field = get(_fields, name, {});\n\n          if (field._f) {\n            field._f.mount = false;\n          }\n\n          (_options.shouldUnregister || options.shouldUnregister) &&\n            !(isNameInFieldArray(_names.array, name) && _state.action) &&\n            _names.unMount.add(name);\n        }\n      },\n    };\n  };\n\n  const _focusError = () =>\n    _options.shouldFocusError &&\n    focusFieldBy(\n      _fields,\n      (key) => key && get(_formState.errors, key),\n      _names.mount,\n    );\n\n  const handleSubmit: UseFormHandleSubmit<TFieldValues> =\n    (onValid, onInvalid) => async (e) => {\n      if (e) {\n        e.preventDefault && e.preventDefault();\n        e.persist && e.persist();\n      }\n      let fieldValues = cloneObject(_formValues);\n\n      _subjects.state.next({\n        isSubmitting: true,\n      });\n\n      if (_options.resolver) {\n        const { errors, values } = await _executeSchema();\n        _formState.errors = errors;\n        fieldValues = values;\n      } else {\n        await executeBuiltInValidation(_fields);\n      }\n\n      unset(_formState.errors, 'root');\n\n      if (isEmptyObject(_formState.errors)) {\n        _subjects.state.next({\n          errors: {},\n        });\n        await onValid(fieldValues as TFieldValues, e);\n      } else {\n        if (onInvalid) {\n          await onInvalid({ ..._formState.errors }, e);\n        }\n        _focusError();\n        setTimeout(_focusError);\n      }\n\n      _subjects.state.next({\n        isSubmitted: true,\n        isSubmitting: false,\n        isSubmitSuccessful: isEmptyObject(_formState.errors),\n        submitCount: _formState.submitCount + 1,\n        errors: _formState.errors,\n      });\n    };\n\n  const resetField: UseFormResetField<TFieldValues> = (name, options = {}) => {\n    if (get(_fields, name)) {\n      if (isUndefined(options.defaultValue)) {\n        setValue(name, get(_defaultValues, name));\n      } else {\n        setValue(\n          name,\n          options.defaultValue as PathValue<\n            TFieldValues,\n            FieldPath<TFieldValues>\n          >,\n        );\n        set(_defaultValues, name, options.defaultValue);\n      }\n\n      if (!options.keepTouched) {\n        unset(_formState.touchedFields, name);\n      }\n\n      if (!options.keepDirty) {\n        unset(_formState.dirtyFields, name);\n        _formState.isDirty = options.defaultValue\n          ? _getDirty(name, get(_defaultValues, name))\n          : _getDirty();\n      }\n\n      if (!options.keepError) {\n        unset(_formState.errors, name);\n        _proxyFormState.isValid && _updateValid();\n      }\n\n      _subjects.state.next({ ..._formState });\n    }\n  };\n\n  const _reset: UseFormReset<TFieldValues> = (\n    formValues,\n    keepStateOptions = {},\n  ) => {\n    const updatedValues = formValues || _defaultValues;\n    const cloneUpdatedValues = cloneObject(updatedValues);\n    const values =\n      formValues && !isEmptyObject(formValues)\n        ? cloneUpdatedValues\n        : _defaultValues;\n\n    if (!keepStateOptions.keepDefaultValues) {\n      _defaultValues = updatedValues;\n    }\n\n    if (!keepStateOptions.keepValues) {\n      if (keepStateOptions.keepDirtyValues || shouldCaptureDirtyFields) {\n        for (const fieldName of _names.mount) {\n          get(_formState.dirtyFields, fieldName)\n            ? set(values, fieldName, get(_formValues, fieldName))\n            : setValue(\n                fieldName as FieldPath<TFieldValues>,\n                get(values, fieldName),\n              );\n        }\n      } else {\n        if (isWeb && isUndefined(formValues)) {\n          for (const name of _names.mount) {\n            const field = get(_fields, name);\n            if (field && field._f) {\n              const fieldReference = Array.isArray(field._f.refs)\n                ? field._f.refs[0]\n                : field._f.ref;\n\n              if (isHTMLElement(fieldReference)) {\n                const form = fieldReference.closest('form');\n                if (form) {\n                  form.reset();\n                  break;\n                }\n              }\n            }\n          }\n        }\n\n        _fields = {};\n      }\n\n      _formValues = props.shouldUnregister\n        ? keepStateOptions.keepDefaultValues\n          ? cloneObject(_defaultValues)\n          : {}\n        : cloneObject(values);\n\n      _subjects.array.next({\n        values: { ...values },\n      });\n\n      _subjects.values.next({\n        values: { ...values },\n      });\n    }\n\n    _names = {\n      mount: new Set(),\n      unMount: new Set(),\n      array: new Set(),\n      watch: new Set(),\n      watchAll: false,\n      focus: '',\n    };\n\n    !_state.mount && flushRootRender();\n\n    _state.mount = !_proxyFormState.isValid || !!keepStateOptions.keepIsValid;\n\n    _state.watch = !!props.shouldUnregister;\n\n    _subjects.state.next({\n      submitCount: keepStateOptions.keepSubmitCount\n        ? _formState.submitCount\n        : 0,\n      isDirty: keepStateOptions.keepDirty\n        ? _formState.isDirty\n        : !!(\n            keepStateOptions.keepDefaultValues &&\n            !deepEqual(formValues, _defaultValues)\n          ),\n      isSubmitted: keepStateOptions.keepIsSubmitted\n        ? _formState.isSubmitted\n        : false,\n      dirtyFields: keepStateOptions.keepDirtyValues\n        ? _formState.dirtyFields\n        : keepStateOptions.keepDefaultValues && formValues\n        ? getDirtyFields(_defaultValues, formValues)\n        : {},\n      touchedFields: keepStateOptions.keepTouched\n        ? _formState.touchedFields\n        : {},\n      errors: keepStateOptions.keepErrors ? _formState.errors : {},\n      isSubmitting: false,\n      isSubmitSuccessful: false,\n    });\n  };\n\n  const reset: UseFormReset<TFieldValues> = (formValues, keepStateOptions) =>\n    _reset(\n      isFunction(formValues)\n        ? formValues(_formValues as TFieldValues)\n        : formValues,\n      keepStateOptions,\n    );\n\n  const setFocus: UseFormSetFocus<TFieldValues> = (name, options = {}) => {\n    const field = get(_fields, name);\n    const fieldReference = field && field._f;\n\n    if (fieldReference) {\n      const fieldRef = fieldReference.refs\n        ? fieldReference.refs[0]\n        : fieldReference.ref;\n\n      if (fieldRef.focus) {\n        fieldRef.focus();\n        options.shouldSelect && fieldRef.select();\n      }\n    }\n  };\n\n  const _updateFormState = (\n    updatedFormState: Partial<FormState<TFieldValues>>,\n  ) => {\n    _formState = {\n      ..._formState,\n      ...updatedFormState,\n    };\n  };\n\n  const _resetDefaultValues = () =>\n    isFunction(_options.defaultValues) &&\n    _options.defaultValues().then((values) => {\n      reset(values, _options.resetOptions);\n      _subjects.state.next({\n        isLoading: false,\n      });\n    });\n\n  return {\n    control: {\n      register,\n      unregister,\n      getFieldState,\n      handleSubmit,\n      setError,\n      _executeSchema,\n      _getWatch,\n      _getDirty,\n      _updateValid,\n      _removeUnmounted,\n      _updateFieldArray,\n      _getFieldArray,\n      _reset,\n      _resetDefaultValues,\n      _updateFormState,\n      _subjects,\n      _proxyFormState,\n      get _fields() {\n        return _fields;\n      },\n      get _formValues() {\n        return _formValues;\n      },\n      get _state() {\n        return _state;\n      },\n      set _state(value) {\n        _state = value;\n      },\n      get _defaultValues() {\n        return _defaultValues;\n      },\n      get _names() {\n        return _names;\n      },\n      set _names(value) {\n        _names = value;\n      },\n      get _formState() {\n        return _formState;\n      },\n      set _formState(value) {\n        _formState = value;\n      },\n      get _options() {\n        return _options;\n      },\n      set _options(value) {\n        _options = {\n          ..._options,\n          ...value,\n        };\n      },\n    },\n    trigger,\n    register,\n    handleSubmit,\n    watch,\n    setValue,\n    getValues,\n    reset,\n    resetField,\n    clearErrors,\n    unregister,\n    setError,\n    setFocus,\n    getFieldState,\n  };\n}\n", "import React from 'react';\n\nimport { createFormControl } from './logic/createFormControl';\nimport getProxyFormState from './logic/getProxyFormState';\nimport shouldRenderFormState from './logic/shouldRenderFormState';\nimport deepEqual from './utils/deepEqual';\nimport isFunction from './utils/isFunction';\nimport {\n  FieldValues,\n  FormState,\n  InternalFieldName,\n  UseFormProps,\n  UseFormReturn,\n} from './types';\nimport { useSubscribe } from './useSubscribe';\n\n/**\n * Custom hook to manage the entire form.\n *\n * @remarks\n * [API](https://react-hook-form.com/api/useform) • [Demo](https://codesandbox.io/s/react-hook-form-get-started-ts-5ksmm) • [Video](https://www.youtube.com/watch?v=RkXv4AXXC_4)\n *\n * @param props - form configuration and validation parameters.\n *\n * @returns methods - individual functions to manage the form state. {@link UseFormReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, watch, formState: { errors } } = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   console.log(watch(\"example\"));\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input defaultValue=\"test\" {...register(\"example\")} />\n *       <input {...register(\"exampleRequired\", { required: true })} />\n *       {errors.exampleRequired && <span>This field is required</span>}\n *       <button>Submit</button>\n *     </form>\n *   );\n * }\n * ```\n */\nexport function useForm<\n  TFieldValues extends FieldValues = FieldValues,\n  TContext = any,\n  TTransformedValues extends FieldValues | undefined = undefined,\n>(\n  props: UseFormProps<TFieldValues, TContext> = {},\n): UseFormReturn<TFieldValues, TContext, TTransformedValues> {\n  const _formControl = React.useRef<\n    UseFormReturn<TFieldValues, TContext, TTransformedValues> | undefined\n  >();\n  const [formState, updateFormState] = React.useState<FormState<TFieldValues>>({\n    isDirty: false,\n    isValidating: false,\n    isLoading: isFunction(props.defaultValues),\n    isSubmitted: false,\n    isSubmitting: false,\n    isSubmitSuccessful: false,\n    isValid: false,\n    submitCount: 0,\n    dirtyFields: {},\n    touchedFields: {},\n    errors: {},\n    defaultValues: isFunction(props.defaultValues)\n      ? undefined\n      : props.defaultValues,\n  });\n\n  if (!_formControl.current) {\n    _formControl.current = {\n      ...createFormControl(props, () =>\n        updateFormState((formState) => ({ ...formState })),\n      ),\n      formState,\n    };\n  }\n\n  const control = _formControl.current.control;\n  control._options = props;\n\n  useSubscribe({\n    subject: control._subjects.state,\n    next: (\n      value: Partial<FormState<TFieldValues>> & { name?: InternalFieldName },\n    ) => {\n      if (\n        shouldRenderFormState(\n          value,\n          control._proxyFormState,\n          control._updateFormState,\n          true,\n        )\n      ) {\n        updateFormState({ ...control._formState });\n      }\n    },\n  });\n\n  React.useEffect(() => {\n    if (props.values && !deepEqual(props.values, control._defaultValues)) {\n      control._reset(props.values, control._options.resetOptions);\n    } else {\n      control._resetDefaultValues();\n    }\n  }, [props.values, control]);\n\n  React.useEffect(() => {\n    if (!control._state.mount) {\n      control._updateValid();\n      control._state.mount = true;\n    }\n\n    if (control._state.watch) {\n      control._state.watch = false;\n      control._subjects.state.next({ ...control._formState });\n    }\n\n    control._removeUnmounted();\n  });\n\n  _formControl.current.formState = getProxyFormState(formState, control);\n\n  return _formControl.current;\n}\n"], "mappings": ";;AAEA,IAAAA,eAAA,GAAgBC,OAAqB,IACnCA,OAAO,CAACC,IAAI,KAAK,UAAU;ACH7B,IAAAC,YAAA,GAAgBC,KAAc,IAAoBA,KAAK,YAAYC,IAAI;ACAvE,IAAAC,iBAAA,GAAgBF,KAAc,IAAgCA,KAAK,IAAI,IAAI;ACGpE,MAAMG,YAAY,GAAIH,KAAc,IAAK,OAAOA,KAAK,KAAK,QAAQ;AAEzE,IAAAI,QAAA,GAAkCJ,KAAc,IAC9C,CAACE,iBAAiB,CAACF,KAAK,CAAC,IACzB,CAACK,KAAK,CAACC,OAAO,CAACN,KAAK,CAAC,IACrBG,YAAY,CAACH,KAAK,CAAC,IACnB,CAACD,YAAY,CAACC,KAAK,CAAC;ACJtB,IAAAO,aAAA,GAAgBC,KAAc,IAC5BJ,QAAQ,CAACI,KAAK,CAAC,IAAKA,KAAe,CAACC,MAAM,GACtCb,eAAe,CAAEY,KAAe,CAACC,MAAM,CAAC,GACrCD,KAAe,CAACC,MAAM,CAACC,OAAO,GAC9BF,KAAe,CAACC,MAAM,CAACT,KAAK,GAC/BQ,KAAK;ACVX,IAAAG,iBAAA,GAAgBC,IAAY,IAC1BA,IAAI,CAACC,SAAS,CAAC,CAAC,EAAED,IAAI,CAACE,MAAM,CAAC,aAAa,CAAC,CAAC,IAAIF,IAAI;ACGvD,IAAAG,kBAAA,GAAeA,CAACC,KAA6B,EAAEJ,IAAuB,KACpEI,KAAK,CAACC,GAAG,CAACN,iBAAiB,CAACC,IAAI,CAAC,CAAC;ACHpC,IAAAM,aAAA,GAAgBC,UAAkB,IAAI;EACpC,MAAMC,aAAa,GACjBD,UAAU,CAACE,WAAW,IAAIF,UAAU,CAACE,WAAW,CAACC,SAAS;EAE5D,OACElB,QAAQ,CAACgB,aAAa,CAAC,IAAIA,aAAa,CAACG,cAAc,CAAC,eAAe,CAAC;AAE5E,CAAC;ACTD,IAAAC,KAAA,GAAe,OAAOC,MAAM,KAAK,WAAW,IAC1C,OAAOA,MAAM,CAACC,WAAW,KAAK,WAAW,IACzC,OAAOC,QAAQ,KAAK,WAAW;ACET,SAAAC,WAAWA,CAAIC,IAAO;EAC5C,IAAIC,IAAS;EACb,MAAMxB,OAAO,GAAGD,KAAK,CAACC,OAAO,CAACuB,IAAI,CAAC;EAEnC,IAAIA,IAAI,YAAY5B,IAAI,EAAE;IACxB6B,IAAI,GAAG,IAAI7B,IAAI,CAAC4B,IAAI,CAAC;EACtB,OAAM,IAAIA,IAAI,YAAYE,GAAG,EAAE;IAC9BD,IAAI,GAAG,IAAIC,GAAG,CAACF,IAAI,CAAC;EACrB,OAAM,IACL,EAAEL,KAAK,KAAKK,IAAI,YAAYG,IAAI,IAAIH,IAAI,YAAYI,QAAQ,CAAC,CAAC,KAC7D3B,OAAO,IAAIF,QAAQ,CAACyB,IAAI,CAAC,CAAC,EAC3B;IACAC,IAAI,GAAGxB,OAAO,GAAG,EAAE,GAAG,EAAE;IAExB,IAAI,CAACA,OAAO,IAAI,CAACY,aAAa,CAACW,IAAI,CAAC,EAAE;MACpCC,IAAI,GAAGD,IAAI;IACZ,OAAM;MACL,KAAK,MAAMK,GAAG,IAAIL,IAAI,EAAE;QACtB,IAAIA,IAAI,CAACN,cAAc,CAACW,GAAG,CAAC,EAAE;UAC5BJ,IAAI,CAACI,GAAG,CAAC,GAAGN,WAAW,CAACC,IAAI,CAACK,GAAG,CAAC,CAAC;QACnC;MACF;IACF;EACF,OAAM;IACL,OAAOL,IAAI;EACZ;EAED,OAAOC,IAAI;AACb;AChCA,IAAAK,OAAA,GAAwBnC,KAAe,IACrCK,KAAK,CAACC,OAAO,CAACN,KAAK,CAAC,GAAGA,KAAK,CAACoC,MAAM,CAACC,OAAO,CAAC,GAAG,EAAE;ACDnD,IAAAC,WAAA,GAAgBC,GAAY,IAAuBA,GAAG,KAAKC,SAAS;ACKpE,IAAAC,GAAA,GAAeA,CAAIC,GAAM,EAAEC,IAAa,EAAEC,YAAsB,KAAS;EACvE,IAAI,CAACD,IAAI,IAAI,CAACvC,QAAQ,CAACsC,GAAG,CAAC,EAAE;IAC3B,OAAOE,YAAY;EACpB;EAED,MAAMC,MAAM,GAAGV,OAAO,CAACQ,IAAI,CAACG,KAAK,CAAC,WAAW,CAAC,CAAC,CAACC,MAAM,CACpD,CAACF,MAAM,EAAEX,GAAG,KACVhC,iBAAiB,CAAC2C,MAAM,CAAC,GAAGA,MAAM,GAAGA,MAAM,CAACX,GAAe,CAAC,EAC9DQ,GAAG,CACJ;EAED,OAAOJ,WAAW,CAACO,MAAM,CAAC,IAAIA,MAAM,KAAKH,GAAG,GACxCJ,WAAW,CAACI,GAAG,CAACC,IAAe,CAAC,CAAC,GAC/BC,YAAY,GACZF,GAAG,CAACC,IAAe,CAAC,GACtBE,MAAM;AACZ,CAAC;ACnBM,MAAMG,MAAM,GAAG;EACpBC,IAAI,EAAE,MAAM;EACZC,SAAS,EAAE,UAAU;EACrBC,MAAM,EAAE;CACT;AAEM,MAAMC,eAAe,GAAmB;EAC7CC,MAAM,EAAE,QAAQ;EAChBC,QAAQ,EAAE,UAAU;EACpBC,QAAQ,EAAE,UAAU;EACpBC,SAAS,EAAE,WAAW;EACtBC,GAAG,EAAE;CACN;AAEM,MAAMC,sBAAsB,GAAG;EACpCC,GAAG,EAAE,KAAK;EACVC,GAAG,EAAE,KAAK;EACVC,SAAS,EAAE,WAAW;EACtBC,SAAS,EAAE,WAAW;EACtBC,OAAO,EAAE,SAAS;EAClBC,QAAQ,EAAE,UAAU;EACpBC,QAAQ,EAAE;CACX;ACpBD,MAAMC,eAAe,GAAGC,cAAK,CAACC,aAAa,CAAuB,IAAI,CAAC;AAEvE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BG;AACI,MAAMC,cAAc,GAAGA,CAAA,KAI5BF,cAAK,CAACG,UAAU,CAACJ,eAAe;AAKlC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BG;AACU,MAAAK,YAAY,GAKvBC,KAAoE,IAClE;EACF,MAAM;IAAEC,QAAQ;IAAE,GAAG5C;EAAI,CAAE,GAAG2C,KAAK;EACnC,OACEL,cAAA,CAAAO,aAAA,CAACR,eAAe,CAACS,QAAQ;IAAC3E,KAAK,EAAE6B;EAAgC,GAC9D4C,QAAQ,CACgB;AAE/B;ACrFA,IAAAG,iBAAA,GAAe,SAAAA,CACbC,SAAkC,EAClCC,OAAwC,EACxCC,mBAAmC,EAEjC;EAAA,IADFC,MAAM,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAzC,SAAA,GAAAyC,SAAA,MAAG,IAAI;EAEb,MAAMpC,MAAM,GAAG;IACbsC,aAAa,EAAEL,OAAO,CAACM;GACJ;EAErB,KAAK,MAAMlD,GAAG,IAAI2C,SAAS,EAAE;IAC3BQ,MAAM,CAACC,cAAc,CAACzC,MAAM,EAAEX,GAAG,EAAE;MACjCO,GAAG,EAAEA,CAAA,KAAK;QACR,MAAM8C,IAAI,GAAGrD,GAA0D;QAEvE,IAAI4C,OAAO,CAACU,eAAe,CAACD,IAAI,CAAC,KAAKnC,eAAe,CAACK,GAAG,EAAE;UACzDqB,OAAO,CAACU,eAAe,CAACD,IAAI,CAAC,GAAG,CAACP,MAAM,IAAI5B,eAAe,CAACK,GAAG;QAC/D;QAEDsB,mBAAmB,KAAKA,mBAAmB,CAACQ,IAAI,CAAC,GAAG,IAAI,CAAC;QACzD,OAAOV,SAAS,CAACU,IAAI,CAAC;;IAEzB,EAAC;EACH;EAED,OAAO1C,MAAM;AACf,CAAC;ACzBD,IAAA4C,aAAA,GAAgBzF,KAAc,IAC5BI,QAAQ,CAACJ,KAAK,CAAC,IAAI,CAACqF,MAAM,CAACK,IAAI,CAAC1F,KAAK,CAAC,CAACkF,MAAM;ACK/C,IAAAS,qBAAA,GAAeA,CACbC,aAAmE,EACnEJ,eAAkB,EAClBK,eAA+C,EAC/Cb,MAAgB,KACd;EACFa,eAAe,CAACD,aAAa,CAAC;EAC9B,MAAM;IAAEhF,IAAI;IAAE,GAAGiE;EAAS,CAAE,GAAGe,aAAa;EAE5C,OACEH,aAAa,CAACZ,SAAS,CAAC,IACxBQ,MAAM,CAACK,IAAI,CAACb,SAAS,CAAC,CAACK,MAAM,IAAIG,MAAM,CAACK,IAAI,CAACF,eAAe,CAAC,CAACN,MAAM,IACpEG,MAAM,CAACK,IAAI,CAACb,SAAS,CAAC,CAACiB,IAAI,CACxB5D,GAAG,IACFsD,eAAe,CAACtD,GAA0B,CAAC,MAC1C,CAAC8C,MAAM,IAAI5B,eAAe,CAACK,GAAG,CAAC,CACnC;AAEL,CAAC;AC5BD,IAAAsC,qBAAA,GAAmB/F,KAAQ,IAAMK,KAAK,CAACC,OAAO,CAACN,KAAK,CAAC,GAAGA,KAAK,GAAG,CAACA,KAAK,CAAE;ACExE,IAAAgG,qBAAA,GAAeA,CACbpF,IAAQ,EACRqF,UAAmB,EACnBC,KAAe,KAEfA,KAAK,IAAID,UAAU,GACfrF,IAAI,KAAKqF,UAAU,GACnB,CAACrF,IAAI,IACL,CAACqF,UAAU,IACXrF,IAAI,KAAKqF,UAAU,IACnBF,qBAAqB,CAACnF,IAAI,CAAC,CAACuF,IAAI,CAC7BC,WAAW,IACVA,WAAW,KACVA,WAAW,CAACC,UAAU,CAACJ,UAAU,CAAC,IACjCA,UAAU,CAACI,UAAU,CAACD,WAAW,CAAC,CAAC,CACxC;ACPD,SAAUE,YAAYA,CAAI9B,KAAe;EAC7C,MAAM+B,MAAM,GAAGpC,cAAK,CAACqC,MAAM,CAAChC,KAAK,CAAC;EAClC+B,MAAM,CAACE,OAAO,GAAGjC,KAAK;EAEtBL,cAAK,CAACuC,SAAS,CAAC,MAAK;IACnB,MAAMC,YAAY,GAChB,CAACnC,KAAK,CAACoC,QAAQ,IACfL,MAAM,CAACE,OAAO,CAACI,OAAO,IACtBN,MAAM,CAACE,OAAO,CAACI,OAAO,CAACC,SAAS,CAAC;MAC/BC,IAAI,EAAER,MAAM,CAACE,OAAO,CAACM;IACtB,EAAC;IAEJ,OAAO,MAAK;MACVJ,YAAY,IAAIA,YAAY,CAACK,WAAW,EAAE;IAC5C,CAAC;EACH,CAAC,EAAE,CAACxC,KAAK,CAACoC,QAAQ,CAAC,CAAC;AACtB;;ACXA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BG;AACH,SAASK,YAAYA,CACnBzC,KAAuC;EAEvC,MAAM0C,OAAO,GAAG7C,cAAc,EAAgB;EAC9C,MAAM;IAAES,OAAO,GAAGoC,OAAO,CAACpC,OAAO;IAAE8B,QAAQ;IAAEhG,IAAI;IAAEsF;EAAK,CAAE,GAAG1B,KAAK,IAAI,EAAE;EACxE,MAAM,CAACK,SAAS,EAAEgB,eAAe,CAAC,GAAG1B,cAAK,CAACgD,QAAQ,CAACrC,OAAO,CAACsC,UAAU,CAAC;EACvE,MAAMC,QAAQ,GAAGlD,cAAK,CAACqC,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMc,oBAAoB,GAAGnD,cAAK,CAACqC,MAAM,CAAC;IACxCe,OAAO,EAAE,KAAK;IACdC,SAAS,EAAE,KAAK;IAChBC,WAAW,EAAE,KAAK;IAClBC,aAAa,EAAE,KAAK;IACpBC,YAAY,EAAE,KAAK;IACnBC,OAAO,EAAE,KAAK;IACdC,MAAM,EAAE;EACT,EAAC;EACF,MAAMC,KAAK,GAAG3D,cAAK,CAACqC,MAAM,CAAC5F,IAAI,CAAC;EAEhCkH,KAAK,CAACrB,OAAO,GAAG7F,IAAI;EAEpB0F,YAAY,CAAC;IACXM,QAAQ;IACRG,IAAI,EACF/G,KAAsE,IAEtEqH,QAAQ,CAACZ,OAAO,IAChBT,qBAAqB,CACnB8B,KAAK,CAACrB,OAA4B,EAClCzG,KAAK,CAACY,IAAI,EACVsF,KAAK,CACN,IACDP,qBAAqB,CACnB3F,KAAK,EACLsH,oBAAoB,CAACb,OAAO,EAC5B3B,OAAO,CAACiD,gBAAgB,CACzB,IACDlC,eAAe,CAAC;MACd,GAAGf,OAAO,CAACsC,UAAU;MACrB,GAAGpH;KACJ,CAAC;IACJ6G,OAAO,EAAE/B,OAAO,CAACkD,SAAS,CAACC;EAC5B,EAAC;EAEF9D,cAAK,CAACuC,SAAS,CAAC,MAAK;IACnBW,QAAQ,CAACZ,OAAO,GAAG,IAAI;IACvBa,oBAAoB,CAACb,OAAO,CAACmB,OAAO,IAAI9C,OAAO,CAACoD,YAAY,CAAC,IAAI,CAAC;IAElE,OAAO,MAAK;MACVb,QAAQ,CAACZ,OAAO,GAAG,KAAK;IAC1B,CAAC;EACH,CAAC,EAAE,CAAC3B,OAAO,CAAC,CAAC;EAEb,OAAOF,iBAAiB,CACtBC,SAAS,EACTC,OAAO,EACPwC,oBAAoB,CAACb,OAAO,EAC5B,KAAK,CACN;AACH;ACvGA,IAAA0B,QAAA,GAAgBnI,KAAc,IAAsB,OAAOA,KAAK,KAAK,QAAQ;ACI7E,IAAAoI,mBAAA,GAAeA,CACbpH,KAAoC,EACpCqH,MAAa,EACbC,UAAwB,EACxBC,QAAkB,EAClB3F,YAAuC,KACrC;EACF,IAAIuF,QAAQ,CAACnH,KAAK,CAAC,EAAE;IACnBuH,QAAQ,IAAIF,MAAM,CAACG,KAAK,CAACC,GAAG,CAACzH,KAAK,CAAC;IACnC,OAAOyB,GAAG,CAAC6F,UAAU,EAAEtH,KAAK,EAAE4B,YAAY,CAAC;EAC5C;EAED,IAAIvC,KAAK,CAACC,OAAO,CAACU,KAAK,CAAC,EAAE;IACxB,OAAOA,KAAK,CAAC0H,GAAG,CACbC,SAAS,KACRJ,QAAQ,IAAIF,MAAM,CAACG,KAAK,CAACC,GAAG,CAACE,SAAS,CAAC,EAAElG,GAAG,CAAC6F,UAAU,EAAEK,SAAS,CAAC,CACpE,CACF;EACF;EAEDJ,QAAQ,KAAKF,MAAM,CAACO,QAAQ,GAAG,IAAI,CAAC;EAEpC,OAAON,UAAU;AACnB,CAAC;;ACmGD;;;;;;;;;;;;;;;AAeG;AACG,SAAUO,QAAQA,CACtBrE,KAAmC;EAEnC,MAAM0C,OAAO,GAAG7C,cAAc,EAAE;EAChC,MAAM;IACJS,OAAO,GAAGoC,OAAO,CAACpC,OAAO;IACzBlE,IAAI;IACJgC,YAAY;IACZgE,QAAQ;IACRV;EAAK,CACN,GAAG1B,KAAK,IAAI,EAAE;EACf,MAAMsD,KAAK,GAAG3D,cAAK,CAACqC,MAAM,CAAC5F,IAAI,CAAC;EAEhCkH,KAAK,CAACrB,OAAO,GAAG7F,IAAI;EAEpB0F,YAAY,CAAC;IACXM,QAAQ;IACRC,OAAO,EAAE/B,OAAO,CAACkD,SAAS,CAACc,MAAM;IACjC/B,IAAI,EAAGlC,SAA6D,IAAI;MACtE,IACEmB,qBAAqB,CACnB8B,KAAK,CAACrB,OAA4B,EAClC5B,SAAS,CAACjE,IAAI,EACdsF,KAAK,CACN,EACD;QACA6C,WAAW,CACTnH,WAAW,CACTwG,mBAAmB,CACjBN,KAAK,CAACrB,OAAkD,EACxD3B,OAAO,CAACuD,MAAM,EACdxD,SAAS,CAACiE,MAAM,IAAIhE,OAAO,CAACkE,WAAW,EACvC,KAAK,EACLpG,YAAY,CACb,CACF,CACF;MACF;;EAEJ,EAAC;EAEF,MAAM,CAAC5C,KAAK,EAAE+I,WAAW,CAAC,GAAG5E,cAAK,CAACgD,QAAQ,CACzCrC,OAAO,CAACmE,SAAS,CACfrI,IAAyB,EACzBgC,YAAqD,CACtD,CACF;EAEDuB,cAAK,CAACuC,SAAS,CAAC,MAAM5B,OAAO,CAACoE,gBAAgB,EAAE,CAAC;EAEjD,OAAOlJ,KAAK;AACd;ACjMA,IAAAmJ,KAAA,GAAgBnJ,KAAa,IAAK,OAAO,CAACoJ,IAAI,CAACpJ,KAAK,CAAC;ACErD,IAAAqJ,YAAA,GAAgBC,KAAa,IAC3BnH,OAAO,CAACmH,KAAK,CAACC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAACzG,KAAK,CAAC,OAAO,CAAC,CAAC;ACGhC,SAAA0G,GAAGA,CACzBC,MAAmB,EACnB9G,IAAY,EACZ3C,KAAe;EAEf,IAAI0J,KAAK,GAAG,CAAC,CAAC;EACd,MAAMC,QAAQ,GAAGR,KAAK,CAACxG,IAAI,CAAC,GAAG,CAACA,IAAI,CAAC,GAAG0G,YAAY,CAAC1G,IAAI,CAAC;EAC1D,MAAMuC,MAAM,GAAGyE,QAAQ,CAACzE,MAAM;EAC9B,MAAM0E,SAAS,GAAG1E,MAAM,GAAG,CAAC;EAE5B,OAAO,EAAEwE,KAAK,GAAGxE,MAAM,EAAE;IACvB,MAAMhD,GAAG,GAAGyH,QAAQ,CAACD,KAAK,CAAC;IAC3B,IAAIG,QAAQ,GAAG7J,KAAK;IAEpB,IAAI0J,KAAK,KAAKE,SAAS,EAAE;MACvB,MAAME,QAAQ,GAAGL,MAAM,CAACvH,GAAG,CAAC;MAC5B2H,QAAQ,GACNzJ,QAAQ,CAAC0J,QAAQ,CAAC,IAAIzJ,KAAK,CAACC,OAAO,CAACwJ,QAAQ,CAAC,GACzCA,QAAQ,GACR,CAACC,KAAK,CAAC,CAACJ,QAAQ,CAACD,KAAK,GAAG,CAAC,CAAC,CAAC,GAC5B,EAAE,GACF,EAAE;IACT;IACDD,MAAM,CAACvH,GAAG,CAAC,GAAG2H,QAAQ;IACtBJ,MAAM,GAAGA,MAAM,CAACvH,GAAG,CAAC;EACrB;EACD,OAAOuH,MAAM;AACf;;ACVA;;;;;;;;;;;;;;;;;;;;;;;AAuBG;AACG,SAAUO,aAAaA,CAI3BxF,KAA8C;EAE9C,MAAM0C,OAAO,GAAG7C,cAAc,EAAgB;EAC9C,MAAM;IAAEzD,IAAI;IAAEkE,OAAO,GAAGoC,OAAO,CAACpC,OAAO;IAAEmF;EAAgB,CAAE,GAAGzF,KAAK;EACnE,MAAM0F,YAAY,GAAGnJ,kBAAkB,CAAC+D,OAAO,CAACuD,MAAM,CAAC8B,KAAK,EAAEvJ,IAAI,CAAC;EACnE,MAAMZ,KAAK,GAAG6I,QAAQ,CAAC;IACrB/D,OAAO;IACPlE,IAAI;IACJgC,YAAY,EAAEH,GAAG,CACfqC,OAAO,CAACkE,WAAW,EACnBpI,IAAI,EACJ6B,GAAG,CAACqC,OAAO,CAACM,cAAc,EAAExE,IAAI,EAAE4D,KAAK,CAAC5B,YAAY,CAAC,CACtD;IACDsD,KAAK,EAAE;EACR,EAAwC;EACzC,MAAMrB,SAAS,GAAGoC,YAAY,CAAC;IAC7BnC,OAAO;IACPlE;EACD,EAAC;EAEF,MAAMwJ,cAAc,GAAGjG,cAAK,CAACqC,MAAM,CACjC1B,OAAO,CAACuF,QAAQ,CAACzJ,IAAI,EAAE;IACrB,GAAG4D,KAAK,CAAC8F,KAAK;IACdtK;EACD,EAAC,CACH;EAEDoK,cAAc,CAAC3D,OAAO,GAAG3B,OAAO,CAACuF,QAAQ,CAACzJ,IAAI,EAAE4D,KAAK,CAAC8F,KAAK,CAAC;EAE5DnG,cAAK,CAACuC,SAAS,CAAC,MAAK;IACnB,MAAM6D,sBAAsB,GAC1BzF,OAAO,CAAC0F,QAAQ,CAACP,gBAAgB,IAAIA,gBAAgB;IAEvD,MAAMQ,aAAa,GAAGA,CAAC7J,IAAuB,EAAEZ,KAAc,KAAI;MAChE,MAAM0K,KAAK,GAAUjI,GAAG,CAACqC,OAAO,CAAC6F,OAAO,EAAE/J,IAAI,CAAC;MAE/C,IAAI8J,KAAK,EAAE;QACTA,KAAK,CAACE,EAAE,CAACC,KAAK,GAAG7K,KAAK;MACvB;IACH,CAAC;IAEDyK,aAAa,CAAC7J,IAAI,EAAE,IAAI,CAAC;IAEzB,IAAI2J,sBAAsB,EAAE;MAC1B,MAAMvK,KAAK,GAAG4B,WAAW,CAACa,GAAG,CAACqC,OAAO,CAAC0F,QAAQ,CAACrF,aAAa,EAAEvE,IAAI,CAAC,CAAC;MACpE4I,GAAG,CAAC1E,OAAO,CAACM,cAAc,EAAExE,IAAI,EAAEZ,KAAK,CAAC;MACxC,IAAIsC,WAAW,CAACG,GAAG,CAACqC,OAAO,CAACkE,WAAW,EAAEpI,IAAI,CAAC,CAAC,EAAE;QAC/C4I,GAAG,CAAC1E,OAAO,CAACkE,WAAW,EAAEpI,IAAI,EAAEZ,KAAK,CAAC;MACtC;IACF;IAED,OAAO,MAAK;MACV,CACEkK,YAAY,GACRK,sBAAsB,IAAI,CAACzF,OAAO,CAACgG,MAAM,CAACC,MAAM,GAChDR,sBAAsB,IAExBzF,OAAO,CAACkG,UAAU,CAACpK,IAAI,CAAC,GACxB6J,aAAa,CAAC7J,IAAI,EAAE,KAAK,CAAC;IAChC,CAAC;GACF,EAAE,CAACA,IAAI,EAAEkE,OAAO,EAAEoF,YAAY,EAAED,gBAAgB,CAAC,CAAC;EAEnD,OAAO;IACLS,KAAK,EAAE;MACL9J,IAAI;MACJZ,KAAK;MACLsD,QAAQ,EAAEa,cAAK,CAAC8G,WAAW,CACxBzK,KAAK,IACJ4J,cAAc,CAAC3D,OAAO,CAACnD,QAAQ,CAAC;QAC9B7C,MAAM,EAAE;UACNT,KAAK,EAAEO,aAAa,CAACC,KAAK,CAAC;UAC3BI,IAAI,EAAEA;QACP;QACDd,IAAI,EAAEkD,MAAM,CAACG;MACd,EAAC,EACJ,CAACvC,IAAI,CAAC,CACP;MACDyC,MAAM,EAAEc,cAAK,CAAC8G,WAAW,CACvB,MACEb,cAAc,CAAC3D,OAAO,CAACpD,MAAM,CAAC;QAC5B5C,MAAM,EAAE;UACNT,KAAK,EAAEyC,GAAG,CAACqC,OAAO,CAACkE,WAAW,EAAEpI,IAAI,CAAC;UACrCA,IAAI,EAAEA;QACP;QACDd,IAAI,EAAEkD,MAAM,CAACC;MACd,EAAC,EACJ,CAACrC,IAAI,EAAEkE,OAAO,CAAC,CAChB;MACDoG,GAAG,EAAGC,GAAG,IAAI;QACX,MAAMT,KAAK,GAAGjI,GAAG,CAACqC,OAAO,CAAC6F,OAAO,EAAE/J,IAAI,CAAC;QAExC,IAAI8J,KAAK,IAAIS,GAAG,EAAE;UAChBT,KAAK,CAACE,EAAE,CAACM,GAAG,GAAG;YACbE,KAAK,EAAEA,CAAA,KAAMD,GAAG,CAACC,KAAK,EAAE;YACxBC,MAAM,EAAEA,CAAA,KAAMF,GAAG,CAACE,MAAM,EAAE;YAC1BC,iBAAiB,EAAGC,OAAe,IACjCJ,GAAG,CAACG,iBAAiB,CAACC,OAAO,CAAC;YAChCC,cAAc,EAAEA,CAAA,KAAML,GAAG,CAACK,cAAc;WACzC;QACF;;IAEJ;IACD3G,SAAS;IACT4G,UAAU,EAAEpG,MAAM,CAACqG,gBAAgB,CACjC,EAAE,EACF;MACEC,OAAO,EAAE;QACPC,UAAU,EAAE,IAAI;QAChBnJ,GAAG,EAAEA,CAAA,KAAM,CAAC,CAACA,GAAG,CAACoC,SAAS,CAACgD,MAAM,EAAEjH,IAAI;MACxC;MACD2G,OAAO,EAAE;QACPqE,UAAU,EAAE,IAAI;QAChBnJ,GAAG,EAAEA,CAAA,KAAM,CAAC,CAACA,GAAG,CAACoC,SAAS,CAAC4C,WAAW,EAAE7G,IAAI;MAC7C;MACDiL,SAAS,EAAE;QACTD,UAAU,EAAE,IAAI;QAChBnJ,GAAG,EAAEA,CAAA,KAAM,CAAC,CAACA,GAAG,CAACoC,SAAS,CAAC6C,aAAa,EAAE9G,IAAI;MAC/C;MACDkL,KAAK,EAAE;QACLF,UAAU,EAAE,IAAI;QAChBnJ,GAAG,EAAEA,CAAA,KAAMA,GAAG,CAACoC,SAAS,CAACgD,MAAM,EAAEjH,IAAI;MACtC;KACF;GAEJ;AACH;;AC7KA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyCG;AACH,MAAMmL,UAAU,GAIdvH,KAA2C,IACxCA,KAAK,CAACwH,MAAM,CAAChC,aAAa,CAAsBxF,KAAK,CAAC;ACR3D,MAAMyH,YAAY,GAAG,MAAM;AAE3B;;;;;;;;;;;;;;;;;;;;;AAqBG;AACG,SAAUC,IAAIA,CAGlB1H,KAAsB;EACtB,MAAM0C,OAAO,GAAG7C,cAAc,EAAK;EACnC,MAAM,CAAC8H,OAAO,EAAEC,UAAU,CAAC,GAAGC,KAAK,CAAClF,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM;IACJrC,OAAO,GAAGoC,OAAO,CAACpC,OAAO;IACzBvB,QAAQ;IACRkB,QAAQ;IACRsG,MAAM;IACNuB,MAAM,GAAGL,YAAY;IACrBM,OAAO;IACPC,OAAO;IACPC,OAAO;IACPT,MAAM;IACNU,SAAS;IACTC,cAAc;IACd,GAAGC;EAAI,CACR,GAAGpI,KAAK;EAET,MAAMqI,MAAM,GAAG,MAAOrM,KAAgC,IAAI;IACxD,IAAIsM,QAAQ,GAAG,KAAK;IACpB,IAAIhN,IAAI,GAAG,EAAE;IAEb,MAAMgF,OAAO,CAACiI,YAAY,CAAC,MAAOlL,IAAI,IAAI;MACxC,MAAMmL,QAAQ,GAAG,IAAIC,QAAQ,EAAE;MAC/B,IAAIC,YAAY,GAAG,EAAE;MAErB,IAAI;QACFA,YAAY,GAAGC,IAAI,CAACC,SAAS,CAACvL,IAAI,CAAC;MACpC,EAAC,OAAAwL,EAAA,EAAM;MAER,KAAK,MAAMzM,IAAI,IAAIkE,OAAO,CAACuD,MAAM,CAACwC,KAAK,EAAE;QACvCmC,QAAQ,CAACM,MAAM,CAAC1M,IAAI,EAAE6B,GAAG,CAACZ,IAAI,EAAEjB,IAAI,CAAC,CAAC;MACvC;MAED,IAAI2C,QAAQ,EAAE;QACZA,QAAQ,CAAC;UACP1B,IAAI;UACJrB,KAAK;UACL8L,MAAM;UACNU,QAAQ;UACRE;QACD,EAAC;MACH;MAED,IAAInC,MAAM,EAAE;QACV,IAAI;UACF,MAAMwC,6BAA6B,GAAG,CACpChB,OAAO,IAAIA,OAAO,CAAC,cAAc,CAAC,EAClCC,OAAO,CACR,CAACrG,IAAI,CAAEnG,KAAK,IAAKA,KAAK,IAAIA,KAAK,CAACwN,QAAQ,CAAC,MAAM,CAAC,CAAC;UAElD,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC3C,MAAM,EAAE;YACnCuB,MAAM;YACNC,OAAO,EAAE;cACP,GAAGA,OAAO;cACV,IAAIC,OAAO,GAAG;gBAAE,cAAc,EAAEA;cAAO,CAAE,GAAG,EAAE;YAC/C;YACDmB,IAAI,EAAEJ,6BAA6B,GAAGL,YAAY,GAAGF;UACtD,EAAC;UAEF,IACES,QAAQ,KACPd,cAAc,GACX,CAACA,cAAc,CAACc,QAAQ,CAACG,MAAM,CAAC,GAChCH,QAAQ,CAACG,MAAM,GAAG,GAAG,IAAIH,QAAQ,CAACG,MAAM,IAAI,GAAG,CAAC,EACpD;YACAd,QAAQ,GAAG,IAAI;YACfL,OAAO,IAAIA,OAAO,CAAC;cAAEgB;YAAQ,CAAE,CAAC;YAChC3N,IAAI,GAAG+N,MAAM,CAACJ,QAAQ,CAACG,MAAM,CAAC;UAC/B,OAAM;YACLlB,SAAS,IAAIA,SAAS,CAAC;cAAEe;YAAQ,CAAE,CAAC;UACrC;QACF,EAAC,OAAO3B,KAAc,EAAE;UACvBgB,QAAQ,GAAG,IAAI;UACfL,OAAO,IAAIA,OAAO,CAAC;YAAEX;UAAK,CAAE,CAAC;QAC9B;MACF;IACH,CAAC,CAAC,CAACtL,KAAK,CAAC;IAET,IAAIsM,QAAQ,IAAItI,KAAK,CAACM,OAAO,EAAE;MAC7BN,KAAK,CAACM,OAAO,CAACkD,SAAS,CAACC,KAAK,CAAClB,IAAI,CAAC;QACjC+G,kBAAkB,EAAE;MACrB,EAAC;MACFtJ,KAAK,CAACM,OAAO,CAACiJ,QAAQ,CAAC,aAAa,EAAE;QACpCjO;MACD,EAAC;IACH;EACH,CAAC;EAEDuM,KAAK,CAAC3F,SAAS,CAAC,MAAK;IACnB0F,UAAU,CAAC,IAAI,CAAC;GACjB,EAAE,EAAE,CAAC;EAEN,OAAOJ,MAAM,GACXK,KAAA,CAAA3H,aAAA,CAAA2H,KAAA,CAAA2B,QAAA,QACGhC,MAAM,CAAC;IACNa;EACD,EAAC,CACD,GAEHR,KAAA,CAAA3H,aAAA;IACEuJ,UAAU,EAAE9B,OAAO;IACnBpB,MAAM,EAAEA,MAAM;IACduB,MAAM,EAAEA,MAAM;IACdE,OAAO,EAAEA,OAAO;IAChBjJ,QAAQ,EAAEsJ,MAAM;IAAA,GACZD;EAAI,GAEPnI,QAAQ,CAEZ;AACH;AC9KA,IAAAyJ,YAAA,GAAeA,CACbtN,IAAuB,EACvBuN,wBAAiC,EACjCtG,MAA2B,EAC3B/H,IAAY,EACZyL,OAAuB,KAEvB4C,wBAAwB,GACpB;EACE,GAAGtG,MAAM,CAACjH,IAAI,CAAC;EACfwN,KAAK,EAAE;IACL,IAAIvG,MAAM,CAACjH,IAAI,CAAC,IAAIiH,MAAM,CAACjH,IAAI,CAAE,CAACwN,KAAK,GAAGvG,MAAM,CAACjH,IAAI,CAAE,CAACwN,KAAK,GAAG,EAAE,CAAC;IACnE,CAACtO,IAAI,GAAGyL,OAAO,IAAI;EACpB;AACF,IACD,EAAE;ACjBR,MAAM8C,YAAY,GAAGA,CACnBC,MAAiB,EACjBC,QAAoC,EACpCC,WAA0D,KACxD;EACF,KAAK,MAAMtM,GAAG,IAAIsM,WAAW,IAAInJ,MAAM,CAACK,IAAI,CAAC4I,MAAM,CAAC,EAAE;IACpD,MAAM5D,KAAK,GAAGjI,GAAG,CAAC6L,MAAM,EAAEpM,GAAG,CAAC;IAE9B,IAAIwI,KAAK,EAAE;MACT,MAAM;QAAEE,EAAE;QAAE,GAAG6D;MAAY,CAAE,GAAG/D,KAAK;MAErC,IAAIE,EAAE,IAAI2D,QAAQ,CAAC3D,EAAE,CAAChK,IAAI,CAAC,EAAE;QAC3B,IAAIgK,EAAE,CAACM,GAAG,CAACE,KAAK,EAAE;UAChBR,EAAE,CAACM,GAAG,CAACE,KAAK,EAAE;UACd;QACD,OAAM,IAAIR,EAAE,CAAC8D,IAAI,IAAI9D,EAAE,CAAC8D,IAAI,CAAC,CAAC,CAAC,CAACtD,KAAK,EAAE;UACtCR,EAAE,CAAC8D,IAAI,CAAC,CAAC,CAAC,CAACtD,KAAK,EAAE;UAClB;QACD;MACF,OAAM,IAAIhL,QAAQ,CAACqO,YAAY,CAAC,EAAE;QACjCJ,YAAY,CAACI,YAAY,EAAEF,QAAQ,CAAC;MACrC;IACF;EACF;AACH,CAAC;AC5BD,IAAAI,UAAA,GAAeA,CAAA,KAAK;EAClB,MAAMC,CAAC,GACL,OAAOC,WAAW,KAAK,WAAW,GAAG5O,IAAI,CAAC6O,GAAG,EAAE,GAAGD,WAAW,CAACC,GAAG,EAAE,GAAG,IAAI;EAE5E,OAAO,sCAAsC,CAACvF,OAAO,CAAC,OAAO,EAAGwF,CAAC,IAAI;IACnE,MAAMC,CAAC,GAAG,CAACC,IAAI,CAACC,MAAM,EAAE,GAAG,EAAE,GAAGN,CAAC,IAAI,EAAE,GAAG,CAAC;IAE3C,OAAO,CAACG,CAAC,IAAI,GAAG,GAAGC,CAAC,GAAIA,CAAC,GAAG,GAAG,GAAI,GAAG,EAAEG,QAAQ,CAAC,EAAE,CAAC;EACtD,CAAC,CAAC;AACJ,CAAC;ACND,IAAAC,iBAAA,GAAe,SAAAA,CACbxO,IAAuB,EACvB8I,KAAa;EAAA,IACb2F,OAAA,GAAApK,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAzC,SAAA,GAAAyC,SAAA,MAAiC,EAAE;EAAA,OAEnCoK,OAAO,CAACC,WAAW,IAAIhN,WAAW,CAAC+M,OAAO,CAACC,WAAW,CAAC,GACnDD,OAAO,CAACE,SAAS,IACjB,GAAG3O,IAAQ,IAAA0B,WAAW,CAAC+M,OAAO,CAACG,UAAU,CAAC,GAAG9F,KAAK,GAAG2F,OAAO,CAACG,UAAa,MAC1E,EAAE;AAAA;ACRR,IAAAC,kBAAA,GACEC,IAAW,KAOP;EACJC,UAAU,EAAE,CAACD,IAAI,IAAIA,IAAI,KAAKtM,eAAe,CAACG,QAAQ;EACtDqM,QAAQ,EAAEF,IAAI,KAAKtM,eAAe,CAACC,MAAM;EACzCwM,UAAU,EAAEH,IAAI,KAAKtM,eAAe,CAACE,QAAQ;EAC7CwM,OAAO,EAAEJ,IAAI,KAAKtM,eAAe,CAACK,GAAG;EACrCsM,SAAS,EAAEL,IAAI,KAAKtM,eAAe,CAACI;AACrC,EAAC;ACfF,IAAAwM,SAAA,GAAeA,CACbpP,IAAuB,EACvByH,MAAa,EACb4H,WAAqB,KAErB,CAACA,WAAW,KACX5H,MAAM,CAACO,QAAQ,IACdP,MAAM,CAACG,KAAK,CAACvH,GAAG,CAACL,IAAI,CAAC,IACtB,CAAC,GAAGyH,MAAM,CAACG,KAAK,CAAC,CAACrC,IAAI,CACnB+J,SAAS,IACRtP,IAAI,CAACyF,UAAU,CAAC6J,SAAS,CAAC,IAC1B,QAAQ,CAAC9G,IAAI,CAACxI,IAAI,CAACuP,KAAK,CAACD,SAAS,CAAChL,MAAM,CAAC,CAAC,CAC9C,CAAC;ACJN,IAAAkL,yBAAA,GAAeA,CACbvI,MAAsB,EACtBiE,KAA0C,EAC1ClL,IAAuB,KACL;EAClB,MAAMyP,gBAAgB,GAAGlO,OAAO,CAACM,GAAG,CAACoF,MAAM,EAAEjH,IAAI,CAAC,CAAC;EACnD4I,GAAG,CAAC6G,gBAAgB,EAAE,MAAM,EAAEvE,KAAK,CAAClL,IAAI,CAAC,CAAC;EAC1C4I,GAAG,CAAC3B,MAAM,EAAEjH,IAAI,EAAEyP,gBAAgB,CAAC;EACnC,OAAOxI,MAAM;AACf,CAAC;ACnBD,IAAAyI,SAAA,GAAgBtQ,KAAc,IAAuB,OAAOA,KAAK,KAAK,SAAS;ACE/E,IAAAuQ,WAAA,GAAgB1Q,OAAqB,IACnCA,OAAO,CAACC,IAAI,KAAK,MAAM;ACHzB,IAAA0Q,UAAA,GAAgBxQ,KAAc,IAC5B,OAAOA,KAAK,KAAK,UAAU;ACC7B,IAAAyQ,aAAA,GAAgBzQ,KAAc,IAA0B;EACtD,IAAI,CAACwB,KAAK,EAAE;IACV,OAAO,KAAK;EACb;EAED,MAAMkP,KAAK,GAAG1Q,KAAK,GAAKA,KAAqB,CAAC2Q,aAA0B,GAAG,CAAC;EAC5E,OACE3Q,KAAK,aACJ0Q,KAAK,IAAIA,KAAK,CAACE,WAAW,GAAGF,KAAK,CAACE,WAAW,CAAClP,WAAW,GAAGA,WAAW,CAAC;AAE9E,CAAC;ACTD,IAAAmP,SAAA,GAAgB7Q,KAAc,IAAuBmI,QAAQ,CAACnI,KAAK,CAAC;ACDpE,IAAA8Q,YAAA,GAAgBjR,OAAqB,IACnCA,OAAO,CAACC,IAAI,KAAK,OAAO;ACH1B,IAAAiR,OAAA,GAAgB/Q,KAAc,IAAsBA,KAAK,YAAYgR,MAAM;ACO3E,MAAMC,aAAa,GAAwB;EACzCjR,KAAK,EAAE,KAAK;EACZ4H,OAAO,EAAE;CACV;AAED,MAAMsJ,WAAW,GAAG;EAAElR,KAAK,EAAE,IAAI;EAAE4H,OAAO,EAAE;AAAI,CAAE;AAElD,IAAAuJ,gBAAA,GAAgB9B,OAA4B,IAAyB;EACnE,IAAIhP,KAAK,CAACC,OAAO,CAAC+O,OAAO,CAAC,EAAE;IAC1B,IAAIA,OAAO,CAACnK,MAAM,GAAG,CAAC,EAAE;MACtB,MAAM4D,MAAM,GAAGuG,OAAO,CACnBjN,MAAM,CAAEgP,MAAM,IAAKA,MAAM,IAAIA,MAAM,CAAC1Q,OAAO,IAAI,CAAC0Q,MAAM,CAACxK,QAAQ,CAAC,CAChE8B,GAAG,CAAE0I,MAAM,IAAKA,MAAM,CAACpR,KAAK,CAAC;MAChC,OAAO;QAAEA,KAAK,EAAE8I,MAAM;QAAElB,OAAO,EAAE,CAAC,CAACkB,MAAM,CAAC5D;MAAM,CAAE;IACnD;IAED,OAAOmK,OAAO,CAAC,CAAC,CAAC,CAAC3O,OAAO,IAAI,CAAC2O,OAAO,CAAC,CAAC,CAAC,CAACzI,QAAQ;IAC/C;IACEyI,OAAO,CAAC,CAAC,CAAC,CAACgC,UAAU,IAAI,CAAC/O,WAAW,CAAC+M,OAAO,CAAC,CAAC,CAAC,CAACgC,UAAU,CAACrR,KAAK,CAAC,GAChEsC,WAAW,CAAC+M,OAAO,CAAC,CAAC,CAAC,CAACrP,KAAK,CAAC,IAAIqP,OAAO,CAAC,CAAC,CAAC,CAACrP,KAAK,KAAK,EAAE,GACtDkR,WAAW,GACX;MAAElR,KAAK,EAAEqP,OAAO,CAAC,CAAC,CAAC,CAACrP,KAAK;MAAE4H,OAAO,EAAE;IAAI,CAAE,GAC5CsJ,WAAW,GACbD,aAAa;EAClB;EAED,OAAOA,aAAa;AACtB,CAAC;AC7BD,MAAMK,aAAa,GAAqB;EACtC1J,OAAO,EAAE,KAAK;EACd5H,KAAK,EAAE;CACR;AAED,IAAAuR,aAAA,GAAgBlC,OAA4B,IAC1ChP,KAAK,CAACC,OAAO,CAAC+O,OAAO,CAAC,GAClBA,OAAO,CAACtM,MAAM,CACZ,CAACyO,QAAQ,EAAEJ,MAAM,KACfA,MAAM,IAAIA,MAAM,CAAC1Q,OAAO,IAAI,CAAC0Q,MAAM,CAACxK,QAAQ,GACxC;EACEgB,OAAO,EAAE,IAAI;EACb5H,KAAK,EAAEoR,MAAM,CAACpR;AACf,IACDwR,QAAQ,EACdF,aAAa,CACd,GACDA,aAAa;AClBL,SAAUG,gBAAgBA,CACtC5O,MAAsB,EACtBqI,GAAQ,EACS;EAAA,IAAjBpL,IAAI,GAAAmF,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAzC,SAAA,GAAAyC,SAAA,MAAG,UAAU;EAEjB,IACE4L,SAAS,CAAChO,MAAM,CAAC,IAChBxC,KAAK,CAACC,OAAO,CAACuC,MAAM,CAAC,IAAIA,MAAM,CAAC6O,KAAK,CAACb,SAAS,CAAE,IACjDP,SAAS,CAACzN,MAAM,CAAC,IAAI,CAACA,MAAO,EAC9B;IACA,OAAO;MACL/C,IAAI;MACJyL,OAAO,EAAEsF,SAAS,CAAChO,MAAM,CAAC,GAAGA,MAAM,GAAG,EAAE;MACxCqI;KACD;EACF;AACH;AChBA,IAAAyG,kBAAA,GAAgBC,cAA+B,IAC7CxR,QAAQ,CAACwR,cAAc,CAAC,IAAI,CAACb,OAAO,CAACa,cAAc,CAAC,GAChDA,cAAc,GACd;EACE5R,KAAK,EAAE4R,cAAc;EACrBrG,OAAO,EAAE;CACV;ACoBP,IAAAsG,aAAA,GAAe,MAAAA,CACbnH,KAAY,EACZpC,UAAa,EACb6F,wBAAiC,EACjC2D,yBAAmC,EACnCC,YAAsB,KACU;EAChC,MAAM;IACJ7G,GAAG;IACHwD,IAAI;IACJ1K,QAAQ;IACRH,SAAS;IACTC,SAAS;IACTF,GAAG;IACHD,GAAG;IACHI,OAAO;IACPE,QAAQ;IACRrD,IAAI;IACJoR,aAAa;IACbnH,KAAK;IACLjE;EAAQ,CACT,GAAG8D,KAAK,CAACE,EAAE;EACZ,MAAMqH,UAAU,GAAqBxP,GAAG,CAAC6F,UAAU,EAAE1H,IAAI,CAAC;EAC1D,IAAI,CAACiK,KAAK,IAAIjE,QAAQ,EAAE;IACtB,OAAO,EAAE;EACV;EACD,MAAMsL,QAAQ,GAAqBxD,IAAI,GAAGA,IAAI,CAAC,CAAC,CAAC,GAAIxD,GAAwB;EAC7E,MAAMI,iBAAiB,GAAIC,OAA0B,IAAI;IACvD,IAAIuG,yBAAyB,IAAII,QAAQ,CAAC1G,cAAc,EAAE;MACxD0G,QAAQ,CAAC5G,iBAAiB,CAACgF,SAAS,CAAC/E,OAAO,CAAC,GAAG,EAAE,GAAGA,OAAO,IAAI,EAAE,CAAC;MACnE2G,QAAQ,CAAC1G,cAAc,EAAE;IAC1B;EACH,CAAC;EACD,MAAMM,KAAK,GAAwB,EAAE;EACrC,MAAMqG,OAAO,GAAGrB,YAAY,CAAC5F,GAAG,CAAC;EACjC,MAAMkH,UAAU,GAAGxS,eAAe,CAACsL,GAAG,CAAC;EACvC,MAAMmH,iBAAiB,GAAGF,OAAO,IAAIC,UAAU;EAC/C,MAAME,OAAO,GACV,CAACN,aAAa,IAAIzB,WAAW,CAACrF,GAAG,CAAC,KACjC5I,WAAW,CAAC4I,GAAG,CAAClL,KAAK,CAAC,IACtBsC,WAAW,CAAC2P,UAAU,CAAC,IACxBxB,aAAa,CAACvF,GAAG,CAAC,IAAIA,GAAG,CAAClL,KAAK,KAAK,EAAG,IACxCiS,UAAU,KAAK,EAAE,IAChB5R,KAAK,CAACC,OAAO,CAAC2R,UAAU,CAAC,IAAI,CAACA,UAAU,CAAC/M,MAAO;EACnD,MAAMqN,iBAAiB,GAAGrE,YAAY,CAACsE,IAAI,CACzC,IAAI,EACJ5R,IAAI,EACJuN,wBAAwB,EACxBrC,KAAK,CACN;EACD,MAAM2G,gBAAgB,GAAG,SAAAA,CACvBC,SAAkB,EAClBC,gBAAyB,EACzBC,gBAAyB,EAGvB;IAAA,IAFFC,OAAO,GAAA5N,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAzC,SAAA,GAAAyC,SAAA,MAAGvB,sBAAsB,CAACG,SAAS;IAAA,IAC1CiP,OAAO,GAAA7N,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAzC,SAAA,GAAAyC,SAAA,MAAGvB,sBAAsB,CAACI,SAAS;IAE1C,MAAMyH,OAAO,GAAGmH,SAAS,GAAGC,gBAAgB,GAAGC,gBAAgB;IAC/D9G,KAAK,CAAClL,IAAI,CAAC,GAAG;MACZd,IAAI,EAAE4S,SAAS,GAAGG,OAAO,GAAGC,OAAO;MACnCvH,OAAO;MACPL,GAAG;MACH,GAAGqH,iBAAiB,CAACG,SAAS,GAAGG,OAAO,GAAGC,OAAO,EAAEvH,OAAO;KAC5D;EACH,CAAC;EAED,IACEwG,YAAY,GACR,CAAC1R,KAAK,CAACC,OAAO,CAAC2R,UAAU,CAAC,IAAI,CAACA,UAAU,CAAC/M,MAAM,GAChDlB,QAAQ,KACN,CAACqO,iBAAiB,KAAKC,OAAO,IAAIpS,iBAAiB,CAAC+R,UAAU,CAAC,CAAC,IAC/D3B,SAAS,CAAC2B,UAAU,CAAC,IAAI,CAACA,UAAW,IACrCG,UAAU,IAAI,CAACjB,gBAAgB,CAACzC,IAAI,CAAC,CAAC9G,OAAQ,IAC9CuK,OAAO,IAAI,CAACZ,aAAa,CAAC7C,IAAI,CAAC,CAAC9G,OAAQ,CAAC,EAChD;IACA,MAAM;MAAE5H,KAAK;MAAEuL;IAAO,CAAE,GAAGsF,SAAS,CAAC7M,QAAQ,CAAC,GAC1C;MAAEhE,KAAK,EAAE,CAAC,CAACgE,QAAQ;MAAEuH,OAAO,EAAEvH;IAAQ,CAAE,GACxC2N,kBAAkB,CAAC3N,QAAQ,CAAC;IAEhC,IAAIhE,KAAK,EAAE;MACT8L,KAAK,CAAClL,IAAI,CAAC,GAAG;QACZd,IAAI,EAAE4D,sBAAsB,CAACM,QAAQ;QACrCuH,OAAO;QACPL,GAAG,EAAEgH,QAAQ;QACb,GAAGK,iBAAiB,CAAC7O,sBAAsB,CAACM,QAAQ,EAAEuH,OAAO;OAC9D;MACD,IAAI,CAAC4C,wBAAwB,EAAE;QAC7B7C,iBAAiB,CAACC,OAAO,CAAC;QAC1B,OAAOO,KAAK;MACb;IACF;EACF;EAED,IAAI,CAACwG,OAAO,KAAK,CAACpS,iBAAiB,CAAC0D,GAAG,CAAC,IAAI,CAAC1D,iBAAiB,CAACyD,GAAG,CAAC,CAAC,EAAE;IACpE,IAAI+O,SAAS;IACb,IAAIK,SAAS;IACb,MAAMC,SAAS,GAAGrB,kBAAkB,CAAChO,GAAG,CAAC;IACzC,MAAMsP,SAAS,GAAGtB,kBAAkB,CAAC/N,GAAG,CAAC;IAEzC,IAAI,CAAC1D,iBAAiB,CAAC+R,UAAU,CAAC,IAAI,CAAClI,KAAK,CAACkI,UAAoB,CAAC,EAAE;MAClE,MAAMiB,WAAW,GACdhI,GAAwB,CAAC8G,aAAa,KACtCC,UAAU,GAAG,CAACA,UAAU,GAAGA,UAAU,CAAC;MACzC,IAAI,CAAC/R,iBAAiB,CAAC8S,SAAS,CAAChT,KAAK,CAAC,EAAE;QACvC0S,SAAS,GAAGQ,WAAW,GAAGF,SAAS,CAAChT,KAAK;MAC1C;MACD,IAAI,CAACE,iBAAiB,CAAC+S,SAAS,CAACjT,KAAK,CAAC,EAAE;QACvC+S,SAAS,GAAGG,WAAW,GAAGD,SAAS,CAACjT,KAAK;MAC1C;IACF,OAAM;MACL,MAAMmT,SAAS,GACZjI,GAAwB,CAACkI,WAAW,IAAI,IAAInT,IAAI,CAACgS,UAAoB,CAAC;MACzE,MAAMoB,iBAAiB,GAAIC,IAAa,IACtC,IAAIrT,IAAI,CAAC,IAAIA,IAAI,EAAE,CAACsT,YAAY,EAAE,GAAG,GAAG,GAAGD,IAAI,CAAC;MAClD,MAAME,MAAM,GAAGtI,GAAG,CAACpL,IAAI,IAAI,MAAM;MACjC,MAAM2T,MAAM,GAAGvI,GAAG,CAACpL,IAAI,IAAI,MAAM;MAEjC,IAAIqI,QAAQ,CAAC6K,SAAS,CAAChT,KAAK,CAAC,IAAIiS,UAAU,EAAE;QAC3CS,SAAS,GAAGc,MAAM,GACdH,iBAAiB,CAACpB,UAAU,CAAC,GAAGoB,iBAAiB,CAACL,SAAS,CAAChT,KAAK,CAAC,GAClEyT,MAAM,GACNxB,UAAU,GAAGe,SAAS,CAAChT,KAAK,GAC5BmT,SAAS,GAAG,IAAIlT,IAAI,CAAC+S,SAAS,CAAChT,KAAK,CAAC;MAC1C;MAED,IAAImI,QAAQ,CAAC8K,SAAS,CAACjT,KAAK,CAAC,IAAIiS,UAAU,EAAE;QAC3Cc,SAAS,GAAGS,MAAM,GACdH,iBAAiB,CAACpB,UAAU,CAAC,GAAGoB,iBAAiB,CAACJ,SAAS,CAACjT,KAAK,CAAC,GAClEyT,MAAM,GACNxB,UAAU,GAAGgB,SAAS,CAACjT,KAAK,GAC5BmT,SAAS,GAAG,IAAIlT,IAAI,CAACgT,SAAS,CAACjT,KAAK,CAAC;MAC1C;IACF;IAED,IAAI0S,SAAS,IAAIK,SAAS,EAAE;MAC1BN,gBAAgB,CACd,CAAC,CAACC,SAAS,EACXM,SAAS,CAACzH,OAAO,EACjB0H,SAAS,CAAC1H,OAAO,EACjB7H,sBAAsB,CAACC,GAAG,EAC1BD,sBAAsB,CAACE,GAAG,CAC3B;MACD,IAAI,CAACuK,wBAAwB,EAAE;QAC7B7C,iBAAiB,CAACQ,KAAK,CAAClL,IAAI,CAAE,CAAC2K,OAAO,CAAC;QACvC,OAAOO,KAAK;MACb;IACF;EACF;EAED,IACE,CAACjI,SAAS,IAAIC,SAAS,KACvB,CAACwO,OAAO,KACPnK,QAAQ,CAAC8J,UAAU,CAAC,IAAKF,YAAY,IAAI1R,KAAK,CAACC,OAAO,CAAC2R,UAAU,CAAE,CAAC,EACrE;IACA,MAAMyB,eAAe,GAAG/B,kBAAkB,CAAC9N,SAAS,CAAC;IACrD,MAAM8P,eAAe,GAAGhC,kBAAkB,CAAC7N,SAAS,CAAC;IACrD,MAAM4O,SAAS,GACb,CAACxS,iBAAiB,CAACwT,eAAe,CAAC1T,KAAK,CAAC,IACzCiS,UAAU,CAAC/M,MAAM,GAAG,CAACwO,eAAe,CAAC1T,KAAK;IAC5C,MAAM+S,SAAS,GACb,CAAC7S,iBAAiB,CAACyT,eAAe,CAAC3T,KAAK,CAAC,IACzCiS,UAAU,CAAC/M,MAAM,GAAG,CAACyO,eAAe,CAAC3T,KAAK;IAE5C,IAAI0S,SAAS,IAAIK,SAAS,EAAE;MAC1BN,gBAAgB,CACdC,SAAS,EACTgB,eAAe,CAACnI,OAAO,EACvBoI,eAAe,CAACpI,OAAO,CACxB;MACD,IAAI,CAAC4C,wBAAwB,EAAE;QAC7B7C,iBAAiB,CAACQ,KAAK,CAAClL,IAAI,CAAE,CAAC2K,OAAO,CAAC;QACvC,OAAOO,KAAK;MACb;IACF;EACF;EAED,IAAI/H,OAAO,IAAI,CAACuO,OAAO,IAAInK,QAAQ,CAAC8J,UAAU,CAAC,EAAE;IAC/C,MAAM;MAAEjS,KAAK,EAAE4T,YAAY;MAAErI;IAAO,CAAE,GAAGoG,kBAAkB,CAAC5N,OAAO,CAAC;IAEpE,IAAIgN,OAAO,CAAC6C,YAAY,CAAC,IAAI,CAAC3B,UAAU,CAAC4B,KAAK,CAACD,YAAY,CAAC,EAAE;MAC5D9H,KAAK,CAAClL,IAAI,CAAC,GAAG;QACZd,IAAI,EAAE4D,sBAAsB,CAACK,OAAO;QACpCwH,OAAO;QACPL,GAAG;QACH,GAAGqH,iBAAiB,CAAC7O,sBAAsB,CAACK,OAAO,EAAEwH,OAAO;OAC7D;MACD,IAAI,CAAC4C,wBAAwB,EAAE;QAC7B7C,iBAAiB,CAACC,OAAO,CAAC;QAC1B,OAAOO,KAAK;MACb;IACF;EACF;EAED,IAAI7H,QAAQ,EAAE;IACZ,IAAIuM,UAAU,CAACvM,QAAQ,CAAC,EAAE;MACxB,MAAMpB,MAAM,GAAG,MAAMoB,QAAQ,CAACgO,UAAU,EAAE3J,UAAU,CAAC;MACrD,MAAMwL,aAAa,GAAGrC,gBAAgB,CAAC5O,MAAM,EAAEqP,QAAQ,CAAC;MAExD,IAAI4B,aAAa,EAAE;QACjBhI,KAAK,CAAClL,IAAI,CAAC,GAAG;UACZ,GAAGkT,aAAa;UAChB,GAAGvB,iBAAiB,CAClB7O,sBAAsB,CAACO,QAAQ,EAC/B6P,aAAa,CAACvI,OAAO;SAExB;QACD,IAAI,CAAC4C,wBAAwB,EAAE;UAC7B7C,iBAAiB,CAACwI,aAAa,CAACvI,OAAO,CAAC;UACxC,OAAOO,KAAK;QACb;MACF;IACF,OAAM,IAAI1L,QAAQ,CAAC6D,QAAQ,CAAC,EAAE;MAC7B,IAAI8P,gBAAgB,GAAG,EAAgB;MAEvC,KAAK,MAAM7R,GAAG,IAAI+B,QAAQ,EAAE;QAC1B,IAAI,CAACwB,aAAa,CAACsO,gBAAgB,CAAC,IAAI,CAAC5F,wBAAwB,EAAE;UACjE;QACD;QAED,MAAM2F,aAAa,GAAGrC,gBAAgB,CACpC,MAAMxN,QAAQ,CAAC/B,GAAG,CAAC,CAAC+P,UAAU,EAAE3J,UAAU,CAAC,EAC3C4J,QAAQ,EACRhQ,GAAG,CACJ;QAED,IAAI4R,aAAa,EAAE;UACjBC,gBAAgB,GAAG;YACjB,GAAGD,aAAa;YAChB,GAAGvB,iBAAiB,CAACrQ,GAAG,EAAE4R,aAAa,CAACvI,OAAO;WAChD;UAEDD,iBAAiB,CAACwI,aAAa,CAACvI,OAAO,CAAC;UAExC,IAAI4C,wBAAwB,EAAE;YAC5BrC,KAAK,CAAClL,IAAI,CAAC,GAAGmT,gBAAgB;UAC/B;QACF;MACF;MAED,IAAI,CAACtO,aAAa,CAACsO,gBAAgB,CAAC,EAAE;QACpCjI,KAAK,CAAClL,IAAI,CAAC,GAAG;UACZsK,GAAG,EAAEgH,QAAQ;UACb,GAAG6B;SACJ;QACD,IAAI,CAAC5F,wBAAwB,EAAE;UAC7B,OAAOrC,KAAK;QACb;MACF;IACF;EACF;EAEDR,iBAAiB,CAAC,IAAI,CAAC;EACvB,OAAOQ,KAAK;AACd,CAAC;ACzRa,SAAUwB,MAAMA,CAAIzL,IAAS,EAAE7B,KAAc;EACzD,OAAO,CAAC,GAAG6B,IAAI,EAAE,GAAGkE,qBAAqB,CAAC/F,KAAK,CAAC,CAAC;AACnD;ACJA,IAAAgU,cAAA,GAAmBhU,KAAc,IAC/BK,KAAK,CAACC,OAAO,CAACN,KAAK,CAAC,GAAGA,KAAK,CAAC0I,GAAG,CAAC,MAAMlG,SAAS,CAAC,GAAGA,SAAS;ACOvC,SAAAyR,MAAMA,CAC5BpS,IAAS,EACT6H,KAAa,EACb1J,KAAe;EAEf,OAAO,CACL,GAAG6B,IAAI,CAACsO,KAAK,CAAC,CAAC,EAAEzG,KAAK,CAAC,EACvB,GAAG3D,qBAAqB,CAAC/F,KAAK,CAAC,EAC/B,GAAG6B,IAAI,CAACsO,KAAK,CAACzG,KAAK,CAAC,CACrB;AACH;AChBA,IAAAwK,WAAA,GAAeA,CACbrS,IAAuB,EACvBsS,IAAY,EACZC,EAAU,KACW;EACrB,IAAI,CAAC/T,KAAK,CAACC,OAAO,CAACuB,IAAI,CAAC,EAAE;IACxB,OAAO,EAAE;EACV;EAED,IAAIS,WAAW,CAACT,IAAI,CAACuS,EAAE,CAAC,CAAC,EAAE;IACzBvS,IAAI,CAACuS,EAAE,CAAC,GAAG5R,SAAS;EACrB;EACDX,IAAI,CAACwS,MAAM,CAACD,EAAE,EAAE,CAAC,EAAEvS,IAAI,CAACwS,MAAM,CAACF,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAE3C,OAAOtS,IAAI;AACb,CAAC;ACfa,SAAUyS,OAAOA,CAAIzS,IAAS,EAAE7B,KAAc;EAC1D,OAAO,CAAC,GAAG+F,qBAAqB,CAAC/F,KAAK,CAAC,EAAE,GAAG+F,qBAAqB,CAAClE,IAAI,CAAC,CAAC;AAC1E;ACAA,SAAS0S,eAAeA,CAAI1S,IAAS,EAAE2S,OAAiB;EACtD,IAAIC,CAAC,GAAG,CAAC;EACT,MAAMC,IAAI,GAAG,CAAC,GAAG7S,IAAI,CAAC;EAEtB,KAAK,MAAM6H,KAAK,IAAI8K,OAAO,EAAE;IAC3BE,IAAI,CAACL,MAAM,CAAC3K,KAAK,GAAG+K,CAAC,EAAE,CAAC,CAAC;IACzBA,CAAC,EAAE;EACJ;EAED,OAAOtS,OAAO,CAACuS,IAAI,CAAC,CAACxP,MAAM,GAAGwP,IAAI,GAAG,EAAE;AACzC;AAEA,IAAAC,aAAA,GAAeA,CAAI9S,IAAS,EAAE6H,KAAyB,KACrDpH,WAAW,CAACoH,KAAK,CAAC,GACd,EAAE,GACF6K,eAAe,CACb1S,IAAI,EACHkE,qBAAqB,CAAC2D,KAAK,CAAc,CAACkL,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAAC,CACjE;ACtBP,IAAAC,WAAA,GAAeA,CAAIlT,IAAS,EAAEmT,MAAc,EAAEC,MAAc,KAAU;EACpEpT,IAAI,CAACmT,MAAM,CAAC,GAAG,CAACnT,IAAI,CAACoT,MAAM,CAAC,EAAGpT,IAAI,CAACoT,MAAM,CAAC,GAAGpT,IAAI,CAACmT,MAAM,CAAC,CAAE,CAAC,CAAC,CAAC;AACjE,CAAC;ACID,SAASE,OAAOA,CAACzL,MAAW,EAAE0L,UAA+B;EAC3D,MAAMjQ,MAAM,GAAGiQ,UAAU,CAAChF,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACjL,MAAM;EAC7C,IAAIwE,KAAK,GAAG,CAAC;EAEb,OAAOA,KAAK,GAAGxE,MAAM,EAAE;IACrBuE,MAAM,GAAGnH,WAAW,CAACmH,MAAM,CAAC,GAAGC,KAAK,EAAE,GAAGD,MAAM,CAAC0L,UAAU,CAACzL,KAAK,EAAE,CAAC,CAAC;EACrE;EAED,OAAOD,MAAM;AACf;AAEA,SAAS2L,YAAYA,CAAC1S,GAAc;EAClC,KAAK,MAAMR,GAAG,IAAIQ,GAAG,EAAE;IACrB,IAAIA,GAAG,CAACnB,cAAc,CAACW,GAAG,CAAC,IAAI,CAACI,WAAW,CAACI,GAAG,CAACR,GAAG,CAAC,CAAC,EAAE;MACrD,OAAO,KAAK;IACb;EACF;EACD,OAAO,IAAI;AACb;AAEc,SAAUmT,KAAKA,CAAC5L,MAAW,EAAE9G,IAAkC;EAC3E,MAAM2S,KAAK,GAAGjV,KAAK,CAACC,OAAO,CAACqC,IAAI,CAAC,GAC7BA,IAAI,GACJwG,KAAK,CAACxG,IAAI,CAAC,GACX,CAACA,IAAI,CAAC,GACN0G,YAAY,CAAC1G,IAAI,CAAC;EAEtB,MAAM4S,WAAW,GAAGD,KAAK,CAACpQ,MAAM,KAAK,CAAC,GAAGuE,MAAM,GAAGyL,OAAO,CAACzL,MAAM,EAAE6L,KAAK,CAAC;EAExE,MAAM5L,KAAK,GAAG4L,KAAK,CAACpQ,MAAM,GAAG,CAAC;EAC9B,MAAMhD,GAAG,GAAGoT,KAAK,CAAC5L,KAAK,CAAC;EAExB,IAAI6L,WAAW,EAAE;IACf,OAAOA,WAAW,CAACrT,GAAG,CAAC;EACxB;EAED,IACEwH,KAAK,KAAK,CAAC,KACTtJ,QAAQ,CAACmV,WAAW,CAAC,IAAI9P,aAAa,CAAC8P,WAAW,CAAC,IAClDlV,KAAK,CAACC,OAAO,CAACiV,WAAW,CAAC,IAAIH,YAAY,CAACG,WAAW,CAAE,CAAC,EAC5D;IACAF,KAAK,CAAC5L,MAAM,EAAE6L,KAAK,CAACnF,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAClC;EAED,OAAO1G,MAAM;AACf;ACnDA,IAAA+L,QAAA,GAAeA,CAAIC,WAAgB,EAAE/L,KAAa,EAAE1J,KAAQ,KAAI;EAC9DyV,WAAW,CAAC/L,KAAK,CAAC,GAAG1J,KAAK;EAC1B,OAAOyV,WAAW;AACpB,CAAC;;ACwCD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoCG;AACG,SAAUC,aAAaA,CAK3BlR,KAAkE;EAElE,MAAM0C,OAAO,GAAG7C,cAAc,EAAE;EAChC,MAAM;IACJS,OAAO,GAAGoC,OAAO,CAACpC,OAAO;IACzBlE,IAAI;IACJ+U,OAAO,GAAG,IAAI;IACd1L;EAAgB,CACjB,GAAGzF,KAAK;EACT,MAAM,CAAC8J,MAAM,EAAEsH,SAAS,CAAC,GAAGzR,cAAK,CAACgD,QAAQ,CAACrC,OAAO,CAAC+Q,cAAc,CAACjV,IAAI,CAAC,CAAC;EACxE,MAAMkV,GAAG,GAAG3R,cAAK,CAACqC,MAAM,CACtB1B,OAAO,CAAC+Q,cAAc,CAACjV,IAAI,CAAC,CAAC8H,GAAG,CAACiG,UAAU,CAAC,CAC7C;EACD,MAAMoH,SAAS,GAAG5R,cAAK,CAACqC,MAAM,CAAC8H,MAAM,CAAC;EACtC,MAAMxG,KAAK,GAAG3D,cAAK,CAACqC,MAAM,CAAC5F,IAAI,CAAC;EAChC,MAAMoV,SAAS,GAAG7R,cAAK,CAACqC,MAAM,CAAC,KAAK,CAAC;EAErCsB,KAAK,CAACrB,OAAO,GAAG7F,IAAI;EACpBmV,SAAS,CAACtP,OAAO,GAAG6H,MAAM;EAC1BxJ,OAAO,CAACuD,MAAM,CAAC8B,KAAK,CAAC1B,GAAG,CAAC7H,IAAI,CAAC;EAE9B4D,KAAK,CAAC8F,KAAK,IACRxF,OAAiC,CAACuF,QAAQ,CACzCzJ,IAA+B,EAC/B4D,KAAK,CAAC8F,KAAsC,CAC7C;EAEHhE,YAAY,CAAC;IACXS,IAAI,EAAEkP,IAAA,IAMD;MAAA,IANE;QACLnN,MAAM;QACNlI,IAAI,EAAEsV;MAAc,CAIrB,GAAAD,IAAA;MACC,IAAIC,cAAc,KAAKpO,KAAK,CAACrB,OAAO,IAAI,CAACyP,cAAc,EAAE;QACvD,MAAMT,WAAW,GAAGhT,GAAG,CAACqG,MAAM,EAAEhB,KAAK,CAACrB,OAAO,CAAC;QAC9C,IAAIpG,KAAK,CAACC,OAAO,CAACmV,WAAW,CAAC,EAAE;UAC9BG,SAAS,CAACH,WAAW,CAAC;UACtBK,GAAG,CAACrP,OAAO,GAAGgP,WAAW,CAAC/M,GAAG,CAACiG,UAAU,CAAC;QAC1C;MACF;KACF;IACD9H,OAAO,EAAE/B,OAAO,CAACkD,SAAS,CAACmC;EAC5B,EAAC;EAEF,MAAMgM,YAAY,GAAGhS,cAAK,CAAC8G,WAAW,CAMlCmL,uBAA0B,IACxB;IACFJ,SAAS,CAACvP,OAAO,GAAG,IAAI;IACxB3B,OAAO,CAACuR,iBAAiB,CAACzV,IAAI,EAAEwV,uBAAuB,CAAC;EAC1D,CAAC,EACD,CAACtR,OAAO,EAAElE,IAAI,CAAC,CAChB;EAED,MAAM0V,QAAM,GAAGhJ,CACbtN,KAEwD,EACxDqP,OAA+B,KAC7B;IACF,MAAMkH,WAAW,GAAGxQ,qBAAqB,CAACnE,WAAW,CAAC5B,KAAK,CAAC,CAAC;IAC7D,MAAMoW,uBAAuB,GAAG9I,MAAQ,CACtCxI,OAAO,CAAC+Q,cAAc,CAACjV,IAAI,CAAC,EAC5B2V,WAAW,CACZ;IACDzR,OAAO,CAACuD,MAAM,CAAC+C,KAAK,GAAGgE,iBAAiB,CACtCxO,IAAI,EACJwV,uBAAuB,CAAClR,MAAM,GAAG,CAAC,EAClCmK,OAAO,CACR;IACDyG,GAAG,CAACrP,OAAO,GAAG6G,MAAQ,CAACwI,GAAG,CAACrP,OAAO,EAAE8P,WAAW,CAAC7N,GAAG,CAACiG,UAAU,CAAC,CAAC;IAChEwH,YAAY,CAACC,uBAAuB,CAAC;IACrCR,SAAS,CAACQ,uBAAuB,CAAC;IAClCtR,OAAO,CAACuR,iBAAiB,CAACzV,IAAI,EAAEwV,uBAAuB,EAAE9I,MAAQ,EAAE;MACjEkJ,IAAI,EAAExC,cAAc,CAAChU,KAAK;IAC3B,EAAC;EACJ,CAAC;EAED,MAAMyW,SAAO,GAAGnC,CACdtU,KAEwD,EACxDqP,OAA+B,KAC7B;IACF,MAAMqH,YAAY,GAAG3Q,qBAAqB,CAACnE,WAAW,CAAC5B,KAAK,CAAC,CAAC;IAC9D,MAAMoW,uBAAuB,GAAG9B,OAAS,CACvCxP,OAAO,CAAC+Q,cAAc,CAACjV,IAAI,CAAC,EAC5B8V,YAAY,CACb;IACD5R,OAAO,CAACuD,MAAM,CAAC+C,KAAK,GAAGgE,iBAAiB,CAACxO,IAAI,EAAE,CAAC,EAAEyO,OAAO,CAAC;IAC1DyG,GAAG,CAACrP,OAAO,GAAG6N,OAAS,CAACwB,GAAG,CAACrP,OAAO,EAAEiQ,YAAY,CAAChO,GAAG,CAACiG,UAAU,CAAC,CAAC;IAClEwH,YAAY,CAACC,uBAAuB,CAAC;IACrCR,SAAS,CAACQ,uBAAuB,CAAC;IAClCtR,OAAO,CAACuR,iBAAiB,CAACzV,IAAI,EAAEwV,uBAAuB,EAAE9B,OAAS,EAAE;MAClEkC,IAAI,EAAExC,cAAc,CAAChU,KAAK;IAC3B,EAAC;EACJ,CAAC;EAED,MAAM2W,MAAM,GAAIjN,KAAyB,IAAI;IAC3C,MAAM0M,uBAAuB,GAEvBzB,aAAa,CAAC7P,OAAO,CAAC+Q,cAAc,CAACjV,IAAI,CAAC,EAAE8I,KAAK,CAAC;IACxDoM,GAAG,CAACrP,OAAO,GAAGkO,aAAa,CAACmB,GAAG,CAACrP,OAAO,EAAEiD,KAAK,CAAC;IAC/CyM,YAAY,CAACC,uBAAuB,CAAC;IACrCR,SAAS,CAACQ,uBAAuB,CAAC;IAClCtR,OAAO,CAACuR,iBAAiB,CAACzV,IAAI,EAAEwV,uBAAuB,EAAEzB,aAAa,EAAE;MACtE6B,IAAI,EAAE9M;IACP,EAAC;EACJ,CAAC;EAED,MAAMkN,QAAM,GAAG3C,CACbvK,KAAa,EACb1J,KAEwD,EACxDqP,OAA+B,KAC7B;IACF,MAAMwH,WAAW,GAAG9Q,qBAAqB,CAACnE,WAAW,CAAC5B,KAAK,CAAC,CAAC;IAC7D,MAAMoW,uBAAuB,GAAGnC,MAAQ,CACtCnP,OAAO,CAAC+Q,cAAc,CAACjV,IAAI,CAAC,EAC5B8I,KAAK,EACLmN,WAAW,CACZ;IACD/R,OAAO,CAACuD,MAAM,CAAC+C,KAAK,GAAGgE,iBAAiB,CAACxO,IAAI,EAAE8I,KAAK,EAAE2F,OAAO,CAAC;IAC9DyG,GAAG,CAACrP,OAAO,GAAGwN,MAAQ,CAAC6B,GAAG,CAACrP,OAAO,EAAEiD,KAAK,EAAEmN,WAAW,CAACnO,GAAG,CAACiG,UAAU,CAAC,CAAC;IACvEwH,YAAY,CAACC,uBAAuB,CAAC;IACrCR,SAAS,CAACQ,uBAAuB,CAAC;IAClCtR,OAAO,CAACuR,iBAAiB,CAACzV,IAAI,EAAEwV,uBAAuB,EAAEnC,MAAQ,EAAE;MACjEuC,IAAI,EAAE9M,KAAK;MACXoN,IAAI,EAAE9C,cAAc,CAAChU,KAAK;IAC3B,EAAC;EACJ,CAAC;EAED,MAAM+W,IAAI,GAAGA,CAAC/B,MAAc,EAAEC,MAAc,KAAI;IAC9C,MAAMmB,uBAAuB,GAAGtR,OAAO,CAAC+Q,cAAc,CAACjV,IAAI,CAAC;IAC5DmU,WAAW,CAACqB,uBAAuB,EAAEpB,MAAM,EAAEC,MAAM,CAAC;IACpDF,WAAW,CAACe,GAAG,CAACrP,OAAO,EAAEuO,MAAM,EAAEC,MAAM,CAAC;IACxCkB,YAAY,CAACC,uBAAuB,CAAC;IACrCR,SAAS,CAACQ,uBAAuB,CAAC;IAClCtR,OAAO,CAACuR,iBAAiB,CACvBzV,IAAI,EACJwV,uBAAuB,EACvBrB,WAAW,EACX;MACEyB,IAAI,EAAExB,MAAM;MACZ8B,IAAI,EAAE7B;KACP,EACD,KAAK,CACN;EACH,CAAC;EAED,MAAM+B,IAAI,GAAGA,CAAC7C,IAAY,EAAEC,EAAU,KAAI;IACxC,MAAMgC,uBAAuB,GAAGtR,OAAO,CAAC+Q,cAAc,CAACjV,IAAI,CAAC;IAC5DsT,WAAW,CAACkC,uBAAuB,EAAEjC,IAAI,EAAEC,EAAE,CAAC;IAC9CF,WAAW,CAAC4B,GAAG,CAACrP,OAAO,EAAE0N,IAAI,EAAEC,EAAE,CAAC;IAClC+B,YAAY,CAACC,uBAAuB,CAAC;IACrCR,SAAS,CAACQ,uBAAuB,CAAC;IAClCtR,OAAO,CAACuR,iBAAiB,CACvBzV,IAAI,EACJwV,uBAAuB,EACvBlC,WAAW,EACX;MACEsC,IAAI,EAAErC,IAAI;MACV2C,IAAI,EAAE1C;KACP,EACD,KAAK,CACN;EACH,CAAC;EAED,MAAM6C,MAAM,GAAGA,CACbvN,KAAa,EACb1J,KAAgD,KAC9C;IACF,MAAM+I,WAAW,GAAGnH,WAAW,CAAC5B,KAAK,CAAC;IACtC,MAAMoW,uBAAuB,GAAGZ,QAAQ,CACtC1Q,OAAO,CAAC+Q,cAAc,CAEpBjV,IAAI,CAAC,EACP8I,KAAK,EACLX,WAAwE,CACzE;IACD+M,GAAG,CAACrP,OAAO,GAAG,CAAC,GAAG2P,uBAAuB,CAAC,CAAC1N,GAAG,CAAC,CAACwO,IAAI,EAAEzC,CAAC,KACrD,CAACyC,IAAI,IAAIzC,CAAC,KAAK/K,KAAK,GAAGiF,UAAU,EAAE,GAAGmH,GAAG,CAACrP,OAAO,CAACgO,CAAC,CAAC,CACrD;IACD0B,YAAY,CAACC,uBAAuB,CAAC;IACrCR,SAAS,CAAC,CAAC,GAAGQ,uBAAuB,CAAC,CAAC;IACvCtR,OAAO,CAACuR,iBAAiB,CACvBzV,IAAI,EACJwV,uBAAuB,EACvBZ,QAAQ,EACR;MACEgB,IAAI,EAAE9M,KAAK;MACXoN,IAAI,EAAE/N;IACP,GACD,IAAI,EACJ,KAAK,CACN;EACH,CAAC;EAED,MAAMQ,OAAO,GACXvJ,KAEwD,IACtD;IACF,MAAMoW,uBAAuB,GAAGrQ,qBAAqB,CAACnE,WAAW,CAAC5B,KAAK,CAAC,CAAC;IACzE8V,GAAG,CAACrP,OAAO,GAAG2P,uBAAuB,CAAC1N,GAAG,CAACiG,UAAU,CAAC;IACrDwH,YAAY,CAAC,CAAC,GAAGC,uBAAuB,CAAC,CAAC;IAC1CR,SAAS,CAAC,CAAC,GAAGQ,uBAAuB,CAAC,CAAC;IACvCtR,OAAO,CAACuR,iBAAiB,CACvBzV,IAAI,EACJ,CAAC,GAAGwV,uBAAuB,CAAC,EACxBvU,IAAO,IAAQA,IAAI,EACvB,EAAE,EACF,IAAI,EACJ,KAAK,CACN;EACH,CAAC;EAEDsC,cAAK,CAACuC,SAAS,CAAC,MAAK;IACnB5B,OAAO,CAACgG,MAAM,CAACC,MAAM,GAAG,KAAK;IAE7BiF,SAAS,CAACpP,IAAI,EAAEkE,OAAO,CAACuD,MAAM,CAAC,IAC7BvD,OAAO,CAACkD,SAAS,CAACC,KAAK,CAAClB,IAAI,CAAC;MAC3B,GAAGjC,OAAO,CAACsC;IACe,EAAC;IAE/B,IACE4O,SAAS,CAACvP,OAAO,KAChB,CAACgJ,kBAAkB,CAAC3K,OAAO,CAAC0F,QAAQ,CAACkF,IAAI,CAAC,CAACC,UAAU,IACpD7K,OAAO,CAACsC,UAAU,CAAC+P,WAAW,CAAC,EACjC;MACA,IAAIrS,OAAO,CAAC0F,QAAQ,CAAC4M,QAAQ,EAAE;QAC7BtS,OAAO,CAACuS,cAAc,CAAC,CAACzW,IAAI,CAAC,CAAC,CAAC0W,IAAI,CAAEzU,MAAM,IAAI;UAC7C,MAAMiJ,KAAK,GAAGrJ,GAAG,CAACI,MAAM,CAACgF,MAAM,EAAEjH,IAAI,CAAC;UACtC,MAAM2W,aAAa,GAAG9U,GAAG,CAACqC,OAAO,CAACsC,UAAU,CAACS,MAAM,EAAEjH,IAAI,CAAC;UAE1D,IACE2W,aAAa,GACR,CAACzL,KAAK,IAAIyL,aAAa,CAACzX,IAAI,IAC5BgM,KAAK,KACHyL,aAAa,CAACzX,IAAI,KAAKgM,KAAK,CAAChM,IAAI,IAChCyX,aAAa,CAAChM,OAAO,KAAKO,KAAK,CAACP,OAAO,CAAE,GAC7CO,KAAK,IAAIA,KAAK,CAAChM,IAAI,EACvB;YACAgM,KAAK,GACDtC,GAAG,CAAC1E,OAAO,CAACsC,UAAU,CAACS,MAAM,EAAEjH,IAAI,EAAEkL,KAAK,CAAC,GAC3CuJ,KAAK,CAACvQ,OAAO,CAACsC,UAAU,CAACS,MAAM,EAAEjH,IAAI,CAAC;YAC1CkE,OAAO,CAACkD,SAAS,CAACC,KAAK,CAAClB,IAAI,CAAC;cAC3Bc,MAAM,EAAE/C,OAAO,CAACsC,UAAU,CAACS;YAC5B,EAAC;UACH;QACH,CAAC,CAAC;MACH,OAAM;QACL,MAAM6C,KAAK,GAAUjI,GAAG,CAACqC,OAAO,CAAC6F,OAAO,EAAE/J,IAAI,CAAC;QAC/C,IAAI8J,KAAK,IAAIA,KAAK,CAACE,EAAE,EAAE;UACrBiH,aAAa,CACXnH,KAAK,EACL5F,OAAO,CAACkE,WAAW,EACnBlE,OAAO,CAAC0F,QAAQ,CAACgN,YAAY,KAAKpU,eAAe,CAACK,GAAG,EACrDqB,OAAO,CAAC0F,QAAQ,CAACsH,yBAAyB,EAC1C,IAAI,CACL,CAACwF,IAAI,CACHxL,KAAK,IACJ,CAACrG,aAAa,CAACqG,KAAK,CAAC,IACrBhH,OAAO,CAACkD,SAAS,CAACC,KAAK,CAAClB,IAAI,CAAC;YAC3Bc,MAAM,EAAEuI,yBAAyB,CAC/BtL,OAAO,CAACsC,UAAU,CAACS,MAAmC,EACtDiE,KAAK,EACLlL,IAAI;UAEP,EAAC,CACL;QACF;MACF;IACF;IAEDkE,OAAO,CAACkD,SAAS,CAACc,MAAM,CAAC/B,IAAI,CAAC;MAC5BnG,IAAI;MACJkI,MAAM,EAAE;QAAE,GAAGhE,OAAO,CAACkE;MAAW;IACjC,EAAC;IAEFlE,OAAO,CAACuD,MAAM,CAAC+C,KAAK,IAClBiD,YAAY,CACVvJ,OAAO,CAAC6F,OAAO,EACdzI,GAAG,IAAK,CAAC,CAACA,GAAG,IAAIA,GAAG,CAACmE,UAAU,CAACvB,OAAO,CAACuD,MAAM,CAAC+C,KAAK,IAAI,EAAE,CAAC,CAC7D;IAEHtG,OAAO,CAACuD,MAAM,CAAC+C,KAAK,GAAG,EAAE;IAEzBtG,OAAO,CAACoD,YAAY,EAAE;GACvB,EAAE,CAACoG,MAAM,EAAE1N,IAAI,EAAEkE,OAAO,CAAC,CAAC;EAE3BX,cAAK,CAACuC,SAAS,CAAC,MAAK;IACnB,CAACjE,GAAG,CAACqC,OAAO,CAACkE,WAAW,EAAEpI,IAAI,CAAC,IAAIkE,OAAO,CAACuR,iBAAiB,CAACzV,IAAI,CAAC;IAElE,OAAO,MAAK;MACV,CAACkE,OAAO,CAAC0F,QAAQ,CAACP,gBAAgB,IAAIA,gBAAgB,KACpDnF,OAAO,CAACkG,UAAU,CAACpK,IAA+B,CAAC;IACvD,CAAC;GACF,EAAE,CAACA,IAAI,EAAEkE,OAAO,EAAE6Q,OAAO,EAAE1L,gBAAgB,CAAC,CAAC;EAE9C,OAAO;IACL8M,IAAI,EAAE5S,cAAK,CAAC8G,WAAW,CAAC8L,IAAI,EAAE,CAACZ,YAAY,EAAEvV,IAAI,EAAEkE,OAAO,CAAC,CAAC;IAC5DkS,IAAI,EAAE7S,cAAK,CAAC8G,WAAW,CAAC+L,IAAI,EAAE,CAACb,YAAY,EAAEvV,IAAI,EAAEkE,OAAO,CAAC,CAAC;IAC5DwP,OAAO,EAAEnQ,cAAK,CAAC8G,WAAW,CAACwL,SAAO,EAAE,CAACN,YAAY,EAAEvV,IAAI,EAAEkE,OAAO,CAAC,CAAC;IAClEwI,MAAM,EAAEnJ,cAAK,CAAC8G,WAAW,CAACqL,QAAM,EAAE,CAACH,YAAY,EAAEvV,IAAI,EAAEkE,OAAO,CAAC,CAAC;IAChE6R,MAAM,EAAExS,cAAK,CAAC8G,WAAW,CAAC0L,MAAM,EAAE,CAACR,YAAY,EAAEvV,IAAI,EAAEkE,OAAO,CAAC,CAAC;IAChEmP,MAAM,EAAE9P,cAAK,CAAC8G,WAAW,CAAC2L,QAAM,EAAE,CAACT,YAAY,EAAEvV,IAAI,EAAEkE,OAAO,CAAC,CAAC;IAChEmS,MAAM,EAAE9S,cAAK,CAAC8G,WAAW,CAACgM,MAAM,EAAE,CAACd,YAAY,EAAEvV,IAAI,EAAEkE,OAAO,CAAC,CAAC;IAChEyE,OAAO,EAAEpF,cAAK,CAAC8G,WAAW,CAAC1B,OAAO,EAAE,CAAC4M,YAAY,EAAEvV,IAAI,EAAEkE,OAAO,CAAC,CAAC;IAClEwJ,MAAM,EAAEnK,cAAK,CAACsT,OAAO,CACnB,MACEnJ,MAAM,CAAC5F,GAAG,CAAC,CAACgC,KAAK,EAAEhB,KAAK,MAAM;MAC5B,GAAGgB,KAAK;MACR,CAACiL,OAAO,GAAGG,GAAG,CAACrP,OAAO,CAACiD,KAAK,CAAC,IAAIiF,UAAU;IAC5C,EAAC,CAAgE,EACpE,CAACL,MAAM,EAAEqH,OAAO,CAAC;GAEpB;AACH;AC1Yc,SAAU+B,aAAaA,CAAA;EACnC,IAAIC,UAAU,GAAkB,EAAE;EAElC,MAAM5Q,IAAI,GAAI/G,KAAQ,IAAI;IACxB,KAAK,MAAM4X,QAAQ,IAAID,UAAU,EAAE;MACjCC,QAAQ,CAAC7Q,IAAI,IAAI6Q,QAAQ,CAAC7Q,IAAI,CAAC/G,KAAK,CAAC;IACtC;EACH,CAAC;EAED,MAAM8G,SAAS,GAAI8Q,QAAqB,IAAkB;IACxDD,UAAU,CAACE,IAAI,CAACD,QAAQ,CAAC;IACzB,OAAO;MACL5Q,WAAW,EAAEA,CAAA,KAAK;QAChB2Q,UAAU,GAAGA,UAAU,CAACvV,MAAM,CAAE0V,CAAC,IAAKA,CAAC,KAAKF,QAAQ,CAAC;;KAExD;EACH,CAAC;EAED,MAAM5Q,WAAW,GAAGA,CAAA,KAAK;IACvB2Q,UAAU,GAAG,EAAE;EACjB,CAAC;EAED,OAAO;IACL,IAAII,SAASA,CAAA;MACX,OAAOJ,UAAU;KAClB;IACD5Q,IAAI;IACJD,SAAS;IACTE;GACD;AACH;ACzCA,IAAAgR,WAAA,GAAgBhY,KAAc,IAC5BE,iBAAiB,CAACF,KAAK,CAAC,IAAI,CAACG,YAAY,CAACH,KAAK,CAAC;ACDpC,SAAUiY,SAASA,CAACC,OAAY,EAAEC,OAAY;EAC1D,IAAIH,WAAW,CAACE,OAAO,CAAC,IAAIF,WAAW,CAACG,OAAO,CAAC,EAAE;IAChD,OAAOD,OAAO,KAAKC,OAAO;EAC3B;EAED,IAAIpY,YAAY,CAACmY,OAAO,CAAC,IAAInY,YAAY,CAACoY,OAAO,CAAC,EAAE;IAClD,OAAOD,OAAO,CAACE,OAAO,EAAE,KAAKD,OAAO,CAACC,OAAO,EAAE;EAC/C;EAED,MAAMC,KAAK,GAAGhT,MAAM,CAACK,IAAI,CAACwS,OAAO,CAAC;EAClC,MAAMI,KAAK,GAAGjT,MAAM,CAACK,IAAI,CAACyS,OAAO,CAAC;EAElC,IAAIE,KAAK,CAACnT,MAAM,KAAKoT,KAAK,CAACpT,MAAM,EAAE;IACjC,OAAO,KAAK;EACb;EAED,KAAK,MAAMhD,GAAG,IAAImW,KAAK,EAAE;IACvB,MAAME,IAAI,GAAGL,OAAO,CAAChW,GAAG,CAAC;IAEzB,IAAI,CAACoW,KAAK,CAAC9K,QAAQ,CAACtL,GAAG,CAAC,EAAE;MACxB,OAAO,KAAK;IACb;IAED,IAAIA,GAAG,KAAK,KAAK,EAAE;MACjB,MAAMsW,IAAI,GAAGL,OAAO,CAACjW,GAAG,CAAC;MAEzB,IACGnC,YAAY,CAACwY,IAAI,CAAC,IAAIxY,YAAY,CAACyY,IAAI,CAAC,IACxCpY,QAAQ,CAACmY,IAAI,CAAC,IAAInY,QAAQ,CAACoY,IAAI,CAAE,IACjCnY,KAAK,CAACC,OAAO,CAACiY,IAAI,CAAC,IAAIlY,KAAK,CAACC,OAAO,CAACkY,IAAI,CAAE,GACxC,CAACP,SAAS,CAACM,IAAI,EAAEC,IAAI,CAAC,GACtBD,IAAI,KAAKC,IAAI,EACjB;QACA,OAAO,KAAK;MACb;IACF;EACF;EAED,OAAO,IAAI;AACb;AC1CA,IAAAC,gBAAA,GAAgB5Y,OAAqB,IACnCA,OAAO,CAACC,IAAI,KAAK,iBAAiB;ACEpC,IAAAuS,iBAAA,GAAgBnH,GAAiB,IAC/B4F,YAAY,CAAC5F,GAAG,CAAC,IAAItL,eAAe,CAACsL,GAAG,CAAC;ACF3C,IAAAwN,IAAA,GAAgBxN,GAAQ,IAAKuF,aAAa,CAACvF,GAAG,CAAC,IAAIA,GAAG,CAACyN,WAAW;ACFlE,IAAAC,iBAAA,GAAmB/W,IAAO,IAAa;EACrC,KAAK,MAAMK,GAAG,IAAIL,IAAI,EAAE;IACtB,IAAI2O,UAAU,CAAC3O,IAAI,CAACK,GAAG,CAAC,CAAC,EAAE;MACzB,OAAO,IAAI;IACZ;EACF;EACD,OAAO,KAAK;AACd,CAAC;ACFD,SAAS2W,eAAeA,CAAIhX,IAAO,EAAkC;EAAA,IAAhCyM,MAAA,GAAArJ,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAzC,SAAA,GAAAyC,SAAA,MAA8B,EAAE;EACnE,MAAM6T,iBAAiB,GAAGzY,KAAK,CAACC,OAAO,CAACuB,IAAI,CAAC;EAE7C,IAAIzB,QAAQ,CAACyB,IAAI,CAAC,IAAIiX,iBAAiB,EAAE;IACvC,KAAK,MAAM5W,GAAG,IAAIL,IAAI,EAAE;MACtB,IACExB,KAAK,CAACC,OAAO,CAACuB,IAAI,CAACK,GAAG,CAAC,CAAC,IACvB9B,QAAQ,CAACyB,IAAI,CAACK,GAAG,CAAC,CAAC,IAAI,CAAC0W,iBAAiB,CAAC/W,IAAI,CAACK,GAAG,CAAC,CAAE,EACtD;QACAoM,MAAM,CAACpM,GAAG,CAAC,GAAG7B,KAAK,CAACC,OAAO,CAACuB,IAAI,CAACK,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE;QAChD2W,eAAe,CAAChX,IAAI,CAACK,GAAG,CAAC,EAAEoM,MAAM,CAACpM,GAAG,CAAC,CAAC;MACxC,OAAM,IAAI,CAAChC,iBAAiB,CAAC2B,IAAI,CAACK,GAAG,CAAC,CAAC,EAAE;QACxCoM,MAAM,CAACpM,GAAG,CAAC,GAAG,IAAI;MACnB;IACF;EACF;EAED,OAAOoM,MAAM;AACf;AAEA,SAASyK,+BAA+BA,CACtClX,IAAO,EACPyG,UAAa,EACb0Q,qBAA0B;EAE1B,MAAMF,iBAAiB,GAAGzY,KAAK,CAACC,OAAO,CAACuB,IAAI,CAAC;EAE7C,IAAIzB,QAAQ,CAACyB,IAAI,CAAC,IAAIiX,iBAAiB,EAAE;IACvC,KAAK,MAAM5W,GAAG,IAAIL,IAAI,EAAE;MACtB,IACExB,KAAK,CAACC,OAAO,CAACuB,IAAI,CAACK,GAAG,CAAC,CAAC,IACvB9B,QAAQ,CAACyB,IAAI,CAACK,GAAG,CAAC,CAAC,IAAI,CAAC0W,iBAAiB,CAAC/W,IAAI,CAACK,GAAG,CAAC,CAAE,EACtD;QACA,IACEI,WAAW,CAACgG,UAAU,CAAC,IACvB0P,WAAW,CAACgB,qBAAqB,CAAC9W,GAAG,CAAC,CAAC,EACvC;UACA8W,qBAAqB,CAAC9W,GAAG,CAAC,GAAG7B,KAAK,CAACC,OAAO,CAACuB,IAAI,CAACK,GAAG,CAAC,CAAC,GACjD2W,eAAe,CAAChX,IAAI,CAACK,GAAG,CAAC,EAAE,EAAE,CAAC,GAC9B;YAAE,GAAG2W,eAAe,CAAChX,IAAI,CAACK,GAAG,CAAC;UAAC,CAAE;QACtC,OAAM;UACL6W,+BAA+B,CAC7BlX,IAAI,CAACK,GAAG,CAAC,EACThC,iBAAiB,CAACoI,UAAU,CAAC,GAAG,EAAE,GAAGA,UAAU,CAACpG,GAAG,CAAC,EACpD8W,qBAAqB,CAAC9W,GAAG,CAAC,CAC3B;QACF;MACF,OAAM;QACL8W,qBAAqB,CAAC9W,GAAG,CAAC,GAAG,CAAC+V,SAAS,CAACpW,IAAI,CAACK,GAAG,CAAC,EAAEoG,UAAU,CAACpG,GAAG,CAAC,CAAC;MACpE;IACF;EACF;EAED,OAAO8W,qBAAqB;AAC9B;AAEA,IAAAC,cAAA,GAAeA,CAAI9T,aAAgB,EAAEmD,UAAa,KAChDyQ,+BAA+B,CAC7B5T,aAAa,EACbmD,UAAU,EACVuQ,eAAe,CAACvQ,UAAU,CAAC,CAC5B;AChEH,IAAA4Q,eAAA,GAAeA,CACblZ,KAAQ,EAAAmZ,KAAA;EAAA,IACR;IAAEnH,aAAa;IAAEoB,WAAW;IAAEgG;EAAU,CAAe,GAAAD,KAAA;EAAA,OAEvD7W,WAAW,CAACtC,KAAK,CAAC,GACdA,KAAK,GACLgS,aAAa,GACbhS,KAAK,KAAK,EAAE,GACVqZ,GAAG,GACHrZ,KAAK,GACL,CAACA,KAAK,GACNA,KAAK,GACPoT,WAAW,IAAIjL,QAAQ,CAACnI,KAAK,CAAC,GAC9B,IAAIC,IAAI,CAACD,KAAK,CAAC,GACfoZ,UAAU,GACVA,UAAU,CAACpZ,KAAK,CAAC,GACjBA,KAAK;AAAA;ACTa,SAAAsZ,aAAaA,CAAC1O,EAAe;EACnD,MAAMM,GAAG,GAAGN,EAAE,CAACM,GAAG;EAElB,IAAIN,EAAE,CAAC8D,IAAI,GAAG9D,EAAE,CAAC8D,IAAI,CAACgD,KAAK,CAAExG,GAAG,IAAKA,GAAG,CAACtE,QAAQ,CAAC,GAAGsE,GAAG,CAACtE,QAAQ,EAAE;IACjE;EACD;EAED,IAAI2J,WAAW,CAACrF,GAAG,CAAC,EAAE;IACpB,OAAOA,GAAG,CAACqO,KAAK;EACjB;EAED,IAAIzI,YAAY,CAAC5F,GAAG,CAAC,EAAE;IACrB,OAAOqG,aAAa,CAAC3G,EAAE,CAAC8D,IAAI,CAAC,CAAC1O,KAAK;EACpC;EAED,IAAIyY,gBAAgB,CAACvN,GAAG,CAAC,EAAE;IACzB,OAAO,CAAC,GAAGA,GAAG,CAACsO,eAAe,CAAC,CAAC9Q,GAAG,CAAC+Q,KAAA;MAAA,IAAC;QAAEzZ;MAAK,CAAE,GAAAyZ,KAAA;MAAA,OAAKzZ,KAAK;IAAA,EAAC;EAC1D;EAED,IAAIJ,eAAU,CAACsL,GAAG,CAAC,EAAE;IACnB,OAAOiG,gBAAgB,CAACvG,EAAE,CAAC8D,IAAI,CAAC,CAAC1O,KAAK;EACvC;EAED,OAAOkZ,eAAe,CAAC5W,WAAW,CAAC4I,GAAG,CAAClL,KAAK,CAAC,GAAG4K,EAAE,CAACM,GAAG,CAAClL,KAAK,GAAGkL,GAAG,CAAClL,KAAK,EAAE4K,EAAE,CAAC;AAC/E;ACxBA,IAAA8O,kBAAA,GAAeA,CACblL,WAAyD,EACzD7D,OAAkB,EAClB6M,YAA2B,EAC3B1F,yBAA+C,KAC7C;EACF,MAAMxD,MAAM,GAA2C,EAAE;EAEzD,KAAK,MAAM1N,IAAI,IAAI4N,WAAW,EAAE;IAC9B,MAAM9D,KAAK,GAAUjI,GAAG,CAACkI,OAAO,EAAE/J,IAAI,CAAC;IAEvC8J,KAAK,IAAIlB,GAAG,CAAC8E,MAAM,EAAE1N,IAAI,EAAE8J,KAAK,CAACE,EAAE,CAAC;EACrC;EAED,OAAO;IACL4M,YAAY;IACZxW,KAAK,EAAE,CAAC,GAAGwN,WAAW,CAA8B;IACpDF,MAAM;IACNwD;GACD;AACH,CAAC;ACtBD,IAAA6H,YAAA,GACEC,IAAoD,IAEpDtX,WAAW,CAACsX,IAAI,CAAC,GACbA,IAAI,GACJ7I,OAAO,CAAC6I,IAAI,CAAC,GACbA,IAAI,CAACC,MAAM,GACXzZ,QAAQ,CAACwZ,IAAI,CAAC,GACd7I,OAAO,CAAC6I,IAAI,CAAC5Z,KAAK,CAAC,GACjB4Z,IAAI,CAAC5Z,KAAK,CAAC6Z,MAAM,GACjBD,IAAI,CAAC5Z,KAAK,GACZ4Z,IAAI;AClBV,IAAAE,aAAA,GAAgBzK,OAAoB,IAClCA,OAAO,CAACxE,KAAK,KACZwE,OAAO,CAACrL,QAAQ,IACfqL,OAAO,CAACzL,GAAG,IACXyL,OAAO,CAAC1L,GAAG,IACX0L,OAAO,CAACxL,SAAS,IACjBwL,OAAO,CAACvL,SAAS,IACjBuL,OAAO,CAACtL,OAAO,IACfsL,OAAO,CAACpL,QAAQ,CAAC;ACNG,SAAA8V,iBAAiBA,CACvClS,MAAsB,EACtB8C,OAAoB,EACpB/J,IAAY;EAKZ,MAAMkL,KAAK,GAAGrJ,GAAG,CAACoF,MAAM,EAAEjH,IAAI,CAAC;EAE/B,IAAIkL,KAAK,IAAI3C,KAAK,CAACvI,IAAI,CAAC,EAAE;IACxB,OAAO;MACLkL,KAAK;MACLlL;KACD;EACF;EAED,MAAMI,KAAK,GAAGJ,IAAI,CAACkC,KAAK,CAAC,GAAG,CAAC;EAE7B,OAAO9B,KAAK,CAACkE,MAAM,EAAE;IACnB,MAAMyD,SAAS,GAAG3H,KAAK,CAACgZ,IAAI,CAAC,GAAG,CAAC;IACjC,MAAMtP,KAAK,GAAGjI,GAAG,CAACkI,OAAO,EAAEhC,SAAS,CAAC;IACrC,MAAMsR,UAAU,GAAGxX,GAAG,CAACoF,MAAM,EAAEc,SAAS,CAAC;IAEzC,IAAI+B,KAAK,IAAI,CAACrK,KAAK,CAACC,OAAO,CAACoK,KAAK,CAAC,IAAI9J,IAAI,KAAK+H,SAAS,EAAE;MACxD,OAAO;QAAE/H;MAAI,CAAE;IAChB;IAED,IAAIqZ,UAAU,IAAIA,UAAU,CAACna,IAAI,EAAE;MACjC,OAAO;QACLc,IAAI,EAAE+H,SAAS;QACfmD,KAAK,EAAEmO;OACR;IACF;IAEDjZ,KAAK,CAACkZ,GAAG,EAAE;EACZ;EAED,OAAO;IACLtZ;GACD;AACH;AC7CA,IAAAuZ,cAAA,GAAeA,CACblK,WAAoB,EACpBpE,SAAkB,EAClBsL,WAAoB,EACpBiD,cAGC,EACD1K,IAME,KACA;EACF,IAAIA,IAAI,CAACI,OAAO,EAAE;IAChB,OAAO,KAAK;EACb,OAAM,IAAI,CAACqH,WAAW,IAAIzH,IAAI,CAACK,SAAS,EAAE;IACzC,OAAO,EAAElE,SAAS,IAAIoE,WAAW,CAAC;EACnC,OAAM,IAAIkH,WAAW,GAAGiD,cAAc,CAACxK,QAAQ,GAAGF,IAAI,CAACE,QAAQ,EAAE;IAChE,OAAO,CAACK,WAAW;EACpB,OAAM,IAAIkH,WAAW,GAAGiD,cAAc,CAACvK,UAAU,GAAGH,IAAI,CAACG,UAAU,EAAE;IACpE,OAAOI,WAAW;EACnB;EACD,OAAO,IAAI;AACb,CAAC;ACtBD,IAAAoK,eAAA,GAAeA,CAAInP,GAAM,EAAEtK,IAAY,KACrC,CAACuB,OAAO,CAACM,GAAG,CAACyI,GAAG,EAAEtK,IAAI,CAAC,CAAC,CAACsE,MAAM,IAAImQ,KAAK,CAACnK,GAAG,EAAEtK,IAAI,CAAC;AC+ErD,MAAM0Z,cAAc,GAAG;EACrB5K,IAAI,EAAEtM,eAAe,CAACG,QAAQ;EAC9B6W,cAAc,EAAEhX,eAAe,CAACE,QAAQ;EACxCiX,gBAAgB,EAAE;CACV;SAEMC,iBAAiBA,CAAA,EAKJ;EAAA,IAD3BhW,KAA8C,GAAAS,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAzC,SAAA,GAAAyC,SAAA,QAAE;EAAA,IAChDwV,eAA2B,GAAAxV,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAzC,SAAA;EAE3B,IAAIgI,QAAQ,GAAG;IACb,GAAG8P,cAAc;IACjB,GAAG9V;GACJ;EACD,IAAI4C,UAAU,GAA4B;IACxCsT,WAAW,EAAE,CAAC;IACdnT,OAAO,EAAE,KAAK;IACdC,SAAS,EAAEgJ,UAAU,CAAChG,QAAQ,CAACrF,aAAa,CAAC;IAC7CwC,YAAY,EAAE,KAAK;IACnBwP,WAAW,EAAE,KAAK;IAClBwD,YAAY,EAAE,KAAK;IACnB7M,kBAAkB,EAAE,KAAK;IACzBlG,OAAO,EAAE,KAAK;IACdF,aAAa,EAAE,EAAE;IACjBD,WAAW,EAAE,EAAE;IACfI,MAAM,EAAE;GACT;EACD,IAAI8C,OAAO,GAAG,EAAE;EAChB,IAAIvF,cAAc,GAChBhF,QAAQ,CAACoK,QAAQ,CAACrF,aAAa,CAAC,IAAI/E,QAAQ,CAACoK,QAAQ,CAAC1B,MAAM,CAAC,GACzDlH,WAAW,CAAC4I,QAAQ,CAACrF,aAAa,IAAIqF,QAAQ,CAAC1B,MAAM,CAAC,IAAI,EAAE,GAC5D,EAAE;EACR,IAAIE,WAAW,GAAGwB,QAAQ,CAACP,gBAAgB,GACvC,EAAE,GACFrI,WAAW,CAACwD,cAAc,CAAC;EAC/B,IAAI0F,MAAM,GAAG;IACXC,MAAM,EAAE,KAAK;IACbF,KAAK,EAAE,KAAK;IACZrC,KAAK,EAAE;GACR;EACD,IAAIH,MAAM,GAAU;IAClBwC,KAAK,EAAE,IAAI9I,GAAG,EAAE;IAChB6Y,OAAO,EAAE,IAAI7Y,GAAG,EAAE;IAClBoI,KAAK,EAAE,IAAIpI,GAAG,EAAE;IAChByG,KAAK,EAAE,IAAIzG,GAAG;GACf;EACD,IAAI8Y,kBAAwC;EAC5C,IAAIC,KAAK,GAAG,CAAC;EACb,MAAMtV,eAAe,GAAG;IACtB+B,OAAO,EAAE,KAAK;IACdE,WAAW,EAAE,KAAK;IAClBC,aAAa,EAAE,KAAK;IACpBC,YAAY,EAAE,KAAK;IACnBC,OAAO,EAAE,KAAK;IACdC,MAAM,EAAE;GACT;EACD,MAAMG,SAAS,GAA2B;IACxCc,MAAM,EAAE4O,aAAa,EAAE;IACvBvN,KAAK,EAAEuN,aAAa,EAAE;IACtBzP,KAAK,EAAEyP,aAAa;GACrB;EACD,MAAMqD,wBAAwB,GAC5BvW,KAAK,CAACwW,YAAY,IAAIxW,KAAK,CAACwW,YAAY,CAACC,eAAe;EAC1D,MAAMC,0BAA0B,GAAGzL,kBAAkB,CAACjF,QAAQ,CAACkF,IAAI,CAAC;EACpE,MAAMyL,yBAAyB,GAAG1L,kBAAkB,CAACjF,QAAQ,CAAC4P,cAAc,CAAC;EAC7E,MAAMgB,gCAAgC,GACpC5Q,QAAQ,CAACgN,YAAY,KAAKpU,eAAe,CAACK,GAAG;EAE/C,MAAM4X,QAAQ,GACS9M,QAAW,IAC/B+M,IAAY,IAAI;IACfC,YAAY,CAACT,KAAK,CAAC;IACnBA,KAAK,GAAGU,UAAU,CAACjN,QAAQ,EAAE+M,IAAI,CAAC;EACpC,CAAC;EAEH,MAAMpT,YAAY,GAAG,MAAOuT,iBAA2B,IAAI;IACzD,IAAIjW,eAAe,CAACoC,OAAO,IAAI6T,iBAAiB,EAAE;MAChD,MAAM7T,OAAO,GAAG4C,QAAQ,CAAC4M,QAAQ,GAC7B3R,aAAa,CAAC,CAAC,MAAM4R,cAAc,EAAE,EAAExP,MAAM,CAAC,GAC9C,MAAM6T,wBAAwB,CAAC/Q,OAAO,EAAE,IAAI,CAAC;MAEjD,IAAI/C,OAAO,KAAKR,UAAU,CAACQ,OAAO,EAAE;QAClCI,SAAS,CAACC,KAAK,CAAClB,IAAI,CAAC;UACnBa;QACD,EAAC;MACH;IACF;EACH,CAAC;EAED,MAAM+T,mBAAmB,GAAI3b,KAAc,IACzCwF,eAAe,CAACmC,YAAY,IAC5BK,SAAS,CAACC,KAAK,CAAClB,IAAI,CAAC;IACnBY,YAAY,EAAE3H;EACf,EAAC;EAEJ,MAAMqW,iBAAiB,GAA0B,SAAAA,CAC/CzV,IAAI,EAMF;IAAA,IALFkI,MAAM,GAAA7D,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAzC,SAAA,GAAAyC,SAAA,MAAG,EAAE;IAAA,IACXqH,MAAM,GAAArH,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAzC,SAAA;IAAA,IACNoZ,IAAI,GAAA3W,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAzC,SAAA;IAAA,IACJqZ,eAAe,GAAA5W,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAzC,SAAA,GAAAyC,SAAA,MAAG,IAAI;IAAA,IACtB6W,0BAA0B,GAAA7W,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAzC,SAAA,GAAAyC,SAAA,MAAG,IAAI;IAEjC,IAAI2W,IAAI,IAAItP,MAAM,EAAE;MAClBxB,MAAM,CAACC,MAAM,GAAG,IAAI;MACpB,IAAI+Q,0BAA0B,IAAIzb,KAAK,CAACC,OAAO,CAACmC,GAAG,CAACkI,OAAO,EAAE/J,IAAI,CAAC,CAAC,EAAE;QACnE,MAAM6U,WAAW,GAAGnJ,MAAM,CAAC7J,GAAG,CAACkI,OAAO,EAAE/J,IAAI,CAAC,EAAEgb,IAAI,CAACpF,IAAI,EAAEoF,IAAI,CAAC9E,IAAI,CAAC;QACpE+E,eAAe,IAAIrS,GAAG,CAACmB,OAAO,EAAE/J,IAAI,EAAE6U,WAAW,CAAC;MACnD;MAED,IACEqG,0BAA0B,IAC1Bzb,KAAK,CAACC,OAAO,CAACmC,GAAG,CAAC2E,UAAU,CAACS,MAAM,EAAEjH,IAAI,CAAC,CAAC,EAC3C;QACA,MAAMiH,MAAM,GAAGyE,MAAM,CACnB7J,GAAG,CAAC2E,UAAU,CAACS,MAAM,EAAEjH,IAAI,CAAC,EAC5Bgb,IAAI,CAACpF,IAAI,EACToF,IAAI,CAAC9E,IAAI,CACV;QACD+E,eAAe,IAAIrS,GAAG,CAACpC,UAAU,CAACS,MAAM,EAAEjH,IAAI,EAAEiH,MAAM,CAAC;QACvDwS,eAAe,CAACjT,UAAU,CAACS,MAAM,EAAEjH,IAAI,CAAC;MACzC;MAED,IACE4E,eAAe,CAACkC,aAAa,IAC7BoU,0BAA0B,IAC1Bzb,KAAK,CAACC,OAAO,CAACmC,GAAG,CAAC2E,UAAU,CAACM,aAAa,EAAE9G,IAAI,CAAC,CAAC,EAClD;QACA,MAAM8G,aAAa,GAAG4E,MAAM,CAC1B7J,GAAG,CAAC2E,UAAU,CAACM,aAAa,EAAE9G,IAAI,CAAC,EACnCgb,IAAI,CAACpF,IAAI,EACToF,IAAI,CAAC9E,IAAI,CACV;QACD+E,eAAe,IAAIrS,GAAG,CAACpC,UAAU,CAACM,aAAa,EAAE9G,IAAI,EAAE8G,aAAa,CAAC;MACtE;MAED,IAAIlC,eAAe,CAACiC,WAAW,EAAE;QAC/BL,UAAU,CAACK,WAAW,GAAGwR,cAAc,CAAC7T,cAAc,EAAE4D,WAAW,CAAC;MACrE;MAEDhB,SAAS,CAACC,KAAK,CAAClB,IAAI,CAAC;QACnBnG,IAAI;QACJ2G,OAAO,EAAEwU,SAAS,CAACnb,IAAI,EAAEkI,MAAM,CAAC;QAChCrB,WAAW,EAAEL,UAAU,CAACK,WAAW;QACnCI,MAAM,EAAET,UAAU,CAACS,MAAM;QACzBD,OAAO,EAAER,UAAU,CAACQ;MACrB,EAAC;IACH,OAAM;MACL4B,GAAG,CAACR,WAAW,EAAEpI,IAAI,EAAEkI,MAAM,CAAC;IAC/B;EACH,CAAC;EAED,MAAMkT,YAAY,GAAGA,CAACpb,IAAuB,EAAEkL,KAAiB,KAAI;IAClEtC,GAAG,CAACpC,UAAU,CAACS,MAAM,EAAEjH,IAAI,EAAEkL,KAAK,CAAC;IACnC9D,SAAS,CAACC,KAAK,CAAClB,IAAI,CAAC;MACnBc,MAAM,EAAET,UAAU,CAACS;IACpB,EAAC;EACJ,CAAC;EAED,MAAMoU,mBAAmB,GAAGA,CAC1Brb,IAAuB,EACvBsb,oBAA6B,EAC7Blc,KAAe,EACfkL,GAAS,KACP;IACF,MAAMR,KAAK,GAAUjI,GAAG,CAACkI,OAAO,EAAE/J,IAAI,CAAC;IAEvC,IAAI8J,KAAK,EAAE;MACT,MAAM9H,YAAY,GAAGH,GAAG,CACtBuG,WAAW,EACXpI,IAAI,EACJ0B,WAAW,CAACtC,KAAK,CAAC,GAAGyC,GAAG,CAAC2C,cAAc,EAAExE,IAAI,CAAC,GAAGZ,KAAK,CACvD;MAEDsC,WAAW,CAACM,YAAY,CAAC,IACxBsI,GAAG,IAAKA,GAAwB,CAACiR,cAAe,IACjDD,oBAAoB,GAChB1S,GAAG,CACDR,WAAW,EACXpI,IAAI,EACJsb,oBAAoB,GAAGtZ,YAAY,GAAG0W,aAAa,CAAC5O,KAAK,CAACE,EAAE,CAAC,CAC9D,GACDwR,aAAa,CAACxb,IAAI,EAAEgC,YAAY,CAAC;MAErCkI,MAAM,CAACD,KAAK,IAAI3C,YAAY,EAAE;IAC/B;EACH,CAAC;EAED,MAAMmU,mBAAmB,GAAGA,CAC1Bzb,IAAuB,EACvB0b,UAAmB,EACnBrM,WAAqB,EACrBsM,WAAqB,EACrBC,YAAsB,KAGpB;IACF,IAAIC,iBAAiB,GAAG,KAAK;IAC7B,IAAIC,eAAe,GAAG,KAAK;IAC3B,MAAMC,MAAM,GAAwD;MAClE/b;KACD;IAED,IAAI,CAACqP,WAAW,IAAIsM,WAAW,EAAE;MAC/B,IAAI/W,eAAe,CAAC+B,OAAO,EAAE;QAC3BmV,eAAe,GAAGtV,UAAU,CAACG,OAAO;QACpCH,UAAU,CAACG,OAAO,GAAGoV,MAAM,CAACpV,OAAO,GAAGwU,SAAS,EAAE;QACjDU,iBAAiB,GAAGC,eAAe,KAAKC,MAAM,CAACpV,OAAO;MACvD;MAED,MAAMqV,sBAAsB,GAAG3E,SAAS,CACtCxV,GAAG,CAAC2C,cAAc,EAAExE,IAAI,CAAC,EACzB0b,UAAU,CACX;MAEDI,eAAe,GAAGja,GAAG,CAAC2E,UAAU,CAACK,WAAW,EAAE7G,IAAI,CAAC;MACnDgc,sBAAsB,GAClBvH,KAAK,CAACjO,UAAU,CAACK,WAAW,EAAE7G,IAAI,CAAC,GACnC4I,GAAG,CAACpC,UAAU,CAACK,WAAW,EAAE7G,IAAI,EAAE,IAAI,CAAC;MAC3C+b,MAAM,CAAClV,WAAW,GAAGL,UAAU,CAACK,WAAW;MAC3CgV,iBAAiB,GACfA,iBAAiB,IAChBjX,eAAe,CAACiC,WAAW,IAC1BiV,eAAe,KAAK,CAACE,sBAAuB;IACjD;IAED,IAAI3M,WAAW,EAAE;MACf,MAAM4M,sBAAsB,GAAGpa,GAAG,CAAC2E,UAAU,CAACM,aAAa,EAAE9G,IAAI,CAAC;MAElE,IAAI,CAACic,sBAAsB,EAAE;QAC3BrT,GAAG,CAACpC,UAAU,CAACM,aAAa,EAAE9G,IAAI,EAAEqP,WAAW,CAAC;QAChD0M,MAAM,CAACjV,aAAa,GAAGN,UAAU,CAACM,aAAa;QAC/C+U,iBAAiB,GACfA,iBAAiB,IAChBjX,eAAe,CAACkC,aAAa,IAC5BmV,sBAAsB,KAAK5M,WAAY;MAC5C;IACF;IAEDwM,iBAAiB,IAAID,YAAY,IAAIxU,SAAS,CAACC,KAAK,CAAClB,IAAI,CAAC4V,MAAM,CAAC;IAEjE,OAAOF,iBAAiB,GAAGE,MAAM,GAAG,EAAE;EACxC,CAAC;EAED,MAAMG,mBAAmB,GAAGA,CAC1Blc,IAAuB,EACvBgH,OAAiB,EACjBkE,KAAkB,EAClBL,UAIC,KACC;IACF,MAAMsR,kBAAkB,GAAGta,GAAG,CAAC2E,UAAU,CAACS,MAAM,EAAEjH,IAAI,CAAC;IACvD,MAAM6a,iBAAiB,GACrBjW,eAAe,CAACoC,OAAO,IACvB0I,SAAS,CAAC1I,OAAO,CAAC,IAClBR,UAAU,CAACQ,OAAO,KAAKA,OAAO;IAEhC,IAAIpD,KAAK,CAACwY,UAAU,IAAIlR,KAAK,EAAE;MAC7B+O,kBAAkB,GAAGQ,QAAQ,CAAC,MAAMW,YAAY,CAACpb,IAAI,EAAEkL,KAAK,CAAC,CAAC;MAC9D+O,kBAAkB,CAACrW,KAAK,CAACwY,UAAU,CAAC;IACrC,OAAM;MACLzB,YAAY,CAACT,KAAK,CAAC;MACnBD,kBAAkB,GAAG,IAAI;MACzB/O,KAAK,GACDtC,GAAG,CAACpC,UAAU,CAACS,MAAM,EAAEjH,IAAI,EAAEkL,KAAK,CAAC,GACnCuJ,KAAK,CAACjO,UAAU,CAACS,MAAM,EAAEjH,IAAI,CAAC;IACnC;IAED,IACE,CAACkL,KAAK,GAAG,CAACmM,SAAS,CAAC8E,kBAAkB,EAAEjR,KAAK,CAAC,GAAGiR,kBAAkB,KACnE,CAACtX,aAAa,CAACgG,UAAU,CAAC,IAC1BgQ,iBAAiB,EACjB;MACA,MAAMwB,gBAAgB,GAAG;QACvB,GAAGxR,UAAU;QACb,IAAIgQ,iBAAiB,IAAInL,SAAS,CAAC1I,OAAO,CAAC,GAAG;UAAEA;QAAO,CAAE,GAAG,EAAE,CAAC;QAC/DC,MAAM,EAAET,UAAU,CAACS,MAAM;QACzBjH;OACD;MAEDwG,UAAU,GAAG;QACX,GAAGA,UAAU;QACb,GAAG6V;OACJ;MAEDjV,SAAS,CAACC,KAAK,CAAClB,IAAI,CAACkW,gBAAgB,CAAC;IACvC;IAEDtB,mBAAmB,CAAC,KAAK,CAAC;EAC5B,CAAC;EAED,MAAMtE,cAAc,GAAG,MAAOzW,IAA0B,IACtD4J,QAAQ,CAAC4M,QAAS,CAChBpO,WAA2B,EAC3BwB,QAAQ,CAAC0S,OAAO,EAChBxD,kBAAkB,CAChB9Y,IAAI,IAAIyH,MAAM,CAACwC,KAAK,EACpBF,OAAO,EACPH,QAAQ,CAACgN,YAAY,EACrBhN,QAAQ,CAACsH,yBAAyB,CACnC,CACF;EAEH,MAAMqL,2BAA2B,GAAG,MAAOnc,KAA2B,IAAI;IACxE,MAAM;MAAE6G;IAAM,CAAE,GAAG,MAAMwP,cAAc,EAAE;IAEzC,IAAIrW,KAAK,EAAE;MACT,KAAK,MAAMJ,IAAI,IAAII,KAAK,EAAE;QACxB,MAAM8K,KAAK,GAAGrJ,GAAG,CAACoF,MAAM,EAAEjH,IAAI,CAAC;QAC/BkL,KAAK,GACDtC,GAAG,CAACpC,UAAU,CAACS,MAAM,EAAEjH,IAAI,EAAEkL,KAAK,CAAC,GACnCuJ,KAAK,CAACjO,UAAU,CAACS,MAAM,EAAEjH,IAAI,CAAC;MACnC;IACF,OAAM;MACLwG,UAAU,CAACS,MAAM,GAAGA,MAAM;IAC3B;IAED,OAAOA,MAAM;EACf,CAAC;EAED,MAAM6T,wBAAwB,GAAG,eAAAA,CAC/BpN,MAAiB,EACjB8O,oBAA8B,EAM5B;IAAA,IALFF,OAEI,GAAAjY,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAzC,SAAA,GAAAyC,SAAA;MACFoY,KAAK,EAAE;IACR;IAED,KAAK,MAAMzc,IAAI,IAAI0N,MAAM,EAAE;MACzB,MAAM5D,KAAK,GAAG4D,MAAM,CAAC1N,IAAI,CAAC;MAE1B,IAAI8J,KAAK,EAAE;QACT,MAAM;UAAEE,EAAE;UAAE,GAAG0R;QAAU,CAAE,GAAG5R,KAAK;QAEnC,IAAIE,EAAE,EAAE;UACN,MAAM0S,gBAAgB,GAAGjV,MAAM,CAAC8B,KAAK,CAAClJ,GAAG,CAAC2J,EAAE,CAAChK,IAAI,CAAC;UAClD,MAAM2c,UAAU,GAAG,MAAM1L,aAAa,CACpCnH,KAAK,EACL1B,WAAW,EACXoS,gCAAgC,EAChC5Q,QAAQ,CAACsH,yBAAyB,IAAI,CAACsL,oBAAoB,EAC3DE,gBAAgB,CACjB;UAED,IAAIC,UAAU,CAAC3S,EAAE,CAAChK,IAAI,CAAC,EAAE;YACvBsc,OAAO,CAACG,KAAK,GAAG,KAAK;YACrB,IAAID,oBAAoB,EAAE;cACxB;YACD;UACF;UAED,CAACA,oBAAoB,KAClB3a,GAAG,CAAC8a,UAAU,EAAE3S,EAAE,CAAChK,IAAI,CAAC,GACrB0c,gBAAgB,GACdlN,yBAAyB,CACvBhJ,UAAU,CAACS,MAAM,EACjB0V,UAAU,EACV3S,EAAE,CAAChK,IAAI,CACR,GACD4I,GAAG,CAACpC,UAAU,CAACS,MAAM,EAAE+C,EAAE,CAAChK,IAAI,EAAE2c,UAAU,CAAC3S,EAAE,CAAChK,IAAI,CAAC,CAAC,GACtDyU,KAAK,CAACjO,UAAU,CAACS,MAAM,EAAE+C,EAAE,CAAChK,IAAI,CAAC,CAAC;QACzC;QAED0b,UAAU,KACP,MAAMZ,wBAAwB,CAC7BY,UAAU,EACVc,oBAAoB,EACpBF,OAAO,CACR,CAAC;MACL;IACF;IAED,OAAOA,OAAO,CAACG,KAAK;EACtB,CAAC;EAED,MAAMnU,gBAAgB,GAAGA,CAAA,KAAK;IAC5B,KAAK,MAAMtI,IAAI,IAAIyH,MAAM,CAACuS,OAAO,EAAE;MACjC,MAAMlQ,KAAK,GAAUjI,GAAG,CAACkI,OAAO,EAAE/J,IAAI,CAAC;MAEvC8J,KAAK,KACFA,KAAK,CAACE,EAAE,CAAC8D,IAAI,GACVhE,KAAK,CAACE,EAAE,CAAC8D,IAAI,CAACgD,KAAK,CAAExG,GAAG,IAAK,CAACwN,IAAI,CAACxN,GAAG,CAAC,CAAC,GACxC,CAACwN,IAAI,CAAChO,KAAK,CAACE,EAAE,CAACM,GAAG,CAAC,CAAC,IACxBF,UAAU,CAACpK,IAA+B,CAAC;IAC9C;IAEDyH,MAAM,CAACuS,OAAO,GAAG,IAAI7Y,GAAG,EAAE;EAC5B,CAAC;EAED,MAAMga,SAAS,GAAeA,CAACnb,IAAI,EAAEiB,IAAI,MACvCjB,IAAI,IAAIiB,IAAI,IAAI2H,GAAG,CAACR,WAAW,EAAEpI,IAAI,EAAEiB,IAAI,CAAC,EAC5C,CAACoW,SAAS,CAACuF,SAAS,EAAE,EAAEpY,cAAc,CAAC,CACxC;EAED,MAAM6D,SAAS,GAAgCA,CAC7CjI,KAAK,EACL4B,YAAY,EACZ2F,QAAQ,KAERH,mBAAmB,CACjBpH,KAAK,EACLqH,MAAM,EACN;IACE,IAAIyC,MAAM,CAACD,KAAK,GACZ7B,WAAW,GACX1G,WAAW,CAACM,YAAY,CAAC,GACzBwC,cAAc,GACd+C,QAAQ,CAACnH,KAAK,CAAC,GACf;MAAE,CAACA,KAAK,GAAG4B;IAAY,CAAE,GACzBA,YAAY;EACjB,GACD2F,QAAQ,EACR3F,YAAY,CACb;EAEH,MAAMiT,cAAc,GAClBjV,IAAuB,IAEvBuB,OAAO,CACLM,GAAG,CACDqI,MAAM,CAACD,KAAK,GAAG7B,WAAW,GAAG5D,cAAc,EAC3CxE,IAAI,EACJ4D,KAAK,CAACyF,gBAAgB,GAAGxH,GAAG,CAAC2C,cAAc,EAAExE,IAAI,EAAE,EAAE,CAAC,GAAG,EAAE,CAC5D,CACF;EAEH,MAAMwb,aAAa,GAAG,SAAAA,CACpBxb,IAAuB,EACvBZ,KAAkC,EAEhC;IAAA,IADFqP,OAAA,GAAApK,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAzC,SAAA,GAAAyC,SAAA,MAA0B,EAAE;IAE5B,MAAMyF,KAAK,GAAUjI,GAAG,CAACkI,OAAO,EAAE/J,IAAI,CAAC;IACvC,IAAI0b,UAAU,GAAYtc,KAAK;IAE/B,IAAI0K,KAAK,EAAE;MACT,MAAM+S,cAAc,GAAG/S,KAAK,CAACE,EAAE;MAE/B,IAAI6S,cAAc,EAAE;QAClB,CAACA,cAAc,CAAC7W,QAAQ,IACtB4C,GAAG,CAACR,WAAW,EAAEpI,IAAI,EAAEsY,eAAe,CAAClZ,KAAK,EAAEyd,cAAc,CAAC,CAAC;QAEhEnB,UAAU,GACR7L,aAAa,CAACgN,cAAc,CAACvS,GAAG,CAAC,IAAIhL,iBAAiB,CAACF,KAAK,CAAC,GACzD,EAAE,GACFA,KAAK;QAEX,IAAIyY,gBAAgB,CAACgF,cAAc,CAACvS,GAAG,CAAC,EAAE;UACxC,CAAC,GAAGuS,cAAc,CAACvS,GAAG,CAACmE,OAAO,CAAC,CAACqO,OAAO,CACpCC,SAAS,IACPA,SAAS,CAACC,QAAQ,GACjBtB,UACD,CAAC9O,QAAQ,CAACmQ,SAAS,CAAC3d,KAAK,CAAE,CAC/B;QACF,OAAM,IAAIyd,cAAc,CAAC/O,IAAI,EAAE;UAC9B,IAAI9O,eAAe,CAAC6d,cAAc,CAACvS,GAAG,CAAC,EAAE;YACvCuS,cAAc,CAAC/O,IAAI,CAACxJ,MAAM,GAAG,CAAC,GAC1BuY,cAAc,CAAC/O,IAAI,CAACgP,OAAO,CACxBG,WAAW,IACV,CAAC,CAACA,WAAW,CAAC1B,cAAc,IAAI,CAAC0B,WAAW,CAACjX,QAAQ,MACpDiX,WAAW,CAACnd,OAAO,GAAGL,KAAK,CAACC,OAAO,CAACgc,UAAU,CAAC,GAC5C,CAAC,CAAEA,UAAiB,CAACxW,IAAI,CACtBjE,IAAY,IAAKA,IAAI,KAAKgc,WAAW,CAAC7d,KAAK,CAC7C,GACDsc,UAAU,KAAKuB,WAAW,CAAC7d,KAAK,CAAC,CACxC,GACDyd,cAAc,CAAC/O,IAAI,CAAC,CAAC,CAAC,KACrB+O,cAAc,CAAC/O,IAAI,CAAC,CAAC,CAAC,CAAChO,OAAO,GAAG,CAAC,CAAC4b,UAAU,CAAC;UACpD,OAAM;YACLmB,cAAc,CAAC/O,IAAI,CAACgP,OAAO,CACxBI,QAA0B,IACxBA,QAAQ,CAACpd,OAAO,GAAGod,QAAQ,CAAC9d,KAAK,KAAKsc,UAAW,CACrD;UACF;QACF,OAAM,IAAI/L,WAAW,CAACkN,cAAc,CAACvS,GAAG,CAAC,EAAE;UAC1CuS,cAAc,CAACvS,GAAG,CAAClL,KAAK,GAAG,EAAE;QAC9B,OAAM;UACLyd,cAAc,CAACvS,GAAG,CAAClL,KAAK,GAAGsc,UAAU;UAErC,IAAI,CAACmB,cAAc,CAACvS,GAAG,CAACpL,IAAI,EAAE;YAC5BkI,SAAS,CAACc,MAAM,CAAC/B,IAAI,CAAC;cACpBnG,IAAI;cACJkI,MAAM,EAAE;gBAAE,GAAGE;cAAW;YACzB,EAAC;UACH;QACF;MACF;IACF;IAED,CAACqG,OAAO,CAACkN,WAAW,IAAIlN,OAAO,CAAC0O,WAAW,KACzC1B,mBAAmB,CACjBzb,IAAI,EACJ0b,UAAU,EACVjN,OAAO,CAAC0O,WAAW,EACnB1O,OAAO,CAACkN,WAAW,EACnB,IAAI,CACL;IAEHlN,OAAO,CAAC2O,cAAc,IAAIC,OAAO,CAACrd,IAA0B,CAAC;EAC/D,CAAC;EAED,MAAMsd,SAAS,GAAGA,CAKhBtd,IAAO,EACPZ,KAAQ,EACRqP,OAAU,KACR;IACF,KAAK,MAAM8O,QAAQ,IAAIne,KAAK,EAAE;MAC5B,MAAMsc,UAAU,GAAGtc,KAAK,CAACme,QAAQ,CAAC;MAClC,MAAMxV,SAAS,GAAG,GAAG/H,IAAQ,IAAAud,QAAQ,EAAE;MACvC,MAAMzT,KAAK,GAAGjI,GAAG,CAACkI,OAAO,EAAEhC,SAAS,CAAC;MAErC,CAACN,MAAM,CAAC8B,KAAK,CAAClJ,GAAG,CAACL,IAAI,CAAC,IACrB,CAACoX,WAAW,CAACsE,UAAU,CAAC,IACvB5R,KAAK,IAAI,CAACA,KAAK,CAACE,EAAG,KACtB,CAAC7K,YAAY,CAACuc,UAAU,CAAC,GACrB4B,SAAS,CAACvV,SAAS,EAAE2T,UAAU,EAAEjN,OAAO,CAAC,GACzC+M,aAAa,CAACzT,SAAS,EAAE2T,UAAU,EAAEjN,OAAO,CAAC;IAClD;EACH,CAAC;EAED,MAAM+O,QAAQ,GAAkC,SAAAA,CAC9Cxd,IAAI,EACJZ,KAAK,EAEH;IAAA,IADFqP,OAAO,GAAApK,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAzC,SAAA,GAAAyC,SAAA,MAAG,EAAE;IAEZ,MAAMyF,KAAK,GAAGjI,GAAG,CAACkI,OAAO,EAAE/J,IAAI,CAAC;IAChC,MAAMmR,YAAY,GAAG1J,MAAM,CAAC8B,KAAK,CAAClJ,GAAG,CAACL,IAAI,CAAC;IAC3C,MAAMyd,UAAU,GAAGzc,WAAW,CAAC5B,KAAK,CAAC;IAErCwJ,GAAG,CAACR,WAAW,EAAEpI,IAAI,EAAEyd,UAAU,CAAC;IAElC,IAAItM,YAAY,EAAE;MAChB/J,SAAS,CAACmC,KAAK,CAACpD,IAAI,CAAC;QACnBnG,IAAI;QACJkI,MAAM,EAAE;UAAE,GAAGE;QAAW;MACzB,EAAC;MAEF,IACE,CAACxD,eAAe,CAAC+B,OAAO,IAAI/B,eAAe,CAACiC,WAAW,KACvD4H,OAAO,CAACkN,WAAW,EACnB;QACAvU,SAAS,CAACC,KAAK,CAAClB,IAAI,CAAC;UACnBnG,IAAI;UACJ6G,WAAW,EAAEwR,cAAc,CAAC7T,cAAc,EAAE4D,WAAW,CAAC;UACxDzB,OAAO,EAAEwU,SAAS,CAACnb,IAAI,EAAEyd,UAAU;QACpC,EAAC;MACH;IACF,OAAM;MACL3T,KAAK,IAAI,CAACA,KAAK,CAACE,EAAE,IAAI,CAAC1K,iBAAiB,CAACme,UAAU,CAAC,GAChDH,SAAS,CAACtd,IAAI,EAAEyd,UAAU,EAAEhP,OAAO,CAAC,GACpC+M,aAAa,CAACxb,IAAI,EAAEyd,UAAU,EAAEhP,OAAO,CAAC;IAC7C;IAEDW,SAAS,CAACpP,IAAI,EAAEyH,MAAM,CAAC,IAAIL,SAAS,CAACC,KAAK,CAAClB,IAAI,CAAC;MAAE,GAAGK;IAAU,CAAE,CAAC;IAClEY,SAAS,CAACc,MAAM,CAAC/B,IAAI,CAAC;MACpBnG,IAAI;MACJkI,MAAM,EAAE;QAAE,GAAGE;MAAW;IACzB,EAAC;IACF,CAAC8B,MAAM,CAACD,KAAK,IAAI4P,eAAe,EAAE;EACpC,CAAC;EAED,MAAMnX,QAAQ,GAAkB,MAAO9C,KAAK,IAAI;IAC9C,MAAMC,MAAM,GAAGD,KAAK,CAACC,MAAM;IAC3B,IAAIG,IAAI,GAAGH,MAAM,CAACG,IAAI;IACtB,IAAI0d,mBAAmB,GAAG,IAAI;IAC9B,MAAM5T,KAAK,GAAUjI,GAAG,CAACkI,OAAO,EAAE/J,IAAI,CAAC;IACvC,MAAM2d,oBAAoB,GAAGA,CAAA,KAC3B9d,MAAM,CAACX,IAAI,GAAGwZ,aAAa,CAAC5O,KAAK,CAACE,EAAE,CAAC,GAAGrK,aAAa,CAACC,KAAK,CAAC;IAE9D,IAAIkK,KAAK,EAAE;MACT,IAAIoB,KAAK;MACT,IAAIlE,OAAO;MACX,MAAM0U,UAAU,GAAGiC,oBAAoB,EAAE;MACzC,MAAMtO,WAAW,GACfzP,KAAK,CAACV,IAAI,KAAKkD,MAAM,CAACC,IAAI,IAAIzC,KAAK,CAACV,IAAI,KAAKkD,MAAM,CAACE,SAAS;MAC/D,MAAMsb,oBAAoB,GACvB,CAAC1E,aAAa,CAACpP,KAAK,CAACE,EAAE,CAAC,IACvB,CAACJ,QAAQ,CAAC4M,QAAQ,IAClB,CAAC3U,GAAG,CAAC2E,UAAU,CAACS,MAAM,EAAEjH,IAAI,CAAC,IAC7B,CAAC8J,KAAK,CAACE,EAAE,CAAC6T,IAAI,IAChBtE,cAAc,CACZlK,WAAW,EACXxN,GAAG,CAAC2E,UAAU,CAACM,aAAa,EAAE9G,IAAI,CAAC,EACnCwG,UAAU,CAAC+P,WAAW,EACtBgE,yBAAyB,EACzBD,0BAA0B,CAC3B;MACH,MAAMwD,OAAO,GAAG1O,SAAS,CAACpP,IAAI,EAAEyH,MAAM,EAAE4H,WAAW,CAAC;MAEpDzG,GAAG,CAACR,WAAW,EAAEpI,IAAI,EAAE0b,UAAU,CAAC;MAElC,IAAIrM,WAAW,EAAE;QACfvF,KAAK,CAACE,EAAE,CAACvH,MAAM,IAAIqH,KAAK,CAACE,EAAE,CAACvH,MAAM,CAAC7C,KAAK,CAAC;QACzCqa,kBAAkB,IAAIA,kBAAkB,CAAC,CAAC,CAAC;MAC5C,OAAM,IAAInQ,KAAK,CAACE,EAAE,CAACtH,QAAQ,EAAE;QAC5BoH,KAAK,CAACE,EAAE,CAACtH,QAAQ,CAAC9C,KAAK,CAAC;MACzB;MAED,MAAMiL,UAAU,GAAG4Q,mBAAmB,CACpCzb,IAAI,EACJ0b,UAAU,EACVrM,WAAW,EACX,KAAK,CACN;MAED,MAAMuM,YAAY,GAAG,CAAC/W,aAAa,CAACgG,UAAU,CAAC,IAAIiT,OAAO;MAE1D,CAACzO,WAAW,IACVjI,SAAS,CAACc,MAAM,CAAC/B,IAAI,CAAC;QACpBnG,IAAI;QACJd,IAAI,EAAEU,KAAK,CAACV,IAAI;QAChBgJ,MAAM,EAAE;UAAE,GAAGE;QAAW;MACzB,EAAC;MAEJ,IAAIwV,oBAAoB,EAAE;QACxBhZ,eAAe,CAACoC,OAAO,IAAIM,YAAY,EAAE;QAEzC,OACEsU,YAAY,IACZxU,SAAS,CAACC,KAAK,CAAClB,IAAI,CAAC;UAAEnG,IAAI;UAAE,IAAI8d,OAAO,GAAG,EAAE,GAAGjT,UAAU;QAAC,CAAE,CAAC;MAEjE;MAED,CAACwE,WAAW,IAAIyO,OAAO,IAAI1W,SAAS,CAACC,KAAK,CAAClB,IAAI,CAAC;QAAE,GAAGK;MAAU,CAAE,CAAC;MAElEuU,mBAAmB,CAAC,IAAI,CAAC;MAEzB,IAAInR,QAAQ,CAAC4M,QAAQ,EAAE;QACrB,MAAM;UAAEvP;QAAM,CAAE,GAAG,MAAMwP,cAAc,CAAC,CAACzW,IAAI,CAAC,CAAC;QAC/C,MAAM+d,yBAAyB,GAAG5E,iBAAiB,CACjD3S,UAAU,CAACS,MAAM,EACjB8C,OAAO,EACP/J,IAAI,CACL;QACD,MAAMge,iBAAiB,GAAG7E,iBAAiB,CACzClS,MAAM,EACN8C,OAAO,EACPgU,yBAAyB,CAAC/d,IAAI,IAAIA,IAAI,CACvC;QAEDkL,KAAK,GAAG8S,iBAAiB,CAAC9S,KAAK;QAC/BlL,IAAI,GAAGge,iBAAiB,CAAChe,IAAI;QAE7BgH,OAAO,GAAGnC,aAAa,CAACoC,MAAM,CAAC;MAChC,OAAM;QACLiE,KAAK,GAAG,CACN,MAAM+F,aAAa,CACjBnH,KAAK,EACL1B,WAAW,EACXoS,gCAAgC,EAChC5Q,QAAQ,CAACsH,yBAAyB,CACnC,EACDlR,IAAI,CAAC;QAEP0d,mBAAmB,GACjBvU,KAAK,CAACuS,UAAU,CAAC,IACjBA,UAAU,KAAK7Z,GAAG,CAACuG,WAAW,EAAEpI,IAAI,EAAE0b,UAAU,CAAC;QAEnD,IAAIgC,mBAAmB,EAAE;UACvB,IAAIxS,KAAK,EAAE;YACTlE,OAAO,GAAG,KAAK;UAChB,OAAM,IAAIpC,eAAe,CAACoC,OAAO,EAAE;YAClCA,OAAO,GAAG,MAAM8T,wBAAwB,CAAC/Q,OAAO,EAAE,IAAI,CAAC;UACxD;QACF;MACF;MAED,IAAI2T,mBAAmB,EAAE;QACvB5T,KAAK,CAACE,EAAE,CAAC6T,IAAI,IACXR,OAAO,CACLvT,KAAK,CAACE,EAAE,CAAC6T,IAEoB,CAC9B;QACH3B,mBAAmB,CAAClc,IAAI,EAAEgH,OAAO,EAAEkE,KAAK,EAAEL,UAAU,CAAC;MACtD;IACF;EACH,CAAC;EAED,MAAMwS,OAAO,GAAiC,eAAAA,CAAOrd,IAAI,EAAkB;IAAA,IAAhByO,OAAO,GAAApK,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAzC,SAAA,GAAAyC,SAAA,MAAG,EAAE;IACrE,IAAI2C,OAAO;IACX,IAAImM,gBAAgB;IACpB,MAAM8K,UAAU,GAAG9Y,qBAAqB,CAACnF,IAAI,CAAwB;IAErE+a,mBAAmB,CAAC,IAAI,CAAC;IAEzB,IAAInR,QAAQ,CAAC4M,QAAQ,EAAE;MACrB,MAAMvP,MAAM,GAAG,MAAMsV,2BAA2B,CAC9C7a,WAAW,CAAC1B,IAAI,CAAC,GAAGA,IAAI,GAAGie,UAAU,CACtC;MAEDjX,OAAO,GAAGnC,aAAa,CAACoC,MAAM,CAAC;MAC/BkM,gBAAgB,GAAGnT,IAAI,GACnB,CAACie,UAAU,CAAC1Y,IAAI,CAAEvF,IAAI,IAAK6B,GAAG,CAACoF,MAAM,EAAEjH,IAAI,CAAC,CAAC,GAC7CgH,OAAO;IACZ,OAAM,IAAIhH,IAAI,EAAE;MACfmT,gBAAgB,GAAG,CACjB,MAAM+K,OAAO,CAACrb,GAAG,CACfob,UAAU,CAACnW,GAAG,CAAC,MAAOC,SAAS,IAAI;QACjC,MAAM+B,KAAK,GAAGjI,GAAG,CAACkI,OAAO,EAAEhC,SAAS,CAAC;QACrC,OAAO,MAAM+S,wBAAwB,CACnChR,KAAK,IAAIA,KAAK,CAACE,EAAE,GAAG;UAAE,CAACjC,SAAS,GAAG+B;QAAK,CAAE,GAAGA,KAAK,CACnD;OACF,CAAC,CACH,EACDgH,KAAK,CAACrP,OAAO,CAAC;MAChB,EAAE,CAAC0R,gBAAgB,IAAI,CAAC3M,UAAU,CAACQ,OAAO,CAAC,IAAIM,YAAY,EAAE;IAC9D,OAAM;MACL6L,gBAAgB,GAAGnM,OAAO,GAAG,MAAM8T,wBAAwB,CAAC/Q,OAAO,CAAC;IACrE;IAED3C,SAAS,CAACC,KAAK,CAAClB,IAAI,CAAC;MACnB,IAAI,CAACoB,QAAQ,CAACvH,IAAI,CAAC,IAClB4E,eAAe,CAACoC,OAAO,IAAIA,OAAO,KAAKR,UAAU,CAACQ,OAAQ,GACvD,EAAE,GACF;QAAEhH;MAAI,CAAE,CAAC;MACb,IAAI4J,QAAQ,CAAC4M,QAAQ,IAAI,CAACxW,IAAI,GAAG;QAAEgH;MAAO,CAAE,GAAG,EAAE,CAAC;MAClDC,MAAM,EAAET,UAAU,CAACS,MAAM;MACzBF,YAAY,EAAE;IACf,EAAC;IAEF0H,OAAO,CAACC,WAAW,IACjB,CAACyE,gBAAgB,IACjB1F,YAAY,CACV1D,OAAO,EACNzI,GAAG,IAAKA,GAAG,IAAIO,GAAG,CAAC2E,UAAU,CAACS,MAAM,EAAE3F,GAAG,CAAC,EAC3CtB,IAAI,GAAGie,UAAU,GAAGxW,MAAM,CAACwC,KAAK,CACjC;IAEH,OAAOkJ,gBAAgB;EACzB,CAAC;EAED,MAAMyJ,SAAS,GACbqB,UAE0C,IACxC;IACF,MAAM/V,MAAM,GAAG;MACb,GAAG1D,cAAc;MACjB,IAAI0F,MAAM,CAACD,KAAK,GAAG7B,WAAW,GAAG,EAAE;KACpC;IAED,OAAO1G,WAAW,CAACuc,UAAU,CAAC,GAC1B/V,MAAM,GACNX,QAAQ,CAAC0W,UAAU,CAAC,GACpBpc,GAAG,CAACqG,MAAM,EAAE+V,UAAU,CAAC,GACvBA,UAAU,CAACnW,GAAG,CAAE9H,IAAI,IAAK6B,GAAG,CAACqG,MAAM,EAAElI,IAAI,CAAC,CAAC;EACjD,CAAC;EAED,MAAMme,aAAa,GAAuCA,CACxDne,IAAI,EACJiE,SAAS,MACL;IACJ8G,OAAO,EAAE,CAAC,CAAClJ,GAAG,CAAC,CAACoC,SAAS,IAAIuC,UAAU,EAAES,MAAM,EAAEjH,IAAI,CAAC;IACtD2G,OAAO,EAAE,CAAC,CAAC9E,GAAG,CAAC,CAACoC,SAAS,IAAIuC,UAAU,EAAEK,WAAW,EAAE7G,IAAI,CAAC;IAC3DiL,SAAS,EAAE,CAAC,CAACpJ,GAAG,CAAC,CAACoC,SAAS,IAAIuC,UAAU,EAAEM,aAAa,EAAE9G,IAAI,CAAC;IAC/DkL,KAAK,EAAErJ,GAAG,CAAC,CAACoC,SAAS,IAAIuC,UAAU,EAAES,MAAM,EAAEjH,IAAI;EAClD,EAAC;EAEF,MAAMoe,WAAW,GAAsCpe,IAAI,IAAI;IAC7DA,IAAI,IACFmF,qBAAqB,CAACnF,IAAI,CAAC,CAAC8c,OAAO,CAAEuB,SAAS,IAC5C5J,KAAK,CAACjO,UAAU,CAACS,MAAM,EAAEoX,SAAS,CAAC,CACpC;IAEHjX,SAAS,CAACC,KAAK,CAAClB,IAAI,CAAC;MACnBc,MAAM,EAAEjH,IAAI,GAAGwG,UAAU,CAACS,MAAM,GAAG;IACpC,EAAC;EACJ,CAAC;EAED,MAAMkG,QAAQ,GAAkCA,CAACnN,IAAI,EAAEkL,KAAK,EAAEuD,OAAO,KAAI;IACvE,MAAMnE,GAAG,GAAG,CAACzI,GAAG,CAACkI,OAAO,EAAE/J,IAAI,EAAE;MAAEgK,EAAE,EAAE;IAAE,CAAE,CAAC,CAACA,EAAE,IAAI,EAAE,EAAEM,GAAG;IAEzD1B,GAAG,CAACpC,UAAU,CAACS,MAAM,EAAEjH,IAAI,EAAE;MAC3B,GAAGkL,KAAK;MACRZ;IACD,EAAC;IAEFlD,SAAS,CAACC,KAAK,CAAClB,IAAI,CAAC;MACnBnG,IAAI;MACJiH,MAAM,EAAET,UAAU,CAACS,MAAM;MACzBD,OAAO,EAAE;IACV,EAAC;IAEFyH,OAAO,IAAIA,OAAO,CAACC,WAAW,IAAIpE,GAAG,IAAIA,GAAG,CAACE,KAAK,IAAIF,GAAG,CAACE,KAAK,EAAE;EACnE,CAAC;EAED,MAAM5C,KAAK,GAA+BA,CACxC5H,IAG+B,EAC/BgC,YAAwC,KAExC4N,UAAU,CAAC5P,IAAI,CAAC,GACZoH,SAAS,CAACc,MAAM,CAAChC,SAAS,CAAC;IACzBC,IAAI,EAAGmY,OAAO,IACZte,IAAI,CACFqI,SAAS,CAACzG,SAAS,EAAEI,YAAY,CAAC,EAClCsc,OAIC;GAEN,CAAC,GACFjW,SAAS,CACPrI,IAA+C,EAC/CgC,YAAY,EACZ,IAAI,CACL;EAEP,MAAMoI,UAAU,GAAoC,SAAAA,CAACpK,IAAI,EAAkB;IAAA,IAAhByO,OAAO,GAAApK,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAzC,SAAA,GAAAyC,SAAA,MAAG,EAAE;IACrE,KAAK,MAAM0D,SAAS,IAAI/H,IAAI,GAAGmF,qBAAqB,CAACnF,IAAI,CAAC,GAAGyH,MAAM,CAACwC,KAAK,EAAE;MACzExC,MAAM,CAACwC,KAAK,CAACsU,MAAM,CAACxW,SAAS,CAAC;MAC9BN,MAAM,CAAC8B,KAAK,CAACgV,MAAM,CAACxW,SAAS,CAAC;MAE9B,IAAI,CAAC0G,OAAO,CAAC+P,SAAS,EAAE;QACtB/J,KAAK,CAAC1K,OAAO,EAAEhC,SAAS,CAAC;QACzB0M,KAAK,CAACrM,WAAW,EAAEL,SAAS,CAAC;MAC9B;MAED,CAAC0G,OAAO,CAACgQ,SAAS,IAAIhK,KAAK,CAACjO,UAAU,CAACS,MAAM,EAAEc,SAAS,CAAC;MACzD,CAAC0G,OAAO,CAACiQ,SAAS,IAAIjK,KAAK,CAACjO,UAAU,CAACK,WAAW,EAAEkB,SAAS,CAAC;MAC9D,CAAC0G,OAAO,CAACkQ,WAAW,IAAIlK,KAAK,CAACjO,UAAU,CAACM,aAAa,EAAEiB,SAAS,CAAC;MAClE,CAAC6B,QAAQ,CAACP,gBAAgB,IACxB,CAACoF,OAAO,CAACmQ,gBAAgB,IACzBnK,KAAK,CAACjQ,cAAc,EAAEuD,SAAS,CAAC;IACnC;IAEDX,SAAS,CAACc,MAAM,CAAC/B,IAAI,CAAC;MACpB+B,MAAM,EAAE;QAAE,GAAGE;MAAW;IACzB,EAAC;IAEFhB,SAAS,CAACC,KAAK,CAAClB,IAAI,CAAC;MACnB,GAAGK,UAAU;MACb,IAAI,CAACiI,OAAO,CAACiQ,SAAS,GAAG,EAAE,GAAG;QAAE/X,OAAO,EAAEwU,SAAS;MAAE,CAAE;IACvD,EAAC;IAEF,CAAC1M,OAAO,CAACoQ,WAAW,IAAIvX,YAAY,EAAE;EACxC,CAAC;EAED,MAAMmC,QAAQ,GAAkC,SAAAA,CAACzJ,IAAI,EAAkB;IAAA,IAAhByO,OAAO,GAAApK,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAzC,SAAA,GAAAyC,SAAA,MAAG,EAAE;IACjE,IAAIyF,KAAK,GAAGjI,GAAG,CAACkI,OAAO,EAAE/J,IAAI,CAAC;IAC9B,MAAM8e,iBAAiB,GAAGpP,SAAS,CAACjB,OAAO,CAACzI,QAAQ,CAAC;IAErD4C,GAAG,CAACmB,OAAO,EAAE/J,IAAI,EAAE;MACjB,IAAI8J,KAAK,IAAI,EAAE,CAAC;MAChBE,EAAE,EAAE;QACF,IAAIF,KAAK,IAAIA,KAAK,CAACE,EAAE,GAAGF,KAAK,CAACE,EAAE,GAAG;UAAEM,GAAG,EAAE;YAAEtK;UAAI;QAAE,CAAE,CAAC;QACrDA,IAAI;QACJiK,KAAK,EAAE,IAAI;QACX,GAAGwE;MACJ;IACF,EAAC;IACFhH,MAAM,CAACwC,KAAK,CAACpC,GAAG,CAAC7H,IAAI,CAAC;IAEtB8J,KAAK,GACDgV,iBAAiB,IACjBlW,GAAG,CACDR,WAAW,EACXpI,IAAI,EACJyO,OAAO,CAACzI,QAAQ,GACZpE,SAAS,GACTC,GAAG,CAACuG,WAAW,EAAEpI,IAAI,EAAE0Y,aAAa,CAAC5O,KAAK,CAACE,EAAE,CAAC,CAAC,CACpD,GACDqR,mBAAmB,CAACrb,IAAI,EAAE,IAAI,EAAEyO,OAAO,CAACrP,KAAK,CAAC;IAElD,OAAO;MACL,IAAI0f,iBAAiB,GAAG;QAAE9Y,QAAQ,EAAEyI,OAAO,CAACzI;MAAQ,CAAE,GAAG,EAAE,CAAC;MAC5D,IAAI4D,QAAQ,CAACmV,WAAW,GACpB;QACE3b,QAAQ,EAAE,CAAC,CAACqL,OAAO,CAACrL,QAAQ;QAC5BJ,GAAG,EAAE+V,YAAY,CAACtK,OAAO,CAACzL,GAAG,CAAC;QAC9BD,GAAG,EAAEgW,YAAY,CAACtK,OAAO,CAAC1L,GAAG,CAAC;QAC9BG,SAAS,EAAE6V,YAAY,CAAStK,OAAO,CAACvL,SAAS,CAAW;QAC5DD,SAAS,EAAE8V,YAAY,CAACtK,OAAO,CAACxL,SAAS,CAAW;QACpDE,OAAO,EAAE4V,YAAY,CAACtK,OAAO,CAACtL,OAAO;MACtC,IACD,EAAE,CAAC;MACPnD,IAAI;MACJ0C,QAAQ;MACRD,MAAM,EAAEC,QAAQ;MAChB4H,GAAG,EAAGA,GAA4B,IAAU;QAC1C,IAAIA,GAAG,EAAE;UACPb,QAAQ,CAACzJ,IAAI,EAAEyO,OAAO,CAAC;UACvB3E,KAAK,GAAGjI,GAAG,CAACkI,OAAO,EAAE/J,IAAI,CAAC;UAE1B,MAAMgf,QAAQ,GAAGtd,WAAW,CAAC4I,GAAG,CAAClL,KAAK,CAAC,GACnCkL,GAAG,CAAC2U,gBAAgB,GACjB3U,GAAG,CAAC2U,gBAAgB,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAS,IAAI3U,GAAG,GAChEA,GAAG,GACLA,GAAG;UACP,MAAM4U,eAAe,GAAGzN,iBAAiB,CAACuN,QAAQ,CAAC;UACnD,MAAMlR,IAAI,GAAGhE,KAAK,CAACE,EAAE,CAAC8D,IAAI,IAAI,EAAE;UAEhC,IACEoR,eAAe,GACXpR,IAAI,CAAC5I,IAAI,CAAEsL,MAAW,IAAKA,MAAM,KAAKwO,QAAQ,CAAC,GAC/CA,QAAQ,KAAKlV,KAAK,CAACE,EAAE,CAACM,GAAG,EAC7B;YACA;UACD;UAED1B,GAAG,CAACmB,OAAO,EAAE/J,IAAI,EAAE;YACjBgK,EAAE,EAAE;cACF,GAAGF,KAAK,CAACE,EAAE;cACX,IAAIkV,eAAe,GACf;gBACEpR,IAAI,EAAE,CACJ,GAAGA,IAAI,CAACtM,MAAM,CAACsW,IAAI,CAAC,EACpBkH,QAAQ,EACR,IAAIvf,KAAK,CAACC,OAAO,CAACmC,GAAG,CAAC2C,cAAc,EAAExE,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAC1D;gBACDsK,GAAG,EAAE;kBAAEpL,IAAI,EAAE8f,QAAQ,CAAC9f,IAAI;kBAAEc;gBAAI;cACjC,IACD;gBAAEsK,GAAG,EAAE0U;cAAQ,CAAE;YACtB;UACF,EAAC;UAEF3D,mBAAmB,CAACrb,IAAI,EAAE,KAAK,EAAE4B,SAAS,EAAEod,QAAQ,CAAC;QACtD,OAAM;UACLlV,KAAK,GAAGjI,GAAG,CAACkI,OAAO,EAAE/J,IAAI,EAAE,EAAE,CAAC;UAE9B,IAAI8J,KAAK,CAACE,EAAE,EAAE;YACZF,KAAK,CAACE,EAAE,CAACC,KAAK,GAAG,KAAK;UACvB;UAED,CAACL,QAAQ,CAACP,gBAAgB,IAAIoF,OAAO,CAACpF,gBAAgB,KACpD,EAAElJ,kBAAkB,CAACsH,MAAM,CAAC8B,KAAK,EAAEvJ,IAAI,CAAC,IAAIkK,MAAM,CAACC,MAAM,CAAC,IAC1D1C,MAAM,CAACuS,OAAO,CAACnS,GAAG,CAAC7H,IAAI,CAAC;QAC3B;;KAEJ;EACH,CAAC;EAED,MAAMmf,WAAW,GAAGA,CAAA,KAClBvV,QAAQ,CAAC+P,gBAAgB,IACzBlM,YAAY,CACV1D,OAAO,EACNzI,GAAG,IAAKA,GAAG,IAAIO,GAAG,CAAC2E,UAAU,CAACS,MAAM,EAAE3F,GAAG,CAAC,EAC3CmG,MAAM,CAACwC,KAAK,CACb;EAEH,MAAMkC,YAAY,GAChBA,CAACiT,OAAO,EAAEC,SAAS,KAAK,MAAOC,CAAC,IAAI;IAClC,IAAIA,CAAC,EAAE;MACLA,CAAC,CAACC,cAAc,IAAID,CAAC,CAACC,cAAc,EAAE;MACtCD,CAAC,CAACE,OAAO,IAAIF,CAAC,CAACE,OAAO,EAAE;IACzB;IACD,IAAI3K,WAAW,GAAG7T,WAAW,CAACoH,WAAW,CAAC;IAE1ChB,SAAS,CAACC,KAAK,CAAClB,IAAI,CAAC;MACnB4T,YAAY,EAAE;IACf,EAAC;IAEF,IAAInQ,QAAQ,CAAC4M,QAAQ,EAAE;MACrB,MAAM;QAAEvP,MAAM;QAAEiB;MAAM,CAAE,GAAG,MAAMuO,cAAc,EAAE;MACjDjQ,UAAU,CAACS,MAAM,GAAGA,MAAM;MAC1B4N,WAAW,GAAG3M,MAAM;IACrB,OAAM;MACL,MAAM4S,wBAAwB,CAAC/Q,OAAO,CAAC;IACxC;IAED0K,KAAK,CAACjO,UAAU,CAACS,MAAM,EAAE,MAAM,CAAC;IAEhC,IAAIpC,aAAa,CAAC2B,UAAU,CAACS,MAAM,CAAC,EAAE;MACpCG,SAAS,CAACC,KAAK,CAAClB,IAAI,CAAC;QACnBc,MAAM,EAAE;MACT,EAAC;MACF,MAAMmY,OAAO,CAACvK,WAA2B,EAAEyK,CAAC,CAAC;IAC9C,OAAM;MACL,IAAID,SAAS,EAAE;QACb,MAAMA,SAAS,CAAC;UAAE,GAAG7Y,UAAU,CAACS;QAAM,CAAE,EAAEqY,CAAC,CAAC;MAC7C;MACDH,WAAW,EAAE;MACbvE,UAAU,CAACuE,WAAW,CAAC;IACxB;IAED/X,SAAS,CAACC,KAAK,CAAClB,IAAI,CAAC;MACnBoQ,WAAW,EAAE,IAAI;MACjBwD,YAAY,EAAE,KAAK;MACnB7M,kBAAkB,EAAErI,aAAa,CAAC2B,UAAU,CAACS,MAAM,CAAC;MACpD6S,WAAW,EAAEtT,UAAU,CAACsT,WAAW,GAAG,CAAC;MACvC7S,MAAM,EAAET,UAAU,CAACS;IACpB,EAAC;EACJ,CAAC;EAEH,MAAMwY,UAAU,GAAoC,SAAAA,CAACzf,IAAI,EAAkB;IAAA,IAAhByO,OAAO,GAAApK,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAzC,SAAA,GAAAyC,SAAA,MAAG,EAAE;IACrE,IAAIxC,GAAG,CAACkI,OAAO,EAAE/J,IAAI,CAAC,EAAE;MACtB,IAAI0B,WAAW,CAAC+M,OAAO,CAACzM,YAAY,CAAC,EAAE;QACrCwb,QAAQ,CAACxd,IAAI,EAAE6B,GAAG,CAAC2C,cAAc,EAAExE,IAAI,CAAC,CAAC;MAC1C,OAAM;QACLwd,QAAQ,CACNxd,IAAI,EACJyO,OAAO,CAACzM,YAGP,CACF;QACD4G,GAAG,CAACpE,cAAc,EAAExE,IAAI,EAAEyO,OAAO,CAACzM,YAAY,CAAC;MAChD;MAED,IAAI,CAACyM,OAAO,CAACkQ,WAAW,EAAE;QACxBlK,KAAK,CAACjO,UAAU,CAACM,aAAa,EAAE9G,IAAI,CAAC;MACtC;MAED,IAAI,CAACyO,OAAO,CAACiQ,SAAS,EAAE;QACtBjK,KAAK,CAACjO,UAAU,CAACK,WAAW,EAAE7G,IAAI,CAAC;QACnCwG,UAAU,CAACG,OAAO,GAAG8H,OAAO,CAACzM,YAAY,GACrCmZ,SAAS,CAACnb,IAAI,EAAE6B,GAAG,CAAC2C,cAAc,EAAExE,IAAI,CAAC,CAAC,GAC1Cmb,SAAS,EAAE;MAChB;MAED,IAAI,CAAC1M,OAAO,CAACgQ,SAAS,EAAE;QACtBhK,KAAK,CAACjO,UAAU,CAACS,MAAM,EAAEjH,IAAI,CAAC;QAC9B4E,eAAe,CAACoC,OAAO,IAAIM,YAAY,EAAE;MAC1C;MAEDF,SAAS,CAACC,KAAK,CAAClB,IAAI,CAAC;QAAE,GAAGK;MAAU,CAAE,CAAC;IACxC;EACH,CAAC;EAED,MAAMkZ,MAAM,GAA+B,SAAAA,CACzChY,UAAU,EAER;IAAA,IADFiY,gBAAgB,GAAAtb,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAzC,SAAA,GAAAyC,SAAA,MAAG,EAAE;IAErB,MAAMub,aAAa,GAAGlY,UAAU,IAAIlD,cAAc;IAClD,MAAMqb,kBAAkB,GAAG7e,WAAW,CAAC4e,aAAa,CAAC;IACrD,MAAM1X,MAAM,GACVR,UAAU,IAAI,CAAC7C,aAAa,CAAC6C,UAAU,CAAC,GACpCmY,kBAAkB,GAClBrb,cAAc;IAEpB,IAAI,CAACmb,gBAAgB,CAACG,iBAAiB,EAAE;MACvCtb,cAAc,GAAGob,aAAa;IAC/B;IAED,IAAI,CAACD,gBAAgB,CAACI,UAAU,EAAE;MAChC,IAAIJ,gBAAgB,CAACtF,eAAe,IAAIF,wBAAwB,EAAE;QAChE,KAAK,MAAMpS,SAAS,IAAIN,MAAM,CAACwC,KAAK,EAAE;UACpCpI,GAAG,CAAC2E,UAAU,CAACK,WAAW,EAAEkB,SAAS,CAAC,GAClCa,GAAG,CAACV,MAAM,EAAEH,SAAS,EAAElG,GAAG,CAACuG,WAAW,EAAEL,SAAS,CAAC,CAAC,GACnDyV,QAAQ,CACNzV,SAAoC,EACpClG,GAAG,CAACqG,MAAM,EAAEH,SAAS,CAAC,CACvB;QACN;MACF,OAAM;QACL,IAAInH,KAAK,IAAIc,WAAW,CAACgG,UAAU,CAAC,EAAE;UACpC,KAAK,MAAM1H,IAAI,IAAIyH,MAAM,CAACwC,KAAK,EAAE;YAC/B,MAAMH,KAAK,GAAGjI,GAAG,CAACkI,OAAO,EAAE/J,IAAI,CAAC;YAChC,IAAI8J,KAAK,IAAIA,KAAK,CAACE,EAAE,EAAE;cACrB,MAAM6S,cAAc,GAAGpd,KAAK,CAACC,OAAO,CAACoK,KAAK,CAACE,EAAE,CAAC8D,IAAI,CAAC,GAC/ChE,KAAK,CAACE,EAAE,CAAC8D,IAAI,CAAC,CAAC,CAAC,GAChBhE,KAAK,CAACE,EAAE,CAACM,GAAG;cAEhB,IAAIuF,aAAa,CAACgN,cAAc,CAAC,EAAE;gBACjC,MAAMmD,IAAI,GAAGnD,cAAc,CAACoD,OAAO,CAAC,MAAM,CAAC;gBAC3C,IAAID,IAAI,EAAE;kBACRA,IAAI,CAACE,KAAK,EAAE;kBACZ;gBACD;cACF;YACF;UACF;QACF;QAEDnW,OAAO,GAAG,EAAE;MACb;MAED3B,WAAW,GAAGxE,KAAK,CAACyF,gBAAgB,GAChCsW,gBAAgB,CAACG,iBAAiB,GAChC9e,WAAW,CAACwD,cAAc,CAAC,GAC3B,EAAE,GACJxD,WAAW,CAACkH,MAAM,CAAC;MAEvBd,SAAS,CAACmC,KAAK,CAACpD,IAAI,CAAC;QACnB+B,MAAM,EAAE;UAAE,GAAGA;QAAM;MACpB,EAAC;MAEFd,SAAS,CAACc,MAAM,CAAC/B,IAAI,CAAC;QACpB+B,MAAM,EAAE;UAAE,GAAGA;QAAM;MACpB,EAAC;IACH;IAEDT,MAAM,GAAG;MACPwC,KAAK,EAAE,IAAI9I,GAAG,EAAE;MAChB6Y,OAAO,EAAE,IAAI7Y,GAAG,EAAE;MAClBoI,KAAK,EAAE,IAAIpI,GAAG,EAAE;MAChByG,KAAK,EAAE,IAAIzG,GAAG,EAAE;MAChB6G,QAAQ,EAAE,KAAK;MACfwC,KAAK,EAAE;KACR;IAED,CAACN,MAAM,CAACD,KAAK,IAAI4P,eAAe,EAAE;IAElC3P,MAAM,CAACD,KAAK,GAAG,CAACrF,eAAe,CAACoC,OAAO,IAAI,CAAC,CAAC2Y,gBAAgB,CAACd,WAAW;IAEzE3U,MAAM,CAACtC,KAAK,GAAG,CAAC,CAAChE,KAAK,CAACyF,gBAAgB;IAEvCjC,SAAS,CAACC,KAAK,CAAClB,IAAI,CAAC;MACnB2T,WAAW,EAAE6F,gBAAgB,CAACQ,eAAe,GACzC3Z,UAAU,CAACsT,WAAW,GACtB,CAAC;MACLnT,OAAO,EAAEgZ,gBAAgB,CAACjB,SAAS,GAC/BlY,UAAU,CAACG,OAAO,GAClB,CAAC,EACCgZ,gBAAgB,CAACG,iBAAiB,IAClC,CAACzI,SAAS,CAAC3P,UAAU,EAAElD,cAAc,CAAC,CACvC;MACL+R,WAAW,EAAEoJ,gBAAgB,CAACS,eAAe,GACzC5Z,UAAU,CAAC+P,WAAW,GACtB,KAAK;MACT1P,WAAW,EAAE8Y,gBAAgB,CAACtF,eAAe,GACzC7T,UAAU,CAACK,WAAW,GACtB8Y,gBAAgB,CAACG,iBAAiB,IAAIpY,UAAU,GAChD2Q,cAAc,CAAC7T,cAAc,EAAEkD,UAAU,CAAC,GAC1C,EAAE;MACNZ,aAAa,EAAE6Y,gBAAgB,CAAChB,WAAW,GACvCnY,UAAU,CAACM,aAAa,GACxB,EAAE;MACNG,MAAM,EAAE0Y,gBAAgB,CAACU,UAAU,GAAG7Z,UAAU,CAACS,MAAM,GAAG,EAAE;MAC5D8S,YAAY,EAAE,KAAK;MACnB7M,kBAAkB,EAAE;IACrB,EAAC;EACJ,CAAC;EAED,MAAMgT,KAAK,GAA+BA,CAACxY,UAAU,EAAEiY,gBAAgB,KACrED,MAAM,CACJ9P,UAAU,CAAClI,UAAU,CAAC,GAClBA,UAAU,CAACU,WAA2B,CAAC,GACvCV,UAAU,EACdiY,gBAAgB,CACjB;EAEH,MAAMW,QAAQ,GAAkC,SAAAA,CAACtgB,IAAI,EAAkB;IAAA,IAAhByO,OAAO,GAAApK,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAzC,SAAA,GAAAyC,SAAA,MAAG,EAAE;IACjE,MAAMyF,KAAK,GAAGjI,GAAG,CAACkI,OAAO,EAAE/J,IAAI,CAAC;IAChC,MAAM6c,cAAc,GAAG/S,KAAK,IAAIA,KAAK,CAACE,EAAE;IAExC,IAAI6S,cAAc,EAAE;MAClB,MAAMmC,QAAQ,GAAGnC,cAAc,CAAC/O,IAAI,GAChC+O,cAAc,CAAC/O,IAAI,CAAC,CAAC,CAAC,GACtB+O,cAAc,CAACvS,GAAG;MAEtB,IAAI0U,QAAQ,CAACxU,KAAK,EAAE;QAClBwU,QAAQ,CAACxU,KAAK,EAAE;QAChBiE,OAAO,CAAC8R,YAAY,IAAIvB,QAAQ,CAACvU,MAAM,EAAE;MAC1C;IACF;EACH,CAAC;EAED,MAAMtD,gBAAgB,GACpBkV,gBAAkD,IAChD;IACF7V,UAAU,GAAG;MACX,GAAGA,UAAU;MACb,GAAG6V;KACJ;EACH,CAAC;EAED,MAAMmE,mBAAmB,GAAGA,CAAA,KAC1B5Q,UAAU,CAAChG,QAAQ,CAACrF,aAAa,CAAC,IAClCqF,QAAQ,CAACrF,aAAa,EAAE,CAACmS,IAAI,CAAExO,MAAM,IAAI;IACvCgY,KAAK,CAAChY,MAAM,EAAE0B,QAAQ,CAACwQ,YAAY,CAAC;IACpChT,SAAS,CAACC,KAAK,CAAClB,IAAI,CAAC;MACnBS,SAAS,EAAE;IACZ,EAAC;EACJ,CAAC,CAAC;EAEJ,OAAO;IACL1C,OAAO,EAAE;MACPuF,QAAQ;MACRW,UAAU;MACV+T,aAAa;MACbhS,YAAY;MACZgB,QAAQ;MACRsJ,cAAc;MACdpO,SAAS;MACT8S,SAAS;MACT7T,YAAY;MACZgB,gBAAgB;MAChBmN,iBAAiB;MACjBR,cAAc;MACdyK,MAAM;MACNc,mBAAmB;MACnBrZ,gBAAgB;MAChBC,SAAS;MACTxC,eAAe;MACf,IAAImF,OAAOA,CAAA;QACT,OAAOA,OAAO;OACf;MACD,IAAI3B,WAAWA,CAAA;QACb,OAAOA,WAAW;OACnB;MACD,IAAI8B,MAAMA,CAAA;QACR,OAAOA,MAAM;OACd;MACD,IAAIA,MAAMA,CAAC9K,KAAK;QACd8K,MAAM,GAAG9K,KAAK;OACf;MACD,IAAIoF,cAAcA,CAAA;QAChB,OAAOA,cAAc;OACtB;MACD,IAAIiD,MAAMA,CAAA;QACR,OAAOA,MAAM;OACd;MACD,IAAIA,MAAMA,CAACrI,KAAK;QACdqI,MAAM,GAAGrI,KAAK;OACf;MACD,IAAIoH,UAAUA,CAAA;QACZ,OAAOA,UAAU;OAClB;MACD,IAAIA,UAAUA,CAACpH,KAAK;QAClBoH,UAAU,GAAGpH,KAAK;OACnB;MACD,IAAIwK,QAAQA,CAAA;QACV,OAAOA,QAAQ;OAChB;MACD,IAAIA,QAAQA,CAACxK,KAAK;QAChBwK,QAAQ,GAAG;UACT,GAAGA,QAAQ;UACX,GAAGxK;SACJ;;IAEJ;IACDie,OAAO;IACP5T,QAAQ;IACR0C,YAAY;IACZvE,KAAK;IACL4V,QAAQ;IACRZ,SAAS;IACTsD,KAAK;IACLT,UAAU;IACVrB,WAAW;IACXhU,UAAU;IACV+C,QAAQ;IACRmT,QAAQ;IACRnC;GACD;AACH;;ACtyCA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BG;AACa,SAAAsC,OAAOA,CAAA,EAK2B;EAAA,IAAhD7c,KAAA,GAAAS,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAzC,SAAA,GAAAyC,SAAA,MAA8C,EAAE;EAEhD,MAAMqc,YAAY,GAAGnd,cAAK,CAACqC,MAAM,EAE9B;EACH,MAAM,CAAC3B,SAAS,EAAEgB,eAAe,CAAC,GAAG1B,cAAK,CAACgD,QAAQ,CAA0B;IAC3EI,OAAO,EAAE,KAAK;IACdI,YAAY,EAAE,KAAK;IACnBH,SAAS,EAAEgJ,UAAU,CAAChM,KAAK,CAACW,aAAa,CAAC;IAC1CgS,WAAW,EAAE,KAAK;IAClBwD,YAAY,EAAE,KAAK;IACnB7M,kBAAkB,EAAE,KAAK;IACzBlG,OAAO,EAAE,KAAK;IACd8S,WAAW,EAAE,CAAC;IACdjT,WAAW,EAAE,EAAE;IACfC,aAAa,EAAE,EAAE;IACjBG,MAAM,EAAE,EAAE;IACV1C,aAAa,EAAEqL,UAAU,CAAChM,KAAK,CAACW,aAAa,CAAC,GAC1C3C,SAAS,GACTgC,KAAK,CAACW;EACX,EAAC;EAEF,IAAI,CAACmc,YAAY,CAAC7a,OAAO,EAAE;IACzB6a,YAAY,CAAC7a,OAAO,GAAG;MACrB,GAAG+T,iBAAiB,CAAChW,KAAK,EAAE,MAC1BqB,eAAe,CAAEhB,SAAS,KAAM;QAAE,GAAGA;MAAS,CAAE,CAAC,CAAC,CACnD;MACDA;KACD;EACF;EAED,MAAMC,OAAO,GAAGwc,YAAY,CAAC7a,OAAO,CAAC3B,OAAO;EAC5CA,OAAO,CAAC0F,QAAQ,GAAGhG,KAAK;EAExB8B,YAAY,CAAC;IACXO,OAAO,EAAE/B,OAAO,CAACkD,SAAS,CAACC,KAAK;IAChClB,IAAI,EACF/G,KAAsE,IACpE;MACF,IACE2F,qBAAqB,CACnB3F,KAAK,EACL8E,OAAO,CAACU,eAAe,EACvBV,OAAO,CAACiD,gBAAgB,EACxB,IAAI,CACL,EACD;QACAlC,eAAe,CAAC;UAAE,GAAGf,OAAO,CAACsC;QAAU,CAAE,CAAC;MAC3C;;EAEJ,EAAC;EAEFjD,cAAK,CAACuC,SAAS,CAAC,MAAK;IACnB,IAAIlC,KAAK,CAACsE,MAAM,IAAI,CAACmP,SAAS,CAACzT,KAAK,CAACsE,MAAM,EAAEhE,OAAO,CAACM,cAAc,CAAC,EAAE;MACpEN,OAAO,CAACwb,MAAM,CAAC9b,KAAK,CAACsE,MAAM,EAAEhE,OAAO,CAAC0F,QAAQ,CAACwQ,YAAY,CAAC;IAC5D,OAAM;MACLlW,OAAO,CAACsc,mBAAmB,EAAE;IAC9B;GACF,EAAE,CAAC5c,KAAK,CAACsE,MAAM,EAAEhE,OAAO,CAAC,CAAC;EAE3BX,cAAK,CAACuC,SAAS,CAAC,MAAK;IACnB,IAAI,CAAC5B,OAAO,CAACgG,MAAM,CAACD,KAAK,EAAE;MACzB/F,OAAO,CAACoD,YAAY,EAAE;MACtBpD,OAAO,CAACgG,MAAM,CAACD,KAAK,GAAG,IAAI;IAC5B;IAED,IAAI/F,OAAO,CAACgG,MAAM,CAACtC,KAAK,EAAE;MACxB1D,OAAO,CAACgG,MAAM,CAACtC,KAAK,GAAG,KAAK;MAC5B1D,OAAO,CAACkD,SAAS,CAACC,KAAK,CAAClB,IAAI,CAAC;QAAE,GAAGjC,OAAO,CAACsC;MAAU,CAAE,CAAC;IACxD;IAEDtC,OAAO,CAACoE,gBAAgB,EAAE;EAC5B,CAAC,CAAC;EAEFoY,YAAY,CAAC7a,OAAO,CAAC5B,SAAS,GAAGD,iBAAiB,CAACC,SAAS,EAAEC,OAAO,CAAC;EAEtE,OAAOwc,YAAY,CAAC7a,OAAO;AAC7B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}