{"ast": null, "code": "import { extend } from '../shared/utils.js';\nexport default function moduleExtendParams(params, allModulesParams) {\n  return function extendParams() {\n    let obj = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    const moduleParamName = Object.keys(obj)[0];\n    const moduleParams = obj[moduleParamName];\n    if (typeof moduleParams !== 'object' || moduleParams === null) {\n      extend(allModulesParams, obj);\n      return;\n    }\n    if (['navigation', 'pagination', 'scrollbar'].indexOf(moduleParamName) >= 0 && params[moduleParamName] === true) {\n      params[moduleParamName] = {\n        auto: true\n      };\n    }\n    if (!(moduleParamName in params && 'enabled' in moduleParams)) {\n      extend(allModulesParams, obj);\n      return;\n    }\n    if (params[moduleParamName] === true) {\n      params[moduleParamName] = {\n        enabled: true\n      };\n    }\n    if (typeof params[moduleParamName] === 'object' && !('enabled' in params[moduleParamName])) {\n      params[moduleParamName].enabled = true;\n    }\n    if (!params[moduleParamName]) params[moduleParamName] = {\n      enabled: false\n    };\n    extend(allModulesParams, obj);\n  };\n}", "map": {"version": 3, "names": ["extend", "moduleExtendParams", "params", "allModulesParams", "extendParams", "obj", "arguments", "length", "undefined", "moduleParamName", "Object", "keys", "moduleParams", "indexOf", "auto", "enabled"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/core/moduleExtendParams.js"], "sourcesContent": ["import { extend } from '../shared/utils.js';\nexport default function moduleExtendParams(params, allModulesParams) {\n  return function extendParams(obj = {}) {\n    const moduleParamName = Object.keys(obj)[0];\n    const moduleParams = obj[moduleParamName];\n    if (typeof moduleParams !== 'object' || moduleParams === null) {\n      extend(allModulesParams, obj);\n      return;\n    }\n    if (['navigation', 'pagination', 'scrollbar'].indexOf(moduleParamName) >= 0 && params[moduleParamName] === true) {\n      params[moduleParamName] = {\n        auto: true\n      };\n    }\n    if (!(moduleParamName in params && 'enabled' in moduleParams)) {\n      extend(allModulesParams, obj);\n      return;\n    }\n    if (params[moduleParamName] === true) {\n      params[moduleParamName] = {\n        enabled: true\n      };\n    }\n    if (typeof params[moduleParamName] === 'object' && !('enabled' in params[moduleParamName])) {\n      params[moduleParamName].enabled = true;\n    }\n    if (!params[moduleParamName]) params[moduleParamName] = {\n      enabled: false\n    };\n    extend(allModulesParams, obj);\n  };\n}"], "mappings": "AAAA,SAASA,MAAM,QAAQ,oBAAoB;AAC3C,eAAe,SAASC,kBAAkBA,CAACC,MAAM,EAAEC,gBAAgB,EAAE;EACnE,OAAO,SAASC,YAAYA,CAAA,EAAW;IAAA,IAAVC,GAAG,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IACnC,MAAMG,eAAe,GAAGC,MAAM,CAACC,IAAI,CAACN,GAAG,CAAC,CAAC,CAAC,CAAC;IAC3C,MAAMO,YAAY,GAAGP,GAAG,CAACI,eAAe,CAAC;IACzC,IAAI,OAAOG,YAAY,KAAK,QAAQ,IAAIA,YAAY,KAAK,IAAI,EAAE;MAC7DZ,MAAM,CAACG,gBAAgB,EAAEE,GAAG,CAAC;MAC7B;IACF;IACA,IAAI,CAAC,YAAY,EAAE,YAAY,EAAE,WAAW,CAAC,CAACQ,OAAO,CAACJ,eAAe,CAAC,IAAI,CAAC,IAAIP,MAAM,CAACO,eAAe,CAAC,KAAK,IAAI,EAAE;MAC/GP,MAAM,CAACO,eAAe,CAAC,GAAG;QACxBK,IAAI,EAAE;MACR,CAAC;IACH;IACA,IAAI,EAAEL,eAAe,IAAIP,MAAM,IAAI,SAAS,IAAIU,YAAY,CAAC,EAAE;MAC7DZ,MAAM,CAACG,gBAAgB,EAAEE,GAAG,CAAC;MAC7B;IACF;IACA,IAAIH,MAAM,CAACO,eAAe,CAAC,KAAK,IAAI,EAAE;MACpCP,MAAM,CAACO,eAAe,CAAC,GAAG;QACxBM,OAAO,EAAE;MACX,CAAC;IACH;IACA,IAAI,OAAOb,MAAM,CAACO,eAAe,CAAC,KAAK,QAAQ,IAAI,EAAE,SAAS,IAAIP,MAAM,CAACO,eAAe,CAAC,CAAC,EAAE;MAC1FP,MAAM,CAACO,eAAe,CAAC,CAACM,OAAO,GAAG,IAAI;IACxC;IACA,IAAI,CAACb,MAAM,CAACO,eAAe,CAAC,EAAEP,MAAM,CAACO,eAAe,CAAC,GAAG;MACtDM,OAAO,EAAE;IACX,CAAC;IACDf,MAAM,CAACG,gBAAgB,EAAEE,GAAG,CAAC;EAC/B,CAAC;AACH"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}