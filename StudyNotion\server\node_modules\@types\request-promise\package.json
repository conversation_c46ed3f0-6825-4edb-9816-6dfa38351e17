{"name": "@types/request-promise", "version": "4.1.48", "description": "TypeScript definitions for request-promise", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/request-promise", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/cglantschnig", "githubUsername": "cglants<PERSON>nig"}, {"name": "<PERSON>", "url": "https://github.com/joeskeen", "githubUsername": "j<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/<PERSON>ya<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/mastermatt", "githubUsername": "mastermatt"}, {"name": "<PERSON>", "url": "https://github.com/vansergen", "githubUsername": "vansergen"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/request-promise"}, "scripts": {}, "dependencies": {"@types/bluebird": "*", "@types/request": "*"}, "typesPublisherContentHash": "3ac8cacbfa54ee9d083c83600938ad012ce85644c3b28b3640d99cc98ccafb09", "typeScriptVersion": "3.6"}