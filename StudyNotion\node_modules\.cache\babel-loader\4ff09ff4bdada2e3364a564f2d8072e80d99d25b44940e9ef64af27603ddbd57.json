{"ast": null, "code": "/**\n * @typedef {import('mdast').Root} Root\n * @typedef {import('mdast').Content} Content\n * @typedef {import('mdast').Definition} Definition\n */\n\n/**\n * @typedef {Root | Content} Node\n *\n * @callback GetDefinition\n *   Get a definition by identifier.\n * @param {string | null | undefined} [identifier]\n *   Identifier of definition.\n * @returns {Definition | null}\n *   Definition corresponding to `identifier` or `null`.\n */\n\nimport { visit } from 'unist-util-visit';\nconst own = {}.hasOwnProperty;\n\n/**\n * Find definitions in `tree`.\n *\n * Uses CommonMark precedence, which means that earlier definitions are\n * preferred over duplicate later definitions.\n *\n * @param {Node} tree\n *   Tree to check.\n * @returns {GetDefinition}\n *   Getter.\n */\nexport function definitions(tree) {\n  /** @type {Record<string, Definition>} */\n  const cache = Object.create(null);\n  if (!tree || !tree.type) {\n    throw new Error('mdast-util-definitions expected node');\n  }\n  visit(tree, 'definition', definition => {\n    const id = clean(definition.identifier);\n    if (id && !own.call(cache, id)) {\n      cache[id] = definition;\n    }\n  });\n  return definition;\n\n  /** @type {GetDefinition} */\n  function definition(identifier) {\n    const id = clean(identifier);\n    // To do: next major: return `undefined` when not found.\n    return id && own.call(cache, id) ? cache[id] : null;\n  }\n}\n\n/**\n * @param {string | null | undefined} [value]\n * @returns {string}\n */\nfunction clean(value) {\n  return String(value || '').toUpperCase();\n}", "map": {"version": 3, "names": ["visit", "own", "hasOwnProperty", "definitions", "tree", "cache", "Object", "create", "type", "Error", "definition", "id", "clean", "identifier", "call", "value", "String", "toUpperCase"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/mdast-util-definitions/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('mdast').Root} Root\n * @typedef {import('mdast').Content} Content\n * @typedef {import('mdast').Definition} Definition\n */\n\n/**\n * @typedef {Root | Content} Node\n *\n * @callback GetDefinition\n *   Get a definition by identifier.\n * @param {string | null | undefined} [identifier]\n *   Identifier of definition.\n * @returns {Definition | null}\n *   Definition corresponding to `identifier` or `null`.\n */\n\nimport {visit} from 'unist-util-visit'\n\nconst own = {}.hasOwnProperty\n\n/**\n * Find definitions in `tree`.\n *\n * Uses CommonMark precedence, which means that earlier definitions are\n * preferred over duplicate later definitions.\n *\n * @param {Node} tree\n *   Tree to check.\n * @returns {GetDefinition}\n *   Getter.\n */\nexport function definitions(tree) {\n  /** @type {Record<string, Definition>} */\n  const cache = Object.create(null)\n\n  if (!tree || !tree.type) {\n    throw new Error('mdast-util-definitions expected node')\n  }\n\n  visit(tree, 'definition', (definition) => {\n    const id = clean(definition.identifier)\n    if (id && !own.call(cache, id)) {\n      cache[id] = definition\n    }\n  })\n\n  return definition\n\n  /** @type {GetDefinition} */\n  function definition(identifier) {\n    const id = clean(identifier)\n    // To do: next major: return `undefined` when not found.\n    return id && own.call(cache, id) ? cache[id] : null\n  }\n}\n\n/**\n * @param {string | null | undefined} [value]\n * @returns {string}\n */\nfunction clean(value) {\n  return String(value || '').toUpperCase()\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,KAAK,QAAO,kBAAkB;AAEtC,MAAMC,GAAG,GAAG,CAAC,CAAC,CAACC,cAAc;;AAE7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAACC,IAAI,EAAE;EAChC;EACA,MAAMC,KAAK,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EAEjC,IAAI,CAACH,IAAI,IAAI,CAACA,IAAI,CAACI,IAAI,EAAE;IACvB,MAAM,IAAIC,KAAK,CAAC,sCAAsC,CAAC;EACzD;EAEAT,KAAK,CAACI,IAAI,EAAE,YAAY,EAAGM,UAAU,IAAK;IACxC,MAAMC,EAAE,GAAGC,KAAK,CAACF,UAAU,CAACG,UAAU,CAAC;IACvC,IAAIF,EAAE,IAAI,CAACV,GAAG,CAACa,IAAI,CAACT,KAAK,EAAEM,EAAE,CAAC,EAAE;MAC9BN,KAAK,CAACM,EAAE,CAAC,GAAGD,UAAU;IACxB;EACF,CAAC,CAAC;EAEF,OAAOA,UAAU;;EAEjB;EACA,SAASA,UAAUA,CAACG,UAAU,EAAE;IAC9B,MAAMF,EAAE,GAAGC,KAAK,CAACC,UAAU,CAAC;IAC5B;IACA,OAAOF,EAAE,IAAIV,GAAG,CAACa,IAAI,CAACT,KAAK,EAAEM,EAAE,CAAC,GAAGN,KAAK,CAACM,EAAE,CAAC,GAAG,IAAI;EACrD;AACF;;AAEA;AACA;AACA;AACA;AACA,SAASC,KAAKA,CAACG,KAAK,EAAE;EACpB,OAAOC,MAAM,CAACD,KAAK,IAAI,EAAE,CAAC,CAACE,WAAW,EAAE;AAC1C"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}