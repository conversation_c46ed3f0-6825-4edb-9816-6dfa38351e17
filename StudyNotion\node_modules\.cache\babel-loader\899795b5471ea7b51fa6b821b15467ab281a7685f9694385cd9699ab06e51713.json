{"ast": null, "code": "import effectInit from '../../shared/effect-init.js';\nimport effectTarget from '../../shared/effect-target.js';\nimport effectVirtualTransitionEnd from '../../shared/effect-virtual-transition-end.js';\nimport { getSlideTransformEl } from '../../shared/utils.js';\nexport default function EffectFade(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on\n  } = _ref;\n  extendParams({\n    fadeEffect: {\n      crossFade: false\n    }\n  });\n  const setTranslate = () => {\n    const {\n      slides\n    } = swiper;\n    const params = swiper.params.fadeEffect;\n    for (let i = 0; i < slides.length; i += 1) {\n      const slideEl = swiper.slides[i];\n      const offset = slideEl.swiperSlideOffset;\n      let tx = -offset;\n      if (!swiper.params.virtualTranslate) tx -= swiper.translate;\n      let ty = 0;\n      if (!swiper.isHorizontal()) {\n        ty = tx;\n        tx = 0;\n      }\n      const slideOpacity = swiper.params.fadeEffect.crossFade ? Math.max(1 - Math.abs(slideEl.progress), 0) : 1 + Math.min(Math.max(slideEl.progress, -1), 0);\n      const targetEl = effectTarget(params, slideEl);\n      targetEl.style.opacity = slideOpacity;\n      targetEl.style.transform = `translate3d(${tx}px, ${ty}px, 0px)`;\n    }\n  };\n  const setTransition = duration => {\n    const transformElements = swiper.slides.map(slideEl => getSlideTransformEl(slideEl));\n    transformElements.forEach(el => {\n      el.style.transitionDuration = `${duration}ms`;\n    });\n    effectVirtualTransitionEnd({\n      swiper,\n      duration,\n      transformElements,\n      allSlides: true\n    });\n  };\n  effectInit({\n    effect: 'fade',\n    swiper,\n    on,\n    setTranslate,\n    setTransition,\n    overwriteParams: () => ({\n      slidesPerView: 1,\n      slidesPerGroup: 1,\n      watchSlidesProgress: true,\n      spaceBetween: 0,\n      virtualTranslate: !swiper.params.cssMode\n    })\n  });\n}", "map": {"version": 3, "names": ["effectInit", "effect<PERSON>arget", "effectVirtualTransitionEnd", "getSlideTransformEl", "EffectFade", "_ref", "swiper", "extendParams", "on", "fadeEffect", "crossFade", "setTranslate", "slides", "params", "i", "length", "slideEl", "offset", "swiperSlideOffset", "tx", "virtualTranslate", "translate", "ty", "isHorizontal", "slideOpacity", "Math", "max", "abs", "progress", "min", "targetEl", "style", "opacity", "transform", "setTransition", "duration", "transformElements", "map", "for<PERSON>ach", "el", "transitionDuration", "allSlides", "effect", "overwriteParams", "<PERSON><PERSON><PERSON><PERSON>iew", "slidesPerGroup", "watchSlidesProgress", "spaceBetween", "cssMode"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/modules/effect-fade/effect-fade.js"], "sourcesContent": ["import effectInit from '../../shared/effect-init.js';\nimport effectTarget from '../../shared/effect-target.js';\nimport effectVirtualTransitionEnd from '../../shared/effect-virtual-transition-end.js';\nimport { getSlideTransformEl } from '../../shared/utils.js';\nexport default function EffectFade({\n  swiper,\n  extendParams,\n  on\n}) {\n  extendParams({\n    fadeEffect: {\n      crossFade: false\n    }\n  });\n  const setTranslate = () => {\n    const {\n      slides\n    } = swiper;\n    const params = swiper.params.fadeEffect;\n    for (let i = 0; i < slides.length; i += 1) {\n      const slideEl = swiper.slides[i];\n      const offset = slideEl.swiperSlideOffset;\n      let tx = -offset;\n      if (!swiper.params.virtualTranslate) tx -= swiper.translate;\n      let ty = 0;\n      if (!swiper.isHorizontal()) {\n        ty = tx;\n        tx = 0;\n      }\n      const slideOpacity = swiper.params.fadeEffect.crossFade ? Math.max(1 - Math.abs(slideEl.progress), 0) : 1 + Math.min(Math.max(slideEl.progress, -1), 0);\n      const targetEl = effectTarget(params, slideEl);\n      targetEl.style.opacity = slideOpacity;\n      targetEl.style.transform = `translate3d(${tx}px, ${ty}px, 0px)`;\n    }\n  };\n  const setTransition = duration => {\n    const transformElements = swiper.slides.map(slideEl => getSlideTransformEl(slideEl));\n    transformElements.forEach(el => {\n      el.style.transitionDuration = `${duration}ms`;\n    });\n    effectVirtualTransitionEnd({\n      swiper,\n      duration,\n      transformElements,\n      allSlides: true\n    });\n  };\n  effectInit({\n    effect: 'fade',\n    swiper,\n    on,\n    setTranslate,\n    setTransition,\n    overwriteParams: () => ({\n      slidesPerView: 1,\n      slidesPerGroup: 1,\n      watchSlidesProgress: true,\n      spaceBetween: 0,\n      virtualTranslate: !swiper.params.cssMode\n    })\n  });\n}"], "mappings": "AAAA,OAAOA,UAAU,MAAM,6BAA6B;AACpD,OAAOC,YAAY,MAAM,+BAA+B;AACxD,OAAOC,0BAA0B,MAAM,+CAA+C;AACtF,SAASC,mBAAmB,QAAQ,uBAAuB;AAC3D,eAAe,SAASC,UAAUA,CAAAC,IAAA,EAI/B;EAAA,IAJgC;IACjCC,MAAM;IACNC,YAAY;IACZC;EACF,CAAC,GAAAH,IAAA;EACCE,YAAY,CAAC;IACXE,UAAU,EAAE;MACVC,SAAS,EAAE;IACb;EACF,CAAC,CAAC;EACF,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAM;MACJC;IACF,CAAC,GAAGN,MAAM;IACV,MAAMO,MAAM,GAAGP,MAAM,CAACO,MAAM,CAACJ,UAAU;IACvC,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,MAAM,CAACG,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;MACzC,MAAME,OAAO,GAAGV,MAAM,CAACM,MAAM,CAACE,CAAC,CAAC;MAChC,MAAMG,MAAM,GAAGD,OAAO,CAACE,iBAAiB;MACxC,IAAIC,EAAE,GAAG,CAACF,MAAM;MAChB,IAAI,CAACX,MAAM,CAACO,MAAM,CAACO,gBAAgB,EAAED,EAAE,IAAIb,MAAM,CAACe,SAAS;MAC3D,IAAIC,EAAE,GAAG,CAAC;MACV,IAAI,CAAChB,MAAM,CAACiB,YAAY,EAAE,EAAE;QAC1BD,EAAE,GAAGH,EAAE;QACPA,EAAE,GAAG,CAAC;MACR;MACA,MAAMK,YAAY,GAAGlB,MAAM,CAACO,MAAM,CAACJ,UAAU,CAACC,SAAS,GAAGe,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGD,IAAI,CAACE,GAAG,CAACX,OAAO,CAACY,QAAQ,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,GAAGH,IAAI,CAACI,GAAG,CAACJ,IAAI,CAACC,GAAG,CAACV,OAAO,CAACY,QAAQ,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACvJ,MAAME,QAAQ,GAAG7B,YAAY,CAACY,MAAM,EAAEG,OAAO,CAAC;MAC9Cc,QAAQ,CAACC,KAAK,CAACC,OAAO,GAAGR,YAAY;MACrCM,QAAQ,CAACC,KAAK,CAACE,SAAS,GAAI,eAAcd,EAAG,OAAMG,EAAG,UAAS;IACjE;EACF,CAAC;EACD,MAAMY,aAAa,GAAGC,QAAQ,IAAI;IAChC,MAAMC,iBAAiB,GAAG9B,MAAM,CAACM,MAAM,CAACyB,GAAG,CAACrB,OAAO,IAAIb,mBAAmB,CAACa,OAAO,CAAC,CAAC;IACpFoB,iBAAiB,CAACE,OAAO,CAACC,EAAE,IAAI;MAC9BA,EAAE,CAACR,KAAK,CAACS,kBAAkB,GAAI,GAAEL,QAAS,IAAG;IAC/C,CAAC,CAAC;IACFjC,0BAA0B,CAAC;MACzBI,MAAM;MACN6B,QAAQ;MACRC,iBAAiB;MACjBK,SAAS,EAAE;IACb,CAAC,CAAC;EACJ,CAAC;EACDzC,UAAU,CAAC;IACT0C,MAAM,EAAE,MAAM;IACdpC,MAAM;IACNE,EAAE;IACFG,YAAY;IACZuB,aAAa;IACbS,eAAe,EAAEA,CAAA,MAAO;MACtBC,aAAa,EAAE,CAAC;MAChBC,cAAc,EAAE,CAAC;MACjBC,mBAAmB,EAAE,IAAI;MACzBC,YAAY,EAAE,CAAC;MACf3B,gBAAgB,EAAE,CAACd,MAAM,CAACO,MAAM,CAACmC;IACnC,CAAC;EACH,CAAC,CAAC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}