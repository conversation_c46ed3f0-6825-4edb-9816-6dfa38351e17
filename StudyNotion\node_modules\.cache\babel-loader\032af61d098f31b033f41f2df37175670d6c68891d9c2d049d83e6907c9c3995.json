{"ast": null, "code": "/*!\n * Determine if an object is a Buffer\n *\n * <AUTHOR> <https://feross.org>\n * @license  MIT\n */\n\nmodule.exports = function isBuffer(obj) {\n  return obj != null && obj.constructor != null && typeof obj.constructor.isBuffer === 'function' && obj.constructor.isBuffer(obj);\n};", "map": {"version": 3, "names": ["module", "exports", "<PERSON><PERSON><PERSON><PERSON>", "obj", "constructor"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/is-buffer/index.js"], "sourcesContent": ["/*!\n * Determine if an object is a Buffer\n *\n * <AUTHOR> <https://feross.org>\n * @license  MIT\n */\n\nmodule.exports = function isBuffer (obj) {\n  return obj != null && obj.constructor != null &&\n    typeof obj.constructor.isBuffer === 'function' && obj.constructor.isBuffer(obj)\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEAA,MAAM,CAACC,OAAO,GAAG,SAASC,QAAQA,CAAEC,GAAG,EAAE;EACvC,OAAOA,GAAG,IAAI,IAAI,IAAIA,GAAG,CAACC,WAAW,IAAI,IAAI,IAC3C,OAAOD,GAAG,CAACC,WAAW,CAACF,QAAQ,KAAK,UAAU,IAAIC,GAAG,CAACC,WAAW,CAACF,QAAQ,CAACC,GAAG,CAAC;AACnF,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}