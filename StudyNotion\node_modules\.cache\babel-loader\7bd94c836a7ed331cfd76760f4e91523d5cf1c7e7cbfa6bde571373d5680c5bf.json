{"ast": null, "code": "/**\n * @typedef {import('micromark-util-types').Construct} Construct\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Tokenizer} Tokenizer\n */\n\nimport { factorySpace } from 'micromark-factory-space';\nimport { markdownLineEnding, markdownSpace } from 'micromark-util-character';\nimport { codes } from 'micromark-util-symbol/codes.js';\nimport { types } from 'micromark-util-symbol/types.js';\n\n/** @type {Construct} */\nexport const blankLine = {\n  tokenize: tokenizeBlankLine,\n  partial: true\n};\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeBlankLine(effects, ok, nok) {\n  return start;\n\n  /**\n   * Start of blank line.\n   *\n   * > 👉 **Note**: `␠` represents a space character.\n   *\n   * ```markdown\n   * > | ␠␠␊\n   *     ^\n   * > | ␊\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    return markdownSpace(code) ? factorySpace(effects, after, types.linePrefix)(code) : after(code);\n  }\n\n  /**\n   * At eof/eol, after optional whitespace.\n   *\n   * > 👉 **Note**: `␠` represents a space character.\n   *\n   * ```markdown\n   * > | ␠␠␊\n   *       ^\n   * > | ␊\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    return code === codes.eof || markdownLineEnding(code) ? ok(code) : nok(code);\n  }\n}", "map": {"version": 3, "names": ["factorySpace", "markdownLineEnding", "markdownSpace", "codes", "types", "blankLine", "tokenize", "tokenizeBlankLine", "partial", "effects", "ok", "nok", "start", "code", "after", "linePrefix", "eof"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/micromark-core-commonmark/dev/lib/blank-line.js"], "sourcesContent": ["/**\n * @typedef {import('micromark-util-types').Construct} Construct\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Tokenizer} Tokenizer\n */\n\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownLineEnding, markdownSpace} from 'micromark-util-character'\nimport {codes} from 'micromark-util-symbol/codes.js'\nimport {types} from 'micromark-util-symbol/types.js'\n\n/** @type {Construct} */\nexport const blankLine = {tokenize: tokenizeBlankLine, partial: true}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeBlankLine(effects, ok, nok) {\n  return start\n\n  /**\n   * Start of blank line.\n   *\n   * > 👉 **Note**: `␠` represents a space character.\n   *\n   * ```markdown\n   * > | ␠␠␊\n   *     ^\n   * > | ␊\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    return markdownSpace(code)\n      ? factorySpace(effects, after, types.linePrefix)(code)\n      : after(code)\n  }\n\n  /**\n   * At eof/eol, after optional whitespace.\n   *\n   * > 👉 **Note**: `␠` represents a space character.\n   *\n   * ```markdown\n   * > | ␠␠␊\n   *       ^\n   * > | ␊\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    return code === codes.eof || markdownLineEnding(code) ? ok(code) : nok(code)\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,YAAY,QAAO,yBAAyB;AACpD,SAAQC,kBAAkB,EAAEC,aAAa,QAAO,0BAA0B;AAC1E,SAAQC,KAAK,QAAO,gCAAgC;AACpD,SAAQC,KAAK,QAAO,gCAAgC;;AAEpD;AACA,OAAO,MAAMC,SAAS,GAAG;EAACC,QAAQ,EAAEC,iBAAiB;EAAEC,OAAO,EAAE;AAAI,CAAC;;AAErE;AACA;AACA;AACA;AACA,SAASD,iBAAiBA,CAACE,OAAO,EAAEC,EAAE,EAAEC,GAAG,EAAE;EAC3C,OAAOC,KAAK;;EAEZ;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,KAAKA,CAACC,IAAI,EAAE;IACnB,OAAOX,aAAa,CAACW,IAAI,CAAC,GACtBb,YAAY,CAACS,OAAO,EAAEK,KAAK,EAAEV,KAAK,CAACW,UAAU,CAAC,CAACF,IAAI,CAAC,GACpDC,KAAK,CAACD,IAAI,CAAC;EACjB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASC,KAAKA,CAACD,IAAI,EAAE;IACnB,OAAOA,IAAI,KAAKV,KAAK,CAACa,GAAG,IAAIf,kBAAkB,CAACY,IAAI,CAAC,GAAGH,EAAE,CAACG,IAAI,CAAC,GAAGF,GAAG,CAACE,IAAI,CAAC;EAC9E;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}