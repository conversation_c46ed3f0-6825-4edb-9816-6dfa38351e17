{"ast": null, "code": "import { getWindow, getDocument } from 'ssr-window';\nfunction deleteProps(obj) {\n  const object = obj;\n  Object.keys(object).forEach(key => {\n    try {\n      object[key] = null;\n    } catch (e) {\n      // no getter for object\n    }\n    try {\n      delete object[key];\n    } catch (e) {\n      // something got wrong\n    }\n  });\n}\nfunction nextTick(callback) {\n  let delay = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n  return setTimeout(callback, delay);\n}\nfunction now() {\n  return Date.now();\n}\nfunction getComputedStyle(el) {\n  const window = getWindow();\n  let style;\n  if (window.getComputedStyle) {\n    style = window.getComputedStyle(el, null);\n  }\n  if (!style && el.currentStyle) {\n    style = el.currentStyle;\n  }\n  if (!style) {\n    style = el.style;\n  }\n  return style;\n}\nfunction getTranslate(el) {\n  let axis = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'x';\n  const window = getWindow();\n  let matrix;\n  let curTransform;\n  let transformMatrix;\n  const curStyle = getComputedStyle(el, null);\n  if (window.WebKitCSSMatrix) {\n    curTransform = curStyle.transform || curStyle.webkitTransform;\n    if (curTransform.split(',').length > 6) {\n      curTransform = curTransform.split(', ').map(a => a.replace(',', '.')).join(', ');\n    }\n    // Some old versions of Webkit choke when 'none' is passed; pass\n    // empty string instead in this case\n    transformMatrix = new window.WebKitCSSMatrix(curTransform === 'none' ? '' : curTransform);\n  } else {\n    transformMatrix = curStyle.MozTransform || curStyle.OTransform || curStyle.MsTransform || curStyle.msTransform || curStyle.transform || curStyle.getPropertyValue('transform').replace('translate(', 'matrix(1, 0, 0, 1,');\n    matrix = transformMatrix.toString().split(',');\n  }\n  if (axis === 'x') {\n    // Latest Chrome and webkits Fix\n    if (window.WebKitCSSMatrix) curTransform = transformMatrix.m41;\n    // Crazy IE10 Matrix\n    else if (matrix.length === 16) curTransform = parseFloat(matrix[12]);\n    // Normal Browsers\n    else curTransform = parseFloat(matrix[4]);\n  }\n  if (axis === 'y') {\n    // Latest Chrome and webkits Fix\n    if (window.WebKitCSSMatrix) curTransform = transformMatrix.m42;\n    // Crazy IE10 Matrix\n    else if (matrix.length === 16) curTransform = parseFloat(matrix[13]);\n    // Normal Browsers\n    else curTransform = parseFloat(matrix[5]);\n  }\n  return curTransform || 0;\n}\nfunction isObject(o) {\n  return typeof o === 'object' && o !== null && o.constructor && Object.prototype.toString.call(o).slice(8, -1) === 'Object';\n}\nfunction isNode(node) {\n  // eslint-disable-next-line\n  if (typeof window !== 'undefined' && typeof window.HTMLElement !== 'undefined') {\n    return node instanceof HTMLElement;\n  }\n  return node && (node.nodeType === 1 || node.nodeType === 11);\n}\nfunction extend() {\n  const to = Object(arguments.length <= 0 ? undefined : arguments[0]);\n  const noExtend = ['__proto__', 'constructor', 'prototype'];\n  for (let i = 1; i < arguments.length; i += 1) {\n    const nextSource = i < 0 || arguments.length <= i ? undefined : arguments[i];\n    if (nextSource !== undefined && nextSource !== null && !isNode(nextSource)) {\n      const keysArray = Object.keys(Object(nextSource)).filter(key => noExtend.indexOf(key) < 0);\n      for (let nextIndex = 0, len = keysArray.length; nextIndex < len; nextIndex += 1) {\n        const nextKey = keysArray[nextIndex];\n        const desc = Object.getOwnPropertyDescriptor(nextSource, nextKey);\n        if (desc !== undefined && desc.enumerable) {\n          if (isObject(to[nextKey]) && isObject(nextSource[nextKey])) {\n            if (nextSource[nextKey].__swiper__) {\n              to[nextKey] = nextSource[nextKey];\n            } else {\n              extend(to[nextKey], nextSource[nextKey]);\n            }\n          } else if (!isObject(to[nextKey]) && isObject(nextSource[nextKey])) {\n            to[nextKey] = {};\n            if (nextSource[nextKey].__swiper__) {\n              to[nextKey] = nextSource[nextKey];\n            } else {\n              extend(to[nextKey], nextSource[nextKey]);\n            }\n          } else {\n            to[nextKey] = nextSource[nextKey];\n          }\n        }\n      }\n    }\n  }\n  return to;\n}\nfunction setCSSProperty(el, varName, varValue) {\n  el.style.setProperty(varName, varValue);\n}\nfunction animateCSSModeScroll(_ref) {\n  let {\n    swiper,\n    targetPosition,\n    side\n  } = _ref;\n  const window = getWindow();\n  const startPosition = -swiper.translate;\n  let startTime = null;\n  let time;\n  const duration = swiper.params.speed;\n  swiper.wrapperEl.style.scrollSnapType = 'none';\n  window.cancelAnimationFrame(swiper.cssModeFrameID);\n  const dir = targetPosition > startPosition ? 'next' : 'prev';\n  const isOutOfBound = (current, target) => {\n    return dir === 'next' && current >= target || dir === 'prev' && current <= target;\n  };\n  const animate = () => {\n    time = new Date().getTime();\n    if (startTime === null) {\n      startTime = time;\n    }\n    const progress = Math.max(Math.min((time - startTime) / duration, 1), 0);\n    const easeProgress = 0.5 - Math.cos(progress * Math.PI) / 2;\n    let currentPosition = startPosition + easeProgress * (targetPosition - startPosition);\n    if (isOutOfBound(currentPosition, targetPosition)) {\n      currentPosition = targetPosition;\n    }\n    swiper.wrapperEl.scrollTo({\n      [side]: currentPosition\n    });\n    if (isOutOfBound(currentPosition, targetPosition)) {\n      swiper.wrapperEl.style.overflow = 'hidden';\n      swiper.wrapperEl.style.scrollSnapType = '';\n      setTimeout(() => {\n        swiper.wrapperEl.style.overflow = '';\n        swiper.wrapperEl.scrollTo({\n          [side]: currentPosition\n        });\n      });\n      window.cancelAnimationFrame(swiper.cssModeFrameID);\n      return;\n    }\n    swiper.cssModeFrameID = window.requestAnimationFrame(animate);\n  };\n  animate();\n}\nfunction getSlideTransformEl(slideEl) {\n  return slideEl.querySelector('.swiper-slide-transform') || slideEl.shadowEl && slideEl.shadowEl.querySelector('.swiper-slide-transform') || slideEl;\n}\nfunction findElementsInElements() {\n  let elements = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n  let selector = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';\n  const found = [];\n  elements.forEach(el => {\n    found.push(...el.querySelectorAll(selector));\n  });\n  return found;\n}\nfunction elementChildren(element) {\n  let selector = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';\n  return [...element.children].filter(el => el.matches(selector));\n}\nfunction createElement(tag) {\n  let classes = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];\n  const el = document.createElement(tag);\n  el.classList.add(...(Array.isArray(classes) ? classes : [classes]));\n  return el;\n}\nfunction elementOffset(el) {\n  const window = getWindow();\n  const document = getDocument();\n  const box = el.getBoundingClientRect();\n  const body = document.body;\n  const clientTop = el.clientTop || body.clientTop || 0;\n  const clientLeft = el.clientLeft || body.clientLeft || 0;\n  const scrollTop = el === window ? window.scrollY : el.scrollTop;\n  const scrollLeft = el === window ? window.scrollX : el.scrollLeft;\n  return {\n    top: box.top + scrollTop - clientTop,\n    left: box.left + scrollLeft - clientLeft\n  };\n}\nfunction elementPrevAll(el, selector) {\n  const prevEls = [];\n  while (el.previousElementSibling) {\n    const prev = el.previousElementSibling; // eslint-disable-line\n    if (selector) {\n      if (prev.matches(selector)) prevEls.push(prev);\n    } else prevEls.push(prev);\n    el = prev;\n  }\n  return prevEls;\n}\nfunction elementNextAll(el, selector) {\n  const nextEls = [];\n  while (el.nextElementSibling) {\n    const next = el.nextElementSibling; // eslint-disable-line\n    if (selector) {\n      if (next.matches(selector)) nextEls.push(next);\n    } else nextEls.push(next);\n    el = next;\n  }\n  return nextEls;\n}\nfunction elementStyle(el, prop) {\n  const window = getWindow();\n  return window.getComputedStyle(el, null).getPropertyValue(prop);\n}\nfunction elementIndex(el) {\n  let child = el;\n  let i;\n  if (child) {\n    i = 0;\n    // eslint-disable-next-line\n    while ((child = child.previousSibling) !== null) {\n      if (child.nodeType === 1) i += 1;\n    }\n    return i;\n  }\n  return undefined;\n}\nfunction elementParents(el, selector) {\n  const parents = []; // eslint-disable-line\n  let parent = el.parentElement; // eslint-disable-line\n  while (parent) {\n    if (selector) {\n      if (parent.matches(selector)) parents.push(parent);\n    } else {\n      parents.push(parent);\n    }\n    parent = parent.parentElement;\n  }\n  return parents;\n}\nfunction elementTransitionEnd(el, callback) {\n  function fireCallBack(e) {\n    if (e.target !== el) return;\n    callback.call(el, e);\n    el.removeEventListener('transitionend', fireCallBack);\n  }\n  if (callback) {\n    el.addEventListener('transitionend', fireCallBack);\n  }\n}\nfunction elementOuterSize(el, size, includeMargins) {\n  const window = getWindow();\n  if (includeMargins) {\n    return el[size === 'width' ? 'offsetWidth' : 'offsetHeight'] + parseFloat(window.getComputedStyle(el, null).getPropertyValue(size === 'width' ? 'margin-right' : 'margin-top')) + parseFloat(window.getComputedStyle(el, null).getPropertyValue(size === 'width' ? 'margin-left' : 'margin-bottom'));\n  }\n  return el.offsetWidth;\n}\nexport { animateCSSModeScroll, deleteProps, nextTick, now, getTranslate, isObject, extend, getComputedStyle, setCSSProperty, getSlideTransformEl,\n// dom\nfindElementsInElements, createElement, elementChildren, elementOffset, elementPrevAll, elementNextAll, elementStyle, elementIndex, elementParents, elementTransitionEnd, elementOuterSize };", "map": {"version": 3, "names": ["getWindow", "getDocument", "deleteProps", "obj", "object", "Object", "keys", "for<PERSON>ach", "key", "e", "nextTick", "callback", "delay", "arguments", "length", "undefined", "setTimeout", "now", "Date", "getComputedStyle", "el", "window", "style", "currentStyle", "getTranslate", "axis", "matrix", "curTransform", "transformMatrix", "curStyle", "WebKitCSSMatrix", "transform", "webkitTransform", "split", "map", "a", "replace", "join", "MozTransform", "OTransform", "MsTransform", "msTransform", "getPropertyValue", "toString", "m41", "parseFloat", "m42", "isObject", "o", "constructor", "prototype", "call", "slice", "isNode", "node", "HTMLElement", "nodeType", "extend", "to", "noExtend", "i", "nextSource", "keysArray", "filter", "indexOf", "nextIndex", "len", "<PERSON><PERSON><PERSON>", "desc", "getOwnPropertyDescriptor", "enumerable", "__swiper__", "setCSSProperty", "varName", "varValue", "setProperty", "animateCSSModeScroll", "_ref", "swiper", "targetPosition", "side", "startPosition", "translate", "startTime", "time", "duration", "params", "speed", "wrapperEl", "scrollSnapType", "cancelAnimationFrame", "cssModeFrameID", "dir", "isOutOfBound", "current", "target", "animate", "getTime", "progress", "Math", "max", "min", "easeProgress", "cos", "PI", "currentPosition", "scrollTo", "overflow", "requestAnimationFrame", "getSlideTransformEl", "slideEl", "querySelector", "shadowEl", "findElementsInElements", "elements", "selector", "found", "push", "querySelectorAll", "elementChildren", "element", "children", "matches", "createElement", "tag", "classes", "document", "classList", "add", "Array", "isArray", "elementOffset", "box", "getBoundingClientRect", "body", "clientTop", "clientLeft", "scrollTop", "scrollY", "scrollLeft", "scrollX", "top", "left", "elementPrevAll", "prevEls", "previousElementSibling", "prev", "elementNextAll", "nextEls", "nextElement<PERSON><PERSON>ling", "next", "elementStyle", "prop", "elementIndex", "child", "previousSibling", "elementParents", "parents", "parent", "parentElement", "elementTransitionEnd", "fireCallBack", "removeEventListener", "addEventListener", "elementOuterSize", "size", "<PERSON><PERSON><PERSON><PERSON>", "offsetWidth"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/shared/utils.js"], "sourcesContent": ["import { getWindow, getDocument } from 'ssr-window';\nfunction deleteProps(obj) {\n  const object = obj;\n  Object.keys(object).forEach(key => {\n    try {\n      object[key] = null;\n    } catch (e) {\n      // no getter for object\n    }\n    try {\n      delete object[key];\n    } catch (e) {\n      // something got wrong\n    }\n  });\n}\nfunction nextTick(callback, delay = 0) {\n  return setTimeout(callback, delay);\n}\nfunction now() {\n  return Date.now();\n}\nfunction getComputedStyle(el) {\n  const window = getWindow();\n  let style;\n  if (window.getComputedStyle) {\n    style = window.getComputedStyle(el, null);\n  }\n  if (!style && el.currentStyle) {\n    style = el.currentStyle;\n  }\n  if (!style) {\n    style = el.style;\n  }\n  return style;\n}\nfunction getTranslate(el, axis = 'x') {\n  const window = getWindow();\n  let matrix;\n  let curTransform;\n  let transformMatrix;\n  const curStyle = getComputedStyle(el, null);\n  if (window.WebKitCSSMatrix) {\n    curTransform = curStyle.transform || curStyle.webkitTransform;\n    if (curTransform.split(',').length > 6) {\n      curTransform = curTransform.split(', ').map(a => a.replace(',', '.')).join(', ');\n    }\n    // Some old versions of Webkit choke when 'none' is passed; pass\n    // empty string instead in this case\n    transformMatrix = new window.WebKitCSSMatrix(curTransform === 'none' ? '' : curTransform);\n  } else {\n    transformMatrix = curStyle.MozTransform || curStyle.OTransform || curStyle.MsTransform || curStyle.msTransform || curStyle.transform || curStyle.getPropertyValue('transform').replace('translate(', 'matrix(1, 0, 0, 1,');\n    matrix = transformMatrix.toString().split(',');\n  }\n  if (axis === 'x') {\n    // Latest Chrome and webkits Fix\n    if (window.WebKitCSSMatrix) curTransform = transformMatrix.m41;\n    // Crazy IE10 Matrix\n    else if (matrix.length === 16) curTransform = parseFloat(matrix[12]);\n    // Normal Browsers\n    else curTransform = parseFloat(matrix[4]);\n  }\n  if (axis === 'y') {\n    // Latest Chrome and webkits Fix\n    if (window.WebKitCSSMatrix) curTransform = transformMatrix.m42;\n    // Crazy IE10 Matrix\n    else if (matrix.length === 16) curTransform = parseFloat(matrix[13]);\n    // Normal Browsers\n    else curTransform = parseFloat(matrix[5]);\n  }\n  return curTransform || 0;\n}\nfunction isObject(o) {\n  return typeof o === 'object' && o !== null && o.constructor && Object.prototype.toString.call(o).slice(8, -1) === 'Object';\n}\nfunction isNode(node) {\n  // eslint-disable-next-line\n  if (typeof window !== 'undefined' && typeof window.HTMLElement !== 'undefined') {\n    return node instanceof HTMLElement;\n  }\n  return node && (node.nodeType === 1 || node.nodeType === 11);\n}\nfunction extend(...args) {\n  const to = Object(args[0]);\n  const noExtend = ['__proto__', 'constructor', 'prototype'];\n  for (let i = 1; i < args.length; i += 1) {\n    const nextSource = args[i];\n    if (nextSource !== undefined && nextSource !== null && !isNode(nextSource)) {\n      const keysArray = Object.keys(Object(nextSource)).filter(key => noExtend.indexOf(key) < 0);\n      for (let nextIndex = 0, len = keysArray.length; nextIndex < len; nextIndex += 1) {\n        const nextKey = keysArray[nextIndex];\n        const desc = Object.getOwnPropertyDescriptor(nextSource, nextKey);\n        if (desc !== undefined && desc.enumerable) {\n          if (isObject(to[nextKey]) && isObject(nextSource[nextKey])) {\n            if (nextSource[nextKey].__swiper__) {\n              to[nextKey] = nextSource[nextKey];\n            } else {\n              extend(to[nextKey], nextSource[nextKey]);\n            }\n          } else if (!isObject(to[nextKey]) && isObject(nextSource[nextKey])) {\n            to[nextKey] = {};\n            if (nextSource[nextKey].__swiper__) {\n              to[nextKey] = nextSource[nextKey];\n            } else {\n              extend(to[nextKey], nextSource[nextKey]);\n            }\n          } else {\n            to[nextKey] = nextSource[nextKey];\n          }\n        }\n      }\n    }\n  }\n  return to;\n}\nfunction setCSSProperty(el, varName, varValue) {\n  el.style.setProperty(varName, varValue);\n}\nfunction animateCSSModeScroll({\n  swiper,\n  targetPosition,\n  side\n}) {\n  const window = getWindow();\n  const startPosition = -swiper.translate;\n  let startTime = null;\n  let time;\n  const duration = swiper.params.speed;\n  swiper.wrapperEl.style.scrollSnapType = 'none';\n  window.cancelAnimationFrame(swiper.cssModeFrameID);\n  const dir = targetPosition > startPosition ? 'next' : 'prev';\n  const isOutOfBound = (current, target) => {\n    return dir === 'next' && current >= target || dir === 'prev' && current <= target;\n  };\n  const animate = () => {\n    time = new Date().getTime();\n    if (startTime === null) {\n      startTime = time;\n    }\n    const progress = Math.max(Math.min((time - startTime) / duration, 1), 0);\n    const easeProgress = 0.5 - Math.cos(progress * Math.PI) / 2;\n    let currentPosition = startPosition + easeProgress * (targetPosition - startPosition);\n    if (isOutOfBound(currentPosition, targetPosition)) {\n      currentPosition = targetPosition;\n    }\n    swiper.wrapperEl.scrollTo({\n      [side]: currentPosition\n    });\n    if (isOutOfBound(currentPosition, targetPosition)) {\n      swiper.wrapperEl.style.overflow = 'hidden';\n      swiper.wrapperEl.style.scrollSnapType = '';\n      setTimeout(() => {\n        swiper.wrapperEl.style.overflow = '';\n        swiper.wrapperEl.scrollTo({\n          [side]: currentPosition\n        });\n      });\n      window.cancelAnimationFrame(swiper.cssModeFrameID);\n      return;\n    }\n    swiper.cssModeFrameID = window.requestAnimationFrame(animate);\n  };\n  animate();\n}\nfunction getSlideTransformEl(slideEl) {\n  return slideEl.querySelector('.swiper-slide-transform') || slideEl.shadowEl && slideEl.shadowEl.querySelector('.swiper-slide-transform') || slideEl;\n}\nfunction findElementsInElements(elements = [], selector = '') {\n  const found = [];\n  elements.forEach(el => {\n    found.push(...el.querySelectorAll(selector));\n  });\n  return found;\n}\nfunction elementChildren(element, selector = '') {\n  return [...element.children].filter(el => el.matches(selector));\n}\nfunction createElement(tag, classes = []) {\n  const el = document.createElement(tag);\n  el.classList.add(...(Array.isArray(classes) ? classes : [classes]));\n  return el;\n}\nfunction elementOffset(el) {\n  const window = getWindow();\n  const document = getDocument();\n  const box = el.getBoundingClientRect();\n  const body = document.body;\n  const clientTop = el.clientTop || body.clientTop || 0;\n  const clientLeft = el.clientLeft || body.clientLeft || 0;\n  const scrollTop = el === window ? window.scrollY : el.scrollTop;\n  const scrollLeft = el === window ? window.scrollX : el.scrollLeft;\n  return {\n    top: box.top + scrollTop - clientTop,\n    left: box.left + scrollLeft - clientLeft\n  };\n}\nfunction elementPrevAll(el, selector) {\n  const prevEls = [];\n  while (el.previousElementSibling) {\n    const prev = el.previousElementSibling; // eslint-disable-line\n    if (selector) {\n      if (prev.matches(selector)) prevEls.push(prev);\n    } else prevEls.push(prev);\n    el = prev;\n  }\n  return prevEls;\n}\nfunction elementNextAll(el, selector) {\n  const nextEls = [];\n  while (el.nextElementSibling) {\n    const next = el.nextElementSibling; // eslint-disable-line\n    if (selector) {\n      if (next.matches(selector)) nextEls.push(next);\n    } else nextEls.push(next);\n    el = next;\n  }\n  return nextEls;\n}\nfunction elementStyle(el, prop) {\n  const window = getWindow();\n  return window.getComputedStyle(el, null).getPropertyValue(prop);\n}\nfunction elementIndex(el) {\n  let child = el;\n  let i;\n  if (child) {\n    i = 0;\n    // eslint-disable-next-line\n    while ((child = child.previousSibling) !== null) {\n      if (child.nodeType === 1) i += 1;\n    }\n    return i;\n  }\n  return undefined;\n}\nfunction elementParents(el, selector) {\n  const parents = []; // eslint-disable-line\n  let parent = el.parentElement; // eslint-disable-line\n  while (parent) {\n    if (selector) {\n      if (parent.matches(selector)) parents.push(parent);\n    } else {\n      parents.push(parent);\n    }\n    parent = parent.parentElement;\n  }\n  return parents;\n}\nfunction elementTransitionEnd(el, callback) {\n  function fireCallBack(e) {\n    if (e.target !== el) return;\n    callback.call(el, e);\n    el.removeEventListener('transitionend', fireCallBack);\n  }\n  if (callback) {\n    el.addEventListener('transitionend', fireCallBack);\n  }\n}\nfunction elementOuterSize(el, size, includeMargins) {\n  const window = getWindow();\n  if (includeMargins) {\n    return el[size === 'width' ? 'offsetWidth' : 'offsetHeight'] + parseFloat(window.getComputedStyle(el, null).getPropertyValue(size === 'width' ? 'margin-right' : 'margin-top')) + parseFloat(window.getComputedStyle(el, null).getPropertyValue(size === 'width' ? 'margin-left' : 'margin-bottom'));\n  }\n  return el.offsetWidth;\n}\nexport { animateCSSModeScroll, deleteProps, nextTick, now, getTranslate, isObject, extend, getComputedStyle, setCSSProperty, getSlideTransformEl,\n// dom\nfindElementsInElements, createElement, elementChildren, elementOffset, elementPrevAll, elementNextAll, elementStyle, elementIndex, elementParents, elementTransitionEnd, elementOuterSize };"], "mappings": "AAAA,SAASA,SAAS,EAAEC,WAAW,QAAQ,YAAY;AACnD,SAASC,WAAWA,CAACC,GAAG,EAAE;EACxB,MAAMC,MAAM,GAAGD,GAAG;EAClBE,MAAM,CAACC,IAAI,CAACF,MAAM,CAAC,CAACG,OAAO,CAACC,GAAG,IAAI;IACjC,IAAI;MACFJ,MAAM,CAACI,GAAG,CAAC,GAAG,IAAI;IACpB,CAAC,CAAC,OAAOC,CAAC,EAAE;MACV;IAAA;IAEF,IAAI;MACF,OAAOL,MAAM,CAACI,GAAG,CAAC;IACpB,CAAC,CAAC,OAAOC,CAAC,EAAE;MACV;IAAA;EAEJ,CAAC,CAAC;AACJ;AACA,SAASC,QAAQA,CAACC,QAAQ,EAAa;EAAA,IAAXC,KAAK,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EACnC,OAAOG,UAAU,CAACL,QAAQ,EAAEC,KAAK,CAAC;AACpC;AACA,SAASK,GAAGA,CAAA,EAAG;EACb,OAAOC,IAAI,CAACD,GAAG,EAAE;AACnB;AACA,SAASE,gBAAgBA,CAACC,EAAE,EAAE;EAC5B,MAAMC,MAAM,GAAGrB,SAAS,EAAE;EAC1B,IAAIsB,KAAK;EACT,IAAID,MAAM,CAACF,gBAAgB,EAAE;IAC3BG,KAAK,GAAGD,MAAM,CAACF,gBAAgB,CAACC,EAAE,EAAE,IAAI,CAAC;EAC3C;EACA,IAAI,CAACE,KAAK,IAAIF,EAAE,CAACG,YAAY,EAAE;IAC7BD,KAAK,GAAGF,EAAE,CAACG,YAAY;EACzB;EACA,IAAI,CAACD,KAAK,EAAE;IACVA,KAAK,GAAGF,EAAE,CAACE,KAAK;EAClB;EACA,OAAOA,KAAK;AACd;AACA,SAASE,YAAYA,CAACJ,EAAE,EAAc;EAAA,IAAZK,IAAI,GAAAZ,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,GAAG;EAClC,MAAMQ,MAAM,GAAGrB,SAAS,EAAE;EAC1B,IAAI0B,MAAM;EACV,IAAIC,YAAY;EAChB,IAAIC,eAAe;EACnB,MAAMC,QAAQ,GAAGV,gBAAgB,CAACC,EAAE,EAAE,IAAI,CAAC;EAC3C,IAAIC,MAAM,CAACS,eAAe,EAAE;IAC1BH,YAAY,GAAGE,QAAQ,CAACE,SAAS,IAAIF,QAAQ,CAACG,eAAe;IAC7D,IAAIL,YAAY,CAACM,KAAK,CAAC,GAAG,CAAC,CAACnB,MAAM,GAAG,CAAC,EAAE;MACtCa,YAAY,GAAGA,YAAY,CAACM,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;IAClF;IACA;IACA;IACAT,eAAe,GAAG,IAAIP,MAAM,CAACS,eAAe,CAACH,YAAY,KAAK,MAAM,GAAG,EAAE,GAAGA,YAAY,CAAC;EAC3F,CAAC,MAAM;IACLC,eAAe,GAAGC,QAAQ,CAACS,YAAY,IAAIT,QAAQ,CAACU,UAAU,IAAIV,QAAQ,CAACW,WAAW,IAAIX,QAAQ,CAACY,WAAW,IAAIZ,QAAQ,CAACE,SAAS,IAAIF,QAAQ,CAACa,gBAAgB,CAAC,WAAW,CAAC,CAACN,OAAO,CAAC,YAAY,EAAE,oBAAoB,CAAC;IAC1NV,MAAM,GAAGE,eAAe,CAACe,QAAQ,EAAE,CAACV,KAAK,CAAC,GAAG,CAAC;EAChD;EACA,IAAIR,IAAI,KAAK,GAAG,EAAE;IAChB;IACA,IAAIJ,MAAM,CAACS,eAAe,EAAEH,YAAY,GAAGC,eAAe,CAACgB,GAAG;IAC9D;IAAA,KACK,IAAIlB,MAAM,CAACZ,MAAM,KAAK,EAAE,EAAEa,YAAY,GAAGkB,UAAU,CAACnB,MAAM,CAAC,EAAE,CAAC,CAAC;IACpE;IAAA,KACKC,YAAY,GAAGkB,UAAU,CAACnB,MAAM,CAAC,CAAC,CAAC,CAAC;EAC3C;EACA,IAAID,IAAI,KAAK,GAAG,EAAE;IAChB;IACA,IAAIJ,MAAM,CAACS,eAAe,EAAEH,YAAY,GAAGC,eAAe,CAACkB,GAAG;IAC9D;IAAA,KACK,IAAIpB,MAAM,CAACZ,MAAM,KAAK,EAAE,EAAEa,YAAY,GAAGkB,UAAU,CAACnB,MAAM,CAAC,EAAE,CAAC,CAAC;IACpE;IAAA,KACKC,YAAY,GAAGkB,UAAU,CAACnB,MAAM,CAAC,CAAC,CAAC,CAAC;EAC3C;EACA,OAAOC,YAAY,IAAI,CAAC;AAC1B;AACA,SAASoB,QAAQA,CAACC,CAAC,EAAE;EACnB,OAAO,OAAOA,CAAC,KAAK,QAAQ,IAAIA,CAAC,KAAK,IAAI,IAAIA,CAAC,CAACC,WAAW,IAAI5C,MAAM,CAAC6C,SAAS,CAACP,QAAQ,CAACQ,IAAI,CAACH,CAAC,CAAC,CAACI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,QAAQ;AAC5H;AACA,SAASC,MAAMA,CAACC,IAAI,EAAE;EACpB;EACA,IAAI,OAAOjC,MAAM,KAAK,WAAW,IAAI,OAAOA,MAAM,CAACkC,WAAW,KAAK,WAAW,EAAE;IAC9E,OAAOD,IAAI,YAAYC,WAAW;EACpC;EACA,OAAOD,IAAI,KAAKA,IAAI,CAACE,QAAQ,KAAK,CAAC,IAAIF,IAAI,CAACE,QAAQ,KAAK,EAAE,CAAC;AAC9D;AACA,SAASC,MAAMA,CAAA,EAAU;EACvB,MAAMC,EAAE,GAAGrD,MAAM,CAAAQ,SAAA,CAAAC,MAAA,QAAAC,SAAA,GAAAF,SAAA,IAAS;EAC1B,MAAM8C,QAAQ,GAAG,CAAC,WAAW,EAAE,aAAa,EAAE,WAAW,CAAC;EAC1D,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG/C,SAAA,CAAKC,MAAM,EAAE8C,CAAC,IAAI,CAAC,EAAE;IACvC,MAAMC,UAAU,GAAQD,CAAC,QAAA/C,SAAA,CAAAC,MAAA,IAAD8C,CAAC,GAAA7C,SAAA,GAAAF,SAAA,CAAD+C,CAAC,CAAC;IAC1B,IAAIC,UAAU,KAAK9C,SAAS,IAAI8C,UAAU,KAAK,IAAI,IAAI,CAACR,MAAM,CAACQ,UAAU,CAAC,EAAE;MAC1E,MAAMC,SAAS,GAAGzD,MAAM,CAACC,IAAI,CAACD,MAAM,CAACwD,UAAU,CAAC,CAAC,CAACE,MAAM,CAACvD,GAAG,IAAImD,QAAQ,CAACK,OAAO,CAACxD,GAAG,CAAC,GAAG,CAAC,CAAC;MAC1F,KAAK,IAAIyD,SAAS,GAAG,CAAC,EAAEC,GAAG,GAAGJ,SAAS,CAAChD,MAAM,EAAEmD,SAAS,GAAGC,GAAG,EAAED,SAAS,IAAI,CAAC,EAAE;QAC/E,MAAME,OAAO,GAAGL,SAAS,CAACG,SAAS,CAAC;QACpC,MAAMG,IAAI,GAAG/D,MAAM,CAACgE,wBAAwB,CAACR,UAAU,EAAEM,OAAO,CAAC;QACjE,IAAIC,IAAI,KAAKrD,SAAS,IAAIqD,IAAI,CAACE,UAAU,EAAE;UACzC,IAAIvB,QAAQ,CAACW,EAAE,CAACS,OAAO,CAAC,CAAC,IAAIpB,QAAQ,CAACc,UAAU,CAACM,OAAO,CAAC,CAAC,EAAE;YAC1D,IAAIN,UAAU,CAACM,OAAO,CAAC,CAACI,UAAU,EAAE;cAClCb,EAAE,CAACS,OAAO,CAAC,GAAGN,UAAU,CAACM,OAAO,CAAC;YACnC,CAAC,MAAM;cACLV,MAAM,CAACC,EAAE,CAACS,OAAO,CAAC,EAAEN,UAAU,CAACM,OAAO,CAAC,CAAC;YAC1C;UACF,CAAC,MAAM,IAAI,CAACpB,QAAQ,CAACW,EAAE,CAACS,OAAO,CAAC,CAAC,IAAIpB,QAAQ,CAACc,UAAU,CAACM,OAAO,CAAC,CAAC,EAAE;YAClET,EAAE,CAACS,OAAO,CAAC,GAAG,CAAC,CAAC;YAChB,IAAIN,UAAU,CAACM,OAAO,CAAC,CAACI,UAAU,EAAE;cAClCb,EAAE,CAACS,OAAO,CAAC,GAAGN,UAAU,CAACM,OAAO,CAAC;YACnC,CAAC,MAAM;cACLV,MAAM,CAACC,EAAE,CAACS,OAAO,CAAC,EAAEN,UAAU,CAACM,OAAO,CAAC,CAAC;YAC1C;UACF,CAAC,MAAM;YACLT,EAAE,CAACS,OAAO,CAAC,GAAGN,UAAU,CAACM,OAAO,CAAC;UACnC;QACF;MACF;IACF;EACF;EACA,OAAOT,EAAE;AACX;AACA,SAASc,cAAcA,CAACpD,EAAE,EAAEqD,OAAO,EAAEC,QAAQ,EAAE;EAC7CtD,EAAE,CAACE,KAAK,CAACqD,WAAW,CAACF,OAAO,EAAEC,QAAQ,CAAC;AACzC;AACA,SAASE,oBAAoBA,CAAAC,IAAA,EAI1B;EAAA,IAJ2B;IAC5BC,MAAM;IACNC,cAAc;IACdC;EACF,CAAC,GAAAH,IAAA;EACC,MAAMxD,MAAM,GAAGrB,SAAS,EAAE;EAC1B,MAAMiF,aAAa,GAAG,CAACH,MAAM,CAACI,SAAS;EACvC,IAAIC,SAAS,GAAG,IAAI;EACpB,IAAIC,IAAI;EACR,MAAMC,QAAQ,GAAGP,MAAM,CAACQ,MAAM,CAACC,KAAK;EACpCT,MAAM,CAACU,SAAS,CAAClE,KAAK,CAACmE,cAAc,GAAG,MAAM;EAC9CpE,MAAM,CAACqE,oBAAoB,CAACZ,MAAM,CAACa,cAAc,CAAC;EAClD,MAAMC,GAAG,GAAGb,cAAc,GAAGE,aAAa,GAAG,MAAM,GAAG,MAAM;EAC5D,MAAMY,YAAY,GAAGA,CAACC,OAAO,EAAEC,MAAM,KAAK;IACxC,OAAOH,GAAG,KAAK,MAAM,IAAIE,OAAO,IAAIC,MAAM,IAAIH,GAAG,KAAK,MAAM,IAAIE,OAAO,IAAIC,MAAM;EACnF,CAAC;EACD,MAAMC,OAAO,GAAGA,CAAA,KAAM;IACpBZ,IAAI,GAAG,IAAIlE,IAAI,EAAE,CAAC+E,OAAO,EAAE;IAC3B,IAAId,SAAS,KAAK,IAAI,EAAE;MACtBA,SAAS,GAAGC,IAAI;IAClB;IACA,MAAMc,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAAC,CAACjB,IAAI,GAAGD,SAAS,IAAIE,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACxE,MAAMiB,YAAY,GAAG,GAAG,GAAGH,IAAI,CAACI,GAAG,CAACL,QAAQ,GAAGC,IAAI,CAACK,EAAE,CAAC,GAAG,CAAC;IAC3D,IAAIC,eAAe,GAAGxB,aAAa,GAAGqB,YAAY,IAAIvB,cAAc,GAAGE,aAAa,CAAC;IACrF,IAAIY,YAAY,CAACY,eAAe,EAAE1B,cAAc,CAAC,EAAE;MACjD0B,eAAe,GAAG1B,cAAc;IAClC;IACAD,MAAM,CAACU,SAAS,CAACkB,QAAQ,CAAC;MACxB,CAAC1B,IAAI,GAAGyB;IACV,CAAC,CAAC;IACF,IAAIZ,YAAY,CAACY,eAAe,EAAE1B,cAAc,CAAC,EAAE;MACjDD,MAAM,CAACU,SAAS,CAAClE,KAAK,CAACqF,QAAQ,GAAG,QAAQ;MAC1C7B,MAAM,CAACU,SAAS,CAAClE,KAAK,CAACmE,cAAc,GAAG,EAAE;MAC1CzE,UAAU,CAAC,MAAM;QACf8D,MAAM,CAACU,SAAS,CAAClE,KAAK,CAACqF,QAAQ,GAAG,EAAE;QACpC7B,MAAM,CAACU,SAAS,CAACkB,QAAQ,CAAC;UACxB,CAAC1B,IAAI,GAAGyB;QACV,CAAC,CAAC;MACJ,CAAC,CAAC;MACFpF,MAAM,CAACqE,oBAAoB,CAACZ,MAAM,CAACa,cAAc,CAAC;MAClD;IACF;IACAb,MAAM,CAACa,cAAc,GAAGtE,MAAM,CAACuF,qBAAqB,CAACZ,OAAO,CAAC;EAC/D,CAAC;EACDA,OAAO,EAAE;AACX;AACA,SAASa,mBAAmBA,CAACC,OAAO,EAAE;EACpC,OAAOA,OAAO,CAACC,aAAa,CAAC,yBAAyB,CAAC,IAAID,OAAO,CAACE,QAAQ,IAAIF,OAAO,CAACE,QAAQ,CAACD,aAAa,CAAC,yBAAyB,CAAC,IAAID,OAAO;AACrJ;AACA,SAASG,sBAAsBA,CAAA,EAA+B;EAAA,IAA9BC,QAAQ,GAAArG,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;EAAA,IAAEsG,QAAQ,GAAAtG,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;EAC1D,MAAMuG,KAAK,GAAG,EAAE;EAChBF,QAAQ,CAAC3G,OAAO,CAACa,EAAE,IAAI;IACrBgG,KAAK,CAACC,IAAI,CAAC,GAAGjG,EAAE,CAACkG,gBAAgB,CAACH,QAAQ,CAAC,CAAC;EAC9C,CAAC,CAAC;EACF,OAAOC,KAAK;AACd;AACA,SAASG,eAAeA,CAACC,OAAO,EAAiB;EAAA,IAAfL,QAAQ,GAAAtG,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;EAC7C,OAAO,CAAC,GAAG2G,OAAO,CAACC,QAAQ,CAAC,CAAC1D,MAAM,CAAC3C,EAAE,IAAIA,EAAE,CAACsG,OAAO,CAACP,QAAQ,CAAC,CAAC;AACjE;AACA,SAASQ,aAAaA,CAACC,GAAG,EAAgB;EAAA,IAAdC,OAAO,GAAAhH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;EACtC,MAAMO,EAAE,GAAG0G,QAAQ,CAACH,aAAa,CAACC,GAAG,CAAC;EACtCxG,EAAE,CAAC2G,SAAS,CAACC,GAAG,CAAC,IAAIC,KAAK,CAACC,OAAO,CAACL,OAAO,CAAC,GAAGA,OAAO,GAAG,CAACA,OAAO,CAAC,CAAC,CAAC;EACnE,OAAOzG,EAAE;AACX;AACA,SAAS+G,aAAaA,CAAC/G,EAAE,EAAE;EACzB,MAAMC,MAAM,GAAGrB,SAAS,EAAE;EAC1B,MAAM8H,QAAQ,GAAG7H,WAAW,EAAE;EAC9B,MAAMmI,GAAG,GAAGhH,EAAE,CAACiH,qBAAqB,EAAE;EACtC,MAAMC,IAAI,GAAGR,QAAQ,CAACQ,IAAI;EAC1B,MAAMC,SAAS,GAAGnH,EAAE,CAACmH,SAAS,IAAID,IAAI,CAACC,SAAS,IAAI,CAAC;EACrD,MAAMC,UAAU,GAAGpH,EAAE,CAACoH,UAAU,IAAIF,IAAI,CAACE,UAAU,IAAI,CAAC;EACxD,MAAMC,SAAS,GAAGrH,EAAE,KAAKC,MAAM,GAAGA,MAAM,CAACqH,OAAO,GAAGtH,EAAE,CAACqH,SAAS;EAC/D,MAAME,UAAU,GAAGvH,EAAE,KAAKC,MAAM,GAAGA,MAAM,CAACuH,OAAO,GAAGxH,EAAE,CAACuH,UAAU;EACjE,OAAO;IACLE,GAAG,EAAET,GAAG,CAACS,GAAG,GAAGJ,SAAS,GAAGF,SAAS;IACpCO,IAAI,EAAEV,GAAG,CAACU,IAAI,GAAGH,UAAU,GAAGH;EAChC,CAAC;AACH;AACA,SAASO,cAAcA,CAAC3H,EAAE,EAAE+F,QAAQ,EAAE;EACpC,MAAM6B,OAAO,GAAG,EAAE;EAClB,OAAO5H,EAAE,CAAC6H,sBAAsB,EAAE;IAChC,MAAMC,IAAI,GAAG9H,EAAE,CAAC6H,sBAAsB,CAAC,CAAC;IACxC,IAAI9B,QAAQ,EAAE;MACZ,IAAI+B,IAAI,CAACxB,OAAO,CAACP,QAAQ,CAAC,EAAE6B,OAAO,CAAC3B,IAAI,CAAC6B,IAAI,CAAC;IAChD,CAAC,MAAMF,OAAO,CAAC3B,IAAI,CAAC6B,IAAI,CAAC;IACzB9H,EAAE,GAAG8H,IAAI;EACX;EACA,OAAOF,OAAO;AAChB;AACA,SAASG,cAAcA,CAAC/H,EAAE,EAAE+F,QAAQ,EAAE;EACpC,MAAMiC,OAAO,GAAG,EAAE;EAClB,OAAOhI,EAAE,CAACiI,kBAAkB,EAAE;IAC5B,MAAMC,IAAI,GAAGlI,EAAE,CAACiI,kBAAkB,CAAC,CAAC;IACpC,IAAIlC,QAAQ,EAAE;MACZ,IAAImC,IAAI,CAAC5B,OAAO,CAACP,QAAQ,CAAC,EAAEiC,OAAO,CAAC/B,IAAI,CAACiC,IAAI,CAAC;IAChD,CAAC,MAAMF,OAAO,CAAC/B,IAAI,CAACiC,IAAI,CAAC;IACzBlI,EAAE,GAAGkI,IAAI;EACX;EACA,OAAOF,OAAO;AAChB;AACA,SAASG,YAAYA,CAACnI,EAAE,EAAEoI,IAAI,EAAE;EAC9B,MAAMnI,MAAM,GAAGrB,SAAS,EAAE;EAC1B,OAAOqB,MAAM,CAACF,gBAAgB,CAACC,EAAE,EAAE,IAAI,CAAC,CAACsB,gBAAgB,CAAC8G,IAAI,CAAC;AACjE;AACA,SAASC,YAAYA,CAACrI,EAAE,EAAE;EACxB,IAAIsI,KAAK,GAAGtI,EAAE;EACd,IAAIwC,CAAC;EACL,IAAI8F,KAAK,EAAE;IACT9F,CAAC,GAAG,CAAC;IACL;IACA,OAAO,CAAC8F,KAAK,GAAGA,KAAK,CAACC,eAAe,MAAM,IAAI,EAAE;MAC/C,IAAID,KAAK,CAAClG,QAAQ,KAAK,CAAC,EAAEI,CAAC,IAAI,CAAC;IAClC;IACA,OAAOA,CAAC;EACV;EACA,OAAO7C,SAAS;AAClB;AACA,SAAS6I,cAAcA,CAACxI,EAAE,EAAE+F,QAAQ,EAAE;EACpC,MAAM0C,OAAO,GAAG,EAAE,CAAC,CAAC;EACpB,IAAIC,MAAM,GAAG1I,EAAE,CAAC2I,aAAa,CAAC,CAAC;EAC/B,OAAOD,MAAM,EAAE;IACb,IAAI3C,QAAQ,EAAE;MACZ,IAAI2C,MAAM,CAACpC,OAAO,CAACP,QAAQ,CAAC,EAAE0C,OAAO,CAACxC,IAAI,CAACyC,MAAM,CAAC;IACpD,CAAC,MAAM;MACLD,OAAO,CAACxC,IAAI,CAACyC,MAAM,CAAC;IACtB;IACAA,MAAM,GAAGA,MAAM,CAACC,aAAa;EAC/B;EACA,OAAOF,OAAO;AAChB;AACA,SAASG,oBAAoBA,CAAC5I,EAAE,EAAET,QAAQ,EAAE;EAC1C,SAASsJ,YAAYA,CAACxJ,CAAC,EAAE;IACvB,IAAIA,CAAC,CAACsF,MAAM,KAAK3E,EAAE,EAAE;IACrBT,QAAQ,CAACwC,IAAI,CAAC/B,EAAE,EAAEX,CAAC,CAAC;IACpBW,EAAE,CAAC8I,mBAAmB,CAAC,eAAe,EAAED,YAAY,CAAC;EACvD;EACA,IAAItJ,QAAQ,EAAE;IACZS,EAAE,CAAC+I,gBAAgB,CAAC,eAAe,EAAEF,YAAY,CAAC;EACpD;AACF;AACA,SAASG,gBAAgBA,CAAChJ,EAAE,EAAEiJ,IAAI,EAAEC,cAAc,EAAE;EAClD,MAAMjJ,MAAM,GAAGrB,SAAS,EAAE;EAC1B,IAAIsK,cAAc,EAAE;IAClB,OAAOlJ,EAAE,CAACiJ,IAAI,KAAK,OAAO,GAAG,aAAa,GAAG,cAAc,CAAC,GAAGxH,UAAU,CAACxB,MAAM,CAACF,gBAAgB,CAACC,EAAE,EAAE,IAAI,CAAC,CAACsB,gBAAgB,CAAC2H,IAAI,KAAK,OAAO,GAAG,cAAc,GAAG,YAAY,CAAC,CAAC,GAAGxH,UAAU,CAACxB,MAAM,CAACF,gBAAgB,CAACC,EAAE,EAAE,IAAI,CAAC,CAACsB,gBAAgB,CAAC2H,IAAI,KAAK,OAAO,GAAG,aAAa,GAAG,eAAe,CAAC,CAAC;EACtS;EACA,OAAOjJ,EAAE,CAACmJ,WAAW;AACvB;AACA,SAAS3F,oBAAoB,EAAE1E,WAAW,EAAEQ,QAAQ,EAAEO,GAAG,EAAEO,YAAY,EAAEuB,QAAQ,EAAEU,MAAM,EAAEtC,gBAAgB,EAAEqD,cAAc,EAAEqC,mBAAmB;AAChJ;AACAI,sBAAsB,EAAEU,aAAa,EAAEJ,eAAe,EAAEY,aAAa,EAAEY,cAAc,EAAEI,cAAc,EAAEI,YAAY,EAAEE,YAAY,EAAEG,cAAc,EAAEI,oBAAoB,EAAEI,gBAAgB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}