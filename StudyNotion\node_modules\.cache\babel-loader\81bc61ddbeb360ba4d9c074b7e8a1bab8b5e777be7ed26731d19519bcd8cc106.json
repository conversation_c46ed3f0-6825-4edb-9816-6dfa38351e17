{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nvar _ForwardReplayControl = _interopRequireDefault(require(\"./ForwardReplayControl\"));\n\n// Pass mode into parent function\nvar ForwardControl = (0, _ForwardReplayControl[\"default\"])('forward');\nForwardControl.displayName = 'ForwardControl';\nvar _default = ForwardControl;\nexports[\"default\"] = _default;", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "_ForwardReplayControl", "ForwardControl", "displayName", "_default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/video-react/lib/components/control-bar/ForwardControl.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _ForwardReplayControl = _interopRequireDefault(require(\"./ForwardReplayControl\"));\n\n// Pass mode into parent function\nvar ForwardControl = (0, _ForwardReplayControl[\"default\"])('forward');\nForwardControl.displayName = 'ForwardControl';\nvar _default = ForwardControl;\nexports[\"default\"] = _default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC;AAEpFC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;AAE3B,IAAIE,qBAAqB,GAAGN,sBAAsB,CAACC,OAAO,CAAC,wBAAwB,CAAC,CAAC;;AAErF;AACA,IAAIM,cAAc,GAAG,CAAC,CAAC,EAAED,qBAAqB,CAAC,SAAS,CAAC,EAAE,SAAS,CAAC;AACrEC,cAAc,CAACC,WAAW,GAAG,gBAAgB;AAC7C,IAAIC,QAAQ,GAAGF,cAAc;AAC7BH,OAAO,CAAC,SAAS,CAAC,GAAGK,QAAQ"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}