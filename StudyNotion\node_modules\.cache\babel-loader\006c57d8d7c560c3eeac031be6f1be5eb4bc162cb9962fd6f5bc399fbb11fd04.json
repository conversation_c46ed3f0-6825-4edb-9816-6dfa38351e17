{"ast": null, "code": "import { getWindow, getDocument } from 'ssr-window';\nimport { now } from '../../shared/utils.js';\n\n// Modified from https://stackoverflow.com/questions/54520554/custom-element-getrootnode-closest-function-crossing-multiple-parent-shadowd\nfunction closestElement(selector) {\n  let base = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : this;\n  function __closestFrom(el) {\n    if (!el || el === getDocument() || el === getWindow()) return null;\n    if (el.assignedSlot) el = el.assignedSlot;\n    const found = el.closest(selector);\n    if (!found && !el.getRootNode) {\n      return null;\n    }\n    return found || __closestFrom(el.getRootNode().host);\n  }\n  return __closestFrom(base);\n}\nexport default function onTouchStart(event) {\n  const swiper = this;\n  const document = getDocument();\n  const window = getWindow();\n  const data = swiper.touchEventsData;\n  data.evCache.push(event);\n  const {\n    params,\n    touches,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n  if (!params.simulateTouch && event.pointerType === 'mouse') return;\n  if (swiper.animating && params.preventInteractionOnTransition) {\n    return;\n  }\n  if (!swiper.animating && params.cssMode && params.loop) {\n    swiper.loopFix();\n  }\n  let e = event;\n  if (e.originalEvent) e = e.originalEvent;\n  let targetEl = e.target;\n  if (params.touchEventsTarget === 'wrapper') {\n    if (!swiper.wrapperEl.contains(targetEl)) return;\n  }\n  if ('which' in e && e.which === 3) return;\n  if ('button' in e && e.button > 0) return;\n  if (data.isTouched && data.isMoved) return;\n\n  // change target el for shadow root component\n  const swipingClassHasValue = !!params.noSwipingClass && params.noSwipingClass !== '';\n  // eslint-disable-next-line\n  const eventPath = event.composedPath ? event.composedPath() : event.path;\n  if (swipingClassHasValue && e.target && e.target.shadowRoot && eventPath) {\n    targetEl = eventPath[0];\n  }\n  const noSwipingSelector = params.noSwipingSelector ? params.noSwipingSelector : `.${params.noSwipingClass}`;\n  const isTargetShadow = !!(e.target && e.target.shadowRoot);\n\n  // use closestElement for shadow root element to get the actual closest for nested shadow root element\n  if (params.noSwiping && (isTargetShadow ? closestElement(noSwipingSelector, targetEl) : targetEl.closest(noSwipingSelector))) {\n    swiper.allowClick = true;\n    return;\n  }\n  if (params.swipeHandler) {\n    if (!targetEl.closest(params.swipeHandler)) return;\n  }\n  touches.currentX = e.pageX;\n  touches.currentY = e.pageY;\n  const startX = touches.currentX;\n  const startY = touches.currentY;\n\n  // Do NOT start if iOS edge swipe is detected. Otherwise iOS app cannot swipe-to-go-back anymore\n\n  const edgeSwipeDetection = params.edgeSwipeDetection || params.iOSEdgeSwipeDetection;\n  const edgeSwipeThreshold = params.edgeSwipeThreshold || params.iOSEdgeSwipeThreshold;\n  if (edgeSwipeDetection && (startX <= edgeSwipeThreshold || startX >= window.innerWidth - edgeSwipeThreshold)) {\n    if (edgeSwipeDetection === 'prevent') {\n      event.preventDefault();\n    } else {\n      return;\n    }\n  }\n  Object.assign(data, {\n    isTouched: true,\n    isMoved: false,\n    allowTouchCallbacks: true,\n    isScrolling: undefined,\n    startMoving: undefined\n  });\n  touches.startX = startX;\n  touches.startY = startY;\n  data.touchStartTime = now();\n  swiper.allowClick = true;\n  swiper.updateSize();\n  swiper.swipeDirection = undefined;\n  if (params.threshold > 0) data.allowThresholdMove = false;\n  let preventDefault = true;\n  if (targetEl.matches(data.focusableElements)) {\n    preventDefault = false;\n    if (targetEl.nodeName === 'SELECT') {\n      data.isTouched = false;\n    }\n  }\n  if (document.activeElement && document.activeElement.matches(data.focusableElements) && document.activeElement !== targetEl) {\n    document.activeElement.blur();\n  }\n  const shouldPreventDefault = preventDefault && swiper.allowTouchMove && params.touchStartPreventDefault;\n  if ((params.touchStartForcePreventDefault || shouldPreventDefault) && !targetEl.isContentEditable) {\n    e.preventDefault();\n  }\n  if (params.freeMode && params.freeMode.enabled && swiper.freeMode && swiper.animating && !params.cssMode) {\n    swiper.freeMode.onTouchStart();\n  }\n  swiper.emit('touchStart', e);\n}", "map": {"version": 3, "names": ["getWindow", "getDocument", "now", "closestElement", "selector", "base", "arguments", "length", "undefined", "__closestFrom", "el", "assignedSlot", "found", "closest", "getRootNode", "host", "onTouchStart", "event", "swiper", "document", "window", "data", "touchEventsData", "ev<PERSON><PERSON>", "push", "params", "touches", "enabled", "simulate<PERSON>ouch", "pointerType", "animating", "preventInteractionOnTransition", "cssMode", "loop", "loopFix", "e", "originalEvent", "targetEl", "target", "touchEventsTarget", "wrapperEl", "contains", "which", "button", "isTouched", "isMoved", "swipingClassHasValue", "noSwipingClass", "eventPath", "<PERSON><PERSON><PERSON>", "path", "shadowRoot", "noSwipingSelector", "isTargetShadow", "noSwiping", "allowClick", "swi<PERSON><PERSON><PERSON><PERSON>", "currentX", "pageX", "currentY", "pageY", "startX", "startY", "edgeSwipeDetection", "iOSEdgeSwipeDetection", "edgeSwipeThreshold", "iOSEdgeSwipeThreshold", "innerWidth", "preventDefault", "Object", "assign", "allowTouchCallbacks", "isScrolling", "startMoving", "touchStartTime", "updateSize", "swipeDirection", "threshold", "allowThresholdMove", "matches", "focusableElements", "nodeName", "activeElement", "blur", "shouldPreventDefault", "allowTouchMove", "touchStartPreventDefault", "touchStartForcePreventDefault", "isContentEditable", "freeMode", "emit"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/core/events/onTouchStart.js"], "sourcesContent": ["import { getWindow, getDocument } from 'ssr-window';\nimport { now } from '../../shared/utils.js';\n\n// Modified from https://stackoverflow.com/questions/54520554/custom-element-getrootnode-closest-function-crossing-multiple-parent-shadowd\nfunction closestElement(selector, base = this) {\n  function __closestFrom(el) {\n    if (!el || el === getDocument() || el === getWindow()) return null;\n    if (el.assignedSlot) el = el.assignedSlot;\n    const found = el.closest(selector);\n    if (!found && !el.getRootNode) {\n      return null;\n    }\n    return found || __closestFrom(el.getRootNode().host);\n  }\n  return __closestFrom(base);\n}\nexport default function onTouchStart(event) {\n  const swiper = this;\n  const document = getDocument();\n  const window = getWindow();\n  const data = swiper.touchEventsData;\n  data.evCache.push(event);\n  const {\n    params,\n    touches,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n  if (!params.simulateTouch && event.pointerType === 'mouse') return;\n  if (swiper.animating && params.preventInteractionOnTransition) {\n    return;\n  }\n  if (!swiper.animating && params.cssMode && params.loop) {\n    swiper.loopFix();\n  }\n  let e = event;\n  if (e.originalEvent) e = e.originalEvent;\n  let targetEl = e.target;\n  if (params.touchEventsTarget === 'wrapper') {\n    if (!swiper.wrapperEl.contains(targetEl)) return;\n  }\n  if ('which' in e && e.which === 3) return;\n  if ('button' in e && e.button > 0) return;\n  if (data.isTouched && data.isMoved) return;\n\n  // change target el for shadow root component\n  const swipingClassHasValue = !!params.noSwipingClass && params.noSwipingClass !== '';\n  // eslint-disable-next-line\n  const eventPath = event.composedPath ? event.composedPath() : event.path;\n  if (swipingClassHasValue && e.target && e.target.shadowRoot && eventPath) {\n    targetEl = eventPath[0];\n  }\n  const noSwipingSelector = params.noSwipingSelector ? params.noSwipingSelector : `.${params.noSwipingClass}`;\n  const isTargetShadow = !!(e.target && e.target.shadowRoot);\n\n  // use closestElement for shadow root element to get the actual closest for nested shadow root element\n  if (params.noSwiping && (isTargetShadow ? closestElement(noSwipingSelector, targetEl) : targetEl.closest(noSwipingSelector))) {\n    swiper.allowClick = true;\n    return;\n  }\n  if (params.swipeHandler) {\n    if (!targetEl.closest(params.swipeHandler)) return;\n  }\n  touches.currentX = e.pageX;\n  touches.currentY = e.pageY;\n  const startX = touches.currentX;\n  const startY = touches.currentY;\n\n  // Do NOT start if iOS edge swipe is detected. Otherwise iOS app cannot swipe-to-go-back anymore\n\n  const edgeSwipeDetection = params.edgeSwipeDetection || params.iOSEdgeSwipeDetection;\n  const edgeSwipeThreshold = params.edgeSwipeThreshold || params.iOSEdgeSwipeThreshold;\n  if (edgeSwipeDetection && (startX <= edgeSwipeThreshold || startX >= window.innerWidth - edgeSwipeThreshold)) {\n    if (edgeSwipeDetection === 'prevent') {\n      event.preventDefault();\n    } else {\n      return;\n    }\n  }\n  Object.assign(data, {\n    isTouched: true,\n    isMoved: false,\n    allowTouchCallbacks: true,\n    isScrolling: undefined,\n    startMoving: undefined\n  });\n  touches.startX = startX;\n  touches.startY = startY;\n  data.touchStartTime = now();\n  swiper.allowClick = true;\n  swiper.updateSize();\n  swiper.swipeDirection = undefined;\n  if (params.threshold > 0) data.allowThresholdMove = false;\n  let preventDefault = true;\n  if (targetEl.matches(data.focusableElements)) {\n    preventDefault = false;\n    if (targetEl.nodeName === 'SELECT') {\n      data.isTouched = false;\n    }\n  }\n  if (document.activeElement && document.activeElement.matches(data.focusableElements) && document.activeElement !== targetEl) {\n    document.activeElement.blur();\n  }\n  const shouldPreventDefault = preventDefault && swiper.allowTouchMove && params.touchStartPreventDefault;\n  if ((params.touchStartForcePreventDefault || shouldPreventDefault) && !targetEl.isContentEditable) {\n    e.preventDefault();\n  }\n  if (params.freeMode && params.freeMode.enabled && swiper.freeMode && swiper.animating && !params.cssMode) {\n    swiper.freeMode.onTouchStart();\n  }\n  swiper.emit('touchStart', e);\n}"], "mappings": "AAAA,SAASA,SAAS,EAAEC,WAAW,QAAQ,YAAY;AACnD,SAASC,GAAG,QAAQ,uBAAuB;;AAE3C;AACA,SAASC,cAAcA,CAACC,QAAQ,EAAe;EAAA,IAAbC,IAAI,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;EAC3C,SAASG,aAAaA,CAACC,EAAE,EAAE;IACzB,IAAI,CAACA,EAAE,IAAIA,EAAE,KAAKT,WAAW,EAAE,IAAIS,EAAE,KAAKV,SAAS,EAAE,EAAE,OAAO,IAAI;IAClE,IAAIU,EAAE,CAACC,YAAY,EAAED,EAAE,GAAGA,EAAE,CAACC,YAAY;IACzC,MAAMC,KAAK,GAAGF,EAAE,CAACG,OAAO,CAACT,QAAQ,CAAC;IAClC,IAAI,CAACQ,KAAK,IAAI,CAACF,EAAE,CAACI,WAAW,EAAE;MAC7B,OAAO,IAAI;IACb;IACA,OAAOF,KAAK,IAAIH,aAAa,CAACC,EAAE,CAACI,WAAW,EAAE,CAACC,IAAI,CAAC;EACtD;EACA,OAAON,aAAa,CAACJ,IAAI,CAAC;AAC5B;AACA,eAAe,SAASW,YAAYA,CAACC,KAAK,EAAE;EAC1C,MAAMC,MAAM,GAAG,IAAI;EACnB,MAAMC,QAAQ,GAAGlB,WAAW,EAAE;EAC9B,MAAMmB,MAAM,GAAGpB,SAAS,EAAE;EAC1B,MAAMqB,IAAI,GAAGH,MAAM,CAACI,eAAe;EACnCD,IAAI,CAACE,OAAO,CAACC,IAAI,CAACP,KAAK,CAAC;EACxB,MAAM;IACJQ,MAAM;IACNC,OAAO;IACPC;EACF,CAAC,GAAGT,MAAM;EACV,IAAI,CAACS,OAAO,EAAE;EACd,IAAI,CAACF,MAAM,CAACG,aAAa,IAAIX,KAAK,CAACY,WAAW,KAAK,OAAO,EAAE;EAC5D,IAAIX,MAAM,CAACY,SAAS,IAAIL,MAAM,CAACM,8BAA8B,EAAE;IAC7D;EACF;EACA,IAAI,CAACb,MAAM,CAACY,SAAS,IAAIL,MAAM,CAACO,OAAO,IAAIP,MAAM,CAACQ,IAAI,EAAE;IACtDf,MAAM,CAACgB,OAAO,EAAE;EAClB;EACA,IAAIC,CAAC,GAAGlB,KAAK;EACb,IAAIkB,CAAC,CAACC,aAAa,EAAED,CAAC,GAAGA,CAAC,CAACC,aAAa;EACxC,IAAIC,QAAQ,GAAGF,CAAC,CAACG,MAAM;EACvB,IAAIb,MAAM,CAACc,iBAAiB,KAAK,SAAS,EAAE;IAC1C,IAAI,CAACrB,MAAM,CAACsB,SAAS,CAACC,QAAQ,CAACJ,QAAQ,CAAC,EAAE;EAC5C;EACA,IAAI,OAAO,IAAIF,CAAC,IAAIA,CAAC,CAACO,KAAK,KAAK,CAAC,EAAE;EACnC,IAAI,QAAQ,IAAIP,CAAC,IAAIA,CAAC,CAACQ,MAAM,GAAG,CAAC,EAAE;EACnC,IAAItB,IAAI,CAACuB,SAAS,IAAIvB,IAAI,CAACwB,OAAO,EAAE;;EAEpC;EACA,MAAMC,oBAAoB,GAAG,CAAC,CAACrB,MAAM,CAACsB,cAAc,IAAItB,MAAM,CAACsB,cAAc,KAAK,EAAE;EACpF;EACA,MAAMC,SAAS,GAAG/B,KAAK,CAACgC,YAAY,GAAGhC,KAAK,CAACgC,YAAY,EAAE,GAAGhC,KAAK,CAACiC,IAAI;EACxE,IAAIJ,oBAAoB,IAAIX,CAAC,CAACG,MAAM,IAAIH,CAAC,CAACG,MAAM,CAACa,UAAU,IAAIH,SAAS,EAAE;IACxEX,QAAQ,GAAGW,SAAS,CAAC,CAAC,CAAC;EACzB;EACA,MAAMI,iBAAiB,GAAG3B,MAAM,CAAC2B,iBAAiB,GAAG3B,MAAM,CAAC2B,iBAAiB,GAAI,IAAG3B,MAAM,CAACsB,cAAe,EAAC;EAC3G,MAAMM,cAAc,GAAG,CAAC,EAAElB,CAAC,CAACG,MAAM,IAAIH,CAAC,CAACG,MAAM,CAACa,UAAU,CAAC;;EAE1D;EACA,IAAI1B,MAAM,CAAC6B,SAAS,KAAKD,cAAc,GAAGlD,cAAc,CAACiD,iBAAiB,EAAEf,QAAQ,CAAC,GAAGA,QAAQ,CAACxB,OAAO,CAACuC,iBAAiB,CAAC,CAAC,EAAE;IAC5HlC,MAAM,CAACqC,UAAU,GAAG,IAAI;IACxB;EACF;EACA,IAAI9B,MAAM,CAAC+B,YAAY,EAAE;IACvB,IAAI,CAACnB,QAAQ,CAACxB,OAAO,CAACY,MAAM,CAAC+B,YAAY,CAAC,EAAE;EAC9C;EACA9B,OAAO,CAAC+B,QAAQ,GAAGtB,CAAC,CAACuB,KAAK;EAC1BhC,OAAO,CAACiC,QAAQ,GAAGxB,CAAC,CAACyB,KAAK;EAC1B,MAAMC,MAAM,GAAGnC,OAAO,CAAC+B,QAAQ;EAC/B,MAAMK,MAAM,GAAGpC,OAAO,CAACiC,QAAQ;;EAE/B;;EAEA,MAAMI,kBAAkB,GAAGtC,MAAM,CAACsC,kBAAkB,IAAItC,MAAM,CAACuC,qBAAqB;EACpF,MAAMC,kBAAkB,GAAGxC,MAAM,CAACwC,kBAAkB,IAAIxC,MAAM,CAACyC,qBAAqB;EACpF,IAAIH,kBAAkB,KAAKF,MAAM,IAAII,kBAAkB,IAAIJ,MAAM,IAAIzC,MAAM,CAAC+C,UAAU,GAAGF,kBAAkB,CAAC,EAAE;IAC5G,IAAIF,kBAAkB,KAAK,SAAS,EAAE;MACpC9C,KAAK,CAACmD,cAAc,EAAE;IACxB,CAAC,MAAM;MACL;IACF;EACF;EACAC,MAAM,CAACC,MAAM,CAACjD,IAAI,EAAE;IAClBuB,SAAS,EAAE,IAAI;IACfC,OAAO,EAAE,KAAK;IACd0B,mBAAmB,EAAE,IAAI;IACzBC,WAAW,EAAEhE,SAAS;IACtBiE,WAAW,EAAEjE;EACf,CAAC,CAAC;EACFkB,OAAO,CAACmC,MAAM,GAAGA,MAAM;EACvBnC,OAAO,CAACoC,MAAM,GAAGA,MAAM;EACvBzC,IAAI,CAACqD,cAAc,GAAGxE,GAAG,EAAE;EAC3BgB,MAAM,CAACqC,UAAU,GAAG,IAAI;EACxBrC,MAAM,CAACyD,UAAU,EAAE;EACnBzD,MAAM,CAAC0D,cAAc,GAAGpE,SAAS;EACjC,IAAIiB,MAAM,CAACoD,SAAS,GAAG,CAAC,EAAExD,IAAI,CAACyD,kBAAkB,GAAG,KAAK;EACzD,IAAIV,cAAc,GAAG,IAAI;EACzB,IAAI/B,QAAQ,CAAC0C,OAAO,CAAC1D,IAAI,CAAC2D,iBAAiB,CAAC,EAAE;IAC5CZ,cAAc,GAAG,KAAK;IACtB,IAAI/B,QAAQ,CAAC4C,QAAQ,KAAK,QAAQ,EAAE;MAClC5D,IAAI,CAACuB,SAAS,GAAG,KAAK;IACxB;EACF;EACA,IAAIzB,QAAQ,CAAC+D,aAAa,IAAI/D,QAAQ,CAAC+D,aAAa,CAACH,OAAO,CAAC1D,IAAI,CAAC2D,iBAAiB,CAAC,IAAI7D,QAAQ,CAAC+D,aAAa,KAAK7C,QAAQ,EAAE;IAC3HlB,QAAQ,CAAC+D,aAAa,CAACC,IAAI,EAAE;EAC/B;EACA,MAAMC,oBAAoB,GAAGhB,cAAc,IAAIlD,MAAM,CAACmE,cAAc,IAAI5D,MAAM,CAAC6D,wBAAwB;EACvG,IAAI,CAAC7D,MAAM,CAAC8D,6BAA6B,IAAIH,oBAAoB,KAAK,CAAC/C,QAAQ,CAACmD,iBAAiB,EAAE;IACjGrD,CAAC,CAACiC,cAAc,EAAE;EACpB;EACA,IAAI3C,MAAM,CAACgE,QAAQ,IAAIhE,MAAM,CAACgE,QAAQ,CAAC9D,OAAO,IAAIT,MAAM,CAACuE,QAAQ,IAAIvE,MAAM,CAACY,SAAS,IAAI,CAACL,MAAM,CAACO,OAAO,EAAE;IACxGd,MAAM,CAACuE,QAAQ,CAACzE,YAAY,EAAE;EAChC;EACAE,MAAM,CAACwE,IAAI,CAAC,YAAY,EAAEvD,CAAC,CAAC;AAC9B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}