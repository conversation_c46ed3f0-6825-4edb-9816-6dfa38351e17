{"ast": null, "code": "// Somewhat based on:\n// <https://github.com/defunctzombie/node-process/blob/master/browser.js>.\n// But I don’t think one tiny line of code can be copyrighted. 😅\nexport const proc = {\n  cwd\n};\nfunction cwd() {\n  return '/';\n}", "map": {"version": 3, "names": ["proc", "cwd"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/vfile/lib/minproc.browser.js"], "sourcesContent": ["// Somewhat based on:\n// <https://github.com/defunctzombie/node-process/blob/master/browser.js>.\n// But I don’t think one tiny line of code can be copyrighted. 😅\nexport const proc = {cwd}\n\nfunction cwd() {\n  return '/'\n}\n"], "mappings": "AAAA;AACA;AACA;AACA,OAAO,MAAMA,IAAI,GAAG;EAACC;AAAG,CAAC;AAEzB,SAASA,GAAGA,CAAA,EAAG;EACb,OAAO,GAAG;AACZ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}