{"ast": null, "code": "import { getWindow } from 'ssr-window';\nexport default function Resize(_ref) {\n  let {\n    swiper,\n    on,\n    emit\n  } = _ref;\n  const window = getWindow();\n  let observer = null;\n  let animationFrame = null;\n  const resizeHandler = () => {\n    if (!swiper || swiper.destroyed || !swiper.initialized) return;\n    emit('beforeResize');\n    emit('resize');\n  };\n  const createObserver = () => {\n    if (!swiper || swiper.destroyed || !swiper.initialized) return;\n    observer = new ResizeObserver(entries => {\n      animationFrame = window.requestAnimationFrame(() => {\n        const {\n          width,\n          height\n        } = swiper;\n        let newWidth = width;\n        let newHeight = height;\n        entries.forEach(_ref2 => {\n          let {\n            contentBoxSize,\n            contentRect,\n            target\n          } = _ref2;\n          if (target && target !== swiper.el) return;\n          newWidth = contentRect ? contentRect.width : (contentBoxSize[0] || contentBoxSize).inlineSize;\n          newHeight = contentRect ? contentRect.height : (contentBoxSize[0] || contentBoxSize).blockSize;\n        });\n        if (newWidth !== width || newHeight !== height) {\n          resizeHandler();\n        }\n      });\n    });\n    observer.observe(swiper.el);\n  };\n  const removeObserver = () => {\n    if (animationFrame) {\n      window.cancelAnimationFrame(animationFrame);\n    }\n    if (observer && observer.unobserve && swiper.el) {\n      observer.unobserve(swiper.el);\n      observer = null;\n    }\n  };\n  const orientationChangeHandler = () => {\n    if (!swiper || swiper.destroyed || !swiper.initialized) return;\n    emit('orientationchange');\n  };\n  on('init', () => {\n    if (swiper.params.resizeObserver && typeof window.ResizeObserver !== 'undefined') {\n      createObserver();\n      return;\n    }\n    window.addEventListener('resize', resizeHandler);\n    window.addEventListener('orientationchange', orientationChangeHandler);\n  });\n  on('destroy', () => {\n    removeObserver();\n    window.removeEventListener('resize', resizeHandler);\n    window.removeEventListener('orientationchange', orientationChangeHandler);\n  });\n}", "map": {"version": 3, "names": ["getWindow", "Resize", "_ref", "swiper", "on", "emit", "window", "observer", "animationFrame", "resize<PERSON><PERSON>ler", "destroyed", "initialized", "createObserver", "ResizeObserver", "entries", "requestAnimationFrame", "width", "height", "newWidth", "newHeight", "for<PERSON>ach", "_ref2", "contentBoxSize", "contentRect", "target", "el", "inlineSize", "blockSize", "observe", "removeObserver", "cancelAnimationFrame", "unobserve", "orientationChangeHandler", "params", "resizeObserver", "addEventListener", "removeEventListener"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/core/modules/resize/resize.js"], "sourcesContent": ["import { getWindow } from 'ssr-window';\nexport default function Resize({\n  swiper,\n  on,\n  emit\n}) {\n  const window = getWindow();\n  let observer = null;\n  let animationFrame = null;\n  const resizeHandler = () => {\n    if (!swiper || swiper.destroyed || !swiper.initialized) return;\n    emit('beforeResize');\n    emit('resize');\n  };\n  const createObserver = () => {\n    if (!swiper || swiper.destroyed || !swiper.initialized) return;\n    observer = new ResizeObserver(entries => {\n      animationFrame = window.requestAnimationFrame(() => {\n        const {\n          width,\n          height\n        } = swiper;\n        let newWidth = width;\n        let newHeight = height;\n        entries.forEach(({\n          contentBoxSize,\n          contentRect,\n          target\n        }) => {\n          if (target && target !== swiper.el) return;\n          newWidth = contentRect ? contentRect.width : (contentBoxSize[0] || contentBoxSize).inlineSize;\n          newHeight = contentRect ? contentRect.height : (contentBoxSize[0] || contentBoxSize).blockSize;\n        });\n        if (newWidth !== width || newHeight !== height) {\n          resizeHandler();\n        }\n      });\n    });\n    observer.observe(swiper.el);\n  };\n  const removeObserver = () => {\n    if (animationFrame) {\n      window.cancelAnimationFrame(animationFrame);\n    }\n    if (observer && observer.unobserve && swiper.el) {\n      observer.unobserve(swiper.el);\n      observer = null;\n    }\n  };\n  const orientationChangeHandler = () => {\n    if (!swiper || swiper.destroyed || !swiper.initialized) return;\n    emit('orientationchange');\n  };\n  on('init', () => {\n    if (swiper.params.resizeObserver && typeof window.ResizeObserver !== 'undefined') {\n      createObserver();\n      return;\n    }\n    window.addEventListener('resize', resizeHandler);\n    window.addEventListener('orientationchange', orientationChangeHandler);\n  });\n  on('destroy', () => {\n    removeObserver();\n    window.removeEventListener('resize', resizeHandler);\n    window.removeEventListener('orientationchange', orientationChangeHandler);\n  });\n}"], "mappings": "AAAA,SAASA,SAAS,QAAQ,YAAY;AACtC,eAAe,SAASC,MAAMA,CAAAC,IAAA,EAI3B;EAAA,IAJ4B;IAC7BC,MAAM;IACNC,EAAE;IACFC;EACF,CAAC,GAAAH,IAAA;EACC,MAAMI,MAAM,GAAGN,SAAS,EAAE;EAC1B,IAAIO,QAAQ,GAAG,IAAI;EACnB,IAAIC,cAAc,GAAG,IAAI;EACzB,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAI,CAACN,MAAM,IAAIA,MAAM,CAACO,SAAS,IAAI,CAACP,MAAM,CAACQ,WAAW,EAAE;IACxDN,IAAI,CAAC,cAAc,CAAC;IACpBA,IAAI,CAAC,QAAQ,CAAC;EAChB,CAAC;EACD,MAAMO,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI,CAACT,MAAM,IAAIA,MAAM,CAACO,SAAS,IAAI,CAACP,MAAM,CAACQ,WAAW,EAAE;IACxDJ,QAAQ,GAAG,IAAIM,cAAc,CAACC,OAAO,IAAI;MACvCN,cAAc,GAAGF,MAAM,CAACS,qBAAqB,CAAC,MAAM;QAClD,MAAM;UACJC,KAAK;UACLC;QACF,CAAC,GAAGd,MAAM;QACV,IAAIe,QAAQ,GAAGF,KAAK;QACpB,IAAIG,SAAS,GAAGF,MAAM;QACtBH,OAAO,CAACM,OAAO,CAACC,KAAA,IAIV;UAAA,IAJW;YACfC,cAAc;YACdC,WAAW;YACXC;UACF,CAAC,GAAAH,KAAA;UACC,IAAIG,MAAM,IAAIA,MAAM,KAAKrB,MAAM,CAACsB,EAAE,EAAE;UACpCP,QAAQ,GAAGK,WAAW,GAAGA,WAAW,CAACP,KAAK,GAAG,CAACM,cAAc,CAAC,CAAC,CAAC,IAAIA,cAAc,EAAEI,UAAU;UAC7FP,SAAS,GAAGI,WAAW,GAAGA,WAAW,CAACN,MAAM,GAAG,CAACK,cAAc,CAAC,CAAC,CAAC,IAAIA,cAAc,EAAEK,SAAS;QAChG,CAAC,CAAC;QACF,IAAIT,QAAQ,KAAKF,KAAK,IAAIG,SAAS,KAAKF,MAAM,EAAE;UAC9CR,aAAa,EAAE;QACjB;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IACFF,QAAQ,CAACqB,OAAO,CAACzB,MAAM,CAACsB,EAAE,CAAC;EAC7B,CAAC;EACD,MAAMI,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIrB,cAAc,EAAE;MAClBF,MAAM,CAACwB,oBAAoB,CAACtB,cAAc,CAAC;IAC7C;IACA,IAAID,QAAQ,IAAIA,QAAQ,CAACwB,SAAS,IAAI5B,MAAM,CAACsB,EAAE,EAAE;MAC/ClB,QAAQ,CAACwB,SAAS,CAAC5B,MAAM,CAACsB,EAAE,CAAC;MAC7BlB,QAAQ,GAAG,IAAI;IACjB;EACF,CAAC;EACD,MAAMyB,wBAAwB,GAAGA,CAAA,KAAM;IACrC,IAAI,CAAC7B,MAAM,IAAIA,MAAM,CAACO,SAAS,IAAI,CAACP,MAAM,CAACQ,WAAW,EAAE;IACxDN,IAAI,CAAC,mBAAmB,CAAC;EAC3B,CAAC;EACDD,EAAE,CAAC,MAAM,EAAE,MAAM;IACf,IAAID,MAAM,CAAC8B,MAAM,CAACC,cAAc,IAAI,OAAO5B,MAAM,CAACO,cAAc,KAAK,WAAW,EAAE;MAChFD,cAAc,EAAE;MAChB;IACF;IACAN,MAAM,CAAC6B,gBAAgB,CAAC,QAAQ,EAAE1B,aAAa,CAAC;IAChDH,MAAM,CAAC6B,gBAAgB,CAAC,mBAAmB,EAAEH,wBAAwB,CAAC;EACxE,CAAC,CAAC;EACF5B,EAAE,CAAC,SAAS,EAAE,MAAM;IAClByB,cAAc,EAAE;IAChBvB,MAAM,CAAC8B,mBAAmB,CAAC,QAAQ,EAAE3B,aAAa,CAAC;IACnDH,MAAM,CAAC8B,mBAAmB,CAAC,mBAAmB,EAAEJ,wBAAwB,CAAC;EAC3E,CAAC,CAAC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}