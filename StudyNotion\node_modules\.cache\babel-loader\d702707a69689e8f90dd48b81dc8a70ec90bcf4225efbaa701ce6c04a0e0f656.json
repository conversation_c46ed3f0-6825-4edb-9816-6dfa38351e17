{"ast": null, "code": "import { visit } from 'unist-util-visit';\n\n/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('hast').Root} Root\n * @typedef {import('hast').Element} Element\n *\n * @callback AllowElement\n * @param {Element} element\n * @param {number} index\n * @param {Element|Root} parent\n * @returns {boolean|undefined}\n *\n * @typedef Options\n * @property {Array<string>} [allowedElements]\n * @property {Array<string>} [disallowedElements=[]]\n * @property {AllowElement} [allowElement]\n * @property {boolean} [unwrapDisallowed=false]\n */\n\n/**\n * @type {import('unified').Plugin<[Options], Root>}\n */\nexport default function rehypeFilter(options) {\n  if (options.allowedElements && options.disallowedElements) {\n    throw new TypeError('Only one of `allowedElements` and `disallowedElements` should be defined');\n  }\n  if (options.allowedElements || options.disallowedElements || options.allowElement) {\n    return tree => {\n      visit(tree, 'element', (node, index, parent_) => {\n        const parent = /** @type {Element|Root} */parent_;\n        /** @type {boolean|undefined} */\n        let remove;\n        if (options.allowedElements) {\n          remove = !options.allowedElements.includes(node.tagName);\n        } else if (options.disallowedElements) {\n          remove = options.disallowedElements.includes(node.tagName);\n        }\n        if (!remove && options.allowElement && typeof index === 'number') {\n          remove = !options.allowElement(node, index, parent);\n        }\n        if (remove && typeof index === 'number') {\n          if (options.unwrapDisallowed && node.children) {\n            parent.children.splice(index, 1, ...node.children);\n          } else {\n            parent.children.splice(index, 1);\n          }\n          return index;\n        }\n        return undefined;\n      });\n    };\n  }\n}", "map": {"version": 3, "names": ["visit", "rehypeFilter", "options", "allowedElements", "disallowedElements", "TypeError", "allowElement", "tree", "node", "index", "parent_", "parent", "remove", "includes", "tagName", "unwrapDisallowed", "children", "splice", "undefined"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/react-markdown/lib/rehype-filter.js"], "sourcesContent": ["import {visit} from 'unist-util-visit'\n\n/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('hast').Root} Root\n * @typedef {import('hast').Element} Element\n *\n * @callback AllowElement\n * @param {Element} element\n * @param {number} index\n * @param {Element|Root} parent\n * @returns {boolean|undefined}\n *\n * @typedef Options\n * @property {Array<string>} [allowedElements]\n * @property {Array<string>} [disallowedElements=[]]\n * @property {AllowElement} [allowElement]\n * @property {boolean} [unwrapDisallowed=false]\n */\n\n/**\n * @type {import('unified').Plugin<[Options], Root>}\n */\nexport default function rehypeFilter(options) {\n  if (options.allowedElements && options.disallowedElements) {\n    throw new TypeError(\n      'Only one of `allowedElements` and `disallowedElements` should be defined'\n    )\n  }\n\n  if (\n    options.allowedElements ||\n    options.disallowedElements ||\n    options.allowElement\n  ) {\n    return (tree) => {\n      visit(tree, 'element', (node, index, parent_) => {\n        const parent = /** @type {Element|Root} */ (parent_)\n        /** @type {boolean|undefined} */\n        let remove\n\n        if (options.allowedElements) {\n          remove = !options.allowedElements.includes(node.tagName)\n        } else if (options.disallowedElements) {\n          remove = options.disallowedElements.includes(node.tagName)\n        }\n\n        if (!remove && options.allowElement && typeof index === 'number') {\n          remove = !options.allowElement(node, index, parent)\n        }\n\n        if (remove && typeof index === 'number') {\n          if (options.unwrapDisallowed && node.children) {\n            parent.children.splice(index, 1, ...node.children)\n          } else {\n            parent.children.splice(index, 1)\n          }\n\n          return index\n        }\n\n        return undefined\n      })\n    }\n  }\n}\n"], "mappings": "AAAA,SAAQA,KAAK,QAAO,kBAAkB;;AAEtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,eAAe,SAASC,YAAYA,CAACC,OAAO,EAAE;EAC5C,IAAIA,OAAO,CAACC,eAAe,IAAID,OAAO,CAACE,kBAAkB,EAAE;IACzD,MAAM,IAAIC,SAAS,CACjB,0EAA0E,CAC3E;EACH;EAEA,IACEH,OAAO,CAACC,eAAe,IACvBD,OAAO,CAACE,kBAAkB,IAC1BF,OAAO,CAACI,YAAY,EACpB;IACA,OAAQC,IAAI,IAAK;MACfP,KAAK,CAACO,IAAI,EAAE,SAAS,EAAE,CAACC,IAAI,EAAEC,KAAK,EAAEC,OAAO,KAAK;QAC/C,MAAMC,MAAM,GAAG,2BAA6BD,OAAQ;QACpD;QACA,IAAIE,MAAM;QAEV,IAAIV,OAAO,CAACC,eAAe,EAAE;UAC3BS,MAAM,GAAG,CAACV,OAAO,CAACC,eAAe,CAACU,QAAQ,CAACL,IAAI,CAACM,OAAO,CAAC;QAC1D,CAAC,MAAM,IAAIZ,OAAO,CAACE,kBAAkB,EAAE;UACrCQ,MAAM,GAAGV,OAAO,CAACE,kBAAkB,CAACS,QAAQ,CAACL,IAAI,CAACM,OAAO,CAAC;QAC5D;QAEA,IAAI,CAACF,MAAM,IAAIV,OAAO,CAACI,YAAY,IAAI,OAAOG,KAAK,KAAK,QAAQ,EAAE;UAChEG,MAAM,GAAG,CAACV,OAAO,CAACI,YAAY,CAACE,IAAI,EAAEC,KAAK,EAAEE,MAAM,CAAC;QACrD;QAEA,IAAIC,MAAM,IAAI,OAAOH,KAAK,KAAK,QAAQ,EAAE;UACvC,IAAIP,OAAO,CAACa,gBAAgB,IAAIP,IAAI,CAACQ,QAAQ,EAAE;YAC7CL,MAAM,CAACK,QAAQ,CAACC,MAAM,CAACR,KAAK,EAAE,CAAC,EAAE,GAAGD,IAAI,CAACQ,QAAQ,CAAC;UACpD,CAAC,MAAM;YACLL,MAAM,CAACK,QAAQ,CAACC,MAAM,CAACR,KAAK,EAAE,CAAC,CAAC;UAClC;UAEA,OAAOA,KAAK;QACd;QAEA,OAAOS,SAAS;MAClB,CAAC,CAAC;IACJ,CAAC;EACH;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}