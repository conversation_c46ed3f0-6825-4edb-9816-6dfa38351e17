{"ast": null, "code": "import Swiper from 'swiper';\nimport { isObject, extend } from './utils.js';\nimport { paramsList } from './params-list.js';\nfunction getParams() {\n  let obj = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  let splitEvents = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n  const params = {\n    on: {}\n  };\n  const events = {};\n  const passedParams = {};\n  extend(params, Swiper.defaults);\n  extend(params, Swiper.extendedDefaults);\n  params._emitClasses = true;\n  params.init = false;\n  const rest = {};\n  const allowedParams = paramsList.map(key => key.replace(/_/, ''));\n  const plainObj = Object.assign({}, obj);\n  Object.keys(plainObj).forEach(key => {\n    if (typeof obj[key] === 'undefined') return;\n    if (allowedParams.indexOf(key) >= 0) {\n      if (isObject(obj[key])) {\n        params[key] = {};\n        passedParams[key] = {};\n        extend(params[key], obj[key]);\n        extend(passedParams[key], obj[key]);\n      } else {\n        params[key] = obj[key];\n        passedParams[key] = obj[key];\n      }\n    } else if (key.search(/on[A-Z]/) === 0 && typeof obj[key] === 'function') {\n      if (splitEvents) {\n        events[`${key[2].toLowerCase()}${key.substr(3)}`] = obj[key];\n      } else {\n        params.on[`${key[2].toLowerCase()}${key.substr(3)}`] = obj[key];\n      }\n    } else {\n      rest[key] = obj[key];\n    }\n  });\n  ['navigation', 'pagination', 'scrollbar'].forEach(key => {\n    if (params[key] === true) params[key] = {};\n    if (params[key] === false) delete params[key];\n  });\n  return {\n    params,\n    passedParams,\n    rest,\n    events\n  };\n}\nexport { getParams };", "map": {"version": 3, "names": ["Swiper", "isObject", "extend", "paramsList", "getParams", "obj", "arguments", "length", "undefined", "splitEvents", "params", "on", "events", "passedParams", "defaults", "extendedDefaults", "_emitClasses", "init", "rest", "allowedParams", "map", "key", "replace", "plainObj", "Object", "assign", "keys", "for<PERSON>ach", "indexOf", "search", "toLowerCase", "substr"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/components-shared/get-params.js"], "sourcesContent": ["import Swiper from 'swiper';\nimport { isObject, extend } from './utils.js';\nimport { paramsList } from './params-list.js';\nfunction getParams(obj = {}, splitEvents = true) {\n  const params = {\n    on: {}\n  };\n  const events = {};\n  const passedParams = {};\n  extend(params, Swiper.defaults);\n  extend(params, Swiper.extendedDefaults);\n  params._emitClasses = true;\n  params.init = false;\n  const rest = {};\n  const allowedParams = paramsList.map(key => key.replace(/_/, ''));\n  const plainObj = Object.assign({}, obj);\n  Object.keys(plainObj).forEach(key => {\n    if (typeof obj[key] === 'undefined') return;\n    if (allowedParams.indexOf(key) >= 0) {\n      if (isObject(obj[key])) {\n        params[key] = {};\n        passedParams[key] = {};\n        extend(params[key], obj[key]);\n        extend(passedParams[key], obj[key]);\n      } else {\n        params[key] = obj[key];\n        passedParams[key] = obj[key];\n      }\n    } else if (key.search(/on[A-Z]/) === 0 && typeof obj[key] === 'function') {\n      if (splitEvents) {\n        events[`${key[2].toLowerCase()}${key.substr(3)}`] = obj[key];\n      } else {\n        params.on[`${key[2].toLowerCase()}${key.substr(3)}`] = obj[key];\n      }\n    } else {\n      rest[key] = obj[key];\n    }\n  });\n  ['navigation', 'pagination', 'scrollbar'].forEach(key => {\n    if (params[key] === true) params[key] = {};\n    if (params[key] === false) delete params[key];\n  });\n  return {\n    params,\n    passedParams,\n    rest,\n    events\n  };\n}\nexport { getParams };"], "mappings": "AAAA,OAAOA,MAAM,MAAM,QAAQ;AAC3B,SAASC,QAAQ,EAAEC,MAAM,QAAQ,YAAY;AAC7C,SAASC,UAAU,QAAQ,kBAAkB;AAC7C,SAASC,SAASA,CAAA,EAA+B;EAAA,IAA9BC,GAAG,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAAA,IAAEG,WAAW,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;EAC7C,MAAMI,MAAM,GAAG;IACbC,EAAE,EAAE,CAAC;EACP,CAAC;EACD,MAAMC,MAAM,GAAG,CAAC,CAAC;EACjB,MAAMC,YAAY,GAAG,CAAC,CAAC;EACvBX,MAAM,CAACQ,MAAM,EAAEV,MAAM,CAACc,QAAQ,CAAC;EAC/BZ,MAAM,CAACQ,MAAM,EAAEV,MAAM,CAACe,gBAAgB,CAAC;EACvCL,MAAM,CAACM,YAAY,GAAG,IAAI;EAC1BN,MAAM,CAACO,IAAI,GAAG,KAAK;EACnB,MAAMC,IAAI,GAAG,CAAC,CAAC;EACf,MAAMC,aAAa,GAAGhB,UAAU,CAACiB,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;EACjE,MAAMC,QAAQ,GAAGC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEpB,GAAG,CAAC;EACvCmB,MAAM,CAACE,IAAI,CAACH,QAAQ,CAAC,CAACI,OAAO,CAACN,GAAG,IAAI;IACnC,IAAI,OAAOhB,GAAG,CAACgB,GAAG,CAAC,KAAK,WAAW,EAAE;IACrC,IAAIF,aAAa,CAACS,OAAO,CAACP,GAAG,CAAC,IAAI,CAAC,EAAE;MACnC,IAAIpB,QAAQ,CAACI,GAAG,CAACgB,GAAG,CAAC,CAAC,EAAE;QACtBX,MAAM,CAACW,GAAG,CAAC,GAAG,CAAC,CAAC;QAChBR,YAAY,CAACQ,GAAG,CAAC,GAAG,CAAC,CAAC;QACtBnB,MAAM,CAACQ,MAAM,CAACW,GAAG,CAAC,EAAEhB,GAAG,CAACgB,GAAG,CAAC,CAAC;QAC7BnB,MAAM,CAACW,YAAY,CAACQ,GAAG,CAAC,EAAEhB,GAAG,CAACgB,GAAG,CAAC,CAAC;MACrC,CAAC,MAAM;QACLX,MAAM,CAACW,GAAG,CAAC,GAAGhB,GAAG,CAACgB,GAAG,CAAC;QACtBR,YAAY,CAACQ,GAAG,CAAC,GAAGhB,GAAG,CAACgB,GAAG,CAAC;MAC9B;IACF,CAAC,MAAM,IAAIA,GAAG,CAACQ,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,OAAOxB,GAAG,CAACgB,GAAG,CAAC,KAAK,UAAU,EAAE;MACxE,IAAIZ,WAAW,EAAE;QACfG,MAAM,CAAE,GAAES,GAAG,CAAC,CAAC,CAAC,CAACS,WAAW,EAAG,GAAET,GAAG,CAACU,MAAM,CAAC,CAAC,CAAE,EAAC,CAAC,GAAG1B,GAAG,CAACgB,GAAG,CAAC;MAC9D,CAAC,MAAM;QACLX,MAAM,CAACC,EAAE,CAAE,GAAEU,GAAG,CAAC,CAAC,CAAC,CAACS,WAAW,EAAG,GAAET,GAAG,CAACU,MAAM,CAAC,CAAC,CAAE,EAAC,CAAC,GAAG1B,GAAG,CAACgB,GAAG,CAAC;MACjE;IACF,CAAC,MAAM;MACLH,IAAI,CAACG,GAAG,CAAC,GAAGhB,GAAG,CAACgB,GAAG,CAAC;IACtB;EACF,CAAC,CAAC;EACF,CAAC,YAAY,EAAE,YAAY,EAAE,WAAW,CAAC,CAACM,OAAO,CAACN,GAAG,IAAI;IACvD,IAAIX,MAAM,CAACW,GAAG,CAAC,KAAK,IAAI,EAAEX,MAAM,CAACW,GAAG,CAAC,GAAG,CAAC,CAAC;IAC1C,IAAIX,MAAM,CAACW,GAAG,CAAC,KAAK,KAAK,EAAE,OAAOX,MAAM,CAACW,GAAG,CAAC;EAC/C,CAAC,CAAC;EACF,OAAO;IACLX,MAAM;IACNG,YAAY;IACZK,IAAI;IACJN;EACF,CAAC;AACH;AACA,SAASR,SAAS"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}