{"ast": null, "code": "/**\n * @typedef {import('micromark-util-types').Code} Code\n * @typedef {import('micromark-util-types').Construct} Construct\n * @typedef {import('micromark-util-types').ContainerState} ContainerState\n * @typedef {import('micromark-util-types').Exiter} Exiter\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Tokenizer} Tokenizer\n */\n\nimport { factorySpace } from 'micromark-factory-space';\nimport { asciiDigit, markdownSpace } from 'micromark-util-character';\nimport { codes } from 'micromark-util-symbol/codes.js';\nimport { constants } from 'micromark-util-symbol/constants.js';\nimport { types } from 'micromark-util-symbol/types.js';\nimport { ok as assert } from 'uvu/assert';\nimport { blankLine } from './blank-line.js';\nimport { thematicBreak } from './thematic-break.js';\n\n/** @type {Construct} */\nexport const list = {\n  name: 'list',\n  tokenize: tokenizeListStart,\n  continuation: {\n    tokenize: tokenizeListContinuation\n  },\n  exit: tokenizeListEnd\n};\n\n/** @type {Construct} */\nconst listItemPrefixWhitespaceConstruct = {\n  tokenize: tokenizeListItemPrefixWhitespace,\n  partial: true\n};\n\n/** @type {Construct} */\nconst indentConstruct = {\n  tokenize: tokenizeIndent,\n  partial: true\n};\n\n// To do: `markdown-rs` parses list items on their own and later stitches them\n// together.\n\n/**\n * @type {Tokenizer}\n * @this {TokenizeContext}\n */\nfunction tokenizeListStart(effects, ok, nok) {\n  const self = this;\n  const tail = self.events[self.events.length - 1];\n  let initialSize = tail && tail[1].type === types.linePrefix ? tail[2].sliceSerialize(tail[1], true).length : 0;\n  let size = 0;\n  return start;\n\n  /** @type {State} */\n  function start(code) {\n    assert(self.containerState, 'expected state');\n    const kind = self.containerState.type || (code === codes.asterisk || code === codes.plusSign || code === codes.dash ? types.listUnordered : types.listOrdered);\n    if (kind === types.listUnordered ? !self.containerState.marker || code === self.containerState.marker : asciiDigit(code)) {\n      if (!self.containerState.type) {\n        self.containerState.type = kind;\n        effects.enter(kind, {\n          _container: true\n        });\n      }\n      if (kind === types.listUnordered) {\n        effects.enter(types.listItemPrefix);\n        return code === codes.asterisk || code === codes.dash ? effects.check(thematicBreak, nok, atMarker)(code) : atMarker(code);\n      }\n      if (!self.interrupt || code === codes.digit1) {\n        effects.enter(types.listItemPrefix);\n        effects.enter(types.listItemValue);\n        return inside(code);\n      }\n    }\n    return nok(code);\n  }\n\n  /** @type {State} */\n  function inside(code) {\n    assert(self.containerState, 'expected state');\n    if (asciiDigit(code) && ++size < constants.listItemValueSizeMax) {\n      effects.consume(code);\n      return inside;\n    }\n    if ((!self.interrupt || size < 2) && (self.containerState.marker ? code === self.containerState.marker : code === codes.rightParenthesis || code === codes.dot)) {\n      effects.exit(types.listItemValue);\n      return atMarker(code);\n    }\n    return nok(code);\n  }\n\n  /**\n   * @type {State}\n   **/\n  function atMarker(code) {\n    assert(self.containerState, 'expected state');\n    assert(code !== codes.eof, 'eof (`null`) is not a marker');\n    effects.enter(types.listItemMarker);\n    effects.consume(code);\n    effects.exit(types.listItemMarker);\n    self.containerState.marker = self.containerState.marker || code;\n    return effects.check(blankLine,\n    // Can’t be empty when interrupting.\n    self.interrupt ? nok : onBlank, effects.attempt(listItemPrefixWhitespaceConstruct, endOfPrefix, otherPrefix));\n  }\n\n  /** @type {State} */\n  function onBlank(code) {\n    assert(self.containerState, 'expected state');\n    self.containerState.initialBlankLine = true;\n    initialSize++;\n    return endOfPrefix(code);\n  }\n\n  /** @type {State} */\n  function otherPrefix(code) {\n    if (markdownSpace(code)) {\n      effects.enter(types.listItemPrefixWhitespace);\n      effects.consume(code);\n      effects.exit(types.listItemPrefixWhitespace);\n      return endOfPrefix;\n    }\n    return nok(code);\n  }\n\n  /** @type {State} */\n  function endOfPrefix(code) {\n    assert(self.containerState, 'expected state');\n    self.containerState.size = initialSize + self.sliceSerialize(effects.exit(types.listItemPrefix), true).length;\n    return ok(code);\n  }\n}\n\n/**\n * @type {Tokenizer}\n * @this {TokenizeContext}\n */\nfunction tokenizeListContinuation(effects, ok, nok) {\n  const self = this;\n  assert(self.containerState, 'expected state');\n  self.containerState._closeFlow = undefined;\n  return effects.check(blankLine, onBlank, notBlank);\n\n  /** @type {State} */\n  function onBlank(code) {\n    assert(self.containerState, 'expected state');\n    assert(typeof self.containerState.size === 'number', 'expected size');\n    self.containerState.furtherBlankLines = self.containerState.furtherBlankLines || self.containerState.initialBlankLine;\n\n    // We have a blank line.\n    // Still, try to consume at most the items size.\n    return factorySpace(effects, ok, types.listItemIndent, self.containerState.size + 1)(code);\n  }\n\n  /** @type {State} */\n  function notBlank(code) {\n    assert(self.containerState, 'expected state');\n    if (self.containerState.furtherBlankLines || !markdownSpace(code)) {\n      self.containerState.furtherBlankLines = undefined;\n      self.containerState.initialBlankLine = undefined;\n      return notInCurrentItem(code);\n    }\n    self.containerState.furtherBlankLines = undefined;\n    self.containerState.initialBlankLine = undefined;\n    return effects.attempt(indentConstruct, ok, notInCurrentItem)(code);\n  }\n\n  /** @type {State} */\n  function notInCurrentItem(code) {\n    assert(self.containerState, 'expected state');\n    // While we do continue, we signal that the flow should be closed.\n    self.containerState._closeFlow = true;\n    // As we’re closing flow, we’re no longer interrupting.\n    self.interrupt = undefined;\n    // Always populated by defaults.\n    assert(self.parser.constructs.disable.null, 'expected `disable.null` to be populated');\n    return factorySpace(effects, effects.attempt(list, ok, nok), types.linePrefix, self.parser.constructs.disable.null.includes('codeIndented') ? undefined : constants.tabSize)(code);\n  }\n}\n\n/**\n * @type {Tokenizer}\n * @this {TokenizeContext}\n */\nfunction tokenizeIndent(effects, ok, nok) {\n  const self = this;\n  assert(self.containerState, 'expected state');\n  assert(typeof self.containerState.size === 'number', 'expected size');\n  return factorySpace(effects, afterPrefix, types.listItemIndent, self.containerState.size + 1);\n\n  /** @type {State} */\n  function afterPrefix(code) {\n    assert(self.containerState, 'expected state');\n    const tail = self.events[self.events.length - 1];\n    return tail && tail[1].type === types.listItemIndent && tail[2].sliceSerialize(tail[1], true).length === self.containerState.size ? ok(code) : nok(code);\n  }\n}\n\n/**\n * @type {Exiter}\n * @this {TokenizeContext}\n */\nfunction tokenizeListEnd(effects) {\n  assert(this.containerState, 'expected state');\n  assert(typeof this.containerState.type === 'string', 'expected type');\n  effects.exit(this.containerState.type);\n}\n\n/**\n * @type {Tokenizer}\n * @this {TokenizeContext}\n */\nfunction tokenizeListItemPrefixWhitespace(effects, ok, nok) {\n  const self = this;\n\n  // Always populated by defaults.\n  assert(self.parser.constructs.disable.null, 'expected `disable.null` to be populated');\n  return factorySpace(effects, afterPrefix, types.listItemPrefixWhitespace, self.parser.constructs.disable.null.includes('codeIndented') ? undefined : constants.tabSize + 1);\n\n  /** @type {State} */\n  function afterPrefix(code) {\n    const tail = self.events[self.events.length - 1];\n    return !markdownSpace(code) && tail && tail[1].type === types.listItemPrefixWhitespace ? ok(code) : nok(code);\n  }\n}", "map": {"version": 3, "names": ["factorySpace", "asciiDigit", "markdownSpace", "codes", "constants", "types", "ok", "assert", "blankLine", "thematicBreak", "list", "name", "tokenize", "tokenizeListStart", "continuation", "tokenizeListContinuation", "exit", "tokenizeListEnd", "listItemPrefixWhitespaceConstruct", "tokenizeListItemPrefixWhitespace", "partial", "indentConstruct", "tokenizeIndent", "effects", "nok", "self", "tail", "events", "length", "initialSize", "type", "linePrefix", "sliceSerialize", "size", "start", "code", "containerState", "kind", "asterisk", "plusSign", "dash", "listUnordered", "listOrdered", "marker", "enter", "_container", "listItemPrefix", "check", "atMarker", "interrupt", "digit1", "listItemValue", "inside", "listItemValueSizeMax", "consume", "rightParenthesis", "dot", "eof", "listItemMarker", "onBlank", "attempt", "endOfPrefix", "otherPrefix", "initialBlankLine", "listItemPrefixWhitespace", "_closeFlow", "undefined", "notBlank", "furtherBlankLines", "listItemIndent", "notInCurrentItem", "parser", "constructs", "disable", "null", "includes", "tabSize", "afterPrefix"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/micromark-core-commonmark/dev/lib/list.js"], "sourcesContent": ["/**\n * @typedef {import('micromark-util-types').Code} Code\n * @typedef {import('micromark-util-types').Construct} Construct\n * @typedef {import('micromark-util-types').ContainerState} ContainerState\n * @typedef {import('micromark-util-types').Exiter} Exiter\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Tokenizer} Tokenizer\n */\n\nimport {factorySpace} from 'micromark-factory-space'\nimport {asciiDigit, markdownSpace} from 'micromark-util-character'\nimport {codes} from 'micromark-util-symbol/codes.js'\nimport {constants} from 'micromark-util-symbol/constants.js'\nimport {types} from 'micromark-util-symbol/types.js'\nimport {ok as assert} from 'uvu/assert'\nimport {blankLine} from './blank-line.js'\nimport {thematicBreak} from './thematic-break.js'\n\n/** @type {Construct} */\nexport const list = {\n  name: 'list',\n  tokenize: tokenizeListStart,\n  continuation: {tokenize: tokenizeListContinuation},\n  exit: tokenizeListEnd\n}\n\n/** @type {Construct} */\nconst listItemPrefixWhitespaceConstruct = {\n  tokenize: tokenizeListItemPrefixWhitespace,\n  partial: true\n}\n\n/** @type {Construct} */\nconst indentConstruct = {tokenize: tokenizeIndent, partial: true}\n\n// To do: `markdown-rs` parses list items on their own and later stitches them\n// together.\n\n/**\n * @type {Tokenizer}\n * @this {TokenizeContext}\n */\nfunction tokenizeListStart(effects, ok, nok) {\n  const self = this\n  const tail = self.events[self.events.length - 1]\n  let initialSize =\n    tail && tail[1].type === types.linePrefix\n      ? tail[2].sliceSerialize(tail[1], true).length\n      : 0\n  let size = 0\n\n  return start\n\n  /** @type {State} */\n  function start(code) {\n    assert(self.containerState, 'expected state')\n    const kind =\n      self.containerState.type ||\n      (code === codes.asterisk || code === codes.plusSign || code === codes.dash\n        ? types.listUnordered\n        : types.listOrdered)\n\n    if (\n      kind === types.listUnordered\n        ? !self.containerState.marker || code === self.containerState.marker\n        : asciiDigit(code)\n    ) {\n      if (!self.containerState.type) {\n        self.containerState.type = kind\n        effects.enter(kind, {_container: true})\n      }\n\n      if (kind === types.listUnordered) {\n        effects.enter(types.listItemPrefix)\n        return code === codes.asterisk || code === codes.dash\n          ? effects.check(thematicBreak, nok, atMarker)(code)\n          : atMarker(code)\n      }\n\n      if (!self.interrupt || code === codes.digit1) {\n        effects.enter(types.listItemPrefix)\n        effects.enter(types.listItemValue)\n        return inside(code)\n      }\n    }\n\n    return nok(code)\n  }\n\n  /** @type {State} */\n  function inside(code) {\n    assert(self.containerState, 'expected state')\n    if (asciiDigit(code) && ++size < constants.listItemValueSizeMax) {\n      effects.consume(code)\n      return inside\n    }\n\n    if (\n      (!self.interrupt || size < 2) &&\n      (self.containerState.marker\n        ? code === self.containerState.marker\n        : code === codes.rightParenthesis || code === codes.dot)\n    ) {\n      effects.exit(types.listItemValue)\n      return atMarker(code)\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * @type {State}\n   **/\n  function atMarker(code) {\n    assert(self.containerState, 'expected state')\n    assert(code !== codes.eof, 'eof (`null`) is not a marker')\n    effects.enter(types.listItemMarker)\n    effects.consume(code)\n    effects.exit(types.listItemMarker)\n    self.containerState.marker = self.containerState.marker || code\n    return effects.check(\n      blankLine,\n      // Can’t be empty when interrupting.\n      self.interrupt ? nok : onBlank,\n      effects.attempt(\n        listItemPrefixWhitespaceConstruct,\n        endOfPrefix,\n        otherPrefix\n      )\n    )\n  }\n\n  /** @type {State} */\n  function onBlank(code) {\n    assert(self.containerState, 'expected state')\n    self.containerState.initialBlankLine = true\n    initialSize++\n    return endOfPrefix(code)\n  }\n\n  /** @type {State} */\n  function otherPrefix(code) {\n    if (markdownSpace(code)) {\n      effects.enter(types.listItemPrefixWhitespace)\n      effects.consume(code)\n      effects.exit(types.listItemPrefixWhitespace)\n      return endOfPrefix\n    }\n\n    return nok(code)\n  }\n\n  /** @type {State} */\n  function endOfPrefix(code) {\n    assert(self.containerState, 'expected state')\n    self.containerState.size =\n      initialSize +\n      self.sliceSerialize(effects.exit(types.listItemPrefix), true).length\n    return ok(code)\n  }\n}\n\n/**\n * @type {Tokenizer}\n * @this {TokenizeContext}\n */\nfunction tokenizeListContinuation(effects, ok, nok) {\n  const self = this\n\n  assert(self.containerState, 'expected state')\n  self.containerState._closeFlow = undefined\n\n  return effects.check(blankLine, onBlank, notBlank)\n\n  /** @type {State} */\n  function onBlank(code) {\n    assert(self.containerState, 'expected state')\n    assert(typeof self.containerState.size === 'number', 'expected size')\n    self.containerState.furtherBlankLines =\n      self.containerState.furtherBlankLines ||\n      self.containerState.initialBlankLine\n\n    // We have a blank line.\n    // Still, try to consume at most the items size.\n    return factorySpace(\n      effects,\n      ok,\n      types.listItemIndent,\n      self.containerState.size + 1\n    )(code)\n  }\n\n  /** @type {State} */\n  function notBlank(code) {\n    assert(self.containerState, 'expected state')\n    if (self.containerState.furtherBlankLines || !markdownSpace(code)) {\n      self.containerState.furtherBlankLines = undefined\n      self.containerState.initialBlankLine = undefined\n      return notInCurrentItem(code)\n    }\n\n    self.containerState.furtherBlankLines = undefined\n    self.containerState.initialBlankLine = undefined\n    return effects.attempt(indentConstruct, ok, notInCurrentItem)(code)\n  }\n\n  /** @type {State} */\n  function notInCurrentItem(code) {\n    assert(self.containerState, 'expected state')\n    // While we do continue, we signal that the flow should be closed.\n    self.containerState._closeFlow = true\n    // As we’re closing flow, we’re no longer interrupting.\n    self.interrupt = undefined\n    // Always populated by defaults.\n    assert(\n      self.parser.constructs.disable.null,\n      'expected `disable.null` to be populated'\n    )\n    return factorySpace(\n      effects,\n      effects.attempt(list, ok, nok),\n      types.linePrefix,\n      self.parser.constructs.disable.null.includes('codeIndented')\n        ? undefined\n        : constants.tabSize\n    )(code)\n  }\n}\n\n/**\n * @type {Tokenizer}\n * @this {TokenizeContext}\n */\nfunction tokenizeIndent(effects, ok, nok) {\n  const self = this\n\n  assert(self.containerState, 'expected state')\n  assert(typeof self.containerState.size === 'number', 'expected size')\n\n  return factorySpace(\n    effects,\n    afterPrefix,\n    types.listItemIndent,\n    self.containerState.size + 1\n  )\n\n  /** @type {State} */\n  function afterPrefix(code) {\n    assert(self.containerState, 'expected state')\n    const tail = self.events[self.events.length - 1]\n    return tail &&\n      tail[1].type === types.listItemIndent &&\n      tail[2].sliceSerialize(tail[1], true).length === self.containerState.size\n      ? ok(code)\n      : nok(code)\n  }\n}\n\n/**\n * @type {Exiter}\n * @this {TokenizeContext}\n */\nfunction tokenizeListEnd(effects) {\n  assert(this.containerState, 'expected state')\n  assert(typeof this.containerState.type === 'string', 'expected type')\n  effects.exit(this.containerState.type)\n}\n\n/**\n * @type {Tokenizer}\n * @this {TokenizeContext}\n */\nfunction tokenizeListItemPrefixWhitespace(effects, ok, nok) {\n  const self = this\n\n  // Always populated by defaults.\n  assert(\n    self.parser.constructs.disable.null,\n    'expected `disable.null` to be populated'\n  )\n\n  return factorySpace(\n    effects,\n    afterPrefix,\n    types.listItemPrefixWhitespace,\n    self.parser.constructs.disable.null.includes('codeIndented')\n      ? undefined\n      : constants.tabSize + 1\n  )\n\n  /** @type {State} */\n  function afterPrefix(code) {\n    const tail = self.events[self.events.length - 1]\n\n    return !markdownSpace(code) &&\n      tail &&\n      tail[1].type === types.listItemPrefixWhitespace\n      ? ok(code)\n      : nok(code)\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,YAAY,QAAO,yBAAyB;AACpD,SAAQC,UAAU,EAAEC,aAAa,QAAO,0BAA0B;AAClE,SAAQC,KAAK,QAAO,gCAAgC;AACpD,SAAQC,SAAS,QAAO,oCAAoC;AAC5D,SAAQC,KAAK,QAAO,gCAAgC;AACpD,SAAQC,EAAE,IAAIC,MAAM,QAAO,YAAY;AACvC,SAAQC,SAAS,QAAO,iBAAiB;AACzC,SAAQC,aAAa,QAAO,qBAAqB;;AAEjD;AACA,OAAO,MAAMC,IAAI,GAAG;EAClBC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAEC,iBAAiB;EAC3BC,YAAY,EAAE;IAACF,QAAQ,EAAEG;EAAwB,CAAC;EAClDC,IAAI,EAAEC;AACR,CAAC;;AAED;AACA,MAAMC,iCAAiC,GAAG;EACxCN,QAAQ,EAAEO,gCAAgC;EAC1CC,OAAO,EAAE;AACX,CAAC;;AAED;AACA,MAAMC,eAAe,GAAG;EAACT,QAAQ,EAAEU,cAAc;EAAEF,OAAO,EAAE;AAAI,CAAC;;AAEjE;AACA;;AAEA;AACA;AACA;AACA;AACA,SAASP,iBAAiBA,CAACU,OAAO,EAAEjB,EAAE,EAAEkB,GAAG,EAAE;EAC3C,MAAMC,IAAI,GAAG,IAAI;EACjB,MAAMC,IAAI,GAAGD,IAAI,CAACE,MAAM,CAACF,IAAI,CAACE,MAAM,CAACC,MAAM,GAAG,CAAC,CAAC;EAChD,IAAIC,WAAW,GACbH,IAAI,IAAIA,IAAI,CAAC,CAAC,CAAC,CAACI,IAAI,KAAKzB,KAAK,CAAC0B,UAAU,GACrCL,IAAI,CAAC,CAAC,CAAC,CAACM,cAAc,CAACN,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAACE,MAAM,GAC5C,CAAC;EACP,IAAIK,IAAI,GAAG,CAAC;EAEZ,OAAOC,KAAK;;EAEZ;EACA,SAASA,KAAKA,CAACC,IAAI,EAAE;IACnB5B,MAAM,CAACkB,IAAI,CAACW,cAAc,EAAE,gBAAgB,CAAC;IAC7C,MAAMC,IAAI,GACRZ,IAAI,CAACW,cAAc,CAACN,IAAI,KACvBK,IAAI,KAAKhC,KAAK,CAACmC,QAAQ,IAAIH,IAAI,KAAKhC,KAAK,CAACoC,QAAQ,IAAIJ,IAAI,KAAKhC,KAAK,CAACqC,IAAI,GACtEnC,KAAK,CAACoC,aAAa,GACnBpC,KAAK,CAACqC,WAAW,CAAC;IAExB,IACEL,IAAI,KAAKhC,KAAK,CAACoC,aAAa,GACxB,CAAChB,IAAI,CAACW,cAAc,CAACO,MAAM,IAAIR,IAAI,KAAKV,IAAI,CAACW,cAAc,CAACO,MAAM,GAClE1C,UAAU,CAACkC,IAAI,CAAC,EACpB;MACA,IAAI,CAACV,IAAI,CAACW,cAAc,CAACN,IAAI,EAAE;QAC7BL,IAAI,CAACW,cAAc,CAACN,IAAI,GAAGO,IAAI;QAC/Bd,OAAO,CAACqB,KAAK,CAACP,IAAI,EAAE;UAACQ,UAAU,EAAE;QAAI,CAAC,CAAC;MACzC;MAEA,IAAIR,IAAI,KAAKhC,KAAK,CAACoC,aAAa,EAAE;QAChClB,OAAO,CAACqB,KAAK,CAACvC,KAAK,CAACyC,cAAc,CAAC;QACnC,OAAOX,IAAI,KAAKhC,KAAK,CAACmC,QAAQ,IAAIH,IAAI,KAAKhC,KAAK,CAACqC,IAAI,GACjDjB,OAAO,CAACwB,KAAK,CAACtC,aAAa,EAAEe,GAAG,EAAEwB,QAAQ,CAAC,CAACb,IAAI,CAAC,GACjDa,QAAQ,CAACb,IAAI,CAAC;MACpB;MAEA,IAAI,CAACV,IAAI,CAACwB,SAAS,IAAId,IAAI,KAAKhC,KAAK,CAAC+C,MAAM,EAAE;QAC5C3B,OAAO,CAACqB,KAAK,CAACvC,KAAK,CAACyC,cAAc,CAAC;QACnCvB,OAAO,CAACqB,KAAK,CAACvC,KAAK,CAAC8C,aAAa,CAAC;QAClC,OAAOC,MAAM,CAACjB,IAAI,CAAC;MACrB;IACF;IAEA,OAAOX,GAAG,CAACW,IAAI,CAAC;EAClB;;EAEA;EACA,SAASiB,MAAMA,CAACjB,IAAI,EAAE;IACpB5B,MAAM,CAACkB,IAAI,CAACW,cAAc,EAAE,gBAAgB,CAAC;IAC7C,IAAInC,UAAU,CAACkC,IAAI,CAAC,IAAI,EAAEF,IAAI,GAAG7B,SAAS,CAACiD,oBAAoB,EAAE;MAC/D9B,OAAO,CAAC+B,OAAO,CAACnB,IAAI,CAAC;MACrB,OAAOiB,MAAM;IACf;IAEA,IACE,CAAC,CAAC3B,IAAI,CAACwB,SAAS,IAAIhB,IAAI,GAAG,CAAC,MAC3BR,IAAI,CAACW,cAAc,CAACO,MAAM,GACvBR,IAAI,KAAKV,IAAI,CAACW,cAAc,CAACO,MAAM,GACnCR,IAAI,KAAKhC,KAAK,CAACoD,gBAAgB,IAAIpB,IAAI,KAAKhC,KAAK,CAACqD,GAAG,CAAC,EAC1D;MACAjC,OAAO,CAACP,IAAI,CAACX,KAAK,CAAC8C,aAAa,CAAC;MACjC,OAAOH,QAAQ,CAACb,IAAI,CAAC;IACvB;IAEA,OAAOX,GAAG,CAACW,IAAI,CAAC;EAClB;;EAEA;AACF;AACA;EACE,SAASa,QAAQA,CAACb,IAAI,EAAE;IACtB5B,MAAM,CAACkB,IAAI,CAACW,cAAc,EAAE,gBAAgB,CAAC;IAC7C7B,MAAM,CAAC4B,IAAI,KAAKhC,KAAK,CAACsD,GAAG,EAAE,8BAA8B,CAAC;IAC1DlC,OAAO,CAACqB,KAAK,CAACvC,KAAK,CAACqD,cAAc,CAAC;IACnCnC,OAAO,CAAC+B,OAAO,CAACnB,IAAI,CAAC;IACrBZ,OAAO,CAACP,IAAI,CAACX,KAAK,CAACqD,cAAc,CAAC;IAClCjC,IAAI,CAACW,cAAc,CAACO,MAAM,GAAGlB,IAAI,CAACW,cAAc,CAACO,MAAM,IAAIR,IAAI;IAC/D,OAAOZ,OAAO,CAACwB,KAAK,CAClBvC,SAAS;IACT;IACAiB,IAAI,CAACwB,SAAS,GAAGzB,GAAG,GAAGmC,OAAO,EAC9BpC,OAAO,CAACqC,OAAO,CACb1C,iCAAiC,EACjC2C,WAAW,EACXC,WAAW,CACZ,CACF;EACH;;EAEA;EACA,SAASH,OAAOA,CAACxB,IAAI,EAAE;IACrB5B,MAAM,CAACkB,IAAI,CAACW,cAAc,EAAE,gBAAgB,CAAC;IAC7CX,IAAI,CAACW,cAAc,CAAC2B,gBAAgB,GAAG,IAAI;IAC3ClC,WAAW,EAAE;IACb,OAAOgC,WAAW,CAAC1B,IAAI,CAAC;EAC1B;;EAEA;EACA,SAAS2B,WAAWA,CAAC3B,IAAI,EAAE;IACzB,IAAIjC,aAAa,CAACiC,IAAI,CAAC,EAAE;MACvBZ,OAAO,CAACqB,KAAK,CAACvC,KAAK,CAAC2D,wBAAwB,CAAC;MAC7CzC,OAAO,CAAC+B,OAAO,CAACnB,IAAI,CAAC;MACrBZ,OAAO,CAACP,IAAI,CAACX,KAAK,CAAC2D,wBAAwB,CAAC;MAC5C,OAAOH,WAAW;IACpB;IAEA,OAAOrC,GAAG,CAACW,IAAI,CAAC;EAClB;;EAEA;EACA,SAAS0B,WAAWA,CAAC1B,IAAI,EAAE;IACzB5B,MAAM,CAACkB,IAAI,CAACW,cAAc,EAAE,gBAAgB,CAAC;IAC7CX,IAAI,CAACW,cAAc,CAACH,IAAI,GACtBJ,WAAW,GACXJ,IAAI,CAACO,cAAc,CAACT,OAAO,CAACP,IAAI,CAACX,KAAK,CAACyC,cAAc,CAAC,EAAE,IAAI,CAAC,CAAClB,MAAM;IACtE,OAAOtB,EAAE,CAAC6B,IAAI,CAAC;EACjB;AACF;;AAEA;AACA;AACA;AACA;AACA,SAASpB,wBAAwBA,CAACQ,OAAO,EAAEjB,EAAE,EAAEkB,GAAG,EAAE;EAClD,MAAMC,IAAI,GAAG,IAAI;EAEjBlB,MAAM,CAACkB,IAAI,CAACW,cAAc,EAAE,gBAAgB,CAAC;EAC7CX,IAAI,CAACW,cAAc,CAAC6B,UAAU,GAAGC,SAAS;EAE1C,OAAO3C,OAAO,CAACwB,KAAK,CAACvC,SAAS,EAAEmD,OAAO,EAAEQ,QAAQ,CAAC;;EAElD;EACA,SAASR,OAAOA,CAACxB,IAAI,EAAE;IACrB5B,MAAM,CAACkB,IAAI,CAACW,cAAc,EAAE,gBAAgB,CAAC;IAC7C7B,MAAM,CAAC,OAAOkB,IAAI,CAACW,cAAc,CAACH,IAAI,KAAK,QAAQ,EAAE,eAAe,CAAC;IACrER,IAAI,CAACW,cAAc,CAACgC,iBAAiB,GACnC3C,IAAI,CAACW,cAAc,CAACgC,iBAAiB,IACrC3C,IAAI,CAACW,cAAc,CAAC2B,gBAAgB;;IAEtC;IACA;IACA,OAAO/D,YAAY,CACjBuB,OAAO,EACPjB,EAAE,EACFD,KAAK,CAACgE,cAAc,EACpB5C,IAAI,CAACW,cAAc,CAACH,IAAI,GAAG,CAAC,CAC7B,CAACE,IAAI,CAAC;EACT;;EAEA;EACA,SAASgC,QAAQA,CAAChC,IAAI,EAAE;IACtB5B,MAAM,CAACkB,IAAI,CAACW,cAAc,EAAE,gBAAgB,CAAC;IAC7C,IAAIX,IAAI,CAACW,cAAc,CAACgC,iBAAiB,IAAI,CAAClE,aAAa,CAACiC,IAAI,CAAC,EAAE;MACjEV,IAAI,CAACW,cAAc,CAACgC,iBAAiB,GAAGF,SAAS;MACjDzC,IAAI,CAACW,cAAc,CAAC2B,gBAAgB,GAAGG,SAAS;MAChD,OAAOI,gBAAgB,CAACnC,IAAI,CAAC;IAC/B;IAEAV,IAAI,CAACW,cAAc,CAACgC,iBAAiB,GAAGF,SAAS;IACjDzC,IAAI,CAACW,cAAc,CAAC2B,gBAAgB,GAAGG,SAAS;IAChD,OAAO3C,OAAO,CAACqC,OAAO,CAACvC,eAAe,EAAEf,EAAE,EAAEgE,gBAAgB,CAAC,CAACnC,IAAI,CAAC;EACrE;;EAEA;EACA,SAASmC,gBAAgBA,CAACnC,IAAI,EAAE;IAC9B5B,MAAM,CAACkB,IAAI,CAACW,cAAc,EAAE,gBAAgB,CAAC;IAC7C;IACAX,IAAI,CAACW,cAAc,CAAC6B,UAAU,GAAG,IAAI;IACrC;IACAxC,IAAI,CAACwB,SAAS,GAAGiB,SAAS;IAC1B;IACA3D,MAAM,CACJkB,IAAI,CAAC8C,MAAM,CAACC,UAAU,CAACC,OAAO,CAACC,IAAI,EACnC,yCAAyC,CAC1C;IACD,OAAO1E,YAAY,CACjBuB,OAAO,EACPA,OAAO,CAACqC,OAAO,CAAClD,IAAI,EAAEJ,EAAE,EAAEkB,GAAG,CAAC,EAC9BnB,KAAK,CAAC0B,UAAU,EAChBN,IAAI,CAAC8C,MAAM,CAACC,UAAU,CAACC,OAAO,CAACC,IAAI,CAACC,QAAQ,CAAC,cAAc,CAAC,GACxDT,SAAS,GACT9D,SAAS,CAACwE,OAAO,CACtB,CAACzC,IAAI,CAAC;EACT;AACF;;AAEA;AACA;AACA;AACA;AACA,SAASb,cAAcA,CAACC,OAAO,EAAEjB,EAAE,EAAEkB,GAAG,EAAE;EACxC,MAAMC,IAAI,GAAG,IAAI;EAEjBlB,MAAM,CAACkB,IAAI,CAACW,cAAc,EAAE,gBAAgB,CAAC;EAC7C7B,MAAM,CAAC,OAAOkB,IAAI,CAACW,cAAc,CAACH,IAAI,KAAK,QAAQ,EAAE,eAAe,CAAC;EAErE,OAAOjC,YAAY,CACjBuB,OAAO,EACPsD,WAAW,EACXxE,KAAK,CAACgE,cAAc,EACpB5C,IAAI,CAACW,cAAc,CAACH,IAAI,GAAG,CAAC,CAC7B;;EAED;EACA,SAAS4C,WAAWA,CAAC1C,IAAI,EAAE;IACzB5B,MAAM,CAACkB,IAAI,CAACW,cAAc,EAAE,gBAAgB,CAAC;IAC7C,MAAMV,IAAI,GAAGD,IAAI,CAACE,MAAM,CAACF,IAAI,CAACE,MAAM,CAACC,MAAM,GAAG,CAAC,CAAC;IAChD,OAAOF,IAAI,IACTA,IAAI,CAAC,CAAC,CAAC,CAACI,IAAI,KAAKzB,KAAK,CAACgE,cAAc,IACrC3C,IAAI,CAAC,CAAC,CAAC,CAACM,cAAc,CAACN,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAACE,MAAM,KAAKH,IAAI,CAACW,cAAc,CAACH,IAAI,GACvE3B,EAAE,CAAC6B,IAAI,CAAC,GACRX,GAAG,CAACW,IAAI,CAAC;EACf;AACF;;AAEA;AACA;AACA;AACA;AACA,SAASlB,eAAeA,CAACM,OAAO,EAAE;EAChChB,MAAM,CAAC,IAAI,CAAC6B,cAAc,EAAE,gBAAgB,CAAC;EAC7C7B,MAAM,CAAC,OAAO,IAAI,CAAC6B,cAAc,CAACN,IAAI,KAAK,QAAQ,EAAE,eAAe,CAAC;EACrEP,OAAO,CAACP,IAAI,CAAC,IAAI,CAACoB,cAAc,CAACN,IAAI,CAAC;AACxC;;AAEA;AACA;AACA;AACA;AACA,SAASX,gCAAgCA,CAACI,OAAO,EAAEjB,EAAE,EAAEkB,GAAG,EAAE;EAC1D,MAAMC,IAAI,GAAG,IAAI;;EAEjB;EACAlB,MAAM,CACJkB,IAAI,CAAC8C,MAAM,CAACC,UAAU,CAACC,OAAO,CAACC,IAAI,EACnC,yCAAyC,CAC1C;EAED,OAAO1E,YAAY,CACjBuB,OAAO,EACPsD,WAAW,EACXxE,KAAK,CAAC2D,wBAAwB,EAC9BvC,IAAI,CAAC8C,MAAM,CAACC,UAAU,CAACC,OAAO,CAACC,IAAI,CAACC,QAAQ,CAAC,cAAc,CAAC,GACxDT,SAAS,GACT9D,SAAS,CAACwE,OAAO,GAAG,CAAC,CAC1B;;EAED;EACA,SAASC,WAAWA,CAAC1C,IAAI,EAAE;IACzB,MAAMT,IAAI,GAAGD,IAAI,CAACE,MAAM,CAACF,IAAI,CAACE,MAAM,CAACC,MAAM,GAAG,CAAC,CAAC;IAEhD,OAAO,CAAC1B,aAAa,CAACiC,IAAI,CAAC,IACzBT,IAAI,IACJA,IAAI,CAAC,CAAC,CAAC,CAACI,IAAI,KAAKzB,KAAK,CAAC2D,wBAAwB,GAC7C1D,EAAE,CAAC6B,IAAI,CAAC,GACRX,GAAG,CAACW,IAAI,CAAC;EACf;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}