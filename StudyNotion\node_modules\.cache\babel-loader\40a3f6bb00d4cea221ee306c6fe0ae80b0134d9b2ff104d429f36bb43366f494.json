{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar _classCallCheck2 = _interopRequireDefault(require(\"@babel/runtime/helpers/classCallCheck\"));\nvar _createClass2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createClass\"));\nvar _inherits2 = _interopRequireDefault(require(\"@babel/runtime/helpers/inherits\"));\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"@babel/runtime/helpers/possibleConstructorReturn\"));\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"@babel/runtime/helpers/getPrototypeOf\"));\nvar _react = _interopRequireDefault(require(\"react\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _tableContext = require(\"../utils/tableContext\");\nvar _allowed = _interopRequireDefault(require(\"../utils/allowed\"));\nfunction _createSuper(Derived) {\n  var hasNativeReflectConstruct = _isNativeReflectConstruct();\n  return function _createSuperInternal() {\n    var Super = (0, _getPrototypeOf2[\"default\"])(Derived),\n      result;\n    if (hasNativeReflectConstruct) {\n      var NewTarget = (0, _getPrototypeOf2[\"default\"])(this).constructor;\n      result = Reflect.construct(Super, arguments, NewTarget);\n    } else {\n      result = Super.apply(this, arguments);\n    }\n    return (0, _possibleConstructorReturn2[\"default\"])(this, result);\n  };\n}\nfunction _isNativeReflectConstruct() {\n  if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n  if (Reflect.construct.sham) return false;\n  if (typeof Proxy === \"function\") return true;\n  try {\n    Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\nvar Table = /*#__PURE__*/function (_React$Component) {\n  (0, _inherits2[\"default\"])(Table, _React$Component);\n  var _super = _createSuper(Table);\n  function Table(props) {\n    var _this;\n    (0, _classCallCheck2[\"default\"])(this, Table);\n    _this = _super.call(this, props);\n    _this.state = {\n      headers: {}\n    };\n    return _this;\n  }\n  (0, _createClass2[\"default\"])(Table, [{\n    key: \"render\",\n    value: function render() {\n      var headers = this.state.headers;\n      var _this$props = this.props,\n        className = _this$props.className,\n        forwardedRef = _this$props.forwardedRef;\n      var classes = \"\".concat(className || '', \" responsiveTable\");\n      return /*#__PURE__*/_react[\"default\"].createElement(_tableContext.Provider, {\n        value: headers\n      }, /*#__PURE__*/_react[\"default\"].createElement(\"table\", (0, _extends2[\"default\"])({\n        \"data-testid\": \"table\"\n      }, (0, _allowed[\"default\"])(this.props), {\n        className: classes,\n        ref: forwardedRef\n      })));\n    }\n  }]);\n  return Table;\n}(_react[\"default\"].Component);\nTable.propTypes = {\n  className: _propTypes[\"default\"].string,\n  forwardedRef: _propTypes[\"default\"].oneOfType([_propTypes[\"default\"].func, _propTypes[\"default\"].shape({\n    current: _propTypes[\"default\"].instanceOf(global.Element)\n  })])\n};\nTable.defaultProps = {\n  className: undefined,\n  forwardedRef: undefined\n};\nvar TableForwardRef = /*#__PURE__*/_react[\"default\"].forwardRef(function (props, ref) {\n  return /*#__PURE__*/_react[\"default\"].createElement(Table, (0, _extends2[\"default\"])({}, props, {\n    forwardedRef: ref\n  }));\n});\nTableForwardRef.displayName = Table.name;\nvar _default = TableForwardRef;\nexports[\"default\"] = _default;", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "_extends2", "_classCallCheck2", "_createClass2", "_inherits2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_react", "_propTypes", "_tableContext", "_allowed", "_createSuper", "Derived", "hasNativeReflectConstruct", "_isNativeReflectConstruct", "_createSuperInternal", "Super", "result", "<PERSON><PERSON><PERSON><PERSON>", "constructor", "Reflect", "construct", "arguments", "apply", "sham", "Proxy", "Boolean", "prototype", "valueOf", "call", "e", "Table", "_React$Component", "_super", "props", "_this", "state", "headers", "key", "render", "_this$props", "className", "forwardedRef", "classes", "concat", "createElement", "Provider", "ref", "Component", "propTypes", "string", "oneOfType", "func", "shape", "current", "instanceOf", "global", "Element", "defaultProps", "undefined", "TableForwardRef", "forwardRef", "displayName", "name", "_default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/react-super-responsive-table/dist/components/Table.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\n\nvar _classCallCheck2 = _interopRequireDefault(require(\"@babel/runtime/helpers/classCallCheck\"));\n\nvar _createClass2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createClass\"));\n\nvar _inherits2 = _interopRequireDefault(require(\"@babel/runtime/helpers/inherits\"));\n\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"@babel/runtime/helpers/possibleConstructorReturn\"));\n\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"@babel/runtime/helpers/getPrototypeOf\"));\n\nvar _react = _interopRequireDefault(require(\"react\"));\n\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\n\nvar _tableContext = require(\"../utils/tableContext\");\n\nvar _allowed = _interopRequireDefault(require(\"../utils/allowed\"));\n\nfunction _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = (0, _getPrototypeOf2[\"default\"])(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = (0, _getPrototypeOf2[\"default\"])(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return (0, _possibleConstructorReturn2[\"default\"])(this, result); }; }\n\nfunction _isNativeReflectConstruct() { if (typeof Reflect === \"undefined\" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === \"function\") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }\n\nvar Table = /*#__PURE__*/function (_React$Component) {\n  (0, _inherits2[\"default\"])(Table, _React$Component);\n\n  var _super = _createSuper(Table);\n\n  function Table(props) {\n    var _this;\n\n    (0, _classCallCheck2[\"default\"])(this, Table);\n    _this = _super.call(this, props);\n    _this.state = {\n      headers: {}\n    };\n    return _this;\n  }\n\n  (0, _createClass2[\"default\"])(Table, [{\n    key: \"render\",\n    value: function render() {\n      var headers = this.state.headers;\n      var _this$props = this.props,\n          className = _this$props.className,\n          forwardedRef = _this$props.forwardedRef;\n      var classes = \"\".concat(className || '', \" responsiveTable\");\n      return /*#__PURE__*/_react[\"default\"].createElement(_tableContext.Provider, {\n        value: headers\n      }, /*#__PURE__*/_react[\"default\"].createElement(\"table\", (0, _extends2[\"default\"])({\n        \"data-testid\": \"table\"\n      }, (0, _allowed[\"default\"])(this.props), {\n        className: classes,\n        ref: forwardedRef\n      })));\n    }\n  }]);\n  return Table;\n}(_react[\"default\"].Component);\n\nTable.propTypes = {\n  className: _propTypes[\"default\"].string,\n  forwardedRef: _propTypes[\"default\"].oneOfType([_propTypes[\"default\"].func, _propTypes[\"default\"].shape({\n    current: _propTypes[\"default\"].instanceOf(global.Element)\n  })])\n};\nTable.defaultProps = {\n  className: undefined,\n  forwardedRef: undefined\n};\n\nvar TableForwardRef = /*#__PURE__*/_react[\"default\"].forwardRef(function (props, ref) {\n  return /*#__PURE__*/_react[\"default\"].createElement(Table, (0, _extends2[\"default\"])({}, props, {\n    forwardedRef: ref\n  }));\n});\n\nTableForwardRef.displayName = Table.name;\nvar _default = TableForwardRef;\nexports[\"default\"] = _default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC;AAEpFC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;AAE3B,IAAIE,SAAS,GAAGN,sBAAsB,CAACC,OAAO,CAAC,gCAAgC,CAAC,CAAC;AAEjF,IAAIM,gBAAgB,GAAGP,sBAAsB,CAACC,OAAO,CAAC,uCAAuC,CAAC,CAAC;AAE/F,IAAIO,aAAa,GAAGR,sBAAsB,CAACC,OAAO,CAAC,oCAAoC,CAAC,CAAC;AAEzF,IAAIQ,UAAU,GAAGT,sBAAsB,CAACC,OAAO,CAAC,iCAAiC,CAAC,CAAC;AAEnF,IAAIS,2BAA2B,GAAGV,sBAAsB,CAACC,OAAO,CAAC,kDAAkD,CAAC,CAAC;AAErH,IAAIU,gBAAgB,GAAGX,sBAAsB,CAACC,OAAO,CAAC,uCAAuC,CAAC,CAAC;AAE/F,IAAIW,MAAM,GAAGZ,sBAAsB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AAErD,IAAIY,UAAU,GAAGb,sBAAsB,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;AAE9D,IAAIa,aAAa,GAAGb,OAAO,CAAC,uBAAuB,CAAC;AAEpD,IAAIc,QAAQ,GAAGf,sBAAsB,CAACC,OAAO,CAAC,kBAAkB,CAAC,CAAC;AAElE,SAASe,YAAYA,CAACC,OAAO,EAAE;EAAE,IAAIC,yBAAyB,GAAGC,yBAAyB,EAAE;EAAE,OAAO,SAASC,oBAAoBA,CAAA,EAAG;IAAE,IAAIC,KAAK,GAAG,CAAC,CAAC,EAAEV,gBAAgB,CAAC,SAAS,CAAC,EAAEM,OAAO,CAAC;MAAEK,MAAM;IAAE,IAAIJ,yBAAyB,EAAE;MAAE,IAAIK,SAAS,GAAG,CAAC,CAAC,EAAEZ,gBAAgB,CAAC,SAAS,CAAC,EAAE,IAAI,CAAC,CAACa,WAAW;MAAEF,MAAM,GAAGG,OAAO,CAACC,SAAS,CAACL,KAAK,EAAEM,SAAS,EAAEJ,SAAS,CAAC;IAAE,CAAC,MAAM;MAAED,MAAM,GAAGD,KAAK,CAACO,KAAK,CAAC,IAAI,EAAED,SAAS,CAAC;IAAE;IAAE,OAAO,CAAC,CAAC,EAAEjB,2BAA2B,CAAC,SAAS,CAAC,EAAE,IAAI,EAAEY,MAAM,CAAC;EAAE,CAAC;AAAE;AAE3d,SAASH,yBAAyBA,CAAA,EAAG;EAAE,IAAI,OAAOM,OAAO,KAAK,WAAW,IAAI,CAACA,OAAO,CAACC,SAAS,EAAE,OAAO,KAAK;EAAE,IAAID,OAAO,CAACC,SAAS,CAACG,IAAI,EAAE,OAAO,KAAK;EAAE,IAAI,OAAOC,KAAK,KAAK,UAAU,EAAE,OAAO,IAAI;EAAE,IAAI;IAAEC,OAAO,CAACC,SAAS,CAACC,OAAO,CAACC,IAAI,CAACT,OAAO,CAACC,SAAS,CAACK,OAAO,EAAE,EAAE,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;IAAE,OAAO,IAAI;EAAE,CAAC,CAAC,OAAOI,CAAC,EAAE;IAAE,OAAO,KAAK;EAAE;AAAE;AAExU,IAAIC,KAAK,GAAG,aAAa,UAAUC,gBAAgB,EAAE;EACnD,CAAC,CAAC,EAAE5B,UAAU,CAAC,SAAS,CAAC,EAAE2B,KAAK,EAAEC,gBAAgB,CAAC;EAEnD,IAAIC,MAAM,GAAGtB,YAAY,CAACoB,KAAK,CAAC;EAEhC,SAASA,KAAKA,CAACG,KAAK,EAAE;IACpB,IAAIC,KAAK;IAET,CAAC,CAAC,EAAEjC,gBAAgB,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE6B,KAAK,CAAC;IAC7CI,KAAK,GAAGF,MAAM,CAACJ,IAAI,CAAC,IAAI,EAAEK,KAAK,CAAC;IAChCC,KAAK,CAACC,KAAK,GAAG;MACZC,OAAO,EAAE,CAAC;IACZ,CAAC;IACD,OAAOF,KAAK;EACd;EAEA,CAAC,CAAC,EAAEhC,aAAa,CAAC,SAAS,CAAC,EAAE4B,KAAK,EAAE,CAAC;IACpCO,GAAG,EAAE,QAAQ;IACbtC,KAAK,EAAE,SAASuC,MAAMA,CAAA,EAAG;MACvB,IAAIF,OAAO,GAAG,IAAI,CAACD,KAAK,CAACC,OAAO;MAChC,IAAIG,WAAW,GAAG,IAAI,CAACN,KAAK;QACxBO,SAAS,GAAGD,WAAW,CAACC,SAAS;QACjCC,YAAY,GAAGF,WAAW,CAACE,YAAY;MAC3C,IAAIC,OAAO,GAAG,EAAE,CAACC,MAAM,CAACH,SAAS,IAAI,EAAE,EAAE,kBAAkB,CAAC;MAC5D,OAAO,aAAalC,MAAM,CAAC,SAAS,CAAC,CAACsC,aAAa,CAACpC,aAAa,CAACqC,QAAQ,EAAE;QAC1E9C,KAAK,EAAEqC;MACT,CAAC,EAAE,aAAa9B,MAAM,CAAC,SAAS,CAAC,CAACsC,aAAa,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE5C,SAAS,CAAC,SAAS,CAAC,EAAE;QACjF,aAAa,EAAE;MACjB,CAAC,EAAE,CAAC,CAAC,EAAES,QAAQ,CAAC,SAAS,CAAC,EAAE,IAAI,CAACwB,KAAK,CAAC,EAAE;QACvCO,SAAS,EAAEE,OAAO;QAClBI,GAAG,EAAEL;MACP,CAAC,CAAC,CAAC,CAAC;IACN;EACF,CAAC,CAAC,CAAC;EACH,OAAOX,KAAK;AACd,CAAC,CAACxB,MAAM,CAAC,SAAS,CAAC,CAACyC,SAAS,CAAC;AAE9BjB,KAAK,CAACkB,SAAS,GAAG;EAChBR,SAAS,EAAEjC,UAAU,CAAC,SAAS,CAAC,CAAC0C,MAAM;EACvCR,YAAY,EAAElC,UAAU,CAAC,SAAS,CAAC,CAAC2C,SAAS,CAAC,CAAC3C,UAAU,CAAC,SAAS,CAAC,CAAC4C,IAAI,EAAE5C,UAAU,CAAC,SAAS,CAAC,CAAC6C,KAAK,CAAC;IACrGC,OAAO,EAAE9C,UAAU,CAAC,SAAS,CAAC,CAAC+C,UAAU,CAACC,MAAM,CAACC,OAAO;EAC1D,CAAC,CAAC,CAAC;AACL,CAAC;AACD1B,KAAK,CAAC2B,YAAY,GAAG;EACnBjB,SAAS,EAAEkB,SAAS;EACpBjB,YAAY,EAAEiB;AAChB,CAAC;AAED,IAAIC,eAAe,GAAG,aAAarD,MAAM,CAAC,SAAS,CAAC,CAACsD,UAAU,CAAC,UAAU3B,KAAK,EAAEa,GAAG,EAAE;EACpF,OAAO,aAAaxC,MAAM,CAAC,SAAS,CAAC,CAACsC,aAAa,CAACd,KAAK,EAAE,CAAC,CAAC,EAAE9B,SAAS,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAEiC,KAAK,EAAE;IAC9FQ,YAAY,EAAEK;EAChB,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AAEFa,eAAe,CAACE,WAAW,GAAG/B,KAAK,CAACgC,IAAI;AACxC,IAAIC,QAAQ,GAAGJ,eAAe;AAC9B7D,OAAO,CAAC,SAAS,CAAC,GAAGiE,QAAQ"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}