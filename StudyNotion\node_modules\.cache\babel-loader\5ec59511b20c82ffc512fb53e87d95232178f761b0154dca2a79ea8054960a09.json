{"ast": null, "code": "function isObject(o) {\n  return typeof o === 'object' && o !== null && o.constructor && Object.prototype.toString.call(o).slice(8, -1) === 'Object';\n}\nfunction extend(target, src) {\n  const noExtend = ['__proto__', 'constructor', 'prototype'];\n  Object.keys(src).filter(key => noExtend.indexOf(key) < 0).forEach(key => {\n    if (typeof target[key] === 'undefined') target[key] = src[key];else if (isObject(src[key]) && isObject(target[key]) && Object.keys(src[key]).length > 0) {\n      if (src[key].__swiper__) target[key] = src[key];else extend(target[key], src[key]);\n    } else {\n      target[key] = src[key];\n    }\n  });\n}\nfunction needsNavigation() {\n  let params = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  return params.navigation && typeof params.navigation.nextEl === 'undefined' && typeof params.navigation.prevEl === 'undefined';\n}\nfunction needsPagination() {\n  let params = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  return params.pagination && typeof params.pagination.el === 'undefined';\n}\nfunction needsScrollbar() {\n  let params = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  return params.scrollbar && typeof params.scrollbar.el === 'undefined';\n}\nfunction uniqueClasses() {\n  let classNames = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '';\n  const classes = classNames.split(' ').map(c => c.trim()).filter(c => !!c);\n  const unique = [];\n  classes.forEach(c => {\n    if (unique.indexOf(c) < 0) unique.push(c);\n  });\n  return unique.join(' ');\n}\nfunction attrToProp() {\n  let attrName = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '';\n  return attrName.replace(/-[a-z]/g, l => l.toUpperCase().replace('-', ''));\n}\nfunction wrapperClass() {\n  let className = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '';\n  if (!className) return 'swiper-wrapper';\n  if (!className.includes('swiper-wrapper')) return `swiper-wrapper ${className}`;\n  return className;\n}\nexport { isObject, extend, needsNavigation, needsPagination, needsScrollbar, uniqueClasses, attrToProp, wrapperClass };", "map": {"version": 3, "names": ["isObject", "o", "constructor", "Object", "prototype", "toString", "call", "slice", "extend", "target", "src", "noExtend", "keys", "filter", "key", "indexOf", "for<PERSON>ach", "length", "__swiper__", "needsNavigation", "params", "arguments", "undefined", "navigation", "nextEl", "prevEl", "needsPagination", "pagination", "el", "needsScrollbar", "scrollbar", "uniqueClasses", "classNames", "classes", "split", "map", "c", "trim", "unique", "push", "join", "attrToProp", "attrName", "replace", "l", "toUpperCase", "wrapperClass", "className", "includes"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/components-shared/utils.js"], "sourcesContent": ["function isObject(o) {\n  return typeof o === 'object' && o !== null && o.constructor && Object.prototype.toString.call(o).slice(8, -1) === 'Object';\n}\nfunction extend(target, src) {\n  const noExtend = ['__proto__', 'constructor', 'prototype'];\n  Object.keys(src).filter(key => noExtend.indexOf(key) < 0).forEach(key => {\n    if (typeof target[key] === 'undefined') target[key] = src[key];else if (isObject(src[key]) && isObject(target[key]) && Object.keys(src[key]).length > 0) {\n      if (src[key].__swiper__) target[key] = src[key];else extend(target[key], src[key]);\n    } else {\n      target[key] = src[key];\n    }\n  });\n}\nfunction needsNavigation(params = {}) {\n  return params.navigation && typeof params.navigation.nextEl === 'undefined' && typeof params.navigation.prevEl === 'undefined';\n}\nfunction needsPagination(params = {}) {\n  return params.pagination && typeof params.pagination.el === 'undefined';\n}\nfunction needsScrollbar(params = {}) {\n  return params.scrollbar && typeof params.scrollbar.el === 'undefined';\n}\nfunction uniqueClasses(classNames = '') {\n  const classes = classNames.split(' ').map(c => c.trim()).filter(c => !!c);\n  const unique = [];\n  classes.forEach(c => {\n    if (unique.indexOf(c) < 0) unique.push(c);\n  });\n  return unique.join(' ');\n}\nfunction attrToProp(attrName = '') {\n  return attrName.replace(/-[a-z]/g, l => l.toUpperCase().replace('-', ''));\n}\nfunction wrapperClass(className = '') {\n  if (!className) return 'swiper-wrapper';\n  if (!className.includes('swiper-wrapper')) return `swiper-wrapper ${className}`;\n  return className;\n}\nexport { isObject, extend, needsNavigation, needsPagination, needsScrollbar, uniqueClasses, attrToProp, wrapperClass };"], "mappings": "AAAA,SAASA,QAAQA,CAACC,CAAC,EAAE;EACnB,OAAO,OAAOA,CAAC,KAAK,QAAQ,IAAIA,CAAC,KAAK,IAAI,IAAIA,CAAC,CAACC,WAAW,IAAIC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACL,CAAC,CAAC,CAACM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,QAAQ;AAC5H;AACA,SAASC,MAAMA,CAACC,MAAM,EAAEC,GAAG,EAAE;EAC3B,MAAMC,QAAQ,GAAG,CAAC,WAAW,EAAE,aAAa,EAAE,WAAW,CAAC;EAC1DR,MAAM,CAACS,IAAI,CAACF,GAAG,CAAC,CAACG,MAAM,CAACC,GAAG,IAAIH,QAAQ,CAACI,OAAO,CAACD,GAAG,CAAC,GAAG,CAAC,CAAC,CAACE,OAAO,CAACF,GAAG,IAAI;IACvE,IAAI,OAAOL,MAAM,CAACK,GAAG,CAAC,KAAK,WAAW,EAAEL,MAAM,CAACK,GAAG,CAAC,GAAGJ,GAAG,CAACI,GAAG,CAAC,CAAC,KAAK,IAAId,QAAQ,CAACU,GAAG,CAACI,GAAG,CAAC,CAAC,IAAId,QAAQ,CAACS,MAAM,CAACK,GAAG,CAAC,CAAC,IAAIX,MAAM,CAACS,IAAI,CAACF,GAAG,CAACI,GAAG,CAAC,CAAC,CAACG,MAAM,GAAG,CAAC,EAAE;MACvJ,IAAIP,GAAG,CAACI,GAAG,CAAC,CAACI,UAAU,EAAET,MAAM,CAACK,GAAG,CAAC,GAAGJ,GAAG,CAACI,GAAG,CAAC,CAAC,KAAKN,MAAM,CAACC,MAAM,CAACK,GAAG,CAAC,EAAEJ,GAAG,CAACI,GAAG,CAAC,CAAC;IACpF,CAAC,MAAM;MACLL,MAAM,CAACK,GAAG,CAAC,GAAGJ,GAAG,CAACI,GAAG,CAAC;IACxB;EACF,CAAC,CAAC;AACJ;AACA,SAASK,eAAeA,CAAA,EAAc;EAAA,IAAbC,MAAM,GAAAC,SAAA,CAAAJ,MAAA,QAAAI,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC,CAAC;EAClC,OAAOD,MAAM,CAACG,UAAU,IAAI,OAAOH,MAAM,CAACG,UAAU,CAACC,MAAM,KAAK,WAAW,IAAI,OAAOJ,MAAM,CAACG,UAAU,CAACE,MAAM,KAAK,WAAW;AAChI;AACA,SAASC,eAAeA,CAAA,EAAc;EAAA,IAAbN,MAAM,GAAAC,SAAA,CAAAJ,MAAA,QAAAI,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC,CAAC;EAClC,OAAOD,MAAM,CAACO,UAAU,IAAI,OAAOP,MAAM,CAACO,UAAU,CAACC,EAAE,KAAK,WAAW;AACzE;AACA,SAASC,cAAcA,CAAA,EAAc;EAAA,IAAbT,MAAM,GAAAC,SAAA,CAAAJ,MAAA,QAAAI,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC,CAAC;EACjC,OAAOD,MAAM,CAACU,SAAS,IAAI,OAAOV,MAAM,CAACU,SAAS,CAACF,EAAE,KAAK,WAAW;AACvE;AACA,SAASG,aAAaA,CAAA,EAAkB;EAAA,IAAjBC,UAAU,GAAAX,SAAA,CAAAJ,MAAA,QAAAI,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,EAAE;EACpC,MAAMY,OAAO,GAAGD,UAAU,CAACE,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,EAAE,CAAC,CAACxB,MAAM,CAACuB,CAAC,IAAI,CAAC,CAACA,CAAC,CAAC;EACzE,MAAME,MAAM,GAAG,EAAE;EACjBL,OAAO,CAACjB,OAAO,CAACoB,CAAC,IAAI;IACnB,IAAIE,MAAM,CAACvB,OAAO,CAACqB,CAAC,CAAC,GAAG,CAAC,EAAEE,MAAM,CAACC,IAAI,CAACH,CAAC,CAAC;EAC3C,CAAC,CAAC;EACF,OAAOE,MAAM,CAACE,IAAI,CAAC,GAAG,CAAC;AACzB;AACA,SAASC,UAAUA,CAAA,EAAgB;EAAA,IAAfC,QAAQ,GAAArB,SAAA,CAAAJ,MAAA,QAAAI,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,EAAE;EAC/B,OAAOqB,QAAQ,CAACC,OAAO,CAAC,SAAS,EAAEC,CAAC,IAAIA,CAAC,CAACC,WAAW,EAAE,CAACF,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;AAC3E;AACA,SAASG,YAAYA,CAAA,EAAiB;EAAA,IAAhBC,SAAS,GAAA1B,SAAA,CAAAJ,MAAA,QAAAI,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,EAAE;EAClC,IAAI,CAAC0B,SAAS,EAAE,OAAO,gBAAgB;EACvC,IAAI,CAACA,SAAS,CAACC,QAAQ,CAAC,gBAAgB,CAAC,EAAE,OAAQ,kBAAiBD,SAAU,EAAC;EAC/E,OAAOA,SAAS;AAClB;AACA,SAAS/C,QAAQ,EAAEQ,MAAM,EAAEW,eAAe,EAAEO,eAAe,EAAEG,cAAc,EAAEE,aAAa,EAAEU,UAAU,EAAEK,YAAY"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}