{"ast": null, "code": "export default function updateAutoHeight(speed) {\n  const swiper = this;\n  const activeSlides = [];\n  const isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n  let newHeight = 0;\n  let i;\n  if (typeof speed === 'number') {\n    swiper.setTransition(speed);\n  } else if (speed === true) {\n    swiper.setTransition(swiper.params.speed);\n  }\n  const getSlideByIndex = index => {\n    if (isVirtual) {\n      return swiper.slides[swiper.getSlideIndexByData(index)];\n    }\n    return swiper.slides[index];\n  };\n  // Find slides currently in view\n  if (swiper.params.slidesPerView !== 'auto' && swiper.params.slidesPerView > 1) {\n    if (swiper.params.centeredSlides) {\n      (swiper.visibleSlides || []).forEach(slide => {\n        activeSlides.push(slide);\n      });\n    } else {\n      for (i = 0; i < Math.ceil(swiper.params.slidesPerView); i += 1) {\n        const index = swiper.activeIndex + i;\n        if (index > swiper.slides.length && !isVirtual) break;\n        activeSlides.push(getSlideByIndex(index));\n      }\n    }\n  } else {\n    activeSlides.push(getSlideByIndex(swiper.activeIndex));\n  }\n\n  // Find new height from highest slide in view\n  for (i = 0; i < activeSlides.length; i += 1) {\n    if (typeof activeSlides[i] !== 'undefined') {\n      const height = activeSlides[i].offsetHeight;\n      newHeight = height > newHeight ? height : newHeight;\n    }\n  }\n\n  // Update Height\n  if (newHeight || newHeight === 0) swiper.wrapperEl.style.height = `${newHeight}px`;\n}", "map": {"version": 3, "names": ["updateAutoHeight", "speed", "swiper", "activeSlides", "isVirtual", "virtual", "params", "enabled", "newHeight", "i", "setTransition", "getSlideByIndex", "index", "slides", "getSlideIndexByData", "<PERSON><PERSON><PERSON><PERSON>iew", "centeredSlides", "visibleSlides", "for<PERSON>ach", "slide", "push", "Math", "ceil", "activeIndex", "length", "height", "offsetHeight", "wrapperEl", "style"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/core/update/updateAutoHeight.js"], "sourcesContent": ["export default function updateAutoHeight(speed) {\n  const swiper = this;\n  const activeSlides = [];\n  const isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n  let newHeight = 0;\n  let i;\n  if (typeof speed === 'number') {\n    swiper.setTransition(speed);\n  } else if (speed === true) {\n    swiper.setTransition(swiper.params.speed);\n  }\n  const getSlideByIndex = index => {\n    if (isVirtual) {\n      return swiper.slides[swiper.getSlideIndexByData(index)];\n    }\n    return swiper.slides[index];\n  };\n  // Find slides currently in view\n  if (swiper.params.slidesPerView !== 'auto' && swiper.params.slidesPerView > 1) {\n    if (swiper.params.centeredSlides) {\n      (swiper.visibleSlides || []).forEach(slide => {\n        activeSlides.push(slide);\n      });\n    } else {\n      for (i = 0; i < Math.ceil(swiper.params.slidesPerView); i += 1) {\n        const index = swiper.activeIndex + i;\n        if (index > swiper.slides.length && !isVirtual) break;\n        activeSlides.push(getSlideByIndex(index));\n      }\n    }\n  } else {\n    activeSlides.push(getSlideByIndex(swiper.activeIndex));\n  }\n\n  // Find new height from highest slide in view\n  for (i = 0; i < activeSlides.length; i += 1) {\n    if (typeof activeSlides[i] !== 'undefined') {\n      const height = activeSlides[i].offsetHeight;\n      newHeight = height > newHeight ? height : newHeight;\n    }\n  }\n\n  // Update Height\n  if (newHeight || newHeight === 0) swiper.wrapperEl.style.height = `${newHeight}px`;\n}"], "mappings": "AAAA,eAAe,SAASA,gBAAgBA,CAACC,KAAK,EAAE;EAC9C,MAAMC,MAAM,GAAG,IAAI;EACnB,MAAMC,YAAY,GAAG,EAAE;EACvB,MAAMC,SAAS,GAAGF,MAAM,CAACG,OAAO,IAAIH,MAAM,CAACI,MAAM,CAACD,OAAO,CAACE,OAAO;EACjE,IAAIC,SAAS,GAAG,CAAC;EACjB,IAAIC,CAAC;EACL,IAAI,OAAOR,KAAK,KAAK,QAAQ,EAAE;IAC7BC,MAAM,CAACQ,aAAa,CAACT,KAAK,CAAC;EAC7B,CAAC,MAAM,IAAIA,KAAK,KAAK,IAAI,EAAE;IACzBC,MAAM,CAACQ,aAAa,CAACR,MAAM,CAACI,MAAM,CAACL,KAAK,CAAC;EAC3C;EACA,MAAMU,eAAe,GAAGC,KAAK,IAAI;IAC/B,IAAIR,SAAS,EAAE;MACb,OAAOF,MAAM,CAACW,MAAM,CAACX,MAAM,CAACY,mBAAmB,CAACF,KAAK,CAAC,CAAC;IACzD;IACA,OAAOV,MAAM,CAACW,MAAM,CAACD,KAAK,CAAC;EAC7B,CAAC;EACD;EACA,IAAIV,MAAM,CAACI,MAAM,CAACS,aAAa,KAAK,MAAM,IAAIb,MAAM,CAACI,MAAM,CAACS,aAAa,GAAG,CAAC,EAAE;IAC7E,IAAIb,MAAM,CAACI,MAAM,CAACU,cAAc,EAAE;MAChC,CAACd,MAAM,CAACe,aAAa,IAAI,EAAE,EAAEC,OAAO,CAACC,KAAK,IAAI;QAC5ChB,YAAY,CAACiB,IAAI,CAACD,KAAK,CAAC;MAC1B,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,KAAKV,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,IAAI,CAACC,IAAI,CAACpB,MAAM,CAACI,MAAM,CAACS,aAAa,CAAC,EAAEN,CAAC,IAAI,CAAC,EAAE;QAC9D,MAAMG,KAAK,GAAGV,MAAM,CAACqB,WAAW,GAAGd,CAAC;QACpC,IAAIG,KAAK,GAAGV,MAAM,CAACW,MAAM,CAACW,MAAM,IAAI,CAACpB,SAAS,EAAE;QAChDD,YAAY,CAACiB,IAAI,CAACT,eAAe,CAACC,KAAK,CAAC,CAAC;MAC3C;IACF;EACF,CAAC,MAAM;IACLT,YAAY,CAACiB,IAAI,CAACT,eAAe,CAACT,MAAM,CAACqB,WAAW,CAAC,CAAC;EACxD;;EAEA;EACA,KAAKd,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,YAAY,CAACqB,MAAM,EAAEf,CAAC,IAAI,CAAC,EAAE;IAC3C,IAAI,OAAON,YAAY,CAACM,CAAC,CAAC,KAAK,WAAW,EAAE;MAC1C,MAAMgB,MAAM,GAAGtB,YAAY,CAACM,CAAC,CAAC,CAACiB,YAAY;MAC3ClB,SAAS,GAAGiB,MAAM,GAAGjB,SAAS,GAAGiB,MAAM,GAAGjB,SAAS;IACrD;EACF;;EAEA;EACA,IAAIA,SAAS,IAAIA,SAAS,KAAK,CAAC,EAAEN,MAAM,CAACyB,SAAS,CAACC,KAAK,CAACH,MAAM,GAAI,GAAEjB,SAAU,IAAG;AACpF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}