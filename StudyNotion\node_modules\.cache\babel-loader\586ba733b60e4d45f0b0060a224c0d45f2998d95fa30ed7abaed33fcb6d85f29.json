{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.handleFullscreenChange = handleFullscreenChange;\nexports.activate = activate;\nexports.userActivate = userActivate;\nexports.play = play;\nexports.pause = pause;\nexports.togglePlay = togglePlay;\nexports.seek = seek;\nexports.forward = forward;\nexports.replay = replay;\nexports.changeRate = changeRate;\nexports.changeVolume = changeVolume;\nexports.mute = mute;\nexports.toggleFullscreen = toggleFullscreen;\nexports.USER_ACTIVATE = exports.PLAYER_ACTIVATE = exports.FULLSCREEN_CHANGE = exports.OPERATE = void 0;\nvar _fullscreen = _interopRequireDefault(require(\"../utils/fullscreen\"));\nvar OPERATE = 'video-react/OPERATE';\nexports.OPERATE = OPERATE;\nvar FULLSCREEN_CHANGE = 'video-react/FULLSCREEN_CHANGE';\nexports.FULLSCREEN_CHANGE = FULLSCREEN_CHANGE;\nvar PLAYER_ACTIVATE = 'video-react/PLAYER_ACTIVATE';\nexports.PLAYER_ACTIVATE = PLAYER_ACTIVATE;\nvar USER_ACTIVATE = 'video-react/USER_ACTIVATE';\nexports.USER_ACTIVATE = USER_ACTIVATE;\nfunction handleFullscreenChange(isFullscreen) {\n  return {\n    type: FULLSCREEN_CHANGE,\n    isFullscreen: isFullscreen\n  };\n}\nfunction activate(activity) {\n  return {\n    type: PLAYER_ACTIVATE,\n    activity: activity\n  };\n}\nfunction userActivate(activity) {\n  return {\n    type: USER_ACTIVATE,\n    activity: activity\n  };\n}\nfunction play() {\n  var operation = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {\n    action: 'play',\n    source: ''\n  };\n  this.video.play();\n  return {\n    type: OPERATE,\n    operation: operation\n  };\n}\nfunction pause() {\n  var operation = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {\n    action: 'pause',\n    source: ''\n  };\n  this.video.pause();\n  return {\n    type: OPERATE,\n    operation: operation\n  };\n}\nfunction togglePlay() {\n  var operation = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {\n    action: 'toggle-play',\n    source: ''\n  };\n  this.video.togglePlay();\n  return {\n    type: OPERATE,\n    operation: operation\n  };\n} // seek video by time\n\nfunction seek(time) {\n  var operation = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n    action: 'seek',\n    source: ''\n  };\n  this.video.seek(time);\n  return {\n    type: OPERATE,\n    operation: operation\n  };\n} // jump forward x seconds\n\nfunction forward(seconds) {\n  var operation = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n    action: \"forward-\".concat(seconds),\n    source: ''\n  };\n  this.video.forward(seconds);\n  return {\n    type: OPERATE,\n    operation: operation\n  };\n} // jump back x seconds\n\nfunction replay(seconds) {\n  var operation = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n    action: \"replay-\".concat(seconds),\n    source: ''\n  };\n  this.video.replay(seconds);\n  return {\n    type: OPERATE,\n    operation: operation\n  };\n}\nfunction changeRate(rate) {\n  var operation = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n    action: 'change-rate',\n    source: ''\n  };\n  this.video.playbackRate = rate;\n  return {\n    type: OPERATE,\n    operation: operation\n  };\n}\nfunction changeVolume(volume) {\n  var operation = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n    action: 'change-volume',\n    source: ''\n  };\n  var v = volume;\n  if (volume < 0) {\n    v = 0;\n  }\n  if (volume > 1) {\n    v = 1;\n  }\n  this.video.volume = v;\n  return {\n    type: OPERATE,\n    operation: operation\n  };\n}\nfunction mute(muted) {\n  var operation = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n    action: muted ? 'muted' : 'unmuted',\n    source: ''\n  };\n  this.video.muted = muted;\n  return {\n    type: OPERATE,\n    operation: operation\n  };\n}\nfunction toggleFullscreen(player) {\n  if (_fullscreen[\"default\"].enabled) {\n    if (_fullscreen[\"default\"].isFullscreen) {\n      _fullscreen[\"default\"].exit();\n    } else {\n      _fullscreen[\"default\"].request(this.rootElement);\n    }\n    return {\n      type: OPERATE,\n      operation: {\n        action: 'toggle-fullscreen',\n        source: ''\n      }\n    };\n  }\n  return {\n    type: FULLSCREEN_CHANGE,\n    isFullscreen: !player.isFullscreen\n  };\n}", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "handleFullscreenChange", "activate", "userActivate", "play", "pause", "togglePlay", "seek", "forward", "replay", "changeRate", "changeVolume", "mute", "toggleFullscreen", "USER_ACTIVATE", "PLAYER_ACTIVATE", "FULLSCREEN_CHANGE", "OPERATE", "_fullscreen", "isFullscreen", "type", "activity", "operation", "arguments", "length", "undefined", "action", "source", "video", "time", "seconds", "concat", "rate", "playbackRate", "volume", "v", "muted", "player", "enabled", "exit", "request", "rootElement"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/video-react/lib/actions/player.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.handleFullscreenChange = handleFullscreenChange;\nexports.activate = activate;\nexports.userActivate = userActivate;\nexports.play = play;\nexports.pause = pause;\nexports.togglePlay = togglePlay;\nexports.seek = seek;\nexports.forward = forward;\nexports.replay = replay;\nexports.changeRate = changeRate;\nexports.changeVolume = changeVolume;\nexports.mute = mute;\nexports.toggleFullscreen = toggleFullscreen;\nexports.USER_ACTIVATE = exports.PLAYER_ACTIVATE = exports.FULLSCREEN_CHANGE = exports.OPERATE = void 0;\n\nvar _fullscreen = _interopRequireDefault(require(\"../utils/fullscreen\"));\n\nvar OPERATE = 'video-react/OPERATE';\nexports.OPERATE = OPERATE;\nvar FULLSCREEN_CHANGE = 'video-react/FULLSCREEN_CHANGE';\nexports.FULLSCREEN_CHANGE = FULLSCREEN_CHANGE;\nvar PLAYER_ACTIVATE = 'video-react/PLAYER_ACTIVATE';\nexports.PLAYER_ACTIVATE = PLAYER_ACTIVATE;\nvar USER_ACTIVATE = 'video-react/USER_ACTIVATE';\nexports.USER_ACTIVATE = USER_ACTIVATE;\n\nfunction handleFullscreenChange(isFullscreen) {\n  return {\n    type: FULLSCREEN_CHANGE,\n    isFullscreen: isFullscreen\n  };\n}\n\nfunction activate(activity) {\n  return {\n    type: PLAYER_ACTIVATE,\n    activity: activity\n  };\n}\n\nfunction userActivate(activity) {\n  return {\n    type: USER_ACTIVATE,\n    activity: activity\n  };\n}\n\nfunction play() {\n  var operation = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {\n    action: 'play',\n    source: ''\n  };\n  this.video.play();\n  return {\n    type: OPERATE,\n    operation: operation\n  };\n}\n\nfunction pause() {\n  var operation = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {\n    action: 'pause',\n    source: ''\n  };\n  this.video.pause();\n  return {\n    type: OPERATE,\n    operation: operation\n  };\n}\n\nfunction togglePlay() {\n  var operation = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {\n    action: 'toggle-play',\n    source: ''\n  };\n  this.video.togglePlay();\n  return {\n    type: OPERATE,\n    operation: operation\n  };\n} // seek video by time\n\n\nfunction seek(time) {\n  var operation = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n    action: 'seek',\n    source: ''\n  };\n  this.video.seek(time);\n  return {\n    type: OPERATE,\n    operation: operation\n  };\n} // jump forward x seconds\n\n\nfunction forward(seconds) {\n  var operation = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n    action: \"forward-\".concat(seconds),\n    source: ''\n  };\n  this.video.forward(seconds);\n  return {\n    type: OPERATE,\n    operation: operation\n  };\n} // jump back x seconds\n\n\nfunction replay(seconds) {\n  var operation = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n    action: \"replay-\".concat(seconds),\n    source: ''\n  };\n  this.video.replay(seconds);\n  return {\n    type: OPERATE,\n    operation: operation\n  };\n}\n\nfunction changeRate(rate) {\n  var operation = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n    action: 'change-rate',\n    source: ''\n  };\n  this.video.playbackRate = rate;\n  return {\n    type: OPERATE,\n    operation: operation\n  };\n}\n\nfunction changeVolume(volume) {\n  var operation = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n    action: 'change-volume',\n    source: ''\n  };\n  var v = volume;\n\n  if (volume < 0) {\n    v = 0;\n  }\n\n  if (volume > 1) {\n    v = 1;\n  }\n\n  this.video.volume = v;\n  return {\n    type: OPERATE,\n    operation: operation\n  };\n}\n\nfunction mute(muted) {\n  var operation = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n    action: muted ? 'muted' : 'unmuted',\n    source: ''\n  };\n  this.video.muted = muted;\n  return {\n    type: OPERATE,\n    operation: operation\n  };\n}\n\nfunction toggleFullscreen(player) {\n  if (_fullscreen[\"default\"].enabled) {\n    if (_fullscreen[\"default\"].isFullscreen) {\n      _fullscreen[\"default\"].exit();\n    } else {\n      _fullscreen[\"default\"].request(this.rootElement);\n    }\n\n    return {\n      type: OPERATE,\n      operation: {\n        action: 'toggle-fullscreen',\n        source: ''\n      }\n    };\n  }\n\n  return {\n    type: FULLSCREEN_CHANGE,\n    isFullscreen: !player.isFullscreen\n  };\n}"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC;AAEpFC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,sBAAsB,GAAGA,sBAAsB;AACvDF,OAAO,CAACG,QAAQ,GAAGA,QAAQ;AAC3BH,OAAO,CAACI,YAAY,GAAGA,YAAY;AACnCJ,OAAO,CAACK,IAAI,GAAGA,IAAI;AACnBL,OAAO,CAACM,KAAK,GAAGA,KAAK;AACrBN,OAAO,CAACO,UAAU,GAAGA,UAAU;AAC/BP,OAAO,CAACQ,IAAI,GAAGA,IAAI;AACnBR,OAAO,CAACS,OAAO,GAAGA,OAAO;AACzBT,OAAO,CAACU,MAAM,GAAGA,MAAM;AACvBV,OAAO,CAACW,UAAU,GAAGA,UAAU;AAC/BX,OAAO,CAACY,YAAY,GAAGA,YAAY;AACnCZ,OAAO,CAACa,IAAI,GAAGA,IAAI;AACnBb,OAAO,CAACc,gBAAgB,GAAGA,gBAAgB;AAC3Cd,OAAO,CAACe,aAAa,GAAGf,OAAO,CAACgB,eAAe,GAAGhB,OAAO,CAACiB,iBAAiB,GAAGjB,OAAO,CAACkB,OAAO,GAAG,KAAK,CAAC;AAEtG,IAAIC,WAAW,GAAGvB,sBAAsB,CAACC,OAAO,CAAC,qBAAqB,CAAC,CAAC;AAExE,IAAIqB,OAAO,GAAG,qBAAqB;AACnClB,OAAO,CAACkB,OAAO,GAAGA,OAAO;AACzB,IAAID,iBAAiB,GAAG,+BAA+B;AACvDjB,OAAO,CAACiB,iBAAiB,GAAGA,iBAAiB;AAC7C,IAAID,eAAe,GAAG,6BAA6B;AACnDhB,OAAO,CAACgB,eAAe,GAAGA,eAAe;AACzC,IAAID,aAAa,GAAG,2BAA2B;AAC/Cf,OAAO,CAACe,aAAa,GAAGA,aAAa;AAErC,SAASb,sBAAsBA,CAACkB,YAAY,EAAE;EAC5C,OAAO;IACLC,IAAI,EAAEJ,iBAAiB;IACvBG,YAAY,EAAEA;EAChB,CAAC;AACH;AAEA,SAASjB,QAAQA,CAACmB,QAAQ,EAAE;EAC1B,OAAO;IACLD,IAAI,EAAEL,eAAe;IACrBM,QAAQ,EAAEA;EACZ,CAAC;AACH;AAEA,SAASlB,YAAYA,CAACkB,QAAQ,EAAE;EAC9B,OAAO;IACLD,IAAI,EAAEN,aAAa;IACnBO,QAAQ,EAAEA;EACZ,CAAC;AACH;AAEA,SAASjB,IAAIA,CAAA,EAAG;EACd,IAAIkB,SAAS,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG;IAClFG,MAAM,EAAE,MAAM;IACdC,MAAM,EAAE;EACV,CAAC;EACD,IAAI,CAACC,KAAK,CAACxB,IAAI,EAAE;EACjB,OAAO;IACLgB,IAAI,EAAEH,OAAO;IACbK,SAAS,EAAEA;EACb,CAAC;AACH;AAEA,SAASjB,KAAKA,CAAA,EAAG;EACf,IAAIiB,SAAS,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG;IAClFG,MAAM,EAAE,OAAO;IACfC,MAAM,EAAE;EACV,CAAC;EACD,IAAI,CAACC,KAAK,CAACvB,KAAK,EAAE;EAClB,OAAO;IACLe,IAAI,EAAEH,OAAO;IACbK,SAAS,EAAEA;EACb,CAAC;AACH;AAEA,SAAShB,UAAUA,CAAA,EAAG;EACpB,IAAIgB,SAAS,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG;IAClFG,MAAM,EAAE,aAAa;IACrBC,MAAM,EAAE;EACV,CAAC;EACD,IAAI,CAACC,KAAK,CAACtB,UAAU,EAAE;EACvB,OAAO;IACLc,IAAI,EAAEH,OAAO;IACbK,SAAS,EAAEA;EACb,CAAC;AACH,CAAC,CAAC;;AAGF,SAASf,IAAIA,CAACsB,IAAI,EAAE;EAClB,IAAIP,SAAS,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG;IAClFG,MAAM,EAAE,MAAM;IACdC,MAAM,EAAE;EACV,CAAC;EACD,IAAI,CAACC,KAAK,CAACrB,IAAI,CAACsB,IAAI,CAAC;EACrB,OAAO;IACLT,IAAI,EAAEH,OAAO;IACbK,SAAS,EAAEA;EACb,CAAC;AACH,CAAC,CAAC;;AAGF,SAASd,OAAOA,CAACsB,OAAO,EAAE;EACxB,IAAIR,SAAS,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG;IAClFG,MAAM,EAAE,UAAU,CAACK,MAAM,CAACD,OAAO,CAAC;IAClCH,MAAM,EAAE;EACV,CAAC;EACD,IAAI,CAACC,KAAK,CAACpB,OAAO,CAACsB,OAAO,CAAC;EAC3B,OAAO;IACLV,IAAI,EAAEH,OAAO;IACbK,SAAS,EAAEA;EACb,CAAC;AACH,CAAC,CAAC;;AAGF,SAASb,MAAMA,CAACqB,OAAO,EAAE;EACvB,IAAIR,SAAS,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG;IAClFG,MAAM,EAAE,SAAS,CAACK,MAAM,CAACD,OAAO,CAAC;IACjCH,MAAM,EAAE;EACV,CAAC;EACD,IAAI,CAACC,KAAK,CAACnB,MAAM,CAACqB,OAAO,CAAC;EAC1B,OAAO;IACLV,IAAI,EAAEH,OAAO;IACbK,SAAS,EAAEA;EACb,CAAC;AACH;AAEA,SAASZ,UAAUA,CAACsB,IAAI,EAAE;EACxB,IAAIV,SAAS,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG;IAClFG,MAAM,EAAE,aAAa;IACrBC,MAAM,EAAE;EACV,CAAC;EACD,IAAI,CAACC,KAAK,CAACK,YAAY,GAAGD,IAAI;EAC9B,OAAO;IACLZ,IAAI,EAAEH,OAAO;IACbK,SAAS,EAAEA;EACb,CAAC;AACH;AAEA,SAASX,YAAYA,CAACuB,MAAM,EAAE;EAC5B,IAAIZ,SAAS,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG;IAClFG,MAAM,EAAE,eAAe;IACvBC,MAAM,EAAE;EACV,CAAC;EACD,IAAIQ,CAAC,GAAGD,MAAM;EAEd,IAAIA,MAAM,GAAG,CAAC,EAAE;IACdC,CAAC,GAAG,CAAC;EACP;EAEA,IAAID,MAAM,GAAG,CAAC,EAAE;IACdC,CAAC,GAAG,CAAC;EACP;EAEA,IAAI,CAACP,KAAK,CAACM,MAAM,GAAGC,CAAC;EACrB,OAAO;IACLf,IAAI,EAAEH,OAAO;IACbK,SAAS,EAAEA;EACb,CAAC;AACH;AAEA,SAASV,IAAIA,CAACwB,KAAK,EAAE;EACnB,IAAId,SAAS,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG;IAClFG,MAAM,EAAEU,KAAK,GAAG,OAAO,GAAG,SAAS;IACnCT,MAAM,EAAE;EACV,CAAC;EACD,IAAI,CAACC,KAAK,CAACQ,KAAK,GAAGA,KAAK;EACxB,OAAO;IACLhB,IAAI,EAAEH,OAAO;IACbK,SAAS,EAAEA;EACb,CAAC;AACH;AAEA,SAAST,gBAAgBA,CAACwB,MAAM,EAAE;EAChC,IAAInB,WAAW,CAAC,SAAS,CAAC,CAACoB,OAAO,EAAE;IAClC,IAAIpB,WAAW,CAAC,SAAS,CAAC,CAACC,YAAY,EAAE;MACvCD,WAAW,CAAC,SAAS,CAAC,CAACqB,IAAI,EAAE;IAC/B,CAAC,MAAM;MACLrB,WAAW,CAAC,SAAS,CAAC,CAACsB,OAAO,CAAC,IAAI,CAACC,WAAW,CAAC;IAClD;IAEA,OAAO;MACLrB,IAAI,EAAEH,OAAO;MACbK,SAAS,EAAE;QACTI,MAAM,EAAE,mBAAmB;QAC3BC,MAAM,EAAE;MACV;IACF,CAAC;EACH;EAEA,OAAO;IACLP,IAAI,EAAEJ,iBAAiB;IACvBG,YAAY,EAAE,CAACkB,MAAM,CAAClB;EACxB,CAAC;AACH"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}