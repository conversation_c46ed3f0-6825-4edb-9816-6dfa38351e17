{"ast": null, "code": "/**\n * @typedef {import('./lib/util/info.js').Info} Info\n * @typedef {import('./lib/util/schema.js').Schema} Schema\n */\n\nimport { merge } from './lib/util/merge.js';\nimport { xlink } from './lib/xlink.js';\nimport { xml } from './lib/xml.js';\nimport { xmlns } from './lib/xmlns.js';\nimport { aria } from './lib/aria.js';\nimport { html as htmlBase } from './lib/html.js';\nimport { svg as svgBase } from './lib/svg.js';\nexport { find } from './lib/find.js';\nexport { hastToReact } from './lib/hast-to-react.js';\nexport { normalize } from './lib/normalize.js';\nexport const html = merge([xml, xlink, xmlns, aria, htmlBase], 'html');\nexport const svg = merge([xml, xlink, xmlns, aria, svgBase], 'svg');", "map": {"version": 3, "names": ["merge", "xlink", "xml", "xmlns", "aria", "html", "htmlBase", "svg", "svgBase", "find", "hastToReact", "normalize"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/property-information/index.js"], "sourcesContent": ["/**\n * @typedef {import('./lib/util/info.js').Info} Info\n * @typedef {import('./lib/util/schema.js').Schema} Schema\n */\n\nimport {merge} from './lib/util/merge.js'\nimport {xlink} from './lib/xlink.js'\nimport {xml} from './lib/xml.js'\nimport {xmlns} from './lib/xmlns.js'\nimport {aria} from './lib/aria.js'\nimport {html as htmlBase} from './lib/html.js'\nimport {svg as svgBase} from './lib/svg.js'\n\nexport {find} from './lib/find.js'\nexport {hastToReact} from './lib/hast-to-react.js'\nexport {normalize} from './lib/normalize.js'\nexport const html = merge([xml, xlink, xmlns, aria, htmlBase], 'html')\nexport const svg = merge([xml, xlink, xmlns, aria, svgBase], 'svg')\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA,SAAQA,KAAK,QAAO,qBAAqB;AACzC,SAAQC,KAAK,QAAO,gBAAgB;AACpC,SAAQC,GAAG,QAAO,cAAc;AAChC,SAAQC,KAAK,QAAO,gBAAgB;AACpC,SAAQC,IAAI,QAAO,eAAe;AAClC,SAAQC,IAAI,IAAIC,QAAQ,QAAO,eAAe;AAC9C,SAAQC,GAAG,IAAIC,OAAO,QAAO,cAAc;AAE3C,SAAQC,IAAI,QAAO,eAAe;AAClC,SAAQC,WAAW,QAAO,wBAAwB;AAClD,SAAQC,SAAS,QAAO,oBAAoB;AAC5C,OAAO,MAAMN,IAAI,GAAGL,KAAK,CAAC,CAACE,GAAG,EAAED,KAAK,EAAEE,KAAK,EAAEC,IAAI,EAAEE,QAAQ,CAAC,EAAE,MAAM,CAAC;AACtE,OAAO,MAAMC,GAAG,GAAGP,KAAK,CAAC,CAACE,GAAG,EAAED,KAAK,EAAEE,KAAK,EAAEC,IAAI,EAAEI,OAAO,CAAC,EAAE,KAAK,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}