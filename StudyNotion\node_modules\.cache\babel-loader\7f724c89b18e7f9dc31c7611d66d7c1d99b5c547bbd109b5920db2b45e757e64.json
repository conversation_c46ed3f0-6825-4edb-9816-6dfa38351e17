{"ast": null, "code": "import { preload } from '../../shared/process-lazy-preloader.js';\nexport function getActiveIndexByTranslate(swiper) {\n  const {\n    slidesGrid,\n    params\n  } = swiper;\n  const translate = swiper.rtlTranslate ? swiper.translate : -swiper.translate;\n  let activeIndex;\n  for (let i = 0; i < slidesGrid.length; i += 1) {\n    if (typeof slidesGrid[i + 1] !== 'undefined') {\n      if (translate >= slidesGrid[i] && translate < slidesGrid[i + 1] - (slidesGrid[i + 1] - slidesGrid[i]) / 2) {\n        activeIndex = i;\n      } else if (translate >= slidesGrid[i] && translate < slidesGrid[i + 1]) {\n        activeIndex = i + 1;\n      }\n    } else if (translate >= slidesGrid[i]) {\n      activeIndex = i;\n    }\n  }\n  // Normalize slideIndex\n  if (params.normalizeSlideIndex) {\n    if (activeIndex < 0 || typeof activeIndex === 'undefined') activeIndex = 0;\n  }\n  return activeIndex;\n}\nexport default function updateActiveIndex(newActiveIndex) {\n  const swiper = this;\n  const translate = swiper.rtlTranslate ? swiper.translate : -swiper.translate;\n  const {\n    snapGrid,\n    params,\n    activeIndex: previousIndex,\n    realIndex: previousRealIndex,\n    snapIndex: previousSnapIndex\n  } = swiper;\n  let activeIndex = newActiveIndex;\n  let snapIndex;\n  const getVirtualRealIndex = aIndex => {\n    let realIndex = aIndex - swiper.virtual.slidesBefore;\n    if (realIndex < 0) {\n      realIndex = swiper.virtual.slides.length + realIndex;\n    }\n    if (realIndex >= swiper.virtual.slides.length) {\n      realIndex -= swiper.virtual.slides.length;\n    }\n    return realIndex;\n  };\n  if (typeof activeIndex === 'undefined') {\n    activeIndex = getActiveIndexByTranslate(swiper);\n  }\n  if (snapGrid.indexOf(translate) >= 0) {\n    snapIndex = snapGrid.indexOf(translate);\n  } else {\n    const skip = Math.min(params.slidesPerGroupSkip, activeIndex);\n    snapIndex = skip + Math.floor((activeIndex - skip) / params.slidesPerGroup);\n  }\n  if (snapIndex >= snapGrid.length) snapIndex = snapGrid.length - 1;\n  if (activeIndex === previousIndex) {\n    if (snapIndex !== previousSnapIndex) {\n      swiper.snapIndex = snapIndex;\n      swiper.emit('snapIndexChange');\n    }\n    if (swiper.params.loop && swiper.virtual && swiper.params.virtual.enabled) {\n      swiper.realIndex = getVirtualRealIndex(activeIndex);\n    }\n    return;\n  }\n  // Get real index\n  let realIndex;\n  if (swiper.virtual && params.virtual.enabled && params.loop) {\n    realIndex = getVirtualRealIndex(activeIndex);\n  } else if (swiper.slides[activeIndex]) {\n    realIndex = parseInt(swiper.slides[activeIndex].getAttribute('data-swiper-slide-index') || activeIndex, 10);\n  } else {\n    realIndex = activeIndex;\n  }\n  Object.assign(swiper, {\n    previousSnapIndex,\n    snapIndex,\n    previousRealIndex,\n    realIndex,\n    previousIndex,\n    activeIndex\n  });\n  if (swiper.initialized) {\n    preload(swiper);\n  }\n  swiper.emit('activeIndexChange');\n  swiper.emit('snapIndexChange');\n  if (previousRealIndex !== realIndex) {\n    swiper.emit('realIndexChange');\n  }\n  if (swiper.initialized || swiper.params.runCallbacksOnInit) {\n    swiper.emit('slideChange');\n  }\n}", "map": {"version": 3, "names": ["preload", "getActiveIndexByTranslate", "swiper", "slidesGrid", "params", "translate", "rtlTranslate", "activeIndex", "i", "length", "normalizeSlideIndex", "updateActiveIndex", "newActiveIndex", "snapGrid", "previousIndex", "realIndex", "previousRealIndex", "snapIndex", "previousSnapIndex", "getVirtualRealIndex", "aIndex", "virtual", "slidesBefore", "slides", "indexOf", "skip", "Math", "min", "slidesPerGroupSkip", "floor", "slidesPerGroup", "emit", "loop", "enabled", "parseInt", "getAttribute", "Object", "assign", "initialized", "runCallbacksOnInit"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/core/update/updateActiveIndex.js"], "sourcesContent": ["import { preload } from '../../shared/process-lazy-preloader.js';\nexport function getActiveIndexByTranslate(swiper) {\n  const {\n    slidesGrid,\n    params\n  } = swiper;\n  const translate = swiper.rtlTranslate ? swiper.translate : -swiper.translate;\n  let activeIndex;\n  for (let i = 0; i < slidesGrid.length; i += 1) {\n    if (typeof slidesGrid[i + 1] !== 'undefined') {\n      if (translate >= slidesGrid[i] && translate < slidesGrid[i + 1] - (slidesGrid[i + 1] - slidesGrid[i]) / 2) {\n        activeIndex = i;\n      } else if (translate >= slidesGrid[i] && translate < slidesGrid[i + 1]) {\n        activeIndex = i + 1;\n      }\n    } else if (translate >= slidesGrid[i]) {\n      activeIndex = i;\n    }\n  }\n  // Normalize slideIndex\n  if (params.normalizeSlideIndex) {\n    if (activeIndex < 0 || typeof activeIndex === 'undefined') activeIndex = 0;\n  }\n  return activeIndex;\n}\nexport default function updateActiveIndex(newActiveIndex) {\n  const swiper = this;\n  const translate = swiper.rtlTranslate ? swiper.translate : -swiper.translate;\n  const {\n    snapGrid,\n    params,\n    activeIndex: previousIndex,\n    realIndex: previousRealIndex,\n    snapIndex: previousSnapIndex\n  } = swiper;\n  let activeIndex = newActiveIndex;\n  let snapIndex;\n  const getVirtualRealIndex = aIndex => {\n    let realIndex = aIndex - swiper.virtual.slidesBefore;\n    if (realIndex < 0) {\n      realIndex = swiper.virtual.slides.length + realIndex;\n    }\n    if (realIndex >= swiper.virtual.slides.length) {\n      realIndex -= swiper.virtual.slides.length;\n    }\n    return realIndex;\n  };\n  if (typeof activeIndex === 'undefined') {\n    activeIndex = getActiveIndexByTranslate(swiper);\n  }\n  if (snapGrid.indexOf(translate) >= 0) {\n    snapIndex = snapGrid.indexOf(translate);\n  } else {\n    const skip = Math.min(params.slidesPerGroupSkip, activeIndex);\n    snapIndex = skip + Math.floor((activeIndex - skip) / params.slidesPerGroup);\n  }\n  if (snapIndex >= snapGrid.length) snapIndex = snapGrid.length - 1;\n  if (activeIndex === previousIndex) {\n    if (snapIndex !== previousSnapIndex) {\n      swiper.snapIndex = snapIndex;\n      swiper.emit('snapIndexChange');\n    }\n    if (swiper.params.loop && swiper.virtual && swiper.params.virtual.enabled) {\n      swiper.realIndex = getVirtualRealIndex(activeIndex);\n    }\n    return;\n  }\n  // Get real index\n  let realIndex;\n  if (swiper.virtual && params.virtual.enabled && params.loop) {\n    realIndex = getVirtualRealIndex(activeIndex);\n  } else if (swiper.slides[activeIndex]) {\n    realIndex = parseInt(swiper.slides[activeIndex].getAttribute('data-swiper-slide-index') || activeIndex, 10);\n  } else {\n    realIndex = activeIndex;\n  }\n  Object.assign(swiper, {\n    previousSnapIndex,\n    snapIndex,\n    previousRealIndex,\n    realIndex,\n    previousIndex,\n    activeIndex\n  });\n  if (swiper.initialized) {\n    preload(swiper);\n  }\n  swiper.emit('activeIndexChange');\n  swiper.emit('snapIndexChange');\n  if (previousRealIndex !== realIndex) {\n    swiper.emit('realIndexChange');\n  }\n  if (swiper.initialized || swiper.params.runCallbacksOnInit) {\n    swiper.emit('slideChange');\n  }\n}"], "mappings": "AAAA,SAASA,OAAO,QAAQ,wCAAwC;AAChE,OAAO,SAASC,yBAAyBA,CAACC,MAAM,EAAE;EAChD,MAAM;IACJC,UAAU;IACVC;EACF,CAAC,GAAGF,MAAM;EACV,MAAMG,SAAS,GAAGH,MAAM,CAACI,YAAY,GAAGJ,MAAM,CAACG,SAAS,GAAG,CAACH,MAAM,CAACG,SAAS;EAC5E,IAAIE,WAAW;EACf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,UAAU,CAACM,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;IAC7C,IAAI,OAAOL,UAAU,CAACK,CAAC,GAAG,CAAC,CAAC,KAAK,WAAW,EAAE;MAC5C,IAAIH,SAAS,IAAIF,UAAU,CAACK,CAAC,CAAC,IAAIH,SAAS,GAAGF,UAAU,CAACK,CAAC,GAAG,CAAC,CAAC,GAAG,CAACL,UAAU,CAACK,CAAC,GAAG,CAAC,CAAC,GAAGL,UAAU,CAACK,CAAC,CAAC,IAAI,CAAC,EAAE;QACzGD,WAAW,GAAGC,CAAC;MACjB,CAAC,MAAM,IAAIH,SAAS,IAAIF,UAAU,CAACK,CAAC,CAAC,IAAIH,SAAS,GAAGF,UAAU,CAACK,CAAC,GAAG,CAAC,CAAC,EAAE;QACtED,WAAW,GAAGC,CAAC,GAAG,CAAC;MACrB;IACF,CAAC,MAAM,IAAIH,SAAS,IAAIF,UAAU,CAACK,CAAC,CAAC,EAAE;MACrCD,WAAW,GAAGC,CAAC;IACjB;EACF;EACA;EACA,IAAIJ,MAAM,CAACM,mBAAmB,EAAE;IAC9B,IAAIH,WAAW,GAAG,CAAC,IAAI,OAAOA,WAAW,KAAK,WAAW,EAAEA,WAAW,GAAG,CAAC;EAC5E;EACA,OAAOA,WAAW;AACpB;AACA,eAAe,SAASI,iBAAiBA,CAACC,cAAc,EAAE;EACxD,MAAMV,MAAM,GAAG,IAAI;EACnB,MAAMG,SAAS,GAAGH,MAAM,CAACI,YAAY,GAAGJ,MAAM,CAACG,SAAS,GAAG,CAACH,MAAM,CAACG,SAAS;EAC5E,MAAM;IACJQ,QAAQ;IACRT,MAAM;IACNG,WAAW,EAAEO,aAAa;IAC1BC,SAAS,EAAEC,iBAAiB;IAC5BC,SAAS,EAAEC;EACb,CAAC,GAAGhB,MAAM;EACV,IAAIK,WAAW,GAAGK,cAAc;EAChC,IAAIK,SAAS;EACb,MAAME,mBAAmB,GAAGC,MAAM,IAAI;IACpC,IAAIL,SAAS,GAAGK,MAAM,GAAGlB,MAAM,CAACmB,OAAO,CAACC,YAAY;IACpD,IAAIP,SAAS,GAAG,CAAC,EAAE;MACjBA,SAAS,GAAGb,MAAM,CAACmB,OAAO,CAACE,MAAM,CAACd,MAAM,GAAGM,SAAS;IACtD;IACA,IAAIA,SAAS,IAAIb,MAAM,CAACmB,OAAO,CAACE,MAAM,CAACd,MAAM,EAAE;MAC7CM,SAAS,IAAIb,MAAM,CAACmB,OAAO,CAACE,MAAM,CAACd,MAAM;IAC3C;IACA,OAAOM,SAAS;EAClB,CAAC;EACD,IAAI,OAAOR,WAAW,KAAK,WAAW,EAAE;IACtCA,WAAW,GAAGN,yBAAyB,CAACC,MAAM,CAAC;EACjD;EACA,IAAIW,QAAQ,CAACW,OAAO,CAACnB,SAAS,CAAC,IAAI,CAAC,EAAE;IACpCY,SAAS,GAAGJ,QAAQ,CAACW,OAAO,CAACnB,SAAS,CAAC;EACzC,CAAC,MAAM;IACL,MAAMoB,IAAI,GAAGC,IAAI,CAACC,GAAG,CAACvB,MAAM,CAACwB,kBAAkB,EAAErB,WAAW,CAAC;IAC7DU,SAAS,GAAGQ,IAAI,GAAGC,IAAI,CAACG,KAAK,CAAC,CAACtB,WAAW,GAAGkB,IAAI,IAAIrB,MAAM,CAAC0B,cAAc,CAAC;EAC7E;EACA,IAAIb,SAAS,IAAIJ,QAAQ,CAACJ,MAAM,EAAEQ,SAAS,GAAGJ,QAAQ,CAACJ,MAAM,GAAG,CAAC;EACjE,IAAIF,WAAW,KAAKO,aAAa,EAAE;IACjC,IAAIG,SAAS,KAAKC,iBAAiB,EAAE;MACnChB,MAAM,CAACe,SAAS,GAAGA,SAAS;MAC5Bf,MAAM,CAAC6B,IAAI,CAAC,iBAAiB,CAAC;IAChC;IACA,IAAI7B,MAAM,CAACE,MAAM,CAAC4B,IAAI,IAAI9B,MAAM,CAACmB,OAAO,IAAInB,MAAM,CAACE,MAAM,CAACiB,OAAO,CAACY,OAAO,EAAE;MACzE/B,MAAM,CAACa,SAAS,GAAGI,mBAAmB,CAACZ,WAAW,CAAC;IACrD;IACA;EACF;EACA;EACA,IAAIQ,SAAS;EACb,IAAIb,MAAM,CAACmB,OAAO,IAAIjB,MAAM,CAACiB,OAAO,CAACY,OAAO,IAAI7B,MAAM,CAAC4B,IAAI,EAAE;IAC3DjB,SAAS,GAAGI,mBAAmB,CAACZ,WAAW,CAAC;EAC9C,CAAC,MAAM,IAAIL,MAAM,CAACqB,MAAM,CAAChB,WAAW,CAAC,EAAE;IACrCQ,SAAS,GAAGmB,QAAQ,CAAChC,MAAM,CAACqB,MAAM,CAAChB,WAAW,CAAC,CAAC4B,YAAY,CAAC,yBAAyB,CAAC,IAAI5B,WAAW,EAAE,EAAE,CAAC;EAC7G,CAAC,MAAM;IACLQ,SAAS,GAAGR,WAAW;EACzB;EACA6B,MAAM,CAACC,MAAM,CAACnC,MAAM,EAAE;IACpBgB,iBAAiB;IACjBD,SAAS;IACTD,iBAAiB;IACjBD,SAAS;IACTD,aAAa;IACbP;EACF,CAAC,CAAC;EACF,IAAIL,MAAM,CAACoC,WAAW,EAAE;IACtBtC,OAAO,CAACE,MAAM,CAAC;EACjB;EACAA,MAAM,CAAC6B,IAAI,CAAC,mBAAmB,CAAC;EAChC7B,MAAM,CAAC6B,IAAI,CAAC,iBAAiB,CAAC;EAC9B,IAAIf,iBAAiB,KAAKD,SAAS,EAAE;IACnCb,MAAM,CAAC6B,IAAI,CAAC,iBAAiB,CAAC;EAChC;EACA,IAAI7B,MAAM,CAACoC,WAAW,IAAIpC,MAAM,CAACE,MAAM,CAACmC,kBAAkB,EAAE;IAC1DrC,MAAM,CAAC6B,IAAI,CAAC,aAAa,CAAC;EAC5B;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}