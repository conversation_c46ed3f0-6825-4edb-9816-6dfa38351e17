{"ast": null, "code": "/**\n * @typedef {import('./info.js').Info} Info\n * @typedef {Record<string, Info>} Properties\n * @typedef {Record<string, string>} Normal\n */\n\nexport class Schema {\n  /**\n   * @constructor\n   * @param {Properties} property\n   * @param {Normal} normal\n   * @param {string} [space]\n   */\n  constructor(property, normal, space) {\n    this.property = property;\n    this.normal = normal;\n    if (space) {\n      this.space = space;\n    }\n  }\n}\n\n/** @type {Properties} */\nSchema.prototype.property = {};\n/** @type {Normal} */\nSchema.prototype.normal = {};\n/** @type {string|null} */\nSchema.prototype.space = null;", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "constructor", "property", "normal", "space", "prototype"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/property-information/lib/util/schema.js"], "sourcesContent": ["/**\n * @typedef {import('./info.js').Info} Info\n * @typedef {Record<string, Info>} Properties\n * @typedef {Record<string, string>} Normal\n */\n\nexport class Schema {\n  /**\n   * @constructor\n   * @param {Properties} property\n   * @param {Normal} normal\n   * @param {string} [space]\n   */\n  constructor(property, normal, space) {\n    this.property = property\n    this.normal = normal\n    if (space) {\n      this.space = space\n    }\n  }\n}\n\n/** @type {Properties} */\nSchema.prototype.property = {}\n/** @type {Normal} */\nSchema.prototype.normal = {}\n/** @type {string|null} */\nSchema.prototype.space = null\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,OAAO,MAAMA,MAAM,CAAC;EAClB;AACF;AACA;AACA;AACA;AACA;EACEC,WAAWA,CAACC,QAAQ,EAAEC,MAAM,EAAEC,KAAK,EAAE;IACnC,IAAI,CAACF,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAIC,KAAK,EAAE;MACT,IAAI,CAACA,KAAK,GAAGA,KAAK;IACpB;EACF;AACF;;AAEA;AACAJ,MAAM,CAACK,SAAS,CAACH,QAAQ,GAAG,CAAC,CAAC;AAC9B;AACAF,MAAM,CAACK,SAAS,CAACF,MAAM,GAAG,CAAC,CAAC;AAC5B;AACAH,MAAM,CAACK,SAAS,CAACD,KAAK,GAAG,IAAI"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}