{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\");\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nvar _classCallCheck2 = _interopRequireDefault(require(\"@babel/runtime/helpers/classCallCheck\"));\nvar _createClass2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createClass\"));\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"@babel/runtime/helpers/possibleConstructorReturn\"));\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"@babel/runtime/helpers/getPrototypeOf\"));\nvar _assertThisInitialized2 = _interopRequireDefault(require(\"@babel/runtime/helpers/assertThisInitialized\"));\nvar _inherits2 = _interopRequireDefault(require(\"@babel/runtime/helpers/inherits\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _react = _interopRequireWildcard(require(\"react\"));\nvar _classnames = _interopRequireDefault(require(\"classnames\"));\nvar propTypes = {\n  actions: _propTypes[\"default\"].object,\n  player: _propTypes[\"default\"].object,\n  className: _propTypes[\"default\"].string\n};\nvar FullscreenToggle = /*#__PURE__*/\nfunction (_Component) {\n  (0, _inherits2[\"default\"])(FullscreenToggle, _Component);\n  function FullscreenToggle(props, context) {\n    var _this;\n    (0, _classCallCheck2[\"default\"])(this, FullscreenToggle);\n    _this = (0, _possibleConstructorReturn2[\"default\"])(this, (0, _getPrototypeOf2[\"default\"])(FullscreenToggle).call(this, props, context));\n    _this.handleClick = _this.handleClick.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    return _this;\n  }\n  (0, _createClass2[\"default\"])(FullscreenToggle, [{\n    key: \"handleClick\",\n    value: function handleClick() {\n      var _this$props = this.props,\n        player = _this$props.player,\n        actions = _this$props.actions;\n      actions.toggleFullscreen(player);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this2 = this;\n      var _this$props2 = this.props,\n        player = _this$props2.player,\n        className = _this$props2.className;\n      return _react[\"default\"].createElement(\"button\", {\n        className: (0, _classnames[\"default\"])(className, {\n          'video-react-icon-fullscreen-exit': player.isFullscreen,\n          'video-react-icon-fullscreen': !player.isFullscreen\n        }, 'video-react-fullscreen-control video-react-control video-react-button video-react-icon'),\n        ref: function ref(c) {\n          _this2.button = c;\n        },\n        type: \"button\",\n        tabIndex: \"0\",\n        onClick: this.handleClick\n      }, _react[\"default\"].createElement(\"span\", {\n        className: \"video-react-control-text\"\n      }, \"Non-Fullscreen\"));\n    }\n  }]);\n  return FullscreenToggle;\n}(_react.Component);\nexports[\"default\"] = FullscreenToggle;\nFullscreenToggle.propTypes = propTypes;\nFullscreenToggle.displayName = 'FullscreenToggle';", "map": {"version": 3, "names": ["_interopRequireWildcard", "require", "_interopRequireDefault", "Object", "defineProperty", "exports", "value", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_assertThisInitialized2", "_inherits2", "_propTypes", "_react", "_classnames", "propTypes", "actions", "object", "player", "className", "string", "FullscreenToggle", "_Component", "props", "context", "_this", "call", "handleClick", "bind", "key", "_this$props", "toggleFullscreen", "render", "_this2", "_this$props2", "createElement", "isFullscreen", "ref", "c", "button", "type", "tabIndex", "onClick", "Component", "displayName"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/video-react/lib/components/control-bar/FullscreenToggle.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\");\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _classCallCheck2 = _interopRequireDefault(require(\"@babel/runtime/helpers/classCallCheck\"));\n\nvar _createClass2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createClass\"));\n\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"@babel/runtime/helpers/possibleConstructorReturn\"));\n\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"@babel/runtime/helpers/getPrototypeOf\"));\n\nvar _assertThisInitialized2 = _interopRequireDefault(require(\"@babel/runtime/helpers/assertThisInitialized\"));\n\nvar _inherits2 = _interopRequireDefault(require(\"@babel/runtime/helpers/inherits\"));\n\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\n\nvar _react = _interopRequireWildcard(require(\"react\"));\n\nvar _classnames = _interopRequireDefault(require(\"classnames\"));\n\nvar propTypes = {\n  actions: _propTypes[\"default\"].object,\n  player: _propTypes[\"default\"].object,\n  className: _propTypes[\"default\"].string\n};\n\nvar FullscreenToggle =\n/*#__PURE__*/\nfunction (_Component) {\n  (0, _inherits2[\"default\"])(FullscreenToggle, _Component);\n\n  function FullscreenToggle(props, context) {\n    var _this;\n\n    (0, _classCallCheck2[\"default\"])(this, FullscreenToggle);\n    _this = (0, _possibleConstructorReturn2[\"default\"])(this, (0, _getPrototypeOf2[\"default\"])(FullscreenToggle).call(this, props, context));\n    _this.handleClick = _this.handleClick.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    return _this;\n  }\n\n  (0, _createClass2[\"default\"])(FullscreenToggle, [{\n    key: \"handleClick\",\n    value: function handleClick() {\n      var _this$props = this.props,\n          player = _this$props.player,\n          actions = _this$props.actions;\n      actions.toggleFullscreen(player);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this2 = this;\n\n      var _this$props2 = this.props,\n          player = _this$props2.player,\n          className = _this$props2.className;\n      return _react[\"default\"].createElement(\"button\", {\n        className: (0, _classnames[\"default\"])(className, {\n          'video-react-icon-fullscreen-exit': player.isFullscreen,\n          'video-react-icon-fullscreen': !player.isFullscreen\n        }, 'video-react-fullscreen-control video-react-control video-react-button video-react-icon'),\n        ref: function ref(c) {\n          _this2.button = c;\n        },\n        type: \"button\",\n        tabIndex: \"0\",\n        onClick: this.handleClick\n      }, _react[\"default\"].createElement(\"span\", {\n        className: \"video-react-control-text\"\n      }, \"Non-Fullscreen\"));\n    }\n  }]);\n  return FullscreenToggle;\n}(_react.Component);\n\nexports[\"default\"] = FullscreenToggle;\nFullscreenToggle.propTypes = propTypes;\nFullscreenToggle.displayName = 'FullscreenToggle';"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,uBAAuB,GAAGC,OAAO,CAAC,+CAA+C,CAAC;AAEtF,IAAIC,sBAAsB,GAAGD,OAAO,CAAC,8CAA8C,CAAC;AAEpFE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;AAE3B,IAAIE,gBAAgB,GAAGL,sBAAsB,CAACD,OAAO,CAAC,uCAAuC,CAAC,CAAC;AAE/F,IAAIO,aAAa,GAAGN,sBAAsB,CAACD,OAAO,CAAC,oCAAoC,CAAC,CAAC;AAEzF,IAAIQ,2BAA2B,GAAGP,sBAAsB,CAACD,OAAO,CAAC,kDAAkD,CAAC,CAAC;AAErH,IAAIS,gBAAgB,GAAGR,sBAAsB,CAACD,OAAO,CAAC,uCAAuC,CAAC,CAAC;AAE/F,IAAIU,uBAAuB,GAAGT,sBAAsB,CAACD,OAAO,CAAC,8CAA8C,CAAC,CAAC;AAE7G,IAAIW,UAAU,GAAGV,sBAAsB,CAACD,OAAO,CAAC,iCAAiC,CAAC,CAAC;AAEnF,IAAIY,UAAU,GAAGX,sBAAsB,CAACD,OAAO,CAAC,YAAY,CAAC,CAAC;AAE9D,IAAIa,MAAM,GAAGd,uBAAuB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AAEtD,IAAIc,WAAW,GAAGb,sBAAsB,CAACD,OAAO,CAAC,YAAY,CAAC,CAAC;AAE/D,IAAIe,SAAS,GAAG;EACdC,OAAO,EAAEJ,UAAU,CAAC,SAAS,CAAC,CAACK,MAAM;EACrCC,MAAM,EAAEN,UAAU,CAAC,SAAS,CAAC,CAACK,MAAM;EACpCE,SAAS,EAAEP,UAAU,CAAC,SAAS,CAAC,CAACQ;AACnC,CAAC;AAED,IAAIC,gBAAgB,GACpB;AACA,UAAUC,UAAU,EAAE;EACpB,CAAC,CAAC,EAAEX,UAAU,CAAC,SAAS,CAAC,EAAEU,gBAAgB,EAAEC,UAAU,CAAC;EAExD,SAASD,gBAAgBA,CAACE,KAAK,EAAEC,OAAO,EAAE;IACxC,IAAIC,KAAK;IAET,CAAC,CAAC,EAAEnB,gBAAgB,CAAC,SAAS,CAAC,EAAE,IAAI,EAAEe,gBAAgB,CAAC;IACxDI,KAAK,GAAG,CAAC,CAAC,EAAEjB,2BAA2B,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAEC,gBAAgB,CAAC,SAAS,CAAC,EAAEY,gBAAgB,CAAC,CAACK,IAAI,CAAC,IAAI,EAAEH,KAAK,EAAEC,OAAO,CAAC,CAAC;IACxIC,KAAK,CAACE,WAAW,GAAGF,KAAK,CAACE,WAAW,CAACC,IAAI,CAAC,CAAC,CAAC,EAAElB,uBAAuB,CAAC,SAAS,CAAC,EAAEe,KAAK,CAAC,CAAC;IAC1F,OAAOA,KAAK;EACd;EAEA,CAAC,CAAC,EAAElB,aAAa,CAAC,SAAS,CAAC,EAAEc,gBAAgB,EAAE,CAAC;IAC/CQ,GAAG,EAAE,aAAa;IAClBxB,KAAK,EAAE,SAASsB,WAAWA,CAAA,EAAG;MAC5B,IAAIG,WAAW,GAAG,IAAI,CAACP,KAAK;QACxBL,MAAM,GAAGY,WAAW,CAACZ,MAAM;QAC3BF,OAAO,GAAGc,WAAW,CAACd,OAAO;MACjCA,OAAO,CAACe,gBAAgB,CAACb,MAAM,CAAC;IAClC;EACF,CAAC,EAAE;IACDW,GAAG,EAAE,QAAQ;IACbxB,KAAK,EAAE,SAAS2B,MAAMA,CAAA,EAAG;MACvB,IAAIC,MAAM,GAAG,IAAI;MAEjB,IAAIC,YAAY,GAAG,IAAI,CAACX,KAAK;QACzBL,MAAM,GAAGgB,YAAY,CAAChB,MAAM;QAC5BC,SAAS,GAAGe,YAAY,CAACf,SAAS;MACtC,OAAON,MAAM,CAAC,SAAS,CAAC,CAACsB,aAAa,CAAC,QAAQ,EAAE;QAC/ChB,SAAS,EAAE,CAAC,CAAC,EAAEL,WAAW,CAAC,SAAS,CAAC,EAAEK,SAAS,EAAE;UAChD,kCAAkC,EAAED,MAAM,CAACkB,YAAY;UACvD,6BAA6B,EAAE,CAAClB,MAAM,CAACkB;QACzC,CAAC,EAAE,wFAAwF,CAAC;QAC5FC,GAAG,EAAE,SAASA,GAAGA,CAACC,CAAC,EAAE;UACnBL,MAAM,CAACM,MAAM,GAAGD,CAAC;QACnB,CAAC;QACDE,IAAI,EAAE,QAAQ;QACdC,QAAQ,EAAE,GAAG;QACbC,OAAO,EAAE,IAAI,CAACf;MAChB,CAAC,EAAEd,MAAM,CAAC,SAAS,CAAC,CAACsB,aAAa,CAAC,MAAM,EAAE;QACzChB,SAAS,EAAE;MACb,CAAC,EAAE,gBAAgB,CAAC,CAAC;IACvB;EACF,CAAC,CAAC,CAAC;EACH,OAAOE,gBAAgB;AACzB,CAAC,CAACR,MAAM,CAAC8B,SAAS,CAAC;AAEnBvC,OAAO,CAAC,SAAS,CAAC,GAAGiB,gBAAgB;AACrCA,gBAAgB,CAACN,SAAS,GAAGA,SAAS;AACtCM,gBAAgB,CAACuB,WAAW,GAAG,kBAAkB"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}