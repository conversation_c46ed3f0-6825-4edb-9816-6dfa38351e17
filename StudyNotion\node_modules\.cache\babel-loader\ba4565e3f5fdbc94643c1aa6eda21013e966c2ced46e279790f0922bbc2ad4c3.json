{"ast": null, "code": "import { getWindow } from 'ssr-window';\nimport { elementChildren, elementOffset, elementParents, getTranslate } from '../../shared/utils.js';\nexport default function Zoom(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on,\n    emit\n  } = _ref;\n  const window = getWindow();\n  extendParams({\n    zoom: {\n      enabled: false,\n      maxRatio: 3,\n      minRatio: 1,\n      toggle: true,\n      containerClass: 'swiper-zoom-container',\n      zoomedSlideClass: 'swiper-slide-zoomed'\n    }\n  });\n  swiper.zoom = {\n    enabled: false\n  };\n  let currentScale = 1;\n  let isScaling = false;\n  let fakeGestureTouched;\n  let fakeGestureMoved;\n  const evCache = [];\n  const gesture = {\n    originX: 0,\n    originY: 0,\n    slideEl: undefined,\n    slideWidth: undefined,\n    slideHeight: undefined,\n    imageEl: undefined,\n    imageWrapEl: undefined,\n    maxRatio: 3\n  };\n  const image = {\n    isTouched: undefined,\n    isMoved: undefined,\n    currentX: undefined,\n    currentY: undefined,\n    minX: undefined,\n    minY: undefined,\n    maxX: undefined,\n    maxY: undefined,\n    width: undefined,\n    height: undefined,\n    startX: undefined,\n    startY: undefined,\n    touchesStart: {},\n    touchesCurrent: {}\n  };\n  const velocity = {\n    x: undefined,\n    y: undefined,\n    prevPositionX: undefined,\n    prevPositionY: undefined,\n    prevTime: undefined\n  };\n  let scale = 1;\n  Object.defineProperty(swiper.zoom, 'scale', {\n    get() {\n      return scale;\n    },\n    set(value) {\n      if (scale !== value) {\n        const imageEl = gesture.imageEl;\n        const slideEl = gesture.slideEl;\n        emit('zoomChange', value, imageEl, slideEl);\n      }\n      scale = value;\n    }\n  });\n  function getDistanceBetweenTouches() {\n    if (evCache.length < 2) return 1;\n    const x1 = evCache[0].pageX;\n    const y1 = evCache[0].pageY;\n    const x2 = evCache[1].pageX;\n    const y2 = evCache[1].pageY;\n    const distance = Math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2);\n    return distance;\n  }\n  function getScaleOrigin() {\n    if (evCache.length < 2) return {\n      x: null,\n      y: null\n    };\n    const box = gesture.imageEl.getBoundingClientRect();\n    return [(evCache[0].pageX + (evCache[1].pageX - evCache[0].pageX) / 2 - box.x) / currentScale, (evCache[0].pageY + (evCache[1].pageY - evCache[0].pageY) / 2 - box.y) / currentScale];\n  }\n  function getSlideSelector() {\n    return swiper.isElement ? `swiper-slide` : `.${swiper.params.slideClass}`;\n  }\n  function eventWithinSlide(e) {\n    const slideSelector = getSlideSelector();\n    if (e.target.matches(slideSelector)) return true;\n    if (swiper.slides.filter(slideEl => slideEl.contains(e.target)).length > 0) return true;\n    return false;\n  }\n  function eventWithinZoomContainer(e) {\n    const selector = `.${swiper.params.zoom.containerClass}`;\n    if (e.target.matches(selector)) return true;\n    if ([...swiper.el.querySelectorAll(selector)].filter(containerEl => containerEl.contains(e.target)).length > 0) return true;\n    return false;\n  }\n\n  // Events\n  function onGestureStart(e) {\n    if (e.pointerType === 'mouse') {\n      evCache.splice(0, evCache.length);\n    }\n    if (!eventWithinSlide(e)) return;\n    const params = swiper.params.zoom;\n    fakeGestureTouched = false;\n    fakeGestureMoved = false;\n    evCache.push(e);\n    if (evCache.length < 2) {\n      return;\n    }\n    fakeGestureTouched = true;\n    gesture.scaleStart = getDistanceBetweenTouches();\n    if (!gesture.slideEl) {\n      gesture.slideEl = e.target.closest(`.${swiper.params.slideClass}, swiper-slide`);\n      if (!gesture.slideEl) gesture.slideEl = swiper.slides[swiper.activeIndex];\n      let imageEl = gesture.slideEl.querySelector(`.${params.containerClass}`);\n      if (imageEl) {\n        imageEl = imageEl.querySelectorAll('picture, img, svg, canvas, .swiper-zoom-target')[0];\n      }\n      gesture.imageEl = imageEl;\n      if (imageEl) {\n        gesture.imageWrapEl = elementParents(gesture.imageEl, `.${params.containerClass}`)[0];\n      } else {\n        gesture.imageWrapEl = undefined;\n      }\n      if (!gesture.imageWrapEl) {\n        gesture.imageEl = undefined;\n        return;\n      }\n      gesture.maxRatio = gesture.imageWrapEl.getAttribute('data-swiper-zoom') || params.maxRatio;\n    }\n    if (gesture.imageEl) {\n      const [originX, originY] = getScaleOrigin();\n      gesture.originX = originX;\n      gesture.originY = originY;\n      gesture.imageEl.style.transitionDuration = '0ms';\n    }\n    isScaling = true;\n  }\n  function onGestureChange(e) {\n    if (!eventWithinSlide(e)) return;\n    const params = swiper.params.zoom;\n    const zoom = swiper.zoom;\n    const pointerIndex = evCache.findIndex(cachedEv => cachedEv.pointerId === e.pointerId);\n    if (pointerIndex >= 0) evCache[pointerIndex] = e;\n    if (evCache.length < 2) {\n      return;\n    }\n    fakeGestureMoved = true;\n    gesture.scaleMove = getDistanceBetweenTouches();\n    if (!gesture.imageEl) {\n      return;\n    }\n    zoom.scale = gesture.scaleMove / gesture.scaleStart * currentScale;\n    if (zoom.scale > gesture.maxRatio) {\n      zoom.scale = gesture.maxRatio - 1 + (zoom.scale - gesture.maxRatio + 1) ** 0.5;\n    }\n    if (zoom.scale < params.minRatio) {\n      zoom.scale = params.minRatio + 1 - (params.minRatio - zoom.scale + 1) ** 0.5;\n    }\n    gesture.imageEl.style.transform = `translate3d(0,0,0) scale(${zoom.scale})`;\n  }\n  function onGestureEnd(e) {\n    if (!eventWithinSlide(e)) return;\n    if (e.pointerType === 'mouse' && e.type === 'pointerout') return;\n    const params = swiper.params.zoom;\n    const zoom = swiper.zoom;\n    const pointerIndex = evCache.findIndex(cachedEv => cachedEv.pointerId === e.pointerId);\n    if (pointerIndex >= 0) evCache.splice(pointerIndex, 1);\n    if (!fakeGestureTouched || !fakeGestureMoved) {\n      return;\n    }\n    fakeGestureTouched = false;\n    fakeGestureMoved = false;\n    if (!gesture.imageEl) return;\n    zoom.scale = Math.max(Math.min(zoom.scale, gesture.maxRatio), params.minRatio);\n    gesture.imageEl.style.transitionDuration = `${swiper.params.speed}ms`;\n    gesture.imageEl.style.transform = `translate3d(0,0,0) scale(${zoom.scale})`;\n    currentScale = zoom.scale;\n    isScaling = false;\n    if (zoom.scale > 1 && gesture.slideEl) {\n      gesture.slideEl.classList.add(`${params.zoomedSlideClass}`);\n    } else if (zoom.scale <= 1 && gesture.slideEl) {\n      gesture.slideEl.classList.remove(`${params.zoomedSlideClass}`);\n    }\n    if (zoom.scale === 1) {\n      gesture.originX = 0;\n      gesture.originY = 0;\n      gesture.slideEl = undefined;\n    }\n  }\n  function onTouchStart(e) {\n    const device = swiper.device;\n    if (!gesture.imageEl) return;\n    if (image.isTouched) return;\n    if (device.android && e.cancelable) e.preventDefault();\n    image.isTouched = true;\n    const event = evCache.length > 0 ? evCache[0] : e;\n    image.touchesStart.x = event.pageX;\n    image.touchesStart.y = event.pageY;\n  }\n  function onTouchMove(e) {\n    if (!eventWithinSlide(e) || !eventWithinZoomContainer(e)) return;\n    const zoom = swiper.zoom;\n    if (!gesture.imageEl) return;\n    if (!image.isTouched || !gesture.slideEl) return;\n    if (!image.isMoved) {\n      image.width = gesture.imageEl.offsetWidth;\n      image.height = gesture.imageEl.offsetHeight;\n      image.startX = getTranslate(gesture.imageWrapEl, 'x') || 0;\n      image.startY = getTranslate(gesture.imageWrapEl, 'y') || 0;\n      gesture.slideWidth = gesture.slideEl.offsetWidth;\n      gesture.slideHeight = gesture.slideEl.offsetHeight;\n      gesture.imageWrapEl.style.transitionDuration = '0ms';\n    }\n    // Define if we need image drag\n    const scaledWidth = image.width * zoom.scale;\n    const scaledHeight = image.height * zoom.scale;\n    if (scaledWidth < gesture.slideWidth && scaledHeight < gesture.slideHeight) return;\n    image.minX = Math.min(gesture.slideWidth / 2 - scaledWidth / 2, 0);\n    image.maxX = -image.minX;\n    image.minY = Math.min(gesture.slideHeight / 2 - scaledHeight / 2, 0);\n    image.maxY = -image.minY;\n    image.touchesCurrent.x = evCache.length > 0 ? evCache[0].pageX : e.pageX;\n    image.touchesCurrent.y = evCache.length > 0 ? evCache[0].pageY : e.pageY;\n    const touchesDiff = Math.max(Math.abs(image.touchesCurrent.x - image.touchesStart.x), Math.abs(image.touchesCurrent.y - image.touchesStart.y));\n    if (touchesDiff > 5) {\n      swiper.allowClick = false;\n    }\n    if (!image.isMoved && !isScaling) {\n      if (swiper.isHorizontal() && (Math.floor(image.minX) === Math.floor(image.startX) && image.touchesCurrent.x < image.touchesStart.x || Math.floor(image.maxX) === Math.floor(image.startX) && image.touchesCurrent.x > image.touchesStart.x)) {\n        image.isTouched = false;\n        return;\n      }\n      if (!swiper.isHorizontal() && (Math.floor(image.minY) === Math.floor(image.startY) && image.touchesCurrent.y < image.touchesStart.y || Math.floor(image.maxY) === Math.floor(image.startY) && image.touchesCurrent.y > image.touchesStart.y)) {\n        image.isTouched = false;\n        return;\n      }\n    }\n    if (e.cancelable) {\n      e.preventDefault();\n    }\n    e.stopPropagation();\n    image.isMoved = true;\n    const scaleRatio = (zoom.scale - currentScale) / (gesture.maxRatio - swiper.params.zoom.minRatio);\n    const {\n      originX,\n      originY\n    } = gesture;\n    image.currentX = image.touchesCurrent.x - image.touchesStart.x + image.startX + scaleRatio * (image.width - originX * 2);\n    image.currentY = image.touchesCurrent.y - image.touchesStart.y + image.startY + scaleRatio * (image.height - originY * 2);\n    if (image.currentX < image.minX) {\n      image.currentX = image.minX + 1 - (image.minX - image.currentX + 1) ** 0.8;\n    }\n    if (image.currentX > image.maxX) {\n      image.currentX = image.maxX - 1 + (image.currentX - image.maxX + 1) ** 0.8;\n    }\n    if (image.currentY < image.minY) {\n      image.currentY = image.minY + 1 - (image.minY - image.currentY + 1) ** 0.8;\n    }\n    if (image.currentY > image.maxY) {\n      image.currentY = image.maxY - 1 + (image.currentY - image.maxY + 1) ** 0.8;\n    }\n\n    // Velocity\n    if (!velocity.prevPositionX) velocity.prevPositionX = image.touchesCurrent.x;\n    if (!velocity.prevPositionY) velocity.prevPositionY = image.touchesCurrent.y;\n    if (!velocity.prevTime) velocity.prevTime = Date.now();\n    velocity.x = (image.touchesCurrent.x - velocity.prevPositionX) / (Date.now() - velocity.prevTime) / 2;\n    velocity.y = (image.touchesCurrent.y - velocity.prevPositionY) / (Date.now() - velocity.prevTime) / 2;\n    if (Math.abs(image.touchesCurrent.x - velocity.prevPositionX) < 2) velocity.x = 0;\n    if (Math.abs(image.touchesCurrent.y - velocity.prevPositionY) < 2) velocity.y = 0;\n    velocity.prevPositionX = image.touchesCurrent.x;\n    velocity.prevPositionY = image.touchesCurrent.y;\n    velocity.prevTime = Date.now();\n    gesture.imageWrapEl.style.transform = `translate3d(${image.currentX}px, ${image.currentY}px,0)`;\n  }\n  function onTouchEnd() {\n    const zoom = swiper.zoom;\n    if (!gesture.imageEl) return;\n    if (!image.isTouched || !image.isMoved) {\n      image.isTouched = false;\n      image.isMoved = false;\n      return;\n    }\n    image.isTouched = false;\n    image.isMoved = false;\n    let momentumDurationX = 300;\n    let momentumDurationY = 300;\n    const momentumDistanceX = velocity.x * momentumDurationX;\n    const newPositionX = image.currentX + momentumDistanceX;\n    const momentumDistanceY = velocity.y * momentumDurationY;\n    const newPositionY = image.currentY + momentumDistanceY;\n\n    // Fix duration\n    if (velocity.x !== 0) momentumDurationX = Math.abs((newPositionX - image.currentX) / velocity.x);\n    if (velocity.y !== 0) momentumDurationY = Math.abs((newPositionY - image.currentY) / velocity.y);\n    const momentumDuration = Math.max(momentumDurationX, momentumDurationY);\n    image.currentX = newPositionX;\n    image.currentY = newPositionY;\n    // Define if we need image drag\n    const scaledWidth = image.width * zoom.scale;\n    const scaledHeight = image.height * zoom.scale;\n    image.minX = Math.min(gesture.slideWidth / 2 - scaledWidth / 2, 0);\n    image.maxX = -image.minX;\n    image.minY = Math.min(gesture.slideHeight / 2 - scaledHeight / 2, 0);\n    image.maxY = -image.minY;\n    image.currentX = Math.max(Math.min(image.currentX, image.maxX), image.minX);\n    image.currentY = Math.max(Math.min(image.currentY, image.maxY), image.minY);\n    gesture.imageWrapEl.style.transitionDuration = `${momentumDuration}ms`;\n    gesture.imageWrapEl.style.transform = `translate3d(${image.currentX}px, ${image.currentY}px,0)`;\n  }\n  function onTransitionEnd() {\n    const zoom = swiper.zoom;\n    if (gesture.slideEl && swiper.activeIndex !== swiper.slides.indexOf(gesture.slideEl)) {\n      if (gesture.imageEl) {\n        gesture.imageEl.style.transform = 'translate3d(0,0,0) scale(1)';\n      }\n      if (gesture.imageWrapEl) {\n        gesture.imageWrapEl.style.transform = 'translate3d(0,0,0)';\n      }\n      gesture.slideEl.classList.remove(`${swiper.params.zoom.zoomedSlideClass}`);\n      zoom.scale = 1;\n      currentScale = 1;\n      gesture.slideEl = undefined;\n      gesture.imageEl = undefined;\n      gesture.imageWrapEl = undefined;\n      gesture.originX = 0;\n      gesture.originY = 0;\n    }\n  }\n  function zoomIn(e) {\n    const zoom = swiper.zoom;\n    const params = swiper.params.zoom;\n    if (!gesture.slideEl) {\n      if (e && e.target) {\n        gesture.slideEl = e.target.closest(`.${swiper.params.slideClass}, swiper-slide`);\n      }\n      if (!gesture.slideEl) {\n        if (swiper.params.virtual && swiper.params.virtual.enabled && swiper.virtual) {\n          gesture.slideEl = elementChildren(swiper.slidesEl, `.${swiper.params.slideActiveClass}`)[0];\n        } else {\n          gesture.slideEl = swiper.slides[swiper.activeIndex];\n        }\n      }\n      let imageEl = gesture.slideEl.querySelector(`.${params.containerClass}`);\n      if (imageEl) {\n        imageEl = imageEl.querySelectorAll('picture, img, svg, canvas, .swiper-zoom-target')[0];\n      }\n      gesture.imageEl = imageEl;\n      if (imageEl) {\n        gesture.imageWrapEl = elementParents(gesture.imageEl, `.${params.containerClass}`)[0];\n      } else {\n        gesture.imageWrapEl = undefined;\n      }\n    }\n    if (!gesture.imageEl || !gesture.imageWrapEl) return;\n    if (swiper.params.cssMode) {\n      swiper.wrapperEl.style.overflow = 'hidden';\n      swiper.wrapperEl.style.touchAction = 'none';\n    }\n    gesture.slideEl.classList.add(`${params.zoomedSlideClass}`);\n    let touchX;\n    let touchY;\n    let offsetX;\n    let offsetY;\n    let diffX;\n    let diffY;\n    let translateX;\n    let translateY;\n    let imageWidth;\n    let imageHeight;\n    let scaledWidth;\n    let scaledHeight;\n    let translateMinX;\n    let translateMinY;\n    let translateMaxX;\n    let translateMaxY;\n    let slideWidth;\n    let slideHeight;\n    if (typeof image.touchesStart.x === 'undefined' && e) {\n      touchX = e.pageX;\n      touchY = e.pageY;\n    } else {\n      touchX = image.touchesStart.x;\n      touchY = image.touchesStart.y;\n    }\n    const forceZoomRatio = typeof e === 'number' ? e : null;\n    if (currentScale === 1 && forceZoomRatio) {\n      touchX = undefined;\n      touchY = undefined;\n    }\n    zoom.scale = forceZoomRatio || gesture.imageWrapEl.getAttribute('data-swiper-zoom') || params.maxRatio;\n    currentScale = forceZoomRatio || gesture.imageWrapEl.getAttribute('data-swiper-zoom') || params.maxRatio;\n    if (e && !(currentScale === 1 && forceZoomRatio)) {\n      slideWidth = gesture.slideEl.offsetWidth;\n      slideHeight = gesture.slideEl.offsetHeight;\n      offsetX = elementOffset(gesture.slideEl).left + window.scrollX;\n      offsetY = elementOffset(gesture.slideEl).top + window.scrollY;\n      diffX = offsetX + slideWidth / 2 - touchX;\n      diffY = offsetY + slideHeight / 2 - touchY;\n      imageWidth = gesture.imageEl.offsetWidth;\n      imageHeight = gesture.imageEl.offsetHeight;\n      scaledWidth = imageWidth * zoom.scale;\n      scaledHeight = imageHeight * zoom.scale;\n      translateMinX = Math.min(slideWidth / 2 - scaledWidth / 2, 0);\n      translateMinY = Math.min(slideHeight / 2 - scaledHeight / 2, 0);\n      translateMaxX = -translateMinX;\n      translateMaxY = -translateMinY;\n      translateX = diffX * zoom.scale;\n      translateY = diffY * zoom.scale;\n      if (translateX < translateMinX) {\n        translateX = translateMinX;\n      }\n      if (translateX > translateMaxX) {\n        translateX = translateMaxX;\n      }\n      if (translateY < translateMinY) {\n        translateY = translateMinY;\n      }\n      if (translateY > translateMaxY) {\n        translateY = translateMaxY;\n      }\n    } else {\n      translateX = 0;\n      translateY = 0;\n    }\n    if (forceZoomRatio && zoom.scale === 1) {\n      gesture.originX = 0;\n      gesture.originY = 0;\n    }\n    gesture.imageWrapEl.style.transitionDuration = '300ms';\n    gesture.imageWrapEl.style.transform = `translate3d(${translateX}px, ${translateY}px,0)`;\n    gesture.imageEl.style.transitionDuration = '300ms';\n    gesture.imageEl.style.transform = `translate3d(0,0,0) scale(${zoom.scale})`;\n  }\n  function zoomOut() {\n    const zoom = swiper.zoom;\n    const params = swiper.params.zoom;\n    if (!gesture.slideEl) {\n      if (swiper.params.virtual && swiper.params.virtual.enabled && swiper.virtual) {\n        gesture.slideEl = elementChildren(swiper.slidesEl, `.${swiper.params.slideActiveClass}`)[0];\n      } else {\n        gesture.slideEl = swiper.slides[swiper.activeIndex];\n      }\n      let imageEl = gesture.slideEl.querySelector(`.${params.containerClass}`);\n      if (imageEl) {\n        imageEl = imageEl.querySelectorAll('picture, img, svg, canvas, .swiper-zoom-target')[0];\n      }\n      gesture.imageEl = imageEl;\n      if (imageEl) {\n        gesture.imageWrapEl = elementParents(gesture.imageEl, `.${params.containerClass}`)[0];\n      } else {\n        gesture.imageWrapEl = undefined;\n      }\n    }\n    if (!gesture.imageEl || !gesture.imageWrapEl) return;\n    if (swiper.params.cssMode) {\n      swiper.wrapperEl.style.overflow = '';\n      swiper.wrapperEl.style.touchAction = '';\n    }\n    zoom.scale = 1;\n    currentScale = 1;\n    gesture.imageWrapEl.style.transitionDuration = '300ms';\n    gesture.imageWrapEl.style.transform = 'translate3d(0,0,0)';\n    gesture.imageEl.style.transitionDuration = '300ms';\n    gesture.imageEl.style.transform = 'translate3d(0,0,0) scale(1)';\n    gesture.slideEl.classList.remove(`${params.zoomedSlideClass}`);\n    gesture.slideEl = undefined;\n    gesture.originX = 0;\n    gesture.originY = 0;\n  }\n\n  // Toggle Zoom\n  function zoomToggle(e) {\n    const zoom = swiper.zoom;\n    if (zoom.scale && zoom.scale !== 1) {\n      // Zoom Out\n      zoomOut();\n    } else {\n      // Zoom In\n      zoomIn(e);\n    }\n  }\n  function getListeners() {\n    const passiveListener = swiper.params.passiveListeners ? {\n      passive: true,\n      capture: false\n    } : false;\n    const activeListenerWithCapture = swiper.params.passiveListeners ? {\n      passive: false,\n      capture: true\n    } : true;\n    return {\n      passiveListener,\n      activeListenerWithCapture\n    };\n  }\n\n  // Attach/Detach Events\n  function enable() {\n    const zoom = swiper.zoom;\n    if (zoom.enabled) return;\n    zoom.enabled = true;\n    const {\n      passiveListener,\n      activeListenerWithCapture\n    } = getListeners();\n\n    // Scale image\n    swiper.wrapperEl.addEventListener('pointerdown', onGestureStart, passiveListener);\n    swiper.wrapperEl.addEventListener('pointermove', onGestureChange, activeListenerWithCapture);\n    ['pointerup', 'pointercancel', 'pointerout'].forEach(eventName => {\n      swiper.wrapperEl.addEventListener(eventName, onGestureEnd, passiveListener);\n    });\n\n    // Move image\n    swiper.wrapperEl.addEventListener('pointermove', onTouchMove, activeListenerWithCapture);\n  }\n  function disable() {\n    const zoom = swiper.zoom;\n    if (!zoom.enabled) return;\n    zoom.enabled = false;\n    const {\n      passiveListener,\n      activeListenerWithCapture\n    } = getListeners();\n\n    // Scale image\n    swiper.wrapperEl.removeEventListener('pointerdown', onGestureStart, passiveListener);\n    swiper.wrapperEl.removeEventListener('pointermove', onGestureChange, activeListenerWithCapture);\n    ['pointerup', 'pointercancel', 'pointerout'].forEach(eventName => {\n      swiper.wrapperEl.removeEventListener(eventName, onGestureEnd, passiveListener);\n    });\n\n    // Move image\n    swiper.wrapperEl.removeEventListener('pointermove', onTouchMove, activeListenerWithCapture);\n  }\n  on('init', () => {\n    if (swiper.params.zoom.enabled) {\n      enable();\n    }\n  });\n  on('destroy', () => {\n    disable();\n  });\n  on('touchStart', (_s, e) => {\n    if (!swiper.zoom.enabled) return;\n    onTouchStart(e);\n  });\n  on('touchEnd', (_s, e) => {\n    if (!swiper.zoom.enabled) return;\n    onTouchEnd(e);\n  });\n  on('doubleTap', (_s, e) => {\n    if (!swiper.animating && swiper.params.zoom.enabled && swiper.zoom.enabled && swiper.params.zoom.toggle) {\n      zoomToggle(e);\n    }\n  });\n  on('transitionEnd', () => {\n    if (swiper.zoom.enabled && swiper.params.zoom.enabled) {\n      onTransitionEnd();\n    }\n  });\n  on('slideChange', () => {\n    if (swiper.zoom.enabled && swiper.params.zoom.enabled && swiper.params.cssMode) {\n      onTransitionEnd();\n    }\n  });\n  Object.assign(swiper.zoom, {\n    enable,\n    disable,\n    in: zoomIn,\n    out: zoomOut,\n    toggle: zoomToggle\n  });\n}", "map": {"version": 3, "names": ["getWindow", "elementChildren", "elementOffset", "elementParents", "getTranslate", "Zoom", "_ref", "swiper", "extendParams", "on", "emit", "window", "zoom", "enabled", "maxRatio", "minRatio", "toggle", "containerClass", "zoomedSlideClass", "currentScale", "isScaling", "fakeGestureTouched", "fakeGestureMoved", "ev<PERSON><PERSON>", "gesture", "originX", "originY", "slideEl", "undefined", "slideWidth", "slideHeight", "imageEl", "imageWrapEl", "image", "isTouched", "isMoved", "currentX", "currentY", "minX", "minY", "maxX", "maxY", "width", "height", "startX", "startY", "touchesStart", "touchesCurrent", "velocity", "x", "y", "prevPositionX", "prevPositionY", "prevTime", "scale", "Object", "defineProperty", "get", "set", "value", "getDistanceBetweenTouches", "length", "x1", "pageX", "y1", "pageY", "x2", "y2", "distance", "Math", "sqrt", "getScaleOrigin", "box", "getBoundingClientRect", "getSlideSelector", "isElement", "params", "slideClass", "eventWithinSlide", "e", "slideSelector", "target", "matches", "slides", "filter", "contains", "eventWithinZoomContainer", "selector", "el", "querySelectorAll", "containerEl", "onGestureStart", "pointerType", "splice", "push", "scaleStart", "closest", "activeIndex", "querySelector", "getAttribute", "style", "transitionDuration", "onGestureChange", "pointerIndex", "findIndex", "cachedEv", "pointerId", "scaleMove", "transform", "onGestureEnd", "type", "max", "min", "speed", "classList", "add", "remove", "onTouchStart", "device", "android", "cancelable", "preventDefault", "event", "onTouchMove", "offsetWidth", "offsetHeight", "scaledWidth", "scaledHeight", "touchesDiff", "abs", "allowClick", "isHorizontal", "floor", "stopPropagation", "scaleRatio", "Date", "now", "onTouchEnd", "momentumDurationX", "momentumDurationY", "momentumDistanceX", "newPositionX", "momentumDistanceY", "newPositionY", "momentumDuration", "onTransitionEnd", "indexOf", "zoomIn", "virtual", "slidesEl", "slideActiveClass", "cssMode", "wrapperEl", "overflow", "touchAction", "touchX", "touchY", "offsetX", "offsetY", "diffX", "diffY", "translateX", "translateY", "imageWidth", "imageHeight", "translateMinX", "translateMinY", "translateMaxX", "translateMaxY", "forceZoomRatio", "left", "scrollX", "top", "scrollY", "zoomOut", "zoomToggle", "getListeners", "passiveListener", "passiveListeners", "passive", "capture", "activeListenerWithCapture", "enable", "addEventListener", "for<PERSON>ach", "eventName", "disable", "removeEventListener", "_s", "animating", "assign", "in", "out"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/modules/zoom/zoom.js"], "sourcesContent": ["import { getWindow } from 'ssr-window';\nimport { elementChildren, elementOffset, elementParents, getTranslate } from '../../shared/utils.js';\nexport default function Zoom({\n  swiper,\n  extendParams,\n  on,\n  emit\n}) {\n  const window = getWindow();\n  extendParams({\n    zoom: {\n      enabled: false,\n      maxRatio: 3,\n      minRatio: 1,\n      toggle: true,\n      containerClass: 'swiper-zoom-container',\n      zoomedSlideClass: 'swiper-slide-zoomed'\n    }\n  });\n  swiper.zoom = {\n    enabled: false\n  };\n  let currentScale = 1;\n  let isScaling = false;\n  let fakeGestureTouched;\n  let fakeGestureMoved;\n  const evCache = [];\n  const gesture = {\n    originX: 0,\n    originY: 0,\n    slideEl: undefined,\n    slideWidth: undefined,\n    slideHeight: undefined,\n    imageEl: undefined,\n    imageWrapEl: undefined,\n    maxRatio: 3\n  };\n  const image = {\n    isTouched: undefined,\n    isMoved: undefined,\n    currentX: undefined,\n    currentY: undefined,\n    minX: undefined,\n    minY: undefined,\n    maxX: undefined,\n    maxY: undefined,\n    width: undefined,\n    height: undefined,\n    startX: undefined,\n    startY: undefined,\n    touchesStart: {},\n    touchesCurrent: {}\n  };\n  const velocity = {\n    x: undefined,\n    y: undefined,\n    prevPositionX: undefined,\n    prevPositionY: undefined,\n    prevTime: undefined\n  };\n  let scale = 1;\n  Object.defineProperty(swiper.zoom, 'scale', {\n    get() {\n      return scale;\n    },\n    set(value) {\n      if (scale !== value) {\n        const imageEl = gesture.imageEl;\n        const slideEl = gesture.slideEl;\n        emit('zoomChange', value, imageEl, slideEl);\n      }\n      scale = value;\n    }\n  });\n  function getDistanceBetweenTouches() {\n    if (evCache.length < 2) return 1;\n    const x1 = evCache[0].pageX;\n    const y1 = evCache[0].pageY;\n    const x2 = evCache[1].pageX;\n    const y2 = evCache[1].pageY;\n    const distance = Math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2);\n    return distance;\n  }\n  function getScaleOrigin() {\n    if (evCache.length < 2) return {\n      x: null,\n      y: null\n    };\n    const box = gesture.imageEl.getBoundingClientRect();\n    return [(evCache[0].pageX + (evCache[1].pageX - evCache[0].pageX) / 2 - box.x) / currentScale, (evCache[0].pageY + (evCache[1].pageY - evCache[0].pageY) / 2 - box.y) / currentScale];\n  }\n  function getSlideSelector() {\n    return swiper.isElement ? `swiper-slide` : `.${swiper.params.slideClass}`;\n  }\n  function eventWithinSlide(e) {\n    const slideSelector = getSlideSelector();\n    if (e.target.matches(slideSelector)) return true;\n    if (swiper.slides.filter(slideEl => slideEl.contains(e.target)).length > 0) return true;\n    return false;\n  }\n  function eventWithinZoomContainer(e) {\n    const selector = `.${swiper.params.zoom.containerClass}`;\n    if (e.target.matches(selector)) return true;\n    if ([...swiper.el.querySelectorAll(selector)].filter(containerEl => containerEl.contains(e.target)).length > 0) return true;\n    return false;\n  }\n\n  // Events\n  function onGestureStart(e) {\n    if (e.pointerType === 'mouse') {\n      evCache.splice(0, evCache.length);\n    }\n    if (!eventWithinSlide(e)) return;\n    const params = swiper.params.zoom;\n    fakeGestureTouched = false;\n    fakeGestureMoved = false;\n    evCache.push(e);\n    if (evCache.length < 2) {\n      return;\n    }\n    fakeGestureTouched = true;\n    gesture.scaleStart = getDistanceBetweenTouches();\n    if (!gesture.slideEl) {\n      gesture.slideEl = e.target.closest(`.${swiper.params.slideClass}, swiper-slide`);\n      if (!gesture.slideEl) gesture.slideEl = swiper.slides[swiper.activeIndex];\n      let imageEl = gesture.slideEl.querySelector(`.${params.containerClass}`);\n      if (imageEl) {\n        imageEl = imageEl.querySelectorAll('picture, img, svg, canvas, .swiper-zoom-target')[0];\n      }\n      gesture.imageEl = imageEl;\n      if (imageEl) {\n        gesture.imageWrapEl = elementParents(gesture.imageEl, `.${params.containerClass}`)[0];\n      } else {\n        gesture.imageWrapEl = undefined;\n      }\n      if (!gesture.imageWrapEl) {\n        gesture.imageEl = undefined;\n        return;\n      }\n      gesture.maxRatio = gesture.imageWrapEl.getAttribute('data-swiper-zoom') || params.maxRatio;\n    }\n    if (gesture.imageEl) {\n      const [originX, originY] = getScaleOrigin();\n      gesture.originX = originX;\n      gesture.originY = originY;\n      gesture.imageEl.style.transitionDuration = '0ms';\n    }\n    isScaling = true;\n  }\n  function onGestureChange(e) {\n    if (!eventWithinSlide(e)) return;\n    const params = swiper.params.zoom;\n    const zoom = swiper.zoom;\n    const pointerIndex = evCache.findIndex(cachedEv => cachedEv.pointerId === e.pointerId);\n    if (pointerIndex >= 0) evCache[pointerIndex] = e;\n    if (evCache.length < 2) {\n      return;\n    }\n    fakeGestureMoved = true;\n    gesture.scaleMove = getDistanceBetweenTouches();\n    if (!gesture.imageEl) {\n      return;\n    }\n    zoom.scale = gesture.scaleMove / gesture.scaleStart * currentScale;\n    if (zoom.scale > gesture.maxRatio) {\n      zoom.scale = gesture.maxRatio - 1 + (zoom.scale - gesture.maxRatio + 1) ** 0.5;\n    }\n    if (zoom.scale < params.minRatio) {\n      zoom.scale = params.minRatio + 1 - (params.minRatio - zoom.scale + 1) ** 0.5;\n    }\n    gesture.imageEl.style.transform = `translate3d(0,0,0) scale(${zoom.scale})`;\n  }\n  function onGestureEnd(e) {\n    if (!eventWithinSlide(e)) return;\n    if (e.pointerType === 'mouse' && e.type === 'pointerout') return;\n    const params = swiper.params.zoom;\n    const zoom = swiper.zoom;\n    const pointerIndex = evCache.findIndex(cachedEv => cachedEv.pointerId === e.pointerId);\n    if (pointerIndex >= 0) evCache.splice(pointerIndex, 1);\n    if (!fakeGestureTouched || !fakeGestureMoved) {\n      return;\n    }\n    fakeGestureTouched = false;\n    fakeGestureMoved = false;\n    if (!gesture.imageEl) return;\n    zoom.scale = Math.max(Math.min(zoom.scale, gesture.maxRatio), params.minRatio);\n    gesture.imageEl.style.transitionDuration = `${swiper.params.speed}ms`;\n    gesture.imageEl.style.transform = `translate3d(0,0,0) scale(${zoom.scale})`;\n    currentScale = zoom.scale;\n    isScaling = false;\n    if (zoom.scale > 1 && gesture.slideEl) {\n      gesture.slideEl.classList.add(`${params.zoomedSlideClass}`);\n    } else if (zoom.scale <= 1 && gesture.slideEl) {\n      gesture.slideEl.classList.remove(`${params.zoomedSlideClass}`);\n    }\n    if (zoom.scale === 1) {\n      gesture.originX = 0;\n      gesture.originY = 0;\n      gesture.slideEl = undefined;\n    }\n  }\n  function onTouchStart(e) {\n    const device = swiper.device;\n    if (!gesture.imageEl) return;\n    if (image.isTouched) return;\n    if (device.android && e.cancelable) e.preventDefault();\n    image.isTouched = true;\n    const event = evCache.length > 0 ? evCache[0] : e;\n    image.touchesStart.x = event.pageX;\n    image.touchesStart.y = event.pageY;\n  }\n  function onTouchMove(e) {\n    if (!eventWithinSlide(e) || !eventWithinZoomContainer(e)) return;\n    const zoom = swiper.zoom;\n    if (!gesture.imageEl) return;\n    if (!image.isTouched || !gesture.slideEl) return;\n    if (!image.isMoved) {\n      image.width = gesture.imageEl.offsetWidth;\n      image.height = gesture.imageEl.offsetHeight;\n      image.startX = getTranslate(gesture.imageWrapEl, 'x') || 0;\n      image.startY = getTranslate(gesture.imageWrapEl, 'y') || 0;\n      gesture.slideWidth = gesture.slideEl.offsetWidth;\n      gesture.slideHeight = gesture.slideEl.offsetHeight;\n      gesture.imageWrapEl.style.transitionDuration = '0ms';\n    }\n    // Define if we need image drag\n    const scaledWidth = image.width * zoom.scale;\n    const scaledHeight = image.height * zoom.scale;\n    if (scaledWidth < gesture.slideWidth && scaledHeight < gesture.slideHeight) return;\n    image.minX = Math.min(gesture.slideWidth / 2 - scaledWidth / 2, 0);\n    image.maxX = -image.minX;\n    image.minY = Math.min(gesture.slideHeight / 2 - scaledHeight / 2, 0);\n    image.maxY = -image.minY;\n    image.touchesCurrent.x = evCache.length > 0 ? evCache[0].pageX : e.pageX;\n    image.touchesCurrent.y = evCache.length > 0 ? evCache[0].pageY : e.pageY;\n    const touchesDiff = Math.max(Math.abs(image.touchesCurrent.x - image.touchesStart.x), Math.abs(image.touchesCurrent.y - image.touchesStart.y));\n    if (touchesDiff > 5) {\n      swiper.allowClick = false;\n    }\n    if (!image.isMoved && !isScaling) {\n      if (swiper.isHorizontal() && (Math.floor(image.minX) === Math.floor(image.startX) && image.touchesCurrent.x < image.touchesStart.x || Math.floor(image.maxX) === Math.floor(image.startX) && image.touchesCurrent.x > image.touchesStart.x)) {\n        image.isTouched = false;\n        return;\n      }\n      if (!swiper.isHorizontal() && (Math.floor(image.minY) === Math.floor(image.startY) && image.touchesCurrent.y < image.touchesStart.y || Math.floor(image.maxY) === Math.floor(image.startY) && image.touchesCurrent.y > image.touchesStart.y)) {\n        image.isTouched = false;\n        return;\n      }\n    }\n    if (e.cancelable) {\n      e.preventDefault();\n    }\n    e.stopPropagation();\n    image.isMoved = true;\n    const scaleRatio = (zoom.scale - currentScale) / (gesture.maxRatio - swiper.params.zoom.minRatio);\n    const {\n      originX,\n      originY\n    } = gesture;\n    image.currentX = image.touchesCurrent.x - image.touchesStart.x + image.startX + scaleRatio * (image.width - originX * 2);\n    image.currentY = image.touchesCurrent.y - image.touchesStart.y + image.startY + scaleRatio * (image.height - originY * 2);\n    if (image.currentX < image.minX) {\n      image.currentX = image.minX + 1 - (image.minX - image.currentX + 1) ** 0.8;\n    }\n    if (image.currentX > image.maxX) {\n      image.currentX = image.maxX - 1 + (image.currentX - image.maxX + 1) ** 0.8;\n    }\n    if (image.currentY < image.minY) {\n      image.currentY = image.minY + 1 - (image.minY - image.currentY + 1) ** 0.8;\n    }\n    if (image.currentY > image.maxY) {\n      image.currentY = image.maxY - 1 + (image.currentY - image.maxY + 1) ** 0.8;\n    }\n\n    // Velocity\n    if (!velocity.prevPositionX) velocity.prevPositionX = image.touchesCurrent.x;\n    if (!velocity.prevPositionY) velocity.prevPositionY = image.touchesCurrent.y;\n    if (!velocity.prevTime) velocity.prevTime = Date.now();\n    velocity.x = (image.touchesCurrent.x - velocity.prevPositionX) / (Date.now() - velocity.prevTime) / 2;\n    velocity.y = (image.touchesCurrent.y - velocity.prevPositionY) / (Date.now() - velocity.prevTime) / 2;\n    if (Math.abs(image.touchesCurrent.x - velocity.prevPositionX) < 2) velocity.x = 0;\n    if (Math.abs(image.touchesCurrent.y - velocity.prevPositionY) < 2) velocity.y = 0;\n    velocity.prevPositionX = image.touchesCurrent.x;\n    velocity.prevPositionY = image.touchesCurrent.y;\n    velocity.prevTime = Date.now();\n    gesture.imageWrapEl.style.transform = `translate3d(${image.currentX}px, ${image.currentY}px,0)`;\n  }\n  function onTouchEnd() {\n    const zoom = swiper.zoom;\n    if (!gesture.imageEl) return;\n    if (!image.isTouched || !image.isMoved) {\n      image.isTouched = false;\n      image.isMoved = false;\n      return;\n    }\n    image.isTouched = false;\n    image.isMoved = false;\n    let momentumDurationX = 300;\n    let momentumDurationY = 300;\n    const momentumDistanceX = velocity.x * momentumDurationX;\n    const newPositionX = image.currentX + momentumDistanceX;\n    const momentumDistanceY = velocity.y * momentumDurationY;\n    const newPositionY = image.currentY + momentumDistanceY;\n\n    // Fix duration\n    if (velocity.x !== 0) momentumDurationX = Math.abs((newPositionX - image.currentX) / velocity.x);\n    if (velocity.y !== 0) momentumDurationY = Math.abs((newPositionY - image.currentY) / velocity.y);\n    const momentumDuration = Math.max(momentumDurationX, momentumDurationY);\n    image.currentX = newPositionX;\n    image.currentY = newPositionY;\n    // Define if we need image drag\n    const scaledWidth = image.width * zoom.scale;\n    const scaledHeight = image.height * zoom.scale;\n    image.minX = Math.min(gesture.slideWidth / 2 - scaledWidth / 2, 0);\n    image.maxX = -image.minX;\n    image.minY = Math.min(gesture.slideHeight / 2 - scaledHeight / 2, 0);\n    image.maxY = -image.minY;\n    image.currentX = Math.max(Math.min(image.currentX, image.maxX), image.minX);\n    image.currentY = Math.max(Math.min(image.currentY, image.maxY), image.minY);\n    gesture.imageWrapEl.style.transitionDuration = `${momentumDuration}ms`;\n    gesture.imageWrapEl.style.transform = `translate3d(${image.currentX}px, ${image.currentY}px,0)`;\n  }\n  function onTransitionEnd() {\n    const zoom = swiper.zoom;\n    if (gesture.slideEl && swiper.activeIndex !== swiper.slides.indexOf(gesture.slideEl)) {\n      if (gesture.imageEl) {\n        gesture.imageEl.style.transform = 'translate3d(0,0,0) scale(1)';\n      }\n      if (gesture.imageWrapEl) {\n        gesture.imageWrapEl.style.transform = 'translate3d(0,0,0)';\n      }\n      gesture.slideEl.classList.remove(`${swiper.params.zoom.zoomedSlideClass}`);\n      zoom.scale = 1;\n      currentScale = 1;\n      gesture.slideEl = undefined;\n      gesture.imageEl = undefined;\n      gesture.imageWrapEl = undefined;\n      gesture.originX = 0;\n      gesture.originY = 0;\n    }\n  }\n  function zoomIn(e) {\n    const zoom = swiper.zoom;\n    const params = swiper.params.zoom;\n    if (!gesture.slideEl) {\n      if (e && e.target) {\n        gesture.slideEl = e.target.closest(`.${swiper.params.slideClass}, swiper-slide`);\n      }\n      if (!gesture.slideEl) {\n        if (swiper.params.virtual && swiper.params.virtual.enabled && swiper.virtual) {\n          gesture.slideEl = elementChildren(swiper.slidesEl, `.${swiper.params.slideActiveClass}`)[0];\n        } else {\n          gesture.slideEl = swiper.slides[swiper.activeIndex];\n        }\n      }\n      let imageEl = gesture.slideEl.querySelector(`.${params.containerClass}`);\n      if (imageEl) {\n        imageEl = imageEl.querySelectorAll('picture, img, svg, canvas, .swiper-zoom-target')[0];\n      }\n      gesture.imageEl = imageEl;\n      if (imageEl) {\n        gesture.imageWrapEl = elementParents(gesture.imageEl, `.${params.containerClass}`)[0];\n      } else {\n        gesture.imageWrapEl = undefined;\n      }\n    }\n    if (!gesture.imageEl || !gesture.imageWrapEl) return;\n    if (swiper.params.cssMode) {\n      swiper.wrapperEl.style.overflow = 'hidden';\n      swiper.wrapperEl.style.touchAction = 'none';\n    }\n    gesture.slideEl.classList.add(`${params.zoomedSlideClass}`);\n    let touchX;\n    let touchY;\n    let offsetX;\n    let offsetY;\n    let diffX;\n    let diffY;\n    let translateX;\n    let translateY;\n    let imageWidth;\n    let imageHeight;\n    let scaledWidth;\n    let scaledHeight;\n    let translateMinX;\n    let translateMinY;\n    let translateMaxX;\n    let translateMaxY;\n    let slideWidth;\n    let slideHeight;\n    if (typeof image.touchesStart.x === 'undefined' && e) {\n      touchX = e.pageX;\n      touchY = e.pageY;\n    } else {\n      touchX = image.touchesStart.x;\n      touchY = image.touchesStart.y;\n    }\n    const forceZoomRatio = typeof e === 'number' ? e : null;\n    if (currentScale === 1 && forceZoomRatio) {\n      touchX = undefined;\n      touchY = undefined;\n    }\n    zoom.scale = forceZoomRatio || gesture.imageWrapEl.getAttribute('data-swiper-zoom') || params.maxRatio;\n    currentScale = forceZoomRatio || gesture.imageWrapEl.getAttribute('data-swiper-zoom') || params.maxRatio;\n    if (e && !(currentScale === 1 && forceZoomRatio)) {\n      slideWidth = gesture.slideEl.offsetWidth;\n      slideHeight = gesture.slideEl.offsetHeight;\n      offsetX = elementOffset(gesture.slideEl).left + window.scrollX;\n      offsetY = elementOffset(gesture.slideEl).top + window.scrollY;\n      diffX = offsetX + slideWidth / 2 - touchX;\n      diffY = offsetY + slideHeight / 2 - touchY;\n      imageWidth = gesture.imageEl.offsetWidth;\n      imageHeight = gesture.imageEl.offsetHeight;\n      scaledWidth = imageWidth * zoom.scale;\n      scaledHeight = imageHeight * zoom.scale;\n      translateMinX = Math.min(slideWidth / 2 - scaledWidth / 2, 0);\n      translateMinY = Math.min(slideHeight / 2 - scaledHeight / 2, 0);\n      translateMaxX = -translateMinX;\n      translateMaxY = -translateMinY;\n      translateX = diffX * zoom.scale;\n      translateY = diffY * zoom.scale;\n      if (translateX < translateMinX) {\n        translateX = translateMinX;\n      }\n      if (translateX > translateMaxX) {\n        translateX = translateMaxX;\n      }\n      if (translateY < translateMinY) {\n        translateY = translateMinY;\n      }\n      if (translateY > translateMaxY) {\n        translateY = translateMaxY;\n      }\n    } else {\n      translateX = 0;\n      translateY = 0;\n    }\n    if (forceZoomRatio && zoom.scale === 1) {\n      gesture.originX = 0;\n      gesture.originY = 0;\n    }\n    gesture.imageWrapEl.style.transitionDuration = '300ms';\n    gesture.imageWrapEl.style.transform = `translate3d(${translateX}px, ${translateY}px,0)`;\n    gesture.imageEl.style.transitionDuration = '300ms';\n    gesture.imageEl.style.transform = `translate3d(0,0,0) scale(${zoom.scale})`;\n  }\n  function zoomOut() {\n    const zoom = swiper.zoom;\n    const params = swiper.params.zoom;\n    if (!gesture.slideEl) {\n      if (swiper.params.virtual && swiper.params.virtual.enabled && swiper.virtual) {\n        gesture.slideEl = elementChildren(swiper.slidesEl, `.${swiper.params.slideActiveClass}`)[0];\n      } else {\n        gesture.slideEl = swiper.slides[swiper.activeIndex];\n      }\n      let imageEl = gesture.slideEl.querySelector(`.${params.containerClass}`);\n      if (imageEl) {\n        imageEl = imageEl.querySelectorAll('picture, img, svg, canvas, .swiper-zoom-target')[0];\n      }\n      gesture.imageEl = imageEl;\n      if (imageEl) {\n        gesture.imageWrapEl = elementParents(gesture.imageEl, `.${params.containerClass}`)[0];\n      } else {\n        gesture.imageWrapEl = undefined;\n      }\n    }\n    if (!gesture.imageEl || !gesture.imageWrapEl) return;\n    if (swiper.params.cssMode) {\n      swiper.wrapperEl.style.overflow = '';\n      swiper.wrapperEl.style.touchAction = '';\n    }\n    zoom.scale = 1;\n    currentScale = 1;\n    gesture.imageWrapEl.style.transitionDuration = '300ms';\n    gesture.imageWrapEl.style.transform = 'translate3d(0,0,0)';\n    gesture.imageEl.style.transitionDuration = '300ms';\n    gesture.imageEl.style.transform = 'translate3d(0,0,0) scale(1)';\n    gesture.slideEl.classList.remove(`${params.zoomedSlideClass}`);\n    gesture.slideEl = undefined;\n    gesture.originX = 0;\n    gesture.originY = 0;\n  }\n\n  // Toggle Zoom\n  function zoomToggle(e) {\n    const zoom = swiper.zoom;\n    if (zoom.scale && zoom.scale !== 1) {\n      // Zoom Out\n      zoomOut();\n    } else {\n      // Zoom In\n      zoomIn(e);\n    }\n  }\n  function getListeners() {\n    const passiveListener = swiper.params.passiveListeners ? {\n      passive: true,\n      capture: false\n    } : false;\n    const activeListenerWithCapture = swiper.params.passiveListeners ? {\n      passive: false,\n      capture: true\n    } : true;\n    return {\n      passiveListener,\n      activeListenerWithCapture\n    };\n  }\n\n  // Attach/Detach Events\n  function enable() {\n    const zoom = swiper.zoom;\n    if (zoom.enabled) return;\n    zoom.enabled = true;\n    const {\n      passiveListener,\n      activeListenerWithCapture\n    } = getListeners();\n\n    // Scale image\n    swiper.wrapperEl.addEventListener('pointerdown', onGestureStart, passiveListener);\n    swiper.wrapperEl.addEventListener('pointermove', onGestureChange, activeListenerWithCapture);\n    ['pointerup', 'pointercancel', 'pointerout'].forEach(eventName => {\n      swiper.wrapperEl.addEventListener(eventName, onGestureEnd, passiveListener);\n    });\n\n    // Move image\n    swiper.wrapperEl.addEventListener('pointermove', onTouchMove, activeListenerWithCapture);\n  }\n  function disable() {\n    const zoom = swiper.zoom;\n    if (!zoom.enabled) return;\n    zoom.enabled = false;\n    const {\n      passiveListener,\n      activeListenerWithCapture\n    } = getListeners();\n\n    // Scale image\n    swiper.wrapperEl.removeEventListener('pointerdown', onGestureStart, passiveListener);\n    swiper.wrapperEl.removeEventListener('pointermove', onGestureChange, activeListenerWithCapture);\n    ['pointerup', 'pointercancel', 'pointerout'].forEach(eventName => {\n      swiper.wrapperEl.removeEventListener(eventName, onGestureEnd, passiveListener);\n    });\n\n    // Move image\n    swiper.wrapperEl.removeEventListener('pointermove', onTouchMove, activeListenerWithCapture);\n  }\n  on('init', () => {\n    if (swiper.params.zoom.enabled) {\n      enable();\n    }\n  });\n  on('destroy', () => {\n    disable();\n  });\n  on('touchStart', (_s, e) => {\n    if (!swiper.zoom.enabled) return;\n    onTouchStart(e);\n  });\n  on('touchEnd', (_s, e) => {\n    if (!swiper.zoom.enabled) return;\n    onTouchEnd(e);\n  });\n  on('doubleTap', (_s, e) => {\n    if (!swiper.animating && swiper.params.zoom.enabled && swiper.zoom.enabled && swiper.params.zoom.toggle) {\n      zoomToggle(e);\n    }\n  });\n  on('transitionEnd', () => {\n    if (swiper.zoom.enabled && swiper.params.zoom.enabled) {\n      onTransitionEnd();\n    }\n  });\n  on('slideChange', () => {\n    if (swiper.zoom.enabled && swiper.params.zoom.enabled && swiper.params.cssMode) {\n      onTransitionEnd();\n    }\n  });\n  Object.assign(swiper.zoom, {\n    enable,\n    disable,\n    in: zoomIn,\n    out: zoomOut,\n    toggle: zoomToggle\n  });\n}"], "mappings": "AAAA,SAASA,SAAS,QAAQ,YAAY;AACtC,SAASC,eAAe,EAAEC,aAAa,EAAEC,cAAc,EAAEC,YAAY,QAAQ,uBAAuB;AACpG,eAAe,SAASC,IAAIA,CAAAC,IAAA,EAKzB;EAAA,IAL0B;IAC3BC,MAAM;IACNC,YAAY;IACZC,EAAE;IACFC;EACF,CAAC,GAAAJ,IAAA;EACC,MAAMK,MAAM,GAAGX,SAAS,EAAE;EAC1BQ,YAAY,CAAC;IACXI,IAAI,EAAE;MACJC,OAAO,EAAE,KAAK;MACdC,QAAQ,EAAE,CAAC;MACXC,QAAQ,EAAE,CAAC;MACXC,MAAM,EAAE,IAAI;MACZC,cAAc,EAAE,uBAAuB;MACvCC,gBAAgB,EAAE;IACpB;EACF,CAAC,CAAC;EACFX,MAAM,CAACK,IAAI,GAAG;IACZC,OAAO,EAAE;EACX,CAAC;EACD,IAAIM,YAAY,GAAG,CAAC;EACpB,IAAIC,SAAS,GAAG,KAAK;EACrB,IAAIC,kBAAkB;EACtB,IAAIC,gBAAgB;EACpB,MAAMC,OAAO,GAAG,EAAE;EAClB,MAAMC,OAAO,GAAG;IACdC,OAAO,EAAE,CAAC;IACVC,OAAO,EAAE,CAAC;IACVC,OAAO,EAAEC,SAAS;IAClBC,UAAU,EAAED,SAAS;IACrBE,WAAW,EAAEF,SAAS;IACtBG,OAAO,EAAEH,SAAS;IAClBI,WAAW,EAAEJ,SAAS;IACtBd,QAAQ,EAAE;EACZ,CAAC;EACD,MAAMmB,KAAK,GAAG;IACZC,SAAS,EAAEN,SAAS;IACpBO,OAAO,EAAEP,SAAS;IAClBQ,QAAQ,EAAER,SAAS;IACnBS,QAAQ,EAAET,SAAS;IACnBU,IAAI,EAAEV,SAAS;IACfW,IAAI,EAAEX,SAAS;IACfY,IAAI,EAAEZ,SAAS;IACfa,IAAI,EAAEb,SAAS;IACfc,KAAK,EAAEd,SAAS;IAChBe,MAAM,EAAEf,SAAS;IACjBgB,MAAM,EAAEhB,SAAS;IACjBiB,MAAM,EAAEjB,SAAS;IACjBkB,YAAY,EAAE,CAAC,CAAC;IAChBC,cAAc,EAAE,CAAC;EACnB,CAAC;EACD,MAAMC,QAAQ,GAAG;IACfC,CAAC,EAAErB,SAAS;IACZsB,CAAC,EAAEtB,SAAS;IACZuB,aAAa,EAAEvB,SAAS;IACxBwB,aAAa,EAAExB,SAAS;IACxByB,QAAQ,EAAEzB;EACZ,CAAC;EACD,IAAI0B,KAAK,GAAG,CAAC;EACbC,MAAM,CAACC,cAAc,CAACjD,MAAM,CAACK,IAAI,EAAE,OAAO,EAAE;IAC1C6C,GAAGA,CAAA,EAAG;MACJ,OAAOH,KAAK;IACd,CAAC;IACDI,GAAGA,CAACC,KAAK,EAAE;MACT,IAAIL,KAAK,KAAKK,KAAK,EAAE;QACnB,MAAM5B,OAAO,GAAGP,OAAO,CAACO,OAAO;QAC/B,MAAMJ,OAAO,GAAGH,OAAO,CAACG,OAAO;QAC/BjB,IAAI,CAAC,YAAY,EAAEiD,KAAK,EAAE5B,OAAO,EAAEJ,OAAO,CAAC;MAC7C;MACA2B,KAAK,GAAGK,KAAK;IACf;EACF,CAAC,CAAC;EACF,SAASC,yBAAyBA,CAAA,EAAG;IACnC,IAAIrC,OAAO,CAACsC,MAAM,GAAG,CAAC,EAAE,OAAO,CAAC;IAChC,MAAMC,EAAE,GAAGvC,OAAO,CAAC,CAAC,CAAC,CAACwC,KAAK;IAC3B,MAAMC,EAAE,GAAGzC,OAAO,CAAC,CAAC,CAAC,CAAC0C,KAAK;IAC3B,MAAMC,EAAE,GAAG3C,OAAO,CAAC,CAAC,CAAC,CAACwC,KAAK;IAC3B,MAAMI,EAAE,GAAG5C,OAAO,CAAC,CAAC,CAAC,CAAC0C,KAAK;IAC3B,MAAMG,QAAQ,GAAGC,IAAI,CAACC,IAAI,CAAC,CAACJ,EAAE,GAAGJ,EAAE,KAAK,CAAC,GAAG,CAACK,EAAE,GAAGH,EAAE,KAAK,CAAC,CAAC;IAC3D,OAAOI,QAAQ;EACjB;EACA,SAASG,cAAcA,CAAA,EAAG;IACxB,IAAIhD,OAAO,CAACsC,MAAM,GAAG,CAAC,EAAE,OAAO;MAC7BZ,CAAC,EAAE,IAAI;MACPC,CAAC,EAAE;IACL,CAAC;IACD,MAAMsB,GAAG,GAAGhD,OAAO,CAACO,OAAO,CAAC0C,qBAAqB,EAAE;IACnD,OAAO,CAAC,CAAClD,OAAO,CAAC,CAAC,CAAC,CAACwC,KAAK,GAAG,CAACxC,OAAO,CAAC,CAAC,CAAC,CAACwC,KAAK,GAAGxC,OAAO,CAAC,CAAC,CAAC,CAACwC,KAAK,IAAI,CAAC,GAAGS,GAAG,CAACvB,CAAC,IAAI9B,YAAY,EAAE,CAACI,OAAO,CAAC,CAAC,CAAC,CAAC0C,KAAK,GAAG,CAAC1C,OAAO,CAAC,CAAC,CAAC,CAAC0C,KAAK,GAAG1C,OAAO,CAAC,CAAC,CAAC,CAAC0C,KAAK,IAAI,CAAC,GAAGO,GAAG,CAACtB,CAAC,IAAI/B,YAAY,CAAC;EACvL;EACA,SAASuD,gBAAgBA,CAAA,EAAG;IAC1B,OAAOnE,MAAM,CAACoE,SAAS,GAAI,cAAa,GAAI,IAAGpE,MAAM,CAACqE,MAAM,CAACC,UAAW,EAAC;EAC3E;EACA,SAASC,gBAAgBA,CAACC,CAAC,EAAE;IAC3B,MAAMC,aAAa,GAAGN,gBAAgB,EAAE;IACxC,IAAIK,CAAC,CAACE,MAAM,CAACC,OAAO,CAACF,aAAa,CAAC,EAAE,OAAO,IAAI;IAChD,IAAIzE,MAAM,CAAC4E,MAAM,CAACC,MAAM,CAACzD,OAAO,IAAIA,OAAO,CAAC0D,QAAQ,CAACN,CAAC,CAACE,MAAM,CAAC,CAAC,CAACpB,MAAM,GAAG,CAAC,EAAE,OAAO,IAAI;IACvF,OAAO,KAAK;EACd;EACA,SAASyB,wBAAwBA,CAACP,CAAC,EAAE;IACnC,MAAMQ,QAAQ,GAAI,IAAGhF,MAAM,CAACqE,MAAM,CAAChE,IAAI,CAACK,cAAe,EAAC;IACxD,IAAI8D,CAAC,CAACE,MAAM,CAACC,OAAO,CAACK,QAAQ,CAAC,EAAE,OAAO,IAAI;IAC3C,IAAI,CAAC,GAAGhF,MAAM,CAACiF,EAAE,CAACC,gBAAgB,CAACF,QAAQ,CAAC,CAAC,CAACH,MAAM,CAACM,WAAW,IAAIA,WAAW,CAACL,QAAQ,CAACN,CAAC,CAACE,MAAM,CAAC,CAAC,CAACpB,MAAM,GAAG,CAAC,EAAE,OAAO,IAAI;IAC3H,OAAO,KAAK;EACd;;EAEA;EACA,SAAS8B,cAAcA,CAACZ,CAAC,EAAE;IACzB,IAAIA,CAAC,CAACa,WAAW,KAAK,OAAO,EAAE;MAC7BrE,OAAO,CAACsE,MAAM,CAAC,CAAC,EAAEtE,OAAO,CAACsC,MAAM,CAAC;IACnC;IACA,IAAI,CAACiB,gBAAgB,CAACC,CAAC,CAAC,EAAE;IAC1B,MAAMH,MAAM,GAAGrE,MAAM,CAACqE,MAAM,CAAChE,IAAI;IACjCS,kBAAkB,GAAG,KAAK;IAC1BC,gBAAgB,GAAG,KAAK;IACxBC,OAAO,CAACuE,IAAI,CAACf,CAAC,CAAC;IACf,IAAIxD,OAAO,CAACsC,MAAM,GAAG,CAAC,EAAE;MACtB;IACF;IACAxC,kBAAkB,GAAG,IAAI;IACzBG,OAAO,CAACuE,UAAU,GAAGnC,yBAAyB,EAAE;IAChD,IAAI,CAACpC,OAAO,CAACG,OAAO,EAAE;MACpBH,OAAO,CAACG,OAAO,GAAGoD,CAAC,CAACE,MAAM,CAACe,OAAO,CAAE,IAAGzF,MAAM,CAACqE,MAAM,CAACC,UAAW,gBAAe,CAAC;MAChF,IAAI,CAACrD,OAAO,CAACG,OAAO,EAAEH,OAAO,CAACG,OAAO,GAAGpB,MAAM,CAAC4E,MAAM,CAAC5E,MAAM,CAAC0F,WAAW,CAAC;MACzE,IAAIlE,OAAO,GAAGP,OAAO,CAACG,OAAO,CAACuE,aAAa,CAAE,IAAGtB,MAAM,CAAC3D,cAAe,EAAC,CAAC;MACxE,IAAIc,OAAO,EAAE;QACXA,OAAO,GAAGA,OAAO,CAAC0D,gBAAgB,CAAC,gDAAgD,CAAC,CAAC,CAAC,CAAC;MACzF;MACAjE,OAAO,CAACO,OAAO,GAAGA,OAAO;MACzB,IAAIA,OAAO,EAAE;QACXP,OAAO,CAACQ,WAAW,GAAG7B,cAAc,CAACqB,OAAO,CAACO,OAAO,EAAG,IAAG6C,MAAM,CAAC3D,cAAe,EAAC,CAAC,CAAC,CAAC,CAAC;MACvF,CAAC,MAAM;QACLO,OAAO,CAACQ,WAAW,GAAGJ,SAAS;MACjC;MACA,IAAI,CAACJ,OAAO,CAACQ,WAAW,EAAE;QACxBR,OAAO,CAACO,OAAO,GAAGH,SAAS;QAC3B;MACF;MACAJ,OAAO,CAACV,QAAQ,GAAGU,OAAO,CAACQ,WAAW,CAACmE,YAAY,CAAC,kBAAkB,CAAC,IAAIvB,MAAM,CAAC9D,QAAQ;IAC5F;IACA,IAAIU,OAAO,CAACO,OAAO,EAAE;MACnB,MAAM,CAACN,OAAO,EAAEC,OAAO,CAAC,GAAG6C,cAAc,EAAE;MAC3C/C,OAAO,CAACC,OAAO,GAAGA,OAAO;MACzBD,OAAO,CAACE,OAAO,GAAGA,OAAO;MACzBF,OAAO,CAACO,OAAO,CAACqE,KAAK,CAACC,kBAAkB,GAAG,KAAK;IAClD;IACAjF,SAAS,GAAG,IAAI;EAClB;EACA,SAASkF,eAAeA,CAACvB,CAAC,EAAE;IAC1B,IAAI,CAACD,gBAAgB,CAACC,CAAC,CAAC,EAAE;IAC1B,MAAMH,MAAM,GAAGrE,MAAM,CAACqE,MAAM,CAAChE,IAAI;IACjC,MAAMA,IAAI,GAAGL,MAAM,CAACK,IAAI;IACxB,MAAM2F,YAAY,GAAGhF,OAAO,CAACiF,SAAS,CAACC,QAAQ,IAAIA,QAAQ,CAACC,SAAS,KAAK3B,CAAC,CAAC2B,SAAS,CAAC;IACtF,IAAIH,YAAY,IAAI,CAAC,EAAEhF,OAAO,CAACgF,YAAY,CAAC,GAAGxB,CAAC;IAChD,IAAIxD,OAAO,CAACsC,MAAM,GAAG,CAAC,EAAE;MACtB;IACF;IACAvC,gBAAgB,GAAG,IAAI;IACvBE,OAAO,CAACmF,SAAS,GAAG/C,yBAAyB,EAAE;IAC/C,IAAI,CAACpC,OAAO,CAACO,OAAO,EAAE;MACpB;IACF;IACAnB,IAAI,CAAC0C,KAAK,GAAG9B,OAAO,CAACmF,SAAS,GAAGnF,OAAO,CAACuE,UAAU,GAAG5E,YAAY;IAClE,IAAIP,IAAI,CAAC0C,KAAK,GAAG9B,OAAO,CAACV,QAAQ,EAAE;MACjCF,IAAI,CAAC0C,KAAK,GAAG9B,OAAO,CAACV,QAAQ,GAAG,CAAC,GAAG,CAACF,IAAI,CAAC0C,KAAK,GAAG9B,OAAO,CAACV,QAAQ,GAAG,CAAC,KAAK,GAAG;IAChF;IACA,IAAIF,IAAI,CAAC0C,KAAK,GAAGsB,MAAM,CAAC7D,QAAQ,EAAE;MAChCH,IAAI,CAAC0C,KAAK,GAAGsB,MAAM,CAAC7D,QAAQ,GAAG,CAAC,GAAG,CAAC6D,MAAM,CAAC7D,QAAQ,GAAGH,IAAI,CAAC0C,KAAK,GAAG,CAAC,KAAK,GAAG;IAC9E;IACA9B,OAAO,CAACO,OAAO,CAACqE,KAAK,CAACQ,SAAS,GAAI,4BAA2BhG,IAAI,CAAC0C,KAAM,GAAE;EAC7E;EACA,SAASuD,YAAYA,CAAC9B,CAAC,EAAE;IACvB,IAAI,CAACD,gBAAgB,CAACC,CAAC,CAAC,EAAE;IAC1B,IAAIA,CAAC,CAACa,WAAW,KAAK,OAAO,IAAIb,CAAC,CAAC+B,IAAI,KAAK,YAAY,EAAE;IAC1D,MAAMlC,MAAM,GAAGrE,MAAM,CAACqE,MAAM,CAAChE,IAAI;IACjC,MAAMA,IAAI,GAAGL,MAAM,CAACK,IAAI;IACxB,MAAM2F,YAAY,GAAGhF,OAAO,CAACiF,SAAS,CAACC,QAAQ,IAAIA,QAAQ,CAACC,SAAS,KAAK3B,CAAC,CAAC2B,SAAS,CAAC;IACtF,IAAIH,YAAY,IAAI,CAAC,EAAEhF,OAAO,CAACsE,MAAM,CAACU,YAAY,EAAE,CAAC,CAAC;IACtD,IAAI,CAAClF,kBAAkB,IAAI,CAACC,gBAAgB,EAAE;MAC5C;IACF;IACAD,kBAAkB,GAAG,KAAK;IAC1BC,gBAAgB,GAAG,KAAK;IACxB,IAAI,CAACE,OAAO,CAACO,OAAO,EAAE;IACtBnB,IAAI,CAAC0C,KAAK,GAAGe,IAAI,CAAC0C,GAAG,CAAC1C,IAAI,CAAC2C,GAAG,CAACpG,IAAI,CAAC0C,KAAK,EAAE9B,OAAO,CAACV,QAAQ,CAAC,EAAE8D,MAAM,CAAC7D,QAAQ,CAAC;IAC9ES,OAAO,CAACO,OAAO,CAACqE,KAAK,CAACC,kBAAkB,GAAI,GAAE9F,MAAM,CAACqE,MAAM,CAACqC,KAAM,IAAG;IACrEzF,OAAO,CAACO,OAAO,CAACqE,KAAK,CAACQ,SAAS,GAAI,4BAA2BhG,IAAI,CAAC0C,KAAM,GAAE;IAC3EnC,YAAY,GAAGP,IAAI,CAAC0C,KAAK;IACzBlC,SAAS,GAAG,KAAK;IACjB,IAAIR,IAAI,CAAC0C,KAAK,GAAG,CAAC,IAAI9B,OAAO,CAACG,OAAO,EAAE;MACrCH,OAAO,CAACG,OAAO,CAACuF,SAAS,CAACC,GAAG,CAAE,GAAEvC,MAAM,CAAC1D,gBAAiB,EAAC,CAAC;IAC7D,CAAC,MAAM,IAAIN,IAAI,CAAC0C,KAAK,IAAI,CAAC,IAAI9B,OAAO,CAACG,OAAO,EAAE;MAC7CH,OAAO,CAACG,OAAO,CAACuF,SAAS,CAACE,MAAM,CAAE,GAAExC,MAAM,CAAC1D,gBAAiB,EAAC,CAAC;IAChE;IACA,IAAIN,IAAI,CAAC0C,KAAK,KAAK,CAAC,EAAE;MACpB9B,OAAO,CAACC,OAAO,GAAG,CAAC;MACnBD,OAAO,CAACE,OAAO,GAAG,CAAC;MACnBF,OAAO,CAACG,OAAO,GAAGC,SAAS;IAC7B;EACF;EACA,SAASyF,YAAYA,CAACtC,CAAC,EAAE;IACvB,MAAMuC,MAAM,GAAG/G,MAAM,CAAC+G,MAAM;IAC5B,IAAI,CAAC9F,OAAO,CAACO,OAAO,EAAE;IACtB,IAAIE,KAAK,CAACC,SAAS,EAAE;IACrB,IAAIoF,MAAM,CAACC,OAAO,IAAIxC,CAAC,CAACyC,UAAU,EAAEzC,CAAC,CAAC0C,cAAc,EAAE;IACtDxF,KAAK,CAACC,SAAS,GAAG,IAAI;IACtB,MAAMwF,KAAK,GAAGnG,OAAO,CAACsC,MAAM,GAAG,CAAC,GAAGtC,OAAO,CAAC,CAAC,CAAC,GAAGwD,CAAC;IACjD9C,KAAK,CAACa,YAAY,CAACG,CAAC,GAAGyE,KAAK,CAAC3D,KAAK;IAClC9B,KAAK,CAACa,YAAY,CAACI,CAAC,GAAGwE,KAAK,CAACzD,KAAK;EACpC;EACA,SAAS0D,WAAWA,CAAC5C,CAAC,EAAE;IACtB,IAAI,CAACD,gBAAgB,CAACC,CAAC,CAAC,IAAI,CAACO,wBAAwB,CAACP,CAAC,CAAC,EAAE;IAC1D,MAAMnE,IAAI,GAAGL,MAAM,CAACK,IAAI;IACxB,IAAI,CAACY,OAAO,CAACO,OAAO,EAAE;IACtB,IAAI,CAACE,KAAK,CAACC,SAAS,IAAI,CAACV,OAAO,CAACG,OAAO,EAAE;IAC1C,IAAI,CAACM,KAAK,CAACE,OAAO,EAAE;MAClBF,KAAK,CAACS,KAAK,GAAGlB,OAAO,CAACO,OAAO,CAAC6F,WAAW;MACzC3F,KAAK,CAACU,MAAM,GAAGnB,OAAO,CAACO,OAAO,CAAC8F,YAAY;MAC3C5F,KAAK,CAACW,MAAM,GAAGxC,YAAY,CAACoB,OAAO,CAACQ,WAAW,EAAE,GAAG,CAAC,IAAI,CAAC;MAC1DC,KAAK,CAACY,MAAM,GAAGzC,YAAY,CAACoB,OAAO,CAACQ,WAAW,EAAE,GAAG,CAAC,IAAI,CAAC;MAC1DR,OAAO,CAACK,UAAU,GAAGL,OAAO,CAACG,OAAO,CAACiG,WAAW;MAChDpG,OAAO,CAACM,WAAW,GAAGN,OAAO,CAACG,OAAO,CAACkG,YAAY;MAClDrG,OAAO,CAACQ,WAAW,CAACoE,KAAK,CAACC,kBAAkB,GAAG,KAAK;IACtD;IACA;IACA,MAAMyB,WAAW,GAAG7F,KAAK,CAACS,KAAK,GAAG9B,IAAI,CAAC0C,KAAK;IAC5C,MAAMyE,YAAY,GAAG9F,KAAK,CAACU,MAAM,GAAG/B,IAAI,CAAC0C,KAAK;IAC9C,IAAIwE,WAAW,GAAGtG,OAAO,CAACK,UAAU,IAAIkG,YAAY,GAAGvG,OAAO,CAACM,WAAW,EAAE;IAC5EG,KAAK,CAACK,IAAI,GAAG+B,IAAI,CAAC2C,GAAG,CAACxF,OAAO,CAACK,UAAU,GAAG,CAAC,GAAGiG,WAAW,GAAG,CAAC,EAAE,CAAC,CAAC;IAClE7F,KAAK,CAACO,IAAI,GAAG,CAACP,KAAK,CAACK,IAAI;IACxBL,KAAK,CAACM,IAAI,GAAG8B,IAAI,CAAC2C,GAAG,CAACxF,OAAO,CAACM,WAAW,GAAG,CAAC,GAAGiG,YAAY,GAAG,CAAC,EAAE,CAAC,CAAC;IACpE9F,KAAK,CAACQ,IAAI,GAAG,CAACR,KAAK,CAACM,IAAI;IACxBN,KAAK,CAACc,cAAc,CAACE,CAAC,GAAG1B,OAAO,CAACsC,MAAM,GAAG,CAAC,GAAGtC,OAAO,CAAC,CAAC,CAAC,CAACwC,KAAK,GAAGgB,CAAC,CAAChB,KAAK;IACxE9B,KAAK,CAACc,cAAc,CAACG,CAAC,GAAG3B,OAAO,CAACsC,MAAM,GAAG,CAAC,GAAGtC,OAAO,CAAC,CAAC,CAAC,CAAC0C,KAAK,GAAGc,CAAC,CAACd,KAAK;IACxE,MAAM+D,WAAW,GAAG3D,IAAI,CAAC0C,GAAG,CAAC1C,IAAI,CAAC4D,GAAG,CAAChG,KAAK,CAACc,cAAc,CAACE,CAAC,GAAGhB,KAAK,CAACa,YAAY,CAACG,CAAC,CAAC,EAAEoB,IAAI,CAAC4D,GAAG,CAAChG,KAAK,CAACc,cAAc,CAACG,CAAC,GAAGjB,KAAK,CAACa,YAAY,CAACI,CAAC,CAAC,CAAC;IAC9I,IAAI8E,WAAW,GAAG,CAAC,EAAE;MACnBzH,MAAM,CAAC2H,UAAU,GAAG,KAAK;IAC3B;IACA,IAAI,CAACjG,KAAK,CAACE,OAAO,IAAI,CAACf,SAAS,EAAE;MAChC,IAAIb,MAAM,CAAC4H,YAAY,EAAE,KAAK9D,IAAI,CAAC+D,KAAK,CAACnG,KAAK,CAACK,IAAI,CAAC,KAAK+B,IAAI,CAAC+D,KAAK,CAACnG,KAAK,CAACW,MAAM,CAAC,IAAIX,KAAK,CAACc,cAAc,CAACE,CAAC,GAAGhB,KAAK,CAACa,YAAY,CAACG,CAAC,IAAIoB,IAAI,CAAC+D,KAAK,CAACnG,KAAK,CAACO,IAAI,CAAC,KAAK6B,IAAI,CAAC+D,KAAK,CAACnG,KAAK,CAACW,MAAM,CAAC,IAAIX,KAAK,CAACc,cAAc,CAACE,CAAC,GAAGhB,KAAK,CAACa,YAAY,CAACG,CAAC,CAAC,EAAE;QAC3OhB,KAAK,CAACC,SAAS,GAAG,KAAK;QACvB;MACF;MACA,IAAI,CAAC3B,MAAM,CAAC4H,YAAY,EAAE,KAAK9D,IAAI,CAAC+D,KAAK,CAACnG,KAAK,CAACM,IAAI,CAAC,KAAK8B,IAAI,CAAC+D,KAAK,CAACnG,KAAK,CAACY,MAAM,CAAC,IAAIZ,KAAK,CAACc,cAAc,CAACG,CAAC,GAAGjB,KAAK,CAACa,YAAY,CAACI,CAAC,IAAImB,IAAI,CAAC+D,KAAK,CAACnG,KAAK,CAACQ,IAAI,CAAC,KAAK4B,IAAI,CAAC+D,KAAK,CAACnG,KAAK,CAACY,MAAM,CAAC,IAAIZ,KAAK,CAACc,cAAc,CAACG,CAAC,GAAGjB,KAAK,CAACa,YAAY,CAACI,CAAC,CAAC,EAAE;QAC5OjB,KAAK,CAACC,SAAS,GAAG,KAAK;QACvB;MACF;IACF;IACA,IAAI6C,CAAC,CAACyC,UAAU,EAAE;MAChBzC,CAAC,CAAC0C,cAAc,EAAE;IACpB;IACA1C,CAAC,CAACsD,eAAe,EAAE;IACnBpG,KAAK,CAACE,OAAO,GAAG,IAAI;IACpB,MAAMmG,UAAU,GAAG,CAAC1H,IAAI,CAAC0C,KAAK,GAAGnC,YAAY,KAAKK,OAAO,CAACV,QAAQ,GAAGP,MAAM,CAACqE,MAAM,CAAChE,IAAI,CAACG,QAAQ,CAAC;IACjG,MAAM;MACJU,OAAO;MACPC;IACF,CAAC,GAAGF,OAAO;IACXS,KAAK,CAACG,QAAQ,GAAGH,KAAK,CAACc,cAAc,CAACE,CAAC,GAAGhB,KAAK,CAACa,YAAY,CAACG,CAAC,GAAGhB,KAAK,CAACW,MAAM,GAAG0F,UAAU,IAAIrG,KAAK,CAACS,KAAK,GAAGjB,OAAO,GAAG,CAAC,CAAC;IACxHQ,KAAK,CAACI,QAAQ,GAAGJ,KAAK,CAACc,cAAc,CAACG,CAAC,GAAGjB,KAAK,CAACa,YAAY,CAACI,CAAC,GAAGjB,KAAK,CAACY,MAAM,GAAGyF,UAAU,IAAIrG,KAAK,CAACU,MAAM,GAAGjB,OAAO,GAAG,CAAC,CAAC;IACzH,IAAIO,KAAK,CAACG,QAAQ,GAAGH,KAAK,CAACK,IAAI,EAAE;MAC/BL,KAAK,CAACG,QAAQ,GAAGH,KAAK,CAACK,IAAI,GAAG,CAAC,GAAG,CAACL,KAAK,CAACK,IAAI,GAAGL,KAAK,CAACG,QAAQ,GAAG,CAAC,KAAK,GAAG;IAC5E;IACA,IAAIH,KAAK,CAACG,QAAQ,GAAGH,KAAK,CAACO,IAAI,EAAE;MAC/BP,KAAK,CAACG,QAAQ,GAAGH,KAAK,CAACO,IAAI,GAAG,CAAC,GAAG,CAACP,KAAK,CAACG,QAAQ,GAAGH,KAAK,CAACO,IAAI,GAAG,CAAC,KAAK,GAAG;IAC5E;IACA,IAAIP,KAAK,CAACI,QAAQ,GAAGJ,KAAK,CAACM,IAAI,EAAE;MAC/BN,KAAK,CAACI,QAAQ,GAAGJ,KAAK,CAACM,IAAI,GAAG,CAAC,GAAG,CAACN,KAAK,CAACM,IAAI,GAAGN,KAAK,CAACI,QAAQ,GAAG,CAAC,KAAK,GAAG;IAC5E;IACA,IAAIJ,KAAK,CAACI,QAAQ,GAAGJ,KAAK,CAACQ,IAAI,EAAE;MAC/BR,KAAK,CAACI,QAAQ,GAAGJ,KAAK,CAACQ,IAAI,GAAG,CAAC,GAAG,CAACR,KAAK,CAACI,QAAQ,GAAGJ,KAAK,CAACQ,IAAI,GAAG,CAAC,KAAK,GAAG;IAC5E;;IAEA;IACA,IAAI,CAACO,QAAQ,CAACG,aAAa,EAAEH,QAAQ,CAACG,aAAa,GAAGlB,KAAK,CAACc,cAAc,CAACE,CAAC;IAC5E,IAAI,CAACD,QAAQ,CAACI,aAAa,EAAEJ,QAAQ,CAACI,aAAa,GAAGnB,KAAK,CAACc,cAAc,CAACG,CAAC;IAC5E,IAAI,CAACF,QAAQ,CAACK,QAAQ,EAAEL,QAAQ,CAACK,QAAQ,GAAGkF,IAAI,CAACC,GAAG,EAAE;IACtDxF,QAAQ,CAACC,CAAC,GAAG,CAAChB,KAAK,CAACc,cAAc,CAACE,CAAC,GAAGD,QAAQ,CAACG,aAAa,KAAKoF,IAAI,CAACC,GAAG,EAAE,GAAGxF,QAAQ,CAACK,QAAQ,CAAC,GAAG,CAAC;IACrGL,QAAQ,CAACE,CAAC,GAAG,CAACjB,KAAK,CAACc,cAAc,CAACG,CAAC,GAAGF,QAAQ,CAACI,aAAa,KAAKmF,IAAI,CAACC,GAAG,EAAE,GAAGxF,QAAQ,CAACK,QAAQ,CAAC,GAAG,CAAC;IACrG,IAAIgB,IAAI,CAAC4D,GAAG,CAAChG,KAAK,CAACc,cAAc,CAACE,CAAC,GAAGD,QAAQ,CAACG,aAAa,CAAC,GAAG,CAAC,EAAEH,QAAQ,CAACC,CAAC,GAAG,CAAC;IACjF,IAAIoB,IAAI,CAAC4D,GAAG,CAAChG,KAAK,CAACc,cAAc,CAACG,CAAC,GAAGF,QAAQ,CAACI,aAAa,CAAC,GAAG,CAAC,EAAEJ,QAAQ,CAACE,CAAC,GAAG,CAAC;IACjFF,QAAQ,CAACG,aAAa,GAAGlB,KAAK,CAACc,cAAc,CAACE,CAAC;IAC/CD,QAAQ,CAACI,aAAa,GAAGnB,KAAK,CAACc,cAAc,CAACG,CAAC;IAC/CF,QAAQ,CAACK,QAAQ,GAAGkF,IAAI,CAACC,GAAG,EAAE;IAC9BhH,OAAO,CAACQ,WAAW,CAACoE,KAAK,CAACQ,SAAS,GAAI,eAAc3E,KAAK,CAACG,QAAS,OAAMH,KAAK,CAACI,QAAS,OAAM;EACjG;EACA,SAASoG,UAAUA,CAAA,EAAG;IACpB,MAAM7H,IAAI,GAAGL,MAAM,CAACK,IAAI;IACxB,IAAI,CAACY,OAAO,CAACO,OAAO,EAAE;IACtB,IAAI,CAACE,KAAK,CAACC,SAAS,IAAI,CAACD,KAAK,CAACE,OAAO,EAAE;MACtCF,KAAK,CAACC,SAAS,GAAG,KAAK;MACvBD,KAAK,CAACE,OAAO,GAAG,KAAK;MACrB;IACF;IACAF,KAAK,CAACC,SAAS,GAAG,KAAK;IACvBD,KAAK,CAACE,OAAO,GAAG,KAAK;IACrB,IAAIuG,iBAAiB,GAAG,GAAG;IAC3B,IAAIC,iBAAiB,GAAG,GAAG;IAC3B,MAAMC,iBAAiB,GAAG5F,QAAQ,CAACC,CAAC,GAAGyF,iBAAiB;IACxD,MAAMG,YAAY,GAAG5G,KAAK,CAACG,QAAQ,GAAGwG,iBAAiB;IACvD,MAAME,iBAAiB,GAAG9F,QAAQ,CAACE,CAAC,GAAGyF,iBAAiB;IACxD,MAAMI,YAAY,GAAG9G,KAAK,CAACI,QAAQ,GAAGyG,iBAAiB;;IAEvD;IACA,IAAI9F,QAAQ,CAACC,CAAC,KAAK,CAAC,EAAEyF,iBAAiB,GAAGrE,IAAI,CAAC4D,GAAG,CAAC,CAACY,YAAY,GAAG5G,KAAK,CAACG,QAAQ,IAAIY,QAAQ,CAACC,CAAC,CAAC;IAChG,IAAID,QAAQ,CAACE,CAAC,KAAK,CAAC,EAAEyF,iBAAiB,GAAGtE,IAAI,CAAC4D,GAAG,CAAC,CAACc,YAAY,GAAG9G,KAAK,CAACI,QAAQ,IAAIW,QAAQ,CAACE,CAAC,CAAC;IAChG,MAAM8F,gBAAgB,GAAG3E,IAAI,CAAC0C,GAAG,CAAC2B,iBAAiB,EAAEC,iBAAiB,CAAC;IACvE1G,KAAK,CAACG,QAAQ,GAAGyG,YAAY;IAC7B5G,KAAK,CAACI,QAAQ,GAAG0G,YAAY;IAC7B;IACA,MAAMjB,WAAW,GAAG7F,KAAK,CAACS,KAAK,GAAG9B,IAAI,CAAC0C,KAAK;IAC5C,MAAMyE,YAAY,GAAG9F,KAAK,CAACU,MAAM,GAAG/B,IAAI,CAAC0C,KAAK;IAC9CrB,KAAK,CAACK,IAAI,GAAG+B,IAAI,CAAC2C,GAAG,CAACxF,OAAO,CAACK,UAAU,GAAG,CAAC,GAAGiG,WAAW,GAAG,CAAC,EAAE,CAAC,CAAC;IAClE7F,KAAK,CAACO,IAAI,GAAG,CAACP,KAAK,CAACK,IAAI;IACxBL,KAAK,CAACM,IAAI,GAAG8B,IAAI,CAAC2C,GAAG,CAACxF,OAAO,CAACM,WAAW,GAAG,CAAC,GAAGiG,YAAY,GAAG,CAAC,EAAE,CAAC,CAAC;IACpE9F,KAAK,CAACQ,IAAI,GAAG,CAACR,KAAK,CAACM,IAAI;IACxBN,KAAK,CAACG,QAAQ,GAAGiC,IAAI,CAAC0C,GAAG,CAAC1C,IAAI,CAAC2C,GAAG,CAAC/E,KAAK,CAACG,QAAQ,EAAEH,KAAK,CAACO,IAAI,CAAC,EAAEP,KAAK,CAACK,IAAI,CAAC;IAC3EL,KAAK,CAACI,QAAQ,GAAGgC,IAAI,CAAC0C,GAAG,CAAC1C,IAAI,CAAC2C,GAAG,CAAC/E,KAAK,CAACI,QAAQ,EAAEJ,KAAK,CAACQ,IAAI,CAAC,EAAER,KAAK,CAACM,IAAI,CAAC;IAC3Ef,OAAO,CAACQ,WAAW,CAACoE,KAAK,CAACC,kBAAkB,GAAI,GAAE2C,gBAAiB,IAAG;IACtExH,OAAO,CAACQ,WAAW,CAACoE,KAAK,CAACQ,SAAS,GAAI,eAAc3E,KAAK,CAACG,QAAS,OAAMH,KAAK,CAACI,QAAS,OAAM;EACjG;EACA,SAAS4G,eAAeA,CAAA,EAAG;IACzB,MAAMrI,IAAI,GAAGL,MAAM,CAACK,IAAI;IACxB,IAAIY,OAAO,CAACG,OAAO,IAAIpB,MAAM,CAAC0F,WAAW,KAAK1F,MAAM,CAAC4E,MAAM,CAAC+D,OAAO,CAAC1H,OAAO,CAACG,OAAO,CAAC,EAAE;MACpF,IAAIH,OAAO,CAACO,OAAO,EAAE;QACnBP,OAAO,CAACO,OAAO,CAACqE,KAAK,CAACQ,SAAS,GAAG,6BAA6B;MACjE;MACA,IAAIpF,OAAO,CAACQ,WAAW,EAAE;QACvBR,OAAO,CAACQ,WAAW,CAACoE,KAAK,CAACQ,SAAS,GAAG,oBAAoB;MAC5D;MACApF,OAAO,CAACG,OAAO,CAACuF,SAAS,CAACE,MAAM,CAAE,GAAE7G,MAAM,CAACqE,MAAM,CAAChE,IAAI,CAACM,gBAAiB,EAAC,CAAC;MAC1EN,IAAI,CAAC0C,KAAK,GAAG,CAAC;MACdnC,YAAY,GAAG,CAAC;MAChBK,OAAO,CAACG,OAAO,GAAGC,SAAS;MAC3BJ,OAAO,CAACO,OAAO,GAAGH,SAAS;MAC3BJ,OAAO,CAACQ,WAAW,GAAGJ,SAAS;MAC/BJ,OAAO,CAACC,OAAO,GAAG,CAAC;MACnBD,OAAO,CAACE,OAAO,GAAG,CAAC;IACrB;EACF;EACA,SAASyH,MAAMA,CAACpE,CAAC,EAAE;IACjB,MAAMnE,IAAI,GAAGL,MAAM,CAACK,IAAI;IACxB,MAAMgE,MAAM,GAAGrE,MAAM,CAACqE,MAAM,CAAChE,IAAI;IACjC,IAAI,CAACY,OAAO,CAACG,OAAO,EAAE;MACpB,IAAIoD,CAAC,IAAIA,CAAC,CAACE,MAAM,EAAE;QACjBzD,OAAO,CAACG,OAAO,GAAGoD,CAAC,CAACE,MAAM,CAACe,OAAO,CAAE,IAAGzF,MAAM,CAACqE,MAAM,CAACC,UAAW,gBAAe,CAAC;MAClF;MACA,IAAI,CAACrD,OAAO,CAACG,OAAO,EAAE;QACpB,IAAIpB,MAAM,CAACqE,MAAM,CAACwE,OAAO,IAAI7I,MAAM,CAACqE,MAAM,CAACwE,OAAO,CAACvI,OAAO,IAAIN,MAAM,CAAC6I,OAAO,EAAE;UAC5E5H,OAAO,CAACG,OAAO,GAAG1B,eAAe,CAACM,MAAM,CAAC8I,QAAQ,EAAG,IAAG9I,MAAM,CAACqE,MAAM,CAAC0E,gBAAiB,EAAC,CAAC,CAAC,CAAC,CAAC;QAC7F,CAAC,MAAM;UACL9H,OAAO,CAACG,OAAO,GAAGpB,MAAM,CAAC4E,MAAM,CAAC5E,MAAM,CAAC0F,WAAW,CAAC;QACrD;MACF;MACA,IAAIlE,OAAO,GAAGP,OAAO,CAACG,OAAO,CAACuE,aAAa,CAAE,IAAGtB,MAAM,CAAC3D,cAAe,EAAC,CAAC;MACxE,IAAIc,OAAO,EAAE;QACXA,OAAO,GAAGA,OAAO,CAAC0D,gBAAgB,CAAC,gDAAgD,CAAC,CAAC,CAAC,CAAC;MACzF;MACAjE,OAAO,CAACO,OAAO,GAAGA,OAAO;MACzB,IAAIA,OAAO,EAAE;QACXP,OAAO,CAACQ,WAAW,GAAG7B,cAAc,CAACqB,OAAO,CAACO,OAAO,EAAG,IAAG6C,MAAM,CAAC3D,cAAe,EAAC,CAAC,CAAC,CAAC,CAAC;MACvF,CAAC,MAAM;QACLO,OAAO,CAACQ,WAAW,GAAGJ,SAAS;MACjC;IACF;IACA,IAAI,CAACJ,OAAO,CAACO,OAAO,IAAI,CAACP,OAAO,CAACQ,WAAW,EAAE;IAC9C,IAAIzB,MAAM,CAACqE,MAAM,CAAC2E,OAAO,EAAE;MACzBhJ,MAAM,CAACiJ,SAAS,CAACpD,KAAK,CAACqD,QAAQ,GAAG,QAAQ;MAC1ClJ,MAAM,CAACiJ,SAAS,CAACpD,KAAK,CAACsD,WAAW,GAAG,MAAM;IAC7C;IACAlI,OAAO,CAACG,OAAO,CAACuF,SAAS,CAACC,GAAG,CAAE,GAAEvC,MAAM,CAAC1D,gBAAiB,EAAC,CAAC;IAC3D,IAAIyI,MAAM;IACV,IAAIC,MAAM;IACV,IAAIC,OAAO;IACX,IAAIC,OAAO;IACX,IAAIC,KAAK;IACT,IAAIC,KAAK;IACT,IAAIC,UAAU;IACd,IAAIC,UAAU;IACd,IAAIC,UAAU;IACd,IAAIC,WAAW;IACf,IAAItC,WAAW;IACf,IAAIC,YAAY;IAChB,IAAIsC,aAAa;IACjB,IAAIC,aAAa;IACjB,IAAIC,aAAa;IACjB,IAAIC,aAAa;IACjB,IAAI3I,UAAU;IACd,IAAIC,WAAW;IACf,IAAI,OAAOG,KAAK,CAACa,YAAY,CAACG,CAAC,KAAK,WAAW,IAAI8B,CAAC,EAAE;MACpD4E,MAAM,GAAG5E,CAAC,CAAChB,KAAK;MAChB6F,MAAM,GAAG7E,CAAC,CAACd,KAAK;IAClB,CAAC,MAAM;MACL0F,MAAM,GAAG1H,KAAK,CAACa,YAAY,CAACG,CAAC;MAC7B2G,MAAM,GAAG3H,KAAK,CAACa,YAAY,CAACI,CAAC;IAC/B;IACA,MAAMuH,cAAc,GAAG,OAAO1F,CAAC,KAAK,QAAQ,GAAGA,CAAC,GAAG,IAAI;IACvD,IAAI5D,YAAY,KAAK,CAAC,IAAIsJ,cAAc,EAAE;MACxCd,MAAM,GAAG/H,SAAS;MAClBgI,MAAM,GAAGhI,SAAS;IACpB;IACAhB,IAAI,CAAC0C,KAAK,GAAGmH,cAAc,IAAIjJ,OAAO,CAACQ,WAAW,CAACmE,YAAY,CAAC,kBAAkB,CAAC,IAAIvB,MAAM,CAAC9D,QAAQ;IACtGK,YAAY,GAAGsJ,cAAc,IAAIjJ,OAAO,CAACQ,WAAW,CAACmE,YAAY,CAAC,kBAAkB,CAAC,IAAIvB,MAAM,CAAC9D,QAAQ;IACxG,IAAIiE,CAAC,IAAI,EAAE5D,YAAY,KAAK,CAAC,IAAIsJ,cAAc,CAAC,EAAE;MAChD5I,UAAU,GAAGL,OAAO,CAACG,OAAO,CAACiG,WAAW;MACxC9F,WAAW,GAAGN,OAAO,CAACG,OAAO,CAACkG,YAAY;MAC1CgC,OAAO,GAAG3J,aAAa,CAACsB,OAAO,CAACG,OAAO,CAAC,CAAC+I,IAAI,GAAG/J,MAAM,CAACgK,OAAO;MAC9Db,OAAO,GAAG5J,aAAa,CAACsB,OAAO,CAACG,OAAO,CAAC,CAACiJ,GAAG,GAAGjK,MAAM,CAACkK,OAAO;MAC7Dd,KAAK,GAAGF,OAAO,GAAGhI,UAAU,GAAG,CAAC,GAAG8H,MAAM;MACzCK,KAAK,GAAGF,OAAO,GAAGhI,WAAW,GAAG,CAAC,GAAG8H,MAAM;MAC1CO,UAAU,GAAG3I,OAAO,CAACO,OAAO,CAAC6F,WAAW;MACxCwC,WAAW,GAAG5I,OAAO,CAACO,OAAO,CAAC8F,YAAY;MAC1CC,WAAW,GAAGqC,UAAU,GAAGvJ,IAAI,CAAC0C,KAAK;MACrCyE,YAAY,GAAGqC,WAAW,GAAGxJ,IAAI,CAAC0C,KAAK;MACvC+G,aAAa,GAAGhG,IAAI,CAAC2C,GAAG,CAACnF,UAAU,GAAG,CAAC,GAAGiG,WAAW,GAAG,CAAC,EAAE,CAAC,CAAC;MAC7DwC,aAAa,GAAGjG,IAAI,CAAC2C,GAAG,CAAClF,WAAW,GAAG,CAAC,GAAGiG,YAAY,GAAG,CAAC,EAAE,CAAC,CAAC;MAC/DwC,aAAa,GAAG,CAACF,aAAa;MAC9BG,aAAa,GAAG,CAACF,aAAa;MAC9BL,UAAU,GAAGF,KAAK,GAAGnJ,IAAI,CAAC0C,KAAK;MAC/B4G,UAAU,GAAGF,KAAK,GAAGpJ,IAAI,CAAC0C,KAAK;MAC/B,IAAI2G,UAAU,GAAGI,aAAa,EAAE;QAC9BJ,UAAU,GAAGI,aAAa;MAC5B;MACA,IAAIJ,UAAU,GAAGM,aAAa,EAAE;QAC9BN,UAAU,GAAGM,aAAa;MAC5B;MACA,IAAIL,UAAU,GAAGI,aAAa,EAAE;QAC9BJ,UAAU,GAAGI,aAAa;MAC5B;MACA,IAAIJ,UAAU,GAAGM,aAAa,EAAE;QAC9BN,UAAU,GAAGM,aAAa;MAC5B;IACF,CAAC,MAAM;MACLP,UAAU,GAAG,CAAC;MACdC,UAAU,GAAG,CAAC;IAChB;IACA,IAAIO,cAAc,IAAI7J,IAAI,CAAC0C,KAAK,KAAK,CAAC,EAAE;MACtC9B,OAAO,CAACC,OAAO,GAAG,CAAC;MACnBD,OAAO,CAACE,OAAO,GAAG,CAAC;IACrB;IACAF,OAAO,CAACQ,WAAW,CAACoE,KAAK,CAACC,kBAAkB,GAAG,OAAO;IACtD7E,OAAO,CAACQ,WAAW,CAACoE,KAAK,CAACQ,SAAS,GAAI,eAAcqD,UAAW,OAAMC,UAAW,OAAM;IACvF1I,OAAO,CAACO,OAAO,CAACqE,KAAK,CAACC,kBAAkB,GAAG,OAAO;IAClD7E,OAAO,CAACO,OAAO,CAACqE,KAAK,CAACQ,SAAS,GAAI,4BAA2BhG,IAAI,CAAC0C,KAAM,GAAE;EAC7E;EACA,SAASwH,OAAOA,CAAA,EAAG;IACjB,MAAMlK,IAAI,GAAGL,MAAM,CAACK,IAAI;IACxB,MAAMgE,MAAM,GAAGrE,MAAM,CAACqE,MAAM,CAAChE,IAAI;IACjC,IAAI,CAACY,OAAO,CAACG,OAAO,EAAE;MACpB,IAAIpB,MAAM,CAACqE,MAAM,CAACwE,OAAO,IAAI7I,MAAM,CAACqE,MAAM,CAACwE,OAAO,CAACvI,OAAO,IAAIN,MAAM,CAAC6I,OAAO,EAAE;QAC5E5H,OAAO,CAACG,OAAO,GAAG1B,eAAe,CAACM,MAAM,CAAC8I,QAAQ,EAAG,IAAG9I,MAAM,CAACqE,MAAM,CAAC0E,gBAAiB,EAAC,CAAC,CAAC,CAAC,CAAC;MAC7F,CAAC,MAAM;QACL9H,OAAO,CAACG,OAAO,GAAGpB,MAAM,CAAC4E,MAAM,CAAC5E,MAAM,CAAC0F,WAAW,CAAC;MACrD;MACA,IAAIlE,OAAO,GAAGP,OAAO,CAACG,OAAO,CAACuE,aAAa,CAAE,IAAGtB,MAAM,CAAC3D,cAAe,EAAC,CAAC;MACxE,IAAIc,OAAO,EAAE;QACXA,OAAO,GAAGA,OAAO,CAAC0D,gBAAgB,CAAC,gDAAgD,CAAC,CAAC,CAAC,CAAC;MACzF;MACAjE,OAAO,CAACO,OAAO,GAAGA,OAAO;MACzB,IAAIA,OAAO,EAAE;QACXP,OAAO,CAACQ,WAAW,GAAG7B,cAAc,CAACqB,OAAO,CAACO,OAAO,EAAG,IAAG6C,MAAM,CAAC3D,cAAe,EAAC,CAAC,CAAC,CAAC,CAAC;MACvF,CAAC,MAAM;QACLO,OAAO,CAACQ,WAAW,GAAGJ,SAAS;MACjC;IACF;IACA,IAAI,CAACJ,OAAO,CAACO,OAAO,IAAI,CAACP,OAAO,CAACQ,WAAW,EAAE;IAC9C,IAAIzB,MAAM,CAACqE,MAAM,CAAC2E,OAAO,EAAE;MACzBhJ,MAAM,CAACiJ,SAAS,CAACpD,KAAK,CAACqD,QAAQ,GAAG,EAAE;MACpClJ,MAAM,CAACiJ,SAAS,CAACpD,KAAK,CAACsD,WAAW,GAAG,EAAE;IACzC;IACA9I,IAAI,CAAC0C,KAAK,GAAG,CAAC;IACdnC,YAAY,GAAG,CAAC;IAChBK,OAAO,CAACQ,WAAW,CAACoE,KAAK,CAACC,kBAAkB,GAAG,OAAO;IACtD7E,OAAO,CAACQ,WAAW,CAACoE,KAAK,CAACQ,SAAS,GAAG,oBAAoB;IAC1DpF,OAAO,CAACO,OAAO,CAACqE,KAAK,CAACC,kBAAkB,GAAG,OAAO;IAClD7E,OAAO,CAACO,OAAO,CAACqE,KAAK,CAACQ,SAAS,GAAG,6BAA6B;IAC/DpF,OAAO,CAACG,OAAO,CAACuF,SAAS,CAACE,MAAM,CAAE,GAAExC,MAAM,CAAC1D,gBAAiB,EAAC,CAAC;IAC9DM,OAAO,CAACG,OAAO,GAAGC,SAAS;IAC3BJ,OAAO,CAACC,OAAO,GAAG,CAAC;IACnBD,OAAO,CAACE,OAAO,GAAG,CAAC;EACrB;;EAEA;EACA,SAASqJ,UAAUA,CAAChG,CAAC,EAAE;IACrB,MAAMnE,IAAI,GAAGL,MAAM,CAACK,IAAI;IACxB,IAAIA,IAAI,CAAC0C,KAAK,IAAI1C,IAAI,CAAC0C,KAAK,KAAK,CAAC,EAAE;MAClC;MACAwH,OAAO,EAAE;IACX,CAAC,MAAM;MACL;MACA3B,MAAM,CAACpE,CAAC,CAAC;IACX;EACF;EACA,SAASiG,YAAYA,CAAA,EAAG;IACtB,MAAMC,eAAe,GAAG1K,MAAM,CAACqE,MAAM,CAACsG,gBAAgB,GAAG;MACvDC,OAAO,EAAE,IAAI;MACbC,OAAO,EAAE;IACX,CAAC,GAAG,KAAK;IACT,MAAMC,yBAAyB,GAAG9K,MAAM,CAACqE,MAAM,CAACsG,gBAAgB,GAAG;MACjEC,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE;IACX,CAAC,GAAG,IAAI;IACR,OAAO;MACLH,eAAe;MACfI;IACF,CAAC;EACH;;EAEA;EACA,SAASC,MAAMA,CAAA,EAAG;IAChB,MAAM1K,IAAI,GAAGL,MAAM,CAACK,IAAI;IACxB,IAAIA,IAAI,CAACC,OAAO,EAAE;IAClBD,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,MAAM;MACJoK,eAAe;MACfI;IACF,CAAC,GAAGL,YAAY,EAAE;;IAElB;IACAzK,MAAM,CAACiJ,SAAS,CAAC+B,gBAAgB,CAAC,aAAa,EAAE5F,cAAc,EAAEsF,eAAe,CAAC;IACjF1K,MAAM,CAACiJ,SAAS,CAAC+B,gBAAgB,CAAC,aAAa,EAAEjF,eAAe,EAAE+E,yBAAyB,CAAC;IAC5F,CAAC,WAAW,EAAE,eAAe,EAAE,YAAY,CAAC,CAACG,OAAO,CAACC,SAAS,IAAI;MAChElL,MAAM,CAACiJ,SAAS,CAAC+B,gBAAgB,CAACE,SAAS,EAAE5E,YAAY,EAAEoE,eAAe,CAAC;IAC7E,CAAC,CAAC;;IAEF;IACA1K,MAAM,CAACiJ,SAAS,CAAC+B,gBAAgB,CAAC,aAAa,EAAE5D,WAAW,EAAE0D,yBAAyB,CAAC;EAC1F;EACA,SAASK,OAAOA,CAAA,EAAG;IACjB,MAAM9K,IAAI,GAAGL,MAAM,CAACK,IAAI;IACxB,IAAI,CAACA,IAAI,CAACC,OAAO,EAAE;IACnBD,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,MAAM;MACJoK,eAAe;MACfI;IACF,CAAC,GAAGL,YAAY,EAAE;;IAElB;IACAzK,MAAM,CAACiJ,SAAS,CAACmC,mBAAmB,CAAC,aAAa,EAAEhG,cAAc,EAAEsF,eAAe,CAAC;IACpF1K,MAAM,CAACiJ,SAAS,CAACmC,mBAAmB,CAAC,aAAa,EAAErF,eAAe,EAAE+E,yBAAyB,CAAC;IAC/F,CAAC,WAAW,EAAE,eAAe,EAAE,YAAY,CAAC,CAACG,OAAO,CAACC,SAAS,IAAI;MAChElL,MAAM,CAACiJ,SAAS,CAACmC,mBAAmB,CAACF,SAAS,EAAE5E,YAAY,EAAEoE,eAAe,CAAC;IAChF,CAAC,CAAC;;IAEF;IACA1K,MAAM,CAACiJ,SAAS,CAACmC,mBAAmB,CAAC,aAAa,EAAEhE,WAAW,EAAE0D,yBAAyB,CAAC;EAC7F;EACA5K,EAAE,CAAC,MAAM,EAAE,MAAM;IACf,IAAIF,MAAM,CAACqE,MAAM,CAAChE,IAAI,CAACC,OAAO,EAAE;MAC9ByK,MAAM,EAAE;IACV;EACF,CAAC,CAAC;EACF7K,EAAE,CAAC,SAAS,EAAE,MAAM;IAClBiL,OAAO,EAAE;EACX,CAAC,CAAC;EACFjL,EAAE,CAAC,YAAY,EAAE,CAACmL,EAAE,EAAE7G,CAAC,KAAK;IAC1B,IAAI,CAACxE,MAAM,CAACK,IAAI,CAACC,OAAO,EAAE;IAC1BwG,YAAY,CAACtC,CAAC,CAAC;EACjB,CAAC,CAAC;EACFtE,EAAE,CAAC,UAAU,EAAE,CAACmL,EAAE,EAAE7G,CAAC,KAAK;IACxB,IAAI,CAACxE,MAAM,CAACK,IAAI,CAACC,OAAO,EAAE;IAC1B4H,UAAU,CAAC1D,CAAC,CAAC;EACf,CAAC,CAAC;EACFtE,EAAE,CAAC,WAAW,EAAE,CAACmL,EAAE,EAAE7G,CAAC,KAAK;IACzB,IAAI,CAACxE,MAAM,CAACsL,SAAS,IAAItL,MAAM,CAACqE,MAAM,CAAChE,IAAI,CAACC,OAAO,IAAIN,MAAM,CAACK,IAAI,CAACC,OAAO,IAAIN,MAAM,CAACqE,MAAM,CAAChE,IAAI,CAACI,MAAM,EAAE;MACvG+J,UAAU,CAAChG,CAAC,CAAC;IACf;EACF,CAAC,CAAC;EACFtE,EAAE,CAAC,eAAe,EAAE,MAAM;IACxB,IAAIF,MAAM,CAACK,IAAI,CAACC,OAAO,IAAIN,MAAM,CAACqE,MAAM,CAAChE,IAAI,CAACC,OAAO,EAAE;MACrDoI,eAAe,EAAE;IACnB;EACF,CAAC,CAAC;EACFxI,EAAE,CAAC,aAAa,EAAE,MAAM;IACtB,IAAIF,MAAM,CAACK,IAAI,CAACC,OAAO,IAAIN,MAAM,CAACqE,MAAM,CAAChE,IAAI,CAACC,OAAO,IAAIN,MAAM,CAACqE,MAAM,CAAC2E,OAAO,EAAE;MAC9EN,eAAe,EAAE;IACnB;EACF,CAAC,CAAC;EACF1F,MAAM,CAACuI,MAAM,CAACvL,MAAM,CAACK,IAAI,EAAE;IACzB0K,MAAM;IACNI,OAAO;IACPK,EAAE,EAAE5C,MAAM;IACV6C,GAAG,EAAElB,OAAO;IACZ9J,MAAM,EAAE+J;EACV,CAAC,CAAC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}