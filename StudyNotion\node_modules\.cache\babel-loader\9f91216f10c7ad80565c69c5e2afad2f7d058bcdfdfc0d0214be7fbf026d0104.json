{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nvar _slicedToArray = function () {\n  function sliceIterator(arr, i) {\n    var _arr = [];\n    var _n = true;\n    var _d = false;\n    var _e = undefined;\n    try {\n      for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) {\n        _arr.push(_s.value);\n        if (i && _arr.length === i) break;\n      }\n    } catch (err) {\n      _d = true;\n      _e = err;\n    } finally {\n      try {\n        if (!_n && _i[\"return\"]) _i[\"return\"]();\n      } finally {\n        if (_d) throw _e;\n      }\n    }\n    return _arr;\n  }\n  return function (arr, i) {\n    if (Array.isArray(arr)) {\n      return arr;\n    } else if (Symbol.iterator in Object(arr)) {\n      return sliceIterator(arr, i);\n    } else {\n      throw new TypeError(\"Invalid attempt to destructure non-iterable instance\");\n    }\n  };\n}();\nvar _react = require('react');\nvar _react2 = _interopRequireDefault(_react);\nvar _propTypes = require('prop-types');\nvar _propTypes2 = _interopRequireDefault(_propTypes);\nvar _useConfig3 = require('./hooks/useConfig');\nvar _useConfig4 = _interopRequireDefault(_useConfig3);\nvar _star = require('./star');\nvar _star2 = _interopRequireDefault(_star);\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nvar parentStyles = {\n  overflow: \"hidden\",\n  position: \"relative\"\n};\nfunction getHalfStarStyles(color, uniqueness) {\n  return '\\n    .react-stars-' + uniqueness + ':before {\\n      position: absolute;\\n      overflow: hidden;\\n      display: block;\\n      z-index: 1;\\n      top: 0; left: 0;\\n      width: 50%;\\n      content: attr(data-forhalf);\\n      color: ' + color + ';\\n  }';\n}\nfunction getHalfStarStyleForIcons(color) {\n  return '\\n          span.react-stars-half > * {\\n          color: ' + color + ';\\n      }';\n}\n;\nfunction ReactStars(props) {\n  var _useState = (0, _react.useState)(''),\n    _useState2 = _slicedToArray(_useState, 2),\n    uniqueness = _useState2[0],\n    setUniqueness = _useState2[1];\n  var _useState3 = (0, _react.useState)(0),\n    _useState4 = _slicedToArray(_useState3, 2),\n    currentValue = _useState4[0],\n    setCurrentValue = _useState4[1];\n  var _useState5 = (0, _react.useState)([]),\n    _useState6 = _slicedToArray(_useState5, 2),\n    stars = _useState6[0],\n    setStars = _useState6[1];\n  var _useState7 = (0, _react.useState)(false),\n    _useState8 = _slicedToArray(_useState7, 2),\n    isUsingIcons = _useState8[0],\n    setIsUsingIcons = _useState8[1];\n  var _useConfig = (0, _useConfig4.default)(props),\n    _useConfig2 = _slicedToArray(_useConfig, 2),\n    config = _useConfig2[0],\n    setConfig = _useConfig2[1];\n  var _useState9 = (0, _react.useState)(0),\n    _useState10 = _slicedToArray(_useState9, 2),\n    halfStarAt = _useState10[0],\n    setHalfStarAt = _useState10[1];\n  var _useState11 = (0, _react.useState)(false),\n    _useState12 = _slicedToArray(_useState11, 2),\n    halfStarHidden = _useState12[0],\n    setHalfStarHidden = _useState12[1];\n  var _useState13 = (0, _react.useState)(''),\n    _useState14 = _slicedToArray(_useState13, 2),\n    classNames = _useState14[0],\n    setClassNames = _useState14[1];\n  function iconsUsed(config) {\n    return !config.isHalf && config.emptyIcon && config.filledIcon || config.isHalf && config.emptyIcon && config.halfIcon && config.filledIcon;\n  }\n  function createUniqueness() {\n    setUniqueness((Math.random() + \"\").replace(\".\", \"\"));\n  }\n  (0, _react.useEffect)(function () {\n    addClassNames();\n    validateInitialValue(props.value, props.count);\n    setStars(getStars(props.value));\n    setConfig(props);\n    createUniqueness();\n    setIsUsingIcons(iconsUsed(props));\n    setHalfStarAt(Math.floor(props.value));\n    setHalfStarHidden(props.isHalf && props.value % 1 < 0.5);\n  }, []);\n  function validateInitialValue(value, count) {\n    if (value < 0 || value > count) {\n      setCurrentValue(0);\n    } else {\n      setCurrentValue(value);\n    }\n  }\n  function addClassNames() {\n    var reactStarsClass = 'react-stars';\n    setClassNames(props.classNames + (' ' + reactStarsClass));\n  }\n  function isDecimal(value) {\n    return value % 1 === 0;\n  }\n  function getRate() {\n    return config.isHalf ? Math.floor(currentValue) : Math.round(currentValue);\n  }\n  function getStars(activeCount) {\n    if (typeof activeCount === 'undefined') {\n      activeCount = getRate();\n    }\n    var stars = [];\n    for (var i = 0; i < config.count; i++) {\n      stars.push({\n        active: i <= activeCount - 1\n      });\n    }\n    return stars;\n  }\n  function mouseOver(event) {\n    if (!config.edit) return;\n    var index = Number(event.currentTarget.getAttribute('data-index'));\n    if (config.isHalf) {\n      var isAtHalf = moreThanHalf(event);\n      setHalfStarHidden(isAtHalf);\n      if (isAtHalf) index += 1;\n      setHalfStarAt(index);\n    } else {\n      index += 1;\n    }\n    updateStars(index);\n  }\n  function updateStars(index) {\n    var currentActive = stars.filter(function (x) {\n      return x.active;\n    });\n    if (index !== currentActive.length) {\n      setStars(getStars(index));\n    }\n  }\n  function moreThanHalf(event) {\n    var target = event.target;\n    var boundingClientRect = target.getBoundingClientRect();\n    var mouseAt = event.clientX - boundingClientRect.left;\n    mouseAt = Math.round(Math.abs(mouseAt));\n    return mouseAt > boundingClientRect.width / 2;\n  }\n  function mouseLeave() {\n    if (!config.edit) return;\n    updateHalfStarValues(currentValue);\n    setStars(getStars());\n  }\n  function updateHalfStarValues(value) {\n    if (config.isHalf) {\n      setHalfStarHidden(isDecimal(value));\n      setHalfStarAt(Math.floor(value));\n    }\n  }\n  function onClick(event) {\n    if (!config.edit) return;\n    var index = Number(event.currentTarget.getAttribute('data-index'));\n    var value = void 0;\n    if (config.isHalf) {\n      var isAtHalf = moreThanHalf(event);\n      setHalfStarHidden(isAtHalf);\n      if (isAtHalf) index += 1;\n      value = isAtHalf ? index : index + 0.5;\n      setHalfStarAt(index);\n    } else {\n      value = index = index + 1;\n    }\n    currentValueUpdated(value);\n  }\n  function renderHalfStarStyleElement() {\n    return _react2.default.createElement('style', {\n      dangerouslySetInnerHTML: {\n        __html: isUsingIcons ? getHalfStarStyleForIcons(config.activeColor) : getHalfStarStyles(config.activeColor, uniqueness)\n      }\n    });\n  }\n  function currentValueUpdated(value) {\n    if (value !== currentValue) {\n      setStars(getStars(value));\n      setCurrentValue(value);\n      props.onChange(value);\n    }\n  }\n  function handleKeyDown(event) {\n    if (!config.a11y && !config.edit) return;\n    var key = event.key;\n    var value = currentValue;\n    var keyNumber = Number(key); // e.g. \"1\" => 1, \"ArrowUp\" => NaN\n    if (keyNumber) {\n      if (Number.isInteger(keyNumber) && keyNumber > 0 && keyNumber <= config.count) {\n        value = keyNumber;\n      }\n    } else {\n      if ((key === \"ArrowUp\" || key === \"ArrowRight\") && value < config.count) {\n        event.preventDefault();\n        value += config.isHalf ? 0.5 : 1;\n      } else if ((key === \"ArrowDown\" || key === \"ArrowLeft\") && value > 0.5) {\n        event.preventDefault();\n        value -= config.isHalf ? 0.5 : 1;\n      }\n    }\n    updateHalfStarValues(value);\n    currentValueUpdated(value);\n  }\n  function renderStars() {\n    return stars.map(function (star, i) {\n      return _react2.default.createElement(_star2.default, {\n        key: i,\n        index: i,\n        active: star.active,\n        config: config,\n        onMouseOver: mouseOver,\n        onMouseLeave: mouseLeave,\n        onClick: onClick,\n        halfStarHidden: halfStarHidden,\n        halfStarAt: halfStarAt,\n        isUsingIcons: isUsingIcons,\n        uniqueness: uniqueness\n      });\n    });\n  }\n  return _react2.default.createElement('div', {\n    className: 'react-stars-wrapper-' + uniqueness,\n    style: {\n      display: 'flex'\n    }\n  }, _react2.default.createElement('div', {\n    tabIndex: config.a11y && config.edit ? 0 : null,\n    'aria-label': 'add rating by typing an integer from 0 to 5 or pressing arrow keys',\n    onKeyDown: handleKeyDown,\n    className: classNames,\n    style: parentStyles\n  }, config.isHalf && renderHalfStarStyleElement(), renderStars(), _react2.default.createElement('p', {\n    style: {\n      position: 'absolute',\n      left: '-200rem'\n    },\n    role: 'status'\n  }, currentValue)));\n}\nReactStars.propTypes = {\n  classNames: _propTypes2.default.string,\n  edit: _propTypes2.default.bool,\n  half: _propTypes2.default.bool,\n  value: _propTypes2.default.number,\n  count: _propTypes2.default.number,\n  char: _propTypes2.default.string,\n  size: _propTypes2.default.number,\n  color: _propTypes2.default.string,\n  activeColor: _propTypes2.default.string,\n  emptyIcon: _propTypes2.default.element,\n  halfIcon: _propTypes2.default.element,\n  filledIcon: _propTypes2.default.element,\n  a11y: _propTypes2.default.bool\n};\nReactStars.defaultProps = {\n  edit: true,\n  half: false,\n  value: 0,\n  count: 5,\n  char: '★',\n  size: 15,\n  color: 'gray',\n  activeColor: '#ffd700',\n  a11y: true,\n  onChange: function onChange() {}\n};\nexports.default = ReactStars;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "_slicedToArray", "sliceIterator", "arr", "i", "_arr", "_n", "_d", "_e", "undefined", "_i", "Symbol", "iterator", "_s", "next", "done", "push", "length", "err", "Array", "isArray", "TypeError", "_react", "require", "_react2", "_interopRequireDefault", "_propTypes", "_propTypes2", "_useConfig3", "_useConfig4", "_star", "_star2", "obj", "__esModule", "default", "parentStyles", "overflow", "position", "getHalfStarStyles", "color", "uniqueness", "getHalfStarStyleForIcons", "ReactStars", "props", "_useState", "useState", "_useState2", "setUniqueness", "_useState3", "_useState4", "currentValue", "setCurrentValue", "_useState5", "_useState6", "stars", "setStars", "_useState7", "_useState8", "isUsingIcons", "setIsUsingIcons", "_useConfig", "_useConfig2", "config", "setConfig", "_useState9", "_useState10", "halfStarAt", "setHalfStarAt", "_useState11", "_useState12", "halfStarHidden", "setHalfStarHidden", "_useState13", "_useState14", "classNames", "setClassNames", "iconsUsed", "is<PERSON>alf", "emptyIcon", "filledIcon", "halfIcon", "createUniqueness", "Math", "random", "replace", "useEffect", "addClassNames", "validateInitialValue", "count", "getStars", "floor", "reactStarsClass", "isDecimal", "getRate", "round", "activeCount", "active", "mouseOver", "event", "edit", "index", "Number", "currentTarget", "getAttribute", "isAtHalf", "moreThanHalf", "updateStars", "currentActive", "filter", "x", "target", "boundingClientRect", "getBoundingClientRect", "mouseAt", "clientX", "left", "abs", "width", "mouseLeave", "updateHalfStarValues", "onClick", "currentValueUpdated", "renderHalfStarStyleElement", "createElement", "dangerouslySetInnerHTML", "__html", "activeColor", "onChange", "handleKeyDown", "a11y", "key", "keyNumber", "isInteger", "preventDefault", "renderStars", "map", "star", "onMouseOver", "onMouseLeave", "className", "style", "display", "tabIndex", "onKeyDown", "role", "propTypes", "string", "bool", "half", "number", "char", "size", "element", "defaultProps"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/react-rating-stars-component/dist/react-stars.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\n\nvar _slicedToArray = function () { function sliceIterator(arr, i) { var _arr = []; var _n = true; var _d = false; var _e = undefined; try { for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"]) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; } return function (arr, i) { if (Array.isArray(arr)) { return arr; } else if (Symbol.iterator in Object(arr)) { return sliceIterator(arr, i); } else { throw new TypeError(\"Invalid attempt to destructure non-iterable instance\"); } }; }();\n\nvar _react = require('react');\n\nvar _react2 = _interopRequireDefault(_react);\n\nvar _propTypes = require('prop-types');\n\nvar _propTypes2 = _interopRequireDefault(_propTypes);\n\nvar _useConfig3 = require('./hooks/useConfig');\n\nvar _useConfig4 = _interopRequireDefault(_useConfig3);\n\nvar _star = require('./star');\n\nvar _star2 = _interopRequireDefault(_star);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar parentStyles = {\n    overflow: \"hidden\",\n    position: \"relative\"\n};\n\nfunction getHalfStarStyles(color, uniqueness) {\n    return '\\n    .react-stars-' + uniqueness + ':before {\\n      position: absolute;\\n      overflow: hidden;\\n      display: block;\\n      z-index: 1;\\n      top: 0; left: 0;\\n      width: 50%;\\n      content: attr(data-forhalf);\\n      color: ' + color + ';\\n  }';\n}\n\nfunction getHalfStarStyleForIcons(color) {\n    return '\\n          span.react-stars-half > * {\\n          color: ' + color + ';\\n      }';\n};\n\nfunction ReactStars(props) {\n    var _useState = (0, _react.useState)(''),\n        _useState2 = _slicedToArray(_useState, 2),\n        uniqueness = _useState2[0],\n        setUniqueness = _useState2[1];\n\n    var _useState3 = (0, _react.useState)(0),\n        _useState4 = _slicedToArray(_useState3, 2),\n        currentValue = _useState4[0],\n        setCurrentValue = _useState4[1];\n\n    var _useState5 = (0, _react.useState)([]),\n        _useState6 = _slicedToArray(_useState5, 2),\n        stars = _useState6[0],\n        setStars = _useState6[1];\n\n    var _useState7 = (0, _react.useState)(false),\n        _useState8 = _slicedToArray(_useState7, 2),\n        isUsingIcons = _useState8[0],\n        setIsUsingIcons = _useState8[1];\n\n    var _useConfig = (0, _useConfig4.default)(props),\n        _useConfig2 = _slicedToArray(_useConfig, 2),\n        config = _useConfig2[0],\n        setConfig = _useConfig2[1];\n\n    var _useState9 = (0, _react.useState)(0),\n        _useState10 = _slicedToArray(_useState9, 2),\n        halfStarAt = _useState10[0],\n        setHalfStarAt = _useState10[1];\n\n    var _useState11 = (0, _react.useState)(false),\n        _useState12 = _slicedToArray(_useState11, 2),\n        halfStarHidden = _useState12[0],\n        setHalfStarHidden = _useState12[1];\n\n    var _useState13 = (0, _react.useState)(''),\n        _useState14 = _slicedToArray(_useState13, 2),\n        classNames = _useState14[0],\n        setClassNames = _useState14[1];\n\n    function iconsUsed(config) {\n        return !config.isHalf && config.emptyIcon && config.filledIcon || config.isHalf && config.emptyIcon && config.halfIcon && config.filledIcon;\n    }\n\n    function createUniqueness() {\n        setUniqueness((Math.random() + \"\").replace(\".\", \"\"));\n    }\n\n    (0, _react.useEffect)(function () {\n        addClassNames();\n        validateInitialValue(props.value, props.count);\n        setStars(getStars(props.value));\n        setConfig(props);\n        createUniqueness();\n        setIsUsingIcons(iconsUsed(props));\n        setHalfStarAt(Math.floor(props.value));\n        setHalfStarHidden(props.isHalf && props.value % 1 < 0.5);\n    }, []);\n\n    function validateInitialValue(value, count) {\n        if (value < 0 || value > count) {\n            setCurrentValue(0);\n        } else {\n            setCurrentValue(value);\n        }\n    }\n\n    function addClassNames() {\n        var reactStarsClass = 'react-stars';\n        setClassNames(props.classNames + (' ' + reactStarsClass));\n    }\n\n    function isDecimal(value) {\n        return value % 1 === 0;\n    }\n\n    function getRate() {\n        return config.isHalf ? Math.floor(currentValue) : Math.round(currentValue);\n    }\n\n    function getStars(activeCount) {\n        if (typeof activeCount === 'undefined') {\n            activeCount = getRate();\n        }\n\n        var stars = [];\n        for (var i = 0; i < config.count; i++) {\n            stars.push({\n                active: i <= activeCount - 1\n            });\n        }\n        return stars;\n    }\n\n    function mouseOver(event) {\n        if (!config.edit) return;\n\n        var index = Number(event.currentTarget.getAttribute('data-index'));\n\n        if (config.isHalf) {\n            var isAtHalf = moreThanHalf(event);\n            setHalfStarHidden(isAtHalf);\n            if (isAtHalf) index += 1;\n            setHalfStarAt(index);\n        } else {\n            index += 1;\n        }\n\n        updateStars(index);\n    }\n\n    function updateStars(index) {\n        var currentActive = stars.filter(function (x) {\n            return x.active;\n        });\n        if (index !== currentActive.length) {\n            setStars(getStars(index));\n        }\n    }\n\n    function moreThanHalf(event) {\n        var target = event.target;\n\n        var boundingClientRect = target.getBoundingClientRect();\n        var mouseAt = event.clientX - boundingClientRect.left;\n        mouseAt = Math.round(Math.abs(mouseAt));\n\n        return mouseAt > boundingClientRect.width / 2;\n    }\n\n    function mouseLeave() {\n        if (!config.edit) return;\n\n        updateHalfStarValues(currentValue);\n        setStars(getStars());\n    }\n\n    function updateHalfStarValues(value) {\n        if (config.isHalf) {\n            setHalfStarHidden(isDecimal(value));\n            setHalfStarAt(Math.floor(value));\n        }\n    }\n\n    function onClick(event) {\n        if (!config.edit) return;\n\n        var index = Number(event.currentTarget.getAttribute('data-index'));\n        var value = void 0;\n        if (config.isHalf) {\n            var isAtHalf = moreThanHalf(event);\n            setHalfStarHidden(isAtHalf);\n            if (isAtHalf) index += 1;\n            value = isAtHalf ? index : index + 0.5;\n            setHalfStarAt(index);\n        } else {\n            value = index = index + 1;\n        }\n\n        currentValueUpdated(value);\n    }\n\n    function renderHalfStarStyleElement() {\n        return _react2.default.createElement('style', { dangerouslySetInnerHTML: {\n                __html: isUsingIcons ? getHalfStarStyleForIcons(config.activeColor) : getHalfStarStyles(config.activeColor, uniqueness)\n            } });\n    }\n\n    function currentValueUpdated(value) {\n        if (value !== currentValue) {\n            setStars(getStars(value));\n            setCurrentValue(value);\n            props.onChange(value);\n        }\n    }\n\n    function handleKeyDown(event) {\n        if (!config.a11y && !config.edit) return;\n\n        var key = event.key;\n\n        var value = currentValue;\n\n        var keyNumber = Number(key); // e.g. \"1\" => 1, \"ArrowUp\" => NaN\n        if (keyNumber) {\n            if (Number.isInteger(keyNumber) && keyNumber > 0 && keyNumber <= config.count) {\n                value = keyNumber;\n            }\n        } else {\n            if ((key === \"ArrowUp\" || key === \"ArrowRight\") && value < config.count) {\n                event.preventDefault();\n\n                value += config.isHalf ? 0.5 : 1;\n            } else if ((key === \"ArrowDown\" || key === \"ArrowLeft\") && value > 0.5) {\n                event.preventDefault();\n                value -= config.isHalf ? 0.5 : 1;\n            }\n        }\n\n        updateHalfStarValues(value);\n\n        currentValueUpdated(value);\n    }\n\n    function renderStars() {\n        return stars.map(function (star, i) {\n            return _react2.default.createElement(_star2.default, {\n                key: i,\n                index: i,\n                active: star.active,\n                config: config,\n                onMouseOver: mouseOver,\n                onMouseLeave: mouseLeave,\n                onClick: onClick,\n                halfStarHidden: halfStarHidden,\n                halfStarAt: halfStarAt,\n                isUsingIcons: isUsingIcons,\n                uniqueness: uniqueness\n            });\n        });\n    }\n\n    return _react2.default.createElement(\n        'div',\n        { className: 'react-stars-wrapper-' + uniqueness,\n            style: { display: 'flex' } },\n        _react2.default.createElement(\n            'div',\n            { tabIndex: config.a11y && config.edit ? 0 : null,\n                'aria-label': 'add rating by typing an integer from 0 to 5 or pressing arrow keys',\n                onKeyDown: handleKeyDown,\n                className: classNames,\n                style: parentStyles },\n            config.isHalf && renderHalfStarStyleElement(),\n            renderStars(),\n            _react2.default.createElement(\n                'p',\n                { style: { position: 'absolute', left: '-200rem' }, role: 'status' },\n                currentValue\n            )\n        )\n    );\n}\n\nReactStars.propTypes = {\n    classNames: _propTypes2.default.string,\n    edit: _propTypes2.default.bool,\n    half: _propTypes2.default.bool,\n    value: _propTypes2.default.number,\n    count: _propTypes2.default.number,\n    char: _propTypes2.default.string,\n    size: _propTypes2.default.number,\n    color: _propTypes2.default.string,\n    activeColor: _propTypes2.default.string,\n    emptyIcon: _propTypes2.default.element,\n    halfIcon: _propTypes2.default.element,\n    filledIcon: _propTypes2.default.element,\n    a11y: _propTypes2.default.bool\n};\n\nReactStars.defaultProps = {\n    edit: true,\n    half: false,\n    value: 0,\n    count: 5,\n    char: '★',\n    size: 15,\n    color: 'gray',\n    activeColor: '#ffd700',\n    a11y: true,\n\n    onChange: function onChange() {}\n};\n\nexports.default = ReactStars;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EACzCC,KAAK,EAAE;AACX,CAAC,CAAC;AAEF,IAAIC,cAAc,GAAG,YAAY;EAAE,SAASC,aAAaA,CAACC,GAAG,EAAEC,CAAC,EAAE;IAAE,IAAIC,IAAI,GAAG,EAAE;IAAE,IAAIC,EAAE,GAAG,IAAI;IAAE,IAAIC,EAAE,GAAG,KAAK;IAAE,IAAIC,EAAE,GAAGC,SAAS;IAAE,IAAI;MAAE,KAAK,IAAIC,EAAE,GAAGP,GAAG,CAACQ,MAAM,CAACC,QAAQ,CAAC,EAAE,EAAEC,EAAE,EAAE,EAAEP,EAAE,GAAG,CAACO,EAAE,GAAGH,EAAE,CAACI,IAAI,EAAE,EAAEC,IAAI,CAAC,EAAET,EAAE,GAAG,IAAI,EAAE;QAAED,IAAI,CAACW,IAAI,CAACH,EAAE,CAACb,KAAK,CAAC;QAAE,IAAII,CAAC,IAAIC,IAAI,CAACY,MAAM,KAAKb,CAAC,EAAE;MAAO;IAAE,CAAC,CAAC,OAAOc,GAAG,EAAE;MAAEX,EAAE,GAAG,IAAI;MAAEC,EAAE,GAAGU,GAAG;IAAE,CAAC,SAAS;MAAE,IAAI;QAAE,IAAI,CAACZ,EAAE,IAAII,EAAE,CAAC,QAAQ,CAAC,EAAEA,EAAE,CAAC,QAAQ,CAAC,EAAE;MAAE,CAAC,SAAS;QAAE,IAAIH,EAAE,EAAE,MAAMC,EAAE;MAAE;IAAE;IAAE,OAAOH,IAAI;EAAE;EAAE,OAAO,UAAUF,GAAG,EAAEC,CAAC,EAAE;IAAE,IAAIe,KAAK,CAACC,OAAO,CAACjB,GAAG,CAAC,EAAE;MAAE,OAAOA,GAAG;IAAE,CAAC,MAAM,IAAIQ,MAAM,CAACC,QAAQ,IAAIf,MAAM,CAACM,GAAG,CAAC,EAAE;MAAE,OAAOD,aAAa,CAACC,GAAG,EAAEC,CAAC,CAAC;IAAE,CAAC,MAAM;MAAE,MAAM,IAAIiB,SAAS,CAAC,sDAAsD,CAAC;IAAE;EAAE,CAAC;AAAE,CAAC,EAAE;AAEvpB,IAAIC,MAAM,GAAGC,OAAO,CAAC,OAAO,CAAC;AAE7B,IAAIC,OAAO,GAAGC,sBAAsB,CAACH,MAAM,CAAC;AAE5C,IAAII,UAAU,GAAGH,OAAO,CAAC,YAAY,CAAC;AAEtC,IAAII,WAAW,GAAGF,sBAAsB,CAACC,UAAU,CAAC;AAEpD,IAAIE,WAAW,GAAGL,OAAO,CAAC,mBAAmB,CAAC;AAE9C,IAAIM,WAAW,GAAGJ,sBAAsB,CAACG,WAAW,CAAC;AAErD,IAAIE,KAAK,GAAGP,OAAO,CAAC,QAAQ,CAAC;AAE7B,IAAIQ,MAAM,GAAGN,sBAAsB,CAACK,KAAK,CAAC;AAE1C,SAASL,sBAAsBA,CAACO,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEE,OAAO,EAAEF;EAAI,CAAC;AAAE;AAE9F,IAAIG,YAAY,GAAG;EACfC,QAAQ,EAAE,QAAQ;EAClBC,QAAQ,EAAE;AACd,CAAC;AAED,SAASC,iBAAiBA,CAACC,KAAK,EAAEC,UAAU,EAAE;EAC1C,OAAO,qBAAqB,GAAGA,UAAU,GAAG,uMAAuM,GAAGD,KAAK,GAAG,QAAQ;AAC1Q;AAEA,SAASE,wBAAwBA,CAACF,KAAK,EAAE;EACrC,OAAO,4DAA4D,GAAGA,KAAK,GAAG,YAAY;AAC9F;AAAC;AAED,SAASG,UAAUA,CAACC,KAAK,EAAE;EACvB,IAAIC,SAAS,GAAG,CAAC,CAAC,EAAEtB,MAAM,CAACuB,QAAQ,EAAE,EAAE,CAAC;IACpCC,UAAU,GAAG7C,cAAc,CAAC2C,SAAS,EAAE,CAAC,CAAC;IACzCJ,UAAU,GAAGM,UAAU,CAAC,CAAC,CAAC;IAC1BC,aAAa,GAAGD,UAAU,CAAC,CAAC,CAAC;EAEjC,IAAIE,UAAU,GAAG,CAAC,CAAC,EAAE1B,MAAM,CAACuB,QAAQ,EAAE,CAAC,CAAC;IACpCI,UAAU,GAAGhD,cAAc,CAAC+C,UAAU,EAAE,CAAC,CAAC;IAC1CE,YAAY,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC5BE,eAAe,GAAGF,UAAU,CAAC,CAAC,CAAC;EAEnC,IAAIG,UAAU,GAAG,CAAC,CAAC,EAAE9B,MAAM,CAACuB,QAAQ,EAAE,EAAE,CAAC;IACrCQ,UAAU,GAAGpD,cAAc,CAACmD,UAAU,EAAE,CAAC,CAAC;IAC1CE,KAAK,GAAGD,UAAU,CAAC,CAAC,CAAC;IACrBE,QAAQ,GAAGF,UAAU,CAAC,CAAC,CAAC;EAE5B,IAAIG,UAAU,GAAG,CAAC,CAAC,EAAElC,MAAM,CAACuB,QAAQ,EAAE,KAAK,CAAC;IACxCY,UAAU,GAAGxD,cAAc,CAACuD,UAAU,EAAE,CAAC,CAAC;IAC1CE,YAAY,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC5BE,eAAe,GAAGF,UAAU,CAAC,CAAC,CAAC;EAEnC,IAAIG,UAAU,GAAG,CAAC,CAAC,EAAE/B,WAAW,CAACK,OAAO,EAAES,KAAK,CAAC;IAC5CkB,WAAW,GAAG5D,cAAc,CAAC2D,UAAU,EAAE,CAAC,CAAC;IAC3CE,MAAM,GAAGD,WAAW,CAAC,CAAC,CAAC;IACvBE,SAAS,GAAGF,WAAW,CAAC,CAAC,CAAC;EAE9B,IAAIG,UAAU,GAAG,CAAC,CAAC,EAAE1C,MAAM,CAACuB,QAAQ,EAAE,CAAC,CAAC;IACpCoB,WAAW,GAAGhE,cAAc,CAAC+D,UAAU,EAAE,CAAC,CAAC;IAC3CE,UAAU,GAAGD,WAAW,CAAC,CAAC,CAAC;IAC3BE,aAAa,GAAGF,WAAW,CAAC,CAAC,CAAC;EAElC,IAAIG,WAAW,GAAG,CAAC,CAAC,EAAE9C,MAAM,CAACuB,QAAQ,EAAE,KAAK,CAAC;IACzCwB,WAAW,GAAGpE,cAAc,CAACmE,WAAW,EAAE,CAAC,CAAC;IAC5CE,cAAc,GAAGD,WAAW,CAAC,CAAC,CAAC;IAC/BE,iBAAiB,GAAGF,WAAW,CAAC,CAAC,CAAC;EAEtC,IAAIG,WAAW,GAAG,CAAC,CAAC,EAAElD,MAAM,CAACuB,QAAQ,EAAE,EAAE,CAAC;IACtC4B,WAAW,GAAGxE,cAAc,CAACuE,WAAW,EAAE,CAAC,CAAC;IAC5CE,UAAU,GAAGD,WAAW,CAAC,CAAC,CAAC;IAC3BE,aAAa,GAAGF,WAAW,CAAC,CAAC,CAAC;EAElC,SAASG,SAASA,CAACd,MAAM,EAAE;IACvB,OAAO,CAACA,MAAM,CAACe,MAAM,IAAIf,MAAM,CAACgB,SAAS,IAAIhB,MAAM,CAACiB,UAAU,IAAIjB,MAAM,CAACe,MAAM,IAAIf,MAAM,CAACgB,SAAS,IAAIhB,MAAM,CAACkB,QAAQ,IAAIlB,MAAM,CAACiB,UAAU;EAC/I;EAEA,SAASE,gBAAgBA,CAAA,EAAG;IACxBlC,aAAa,CAAC,CAACmC,IAAI,CAACC,MAAM,EAAE,GAAG,EAAE,EAAEC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;EACxD;EAEA,CAAC,CAAC,EAAE9D,MAAM,CAAC+D,SAAS,EAAE,YAAY;IAC9BC,aAAa,EAAE;IACfC,oBAAoB,CAAC5C,KAAK,CAAC3C,KAAK,EAAE2C,KAAK,CAAC6C,KAAK,CAAC;IAC9CjC,QAAQ,CAACkC,QAAQ,CAAC9C,KAAK,CAAC3C,KAAK,CAAC,CAAC;IAC/B+D,SAAS,CAACpB,KAAK,CAAC;IAChBsC,gBAAgB,EAAE;IAClBtB,eAAe,CAACiB,SAAS,CAACjC,KAAK,CAAC,CAAC;IACjCwB,aAAa,CAACe,IAAI,CAACQ,KAAK,CAAC/C,KAAK,CAAC3C,KAAK,CAAC,CAAC;IACtCuE,iBAAiB,CAAC5B,KAAK,CAACkC,MAAM,IAAIlC,KAAK,CAAC3C,KAAK,GAAG,CAAC,GAAG,GAAG,CAAC;EAC5D,CAAC,EAAE,EAAE,CAAC;EAEN,SAASuF,oBAAoBA,CAACvF,KAAK,EAAEwF,KAAK,EAAE;IACxC,IAAIxF,KAAK,GAAG,CAAC,IAAIA,KAAK,GAAGwF,KAAK,EAAE;MAC5BrC,eAAe,CAAC,CAAC,CAAC;IACtB,CAAC,MAAM;MACHA,eAAe,CAACnD,KAAK,CAAC;IAC1B;EACJ;EAEA,SAASsF,aAAaA,CAAA,EAAG;IACrB,IAAIK,eAAe,GAAG,aAAa;IACnChB,aAAa,CAAChC,KAAK,CAAC+B,UAAU,IAAI,GAAG,GAAGiB,eAAe,CAAC,CAAC;EAC7D;EAEA,SAASC,SAASA,CAAC5F,KAAK,EAAE;IACtB,OAAOA,KAAK,GAAG,CAAC,KAAK,CAAC;EAC1B;EAEA,SAAS6F,OAAOA,CAAA,EAAG;IACf,OAAO/B,MAAM,CAACe,MAAM,GAAGK,IAAI,CAACQ,KAAK,CAACxC,YAAY,CAAC,GAAGgC,IAAI,CAACY,KAAK,CAAC5C,YAAY,CAAC;EAC9E;EAEA,SAASuC,QAAQA,CAACM,WAAW,EAAE;IAC3B,IAAI,OAAOA,WAAW,KAAK,WAAW,EAAE;MACpCA,WAAW,GAAGF,OAAO,EAAE;IAC3B;IAEA,IAAIvC,KAAK,GAAG,EAAE;IACd,KAAK,IAAIlD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0D,MAAM,CAAC0B,KAAK,EAAEpF,CAAC,EAAE,EAAE;MACnCkD,KAAK,CAACtC,IAAI,CAAC;QACPgF,MAAM,EAAE5F,CAAC,IAAI2F,WAAW,GAAG;MAC/B,CAAC,CAAC;IACN;IACA,OAAOzC,KAAK;EAChB;EAEA,SAAS2C,SAASA,CAACC,KAAK,EAAE;IACtB,IAAI,CAACpC,MAAM,CAACqC,IAAI,EAAE;IAElB,IAAIC,KAAK,GAAGC,MAAM,CAACH,KAAK,CAACI,aAAa,CAACC,YAAY,CAAC,YAAY,CAAC,CAAC;IAElE,IAAIzC,MAAM,CAACe,MAAM,EAAE;MACf,IAAI2B,QAAQ,GAAGC,YAAY,CAACP,KAAK,CAAC;MAClC3B,iBAAiB,CAACiC,QAAQ,CAAC;MAC3B,IAAIA,QAAQ,EAAEJ,KAAK,IAAI,CAAC;MACxBjC,aAAa,CAACiC,KAAK,CAAC;IACxB,CAAC,MAAM;MACHA,KAAK,IAAI,CAAC;IACd;IAEAM,WAAW,CAACN,KAAK,CAAC;EACtB;EAEA,SAASM,WAAWA,CAACN,KAAK,EAAE;IACxB,IAAIO,aAAa,GAAGrD,KAAK,CAACsD,MAAM,CAAC,UAAUC,CAAC,EAAE;MAC1C,OAAOA,CAAC,CAACb,MAAM;IACnB,CAAC,CAAC;IACF,IAAII,KAAK,KAAKO,aAAa,CAAC1F,MAAM,EAAE;MAChCsC,QAAQ,CAACkC,QAAQ,CAACW,KAAK,CAAC,CAAC;IAC7B;EACJ;EAEA,SAASK,YAAYA,CAACP,KAAK,EAAE;IACzB,IAAIY,MAAM,GAAGZ,KAAK,CAACY,MAAM;IAEzB,IAAIC,kBAAkB,GAAGD,MAAM,CAACE,qBAAqB,EAAE;IACvD,IAAIC,OAAO,GAAGf,KAAK,CAACgB,OAAO,GAAGH,kBAAkB,CAACI,IAAI;IACrDF,OAAO,GAAG/B,IAAI,CAACY,KAAK,CAACZ,IAAI,CAACkC,GAAG,CAACH,OAAO,CAAC,CAAC;IAEvC,OAAOA,OAAO,GAAGF,kBAAkB,CAACM,KAAK,GAAG,CAAC;EACjD;EAEA,SAASC,UAAUA,CAAA,EAAG;IAClB,IAAI,CAACxD,MAAM,CAACqC,IAAI,EAAE;IAElBoB,oBAAoB,CAACrE,YAAY,CAAC;IAClCK,QAAQ,CAACkC,QAAQ,EAAE,CAAC;EACxB;EAEA,SAAS8B,oBAAoBA,CAACvH,KAAK,EAAE;IACjC,IAAI8D,MAAM,CAACe,MAAM,EAAE;MACfN,iBAAiB,CAACqB,SAAS,CAAC5F,KAAK,CAAC,CAAC;MACnCmE,aAAa,CAACe,IAAI,CAACQ,KAAK,CAAC1F,KAAK,CAAC,CAAC;IACpC;EACJ;EAEA,SAASwH,OAAOA,CAACtB,KAAK,EAAE;IACpB,IAAI,CAACpC,MAAM,CAACqC,IAAI,EAAE;IAElB,IAAIC,KAAK,GAAGC,MAAM,CAACH,KAAK,CAACI,aAAa,CAACC,YAAY,CAAC,YAAY,CAAC,CAAC;IAClE,IAAIvG,KAAK,GAAG,KAAK,CAAC;IAClB,IAAI8D,MAAM,CAACe,MAAM,EAAE;MACf,IAAI2B,QAAQ,GAAGC,YAAY,CAACP,KAAK,CAAC;MAClC3B,iBAAiB,CAACiC,QAAQ,CAAC;MAC3B,IAAIA,QAAQ,EAAEJ,KAAK,IAAI,CAAC;MACxBpG,KAAK,GAAGwG,QAAQ,GAAGJ,KAAK,GAAGA,KAAK,GAAG,GAAG;MACtCjC,aAAa,CAACiC,KAAK,CAAC;IACxB,CAAC,MAAM;MACHpG,KAAK,GAAGoG,KAAK,GAAGA,KAAK,GAAG,CAAC;IAC7B;IAEAqB,mBAAmB,CAACzH,KAAK,CAAC;EAC9B;EAEA,SAAS0H,0BAA0BA,CAAA,EAAG;IAClC,OAAOlG,OAAO,CAACU,OAAO,CAACyF,aAAa,CAAC,OAAO,EAAE;MAAEC,uBAAuB,EAAE;QACjEC,MAAM,EAAEnE,YAAY,GAAGjB,wBAAwB,CAACqB,MAAM,CAACgE,WAAW,CAAC,GAAGxF,iBAAiB,CAACwB,MAAM,CAACgE,WAAW,EAAEtF,UAAU;MAC1H;IAAE,CAAC,CAAC;EACZ;EAEA,SAASiF,mBAAmBA,CAACzH,KAAK,EAAE;IAChC,IAAIA,KAAK,KAAKkD,YAAY,EAAE;MACxBK,QAAQ,CAACkC,QAAQ,CAACzF,KAAK,CAAC,CAAC;MACzBmD,eAAe,CAACnD,KAAK,CAAC;MACtB2C,KAAK,CAACoF,QAAQ,CAAC/H,KAAK,CAAC;IACzB;EACJ;EAEA,SAASgI,aAAaA,CAAC9B,KAAK,EAAE;IAC1B,IAAI,CAACpC,MAAM,CAACmE,IAAI,IAAI,CAACnE,MAAM,CAACqC,IAAI,EAAE;IAElC,IAAI+B,GAAG,GAAGhC,KAAK,CAACgC,GAAG;IAEnB,IAAIlI,KAAK,GAAGkD,YAAY;IAExB,IAAIiF,SAAS,GAAG9B,MAAM,CAAC6B,GAAG,CAAC,CAAC,CAAC;IAC7B,IAAIC,SAAS,EAAE;MACX,IAAI9B,MAAM,CAAC+B,SAAS,CAACD,SAAS,CAAC,IAAIA,SAAS,GAAG,CAAC,IAAIA,SAAS,IAAIrE,MAAM,CAAC0B,KAAK,EAAE;QAC3ExF,KAAK,GAAGmI,SAAS;MACrB;IACJ,CAAC,MAAM;MACH,IAAI,CAACD,GAAG,KAAK,SAAS,IAAIA,GAAG,KAAK,YAAY,KAAKlI,KAAK,GAAG8D,MAAM,CAAC0B,KAAK,EAAE;QACrEU,KAAK,CAACmC,cAAc,EAAE;QAEtBrI,KAAK,IAAI8D,MAAM,CAACe,MAAM,GAAG,GAAG,GAAG,CAAC;MACpC,CAAC,MAAM,IAAI,CAACqD,GAAG,KAAK,WAAW,IAAIA,GAAG,KAAK,WAAW,KAAKlI,KAAK,GAAG,GAAG,EAAE;QACpEkG,KAAK,CAACmC,cAAc,EAAE;QACtBrI,KAAK,IAAI8D,MAAM,CAACe,MAAM,GAAG,GAAG,GAAG,CAAC;MACpC;IACJ;IAEA0C,oBAAoB,CAACvH,KAAK,CAAC;IAE3ByH,mBAAmB,CAACzH,KAAK,CAAC;EAC9B;EAEA,SAASsI,WAAWA,CAAA,EAAG;IACnB,OAAOhF,KAAK,CAACiF,GAAG,CAAC,UAAUC,IAAI,EAAEpI,CAAC,EAAE;MAChC,OAAOoB,OAAO,CAACU,OAAO,CAACyF,aAAa,CAAC5F,MAAM,CAACG,OAAO,EAAE;QACjDgG,GAAG,EAAE9H,CAAC;QACNgG,KAAK,EAAEhG,CAAC;QACR4F,MAAM,EAAEwC,IAAI,CAACxC,MAAM;QACnBlC,MAAM,EAAEA,MAAM;QACd2E,WAAW,EAAExC,SAAS;QACtByC,YAAY,EAAEpB,UAAU;QACxBE,OAAO,EAAEA,OAAO;QAChBlD,cAAc,EAAEA,cAAc;QAC9BJ,UAAU,EAAEA,UAAU;QACtBR,YAAY,EAAEA,YAAY;QAC1BlB,UAAU,EAAEA;MAChB,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EAEA,OAAOhB,OAAO,CAACU,OAAO,CAACyF,aAAa,CAChC,KAAK,EACL;IAAEgB,SAAS,EAAE,sBAAsB,GAAGnG,UAAU;IAC5CoG,KAAK,EAAE;MAAEC,OAAO,EAAE;IAAO;EAAE,CAAC,EAChCrH,OAAO,CAACU,OAAO,CAACyF,aAAa,CACzB,KAAK,EACL;IAAEmB,QAAQ,EAAEhF,MAAM,CAACmE,IAAI,IAAInE,MAAM,CAACqC,IAAI,GAAG,CAAC,GAAG,IAAI;IAC7C,YAAY,EAAE,oEAAoE;IAClF4C,SAAS,EAAEf,aAAa;IACxBW,SAAS,EAAEjE,UAAU;IACrBkE,KAAK,EAAEzG;EAAa,CAAC,EACzB2B,MAAM,CAACe,MAAM,IAAI6C,0BAA0B,EAAE,EAC7CY,WAAW,EAAE,EACb9G,OAAO,CAACU,OAAO,CAACyF,aAAa,CACzB,GAAG,EACH;IAAEiB,KAAK,EAAE;MAAEvG,QAAQ,EAAE,UAAU;MAAE8E,IAAI,EAAE;IAAU,CAAC;IAAE6B,IAAI,EAAE;EAAS,CAAC,EACpE9F,YAAY,CACf,CACJ,CACJ;AACL;AAEAR,UAAU,CAACuG,SAAS,GAAG;EACnBvE,UAAU,EAAE/C,WAAW,CAACO,OAAO,CAACgH,MAAM;EACtC/C,IAAI,EAAExE,WAAW,CAACO,OAAO,CAACiH,IAAI;EAC9BC,IAAI,EAAEzH,WAAW,CAACO,OAAO,CAACiH,IAAI;EAC9BnJ,KAAK,EAAE2B,WAAW,CAACO,OAAO,CAACmH,MAAM;EACjC7D,KAAK,EAAE7D,WAAW,CAACO,OAAO,CAACmH,MAAM;EACjCC,IAAI,EAAE3H,WAAW,CAACO,OAAO,CAACgH,MAAM;EAChCK,IAAI,EAAE5H,WAAW,CAACO,OAAO,CAACmH,MAAM;EAChC9G,KAAK,EAAEZ,WAAW,CAACO,OAAO,CAACgH,MAAM;EACjCpB,WAAW,EAAEnG,WAAW,CAACO,OAAO,CAACgH,MAAM;EACvCpE,SAAS,EAAEnD,WAAW,CAACO,OAAO,CAACsH,OAAO;EACtCxE,QAAQ,EAAErD,WAAW,CAACO,OAAO,CAACsH,OAAO;EACrCzE,UAAU,EAAEpD,WAAW,CAACO,OAAO,CAACsH,OAAO;EACvCvB,IAAI,EAAEtG,WAAW,CAACO,OAAO,CAACiH;AAC9B,CAAC;AAEDzG,UAAU,CAAC+G,YAAY,GAAG;EACtBtD,IAAI,EAAE,IAAI;EACViD,IAAI,EAAE,KAAK;EACXpJ,KAAK,EAAE,CAAC;EACRwF,KAAK,EAAE,CAAC;EACR8D,IAAI,EAAE,GAAG;EACTC,IAAI,EAAE,EAAE;EACRhH,KAAK,EAAE,MAAM;EACbuF,WAAW,EAAE,SAAS;EACtBG,IAAI,EAAE,IAAI;EAEVF,QAAQ,EAAE,SAASA,QAAQA,CAAA,EAAG,CAAC;AACnC,CAAC;AAEDhI,OAAO,CAACmC,OAAO,GAAGQ,UAAU"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}