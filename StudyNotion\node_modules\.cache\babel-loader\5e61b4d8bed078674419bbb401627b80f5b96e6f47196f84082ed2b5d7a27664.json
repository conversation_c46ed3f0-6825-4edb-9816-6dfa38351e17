{"ast": null, "code": "import classesToSelector from '../../shared/classes-to-selector.js';\nimport { createElement, elementIndex } from '../../shared/utils.js';\nexport default function A11y(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on\n  } = _ref;\n  extendParams({\n    a11y: {\n      enabled: true,\n      notificationClass: 'swiper-notification',\n      prevSlideMessage: 'Previous slide',\n      nextSlideMessage: 'Next slide',\n      firstSlideMessage: 'This is the first slide',\n      lastSlideMessage: 'This is the last slide',\n      paginationBulletMessage: 'Go to slide {{index}}',\n      slideLabelMessage: '{{index}} / {{slidesLength}}',\n      containerMessage: null,\n      containerRoleDescriptionMessage: null,\n      itemRoleDescriptionMessage: null,\n      slideRole: 'group',\n      id: null\n    }\n  });\n  swiper.a11y = {\n    clicked: false\n  };\n  let liveRegion = null;\n  function notify(message) {\n    const notification = liveRegion;\n    if (notification.length === 0) return;\n    notification.innerHTML = '';\n    notification.innerHTML = message;\n  }\n  const makeElementsArray = el => {\n    if (!Array.isArray(el)) el = [el].filter(e => !!e);\n    return el;\n  };\n  function getRandomNumber() {\n    let size = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 16;\n    const randomChar = () => Math.round(16 * Math.random()).toString(16);\n    return 'x'.repeat(size).replace(/x/g, randomChar);\n  }\n  function makeElFocusable(el) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('tabIndex', '0');\n    });\n  }\n  function makeElNotFocusable(el) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('tabIndex', '-1');\n    });\n  }\n  function addElRole(el, role) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('role', role);\n    });\n  }\n  function addElRoleDescription(el, description) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('aria-roledescription', description);\n    });\n  }\n  function addElControls(el, controls) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('aria-controls', controls);\n    });\n  }\n  function addElLabel(el, label) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('aria-label', label);\n    });\n  }\n  function addElId(el, id) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('id', id);\n    });\n  }\n  function addElLive(el, live) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('aria-live', live);\n    });\n  }\n  function disableEl(el) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('aria-disabled', true);\n    });\n  }\n  function enableEl(el) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('aria-disabled', false);\n    });\n  }\n  function onEnterOrSpaceKey(e) {\n    if (e.keyCode !== 13 && e.keyCode !== 32) return;\n    const params = swiper.params.a11y;\n    const targetEl = e.target;\n    if (swiper.pagination && swiper.pagination.el && (targetEl === swiper.pagination.el || swiper.pagination.el.contains(e.target))) {\n      if (!e.target.matches(classesToSelector(swiper.params.pagination.bulletClass))) return;\n    }\n    if (swiper.navigation && swiper.navigation.nextEl && targetEl === swiper.navigation.nextEl) {\n      if (!(swiper.isEnd && !swiper.params.loop)) {\n        swiper.slideNext();\n      }\n      if (swiper.isEnd) {\n        notify(params.lastSlideMessage);\n      } else {\n        notify(params.nextSlideMessage);\n      }\n    }\n    if (swiper.navigation && swiper.navigation.prevEl && targetEl === swiper.navigation.prevEl) {\n      if (!(swiper.isBeginning && !swiper.params.loop)) {\n        swiper.slidePrev();\n      }\n      if (swiper.isBeginning) {\n        notify(params.firstSlideMessage);\n      } else {\n        notify(params.prevSlideMessage);\n      }\n    }\n    if (swiper.pagination && targetEl.matches(classesToSelector(swiper.params.pagination.bulletClass))) {\n      targetEl.click();\n    }\n  }\n  function updateNavigation() {\n    if (swiper.params.loop || swiper.params.rewind || !swiper.navigation) return;\n    const {\n      nextEl,\n      prevEl\n    } = swiper.navigation;\n    if (prevEl) {\n      if (swiper.isBeginning) {\n        disableEl(prevEl);\n        makeElNotFocusable(prevEl);\n      } else {\n        enableEl(prevEl);\n        makeElFocusable(prevEl);\n      }\n    }\n    if (nextEl) {\n      if (swiper.isEnd) {\n        disableEl(nextEl);\n        makeElNotFocusable(nextEl);\n      } else {\n        enableEl(nextEl);\n        makeElFocusable(nextEl);\n      }\n    }\n  }\n  function hasPagination() {\n    return swiper.pagination && swiper.pagination.bullets && swiper.pagination.bullets.length;\n  }\n  function hasClickablePagination() {\n    return hasPagination() && swiper.params.pagination.clickable;\n  }\n  function updatePagination() {\n    const params = swiper.params.a11y;\n    if (!hasPagination()) return;\n    swiper.pagination.bullets.forEach(bulletEl => {\n      if (swiper.params.pagination.clickable) {\n        makeElFocusable(bulletEl);\n        if (!swiper.params.pagination.renderBullet) {\n          addElRole(bulletEl, 'button');\n          addElLabel(bulletEl, params.paginationBulletMessage.replace(/\\{\\{index\\}\\}/, elementIndex(bulletEl) + 1));\n        }\n      }\n      if (bulletEl.matches(classesToSelector(swiper.params.pagination.bulletActiveClass))) {\n        bulletEl.setAttribute('aria-current', 'true');\n      } else {\n        bulletEl.removeAttribute('aria-current');\n      }\n    });\n  }\n  const initNavEl = (el, wrapperId, message) => {\n    makeElFocusable(el);\n    if (el.tagName !== 'BUTTON') {\n      addElRole(el, 'button');\n      el.addEventListener('keydown', onEnterOrSpaceKey);\n    }\n    addElLabel(el, message);\n    addElControls(el, wrapperId);\n  };\n  const handlePointerDown = () => {\n    swiper.a11y.clicked = true;\n  };\n  const handlePointerUp = () => {\n    requestAnimationFrame(() => {\n      requestAnimationFrame(() => {\n        if (!swiper.destroyed) {\n          swiper.a11y.clicked = false;\n        }\n      });\n    });\n  };\n  const handleFocus = e => {\n    if (swiper.a11y.clicked) return;\n    const slideEl = e.target.closest(`.${swiper.params.slideClass}, swiper-slide`);\n    if (!slideEl || !swiper.slides.includes(slideEl)) return;\n    const isActive = swiper.slides.indexOf(slideEl) === swiper.activeIndex;\n    const isVisible = swiper.params.watchSlidesProgress && swiper.visibleSlides && swiper.visibleSlides.includes(slideEl);\n    if (isActive || isVisible) return;\n    if (e.sourceCapabilities && e.sourceCapabilities.firesTouchEvents) return;\n    if (swiper.isHorizontal()) {\n      swiper.el.scrollLeft = 0;\n    } else {\n      swiper.el.scrollTop = 0;\n    }\n    swiper.slideTo(swiper.slides.indexOf(slideEl), 0);\n  };\n  const initSlides = () => {\n    const params = swiper.params.a11y;\n    if (params.itemRoleDescriptionMessage) {\n      addElRoleDescription(swiper.slides, params.itemRoleDescriptionMessage);\n    }\n    if (params.slideRole) {\n      addElRole(swiper.slides, params.slideRole);\n    }\n    const slidesLength = swiper.slides.length;\n    if (params.slideLabelMessage) {\n      swiper.slides.forEach((slideEl, index) => {\n        const slideIndex = swiper.params.loop ? parseInt(slideEl.getAttribute('data-swiper-slide-index'), 10) : index;\n        const ariaLabelMessage = params.slideLabelMessage.replace(/\\{\\{index\\}\\}/, slideIndex + 1).replace(/\\{\\{slidesLength\\}\\}/, slidesLength);\n        addElLabel(slideEl, ariaLabelMessage);\n      });\n    }\n  };\n  const init = () => {\n    const params = swiper.params.a11y;\n    if (swiper.isElement) {\n      swiper.el.shadowEl.append(liveRegion);\n    } else {\n      swiper.el.append(liveRegion);\n    }\n\n    // Container\n    const containerEl = swiper.el;\n    if (params.containerRoleDescriptionMessage) {\n      addElRoleDescription(containerEl, params.containerRoleDescriptionMessage);\n    }\n    if (params.containerMessage) {\n      addElLabel(containerEl, params.containerMessage);\n    }\n\n    // Wrapper\n    const wrapperEl = swiper.wrapperEl;\n    const wrapperId = params.id || wrapperEl.getAttribute('id') || `swiper-wrapper-${getRandomNumber(16)}`;\n    const live = swiper.params.autoplay && swiper.params.autoplay.enabled ? 'off' : 'polite';\n    addElId(wrapperEl, wrapperId);\n    addElLive(wrapperEl, live);\n\n    // Slide\n    initSlides();\n\n    // Navigation\n    let {\n      nextEl,\n      prevEl\n    } = swiper.navigation ? swiper.navigation : {};\n    nextEl = makeElementsArray(nextEl);\n    prevEl = makeElementsArray(prevEl);\n    if (nextEl) {\n      nextEl.forEach(el => initNavEl(el, wrapperId, params.nextSlideMessage));\n    }\n    if (prevEl) {\n      prevEl.forEach(el => initNavEl(el, wrapperId, params.prevSlideMessage));\n    }\n\n    // Pagination\n    if (hasClickablePagination()) {\n      const paginationEl = Array.isArray(swiper.pagination.el) ? swiper.pagination.el : [swiper.pagination.el];\n      paginationEl.forEach(el => {\n        el.addEventListener('keydown', onEnterOrSpaceKey);\n      });\n    }\n\n    // Tab focus\n    swiper.el.addEventListener('focus', handleFocus, true);\n    swiper.el.addEventListener('pointerdown', handlePointerDown, true);\n    swiper.el.addEventListener('pointerup', handlePointerUp, true);\n  };\n  function destroy() {\n    if (liveRegion) liveRegion.remove();\n    let {\n      nextEl,\n      prevEl\n    } = swiper.navigation ? swiper.navigation : {};\n    nextEl = makeElementsArray(nextEl);\n    prevEl = makeElementsArray(prevEl);\n    if (nextEl) {\n      nextEl.forEach(el => el.removeEventListener('keydown', onEnterOrSpaceKey));\n    }\n    if (prevEl) {\n      prevEl.forEach(el => el.removeEventListener('keydown', onEnterOrSpaceKey));\n    }\n\n    // Pagination\n    if (hasClickablePagination()) {\n      const paginationEl = Array.isArray(swiper.pagination.el) ? swiper.pagination.el : [swiper.pagination.el];\n      paginationEl.forEach(el => {\n        el.removeEventListener('keydown', onEnterOrSpaceKey);\n      });\n    }\n\n    // Tab focus\n    swiper.el.removeEventListener('focus', handleFocus, true);\n    swiper.el.removeEventListener('pointerdown', handlePointerDown, true);\n    swiper.el.removeEventListener('pointerup', handlePointerUp, true);\n  }\n  on('beforeInit', () => {\n    liveRegion = createElement('span', swiper.params.a11y.notificationClass);\n    liveRegion.setAttribute('aria-live', 'assertive');\n    liveRegion.setAttribute('aria-atomic', 'true');\n  });\n  on('afterInit', () => {\n    if (!swiper.params.a11y.enabled) return;\n    init();\n  });\n  on('slidesLengthChange snapGridLengthChange slidesGridLengthChange', () => {\n    if (!swiper.params.a11y.enabled) return;\n    initSlides();\n  });\n  on('fromEdge toEdge afterInit lock unlock', () => {\n    if (!swiper.params.a11y.enabled) return;\n    updateNavigation();\n  });\n  on('paginationUpdate', () => {\n    if (!swiper.params.a11y.enabled) return;\n    updatePagination();\n  });\n  on('destroy', () => {\n    if (!swiper.params.a11y.enabled) return;\n    destroy();\n  });\n}", "map": {"version": 3, "names": ["classesToSelector", "createElement", "elementIndex", "A11y", "_ref", "swiper", "extendParams", "on", "a11y", "enabled", "notificationClass", "prevSlideMessage", "nextSlideMessage", "firstSlideMessage", "lastSlideMessage", "paginationBulletMessage", "slideLabelMessage", "containerMessage", "containerRoleDescriptionMessage", "itemRoleDescriptionMessage", "slideRole", "id", "clicked", "liveRegion", "notify", "message", "notification", "length", "innerHTML", "makeElementsArray", "el", "Array", "isArray", "filter", "e", "getRandomNumber", "size", "arguments", "undefined", "randomChar", "Math", "round", "random", "toString", "repeat", "replace", "makeElFocusable", "for<PERSON>ach", "subEl", "setAttribute", "makeElNotFocusable", "addElRole", "role", "addElRoleDescription", "description", "addElControls", "controls", "addElLabel", "label", "addElId", "addElLive", "live", "disableEl", "enableEl", "onEnterOrSpaceKey", "keyCode", "params", "targetEl", "target", "pagination", "contains", "matches", "bulletClass", "navigation", "nextEl", "isEnd", "loop", "slideNext", "prevEl", "isBeginning", "slidePrev", "click", "updateNavigation", "rewind", "hasPagination", "bullets", "hasClickablePagination", "clickable", "updatePagination", "bulletEl", "renderBullet", "bulletActiveClass", "removeAttribute", "initNavEl", "wrapperId", "tagName", "addEventListener", "handlePointerDown", "handlePointerUp", "requestAnimationFrame", "destroyed", "handleFocus", "slideEl", "closest", "slideClass", "slides", "includes", "isActive", "indexOf", "activeIndex", "isVisible", "watchSlidesProgress", "visibleSlides", "sourceCapabilities", "firesTouchEvents", "isHorizontal", "scrollLeft", "scrollTop", "slideTo", "initSlides", "<PERSON><PERSON><PERSON><PERSON>", "index", "slideIndex", "parseInt", "getAttribute", "ariaLabelMessage", "init", "isElement", "shadowEl", "append", "containerEl", "wrapperEl", "autoplay", "paginationEl", "destroy", "remove", "removeEventListener"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/modules/a11y/a11y.js"], "sourcesContent": ["import classesToSelector from '../../shared/classes-to-selector.js';\nimport { createElement, elementIndex } from '../../shared/utils.js';\nexport default function A11y({\n  swiper,\n  extendParams,\n  on\n}) {\n  extendParams({\n    a11y: {\n      enabled: true,\n      notificationClass: 'swiper-notification',\n      prevSlideMessage: 'Previous slide',\n      nextSlideMessage: 'Next slide',\n      firstSlideMessage: 'This is the first slide',\n      lastSlideMessage: 'This is the last slide',\n      paginationBulletMessage: 'Go to slide {{index}}',\n      slideLabelMessage: '{{index}} / {{slidesLength}}',\n      containerMessage: null,\n      containerRoleDescriptionMessage: null,\n      itemRoleDescriptionMessage: null,\n      slideRole: 'group',\n      id: null\n    }\n  });\n  swiper.a11y = {\n    clicked: false\n  };\n  let liveRegion = null;\n  function notify(message) {\n    const notification = liveRegion;\n    if (notification.length === 0) return;\n    notification.innerHTML = '';\n    notification.innerHTML = message;\n  }\n  const makeElementsArray = el => {\n    if (!Array.isArray(el)) el = [el].filter(e => !!e);\n    return el;\n  };\n  function getRandomNumber(size = 16) {\n    const randomChar = () => Math.round(16 * Math.random()).toString(16);\n    return 'x'.repeat(size).replace(/x/g, randomChar);\n  }\n  function makeElFocusable(el) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('tabIndex', '0');\n    });\n  }\n  function makeElNotFocusable(el) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('tabIndex', '-1');\n    });\n  }\n  function addElRole(el, role) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('role', role);\n    });\n  }\n  function addElRoleDescription(el, description) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('aria-roledescription', description);\n    });\n  }\n  function addElControls(el, controls) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('aria-controls', controls);\n    });\n  }\n  function addElLabel(el, label) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('aria-label', label);\n    });\n  }\n  function addElId(el, id) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('id', id);\n    });\n  }\n  function addElLive(el, live) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('aria-live', live);\n    });\n  }\n  function disableEl(el) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('aria-disabled', true);\n    });\n  }\n  function enableEl(el) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('aria-disabled', false);\n    });\n  }\n  function onEnterOrSpaceKey(e) {\n    if (e.keyCode !== 13 && e.keyCode !== 32) return;\n    const params = swiper.params.a11y;\n    const targetEl = e.target;\n    if (swiper.pagination && swiper.pagination.el && (targetEl === swiper.pagination.el || swiper.pagination.el.contains(e.target))) {\n      if (!e.target.matches(classesToSelector(swiper.params.pagination.bulletClass))) return;\n    }\n    if (swiper.navigation && swiper.navigation.nextEl && targetEl === swiper.navigation.nextEl) {\n      if (!(swiper.isEnd && !swiper.params.loop)) {\n        swiper.slideNext();\n      }\n      if (swiper.isEnd) {\n        notify(params.lastSlideMessage);\n      } else {\n        notify(params.nextSlideMessage);\n      }\n    }\n    if (swiper.navigation && swiper.navigation.prevEl && targetEl === swiper.navigation.prevEl) {\n      if (!(swiper.isBeginning && !swiper.params.loop)) {\n        swiper.slidePrev();\n      }\n      if (swiper.isBeginning) {\n        notify(params.firstSlideMessage);\n      } else {\n        notify(params.prevSlideMessage);\n      }\n    }\n    if (swiper.pagination && targetEl.matches(classesToSelector(swiper.params.pagination.bulletClass))) {\n      targetEl.click();\n    }\n  }\n  function updateNavigation() {\n    if (swiper.params.loop || swiper.params.rewind || !swiper.navigation) return;\n    const {\n      nextEl,\n      prevEl\n    } = swiper.navigation;\n    if (prevEl) {\n      if (swiper.isBeginning) {\n        disableEl(prevEl);\n        makeElNotFocusable(prevEl);\n      } else {\n        enableEl(prevEl);\n        makeElFocusable(prevEl);\n      }\n    }\n    if (nextEl) {\n      if (swiper.isEnd) {\n        disableEl(nextEl);\n        makeElNotFocusable(nextEl);\n      } else {\n        enableEl(nextEl);\n        makeElFocusable(nextEl);\n      }\n    }\n  }\n  function hasPagination() {\n    return swiper.pagination && swiper.pagination.bullets && swiper.pagination.bullets.length;\n  }\n  function hasClickablePagination() {\n    return hasPagination() && swiper.params.pagination.clickable;\n  }\n  function updatePagination() {\n    const params = swiper.params.a11y;\n    if (!hasPagination()) return;\n    swiper.pagination.bullets.forEach(bulletEl => {\n      if (swiper.params.pagination.clickable) {\n        makeElFocusable(bulletEl);\n        if (!swiper.params.pagination.renderBullet) {\n          addElRole(bulletEl, 'button');\n          addElLabel(bulletEl, params.paginationBulletMessage.replace(/\\{\\{index\\}\\}/, elementIndex(bulletEl) + 1));\n        }\n      }\n      if (bulletEl.matches(classesToSelector(swiper.params.pagination.bulletActiveClass))) {\n        bulletEl.setAttribute('aria-current', 'true');\n      } else {\n        bulletEl.removeAttribute('aria-current');\n      }\n    });\n  }\n  const initNavEl = (el, wrapperId, message) => {\n    makeElFocusable(el);\n    if (el.tagName !== 'BUTTON') {\n      addElRole(el, 'button');\n      el.addEventListener('keydown', onEnterOrSpaceKey);\n    }\n    addElLabel(el, message);\n    addElControls(el, wrapperId);\n  };\n  const handlePointerDown = () => {\n    swiper.a11y.clicked = true;\n  };\n  const handlePointerUp = () => {\n    requestAnimationFrame(() => {\n      requestAnimationFrame(() => {\n        if (!swiper.destroyed) {\n          swiper.a11y.clicked = false;\n        }\n      });\n    });\n  };\n  const handleFocus = e => {\n    if (swiper.a11y.clicked) return;\n    const slideEl = e.target.closest(`.${swiper.params.slideClass}, swiper-slide`);\n    if (!slideEl || !swiper.slides.includes(slideEl)) return;\n    const isActive = swiper.slides.indexOf(slideEl) === swiper.activeIndex;\n    const isVisible = swiper.params.watchSlidesProgress && swiper.visibleSlides && swiper.visibleSlides.includes(slideEl);\n    if (isActive || isVisible) return;\n    if (e.sourceCapabilities && e.sourceCapabilities.firesTouchEvents) return;\n    if (swiper.isHorizontal()) {\n      swiper.el.scrollLeft = 0;\n    } else {\n      swiper.el.scrollTop = 0;\n    }\n    swiper.slideTo(swiper.slides.indexOf(slideEl), 0);\n  };\n  const initSlides = () => {\n    const params = swiper.params.a11y;\n    if (params.itemRoleDescriptionMessage) {\n      addElRoleDescription(swiper.slides, params.itemRoleDescriptionMessage);\n    }\n    if (params.slideRole) {\n      addElRole(swiper.slides, params.slideRole);\n    }\n    const slidesLength = swiper.slides.length;\n    if (params.slideLabelMessage) {\n      swiper.slides.forEach((slideEl, index) => {\n        const slideIndex = swiper.params.loop ? parseInt(slideEl.getAttribute('data-swiper-slide-index'), 10) : index;\n        const ariaLabelMessage = params.slideLabelMessage.replace(/\\{\\{index\\}\\}/, slideIndex + 1).replace(/\\{\\{slidesLength\\}\\}/, slidesLength);\n        addElLabel(slideEl, ariaLabelMessage);\n      });\n    }\n  };\n  const init = () => {\n    const params = swiper.params.a11y;\n    if (swiper.isElement) {\n      swiper.el.shadowEl.append(liveRegion);\n    } else {\n      swiper.el.append(liveRegion);\n    }\n\n    // Container\n    const containerEl = swiper.el;\n    if (params.containerRoleDescriptionMessage) {\n      addElRoleDescription(containerEl, params.containerRoleDescriptionMessage);\n    }\n    if (params.containerMessage) {\n      addElLabel(containerEl, params.containerMessage);\n    }\n\n    // Wrapper\n    const wrapperEl = swiper.wrapperEl;\n    const wrapperId = params.id || wrapperEl.getAttribute('id') || `swiper-wrapper-${getRandomNumber(16)}`;\n    const live = swiper.params.autoplay && swiper.params.autoplay.enabled ? 'off' : 'polite';\n    addElId(wrapperEl, wrapperId);\n    addElLive(wrapperEl, live);\n\n    // Slide\n    initSlides();\n\n    // Navigation\n    let {\n      nextEl,\n      prevEl\n    } = swiper.navigation ? swiper.navigation : {};\n    nextEl = makeElementsArray(nextEl);\n    prevEl = makeElementsArray(prevEl);\n    if (nextEl) {\n      nextEl.forEach(el => initNavEl(el, wrapperId, params.nextSlideMessage));\n    }\n    if (prevEl) {\n      prevEl.forEach(el => initNavEl(el, wrapperId, params.prevSlideMessage));\n    }\n\n    // Pagination\n    if (hasClickablePagination()) {\n      const paginationEl = Array.isArray(swiper.pagination.el) ? swiper.pagination.el : [swiper.pagination.el];\n      paginationEl.forEach(el => {\n        el.addEventListener('keydown', onEnterOrSpaceKey);\n      });\n    }\n\n    // Tab focus\n    swiper.el.addEventListener('focus', handleFocus, true);\n    swiper.el.addEventListener('pointerdown', handlePointerDown, true);\n    swiper.el.addEventListener('pointerup', handlePointerUp, true);\n  };\n  function destroy() {\n    if (liveRegion) liveRegion.remove();\n    let {\n      nextEl,\n      prevEl\n    } = swiper.navigation ? swiper.navigation : {};\n    nextEl = makeElementsArray(nextEl);\n    prevEl = makeElementsArray(prevEl);\n    if (nextEl) {\n      nextEl.forEach(el => el.removeEventListener('keydown', onEnterOrSpaceKey));\n    }\n    if (prevEl) {\n      prevEl.forEach(el => el.removeEventListener('keydown', onEnterOrSpaceKey));\n    }\n\n    // Pagination\n    if (hasClickablePagination()) {\n      const paginationEl = Array.isArray(swiper.pagination.el) ? swiper.pagination.el : [swiper.pagination.el];\n      paginationEl.forEach(el => {\n        el.removeEventListener('keydown', onEnterOrSpaceKey);\n      });\n    }\n\n    // Tab focus\n    swiper.el.removeEventListener('focus', handleFocus, true);\n    swiper.el.removeEventListener('pointerdown', handlePointerDown, true);\n    swiper.el.removeEventListener('pointerup', handlePointerUp, true);\n  }\n  on('beforeInit', () => {\n    liveRegion = createElement('span', swiper.params.a11y.notificationClass);\n    liveRegion.setAttribute('aria-live', 'assertive');\n    liveRegion.setAttribute('aria-atomic', 'true');\n  });\n  on('afterInit', () => {\n    if (!swiper.params.a11y.enabled) return;\n    init();\n  });\n  on('slidesLengthChange snapGridLengthChange slidesGridLengthChange', () => {\n    if (!swiper.params.a11y.enabled) return;\n    initSlides();\n  });\n  on('fromEdge toEdge afterInit lock unlock', () => {\n    if (!swiper.params.a11y.enabled) return;\n    updateNavigation();\n  });\n  on('paginationUpdate', () => {\n    if (!swiper.params.a11y.enabled) return;\n    updatePagination();\n  });\n  on('destroy', () => {\n    if (!swiper.params.a11y.enabled) return;\n    destroy();\n  });\n}"], "mappings": "AAAA,OAAOA,iBAAiB,MAAM,qCAAqC;AACnE,SAASC,aAAa,EAAEC,YAAY,QAAQ,uBAAuB;AACnE,eAAe,SAASC,IAAIA,CAAAC,IAAA,EAIzB;EAAA,IAJ0B;IAC3BC,MAAM;IACNC,YAAY;IACZC;EACF,CAAC,GAAAH,IAAA;EACCE,YAAY,CAAC;IACXE,IAAI,EAAE;MACJC,OAAO,EAAE,IAAI;MACbC,iBAAiB,EAAE,qBAAqB;MACxCC,gBAAgB,EAAE,gBAAgB;MAClCC,gBAAgB,EAAE,YAAY;MAC9BC,iBAAiB,EAAE,yBAAyB;MAC5CC,gBAAgB,EAAE,wBAAwB;MAC1CC,uBAAuB,EAAE,uBAAuB;MAChDC,iBAAiB,EAAE,8BAA8B;MACjDC,gBAAgB,EAAE,IAAI;MACtBC,+BAA+B,EAAE,IAAI;MACrCC,0BAA0B,EAAE,IAAI;MAChCC,SAAS,EAAE,OAAO;MAClBC,EAAE,EAAE;IACN;EACF,CAAC,CAAC;EACFhB,MAAM,CAACG,IAAI,GAAG;IACZc,OAAO,EAAE;EACX,CAAC;EACD,IAAIC,UAAU,GAAG,IAAI;EACrB,SAASC,MAAMA,CAACC,OAAO,EAAE;IACvB,MAAMC,YAAY,GAAGH,UAAU;IAC/B,IAAIG,YAAY,CAACC,MAAM,KAAK,CAAC,EAAE;IAC/BD,YAAY,CAACE,SAAS,GAAG,EAAE;IAC3BF,YAAY,CAACE,SAAS,GAAGH,OAAO;EAClC;EACA,MAAMI,iBAAiB,GAAGC,EAAE,IAAI;IAC9B,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,EAAE,CAAC,EAAEA,EAAE,GAAG,CAACA,EAAE,CAAC,CAACG,MAAM,CAACC,CAAC,IAAI,CAAC,CAACA,CAAC,CAAC;IAClD,OAAOJ,EAAE;EACX,CAAC;EACD,SAASK,eAAeA,CAAA,EAAY;IAAA,IAAXC,IAAI,GAAAC,SAAA,CAAAV,MAAA,QAAAU,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,EAAE;IAChC,MAAME,UAAU,GAAGA,CAAA,KAAMC,IAAI,CAACC,KAAK,CAAC,EAAE,GAAGD,IAAI,CAACE,MAAM,EAAE,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC;IACpE,OAAO,GAAG,CAACC,MAAM,CAACR,IAAI,CAAC,CAACS,OAAO,CAAC,IAAI,EAAEN,UAAU,CAAC;EACnD;EACA,SAASO,eAAeA,CAAChB,EAAE,EAAE;IAC3BA,EAAE,GAAGD,iBAAiB,CAACC,EAAE,CAAC;IAC1BA,EAAE,CAACiB,OAAO,CAACC,KAAK,IAAI;MAClBA,KAAK,CAACC,YAAY,CAAC,UAAU,EAAE,GAAG,CAAC;IACrC,CAAC,CAAC;EACJ;EACA,SAASC,kBAAkBA,CAACpB,EAAE,EAAE;IAC9BA,EAAE,GAAGD,iBAAiB,CAACC,EAAE,CAAC;IAC1BA,EAAE,CAACiB,OAAO,CAACC,KAAK,IAAI;MAClBA,KAAK,CAACC,YAAY,CAAC,UAAU,EAAE,IAAI,CAAC;IACtC,CAAC,CAAC;EACJ;EACA,SAASE,SAASA,CAACrB,EAAE,EAAEsB,IAAI,EAAE;IAC3BtB,EAAE,GAAGD,iBAAiB,CAACC,EAAE,CAAC;IAC1BA,EAAE,CAACiB,OAAO,CAACC,KAAK,IAAI;MAClBA,KAAK,CAACC,YAAY,CAAC,MAAM,EAAEG,IAAI,CAAC;IAClC,CAAC,CAAC;EACJ;EACA,SAASC,oBAAoBA,CAACvB,EAAE,EAAEwB,WAAW,EAAE;IAC7CxB,EAAE,GAAGD,iBAAiB,CAACC,EAAE,CAAC;IAC1BA,EAAE,CAACiB,OAAO,CAACC,KAAK,IAAI;MAClBA,KAAK,CAACC,YAAY,CAAC,sBAAsB,EAAEK,WAAW,CAAC;IACzD,CAAC,CAAC;EACJ;EACA,SAASC,aAAaA,CAACzB,EAAE,EAAE0B,QAAQ,EAAE;IACnC1B,EAAE,GAAGD,iBAAiB,CAACC,EAAE,CAAC;IAC1BA,EAAE,CAACiB,OAAO,CAACC,KAAK,IAAI;MAClBA,KAAK,CAACC,YAAY,CAAC,eAAe,EAAEO,QAAQ,CAAC;IAC/C,CAAC,CAAC;EACJ;EACA,SAASC,UAAUA,CAAC3B,EAAE,EAAE4B,KAAK,EAAE;IAC7B5B,EAAE,GAAGD,iBAAiB,CAACC,EAAE,CAAC;IAC1BA,EAAE,CAACiB,OAAO,CAACC,KAAK,IAAI;MAClBA,KAAK,CAACC,YAAY,CAAC,YAAY,EAAES,KAAK,CAAC;IACzC,CAAC,CAAC;EACJ;EACA,SAASC,OAAOA,CAAC7B,EAAE,EAAET,EAAE,EAAE;IACvBS,EAAE,GAAGD,iBAAiB,CAACC,EAAE,CAAC;IAC1BA,EAAE,CAACiB,OAAO,CAACC,KAAK,IAAI;MAClBA,KAAK,CAACC,YAAY,CAAC,IAAI,EAAE5B,EAAE,CAAC;IAC9B,CAAC,CAAC;EACJ;EACA,SAASuC,SAASA,CAAC9B,EAAE,EAAE+B,IAAI,EAAE;IAC3B/B,EAAE,GAAGD,iBAAiB,CAACC,EAAE,CAAC;IAC1BA,EAAE,CAACiB,OAAO,CAACC,KAAK,IAAI;MAClBA,KAAK,CAACC,YAAY,CAAC,WAAW,EAAEY,IAAI,CAAC;IACvC,CAAC,CAAC;EACJ;EACA,SAASC,SAASA,CAAChC,EAAE,EAAE;IACrBA,EAAE,GAAGD,iBAAiB,CAACC,EAAE,CAAC;IAC1BA,EAAE,CAACiB,OAAO,CAACC,KAAK,IAAI;MAClBA,KAAK,CAACC,YAAY,CAAC,eAAe,EAAE,IAAI,CAAC;IAC3C,CAAC,CAAC;EACJ;EACA,SAASc,QAAQA,CAACjC,EAAE,EAAE;IACpBA,EAAE,GAAGD,iBAAiB,CAACC,EAAE,CAAC;IAC1BA,EAAE,CAACiB,OAAO,CAACC,KAAK,IAAI;MAClBA,KAAK,CAACC,YAAY,CAAC,eAAe,EAAE,KAAK,CAAC;IAC5C,CAAC,CAAC;EACJ;EACA,SAASe,iBAAiBA,CAAC9B,CAAC,EAAE;IAC5B,IAAIA,CAAC,CAAC+B,OAAO,KAAK,EAAE,IAAI/B,CAAC,CAAC+B,OAAO,KAAK,EAAE,EAAE;IAC1C,MAAMC,MAAM,GAAG7D,MAAM,CAAC6D,MAAM,CAAC1D,IAAI;IACjC,MAAM2D,QAAQ,GAAGjC,CAAC,CAACkC,MAAM;IACzB,IAAI/D,MAAM,CAACgE,UAAU,IAAIhE,MAAM,CAACgE,UAAU,CAACvC,EAAE,KAAKqC,QAAQ,KAAK9D,MAAM,CAACgE,UAAU,CAACvC,EAAE,IAAIzB,MAAM,CAACgE,UAAU,CAACvC,EAAE,CAACwC,QAAQ,CAACpC,CAAC,CAACkC,MAAM,CAAC,CAAC,EAAE;MAC/H,IAAI,CAAClC,CAAC,CAACkC,MAAM,CAACG,OAAO,CAACvE,iBAAiB,CAACK,MAAM,CAAC6D,MAAM,CAACG,UAAU,CAACG,WAAW,CAAC,CAAC,EAAE;IAClF;IACA,IAAInE,MAAM,CAACoE,UAAU,IAAIpE,MAAM,CAACoE,UAAU,CAACC,MAAM,IAAIP,QAAQ,KAAK9D,MAAM,CAACoE,UAAU,CAACC,MAAM,EAAE;MAC1F,IAAI,EAAErE,MAAM,CAACsE,KAAK,IAAI,CAACtE,MAAM,CAAC6D,MAAM,CAACU,IAAI,CAAC,EAAE;QAC1CvE,MAAM,CAACwE,SAAS,EAAE;MACpB;MACA,IAAIxE,MAAM,CAACsE,KAAK,EAAE;QAChBnD,MAAM,CAAC0C,MAAM,CAACpD,gBAAgB,CAAC;MACjC,CAAC,MAAM;QACLU,MAAM,CAAC0C,MAAM,CAACtD,gBAAgB,CAAC;MACjC;IACF;IACA,IAAIP,MAAM,CAACoE,UAAU,IAAIpE,MAAM,CAACoE,UAAU,CAACK,MAAM,IAAIX,QAAQ,KAAK9D,MAAM,CAACoE,UAAU,CAACK,MAAM,EAAE;MAC1F,IAAI,EAAEzE,MAAM,CAAC0E,WAAW,IAAI,CAAC1E,MAAM,CAAC6D,MAAM,CAACU,IAAI,CAAC,EAAE;QAChDvE,MAAM,CAAC2E,SAAS,EAAE;MACpB;MACA,IAAI3E,MAAM,CAAC0E,WAAW,EAAE;QACtBvD,MAAM,CAAC0C,MAAM,CAACrD,iBAAiB,CAAC;MAClC,CAAC,MAAM;QACLW,MAAM,CAAC0C,MAAM,CAACvD,gBAAgB,CAAC;MACjC;IACF;IACA,IAAIN,MAAM,CAACgE,UAAU,IAAIF,QAAQ,CAACI,OAAO,CAACvE,iBAAiB,CAACK,MAAM,CAAC6D,MAAM,CAACG,UAAU,CAACG,WAAW,CAAC,CAAC,EAAE;MAClGL,QAAQ,CAACc,KAAK,EAAE;IAClB;EACF;EACA,SAASC,gBAAgBA,CAAA,EAAG;IAC1B,IAAI7E,MAAM,CAAC6D,MAAM,CAACU,IAAI,IAAIvE,MAAM,CAAC6D,MAAM,CAACiB,MAAM,IAAI,CAAC9E,MAAM,CAACoE,UAAU,EAAE;IACtE,MAAM;MACJC,MAAM;MACNI;IACF,CAAC,GAAGzE,MAAM,CAACoE,UAAU;IACrB,IAAIK,MAAM,EAAE;MACV,IAAIzE,MAAM,CAAC0E,WAAW,EAAE;QACtBjB,SAAS,CAACgB,MAAM,CAAC;QACjB5B,kBAAkB,CAAC4B,MAAM,CAAC;MAC5B,CAAC,MAAM;QACLf,QAAQ,CAACe,MAAM,CAAC;QAChBhC,eAAe,CAACgC,MAAM,CAAC;MACzB;IACF;IACA,IAAIJ,MAAM,EAAE;MACV,IAAIrE,MAAM,CAACsE,KAAK,EAAE;QAChBb,SAAS,CAACY,MAAM,CAAC;QACjBxB,kBAAkB,CAACwB,MAAM,CAAC;MAC5B,CAAC,MAAM;QACLX,QAAQ,CAACW,MAAM,CAAC;QAChB5B,eAAe,CAAC4B,MAAM,CAAC;MACzB;IACF;EACF;EACA,SAASU,aAAaA,CAAA,EAAG;IACvB,OAAO/E,MAAM,CAACgE,UAAU,IAAIhE,MAAM,CAACgE,UAAU,CAACgB,OAAO,IAAIhF,MAAM,CAACgE,UAAU,CAACgB,OAAO,CAAC1D,MAAM;EAC3F;EACA,SAAS2D,sBAAsBA,CAAA,EAAG;IAChC,OAAOF,aAAa,EAAE,IAAI/E,MAAM,CAAC6D,MAAM,CAACG,UAAU,CAACkB,SAAS;EAC9D;EACA,SAASC,gBAAgBA,CAAA,EAAG;IAC1B,MAAMtB,MAAM,GAAG7D,MAAM,CAAC6D,MAAM,CAAC1D,IAAI;IACjC,IAAI,CAAC4E,aAAa,EAAE,EAAE;IACtB/E,MAAM,CAACgE,UAAU,CAACgB,OAAO,CAACtC,OAAO,CAAC0C,QAAQ,IAAI;MAC5C,IAAIpF,MAAM,CAAC6D,MAAM,CAACG,UAAU,CAACkB,SAAS,EAAE;QACtCzC,eAAe,CAAC2C,QAAQ,CAAC;QACzB,IAAI,CAACpF,MAAM,CAAC6D,MAAM,CAACG,UAAU,CAACqB,YAAY,EAAE;UAC1CvC,SAAS,CAACsC,QAAQ,EAAE,QAAQ,CAAC;UAC7BhC,UAAU,CAACgC,QAAQ,EAAEvB,MAAM,CAACnD,uBAAuB,CAAC8B,OAAO,CAAC,eAAe,EAAE3C,YAAY,CAACuF,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;QAC3G;MACF;MACA,IAAIA,QAAQ,CAAClB,OAAO,CAACvE,iBAAiB,CAACK,MAAM,CAAC6D,MAAM,CAACG,UAAU,CAACsB,iBAAiB,CAAC,CAAC,EAAE;QACnFF,QAAQ,CAACxC,YAAY,CAAC,cAAc,EAAE,MAAM,CAAC;MAC/C,CAAC,MAAM;QACLwC,QAAQ,CAACG,eAAe,CAAC,cAAc,CAAC;MAC1C;IACF,CAAC,CAAC;EACJ;EACA,MAAMC,SAAS,GAAGA,CAAC/D,EAAE,EAAEgE,SAAS,EAAErE,OAAO,KAAK;IAC5CqB,eAAe,CAAChB,EAAE,CAAC;IACnB,IAAIA,EAAE,CAACiE,OAAO,KAAK,QAAQ,EAAE;MAC3B5C,SAAS,CAACrB,EAAE,EAAE,QAAQ,CAAC;MACvBA,EAAE,CAACkE,gBAAgB,CAAC,SAAS,EAAEhC,iBAAiB,CAAC;IACnD;IACAP,UAAU,CAAC3B,EAAE,EAAEL,OAAO,CAAC;IACvB8B,aAAa,CAACzB,EAAE,EAAEgE,SAAS,CAAC;EAC9B,CAAC;EACD,MAAMG,iBAAiB,GAAGA,CAAA,KAAM;IAC9B5F,MAAM,CAACG,IAAI,CAACc,OAAO,GAAG,IAAI;EAC5B,CAAC;EACD,MAAM4E,eAAe,GAAGA,CAAA,KAAM;IAC5BC,qBAAqB,CAAC,MAAM;MAC1BA,qBAAqB,CAAC,MAAM;QAC1B,IAAI,CAAC9F,MAAM,CAAC+F,SAAS,EAAE;UACrB/F,MAAM,CAACG,IAAI,CAACc,OAAO,GAAG,KAAK;QAC7B;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EACD,MAAM+E,WAAW,GAAGnE,CAAC,IAAI;IACvB,IAAI7B,MAAM,CAACG,IAAI,CAACc,OAAO,EAAE;IACzB,MAAMgF,OAAO,GAAGpE,CAAC,CAACkC,MAAM,CAACmC,OAAO,CAAE,IAAGlG,MAAM,CAAC6D,MAAM,CAACsC,UAAW,gBAAe,CAAC;IAC9E,IAAI,CAACF,OAAO,IAAI,CAACjG,MAAM,CAACoG,MAAM,CAACC,QAAQ,CAACJ,OAAO,CAAC,EAAE;IAClD,MAAMK,QAAQ,GAAGtG,MAAM,CAACoG,MAAM,CAACG,OAAO,CAACN,OAAO,CAAC,KAAKjG,MAAM,CAACwG,WAAW;IACtE,MAAMC,SAAS,GAAGzG,MAAM,CAAC6D,MAAM,CAAC6C,mBAAmB,IAAI1G,MAAM,CAAC2G,aAAa,IAAI3G,MAAM,CAAC2G,aAAa,CAACN,QAAQ,CAACJ,OAAO,CAAC;IACrH,IAAIK,QAAQ,IAAIG,SAAS,EAAE;IAC3B,IAAI5E,CAAC,CAAC+E,kBAAkB,IAAI/E,CAAC,CAAC+E,kBAAkB,CAACC,gBAAgB,EAAE;IACnE,IAAI7G,MAAM,CAAC8G,YAAY,EAAE,EAAE;MACzB9G,MAAM,CAACyB,EAAE,CAACsF,UAAU,GAAG,CAAC;IAC1B,CAAC,MAAM;MACL/G,MAAM,CAACyB,EAAE,CAACuF,SAAS,GAAG,CAAC;IACzB;IACAhH,MAAM,CAACiH,OAAO,CAACjH,MAAM,CAACoG,MAAM,CAACG,OAAO,CAACN,OAAO,CAAC,EAAE,CAAC,CAAC;EACnD,CAAC;EACD,MAAMiB,UAAU,GAAGA,CAAA,KAAM;IACvB,MAAMrD,MAAM,GAAG7D,MAAM,CAAC6D,MAAM,CAAC1D,IAAI;IACjC,IAAI0D,MAAM,CAAC/C,0BAA0B,EAAE;MACrCkC,oBAAoB,CAAChD,MAAM,CAACoG,MAAM,EAAEvC,MAAM,CAAC/C,0BAA0B,CAAC;IACxE;IACA,IAAI+C,MAAM,CAAC9C,SAAS,EAAE;MACpB+B,SAAS,CAAC9C,MAAM,CAACoG,MAAM,EAAEvC,MAAM,CAAC9C,SAAS,CAAC;IAC5C;IACA,MAAMoG,YAAY,GAAGnH,MAAM,CAACoG,MAAM,CAAC9E,MAAM;IACzC,IAAIuC,MAAM,CAAClD,iBAAiB,EAAE;MAC5BX,MAAM,CAACoG,MAAM,CAAC1D,OAAO,CAAC,CAACuD,OAAO,EAAEmB,KAAK,KAAK;QACxC,MAAMC,UAAU,GAAGrH,MAAM,CAAC6D,MAAM,CAACU,IAAI,GAAG+C,QAAQ,CAACrB,OAAO,CAACsB,YAAY,CAAC,yBAAyB,CAAC,EAAE,EAAE,CAAC,GAAGH,KAAK;QAC7G,MAAMI,gBAAgB,GAAG3D,MAAM,CAAClD,iBAAiB,CAAC6B,OAAO,CAAC,eAAe,EAAE6E,UAAU,GAAG,CAAC,CAAC,CAAC7E,OAAO,CAAC,sBAAsB,EAAE2E,YAAY,CAAC;QACxI/D,UAAU,CAAC6C,OAAO,EAAEuB,gBAAgB,CAAC;MACvC,CAAC,CAAC;IACJ;EACF,CAAC;EACD,MAAMC,IAAI,GAAGA,CAAA,KAAM;IACjB,MAAM5D,MAAM,GAAG7D,MAAM,CAAC6D,MAAM,CAAC1D,IAAI;IACjC,IAAIH,MAAM,CAAC0H,SAAS,EAAE;MACpB1H,MAAM,CAACyB,EAAE,CAACkG,QAAQ,CAACC,MAAM,CAAC1G,UAAU,CAAC;IACvC,CAAC,MAAM;MACLlB,MAAM,CAACyB,EAAE,CAACmG,MAAM,CAAC1G,UAAU,CAAC;IAC9B;;IAEA;IACA,MAAM2G,WAAW,GAAG7H,MAAM,CAACyB,EAAE;IAC7B,IAAIoC,MAAM,CAAChD,+BAA+B,EAAE;MAC1CmC,oBAAoB,CAAC6E,WAAW,EAAEhE,MAAM,CAAChD,+BAA+B,CAAC;IAC3E;IACA,IAAIgD,MAAM,CAACjD,gBAAgB,EAAE;MAC3BwC,UAAU,CAACyE,WAAW,EAAEhE,MAAM,CAACjD,gBAAgB,CAAC;IAClD;;IAEA;IACA,MAAMkH,SAAS,GAAG9H,MAAM,CAAC8H,SAAS;IAClC,MAAMrC,SAAS,GAAG5B,MAAM,CAAC7C,EAAE,IAAI8G,SAAS,CAACP,YAAY,CAAC,IAAI,CAAC,IAAK,kBAAiBzF,eAAe,CAAC,EAAE,CAAE,EAAC;IACtG,MAAM0B,IAAI,GAAGxD,MAAM,CAAC6D,MAAM,CAACkE,QAAQ,IAAI/H,MAAM,CAAC6D,MAAM,CAACkE,QAAQ,CAAC3H,OAAO,GAAG,KAAK,GAAG,QAAQ;IACxFkD,OAAO,CAACwE,SAAS,EAAErC,SAAS,CAAC;IAC7BlC,SAAS,CAACuE,SAAS,EAAEtE,IAAI,CAAC;;IAE1B;IACA0D,UAAU,EAAE;;IAEZ;IACA,IAAI;MACF7C,MAAM;MACNI;IACF,CAAC,GAAGzE,MAAM,CAACoE,UAAU,GAAGpE,MAAM,CAACoE,UAAU,GAAG,CAAC,CAAC;IAC9CC,MAAM,GAAG7C,iBAAiB,CAAC6C,MAAM,CAAC;IAClCI,MAAM,GAAGjD,iBAAiB,CAACiD,MAAM,CAAC;IAClC,IAAIJ,MAAM,EAAE;MACVA,MAAM,CAAC3B,OAAO,CAACjB,EAAE,IAAI+D,SAAS,CAAC/D,EAAE,EAAEgE,SAAS,EAAE5B,MAAM,CAACtD,gBAAgB,CAAC,CAAC;IACzE;IACA,IAAIkE,MAAM,EAAE;MACVA,MAAM,CAAC/B,OAAO,CAACjB,EAAE,IAAI+D,SAAS,CAAC/D,EAAE,EAAEgE,SAAS,EAAE5B,MAAM,CAACvD,gBAAgB,CAAC,CAAC;IACzE;;IAEA;IACA,IAAI2E,sBAAsB,EAAE,EAAE;MAC5B,MAAM+C,YAAY,GAAGtG,KAAK,CAACC,OAAO,CAAC3B,MAAM,CAACgE,UAAU,CAACvC,EAAE,CAAC,GAAGzB,MAAM,CAACgE,UAAU,CAACvC,EAAE,GAAG,CAACzB,MAAM,CAACgE,UAAU,CAACvC,EAAE,CAAC;MACxGuG,YAAY,CAACtF,OAAO,CAACjB,EAAE,IAAI;QACzBA,EAAE,CAACkE,gBAAgB,CAAC,SAAS,EAAEhC,iBAAiB,CAAC;MACnD,CAAC,CAAC;IACJ;;IAEA;IACA3D,MAAM,CAACyB,EAAE,CAACkE,gBAAgB,CAAC,OAAO,EAAEK,WAAW,EAAE,IAAI,CAAC;IACtDhG,MAAM,CAACyB,EAAE,CAACkE,gBAAgB,CAAC,aAAa,EAAEC,iBAAiB,EAAE,IAAI,CAAC;IAClE5F,MAAM,CAACyB,EAAE,CAACkE,gBAAgB,CAAC,WAAW,EAAEE,eAAe,EAAE,IAAI,CAAC;EAChE,CAAC;EACD,SAASoC,OAAOA,CAAA,EAAG;IACjB,IAAI/G,UAAU,EAAEA,UAAU,CAACgH,MAAM,EAAE;IACnC,IAAI;MACF7D,MAAM;MACNI;IACF,CAAC,GAAGzE,MAAM,CAACoE,UAAU,GAAGpE,MAAM,CAACoE,UAAU,GAAG,CAAC,CAAC;IAC9CC,MAAM,GAAG7C,iBAAiB,CAAC6C,MAAM,CAAC;IAClCI,MAAM,GAAGjD,iBAAiB,CAACiD,MAAM,CAAC;IAClC,IAAIJ,MAAM,EAAE;MACVA,MAAM,CAAC3B,OAAO,CAACjB,EAAE,IAAIA,EAAE,CAAC0G,mBAAmB,CAAC,SAAS,EAAExE,iBAAiB,CAAC,CAAC;IAC5E;IACA,IAAIc,MAAM,EAAE;MACVA,MAAM,CAAC/B,OAAO,CAACjB,EAAE,IAAIA,EAAE,CAAC0G,mBAAmB,CAAC,SAAS,EAAExE,iBAAiB,CAAC,CAAC;IAC5E;;IAEA;IACA,IAAIsB,sBAAsB,EAAE,EAAE;MAC5B,MAAM+C,YAAY,GAAGtG,KAAK,CAACC,OAAO,CAAC3B,MAAM,CAACgE,UAAU,CAACvC,EAAE,CAAC,GAAGzB,MAAM,CAACgE,UAAU,CAACvC,EAAE,GAAG,CAACzB,MAAM,CAACgE,UAAU,CAACvC,EAAE,CAAC;MACxGuG,YAAY,CAACtF,OAAO,CAACjB,EAAE,IAAI;QACzBA,EAAE,CAAC0G,mBAAmB,CAAC,SAAS,EAAExE,iBAAiB,CAAC;MACtD,CAAC,CAAC;IACJ;;IAEA;IACA3D,MAAM,CAACyB,EAAE,CAAC0G,mBAAmB,CAAC,OAAO,EAAEnC,WAAW,EAAE,IAAI,CAAC;IACzDhG,MAAM,CAACyB,EAAE,CAAC0G,mBAAmB,CAAC,aAAa,EAAEvC,iBAAiB,EAAE,IAAI,CAAC;IACrE5F,MAAM,CAACyB,EAAE,CAAC0G,mBAAmB,CAAC,WAAW,EAAEtC,eAAe,EAAE,IAAI,CAAC;EACnE;EACA3F,EAAE,CAAC,YAAY,EAAE,MAAM;IACrBgB,UAAU,GAAGtB,aAAa,CAAC,MAAM,EAAEI,MAAM,CAAC6D,MAAM,CAAC1D,IAAI,CAACE,iBAAiB,CAAC;IACxEa,UAAU,CAAC0B,YAAY,CAAC,WAAW,EAAE,WAAW,CAAC;IACjD1B,UAAU,CAAC0B,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;EAChD,CAAC,CAAC;EACF1C,EAAE,CAAC,WAAW,EAAE,MAAM;IACpB,IAAI,CAACF,MAAM,CAAC6D,MAAM,CAAC1D,IAAI,CAACC,OAAO,EAAE;IACjCqH,IAAI,EAAE;EACR,CAAC,CAAC;EACFvH,EAAE,CAAC,gEAAgE,EAAE,MAAM;IACzE,IAAI,CAACF,MAAM,CAAC6D,MAAM,CAAC1D,IAAI,CAACC,OAAO,EAAE;IACjC8G,UAAU,EAAE;EACd,CAAC,CAAC;EACFhH,EAAE,CAAC,uCAAuC,EAAE,MAAM;IAChD,IAAI,CAACF,MAAM,CAAC6D,MAAM,CAAC1D,IAAI,CAACC,OAAO,EAAE;IACjCyE,gBAAgB,EAAE;EACpB,CAAC,CAAC;EACF3E,EAAE,CAAC,kBAAkB,EAAE,MAAM;IAC3B,IAAI,CAACF,MAAM,CAAC6D,MAAM,CAAC1D,IAAI,CAACC,OAAO,EAAE;IACjC+E,gBAAgB,EAAE;EACpB,CAAC,CAAC;EACFjF,EAAE,CAAC,SAAS,EAAE,MAAM;IAClB,IAAI,CAACF,MAAM,CAAC6D,MAAM,CAAC1D,IAAI,CAACC,OAAO,EAAE;IACjC6H,OAAO,EAAE;EACX,CAAC,CAAC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}