{"ast": null, "code": "/**\n * @typedef {(error?: Error|null|undefined, ...output: Array<any>) => void} Callback\n * @typedef {(...input: Array<any>) => any} Middleware\n *\n * @typedef {(...input: Array<any>) => void} Run\n *   Call all middleware.\n * @typedef {(fn: Middleware) => Pipeline} Use\n *   Add `fn` (middleware) to the list.\n * @typedef {{run: Run, use: Use}} Pipeline\n *   Middleware.\n */\n\n/**\n * Create new middleware.\n *\n * @returns {Pipeline}\n */\nexport function trough() {\n  /** @type {Array<Middleware>} */\n  const fns = [];\n  /** @type {Pipeline} */\n  const pipeline = {\n    run,\n    use\n  };\n  return pipeline;\n\n  /** @type {Run} */\n  function run() {\n    for (var _len = arguments.length, values = new Array(_len), _key = 0; _key < _len; _key++) {\n      values[_key] = arguments[_key];\n    }\n    let middlewareIndex = -1;\n    /** @type {Callback} */\n    const callback = values.pop();\n    if (typeof callback !== 'function') {\n      throw new TypeError('Expected function as last argument, not ' + callback);\n    }\n    next(null, ...values);\n\n    /**\n     * Run the next `fn`, or we’re done.\n     *\n     * @param {Error|null|undefined} error\n     * @param {Array<any>} output\n     */\n    function next(error) {\n      const fn = fns[++middlewareIndex];\n      let index = -1;\n      if (error) {\n        callback(error);\n        return;\n      }\n\n      // Copy non-nullish input into values.\n      for (var _len2 = arguments.length, output = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n        output[_key2 - 1] = arguments[_key2];\n      }\n      while (++index < values.length) {\n        if (output[index] === null || output[index] === undefined) {\n          output[index] = values[index];\n        }\n      }\n\n      // Save the newly created `output` for the next call.\n      values = output;\n\n      // Next or done.\n      if (fn) {\n        wrap(fn, next)(...output);\n      } else {\n        callback(null, ...output);\n      }\n    }\n  }\n\n  /** @type {Use} */\n  function use(middelware) {\n    if (typeof middelware !== 'function') {\n      throw new TypeError('Expected `middelware` to be a function, not ' + middelware);\n    }\n    fns.push(middelware);\n    return pipeline;\n  }\n}\n\n/**\n * Wrap `middleware`.\n * Can be sync or async; return a promise, receive a callback, or return new\n * values and errors.\n *\n * @param {Middleware} middleware\n * @param {Callback} callback\n */\nexport function wrap(middleware, callback) {\n  /** @type {boolean} */\n  let called;\n  return wrapped;\n\n  /**\n   * Call `middleware`.\n   * @this {any}\n   * @param {Array<any>} parameters\n   * @returns {void}\n   */\n  function wrapped() {\n    for (var _len3 = arguments.length, parameters = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n      parameters[_key3] = arguments[_key3];\n    }\n    const fnExpectsCallback = middleware.length > parameters.length;\n    /** @type {any} */\n    let result;\n    if (fnExpectsCallback) {\n      parameters.push(done);\n    }\n    try {\n      result = middleware.apply(this, parameters);\n    } catch (error) {\n      const exception = /** @type {Error} */error;\n\n      // Well, this is quite the pickle.\n      // `middleware` received a callback and called it synchronously, but that\n      // threw an error.\n      // The only thing left to do is to throw the thing instead.\n      if (fnExpectsCallback && called) {\n        throw exception;\n      }\n      return done(exception);\n    }\n    if (!fnExpectsCallback) {\n      if (result instanceof Promise) {\n        result.then(then, done);\n      } else if (result instanceof Error) {\n        done(result);\n      } else {\n        then(result);\n      }\n    }\n  }\n\n  /**\n   * Call `callback`, only once.\n   * @type {Callback}\n   */\n  function done(error) {\n    if (!called) {\n      called = true;\n      for (var _len4 = arguments.length, output = new Array(_len4 > 1 ? _len4 - 1 : 0), _key4 = 1; _key4 < _len4; _key4++) {\n        output[_key4 - 1] = arguments[_key4];\n      }\n      callback(error, ...output);\n    }\n  }\n\n  /**\n   * Call `done` with one value.\n   *\n   * @param {any} [value]\n   */\n  function then(value) {\n    done(null, value);\n  }\n}", "map": {"version": 3, "names": ["trough", "fns", "pipeline", "run", "use", "_len", "arguments", "length", "values", "Array", "_key", "middlewareIndex", "callback", "pop", "TypeError", "next", "error", "fn", "index", "_len2", "output", "_key2", "undefined", "wrap", "middelware", "push", "middleware", "called", "wrapped", "_len3", "parameters", "_key3", "fnExpectsCallback", "result", "done", "apply", "exception", "Promise", "then", "Error", "_len4", "_key4", "value"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/trough/index.js"], "sourcesContent": ["/**\n * @typedef {(error?: Error|null|undefined, ...output: Array<any>) => void} Callback\n * @typedef {(...input: Array<any>) => any} Middleware\n *\n * @typedef {(...input: Array<any>) => void} Run\n *   Call all middleware.\n * @typedef {(fn: Middleware) => Pipeline} Use\n *   Add `fn` (middleware) to the list.\n * @typedef {{run: Run, use: Use}} Pipeline\n *   Middleware.\n */\n\n/**\n * Create new middleware.\n *\n * @returns {Pipeline}\n */\nexport function trough() {\n  /** @type {Array<Middleware>} */\n  const fns = []\n  /** @type {Pipeline} */\n  const pipeline = {run, use}\n\n  return pipeline\n\n  /** @type {Run} */\n  function run(...values) {\n    let middlewareIndex = -1\n    /** @type {Callback} */\n    const callback = values.pop()\n\n    if (typeof callback !== 'function') {\n      throw new TypeError('Expected function as last argument, not ' + callback)\n    }\n\n    next(null, ...values)\n\n    /**\n     * Run the next `fn`, or we’re done.\n     *\n     * @param {Error|null|undefined} error\n     * @param {Array<any>} output\n     */\n    function next(error, ...output) {\n      const fn = fns[++middlewareIndex]\n      let index = -1\n\n      if (error) {\n        callback(error)\n        return\n      }\n\n      // Copy non-nullish input into values.\n      while (++index < values.length) {\n        if (output[index] === null || output[index] === undefined) {\n          output[index] = values[index]\n        }\n      }\n\n      // Save the newly created `output` for the next call.\n      values = output\n\n      // Next or done.\n      if (fn) {\n        wrap(fn, next)(...output)\n      } else {\n        callback(null, ...output)\n      }\n    }\n  }\n\n  /** @type {Use} */\n  function use(middelware) {\n    if (typeof middelware !== 'function') {\n      throw new TypeError(\n        'Expected `middelware` to be a function, not ' + middelware\n      )\n    }\n\n    fns.push(middelware)\n    return pipeline\n  }\n}\n\n/**\n * Wrap `middleware`.\n * Can be sync or async; return a promise, receive a callback, or return new\n * values and errors.\n *\n * @param {Middleware} middleware\n * @param {Callback} callback\n */\nexport function wrap(middleware, callback) {\n  /** @type {boolean} */\n  let called\n\n  return wrapped\n\n  /**\n   * Call `middleware`.\n   * @this {any}\n   * @param {Array<any>} parameters\n   * @returns {void}\n   */\n  function wrapped(...parameters) {\n    const fnExpectsCallback = middleware.length > parameters.length\n    /** @type {any} */\n    let result\n\n    if (fnExpectsCallback) {\n      parameters.push(done)\n    }\n\n    try {\n      result = middleware.apply(this, parameters)\n    } catch (error) {\n      const exception = /** @type {Error} */ (error)\n\n      // Well, this is quite the pickle.\n      // `middleware` received a callback and called it synchronously, but that\n      // threw an error.\n      // The only thing left to do is to throw the thing instead.\n      if (fnExpectsCallback && called) {\n        throw exception\n      }\n\n      return done(exception)\n    }\n\n    if (!fnExpectsCallback) {\n      if (result instanceof Promise) {\n        result.then(then, done)\n      } else if (result instanceof Error) {\n        done(result)\n      } else {\n        then(result)\n      }\n    }\n  }\n\n  /**\n   * Call `callback`, only once.\n   * @type {Callback}\n   */\n  function done(error, ...output) {\n    if (!called) {\n      called = true\n      callback(error, ...output)\n    }\n  }\n\n  /**\n   * Call `done` with one value.\n   *\n   * @param {any} [value]\n   */\n  function then(value) {\n    done(null, value)\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,MAAMA,CAAA,EAAG;EACvB;EACA,MAAMC,GAAG,GAAG,EAAE;EACd;EACA,MAAMC,QAAQ,GAAG;IAACC,GAAG;IAAEC;EAAG,CAAC;EAE3B,OAAOF,QAAQ;;EAEf;EACA,SAASC,GAAGA,CAAA,EAAY;IAAA,SAAAE,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAARC,MAAM,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;MAANF,MAAM,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;IAAA;IACpB,IAAIC,eAAe,GAAG,CAAC,CAAC;IACxB;IACA,MAAMC,QAAQ,GAAGJ,MAAM,CAACK,GAAG,EAAE;IAE7B,IAAI,OAAOD,QAAQ,KAAK,UAAU,EAAE;MAClC,MAAM,IAAIE,SAAS,CAAC,0CAA0C,GAAGF,QAAQ,CAAC;IAC5E;IAEAG,IAAI,CAAC,IAAI,EAAE,GAAGP,MAAM,CAAC;;IAErB;AACJ;AACA;AACA;AACA;AACA;IACI,SAASO,IAAIA,CAACC,KAAK,EAAa;MAC9B,MAAMC,EAAE,GAAGhB,GAAG,CAAC,EAAEU,eAAe,CAAC;MACjC,IAAIO,KAAK,GAAG,CAAC,CAAC;MAEd,IAAIF,KAAK,EAAE;QACTJ,QAAQ,CAACI,KAAK,CAAC;QACf;MACF;;MAEA;MAAA,SAAAG,KAAA,GAAAb,SAAA,CAAAC,MAAA,EATsBa,MAAM,OAAAX,KAAA,CAAAU,KAAA,OAAAA,KAAA,WAAAE,KAAA,MAAAA,KAAA,GAAAF,KAAA,EAAAE,KAAA;QAAND,MAAM,CAAAC,KAAA,QAAAf,SAAA,CAAAe,KAAA;MAAA;MAU5B,OAAO,EAAEH,KAAK,GAAGV,MAAM,CAACD,MAAM,EAAE;QAC9B,IAAIa,MAAM,CAACF,KAAK,CAAC,KAAK,IAAI,IAAIE,MAAM,CAACF,KAAK,CAAC,KAAKI,SAAS,EAAE;UACzDF,MAAM,CAACF,KAAK,CAAC,GAAGV,MAAM,CAACU,KAAK,CAAC;QAC/B;MACF;;MAEA;MACAV,MAAM,GAAGY,MAAM;;MAEf;MACA,IAAIH,EAAE,EAAE;QACNM,IAAI,CAACN,EAAE,EAAEF,IAAI,CAAC,CAAC,GAAGK,MAAM,CAAC;MAC3B,CAAC,MAAM;QACLR,QAAQ,CAAC,IAAI,EAAE,GAAGQ,MAAM,CAAC;MAC3B;IACF;EACF;;EAEA;EACA,SAAShB,GAAGA,CAACoB,UAAU,EAAE;IACvB,IAAI,OAAOA,UAAU,KAAK,UAAU,EAAE;MACpC,MAAM,IAAIV,SAAS,CACjB,8CAA8C,GAAGU,UAAU,CAC5D;IACH;IAEAvB,GAAG,CAACwB,IAAI,CAACD,UAAU,CAAC;IACpB,OAAOtB,QAAQ;EACjB;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASqB,IAAIA,CAACG,UAAU,EAAEd,QAAQ,EAAE;EACzC;EACA,IAAIe,MAAM;EAEV,OAAOC,OAAO;;EAEd;AACF;AACA;AACA;AACA;AACA;EACE,SAASA,OAAOA,CAAA,EAAgB;IAAA,SAAAC,KAAA,GAAAvB,SAAA,CAAAC,MAAA,EAAZuB,UAAU,OAAArB,KAAA,CAAAoB,KAAA,GAAAE,KAAA,MAAAA,KAAA,GAAAF,KAAA,EAAAE,KAAA;MAAVD,UAAU,CAAAC,KAAA,IAAAzB,SAAA,CAAAyB,KAAA;IAAA;IAC5B,MAAMC,iBAAiB,GAAGN,UAAU,CAACnB,MAAM,GAAGuB,UAAU,CAACvB,MAAM;IAC/D;IACA,IAAI0B,MAAM;IAEV,IAAID,iBAAiB,EAAE;MACrBF,UAAU,CAACL,IAAI,CAACS,IAAI,CAAC;IACvB;IAEA,IAAI;MACFD,MAAM,GAAGP,UAAU,CAACS,KAAK,CAAC,IAAI,EAAEL,UAAU,CAAC;IAC7C,CAAC,CAAC,OAAOd,KAAK,EAAE;MACd,MAAMoB,SAAS,GAAG,oBAAsBpB,KAAM;;MAE9C;MACA;MACA;MACA;MACA,IAAIgB,iBAAiB,IAAIL,MAAM,EAAE;QAC/B,MAAMS,SAAS;MACjB;MAEA,OAAOF,IAAI,CAACE,SAAS,CAAC;IACxB;IAEA,IAAI,CAACJ,iBAAiB,EAAE;MACtB,IAAIC,MAAM,YAAYI,OAAO,EAAE;QAC7BJ,MAAM,CAACK,IAAI,CAACA,IAAI,EAAEJ,IAAI,CAAC;MACzB,CAAC,MAAM,IAAID,MAAM,YAAYM,KAAK,EAAE;QAClCL,IAAI,CAACD,MAAM,CAAC;MACd,CAAC,MAAM;QACLK,IAAI,CAACL,MAAM,CAAC;MACd;IACF;EACF;;EAEA;AACF;AACA;AACA;EACE,SAASC,IAAIA,CAAClB,KAAK,EAAa;IAC9B,IAAI,CAACW,MAAM,EAAE;MACXA,MAAM,GAAG,IAAI;MAAA,SAAAa,KAAA,GAAAlC,SAAA,CAAAC,MAAA,EAFOa,MAAM,OAAAX,KAAA,CAAA+B,KAAA,OAAAA,KAAA,WAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;QAANrB,MAAM,CAAAqB,KAAA,QAAAnC,SAAA,CAAAmC,KAAA;MAAA;MAG1B7B,QAAQ,CAACI,KAAK,EAAE,GAAGI,MAAM,CAAC;IAC5B;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,SAASkB,IAAIA,CAACI,KAAK,EAAE;IACnBR,IAAI,CAAC,IAAI,EAAEQ,KAAK,CAAC;EACnB;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}