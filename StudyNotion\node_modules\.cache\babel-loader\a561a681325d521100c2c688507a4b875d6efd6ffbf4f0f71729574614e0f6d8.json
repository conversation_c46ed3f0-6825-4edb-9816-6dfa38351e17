{"ast": null, "code": "export const HomePageExplore = [{\n  tag: 'Free',\n  courses: [{\n    heading: \"Learn HTML\",\n    description: \"This course covers the basic concepts of HTML including creating and structuring web pages, adding text, links, images, and more.\",\n    level: 'Beginner',\n    lessionNumber: 6\n  }, {\n    heading: \"Learn CSS\",\n    description: \"This course explores advanced topics in HTML5 and CSS3, including animations, transitions, and layout techniques\",\n    level: 'Beginner',\n    lessionNumber: 6\n  }, {\n    heading: \"Responsive Web design\",\n    description: \"This course teaches responsive web design techniques, allowing web pages to adapt to different devices and screen sizes\",\n    level: 'Beginner',\n    lessionNumber: 6\n  }]\n}, {\n  tag: 'New to coding',\n  courses: [{\n    heading: \"HTML\",\n    description: \"This course covers the basic concepts of HTML including creating and structuring web pages, adding text, links, images, and more.\",\n    level: 'Beginner',\n    lessionNumber: 6\n  }, {\n    heading: \"CSS\",\n    description: \"This course explores advanced topics in HTML5 and CSS3, including animations, transitions, and layout techniques\",\n    level: 'Beginner',\n    lessionNumber: 6\n  }, {\n    heading: \"Responsive \",\n    description: \"This course teaches responsive web design techniques, allowing web pages to adapt to different devices and screen sizes\",\n    level: 'Beginner',\n    lessionNumber: 6\n  }]\n}, {\n  tag: 'Most popular',\n  courses: [{\n    heading: \"Java\",\n    description: \"This course covers the basic concepts of HTML including creating and structuring web pages, adding text, links, images, and more.\",\n    level: 'Beginner',\n    lessionNumber: 6\n  }, {\n    heading: \"Python\",\n    description: \"This course explores advanced topics in HTML5 and CSS3, including animations, transitions, and layout techniques\",\n    level: 'Beginner',\n    lessionNumber: 6\n  }, {\n    heading: \"SCSS\",\n    description: \"This course teaches responsive web design techniques, allowing web pages to adapt to different devices and screen sizes\",\n    level: 'Beginner',\n    lessionNumber: 6\n  }]\n}, {\n  tag: 'Skills paths',\n  courses: [{\n    heading: \"Flask\",\n    description: \"This course covers the basic concepts of HTML including creating and structuring web pages, adding text, links, images, and more.\",\n    level: 'Beginner',\n    lessionNumber: 6\n  }, {\n    heading: \"Django\",\n    description: \"This course explores advanced topics in HTML5 and CSS3, including animations, transitions, and layout techniques\",\n    level: 'Beginner',\n    lessionNumber: 6\n  }, {\n    heading: \"Fast API\",\n    description: \"This course teaches responsive web design techniques, allowing web pages to adapt to different devices and screen sizes\",\n    level: 'Beginner',\n    lessionNumber: 6\n  }]\n}, {\n  tag: 'Career paths',\n  courses: [{\n    heading: \"Next.js\",\n    description: \"This course covers the basic concepts of HTML including creating and structuring web pages, adding text, links, images, and more.\",\n    level: 'Beginner',\n    lessionNumber: 6\n  }, {\n    heading: \"Nuxt.js\",\n    description: \"This course explores advanced topics in HTML5 and CSS3, including animations, transitions, and layout techniques\",\n    level: 'Beginner',\n    lessionNumber: 6\n  }, {\n    heading: \"Sanity\",\n    description: \"This course teaches responsive web design techniques, allowing web pages to adapt to different devices and screen sizes\",\n    level: 'Beginner',\n    lessionNumber: 6\n  }]\n}];", "map": {"version": 3, "names": ["HomePageExplore", "tag", "courses", "heading", "description", "level", "lessionNumber"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/src/data/homepage-explore.js"], "sourcesContent": ["export const HomePageExplore = [\r\n    {\r\n        tag: 'Free',\r\n        courses : [\r\n            {\r\n                heading : \"Learn HTML\",\r\n                description : \"This course covers the basic concepts of HTML including creating and structuring web pages, adding text, links, images, and more.\",\r\n                level : 'Beginner',\r\n                lessionNumber : 6\r\n            },\r\n            {\r\n                heading : \"Learn CSS\",\r\n                description : \"This course explores advanced topics in HTML5 and CSS3, including animations, transitions, and layout techniques\",\r\n                level : 'Beginner',\r\n                lessionNumber : 6\r\n            },\r\n            {\r\n                heading : \"Responsive Web design\",\r\n                description : \"This course teaches responsive web design techniques, allowing web pages to adapt to different devices and screen sizes\",\r\n                level : 'Beginner',\r\n                lessionNumber : 6\r\n            },\r\n        ]\r\n    },\r\n    {\r\n        tag: 'New to coding',\r\n        courses : [\r\n            {\r\n                heading : \"HTML\",\r\n                description : \"This course covers the basic concepts of HTML including creating and structuring web pages, adding text, links, images, and more.\",\r\n                level : 'Beginner',\r\n                lessionNumber : 6\r\n            },\r\n            {\r\n                heading : \"CSS\",\r\n                description : \"This course explores advanced topics in HTML5 and CSS3, including animations, transitions, and layout techniques\",\r\n                level : 'Beginner',\r\n                lessionNumber : 6\r\n            },\r\n            {\r\n                heading : \"Responsive \",\r\n                description : \"This course teaches responsive web design techniques, allowing web pages to adapt to different devices and screen sizes\",\r\n                level : 'Beginner',\r\n                lessionNumber : 6\r\n            },\r\n        ]\r\n    },\r\n    {\r\n        tag: 'Most popular',\r\n        courses : [\r\n            {\r\n                heading : \"Java\",\r\n                description : \"This course covers the basic concepts of HTML including creating and structuring web pages, adding text, links, images, and more.\",\r\n                level : 'Beginner',\r\n                lessionNumber : 6\r\n            },\r\n            {\r\n                heading : \"Python\",\r\n                description : \"This course explores advanced topics in HTML5 and CSS3, including animations, transitions, and layout techniques\",\r\n                level : 'Beginner',\r\n                lessionNumber : 6\r\n            },\r\n            {\r\n                heading : \"SCSS\",\r\n                description : \"This course teaches responsive web design techniques, allowing web pages to adapt to different devices and screen sizes\",\r\n                level : 'Beginner',\r\n                lessionNumber : 6\r\n            },\r\n        ]\r\n    },\r\n    {\r\n        tag: 'Skills paths',\r\n        courses : [\r\n            {\r\n                heading : \"Flask\",\r\n                description : \"This course covers the basic concepts of HTML including creating and structuring web pages, adding text, links, images, and more.\",\r\n                level : 'Beginner',\r\n                lessionNumber : 6\r\n            },\r\n            {\r\n                heading : \"Django\",\r\n                description : \"This course explores advanced topics in HTML5 and CSS3, including animations, transitions, and layout techniques\",\r\n                level : 'Beginner',\r\n                lessionNumber : 6\r\n            },\r\n            {\r\n                heading : \"Fast API\",\r\n                description : \"This course teaches responsive web design techniques, allowing web pages to adapt to different devices and screen sizes\",\r\n                level : 'Beginner',\r\n                lessionNumber : 6\r\n            },\r\n        ]\r\n    },\r\n    {\r\n        tag: 'Career paths',\r\n        courses : [\r\n            {\r\n                heading : \"Next.js\",\r\n                description : \"This course covers the basic concepts of HTML including creating and structuring web pages, adding text, links, images, and more.\",\r\n                level : 'Beginner',\r\n                lessionNumber : 6\r\n            },\r\n            {\r\n                heading : \"Nuxt.js\",\r\n                description : \"This course explores advanced topics in HTML5 and CSS3, including animations, transitions, and layout techniques\",\r\n                level : 'Beginner',\r\n                lessionNumber : 6\r\n            },\r\n            {\r\n                heading : \"Sanity\",\r\n                description : \"This course teaches responsive web design techniques, allowing web pages to adapt to different devices and screen sizes\",\r\n                level : 'Beginner',\r\n                lessionNumber : 6\r\n            },\r\n        ]\r\n    },\r\n]"], "mappings": "AAAA,OAAO,MAAMA,eAAe,GAAG,CAC3B;EACIC,GAAG,EAAE,MAAM;EACXC,OAAO,EAAG,CACN;IACIC,OAAO,EAAG,YAAY;IACtBC,WAAW,EAAG,mIAAmI;IACjJC,KAAK,EAAG,UAAU;IAClBC,aAAa,EAAG;EACpB,CAAC,EACD;IACIH,OAAO,EAAG,WAAW;IACrBC,WAAW,EAAG,kHAAkH;IAChIC,KAAK,EAAG,UAAU;IAClBC,aAAa,EAAG;EACpB,CAAC,EACD;IACIH,OAAO,EAAG,uBAAuB;IACjCC,WAAW,EAAG,yHAAyH;IACvIC,KAAK,EAAG,UAAU;IAClBC,aAAa,EAAG;EACpB,CAAC;AAET,CAAC,EACD;EACIL,GAAG,EAAE,eAAe;EACpBC,OAAO,EAAG,CACN;IACIC,OAAO,EAAG,MAAM;IAChBC,WAAW,EAAG,mIAAmI;IACjJC,KAAK,EAAG,UAAU;IAClBC,aAAa,EAAG;EACpB,CAAC,EACD;IACIH,OAAO,EAAG,KAAK;IACfC,WAAW,EAAG,kHAAkH;IAChIC,KAAK,EAAG,UAAU;IAClBC,aAAa,EAAG;EACpB,CAAC,EACD;IACIH,OAAO,EAAG,aAAa;IACvBC,WAAW,EAAG,yHAAyH;IACvIC,KAAK,EAAG,UAAU;IAClBC,aAAa,EAAG;EACpB,CAAC;AAET,CAAC,EACD;EACIL,GAAG,EAAE,cAAc;EACnBC,OAAO,EAAG,CACN;IACIC,OAAO,EAAG,MAAM;IAChBC,WAAW,EAAG,mIAAmI;IACjJC,KAAK,EAAG,UAAU;IAClBC,aAAa,EAAG;EACpB,CAAC,EACD;IACIH,OAAO,EAAG,QAAQ;IAClBC,WAAW,EAAG,kHAAkH;IAChIC,KAAK,EAAG,UAAU;IAClBC,aAAa,EAAG;EACpB,CAAC,EACD;IACIH,OAAO,EAAG,MAAM;IAChBC,WAAW,EAAG,yHAAyH;IACvIC,KAAK,EAAG,UAAU;IAClBC,aAAa,EAAG;EACpB,CAAC;AAET,CAAC,EACD;EACIL,GAAG,EAAE,cAAc;EACnBC,OAAO,EAAG,CACN;IACIC,OAAO,EAAG,OAAO;IACjBC,WAAW,EAAG,mIAAmI;IACjJC,KAAK,EAAG,UAAU;IAClBC,aAAa,EAAG;EACpB,CAAC,EACD;IACIH,OAAO,EAAG,QAAQ;IAClBC,WAAW,EAAG,kHAAkH;IAChIC,KAAK,EAAG,UAAU;IAClBC,aAAa,EAAG;EACpB,CAAC,EACD;IACIH,OAAO,EAAG,UAAU;IACpBC,WAAW,EAAG,yHAAyH;IACvIC,KAAK,EAAG,UAAU;IAClBC,aAAa,EAAG;EACpB,CAAC;AAET,CAAC,EACD;EACIL,GAAG,EAAE,cAAc;EACnBC,OAAO,EAAG,CACN;IACIC,OAAO,EAAG,SAAS;IACnBC,WAAW,EAAG,mIAAmI;IACjJC,KAAK,EAAG,UAAU;IAClBC,aAAa,EAAG;EACpB,CAAC,EACD;IACIH,OAAO,EAAG,SAAS;IACnBC,WAAW,EAAG,kHAAkH;IAChIC,KAAK,EAAG,UAAU;IAClBC,aAAa,EAAG;EACpB,CAAC,EACD;IACIH,OAAO,EAAG,QAAQ;IAClBC,WAAW,EAAG,yHAAyH;IACvIC,KAAK,EAAG,UAAU;IAClBC,aAAa,EAAG;EACpB,CAAC;AAET,CAAC,CACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}