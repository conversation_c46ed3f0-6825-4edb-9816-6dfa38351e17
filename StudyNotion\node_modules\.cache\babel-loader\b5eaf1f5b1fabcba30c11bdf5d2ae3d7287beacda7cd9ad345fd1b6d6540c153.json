{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\temp\\\\StudyNotion\\\\src\\\\components\\\\common\\\\Tab.jsx\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function Tab(_ref) {\n  let {\n    tabData,\n    field,\n    setField\n  } = _ref;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      boxShadow: \"inset 0px -1px 0px rgba(255, 255, 255, 0.18)\"\n    },\n    className: \"flex bg-richblack-800 p-1 gap-x-1 my-6 rounded-full max-w-max\",\n    children: tabData.map(tab => /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: () => setField(tab.type),\n      className: `${field === tab.type ? \"bg-richblack-900 text-richblack-5\" : \"bg-transparent text-richblack-200\"} py-2 px-5 rounded-full transition-all duration-200`,\n      children: tab === null || tab === void 0 ? void 0 : tab.tabName\n    }, tab.id, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 11\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 3,\n    columnNumber: 7\n  }, this);\n}\n_c = Tab;\nvar _c;\n$RefreshReg$(_c, \"Tab\");", "map": {"version": 3, "names": ["Tab", "_ref", "tabData", "field", "set<PERSON><PERSON>", "_jsxDEV", "style", "boxShadow", "className", "children", "map", "tab", "onClick", "type", "tabName", "id", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/src/components/common/Tab.jsx"], "sourcesContent": ["export default function Tab({ tabData, field, setField }) {\r\n    return (\r\n      <div\r\n        style={{\r\n          boxShadow: \"inset 0px -1px 0px rgba(255, 255, 255, 0.18)\",\r\n        }}\r\n        className=\"flex bg-richblack-800 p-1 gap-x-1 my-6 rounded-full max-w-max\"\r\n      >\r\n        {tabData.map((tab) => (\r\n          <button\r\n            key={tab.id}\r\n            onClick={() => setField(tab.type)}\r\n            className={`${\r\n              field === tab.type\r\n                ? \"bg-richblack-900 text-richblack-5\"\r\n                : \"bg-transparent text-richblack-200\"\r\n            } py-2 px-5 rounded-full transition-all duration-200`}\r\n          >\r\n            {tab?.tabName}\r\n          </button>\r\n        ))}\r\n      </div>\r\n    );\r\n  }"], "mappings": ";;AAAA,eAAe,SAASA,GAAGA,CAAAC,IAAA,EAA+B;EAAA,IAA9B;IAAEC,OAAO;IAAEC,KAAK;IAAEC;EAAS,CAAC,GAAAH,IAAA;EACpD,oBACEI,OAAA;IACEC,KAAK,EAAE;MACLC,SAAS,EAAE;IACb,CAAE;IACFC,SAAS,EAAC,+DAA+D;IAAAC,QAAA,EAExEP,OAAO,CAACQ,GAAG,CAAEC,GAAG,iBACfN,OAAA;MAEEO,OAAO,EAAEA,CAAA,KAAMR,QAAQ,CAACO,GAAG,CAACE,IAAI,CAAE;MAClCL,SAAS,EAAG,GACVL,KAAK,KAAKQ,GAAG,CAACE,IAAI,GACd,mCAAmC,GACnC,mCACL,qDAAqD;MAAAJ,QAAA,EAErDE,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEG;IAAO,GARRH,GAAG,CAACI,EAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAUd;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACE;AAEV;AAACC,EAAA,GAvBqBpB,GAAG;AAAA,IAAAoB,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}