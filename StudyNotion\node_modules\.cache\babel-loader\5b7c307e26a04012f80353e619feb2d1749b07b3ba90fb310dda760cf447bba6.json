{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\temp\\\\StudyNotion\\\\src\\\\components\\\\core\\\\Dashboard\\\\AddCourse\\\\CourseInformation\\\\CourseInformationForm.jsx\",\n  _s = $RefreshSig$();\nimport { useEffect, useState } from \"react\";\nimport { useForm } from \"react-hook-form\";\nimport { toast } from \"react-hot-toast\";\nimport { HiOutlineCurrencyRupee } from \"react-icons/hi\";\nimport { MdNavigateNext } from \"react-icons/md\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { addCourseDetails, editCourseDetails, fetchCourseCategories } from \"../../../../../services/operations/courseDetailsAPI\";\nimport { setCourse, setStep } from \"../../../../../slices/courseSlice\";\nimport { COURSE_STATUS } from \"../../../../../utils/constants\";\nimport IconBtn from \"../../../../common/IconBtn\";\nimport Upload from \"../Upload\";\nimport ChipInput from \"./ChipInput\";\nimport RequirementsField from \"./RequirementField\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function CourseInformationForm() {\n  _s();\n  const {\n    register,\n    handleSubmit,\n    setValue,\n    getValues,\n    formState: {\n      errors\n    }\n  } = useForm();\n  const dispatch = useDispatch();\n  const {\n    token\n  } = useSelector(state => state.auth);\n  const {\n    course,\n    editCourse\n  } = useSelector(state => state.course);\n  const [loading, setLoading] = useState(false);\n  const [courseCategories, setCourseCategories] = useState([]);\n  useEffect(() => {\n    const getCategories = async () => {\n      setLoading(true);\n      const categories = await fetchCourseCategories();\n      if (categories.length > 0) {\n        // console.log(\"categories\", categories)\n        setCourseCategories(categories);\n      }\n      setLoading(false);\n    };\n    // if form is in edit mode\n    if (editCourse) {\n      // console.log(\"data populated\", editCourse)\n      setValue(\"courseTitle\", course.courseName);\n      setValue(\"courseShortDesc\", course.courseDescription);\n      setValue(\"coursePrice\", course.price);\n      setValue(\"courseTags\", course.tag);\n      setValue(\"courseBenefits\", course.whatYouWillLearn);\n      setValue(\"courseCategory\", course.category);\n      setValue(\"courseRequirements\", course.instructions);\n      setValue(\"courseImage\", course.thumbnail);\n    }\n    getCategories();\n\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  const isFormUpdated = () => {\n    const currentValues = getValues();\n    // console.log(\"changes after editing form values:\", currentValues)\n    if (currentValues.courseTitle !== course.courseName || currentValues.courseShortDesc !== course.courseDescription || currentValues.coursePrice !== course.price || currentValues.courseTags.toString() !== course.tag.toString() || currentValues.courseBenefits !== course.whatYouWillLearn || currentValues.courseCategory._id !== course.category._id || currentValues.courseRequirements.toString() !== course.instructions.toString() || currentValues.courseImage !== course.thumbnail) {\n      return true;\n    }\n    return false;\n  };\n\n  //   handle next button click\n  const onSubmit = async data => {\n    // console.log(data)\n\n    if (editCourse) {\n      // const currentValues = getValues()\n      // console.log(\"changes after editing form values:\", currentValues)\n      // console.log(\"now course:\", course)\n      // console.log(\"Has Form Changed:\", isFormUpdated())\n      if (isFormUpdated()) {\n        const currentValues = getValues();\n        const formData = new FormData();\n        // console.log(data)\n        formData.append(\"courseId\", course._id);\n        if (currentValues.courseTitle !== course.courseName) {\n          formData.append(\"courseName\", data.courseTitle);\n        }\n        if (currentValues.courseShortDesc !== course.courseDescription) {\n          formData.append(\"courseDescription\", data.courseShortDesc);\n        }\n        if (currentValues.coursePrice !== course.price) {\n          formData.append(\"price\", data.coursePrice);\n        }\n        if (currentValues.courseTags.toString() !== course.tag.toString()) {\n          formData.append(\"tag\", JSON.stringify(data.courseTags));\n        }\n        if (currentValues.courseBenefits !== course.whatYouWillLearn) {\n          formData.append(\"whatYouWillLearn\", data.courseBenefits);\n        }\n        if (currentValues.courseCategory._id !== course.category._id) {\n          formData.append(\"category\", data.courseCategory);\n        }\n        if (currentValues.courseRequirements.toString() !== course.instructions.toString()) {\n          formData.append(\"instructions\", JSON.stringify(data.courseRequirements));\n        }\n        if (currentValues.courseImage !== course.thumbnail) {\n          formData.append(\"thumbnailImage\", data.courseImage);\n        }\n        // console.log(\"Edit Form data: \", formData)\n        setLoading(true);\n        const result = await editCourseDetails(formData, token);\n        setLoading(false);\n        if (result) {\n          dispatch(setStep(2));\n          dispatch(setCourse(result));\n        }\n      } else {\n        toast.error(\"No changes made to the form\");\n      }\n      return;\n    }\n    const formData = new FormData();\n    formData.append(\"courseName\", data.courseTitle);\n    formData.append(\"courseDescription\", data.courseShortDesc);\n    formData.append(\"price\", data.coursePrice);\n    formData.append(\"tag\", JSON.stringify(data.courseTags));\n    formData.append(\"whatYouWillLearn\", data.courseBenefits);\n    formData.append(\"category\", data.courseCategory);\n    formData.append(\"status\", COURSE_STATUS.DRAFT);\n    formData.append(\"instructions\", JSON.stringify(data.courseRequirements));\n    formData.append(\"thumbnailImage\", data.courseImage);\n    setLoading(true);\n    const result = await addCourseDetails(formData, token);\n    if (result) {\n      dispatch(setStep(2));\n      dispatch(setCourse(result));\n    }\n    setLoading(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"form\", {\n    onSubmit: handleSubmit(onSubmit),\n    className: \"space-y-8 rounded-md border-[1px] border-richblack-700 bg-richblack-800 p-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col space-y-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"text-sm text-richblack-5\",\n        htmlFor: \"courseTitle\",\n        children: [\"Course Title \", /*#__PURE__*/_jsxDEV(\"sup\", {\n          className: \"text-pink-200\",\n          children: \"*\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 24\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        id: \"courseTitle\",\n        placeholder: \"Enter Course Title\",\n        ...register(\"courseTitle\", {\n          required: true\n        }),\n        className: \"form-style w-full\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 9\n      }, this), errors.courseTitle && /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"ml-2 text-xs tracking-wide text-pink-200\",\n        children: \"Course title is required\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 164,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col space-y-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"text-sm text-richblack-5\",\n        htmlFor: \"courseShortDesc\",\n        children: [\"Course Short Description \", /*#__PURE__*/_jsxDEV(\"sup\", {\n          className: \"text-pink-200\",\n          children: \"*\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 36\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n        id: \"courseShortDesc\",\n        placeholder: \"Enter Description\",\n        ...register(\"courseShortDesc\", {\n          required: true\n        }),\n        className: \"form-style resize-x-none min-h-[130px] w-full\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 9\n      }, this), errors.courseShortDesc && /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"ml-2 text-xs tracking-wide text-pink-200\",\n        children: \"Course Description is required\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col space-y-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"text-sm text-richblack-5\",\n        htmlFor: \"coursePrice\",\n        children: [\"Course Price \", /*#__PURE__*/_jsxDEV(\"sup\", {\n          className: \"text-pink-200\",\n          children: \"*\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 24\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          id: \"coursePrice\",\n          placeholder: \"Enter Course Price\",\n          ...register(\"coursePrice\", {\n            required: true,\n            valueAsNumber: true,\n            pattern: {\n              value: /^(0|[1-9]\\d*)(\\.\\d+)?$/\n            }\n          }),\n          className: \"form-style w-full !pl-12\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(HiOutlineCurrencyRupee, {\n          className: \"absolute left-3 top-1/2 inline-block -translate-y-1/2 text-2xl text-richblack-400\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 9\n      }, this), errors.coursePrice && /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"ml-2 text-xs tracking-wide text-pink-200\",\n        children: \"Course Price is required\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 198,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col space-y-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"text-sm text-richblack-5\",\n        htmlFor: \"courseCategory\",\n        children: [\"Course Category \", /*#__PURE__*/_jsxDEV(\"sup\", {\n          className: \"text-pink-200\",\n          children: \"*\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 27\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 225,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n        ...register(\"courseCategory\", {\n          required: true\n        }),\n        defaultValue: \"\",\n        id: \"courseCategory\",\n        className: \"form-style w-full\",\n        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"\",\n          disabled: true,\n          children: \"Choose a Category\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 11\n        }, this), !loading && (courseCategories === null || courseCategories === void 0 ? void 0 : courseCategories.map((category, indx) => /*#__PURE__*/_jsxDEV(\"option\", {\n          value: category === null || category === void 0 ? void 0 : category._id,\n          children: category === null || category === void 0 ? void 0 : category.name\n        }, indx, false, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 15\n        }, this)))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 9\n      }, this), errors.courseCategory && /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"ml-2 text-xs tracking-wide text-pink-200\",\n        children: \"Course Category is required\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 224,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ChipInput, {\n      label: \"Tags\",\n      name: \"courseTags\",\n      placeholder: \"Enter Tags and press Enter\",\n      register: register,\n      errors: errors,\n      setValue: setValue,\n      getValues: getValues\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 251,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Upload, {\n      name: \"courseImage\",\n      label: \"Course Thumbnail\",\n      register: register,\n      setValue: setValue,\n      errors: errors,\n      editData: editCourse ? course === null || course === void 0 ? void 0 : course.thumbnail : null\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 261,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col space-y-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"text-sm text-richblack-5\",\n        htmlFor: \"courseBenefits\",\n        children: [\"Benefits of the course \", /*#__PURE__*/_jsxDEV(\"sup\", {\n          className: \"text-pink-200\",\n          children: \"*\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 34\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n        id: \"courseBenefits\",\n        placeholder: \"Enter benefits of the course\",\n        ...register(\"courseBenefits\", {\n          required: true\n        }),\n        className: \"form-style resize-x-none min-h-[130px] w-full\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 274,\n        columnNumber: 9\n      }, this), errors.courseBenefits && /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"ml-2 text-xs tracking-wide text-pink-200\",\n        children: \"Benefits of the course is required\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 281,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 270,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(RequirementsField, {\n      name: \"courseRequirements\",\n      label: \"Requirements/Instructions\",\n      register: register,\n      setValue: setValue,\n      errors: errors,\n      getValues: getValues\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 287,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-end gap-x-2\",\n      children: [editCourse && /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => dispatch(setStep(2)),\n        disabled: loading,\n        className: `flex cursor-pointer items-center gap-x-2 rounded-md bg-richblack-300 py-[8px] px-[20px] font-semibold text-richblack-900`,\n        children: \"Continue Wihout Saving\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 298,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(IconBtn, {\n        disabled: loading,\n        text: !editCourse ? \"Next\" : \"Save Changes\",\n        children: /*#__PURE__*/_jsxDEV(MdNavigateNext, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 306,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 296,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 159,\n    columnNumber: 5\n  }, this);\n}\n_s(CourseInformationForm, \"mm1gqFE6EGZSSXPjWhnNTeZ6TLc=\", false, function () {\n  return [useForm, useDispatch, useSelector, useSelector];\n});\n_c = CourseInformationForm;\nvar _c;\n$RefreshReg$(_c, \"CourseInformationForm\");", "map": {"version": 3, "names": ["useEffect", "useState", "useForm", "toast", "HiOutlineCurrencyRupee", "MdNavigateNext", "useDispatch", "useSelector", "addCourseDetails", "editCourseDetails", "fetchCourseCategories", "setCourse", "setStep", "COURSE_STATUS", "IconBtn", "Upload", "ChipInput", "RequirementsField", "jsxDEV", "_jsxDEV", "CourseInformationForm", "_s", "register", "handleSubmit", "setValue", "getV<PERSON>ues", "formState", "errors", "dispatch", "token", "state", "auth", "course", "editCourse", "loading", "setLoading", "courseCategories", "setCourseCategories", "getCategories", "categories", "length", "courseName", "courseDescription", "price", "tag", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "category", "instructions", "thumbnail", "isFormUpdated", "currentV<PERSON>ues", "courseTitle", "courseShortDesc", "coursePrice", "courseTags", "toString", "courseBenefits", "courseCategory", "_id", "courseRequirements", "courseImage", "onSubmit", "data", "formData", "FormData", "append", "JSON", "stringify", "result", "error", "DRAFT", "className", "children", "htmlFor", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "id", "placeholder", "required", "valueAsNumber", "pattern", "value", "defaultValue", "disabled", "map", "indx", "name", "label", "editData", "onClick", "text", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/src/components/core/Dashboard/AddCourse/CourseInformation/CourseInformationForm.jsx"], "sourcesContent": ["import { useEffect, useState } from \"react\"\r\nimport { useForm } from \"react-hook-form\"\r\nimport { toast } from \"react-hot-toast\"\r\nimport { HiOutlineCurrencyRupee } from \"react-icons/hi\"\r\nimport { MdNavigateNext } from \"react-icons/md\"\r\nimport { useDispatch, useSelector } from \"react-redux\"\r\n\r\nimport {\r\n  addCourseDetails,\r\n  editCourseDetails,\r\n  fetchCourseCategories,\r\n} from \"../../../../../services/operations/courseDetailsAPI\"\r\nimport { setCourse, setStep } from \"../../../../../slices/courseSlice\"\r\nimport { COURSE_STATUS } from \"../../../../../utils/constants\"\r\nimport IconBtn from \"../../../../common/IconBtn\"\r\nimport Upload from \"../Upload\"\r\nimport ChipInput from \"./ChipInput\"\r\nimport RequirementsField from \"./RequirementField\"\r\n\r\nexport default function CourseInformationForm() {\r\n  const {\r\n    register,\r\n    handleSubmit,\r\n    setValue,\r\n    getValues,\r\n    formState: { errors },\r\n  } = useForm()\r\n\r\n  const dispatch = useDispatch()\r\n  const { token } = useSelector((state) => state.auth)\r\n  const { course, editCourse } = useSelector((state) => state.course)\r\n  const [loading, setLoading] = useState(false)\r\n  const [courseCategories, setCourseCategories] = useState([])\r\n\r\n  useEffect(() => {\r\n    const getCategories = async () => {\r\n      setLoading(true)\r\n      const categories = await fetchCourseCategories()\r\n      if (categories.length > 0) {\r\n        // console.log(\"categories\", categories)\r\n        setCourseCategories(categories)\r\n      }\r\n      setLoading(false)\r\n    }\r\n    // if form is in edit mode\r\n    if (editCourse) {\r\n      // console.log(\"data populated\", editCourse)\r\n      setValue(\"courseTitle\", course.courseName)\r\n      setValue(\"courseShortDesc\", course.courseDescription)\r\n      setValue(\"coursePrice\", course.price)\r\n      setValue(\"courseTags\", course.tag)\r\n      setValue(\"courseBenefits\", course.whatYouWillLearn)\r\n      setValue(\"courseCategory\", course.category)\r\n      setValue(\"courseRequirements\", course.instructions)\r\n      setValue(\"courseImage\", course.thumbnail)\r\n    }\r\n    getCategories()\r\n\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [])\r\n\r\n  const isFormUpdated = () => {\r\n    const currentValues = getValues()\r\n    // console.log(\"changes after editing form values:\", currentValues)\r\n    if (\r\n      currentValues.courseTitle !== course.courseName ||\r\n      currentValues.courseShortDesc !== course.courseDescription ||\r\n      currentValues.coursePrice !== course.price ||\r\n      currentValues.courseTags.toString() !== course.tag.toString() ||\r\n      currentValues.courseBenefits !== course.whatYouWillLearn ||\r\n      currentValues.courseCategory._id !== course.category._id ||\r\n      currentValues.courseRequirements.toString() !==\r\n        course.instructions.toString() ||\r\n      currentValues.courseImage !== course.thumbnail\r\n    ) {\r\n      return true\r\n    }\r\n    return false\r\n  }\r\n\r\n  //   handle next button click\r\n  const onSubmit = async (data) => {\r\n    // console.log(data)\r\n\r\n    if (editCourse) {\r\n      // const currentValues = getValues()\r\n      // console.log(\"changes after editing form values:\", currentValues)\r\n      // console.log(\"now course:\", course)\r\n      // console.log(\"Has Form Changed:\", isFormUpdated())\r\n      if (isFormUpdated()) {\r\n        const currentValues = getValues()\r\n        const formData = new FormData()\r\n        // console.log(data)\r\n        formData.append(\"courseId\", course._id)\r\n        if (currentValues.courseTitle !== course.courseName) {\r\n          formData.append(\"courseName\", data.courseTitle)\r\n        }\r\n        if (currentValues.courseShortDesc !== course.courseDescription) {\r\n          formData.append(\"courseDescription\", data.courseShortDesc)\r\n        }\r\n        if (currentValues.coursePrice !== course.price) {\r\n          formData.append(\"price\", data.coursePrice)\r\n        }\r\n        if (currentValues.courseTags.toString() !== course.tag.toString()) {\r\n          formData.append(\"tag\", JSON.stringify(data.courseTags))\r\n        }\r\n        if (currentValues.courseBenefits !== course.whatYouWillLearn) {\r\n          formData.append(\"whatYouWillLearn\", data.courseBenefits)\r\n        }\r\n        if (currentValues.courseCategory._id !== course.category._id) {\r\n          formData.append(\"category\", data.courseCategory)\r\n        }\r\n        if (\r\n          currentValues.courseRequirements.toString() !==\r\n          course.instructions.toString()\r\n        ) {\r\n          formData.append(\r\n            \"instructions\",\r\n            JSON.stringify(data.courseRequirements)\r\n          )\r\n        }\r\n        if (currentValues.courseImage !== course.thumbnail) {\r\n          formData.append(\"thumbnailImage\", data.courseImage)\r\n        }\r\n        // console.log(\"Edit Form data: \", formData)\r\n        setLoading(true)\r\n        const result = await editCourseDetails(formData, token)\r\n        setLoading(false)\r\n        if (result) {\r\n          dispatch(setStep(2))\r\n          dispatch(setCourse(result))\r\n        }\r\n      } else {\r\n        toast.error(\"No changes made to the form\")\r\n      }\r\n      return\r\n    }\r\n\r\n    const formData = new FormData()\r\n    formData.append(\"courseName\", data.courseTitle)\r\n    formData.append(\"courseDescription\", data.courseShortDesc)\r\n    formData.append(\"price\", data.coursePrice)\r\n    formData.append(\"tag\", JSON.stringify(data.courseTags))\r\n    formData.append(\"whatYouWillLearn\", data.courseBenefits)\r\n    formData.append(\"category\", data.courseCategory)\r\n    formData.append(\"status\", COURSE_STATUS.DRAFT)\r\n    formData.append(\"instructions\", JSON.stringify(data.courseRequirements))\r\n    formData.append(\"thumbnailImage\", data.courseImage)\r\n    setLoading(true)\r\n    const result = await addCourseDetails(formData, token)\r\n    if (result) {\r\n      dispatch(setStep(2))\r\n      dispatch(setCourse(result))\r\n    }\r\n    setLoading(false)\r\n  }\r\n\r\n  return (\r\n    <form\r\n      onSubmit={handleSubmit(onSubmit)}\r\n      className=\"space-y-8 rounded-md border-[1px] border-richblack-700 bg-richblack-800 p-6\"\r\n    >\r\n      {/* Course Title */}\r\n      <div className=\"flex flex-col space-y-2\">\r\n        <label className=\"text-sm text-richblack-5\" htmlFor=\"courseTitle\">\r\n          Course Title <sup className=\"text-pink-200\">*</sup>\r\n        </label>\r\n        <input\r\n          id=\"courseTitle\"\r\n          placeholder=\"Enter Course Title\"\r\n          {...register(\"courseTitle\", { required: true })}\r\n          className=\"form-style w-full\"\r\n        />\r\n        {errors.courseTitle && (\r\n          <span className=\"ml-2 text-xs tracking-wide text-pink-200\">\r\n            Course title is required\r\n          </span>\r\n        )}\r\n      </div>\r\n      {/* Course Short Description */}\r\n      <div className=\"flex flex-col space-y-2\">\r\n        <label className=\"text-sm text-richblack-5\" htmlFor=\"courseShortDesc\">\r\n          Course Short Description <sup className=\"text-pink-200\">*</sup>\r\n        </label>\r\n        <textarea\r\n          id=\"courseShortDesc\"\r\n          placeholder=\"Enter Description\"\r\n          {...register(\"courseShortDesc\", { required: true })}\r\n          className=\"form-style resize-x-none min-h-[130px] w-full\"\r\n        />\r\n        {errors.courseShortDesc && (\r\n          <span className=\"ml-2 text-xs tracking-wide text-pink-200\">\r\n            Course Description is required\r\n          </span>\r\n        )}\r\n      </div>\r\n      {/* Course Price */}\r\n      <div className=\"flex flex-col space-y-2\">\r\n        <label className=\"text-sm text-richblack-5\" htmlFor=\"coursePrice\">\r\n          Course Price <sup className=\"text-pink-200\">*</sup>\r\n        </label>\r\n        <div className=\"relative\">\r\n          <input\r\n            id=\"coursePrice\"\r\n            placeholder=\"Enter Course Price\"\r\n            {...register(\"coursePrice\", {\r\n              required: true,\r\n              valueAsNumber: true,\r\n              pattern: {\r\n                value: /^(0|[1-9]\\d*)(\\.\\d+)?$/,\r\n              },\r\n            })}\r\n            className=\"form-style w-full !pl-12\"\r\n          />\r\n          <HiOutlineCurrencyRupee className=\"absolute left-3 top-1/2 inline-block -translate-y-1/2 text-2xl text-richblack-400\" />\r\n        </div>\r\n        {errors.coursePrice && (\r\n          <span className=\"ml-2 text-xs tracking-wide text-pink-200\">\r\n            Course Price is required\r\n          </span>\r\n        )}\r\n      </div>\r\n      {/* Course Category */}\r\n      <div className=\"flex flex-col space-y-2\">\r\n        <label className=\"text-sm text-richblack-5\" htmlFor=\"courseCategory\">\r\n          Course Category <sup className=\"text-pink-200\">*</sup>\r\n        </label>\r\n        <select\r\n          {...register(\"courseCategory\", { required: true })}\r\n          defaultValue=\"\"\r\n          id=\"courseCategory\"\r\n          className=\"form-style w-full\"\r\n        >\r\n          <option value=\"\" disabled>\r\n            Choose a Category\r\n          </option>\r\n          {!loading &&\r\n            courseCategories?.map((category, indx) => (\r\n              <option key={indx} value={category?._id}>\r\n                {category?.name}\r\n              </option>\r\n            ))}\r\n        </select>\r\n        {errors.courseCategory && (\r\n          <span className=\"ml-2 text-xs tracking-wide text-pink-200\">\r\n            Course Category is required\r\n          </span>\r\n        )}\r\n      </div>\r\n      {/* Course Tags */}\r\n      <ChipInput\r\n        label=\"Tags\"\r\n        name=\"courseTags\"\r\n        placeholder=\"Enter Tags and press Enter\"\r\n        register={register}\r\n        errors={errors}\r\n        setValue={setValue}\r\n        getValues={getValues}\r\n      />\r\n      {/* Course Thumbnail Image */}\r\n      <Upload\r\n        name=\"courseImage\"\r\n        label=\"Course Thumbnail\"\r\n        register={register}\r\n        setValue={setValue}\r\n        errors={errors}\r\n        editData={editCourse ? course?.thumbnail : null}\r\n      />\r\n      {/* Benefits of the course */}\r\n      <div className=\"flex flex-col space-y-2\">\r\n        <label className=\"text-sm text-richblack-5\" htmlFor=\"courseBenefits\">\r\n          Benefits of the course <sup className=\"text-pink-200\">*</sup>\r\n        </label>\r\n        <textarea\r\n          id=\"courseBenefits\"\r\n          placeholder=\"Enter benefits of the course\"\r\n          {...register(\"courseBenefits\", { required: true })}\r\n          className=\"form-style resize-x-none min-h-[130px] w-full\"\r\n        />\r\n        {errors.courseBenefits && (\r\n          <span className=\"ml-2 text-xs tracking-wide text-pink-200\">\r\n            Benefits of the course is required\r\n          </span>\r\n        )}\r\n      </div>\r\n      {/* Requirements/Instructions */}\r\n      <RequirementsField\r\n        name=\"courseRequirements\"\r\n        label=\"Requirements/Instructions\"\r\n        register={register}\r\n        setValue={setValue}\r\n        errors={errors}\r\n        getValues={getValues}\r\n      />\r\n      {/* Next Button */}\r\n      <div className=\"flex justify-end gap-x-2\">\r\n        {editCourse && (\r\n          <button\r\n            onClick={() => dispatch(setStep(2))}\r\n            disabled={loading}\r\n            className={`flex cursor-pointer items-center gap-x-2 rounded-md bg-richblack-300 py-[8px] px-[20px] font-semibold text-richblack-900`}\r\n          >\r\n            Continue Wihout Saving\r\n          </button>\r\n        )}\r\n        <IconBtn\r\n          disabled={loading}\r\n          text={!editCourse ? \"Next\" : \"Save Changes\"}\r\n        >\r\n          <MdNavigateNext />\r\n        </IconBtn>\r\n      </div>\r\n    </form>\r\n  )\r\n}"], "mappings": ";;AAAA,SAASA,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,SAASC,OAAO,QAAQ,iBAAiB;AACzC,SAASC,KAAK,QAAQ,iBAAiB;AACvC,SAASC,sBAAsB,QAAQ,gBAAgB;AACvD,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AAEtD,SACEC,gBAAgB,EAChBC,iBAAiB,EACjBC,qBAAqB,QAChB,qDAAqD;AAC5D,SAASC,SAAS,EAAEC,OAAO,QAAQ,mCAAmC;AACtE,SAASC,aAAa,QAAQ,gCAAgC;AAC9D,OAAOC,OAAO,MAAM,4BAA4B;AAChD,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,iBAAiB,MAAM,oBAAoB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAElD,eAAe,SAASC,qBAAqBA,CAAA,EAAG;EAAAC,EAAA;EAC9C,MAAM;IACJC,QAAQ;IACRC,YAAY;IACZC,QAAQ;IACRC,SAAS;IACTC,SAAS,EAAE;MAAEC;IAAO;EACtB,CAAC,GAAGzB,OAAO,EAAE;EAEb,MAAM0B,QAAQ,GAAGtB,WAAW,EAAE;EAC9B,MAAM;IAAEuB;EAAM,CAAC,GAAGtB,WAAW,CAAEuB,KAAK,IAAKA,KAAK,CAACC,IAAI,CAAC;EACpD,MAAM;IAAEC,MAAM;IAAEC;EAAW,CAAC,GAAG1B,WAAW,CAAEuB,KAAK,IAAKA,KAAK,CAACE,MAAM,CAAC;EACnE,MAAM,CAACE,OAAO,EAAEC,UAAU,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACmC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAE5DD,SAAS,CAAC,MAAM;IACd,MAAMsC,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChCH,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMI,UAAU,GAAG,MAAM7B,qBAAqB,EAAE;MAChD,IAAI6B,UAAU,CAACC,MAAM,GAAG,CAAC,EAAE;QACzB;QACAH,mBAAmB,CAACE,UAAU,CAAC;MACjC;MACAJ,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC;IACD;IACA,IAAIF,UAAU,EAAE;MACd;MACAT,QAAQ,CAAC,aAAa,EAAEQ,MAAM,CAACS,UAAU,CAAC;MAC1CjB,QAAQ,CAAC,iBAAiB,EAAEQ,MAAM,CAACU,iBAAiB,CAAC;MACrDlB,QAAQ,CAAC,aAAa,EAAEQ,MAAM,CAACW,KAAK,CAAC;MACrCnB,QAAQ,CAAC,YAAY,EAAEQ,MAAM,CAACY,GAAG,CAAC;MAClCpB,QAAQ,CAAC,gBAAgB,EAAEQ,MAAM,CAACa,gBAAgB,CAAC;MACnDrB,QAAQ,CAAC,gBAAgB,EAAEQ,MAAM,CAACc,QAAQ,CAAC;MAC3CtB,QAAQ,CAAC,oBAAoB,EAAEQ,MAAM,CAACe,YAAY,CAAC;MACnDvB,QAAQ,CAAC,aAAa,EAAEQ,MAAM,CAACgB,SAAS,CAAC;IAC3C;IACAV,aAAa,EAAE;;IAEf;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMW,aAAa,GAAGA,CAAA,KAAM;IAC1B,MAAMC,aAAa,GAAGzB,SAAS,EAAE;IACjC;IACA,IACEyB,aAAa,CAACC,WAAW,KAAKnB,MAAM,CAACS,UAAU,IAC/CS,aAAa,CAACE,eAAe,KAAKpB,MAAM,CAACU,iBAAiB,IAC1DQ,aAAa,CAACG,WAAW,KAAKrB,MAAM,CAACW,KAAK,IAC1CO,aAAa,CAACI,UAAU,CAACC,QAAQ,EAAE,KAAKvB,MAAM,CAACY,GAAG,CAACW,QAAQ,EAAE,IAC7DL,aAAa,CAACM,cAAc,KAAKxB,MAAM,CAACa,gBAAgB,IACxDK,aAAa,CAACO,cAAc,CAACC,GAAG,KAAK1B,MAAM,CAACc,QAAQ,CAACY,GAAG,IACxDR,aAAa,CAACS,kBAAkB,CAACJ,QAAQ,EAAE,KACzCvB,MAAM,CAACe,YAAY,CAACQ,QAAQ,EAAE,IAChCL,aAAa,CAACU,WAAW,KAAK5B,MAAM,CAACgB,SAAS,EAC9C;MACA,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd,CAAC;;EAED;EACA,MAAMa,QAAQ,GAAG,MAAOC,IAAI,IAAK;IAC/B;;IAEA,IAAI7B,UAAU,EAAE;MACd;MACA;MACA;MACA;MACA,IAAIgB,aAAa,EAAE,EAAE;QACnB,MAAMC,aAAa,GAAGzB,SAAS,EAAE;QACjC,MAAMsC,QAAQ,GAAG,IAAIC,QAAQ,EAAE;QAC/B;QACAD,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEjC,MAAM,CAAC0B,GAAG,CAAC;QACvC,IAAIR,aAAa,CAACC,WAAW,KAAKnB,MAAM,CAACS,UAAU,EAAE;UACnDsB,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAEH,IAAI,CAACX,WAAW,CAAC;QACjD;QACA,IAAID,aAAa,CAACE,eAAe,KAAKpB,MAAM,CAACU,iBAAiB,EAAE;UAC9DqB,QAAQ,CAACE,MAAM,CAAC,mBAAmB,EAAEH,IAAI,CAACV,eAAe,CAAC;QAC5D;QACA,IAAIF,aAAa,CAACG,WAAW,KAAKrB,MAAM,CAACW,KAAK,EAAE;UAC9CoB,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEH,IAAI,CAACT,WAAW,CAAC;QAC5C;QACA,IAAIH,aAAa,CAACI,UAAU,CAACC,QAAQ,EAAE,KAAKvB,MAAM,CAACY,GAAG,CAACW,QAAQ,EAAE,EAAE;UACjEQ,QAAQ,CAACE,MAAM,CAAC,KAAK,EAAEC,IAAI,CAACC,SAAS,CAACL,IAAI,CAACR,UAAU,CAAC,CAAC;QACzD;QACA,IAAIJ,aAAa,CAACM,cAAc,KAAKxB,MAAM,CAACa,gBAAgB,EAAE;UAC5DkB,QAAQ,CAACE,MAAM,CAAC,kBAAkB,EAAEH,IAAI,CAACN,cAAc,CAAC;QAC1D;QACA,IAAIN,aAAa,CAACO,cAAc,CAACC,GAAG,KAAK1B,MAAM,CAACc,QAAQ,CAACY,GAAG,EAAE;UAC5DK,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEH,IAAI,CAACL,cAAc,CAAC;QAClD;QACA,IACEP,aAAa,CAACS,kBAAkB,CAACJ,QAAQ,EAAE,KAC3CvB,MAAM,CAACe,YAAY,CAACQ,QAAQ,EAAE,EAC9B;UACAQ,QAAQ,CAACE,MAAM,CACb,cAAc,EACdC,IAAI,CAACC,SAAS,CAACL,IAAI,CAACH,kBAAkB,CAAC,CACxC;QACH;QACA,IAAIT,aAAa,CAACU,WAAW,KAAK5B,MAAM,CAACgB,SAAS,EAAE;UAClDe,QAAQ,CAACE,MAAM,CAAC,gBAAgB,EAAEH,IAAI,CAACF,WAAW,CAAC;QACrD;QACA;QACAzB,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMiC,MAAM,GAAG,MAAM3D,iBAAiB,CAACsD,QAAQ,EAAElC,KAAK,CAAC;QACvDM,UAAU,CAAC,KAAK,CAAC;QACjB,IAAIiC,MAAM,EAAE;UACVxC,QAAQ,CAAChB,OAAO,CAAC,CAAC,CAAC,CAAC;UACpBgB,QAAQ,CAACjB,SAAS,CAACyD,MAAM,CAAC,CAAC;QAC7B;MACF,CAAC,MAAM;QACLjE,KAAK,CAACkE,KAAK,CAAC,6BAA6B,CAAC;MAC5C;MACA;IACF;IAEA,MAAMN,QAAQ,GAAG,IAAIC,QAAQ,EAAE;IAC/BD,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAEH,IAAI,CAACX,WAAW,CAAC;IAC/CY,QAAQ,CAACE,MAAM,CAAC,mBAAmB,EAAEH,IAAI,CAACV,eAAe,CAAC;IAC1DW,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEH,IAAI,CAACT,WAAW,CAAC;IAC1CU,QAAQ,CAACE,MAAM,CAAC,KAAK,EAAEC,IAAI,CAACC,SAAS,CAACL,IAAI,CAACR,UAAU,CAAC,CAAC;IACvDS,QAAQ,CAACE,MAAM,CAAC,kBAAkB,EAAEH,IAAI,CAACN,cAAc,CAAC;IACxDO,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEH,IAAI,CAACL,cAAc,CAAC;IAChDM,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAEpD,aAAa,CAACyD,KAAK,CAAC;IAC9CP,QAAQ,CAACE,MAAM,CAAC,cAAc,EAAEC,IAAI,CAACC,SAAS,CAACL,IAAI,CAACH,kBAAkB,CAAC,CAAC;IACxEI,QAAQ,CAACE,MAAM,CAAC,gBAAgB,EAAEH,IAAI,CAACF,WAAW,CAAC;IACnDzB,UAAU,CAAC,IAAI,CAAC;IAChB,MAAMiC,MAAM,GAAG,MAAM5D,gBAAgB,CAACuD,QAAQ,EAAElC,KAAK,CAAC;IACtD,IAAIuC,MAAM,EAAE;MACVxC,QAAQ,CAAChB,OAAO,CAAC,CAAC,CAAC,CAAC;MACpBgB,QAAQ,CAACjB,SAAS,CAACyD,MAAM,CAAC,CAAC;IAC7B;IACAjC,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,oBACEhB,OAAA;IACE0C,QAAQ,EAAEtC,YAAY,CAACsC,QAAQ,CAAE;IACjCU,SAAS,EAAC,6EAA6E;IAAAC,QAAA,gBAGvFrD,OAAA;MAAKoD,SAAS,EAAC,yBAAyB;MAAAC,QAAA,gBACtCrD,OAAA;QAAOoD,SAAS,EAAC,0BAA0B;QAACE,OAAO,EAAC,aAAa;QAAAD,QAAA,GAAC,eACnD,eAAArD,OAAA;UAAKoD,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAM;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAC7C,eACR1D,OAAA;QACE2D,EAAE,EAAC,aAAa;QAChBC,WAAW,EAAC,oBAAoB;QAAA,GAC5BzD,QAAQ,CAAC,aAAa,EAAE;UAAE0D,QAAQ,EAAE;QAAK,CAAC,CAAC;QAC/CT,SAAS,EAAC;MAAmB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAC7B,EACDlD,MAAM,CAACwB,WAAW,iBACjBhC,OAAA;QAAMoD,SAAS,EAAC,0CAA0C;QAAAC,QAAA,EAAC;MAE3D;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACD;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACG,eAEN1D,OAAA;MAAKoD,SAAS,EAAC,yBAAyB;MAAAC,QAAA,gBACtCrD,OAAA;QAAOoD,SAAS,EAAC,0BAA0B;QAACE,OAAO,EAAC,iBAAiB;QAAAD,QAAA,GAAC,2BAC3C,eAAArD,OAAA;UAAKoD,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAM;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACzD,eACR1D,OAAA;QACE2D,EAAE,EAAC,iBAAiB;QACpBC,WAAW,EAAC,mBAAmB;QAAA,GAC3BzD,QAAQ,CAAC,iBAAiB,EAAE;UAAE0D,QAAQ,EAAE;QAAK,CAAC,CAAC;QACnDT,SAAS,EAAC;MAA+C;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACzD,EACDlD,MAAM,CAACyB,eAAe,iBACrBjC,OAAA;QAAMoD,SAAS,EAAC,0CAA0C;QAAAC,QAAA,EAAC;MAE3D;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACD;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACG,eAEN1D,OAAA;MAAKoD,SAAS,EAAC,yBAAyB;MAAAC,QAAA,gBACtCrD,OAAA;QAAOoD,SAAS,EAAC,0BAA0B;QAACE,OAAO,EAAC,aAAa;QAAAD,QAAA,GAAC,eACnD,eAAArD,OAAA;UAAKoD,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAM;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAC7C,eACR1D,OAAA;QAAKoD,SAAS,EAAC,UAAU;QAAAC,QAAA,gBACvBrD,OAAA;UACE2D,EAAE,EAAC,aAAa;UAChBC,WAAW,EAAC,oBAAoB;UAAA,GAC5BzD,QAAQ,CAAC,aAAa,EAAE;YAC1B0D,QAAQ,EAAE,IAAI;YACdC,aAAa,EAAE,IAAI;YACnBC,OAAO,EAAE;cACPC,KAAK,EAAE;YACT;UACF,CAAC,CAAC;UACFZ,SAAS,EAAC;QAA0B;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACpC,eACF1D,OAAA,CAACf,sBAAsB;UAACmE,SAAS,EAAC;QAAmF;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAG;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACpH,EACLlD,MAAM,CAAC0B,WAAW,iBACjBlC,OAAA;QAAMoD,SAAS,EAAC,0CAA0C;QAAAC,QAAA,EAAC;MAE3D;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACD;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACG,eAEN1D,OAAA;MAAKoD,SAAS,EAAC,yBAAyB;MAAAC,QAAA,gBACtCrD,OAAA;QAAOoD,SAAS,EAAC,0BAA0B;QAACE,OAAO,EAAC,gBAAgB;QAAAD,QAAA,GAAC,kBACnD,eAAArD,OAAA;UAAKoD,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAM;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAChD,eACR1D,OAAA;QAAA,GACMG,QAAQ,CAAC,gBAAgB,EAAE;UAAE0D,QAAQ,EAAE;QAAK,CAAC,CAAC;QAClDI,YAAY,EAAC,EAAE;QACfN,EAAE,EAAC,gBAAgB;QACnBP,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAE7BrD,OAAA;UAAQgE,KAAK,EAAC,EAAE;UAACE,QAAQ;UAAAb,QAAA,EAAC;QAE1B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAS,EACR,CAAC3C,OAAO,KACPE,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEkD,GAAG,CAAC,CAACxC,QAAQ,EAAEyC,IAAI,kBACnCpE,OAAA;UAAmBgE,KAAK,EAAErC,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEY,GAAI;UAAAc,QAAA,EACrC1B,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE0C;QAAI,GADJD,IAAI;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAGlB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACG,EACRlD,MAAM,CAAC8B,cAAc,iBACpBtC,OAAA;QAAMoD,SAAS,EAAC,0CAA0C;QAAAC,QAAA,EAAC;MAE3D;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACD;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACG,eAEN1D,OAAA,CAACH,SAAS;MACRyE,KAAK,EAAC,MAAM;MACZD,IAAI,EAAC,YAAY;MACjBT,WAAW,EAAC,4BAA4B;MACxCzD,QAAQ,EAAEA,QAAS;MACnBK,MAAM,EAAEA,MAAO;MACfH,QAAQ,EAAEA,QAAS;MACnBC,SAAS,EAAEA;IAAU;MAAAiD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACrB,eAEF1D,OAAA,CAACJ,MAAM;MACLyE,IAAI,EAAC,aAAa;MAClBC,KAAK,EAAC,kBAAkB;MACxBnE,QAAQ,EAAEA,QAAS;MACnBE,QAAQ,EAAEA,QAAS;MACnBG,MAAM,EAAEA,MAAO;MACf+D,QAAQ,EAAEzD,UAAU,GAAGD,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEgB,SAAS,GAAG;IAAK;MAAA0B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAChD,eAEF1D,OAAA;MAAKoD,SAAS,EAAC,yBAAyB;MAAAC,QAAA,gBACtCrD,OAAA;QAAOoD,SAAS,EAAC,0BAA0B;QAACE,OAAO,EAAC,gBAAgB;QAAAD,QAAA,GAAC,yBAC5C,eAAArD,OAAA;UAAKoD,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAM;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACvD,eACR1D,OAAA;QACE2D,EAAE,EAAC,gBAAgB;QACnBC,WAAW,EAAC,8BAA8B;QAAA,GACtCzD,QAAQ,CAAC,gBAAgB,EAAE;UAAE0D,QAAQ,EAAE;QAAK,CAAC,CAAC;QAClDT,SAAS,EAAC;MAA+C;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACzD,EACDlD,MAAM,CAAC6B,cAAc,iBACpBrC,OAAA;QAAMoD,SAAS,EAAC,0CAA0C;QAAAC,QAAA,EAAC;MAE3D;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACD;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACG,eAEN1D,OAAA,CAACF,iBAAiB;MAChBuE,IAAI,EAAC,oBAAoB;MACzBC,KAAK,EAAC,2BAA2B;MACjCnE,QAAQ,EAAEA,QAAS;MACnBE,QAAQ,EAAEA,QAAS;MACnBG,MAAM,EAAEA,MAAO;MACfF,SAAS,EAAEA;IAAU;MAAAiD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACrB,eAEF1D,OAAA;MAAKoD,SAAS,EAAC,0BAA0B;MAAAC,QAAA,GACtCvC,UAAU,iBACTd,OAAA;QACEwE,OAAO,EAAEA,CAAA,KAAM/D,QAAQ,CAAChB,OAAO,CAAC,CAAC,CAAC,CAAE;QACpCyE,QAAQ,EAAEnD,OAAQ;QAClBqC,SAAS,EAAG,0HAA0H;QAAAC,QAAA,EACvI;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACD,eACD1D,OAAA,CAACL,OAAO;QACNuE,QAAQ,EAAEnD,OAAQ;QAClB0D,IAAI,EAAE,CAAC3D,UAAU,GAAG,MAAM,GAAG,cAAe;QAAAuC,QAAA,eAE5CrD,OAAA,CAACd,cAAc;UAAAqE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAG;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACV;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACD;AAEX;AAACxD,EAAA,CAvSuBD,qBAAqB;EAAA,QAOvClB,OAAO,EAEMI,WAAW,EACVC,WAAW,EACEA,WAAW;AAAA;AAAAsF,EAAA,GAXpBzE,qBAAqB;AAAA,IAAAyE,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}