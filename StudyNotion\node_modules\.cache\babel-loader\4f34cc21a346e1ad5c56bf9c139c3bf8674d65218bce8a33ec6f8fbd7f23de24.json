{"ast": null, "code": "import { animateCSSModeScroll } from '../../shared/utils.js';\nexport default function translateTo() {\n  let translate = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n  let speed = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : this.params.speed;\n  let runCallbacks = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;\n  let translateBounds = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : true;\n  let internal = arguments.length > 4 ? arguments[4] : undefined;\n  const swiper = this;\n  const {\n    params,\n    wrapperEl\n  } = swiper;\n  if (swiper.animating && params.preventInteractionOnTransition) {\n    return false;\n  }\n  const minTranslate = swiper.minTranslate();\n  const maxTranslate = swiper.maxTranslate();\n  let newTranslate;\n  if (translateBounds && translate > minTranslate) newTranslate = minTranslate;else if (translateBounds && translate < maxTranslate) newTranslate = maxTranslate;else newTranslate = translate;\n\n  // Update progress\n  swiper.updateProgress(newTranslate);\n  if (params.cssMode) {\n    const isH = swiper.isHorizontal();\n    if (speed === 0) {\n      wrapperEl[isH ? 'scrollLeft' : 'scrollTop'] = -newTranslate;\n    } else {\n      if (!swiper.support.smoothScroll) {\n        animateCSSModeScroll({\n          swiper,\n          targetPosition: -newTranslate,\n          side: isH ? 'left' : 'top'\n        });\n        return true;\n      }\n      wrapperEl.scrollTo({\n        [isH ? 'left' : 'top']: -newTranslate,\n        behavior: 'smooth'\n      });\n    }\n    return true;\n  }\n  if (speed === 0) {\n    swiper.setTransition(0);\n    swiper.setTranslate(newTranslate);\n    if (runCallbacks) {\n      swiper.emit('beforeTransitionStart', speed, internal);\n      swiper.emit('transitionEnd');\n    }\n  } else {\n    swiper.setTransition(speed);\n    swiper.setTranslate(newTranslate);\n    if (runCallbacks) {\n      swiper.emit('beforeTransitionStart', speed, internal);\n      swiper.emit('transitionStart');\n    }\n    if (!swiper.animating) {\n      swiper.animating = true;\n      if (!swiper.onTranslateToWrapperTransitionEnd) {\n        swiper.onTranslateToWrapperTransitionEnd = function transitionEnd(e) {\n          if (!swiper || swiper.destroyed) return;\n          if (e.target !== this) return;\n          swiper.wrapperEl.removeEventListener('transitionend', swiper.onTranslateToWrapperTransitionEnd);\n          swiper.onTranslateToWrapperTransitionEnd = null;\n          delete swiper.onTranslateToWrapperTransitionEnd;\n          if (runCallbacks) {\n            swiper.emit('transitionEnd');\n          }\n        };\n      }\n      swiper.wrapperEl.addEventListener('transitionend', swiper.onTranslateToWrapperTransitionEnd);\n    }\n  }\n  return true;\n}", "map": {"version": 3, "names": ["animateCSSModeScroll", "translateTo", "translate", "arguments", "length", "undefined", "speed", "params", "runCallbacks", "translateBounds", "internal", "swiper", "wrapperEl", "animating", "preventInteractionOnTransition", "minTranslate", "maxTranslate", "newTranslate", "updateProgress", "cssMode", "isH", "isHorizontal", "support", "smoothScroll", "targetPosition", "side", "scrollTo", "behavior", "setTransition", "setTranslate", "emit", "onTranslateToWrapperTransitionEnd", "transitionEnd", "e", "destroyed", "target", "removeEventListener", "addEventListener"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/core/translate/translateTo.js"], "sourcesContent": ["import { animateCSSModeScroll } from '../../shared/utils.js';\nexport default function translateTo(translate = 0, speed = this.params.speed, runCallbacks = true, translateBounds = true, internal) {\n  const swiper = this;\n  const {\n    params,\n    wrapperEl\n  } = swiper;\n  if (swiper.animating && params.preventInteractionOnTransition) {\n    return false;\n  }\n  const minTranslate = swiper.minTranslate();\n  const maxTranslate = swiper.maxTranslate();\n  let newTranslate;\n  if (translateBounds && translate > minTranslate) newTranslate = minTranslate;else if (translateBounds && translate < maxTranslate) newTranslate = maxTranslate;else newTranslate = translate;\n\n  // Update progress\n  swiper.updateProgress(newTranslate);\n  if (params.cssMode) {\n    const isH = swiper.isHorizontal();\n    if (speed === 0) {\n      wrapperEl[isH ? 'scrollLeft' : 'scrollTop'] = -newTranslate;\n    } else {\n      if (!swiper.support.smoothScroll) {\n        animateCSSModeScroll({\n          swiper,\n          targetPosition: -newTranslate,\n          side: isH ? 'left' : 'top'\n        });\n        return true;\n      }\n      wrapperEl.scrollTo({\n        [isH ? 'left' : 'top']: -newTranslate,\n        behavior: 'smooth'\n      });\n    }\n    return true;\n  }\n  if (speed === 0) {\n    swiper.setTransition(0);\n    swiper.setTranslate(newTranslate);\n    if (runCallbacks) {\n      swiper.emit('beforeTransitionStart', speed, internal);\n      swiper.emit('transitionEnd');\n    }\n  } else {\n    swiper.setTransition(speed);\n    swiper.setTranslate(newTranslate);\n    if (runCallbacks) {\n      swiper.emit('beforeTransitionStart', speed, internal);\n      swiper.emit('transitionStart');\n    }\n    if (!swiper.animating) {\n      swiper.animating = true;\n      if (!swiper.onTranslateToWrapperTransitionEnd) {\n        swiper.onTranslateToWrapperTransitionEnd = function transitionEnd(e) {\n          if (!swiper || swiper.destroyed) return;\n          if (e.target !== this) return;\n          swiper.wrapperEl.removeEventListener('transitionend', swiper.onTranslateToWrapperTransitionEnd);\n          swiper.onTranslateToWrapperTransitionEnd = null;\n          delete swiper.onTranslateToWrapperTransitionEnd;\n          if (runCallbacks) {\n            swiper.emit('transitionEnd');\n          }\n        };\n      }\n      swiper.wrapperEl.addEventListener('transitionend', swiper.onTranslateToWrapperTransitionEnd);\n    }\n  }\n  return true;\n}"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,uBAAuB;AAC5D,eAAe,SAASC,WAAWA,CAAA,EAAkG;EAAA,IAAjGC,SAAS,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAAA,IAAEG,KAAK,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI,CAACI,MAAM,CAACD,KAAK;EAAA,IAAEE,YAAY,GAAAL,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;EAAA,IAAEM,eAAe,GAAAN,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;EAAA,IAAEO,QAAQ,GAAAP,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EACjI,MAAMM,MAAM,GAAG,IAAI;EACnB,MAAM;IACJJ,MAAM;IACNK;EACF,CAAC,GAAGD,MAAM;EACV,IAAIA,MAAM,CAACE,SAAS,IAAIN,MAAM,CAACO,8BAA8B,EAAE;IAC7D,OAAO,KAAK;EACd;EACA,MAAMC,YAAY,GAAGJ,MAAM,CAACI,YAAY,EAAE;EAC1C,MAAMC,YAAY,GAAGL,MAAM,CAACK,YAAY,EAAE;EAC1C,IAAIC,YAAY;EAChB,IAAIR,eAAe,IAAIP,SAAS,GAAGa,YAAY,EAAEE,YAAY,GAAGF,YAAY,CAAC,KAAK,IAAIN,eAAe,IAAIP,SAAS,GAAGc,YAAY,EAAEC,YAAY,GAAGD,YAAY,CAAC,KAAKC,YAAY,GAAGf,SAAS;;EAE5L;EACAS,MAAM,CAACO,cAAc,CAACD,YAAY,CAAC;EACnC,IAAIV,MAAM,CAACY,OAAO,EAAE;IAClB,MAAMC,GAAG,GAAGT,MAAM,CAACU,YAAY,EAAE;IACjC,IAAIf,KAAK,KAAK,CAAC,EAAE;MACfM,SAAS,CAACQ,GAAG,GAAG,YAAY,GAAG,WAAW,CAAC,GAAG,CAACH,YAAY;IAC7D,CAAC,MAAM;MACL,IAAI,CAACN,MAAM,CAACW,OAAO,CAACC,YAAY,EAAE;QAChCvB,oBAAoB,CAAC;UACnBW,MAAM;UACNa,cAAc,EAAE,CAACP,YAAY;UAC7BQ,IAAI,EAAEL,GAAG,GAAG,MAAM,GAAG;QACvB,CAAC,CAAC;QACF,OAAO,IAAI;MACb;MACAR,SAAS,CAACc,QAAQ,CAAC;QACjB,CAACN,GAAG,GAAG,MAAM,GAAG,KAAK,GAAG,CAACH,YAAY;QACrCU,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;IACA,OAAO,IAAI;EACb;EACA,IAAIrB,KAAK,KAAK,CAAC,EAAE;IACfK,MAAM,CAACiB,aAAa,CAAC,CAAC,CAAC;IACvBjB,MAAM,CAACkB,YAAY,CAACZ,YAAY,CAAC;IACjC,IAAIT,YAAY,EAAE;MAChBG,MAAM,CAACmB,IAAI,CAAC,uBAAuB,EAAExB,KAAK,EAAEI,QAAQ,CAAC;MACrDC,MAAM,CAACmB,IAAI,CAAC,eAAe,CAAC;IAC9B;EACF,CAAC,MAAM;IACLnB,MAAM,CAACiB,aAAa,CAACtB,KAAK,CAAC;IAC3BK,MAAM,CAACkB,YAAY,CAACZ,YAAY,CAAC;IACjC,IAAIT,YAAY,EAAE;MAChBG,MAAM,CAACmB,IAAI,CAAC,uBAAuB,EAAExB,KAAK,EAAEI,QAAQ,CAAC;MACrDC,MAAM,CAACmB,IAAI,CAAC,iBAAiB,CAAC;IAChC;IACA,IAAI,CAACnB,MAAM,CAACE,SAAS,EAAE;MACrBF,MAAM,CAACE,SAAS,GAAG,IAAI;MACvB,IAAI,CAACF,MAAM,CAACoB,iCAAiC,EAAE;QAC7CpB,MAAM,CAACoB,iCAAiC,GAAG,SAASC,aAAaA,CAACC,CAAC,EAAE;UACnE,IAAI,CAACtB,MAAM,IAAIA,MAAM,CAACuB,SAAS,EAAE;UACjC,IAAID,CAAC,CAACE,MAAM,KAAK,IAAI,EAAE;UACvBxB,MAAM,CAACC,SAAS,CAACwB,mBAAmB,CAAC,eAAe,EAAEzB,MAAM,CAACoB,iCAAiC,CAAC;UAC/FpB,MAAM,CAACoB,iCAAiC,GAAG,IAAI;UAC/C,OAAOpB,MAAM,CAACoB,iCAAiC;UAC/C,IAAIvB,YAAY,EAAE;YAChBG,MAAM,CAACmB,IAAI,CAAC,eAAe,CAAC;UAC9B;QACF,CAAC;MACH;MACAnB,MAAM,CAACC,SAAS,CAACyB,gBAAgB,CAAC,eAAe,EAAE1B,MAAM,CAACoB,iCAAiC,CAAC;IAC9F;EACF;EACA,OAAO,IAAI;AACb"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}