{"ast": null, "code": "import addClasses from './addClasses.js';\nimport removeClasses from './removeClasses.js';\nexport default {\n  addClasses,\n  removeClasses\n};", "map": {"version": 3, "names": ["addClasses", "removeClasses"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/core/classes/index.js"], "sourcesContent": ["import addClasses from './addClasses.js';\nimport removeClasses from './removeClasses.js';\nexport default {\n  addClasses,\n  removeClasses\n};"], "mappings": "AAAA,OAAOA,UAAU,MAAM,iBAAiB;AACxC,OAAOC,aAAa,MAAM,oBAAoB;AAC9C,eAAe;EACbD,UAAU;EACVC;AACF,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}