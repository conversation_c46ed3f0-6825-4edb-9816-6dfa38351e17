{"ast": null, "code": "export const FooterLink2 = [{\n  title: \"Subjects\",\n  links: [{\n    title: \"Al\",\n    link: \"/al\"\n  }, {\n    title: \"Cloud Computing\",\n    link: \"/cloud-computing\"\n  }, {\n    title: \"Code Foundations\",\n    link: \"/code-foundations\"\n  }, {\n    title: \"Computer Science\",\n    link: \"/computer-science\"\n  }, {\n    title: \"Cybersecurity\",\n    link: \"/cybersecurity\"\n  }, {\n    title: \"Data Analytics\",\n    link: \"/data-analytics\"\n  }, {\n    title: \"Data Science\",\n    link: \"/data-science\"\n  }, {\n    title: \"Data Visualization\",\n    link: \"/data-visualization\"\n  }, {\n    title: \"Developer Tools\",\n    link: \"/developer-tools\"\n  }, {\n    title: \"DevOps\",\n    link: \"/devops\"\n  }, {\n    title: \"Game Development\",\n    link: \"/game-development\"\n  }, {\n    title: \"IT\",\n    link: \"/it\"\n  }, {\n    title: \"Machine Learning\",\n    link: \"/machine-learning\"\n  }, {\n    title: \"Math\",\n    link: \"/math\"\n  }, {\n    title: \"Mobile Development\",\n    link: \"/mobile-development\"\n  }, {\n    title: \"Web Design\",\n    link: \"/web-design\"\n  }, {\n    title: \"Web Development\",\n    link: \"/web-development\"\n  }]\n}, {\n  title: \"Languages\",\n  links: [{\n    title: \"Bash\",\n    link: \"/bash\"\n  }, {\n    title: \"C++\",\n    link: \"/c++\"\n  }, {\n    title: \"C#\",\n    link: \"/csharp\"\n  }, {\n    title: \"Go\",\n    link: \"/go\"\n  }, {\n    title: \"HTML & CSS\",\n    link: \"/html-css\"\n  }, {\n    title: \"Java\",\n    link: \"/java\"\n  }, {\n    title: \"JavaScript\",\n    link: \"/javascript\"\n  }, {\n    title: \"Kotlin\",\n    link: \"/kotlin\"\n  }, {\n    title: \"PHP\",\n    link: \"/php\"\n  }, {\n    title: \"Python\",\n    link: \"/python\"\n  }, {\n    title: \"R\",\n    link: \"/r\"\n  }, {\n    title: \"Ruby\",\n    link: \"/ruby\"\n  }, {\n    title: \"SQL\",\n    link: \"/sql\"\n  }, {\n    title: \"Swift\",\n    link: \"/swift\"\n  }]\n}, {\n  title: \"Career building\",\n  links: [{\n    title: \"Career paths\",\n    link: \"/career-paths\"\n  }, {\n    title: \"Career services\",\n    link: \"/career-services\"\n  }, {\n    title: \"Interview prep\",\n    link: \"/interview-prep\"\n  }, {\n    title: \"Professional certification\",\n    link: \"/professional-certification\"\n  }, {\n    title: \"-\",\n    link: \"/hi\"\n  }, {\n    title: \"Full Catalog\",\n    link: \"/full-catalog\"\n  }, {\n    title: \"Beta Content\",\n    link: \"/beta-content\"\n  }]\n}];", "map": {"version": 3, "names": ["FooterLink2", "title", "links", "link"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/src/data/footer-links.js"], "sourcesContent": ["export const FooterLink2 = [\r\n  {\r\n    title: \"Subjects\",\r\n    links: [\r\n      { title: \"Al\", link: \"/al\" },\r\n      { title: \"Cloud Computing\", link: \"/cloud-computing\" },\r\n      { title: \"Code Foundations\", link: \"/code-foundations\" },\r\n      { title: \"Computer Science\", link: \"/computer-science\" },\r\n      { title: \"Cybersecurity\", link: \"/cybersecurity\" },\r\n      { title: \"Data Analytics\", link: \"/data-analytics\" },\r\n      { title: \"Data Science\", link: \"/data-science\" },\r\n      { title: \"Data Visualization\", link: \"/data-visualization\" },\r\n      { title: \"Developer Tools\", link: \"/developer-tools\" },\r\n      { title: \"DevOps\", link: \"/devops\" },\r\n      { title: \"Game Development\", link: \"/game-development\" },\r\n      { title: \"IT\", link: \"/it\" },\r\n      { title: \"Machine Learning\", link: \"/machine-learning\" },\r\n      { title: \"Math\", link: \"/math\" },\r\n      { title: \"Mobile Development\", link: \"/mobile-development\" },\r\n      { title: \"Web Design\", link: \"/web-design\" },\r\n      { title: \"Web Development\", link: \"/web-development\" },\r\n    ],\r\n  },\r\n  {\r\n    title: \"Languages\",\r\n    links: [\r\n      { title: \"Bash\", link: \"/bash\" },\r\n      { title: \"C++\", link: \"/c++\" },\r\n      { title: \"C#\", link: \"/csharp\" },\r\n      { title: \"Go\", link: \"/go\" },\r\n      { title: \"HTML & CSS\", link: \"/html-css\" },\r\n      { title: \"Java\", link: \"/java\" },\r\n      { title: \"JavaScript\", link: \"/javascript\" },\r\n      { title: \"Kotlin\", link: \"/kotlin\" },\r\n      { title: \"PHP\", link: \"/php\" },\r\n      { title: \"Python\", link: \"/python\" },\r\n      { title: \"R\", link: \"/r\" },\r\n      { title: \"Ruby\", link: \"/ruby\" },\r\n      { title: \"SQL\", link: \"/sql\" },\r\n      { title: \"Swift\", link: \"/swift\" },\r\n    ],\r\n  },\r\n  {\r\n    title: \"Career building\",\r\n    links: [\r\n        {title: \"Career paths\", link: \"/career-paths\"},\r\n        {title: \"Career services\", link: \"/career-services\"},\r\n        {title: \"Interview prep\", link: \"/interview-prep\"},\r\n        {title: \"Professional certification\", link: \"/professional-certification\"},\r\n        {title: \"-\", link: \"/hi\"},\r\n        {title: \"Full Catalog\", link: \"/full-catalog\"},\r\n        {title: \"Beta Content\", link: \"/beta-content\"}\r\n    ]\r\n  }\r\n];\r\n"], "mappings": "AAAA,OAAO,MAAMA,WAAW,GAAG,CACzB;EACEC,KAAK,EAAE,UAAU;EACjBC,KAAK,EAAE,CACL;IAAED,KAAK,EAAE,IAAI;IAAEE,IAAI,EAAE;EAAM,CAAC,EAC5B;IAAEF,KAAK,EAAE,iBAAiB;IAAEE,IAAI,EAAE;EAAmB,CAAC,EACtD;IAAEF,KAAK,EAAE,kBAAkB;IAAEE,IAAI,EAAE;EAAoB,CAAC,EACxD;IAAEF,KAAK,EAAE,kBAAkB;IAAEE,IAAI,EAAE;EAAoB,CAAC,EACxD;IAAEF,KAAK,EAAE,eAAe;IAAEE,IAAI,EAAE;EAAiB,CAAC,EAClD;IAAEF,KAAK,EAAE,gBAAgB;IAAEE,IAAI,EAAE;EAAkB,CAAC,EACpD;IAAEF,KAAK,EAAE,cAAc;IAAEE,IAAI,EAAE;EAAgB,CAAC,EAChD;IAAEF,KAAK,EAAE,oBAAoB;IAAEE,IAAI,EAAE;EAAsB,CAAC,EAC5D;IAAEF,KAAK,EAAE,iBAAiB;IAAEE,IAAI,EAAE;EAAmB,CAAC,EACtD;IAAEF,KAAK,EAAE,QAAQ;IAAEE,IAAI,EAAE;EAAU,CAAC,EACpC;IAAEF,KAAK,EAAE,kBAAkB;IAAEE,IAAI,EAAE;EAAoB,CAAC,EACxD;IAAEF,KAAK,EAAE,IAAI;IAAEE,IAAI,EAAE;EAAM,CAAC,EAC5B;IAAEF,KAAK,EAAE,kBAAkB;IAAEE,IAAI,EAAE;EAAoB,CAAC,EACxD;IAAEF,KAAK,EAAE,MAAM;IAAEE,IAAI,EAAE;EAAQ,CAAC,EAChC;IAAEF,KAAK,EAAE,oBAAoB;IAAEE,IAAI,EAAE;EAAsB,CAAC,EAC5D;IAAEF,KAAK,EAAE,YAAY;IAAEE,IAAI,EAAE;EAAc,CAAC,EAC5C;IAAEF,KAAK,EAAE,iBAAiB;IAAEE,IAAI,EAAE;EAAmB,CAAC;AAE1D,CAAC,EACD;EACEF,KAAK,EAAE,WAAW;EAClBC,KAAK,EAAE,CACL;IAAED,KAAK,EAAE,MAAM;IAAEE,IAAI,EAAE;EAAQ,CAAC,EAChC;IAAEF,KAAK,EAAE,KAAK;IAAEE,IAAI,EAAE;EAAO,CAAC,EAC9B;IAAEF,KAAK,EAAE,IAAI;IAAEE,IAAI,EAAE;EAAU,CAAC,EAChC;IAAEF,KAAK,EAAE,IAAI;IAAEE,IAAI,EAAE;EAAM,CAAC,EAC5B;IAAEF,KAAK,EAAE,YAAY;IAAEE,IAAI,EAAE;EAAY,CAAC,EAC1C;IAAEF,KAAK,EAAE,MAAM;IAAEE,IAAI,EAAE;EAAQ,CAAC,EAChC;IAAEF,KAAK,EAAE,YAAY;IAAEE,IAAI,EAAE;EAAc,CAAC,EAC5C;IAAEF,KAAK,EAAE,QAAQ;IAAEE,IAAI,EAAE;EAAU,CAAC,EACpC;IAAEF,KAAK,EAAE,KAAK;IAAEE,IAAI,EAAE;EAAO,CAAC,EAC9B;IAAEF,KAAK,EAAE,QAAQ;IAAEE,IAAI,EAAE;EAAU,CAAC,EACpC;IAAEF,KAAK,EAAE,GAAG;IAAEE,IAAI,EAAE;EAAK,CAAC,EAC1B;IAAEF,KAAK,EAAE,MAAM;IAAEE,IAAI,EAAE;EAAQ,CAAC,EAChC;IAAEF,KAAK,EAAE,KAAK;IAAEE,IAAI,EAAE;EAAO,CAAC,EAC9B;IAAEF,KAAK,EAAE,OAAO;IAAEE,IAAI,EAAE;EAAS,CAAC;AAEtC,CAAC,EACD;EACEF,KAAK,EAAE,iBAAiB;EACxBC,KAAK,EAAE,CACH;IAACD,KAAK,EAAE,cAAc;IAAEE,IAAI,EAAE;EAAe,CAAC,EAC9C;IAACF,KAAK,EAAE,iBAAiB;IAAEE,IAAI,EAAE;EAAkB,CAAC,EACpD;IAACF,KAAK,EAAE,gBAAgB;IAAEE,IAAI,EAAE;EAAiB,CAAC,EAClD;IAACF,KAAK,EAAE,4BAA4B;IAAEE,IAAI,EAAE;EAA6B,CAAC,EAC1E;IAACF,KAAK,EAAE,GAAG;IAAEE,IAAI,EAAE;EAAK,CAAC,EACzB;IAACF,KAAK,EAAE,cAAc;IAAEE,IAAI,EAAE;EAAe,CAAC,EAC9C;IAACF,KAAK,EAAE,cAAc;IAAEE,IAAI,EAAE;EAAe,CAAC;AAEpD,CAAC,CACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}