{"ast": null, "code": "/**\n * @typedef {import('micromark-util-types').Construct} Construct\n * @typedef {import('micromark-util-types').Resolver} Resolver\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').Token} Token\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Tokenizer} Tokenizer\n */\n\nimport { factorySpace } from 'micromark-factory-space';\nimport { markdownLineEnding } from 'micromark-util-character';\nimport { subtokenize } from 'micromark-util-subtokenize';\nimport { codes } from 'micromark-util-symbol/codes.js';\nimport { constants } from 'micromark-util-symbol/constants.js';\nimport { types } from 'micromark-util-symbol/types.js';\nimport { ok as assert } from 'uvu/assert';\n\n/**\n * No name because it must not be turned off.\n * @type {Construct}\n */\nexport const content = {\n  tokenize: tokenizeContent,\n  resolve: resolveContent\n};\n\n/** @type {Construct} */\nconst continuationConstruct = {\n  tokenize: tokenizeContinuation,\n  partial: true\n};\n\n/**\n * Content is transparent: it’s parsed right now. That way, definitions are also\n * parsed right now: before text in paragraphs (specifically, media) are parsed.\n *\n * @type {Resolver}\n */\nfunction resolveContent(events) {\n  subtokenize(events);\n  return events;\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeContent(effects, ok) {\n  /** @type {Token | undefined} */\n  let previous;\n  return chunkStart;\n\n  /**\n   * Before a content chunk.\n   *\n   * ```markdown\n   * > | abc\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function chunkStart(code) {\n    assert(code !== codes.eof && !markdownLineEnding(code), 'expected no eof or eol');\n    effects.enter(types.content);\n    previous = effects.enter(types.chunkContent, {\n      contentType: constants.contentTypeContent\n    });\n    return chunkInside(code);\n  }\n\n  /**\n   * In a content chunk.\n   *\n   * ```markdown\n   * > | abc\n   *     ^^^\n   * ```\n   *\n   * @type {State}\n   */\n  function chunkInside(code) {\n    if (code === codes.eof) {\n      return contentEnd(code);\n    }\n\n    // To do: in `markdown-rs`, each line is parsed on its own, and everything\n    // is stitched together resolving.\n    if (markdownLineEnding(code)) {\n      return effects.check(continuationConstruct, contentContinue, contentEnd)(code);\n    }\n\n    // Data.\n    effects.consume(code);\n    return chunkInside;\n  }\n\n  /**\n   *\n   *\n   * @type {State}\n   */\n  function contentEnd(code) {\n    effects.exit(types.chunkContent);\n    effects.exit(types.content);\n    return ok(code);\n  }\n\n  /**\n   *\n   *\n   * @type {State}\n   */\n  function contentContinue(code) {\n    assert(markdownLineEnding(code), 'expected eol');\n    effects.consume(code);\n    effects.exit(types.chunkContent);\n    assert(previous, 'expected previous token');\n    previous.next = effects.enter(types.chunkContent, {\n      contentType: constants.contentTypeContent,\n      previous\n    });\n    previous = previous.next;\n    return chunkInside;\n  }\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeContinuation(effects, ok, nok) {\n  const self = this;\n  return startLookahead;\n\n  /**\n   *\n   *\n   * @type {State}\n   */\n  function startLookahead(code) {\n    assert(markdownLineEnding(code), 'expected a line ending');\n    effects.exit(types.chunkContent);\n    effects.enter(types.lineEnding);\n    effects.consume(code);\n    effects.exit(types.lineEnding);\n    return factorySpace(effects, prefixed, types.linePrefix);\n  }\n\n  /**\n   *\n   *\n   * @type {State}\n   */\n  function prefixed(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      return nok(code);\n    }\n\n    // Always populated by defaults.\n    assert(self.parser.constructs.disable.null, 'expected `disable.null` to be populated');\n    const tail = self.events[self.events.length - 1];\n    if (!self.parser.constructs.disable.null.includes('codeIndented') && tail && tail[1].type === types.linePrefix && tail[2].sliceSerialize(tail[1], true).length >= constants.tabSize) {\n      return ok(code);\n    }\n    return effects.interrupt(self.parser.constructs.flow, nok, ok)(code);\n  }\n}", "map": {"version": 3, "names": ["factorySpace", "markdownLineEnding", "subtokenize", "codes", "constants", "types", "ok", "assert", "content", "tokenize", "tokenizeContent", "resolve", "resolveContent", "continuationConstruct", "tokenizeContinuation", "partial", "events", "effects", "previous", "chunkStart", "code", "eof", "enter", "chunkContent", "contentType", "contentTypeContent", "chunkInside", "contentEnd", "check", "contentContinue", "consume", "exit", "next", "nok", "self", "startLookahead", "lineEnding", "prefixed", "linePrefix", "parser", "constructs", "disable", "null", "tail", "length", "includes", "type", "sliceSerialize", "tabSize", "interrupt", "flow"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/micromark-core-commonmark/dev/lib/content.js"], "sourcesContent": ["/**\n * @typedef {import('micromark-util-types').Construct} Construct\n * @typedef {import('micromark-util-types').Resolver} Resolver\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').Token} Token\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Tokenizer} Tokenizer\n */\n\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownLineEnding} from 'micromark-util-character'\nimport {subtokenize} from 'micromark-util-subtokenize'\nimport {codes} from 'micromark-util-symbol/codes.js'\nimport {constants} from 'micromark-util-symbol/constants.js'\nimport {types} from 'micromark-util-symbol/types.js'\nimport {ok as assert} from 'uvu/assert'\n\n/**\n * No name because it must not be turned off.\n * @type {Construct}\n */\nexport const content = {tokenize: tokenizeContent, resolve: resolveContent}\n\n/** @type {Construct} */\nconst continuationConstruct = {tokenize: tokenizeContinuation, partial: true}\n\n/**\n * Content is transparent: it’s parsed right now. That way, definitions are also\n * parsed right now: before text in paragraphs (specifically, media) are parsed.\n *\n * @type {Resolver}\n */\nfunction resolveContent(events) {\n  subtokenize(events)\n  return events\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeContent(effects, ok) {\n  /** @type {Token | undefined} */\n  let previous\n\n  return chunkStart\n\n  /**\n   * Before a content chunk.\n   *\n   * ```markdown\n   * > | abc\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function chunkStart(code) {\n    assert(\n      code !== codes.eof && !markdownLineEnding(code),\n      'expected no eof or eol'\n    )\n\n    effects.enter(types.content)\n    previous = effects.enter(types.chunkContent, {\n      contentType: constants.contentTypeContent\n    })\n    return chunkInside(code)\n  }\n\n  /**\n   * In a content chunk.\n   *\n   * ```markdown\n   * > | abc\n   *     ^^^\n   * ```\n   *\n   * @type {State}\n   */\n  function chunkInside(code) {\n    if (code === codes.eof) {\n      return contentEnd(code)\n    }\n\n    // To do: in `markdown-rs`, each line is parsed on its own, and everything\n    // is stitched together resolving.\n    if (markdownLineEnding(code)) {\n      return effects.check(\n        continuationConstruct,\n        contentContinue,\n        contentEnd\n      )(code)\n    }\n\n    // Data.\n    effects.consume(code)\n    return chunkInside\n  }\n\n  /**\n   *\n   *\n   * @type {State}\n   */\n  function contentEnd(code) {\n    effects.exit(types.chunkContent)\n    effects.exit(types.content)\n    return ok(code)\n  }\n\n  /**\n   *\n   *\n   * @type {State}\n   */\n  function contentContinue(code) {\n    assert(markdownLineEnding(code), 'expected eol')\n    effects.consume(code)\n    effects.exit(types.chunkContent)\n    assert(previous, 'expected previous token')\n    previous.next = effects.enter(types.chunkContent, {\n      contentType: constants.contentTypeContent,\n      previous\n    })\n    previous = previous.next\n    return chunkInside\n  }\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeContinuation(effects, ok, nok) {\n  const self = this\n\n  return startLookahead\n\n  /**\n   *\n   *\n   * @type {State}\n   */\n  function startLookahead(code) {\n    assert(markdownLineEnding(code), 'expected a line ending')\n    effects.exit(types.chunkContent)\n    effects.enter(types.lineEnding)\n    effects.consume(code)\n    effects.exit(types.lineEnding)\n    return factorySpace(effects, prefixed, types.linePrefix)\n  }\n\n  /**\n   *\n   *\n   * @type {State}\n   */\n  function prefixed(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      return nok(code)\n    }\n\n    // Always populated by defaults.\n    assert(\n      self.parser.constructs.disable.null,\n      'expected `disable.null` to be populated'\n    )\n\n    const tail = self.events[self.events.length - 1]\n\n    if (\n      !self.parser.constructs.disable.null.includes('codeIndented') &&\n      tail &&\n      tail[1].type === types.linePrefix &&\n      tail[2].sliceSerialize(tail[1], true).length >= constants.tabSize\n    ) {\n      return ok(code)\n    }\n\n    return effects.interrupt(self.parser.constructs.flow, nok, ok)(code)\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,YAAY,QAAO,yBAAyB;AACpD,SAAQC,kBAAkB,QAAO,0BAA0B;AAC3D,SAAQC,WAAW,QAAO,4BAA4B;AACtD,SAAQC,KAAK,QAAO,gCAAgC;AACpD,SAAQC,SAAS,QAAO,oCAAoC;AAC5D,SAAQC,KAAK,QAAO,gCAAgC;AACpD,SAAQC,EAAE,IAAIC,MAAM,QAAO,YAAY;;AAEvC;AACA;AACA;AACA;AACA,OAAO,MAAMC,OAAO,GAAG;EAACC,QAAQ,EAAEC,eAAe;EAAEC,OAAO,EAAEC;AAAc,CAAC;;AAE3E;AACA,MAAMC,qBAAqB,GAAG;EAACJ,QAAQ,EAAEK,oBAAoB;EAAEC,OAAO,EAAE;AAAI,CAAC;;AAE7E;AACA;AACA;AACA;AACA;AACA;AACA,SAASH,cAAcA,CAACI,MAAM,EAAE;EAC9Bd,WAAW,CAACc,MAAM,CAAC;EACnB,OAAOA,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA,SAASN,eAAeA,CAACO,OAAO,EAAEX,EAAE,EAAE;EACpC;EACA,IAAIY,QAAQ;EAEZ,OAAOC,UAAU;;EAEjB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,UAAUA,CAACC,IAAI,EAAE;IACxBb,MAAM,CACJa,IAAI,KAAKjB,KAAK,CAACkB,GAAG,IAAI,CAACpB,kBAAkB,CAACmB,IAAI,CAAC,EAC/C,wBAAwB,CACzB;IAEDH,OAAO,CAACK,KAAK,CAACjB,KAAK,CAACG,OAAO,CAAC;IAC5BU,QAAQ,GAAGD,OAAO,CAACK,KAAK,CAACjB,KAAK,CAACkB,YAAY,EAAE;MAC3CC,WAAW,EAAEpB,SAAS,CAACqB;IACzB,CAAC,CAAC;IACF,OAAOC,WAAW,CAACN,IAAI,CAAC;EAC1B;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASM,WAAWA,CAACN,IAAI,EAAE;IACzB,IAAIA,IAAI,KAAKjB,KAAK,CAACkB,GAAG,EAAE;MACtB,OAAOM,UAAU,CAACP,IAAI,CAAC;IACzB;;IAEA;IACA;IACA,IAAInB,kBAAkB,CAACmB,IAAI,CAAC,EAAE;MAC5B,OAAOH,OAAO,CAACW,KAAK,CAClBf,qBAAqB,EACrBgB,eAAe,EACfF,UAAU,CACX,CAACP,IAAI,CAAC;IACT;;IAEA;IACAH,OAAO,CAACa,OAAO,CAACV,IAAI,CAAC;IACrB,OAAOM,WAAW;EACpB;;EAEA;AACF;AACA;AACA;AACA;EACE,SAASC,UAAUA,CAACP,IAAI,EAAE;IACxBH,OAAO,CAACc,IAAI,CAAC1B,KAAK,CAACkB,YAAY,CAAC;IAChCN,OAAO,CAACc,IAAI,CAAC1B,KAAK,CAACG,OAAO,CAAC;IAC3B,OAAOF,EAAE,CAACc,IAAI,CAAC;EACjB;;EAEA;AACF;AACA;AACA;AACA;EACE,SAASS,eAAeA,CAACT,IAAI,EAAE;IAC7Bb,MAAM,CAACN,kBAAkB,CAACmB,IAAI,CAAC,EAAE,cAAc,CAAC;IAChDH,OAAO,CAACa,OAAO,CAACV,IAAI,CAAC;IACrBH,OAAO,CAACc,IAAI,CAAC1B,KAAK,CAACkB,YAAY,CAAC;IAChChB,MAAM,CAACW,QAAQ,EAAE,yBAAyB,CAAC;IAC3CA,QAAQ,CAACc,IAAI,GAAGf,OAAO,CAACK,KAAK,CAACjB,KAAK,CAACkB,YAAY,EAAE;MAChDC,WAAW,EAAEpB,SAAS,CAACqB,kBAAkB;MACzCP;IACF,CAAC,CAAC;IACFA,QAAQ,GAAGA,QAAQ,CAACc,IAAI;IACxB,OAAON,WAAW;EACpB;AACF;;AAEA;AACA;AACA;AACA;AACA,SAASZ,oBAAoBA,CAACG,OAAO,EAAEX,EAAE,EAAE2B,GAAG,EAAE;EAC9C,MAAMC,IAAI,GAAG,IAAI;EAEjB,OAAOC,cAAc;;EAErB;AACF;AACA;AACA;AACA;EACE,SAASA,cAAcA,CAACf,IAAI,EAAE;IAC5Bb,MAAM,CAACN,kBAAkB,CAACmB,IAAI,CAAC,EAAE,wBAAwB,CAAC;IAC1DH,OAAO,CAACc,IAAI,CAAC1B,KAAK,CAACkB,YAAY,CAAC;IAChCN,OAAO,CAACK,KAAK,CAACjB,KAAK,CAAC+B,UAAU,CAAC;IAC/BnB,OAAO,CAACa,OAAO,CAACV,IAAI,CAAC;IACrBH,OAAO,CAACc,IAAI,CAAC1B,KAAK,CAAC+B,UAAU,CAAC;IAC9B,OAAOpC,YAAY,CAACiB,OAAO,EAAEoB,QAAQ,EAAEhC,KAAK,CAACiC,UAAU,CAAC;EAC1D;;EAEA;AACF;AACA;AACA;AACA;EACE,SAASD,QAAQA,CAACjB,IAAI,EAAE;IACtB,IAAIA,IAAI,KAAKjB,KAAK,CAACkB,GAAG,IAAIpB,kBAAkB,CAACmB,IAAI,CAAC,EAAE;MAClD,OAAOa,GAAG,CAACb,IAAI,CAAC;IAClB;;IAEA;IACAb,MAAM,CACJ2B,IAAI,CAACK,MAAM,CAACC,UAAU,CAACC,OAAO,CAACC,IAAI,EACnC,yCAAyC,CAC1C;IAED,MAAMC,IAAI,GAAGT,IAAI,CAAClB,MAAM,CAACkB,IAAI,CAAClB,MAAM,CAAC4B,MAAM,GAAG,CAAC,CAAC;IAEhD,IACE,CAACV,IAAI,CAACK,MAAM,CAACC,UAAU,CAACC,OAAO,CAACC,IAAI,CAACG,QAAQ,CAAC,cAAc,CAAC,IAC7DF,IAAI,IACJA,IAAI,CAAC,CAAC,CAAC,CAACG,IAAI,KAAKzC,KAAK,CAACiC,UAAU,IACjCK,IAAI,CAAC,CAAC,CAAC,CAACI,cAAc,CAACJ,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAACC,MAAM,IAAIxC,SAAS,CAAC4C,OAAO,EACjE;MACA,OAAO1C,EAAE,CAACc,IAAI,CAAC;IACjB;IAEA,OAAOH,OAAO,CAACgC,SAAS,CAACf,IAAI,CAACK,MAAM,CAACC,UAAU,CAACU,IAAI,EAAEjB,GAAG,EAAE3B,EAAE,CAAC,CAACc,IAAI,CAAC;EACtE;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}