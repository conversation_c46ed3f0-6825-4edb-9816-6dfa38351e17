{"ast": null, "code": "var isCallable = require('../internals/is-callable');\nvar $documentAll = require('../internals/document-all');\nvar documentAll = $documentAll.all;\nmodule.exports = $documentAll.IS_HTMLDDA ? function (it) {\n  return typeof it == 'object' ? it !== null : isCallable(it) || it === documentAll;\n} : function (it) {\n  return typeof it == 'object' ? it !== null : isCallable(it);\n};", "map": {"version": 3, "names": ["isCallable", "require", "$documentAll", "documentAll", "all", "module", "exports", "IS_HTMLDDA", "it"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/core-js-pure/internals/is-object.js"], "sourcesContent": ["var isCallable = require('../internals/is-callable');\nvar $documentAll = require('../internals/document-all');\n\nvar documentAll = $documentAll.all;\n\nmodule.exports = $documentAll.IS_HTMLDDA ? function (it) {\n  return typeof it == 'object' ? it !== null : isCallable(it) || it === documentAll;\n} : function (it) {\n  return typeof it == 'object' ? it !== null : isCallable(it);\n};\n"], "mappings": "AAAA,IAAIA,UAAU,GAAGC,OAAO,CAAC,0BAA0B,CAAC;AACpD,IAAIC,YAAY,GAAGD,OAAO,CAAC,2BAA2B,CAAC;AAEvD,IAAIE,WAAW,GAAGD,YAAY,CAACE,GAAG;AAElCC,MAAM,CAACC,OAAO,GAAGJ,YAAY,CAACK,UAAU,GAAG,UAAUC,EAAE,EAAE;EACvD,OAAO,OAAOA,EAAE,IAAI,QAAQ,GAAGA,EAAE,KAAK,IAAI,GAAGR,UAAU,CAACQ,EAAE,CAAC,IAAIA,EAAE,KAAKL,WAAW;AACnF,CAAC,GAAG,UAAUK,EAAE,EAAE;EAChB,OAAO,OAAOA,EAAE,IAAI,QAAQ,GAAGA,EAAE,KAAK,IAAI,GAAGR,UAAU,CAACQ,EAAE,CAAC;AAC7D,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}