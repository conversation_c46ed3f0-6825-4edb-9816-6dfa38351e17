{"ast": null, "code": "export default function setTransition(duration, byController) {\n  const swiper = this;\n  if (!swiper.params.cssMode) {\n    swiper.wrapperEl.style.transitionDuration = `${duration}ms`;\n  }\n  swiper.emit('setTransition', duration, byController);\n}", "map": {"version": 3, "names": ["setTransition", "duration", "byController", "swiper", "params", "cssMode", "wrapperEl", "style", "transitionDuration", "emit"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/core/transition/setTransition.js"], "sourcesContent": ["export default function setTransition(duration, byController) {\n  const swiper = this;\n  if (!swiper.params.cssMode) {\n    swiper.wrapperEl.style.transitionDuration = `${duration}ms`;\n  }\n  swiper.emit('setTransition', duration, byController);\n}"], "mappings": "AAAA,eAAe,SAASA,aAAaA,CAACC,QAAQ,EAAEC,YAAY,EAAE;EAC5D,MAAMC,MAAM,GAAG,IAAI;EACnB,IAAI,CAACA,MAAM,CAACC,MAAM,CAACC,OAAO,EAAE;IAC1BF,MAAM,CAACG,SAAS,CAACC,KAAK,CAACC,kBAAkB,GAAI,GAAEP,QAAS,IAAG;EAC7D;EACAE,MAAM,CAACM,IAAI,CAAC,eAAe,EAAER,QAAQ,EAAEC,YAAY,CAAC;AACtD"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}