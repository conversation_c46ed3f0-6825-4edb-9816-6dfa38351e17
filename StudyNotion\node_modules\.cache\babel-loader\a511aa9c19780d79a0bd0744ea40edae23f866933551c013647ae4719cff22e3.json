{"ast": null, "code": "/**\n * @typedef {import('micromark-util-types').Construct} Construct\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Tokenizer} Tokenizer\n */\n\nimport { codes } from 'micromark-util-symbol/codes.js';\nimport { types } from 'micromark-util-symbol/types.js';\nimport { ok as assert } from 'uvu/assert';\nimport { labelEnd } from './label-end.js';\n\n/** @type {Construct} */\nexport const labelStartImage = {\n  name: 'labelStartImage',\n  tokenize: tokenizeLabelStartImage,\n  resolveAll: labelEnd.resolveAll\n};\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeLabelStartImage(effects, ok, nok) {\n  const self = this;\n  return start;\n\n  /**\n   * Start of label (image) start.\n   *\n   * ```markdown\n   * > | a ![b] c\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.exclamationMark, 'expected `!`');\n    effects.enter(types.labelImage);\n    effects.enter(types.labelImageMarker);\n    effects.consume(code);\n    effects.exit(types.labelImageMarker);\n    return open;\n  }\n\n  /**\n   * After `!`, at `[`.\n   *\n   * ```markdown\n   * > | a ![b] c\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function open(code) {\n    if (code === codes.leftSquareBracket) {\n      effects.enter(types.labelMarker);\n      effects.consume(code);\n      effects.exit(types.labelMarker);\n      effects.exit(types.labelImage);\n      return after;\n    }\n    return nok(code);\n  }\n\n  /**\n   * After `![`.\n   *\n   * ```markdown\n   * > | a ![b] c\n   *         ^\n   * ```\n   *\n   * This is needed in because, when GFM footnotes are enabled, images never\n   * form when started with a `^`.\n   * Instead, links form:\n   *\n   * ```markdown\n   * ![^a](b)\n   *\n   * ![^a][b]\n   *\n   * [b]: c\n   * ```\n   *\n   * ```html\n   * <p>!<a href=\\\"b\\\">^a</a></p>\n   * <p>!<a href=\\\"c\\\">^a</a></p>\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    // To do: use a new field to do this, this is still needed for\n    // `micromark-extension-gfm-footnote`, but the `label-start-link`\n    // behavior isn’t.\n    // Hidden footnotes hook.\n    /* c8 ignore next 3 */\n    return code === codes.caret && '_hiddenFootnoteSupport' in self.parser.constructs ? nok(code) : ok(code);\n  }\n}", "map": {"version": 3, "names": ["codes", "types", "ok", "assert", "labelEnd", "labelStartImage", "name", "tokenize", "tokenizeLabelStartImage", "resolveAll", "effects", "nok", "self", "start", "code", "exclamationMark", "enter", "labelImage", "labelImageMarker", "consume", "exit", "open", "leftSquareBracket", "labelMarker", "after", "caret", "parser", "constructs"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/micromark-core-commonmark/dev/lib/label-start-image.js"], "sourcesContent": ["/**\n * @typedef {import('micromark-util-types').Construct} Construct\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Tokenizer} Tokenizer\n */\n\nimport {codes} from 'micromark-util-symbol/codes.js'\nimport {types} from 'micromark-util-symbol/types.js'\nimport {ok as assert} from 'uvu/assert'\nimport {labelEnd} from './label-end.js'\n\n/** @type {Construct} */\nexport const labelStartImage = {\n  name: 'labelStartImage',\n  tokenize: tokenizeLabelStartImage,\n  resolveAll: labelEnd.resolveAll\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeLabelStartImage(effects, ok, nok) {\n  const self = this\n\n  return start\n\n  /**\n   * Start of label (image) start.\n   *\n   * ```markdown\n   * > | a ![b] c\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.exclamationMark, 'expected `!`')\n    effects.enter(types.labelImage)\n    effects.enter(types.labelImageMarker)\n    effects.consume(code)\n    effects.exit(types.labelImageMarker)\n    return open\n  }\n\n  /**\n   * After `!`, at `[`.\n   *\n   * ```markdown\n   * > | a ![b] c\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function open(code) {\n    if (code === codes.leftSquareBracket) {\n      effects.enter(types.labelMarker)\n      effects.consume(code)\n      effects.exit(types.labelMarker)\n      effects.exit(types.labelImage)\n      return after\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After `![`.\n   *\n   * ```markdown\n   * > | a ![b] c\n   *         ^\n   * ```\n   *\n   * This is needed in because, when GFM footnotes are enabled, images never\n   * form when started with a `^`.\n   * Instead, links form:\n   *\n   * ```markdown\n   * ![^a](b)\n   *\n   * ![^a][b]\n   *\n   * [b]: c\n   * ```\n   *\n   * ```html\n   * <p>!<a href=\\\"b\\\">^a</a></p>\n   * <p>!<a href=\\\"c\\\">^a</a></p>\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    // To do: use a new field to do this, this is still needed for\n    // `micromark-extension-gfm-footnote`, but the `label-start-link`\n    // behavior isn’t.\n    // Hidden footnotes hook.\n    /* c8 ignore next 3 */\n    return code === codes.caret &&\n      '_hiddenFootnoteSupport' in self.parser.constructs\n      ? nok(code)\n      : ok(code)\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,KAAK,QAAO,gCAAgC;AACpD,SAAQC,KAAK,QAAO,gCAAgC;AACpD,SAAQC,EAAE,IAAIC,MAAM,QAAO,YAAY;AACvC,SAAQC,QAAQ,QAAO,gBAAgB;;AAEvC;AACA,OAAO,MAAMC,eAAe,GAAG;EAC7BC,IAAI,EAAE,iBAAiB;EACvBC,QAAQ,EAAEC,uBAAuB;EACjCC,UAAU,EAAEL,QAAQ,CAACK;AACvB,CAAC;;AAED;AACA;AACA;AACA;AACA,SAASD,uBAAuBA,CAACE,OAAO,EAAER,EAAE,EAAES,GAAG,EAAE;EACjD,MAAMC,IAAI,GAAG,IAAI;EAEjB,OAAOC,KAAK;;EAEZ;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,KAAKA,CAACC,IAAI,EAAE;IACnBX,MAAM,CAACW,IAAI,KAAKd,KAAK,CAACe,eAAe,EAAE,cAAc,CAAC;IACtDL,OAAO,CAACM,KAAK,CAACf,KAAK,CAACgB,UAAU,CAAC;IAC/BP,OAAO,CAACM,KAAK,CAACf,KAAK,CAACiB,gBAAgB,CAAC;IACrCR,OAAO,CAACS,OAAO,CAACL,IAAI,CAAC;IACrBJ,OAAO,CAACU,IAAI,CAACnB,KAAK,CAACiB,gBAAgB,CAAC;IACpC,OAAOG,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,IAAIA,CAACP,IAAI,EAAE;IAClB,IAAIA,IAAI,KAAKd,KAAK,CAACsB,iBAAiB,EAAE;MACpCZ,OAAO,CAACM,KAAK,CAACf,KAAK,CAACsB,WAAW,CAAC;MAChCb,OAAO,CAACS,OAAO,CAACL,IAAI,CAAC;MACrBJ,OAAO,CAACU,IAAI,CAACnB,KAAK,CAACsB,WAAW,CAAC;MAC/Bb,OAAO,CAACU,IAAI,CAACnB,KAAK,CAACgB,UAAU,CAAC;MAC9B,OAAOO,KAAK;IACd;IAEA,OAAOb,GAAG,CAACG,IAAI,CAAC;EAClB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASU,KAAKA,CAACV,IAAI,EAAE;IACnB;IACA;IACA;IACA;IACA;IACA,OAAOA,IAAI,KAAKd,KAAK,CAACyB,KAAK,IACzB,wBAAwB,IAAIb,IAAI,CAACc,MAAM,CAACC,UAAU,GAChDhB,GAAG,CAACG,IAAI,CAAC,GACTZ,EAAE,CAACY,IAAI,CAAC;EACd;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}