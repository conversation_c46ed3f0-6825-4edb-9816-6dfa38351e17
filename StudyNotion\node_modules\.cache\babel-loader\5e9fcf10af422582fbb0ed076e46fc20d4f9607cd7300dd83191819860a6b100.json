{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\temp\\\\StudyNotion\\\\src\\\\components\\\\core\\\\Course\\\\CourseSubSectionAccordion.jsx\";\nimport React, { useEffect, useRef, useState } from \"react\";\nimport { AiOutlineDown } from \"react-icons/ai\";\nimport { HiOutlineVideoCamera } from \"react-icons/hi\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction CourseSubSectionAccordion(_ref) {\n  let {\n    subSec\n  } = _ref;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between py-2\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `flex items-center gap-2`,\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: /*#__PURE__*/_jsxDEV(HiOutlineVideoCamera, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 11,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 10,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: subSec === null || subSec === void 0 ? void 0 : subSec.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 13,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 5\n  }, this);\n}\n_c = CourseSubSectionAccordion;\nexport default CourseSubSectionAccordion;\nvar _c;\n$RefreshReg$(_c, \"CourseSubSectionAccordion\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "AiOutlineDown", "HiOutlineVideoCamera", "jsxDEV", "_jsxDEV", "CourseSubSectionAccordion", "_ref", "subSec", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/src/components/core/Course/CourseSubSectionAccordion.jsx"], "sourcesContent": ["import React, { useEffect, useRef, useState } from \"react\"\r\nimport { AiOutlineDown } from \"react-icons/ai\"\r\nimport { HiOutlineVideoCamera } from \"react-icons/hi\"\r\n\r\nfunction CourseSubSectionAccordion({ subSec }) {\r\n  return (\r\n    <div>\r\n      <div className=\"flex justify-between py-2\">\r\n        <div className={`flex items-center gap-2`}>\r\n          <span>\r\n            <HiOutlineVideoCamera />\r\n          </span>\r\n          <p>{subSec?.title}</p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default CourseSubSectionAccordion"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,SAASC,aAAa,QAAQ,gBAAgB;AAC9C,SAASC,oBAAoB,QAAQ,gBAAgB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAErD,SAASC,yBAAyBA,CAAAC,IAAA,EAAa;EAAA,IAAZ;IAAEC;EAAO,CAAC,GAAAD,IAAA;EAC3C,oBACEF,OAAA;IAAAI,QAAA,eACEJ,OAAA;MAAKK,SAAS,EAAC,2BAA2B;MAAAD,QAAA,eACxCJ,OAAA;QAAKK,SAAS,EAAG,yBAAyB;QAAAD,QAAA,gBACxCJ,OAAA;UAAAI,QAAA,eACEJ,OAAA,CAACF,oBAAoB;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAG;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACnB,eACPT,OAAA;UAAAI,QAAA,EAAID,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEO;QAAK;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAK;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAClB;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EACF;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACF;AAEV;AAACE,EAAA,GAbQV,yBAAyB;AAelC,eAAeA,yBAAyB;AAAA,IAAAU,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}