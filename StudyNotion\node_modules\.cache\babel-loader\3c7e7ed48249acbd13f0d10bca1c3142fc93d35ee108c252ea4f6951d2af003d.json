{"ast": null, "code": "/*!\n * Chart.js v4.3.0\n * https://www.chartjs.org\n * (c) 2023 Chart.js Contributors\n * Released under the MIT License\n */\nimport { Color } from '@kurkle/color';\n\n/**\n * @namespace Chart.helpers\n */ /**\n    * An empty function that can be used, for example, for optional callback.\n    */\nfunction noop() {\n  /* noop */}\n/**\n * Returns a unique id, sequentially generated from a global variable.\n */\nconst uid = (() => {\n  let id = 0;\n  return () => id++;\n})();\n/**\n * Returns true if `value` is neither null nor undefined, else returns false.\n * @param value - The value to test.\n * @since 2.7.0\n */\nfunction isNullOrUndef(value) {\n  return value === null || typeof value === 'undefined';\n}\n/**\n * Returns true if `value` is an array (including typed arrays), else returns false.\n * @param value - The value to test.\n * @function\n */\nfunction isArray(value) {\n  if (Array.isArray && Array.isArray(value)) {\n    return true;\n  }\n  const type = Object.prototype.toString.call(value);\n  if (type.slice(0, 7) === '[object' && type.slice(-6) === 'Array]') {\n    return true;\n  }\n  return false;\n}\n/**\n * Returns true if `value` is an object (excluding null), else returns false.\n * @param value - The value to test.\n * @since 2.7.0\n */\nfunction isObject(value) {\n  return value !== null && Object.prototype.toString.call(value) === '[object Object]';\n}\n/**\n * Returns true if `value` is a finite number, else returns false\n * @param value  - The value to test.\n */\nfunction isNumberFinite(value) {\n  return (typeof value === 'number' || value instanceof Number) && isFinite(+value);\n}\n/**\n * Returns `value` if finite, else returns `defaultValue`.\n * @param value - The value to return if defined.\n * @param defaultValue - The value to return if `value` is not finite.\n */\nfunction finiteOrDefault(value, defaultValue) {\n  return isNumberFinite(value) ? value : defaultValue;\n}\n/**\n * Returns `value` if defined, else returns `defaultValue`.\n * @param value - The value to return if defined.\n * @param defaultValue - The value to return if `value` is undefined.\n */\nfunction valueOrDefault(value, defaultValue) {\n  return typeof value === 'undefined' ? defaultValue : value;\n}\nconst toPercentage = (value, dimension) => typeof value === 'string' && value.endsWith('%') ? parseFloat(value) / 100 : +value / dimension;\nconst toDimension = (value, dimension) => typeof value === 'string' && value.endsWith('%') ? parseFloat(value) / 100 * dimension : +value;\n/**\n * Calls `fn` with the given `args` in the scope defined by `thisArg` and returns the\n * value returned by `fn`. If `fn` is not a function, this method returns undefined.\n * @param fn - The function to call.\n * @param args - The arguments with which `fn` should be called.\n * @param [thisArg] - The value of `this` provided for the call to `fn`.\n */\nfunction callback(fn, args, thisArg) {\n  if (fn && typeof fn.call === 'function') {\n    return fn.apply(thisArg, args);\n  }\n}\nfunction each(loopable, fn, thisArg, reverse) {\n  let i, len, keys;\n  if (isArray(loopable)) {\n    len = loopable.length;\n    if (reverse) {\n      for (i = len - 1; i >= 0; i--) {\n        fn.call(thisArg, loopable[i], i);\n      }\n    } else {\n      for (i = 0; i < len; i++) {\n        fn.call(thisArg, loopable[i], i);\n      }\n    }\n  } else if (isObject(loopable)) {\n    keys = Object.keys(loopable);\n    len = keys.length;\n    for (i = 0; i < len; i++) {\n      fn.call(thisArg, loopable[keys[i]], keys[i]);\n    }\n  }\n}\n/**\n * Returns true if the `a0` and `a1` arrays have the same content, else returns false.\n * @param a0 - The array to compare\n * @param a1 - The array to compare\n * @private\n */\nfunction _elementsEqual(a0, a1) {\n  let i, ilen, v0, v1;\n  if (!a0 || !a1 || a0.length !== a1.length) {\n    return false;\n  }\n  for (i = 0, ilen = a0.length; i < ilen; ++i) {\n    v0 = a0[i];\n    v1 = a1[i];\n    if (v0.datasetIndex !== v1.datasetIndex || v0.index !== v1.index) {\n      return false;\n    }\n  }\n  return true;\n}\n/**\n * Returns a deep copy of `source` without keeping references on objects and arrays.\n * @param source - The value to clone.\n */\nfunction clone(source) {\n  if (isArray(source)) {\n    return source.map(clone);\n  }\n  if (isObject(source)) {\n    const target = Object.create(null);\n    const keys = Object.keys(source);\n    const klen = keys.length;\n    let k = 0;\n    for (; k < klen; ++k) {\n      target[keys[k]] = clone(source[keys[k]]);\n    }\n    return target;\n  }\n  return source;\n}\nfunction isValidKey(key) {\n  return ['__proto__', 'prototype', 'constructor'].indexOf(key) === -1;\n}\n/**\n * The default merger when Chart.helpers.merge is called without merger option.\n * Note(SB): also used by mergeConfig and mergeScaleConfig as fallback.\n * @private\n */\nfunction _merger(key, target, source, options) {\n  if (!isValidKey(key)) {\n    return;\n  }\n  const tval = target[key];\n  const sval = source[key];\n  if (isObject(tval) && isObject(sval)) {\n    // eslint-disable-next-line @typescript-eslint/no-use-before-define\n    merge(tval, sval, options);\n  } else {\n    target[key] = clone(sval);\n  }\n}\nfunction merge(target, source, options) {\n  const sources = isArray(source) ? source : [source];\n  const ilen = sources.length;\n  if (!isObject(target)) {\n    return target;\n  }\n  options = options || {};\n  const merger = options.merger || _merger;\n  let current;\n  for (let i = 0; i < ilen; ++i) {\n    current = sources[i];\n    if (!isObject(current)) {\n      continue;\n    }\n    const keys = Object.keys(current);\n    for (let k = 0, klen = keys.length; k < klen; ++k) {\n      merger(keys[k], target, current, options);\n    }\n  }\n  return target;\n}\nfunction mergeIf(target, source) {\n  // eslint-disable-next-line @typescript-eslint/no-use-before-define\n  return merge(target, source, {\n    merger: _mergerIf\n  });\n}\n/**\n * Merges source[key] in target[key] only if target[key] is undefined.\n * @private\n */\nfunction _mergerIf(key, target, source) {\n  if (!isValidKey(key)) {\n    return;\n  }\n  const tval = target[key];\n  const sval = source[key];\n  if (isObject(tval) && isObject(sval)) {\n    mergeIf(tval, sval);\n  } else if (!Object.prototype.hasOwnProperty.call(target, key)) {\n    target[key] = clone(sval);\n  }\n}\n/**\n * @private\n */\nfunction _deprecated(scope, value, previous, current) {\n  if (value !== undefined) {\n    console.warn(scope + ': \"' + previous + '\" is deprecated. Please use \"' + current + '\" instead');\n  }\n}\n// resolveObjectKey resolver cache\nconst keyResolvers = {\n  // Chart.helpers.core resolveObjectKey should resolve empty key to root object\n  '': v => v,\n  // default resolvers\n  x: o => o.x,\n  y: o => o.y\n};\n/**\n * @private\n */\nfunction _splitKey(key) {\n  const parts = key.split('.');\n  const keys = [];\n  let tmp = '';\n  for (const part of parts) {\n    tmp += part;\n    if (tmp.endsWith('\\\\')) {\n      tmp = tmp.slice(0, -1) + '.';\n    } else {\n      keys.push(tmp);\n      tmp = '';\n    }\n  }\n  return keys;\n}\nfunction _getKeyResolver(key) {\n  const keys = _splitKey(key);\n  return obj => {\n    for (const k of keys) {\n      if (k === '') {\n        break;\n      }\n      obj = obj && obj[k];\n    }\n    return obj;\n  };\n}\nfunction resolveObjectKey(obj, key) {\n  const resolver = keyResolvers[key] || (keyResolvers[key] = _getKeyResolver(key));\n  return resolver(obj);\n}\n/**\n * @private\n */\nfunction _capitalize(str) {\n  return str.charAt(0).toUpperCase() + str.slice(1);\n}\nconst defined = value => typeof value !== 'undefined';\nconst isFunction = value => typeof value === 'function';\n// Adapted from https://stackoverflow.com/questions/31128855/comparing-ecma6-sets-for-equality#31129384\nconst setsEqual = (a, b) => {\n  if (a.size !== b.size) {\n    return false;\n  }\n  for (const item of a) {\n    if (!b.has(item)) {\n      return false;\n    }\n  }\n  return true;\n};\n/**\n * @param e - The event\n * @private\n */\nfunction _isClickEvent(e) {\n  return e.type === 'mouseup' || e.type === 'click' || e.type === 'contextmenu';\n}\n\n/**\n * @alias Chart.helpers.math\n * @namespace\n */\nconst PI = Math.PI;\nconst TAU = 2 * PI;\nconst PITAU = TAU + PI;\nconst INFINITY = Number.POSITIVE_INFINITY;\nconst RAD_PER_DEG = PI / 180;\nconst HALF_PI = PI / 2;\nconst QUARTER_PI = PI / 4;\nconst TWO_THIRDS_PI = PI * 2 / 3;\nconst log10 = Math.log10;\nconst sign = Math.sign;\nfunction almostEquals(x, y, epsilon) {\n  return Math.abs(x - y) < epsilon;\n}\n/**\n * Implementation of the nice number algorithm used in determining where axis labels will go\n */\nfunction niceNum(range) {\n  const roundedRange = Math.round(range);\n  range = almostEquals(range, roundedRange, range / 1000) ? roundedRange : range;\n  const niceRange = Math.pow(10, Math.floor(log10(range)));\n  const fraction = range / niceRange;\n  const niceFraction = fraction <= 1 ? 1 : fraction <= 2 ? 2 : fraction <= 5 ? 5 : 10;\n  return niceFraction * niceRange;\n}\n/**\n * Returns an array of factors sorted from 1 to sqrt(value)\n * @private\n */\nfunction _factorize(value) {\n  const result = [];\n  const sqrt = Math.sqrt(value);\n  let i;\n  for (i = 1; i < sqrt; i++) {\n    if (value % i === 0) {\n      result.push(i);\n      result.push(value / i);\n    }\n  }\n  if (sqrt === (sqrt | 0)) {\n    result.push(sqrt);\n  }\n  result.sort((a, b) => a - b).pop();\n  return result;\n}\nfunction isNumber(n) {\n  return !isNaN(parseFloat(n)) && isFinite(n);\n}\nfunction almostWhole(x, epsilon) {\n  const rounded = Math.round(x);\n  return rounded - epsilon <= x && rounded + epsilon >= x;\n}\n/**\n * @private\n */\nfunction _setMinAndMaxByKey(array, target, property) {\n  let i, ilen, value;\n  for (i = 0, ilen = array.length; i < ilen; i++) {\n    value = array[i][property];\n    if (!isNaN(value)) {\n      target.min = Math.min(target.min, value);\n      target.max = Math.max(target.max, value);\n    }\n  }\n}\nfunction toRadians(degrees) {\n  return degrees * (PI / 180);\n}\nfunction toDegrees(radians) {\n  return radians * (180 / PI);\n}\n/**\n * Returns the number of decimal places\n * i.e. the number of digits after the decimal point, of the value of this Number.\n * @param x - A number.\n * @returns The number of decimal places.\n * @private\n */\nfunction _decimalPlaces(x) {\n  if (!isNumberFinite(x)) {\n    return;\n  }\n  let e = 1;\n  let p = 0;\n  while (Math.round(x * e) / e !== x) {\n    e *= 10;\n    p++;\n  }\n  return p;\n}\n// Gets the angle from vertical upright to the point about a centre.\nfunction getAngleFromPoint(centrePoint, anglePoint) {\n  const distanceFromXCenter = anglePoint.x - centrePoint.x;\n  const distanceFromYCenter = anglePoint.y - centrePoint.y;\n  const radialDistanceFromCenter = Math.sqrt(distanceFromXCenter * distanceFromXCenter + distanceFromYCenter * distanceFromYCenter);\n  let angle = Math.atan2(distanceFromYCenter, distanceFromXCenter);\n  if (angle < -0.5 * PI) {\n    angle += TAU; // make sure the returned angle is in the range of (-PI/2, 3PI/2]\n  }\n\n  return {\n    angle,\n    distance: radialDistanceFromCenter\n  };\n}\nfunction distanceBetweenPoints(pt1, pt2) {\n  return Math.sqrt(Math.pow(pt2.x - pt1.x, 2) + Math.pow(pt2.y - pt1.y, 2));\n}\n/**\n * Shortest distance between angles, in either direction.\n * @private\n */\nfunction _angleDiff(a, b) {\n  return (a - b + PITAU) % TAU - PI;\n}\n/**\n * Normalize angle to be between 0 and 2*PI\n * @private\n */\nfunction _normalizeAngle(a) {\n  return (a % TAU + TAU) % TAU;\n}\n/**\n * @private\n */\nfunction _angleBetween(angle, start, end, sameAngleIsFullCircle) {\n  const a = _normalizeAngle(angle);\n  const s = _normalizeAngle(start);\n  const e = _normalizeAngle(end);\n  const angleToStart = _normalizeAngle(s - a);\n  const angleToEnd = _normalizeAngle(e - a);\n  const startToAngle = _normalizeAngle(a - s);\n  const endToAngle = _normalizeAngle(a - e);\n  return a === s || a === e || sameAngleIsFullCircle && s === e || angleToStart > angleToEnd && startToAngle < endToAngle;\n}\n/**\n * Limit `value` between `min` and `max`\n * @param value\n * @param min\n * @param max\n * @private\n */\nfunction _limitValue(value, min, max) {\n  return Math.max(min, Math.min(max, value));\n}\n/**\n * @param {number} value\n * @private\n */\nfunction _int16Range(value) {\n  return _limitValue(value, -32768, 32767);\n}\n/**\n * @param value\n * @param start\n * @param end\n * @param [epsilon]\n * @private\n */\nfunction _isBetween(value, start, end) {\n  let epsilon = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 1e-6;\n  return value >= Math.min(start, end) - epsilon && value <= Math.max(start, end) + epsilon;\n}\nfunction _lookup(table, value, cmp) {\n  cmp = cmp || (index => table[index] < value);\n  let hi = table.length - 1;\n  let lo = 0;\n  let mid;\n  while (hi - lo > 1) {\n    mid = lo + hi >> 1;\n    if (cmp(mid)) {\n      lo = mid;\n    } else {\n      hi = mid;\n    }\n  }\n  return {\n    lo,\n    hi\n  };\n}\n/**\n * Binary search\n * @param table - the table search. must be sorted!\n * @param key - property name for the value in each entry\n * @param value - value to find\n * @param last - lookup last index\n * @private\n */\nconst _lookupByKey = (table, key, value, last) => _lookup(table, value, last ? index => {\n  const ti = table[index][key];\n  return ti < value || ti === value && table[index + 1][key] === value;\n} : index => table[index][key] < value);\n/**\n * Reverse binary search\n * @param table - the table search. must be sorted!\n * @param key - property name for the value in each entry\n * @param value - value to find\n * @private\n */\nconst _rlookupByKey = (table, key, value) => _lookup(table, value, index => table[index][key] >= value);\n/**\n * Return subset of `values` between `min` and `max` inclusive.\n * Values are assumed to be in sorted order.\n * @param values - sorted array of values\n * @param min - min value\n * @param max - max value\n */\nfunction _filterBetween(values, min, max) {\n  let start = 0;\n  let end = values.length;\n  while (start < end && values[start] < min) {\n    start++;\n  }\n  while (end > start && values[end - 1] > max) {\n    end--;\n  }\n  return start > 0 || end < values.length ? values.slice(start, end) : values;\n}\nconst arrayEvents = ['push', 'pop', 'shift', 'splice', 'unshift'];\nfunction listenArrayEvents(array, listener) {\n  if (array._chartjs) {\n    array._chartjs.listeners.push(listener);\n    return;\n  }\n  Object.defineProperty(array, '_chartjs', {\n    configurable: true,\n    enumerable: false,\n    value: {\n      listeners: [listener]\n    }\n  });\n  arrayEvents.forEach(key => {\n    const method = '_onData' + _capitalize(key);\n    const base = array[key];\n    Object.defineProperty(array, key, {\n      configurable: true,\n      enumerable: false,\n      value() {\n        for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n          args[_key] = arguments[_key];\n        }\n        const res = base.apply(this, args);\n        array._chartjs.listeners.forEach(object => {\n          if (typeof object[method] === 'function') {\n            object[method](...args);\n          }\n        });\n        return res;\n      }\n    });\n  });\n}\nfunction unlistenArrayEvents(array, listener) {\n  const stub = array._chartjs;\n  if (!stub) {\n    return;\n  }\n  const listeners = stub.listeners;\n  const index = listeners.indexOf(listener);\n  if (index !== -1) {\n    listeners.splice(index, 1);\n  }\n  if (listeners.length > 0) {\n    return;\n  }\n  arrayEvents.forEach(key => {\n    delete array[key];\n  });\n  delete array._chartjs;\n}\n/**\n * @param items\n */\nfunction _arrayUnique(items) {\n  const set = new Set(items);\n  if (set.size === items.length) {\n    return items;\n  }\n  return Array.from(set);\n}\nfunction fontString(pixelSize, fontStyle, fontFamily) {\n  return fontStyle + ' ' + pixelSize + 'px ' + fontFamily;\n}\n/**\n* Request animation polyfill\n*/\nconst requestAnimFrame = function () {\n  if (typeof window === 'undefined') {\n    return function (callback) {\n      return callback();\n    };\n  }\n  return window.requestAnimationFrame;\n}();\n/**\n * Throttles calling `fn` once per animation frame\n * Latest arguments are used on the actual call\n */\nfunction throttled(fn, thisArg) {\n  let argsToUse = [];\n  let ticking = false;\n  return function () {\n    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      args[_key2] = arguments[_key2];\n    }\n    // Save the args for use later\n    argsToUse = args;\n    if (!ticking) {\n      ticking = true;\n      requestAnimFrame.call(window, () => {\n        ticking = false;\n        fn.apply(thisArg, argsToUse);\n      });\n    }\n  };\n}\n/**\n * Debounces calling `fn` for `delay` ms\n */\nfunction debounce(fn, delay) {\n  let timeout;\n  return function () {\n    for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n      args[_key3] = arguments[_key3];\n    }\n    if (delay) {\n      clearTimeout(timeout);\n      timeout = setTimeout(fn, delay, args);\n    } else {\n      fn.apply(this, args);\n    }\n    return delay;\n  };\n}\n/**\n * Converts 'start' to 'left', 'end' to 'right' and others to 'center'\n * @private\n */\nconst _toLeftRightCenter = align => align === 'start' ? 'left' : align === 'end' ? 'right' : 'center';\n/**\n * Returns `start`, `end` or `(start + end) / 2` depending on `align`. Defaults to `center`\n * @private\n */\nconst _alignStartEnd = (align, start, end) => align === 'start' ? start : align === 'end' ? end : (start + end) / 2;\n/**\n * Returns `left`, `right` or `(left + right) / 2` depending on `align`. Defaults to `left`\n * @private\n */\nconst _textX = (align, left, right, rtl) => {\n  const check = rtl ? 'left' : 'right';\n  return align === check ? right : align === 'center' ? (left + right) / 2 : left;\n};\n/**\n * Return start and count of visible points.\n * @private\n */\nfunction _getStartAndCountOfVisiblePoints(meta, points, animationsDisabled) {\n  const pointCount = points.length;\n  let start = 0;\n  let count = pointCount;\n  if (meta._sorted) {\n    const {\n      iScale,\n      _parsed\n    } = meta;\n    const axis = iScale.axis;\n    const {\n      min,\n      max,\n      minDefined,\n      maxDefined\n    } = iScale.getUserBounds();\n    if (minDefined) {\n      start = _limitValue(Math.min(\n      // @ts-expect-error Need to type _parsed\n      _lookupByKey(_parsed, iScale.axis, min).lo,\n      // @ts-expect-error Need to fix types on _lookupByKey\n      animationsDisabled ? pointCount : _lookupByKey(points, axis, iScale.getPixelForValue(min)).lo), 0, pointCount - 1);\n    }\n    if (maxDefined) {\n      count = _limitValue(Math.max(\n      // @ts-expect-error Need to type _parsed\n      _lookupByKey(_parsed, iScale.axis, max, true).hi + 1,\n      // @ts-expect-error Need to fix types on _lookupByKey\n      animationsDisabled ? 0 : _lookupByKey(points, axis, iScale.getPixelForValue(max), true).hi + 1), start, pointCount) - start;\n    } else {\n      count = pointCount - start;\n    }\n  }\n  return {\n    start,\n    count\n  };\n}\n/**\n * Checks if the scale ranges have changed.\n * @param {object} meta - dataset meta.\n * @returns {boolean}\n * @private\n */\nfunction _scaleRangesChanged(meta) {\n  const {\n    xScale,\n    yScale,\n    _scaleRanges\n  } = meta;\n  const newRanges = {\n    xmin: xScale.min,\n    xmax: xScale.max,\n    ymin: yScale.min,\n    ymax: yScale.max\n  };\n  if (!_scaleRanges) {\n    meta._scaleRanges = newRanges;\n    return true;\n  }\n  const changed = _scaleRanges.xmin !== xScale.min || _scaleRanges.xmax !== xScale.max || _scaleRanges.ymin !== yScale.min || _scaleRanges.ymax !== yScale.max;\n  Object.assign(_scaleRanges, newRanges);\n  return changed;\n}\nconst atEdge = t => t === 0 || t === 1;\nconst elasticIn = (t, s, p) => -(Math.pow(2, 10 * (t -= 1)) * Math.sin((t - s) * TAU / p));\nconst elasticOut = (t, s, p) => Math.pow(2, -10 * t) * Math.sin((t - s) * TAU / p) + 1;\n/**\n * Easing functions adapted from Robert Penner's easing equations.\n * @namespace Chart.helpers.easing.effects\n * @see http://www.robertpenner.com/easing/\n */\nconst effects = {\n  linear: t => t,\n  easeInQuad: t => t * t,\n  easeOutQuad: t => -t * (t - 2),\n  easeInOutQuad: t => (t /= 0.5) < 1 ? 0.5 * t * t : -0.5 * (--t * (t - 2) - 1),\n  easeInCubic: t => t * t * t,\n  easeOutCubic: t => (t -= 1) * t * t + 1,\n  easeInOutCubic: t => (t /= 0.5) < 1 ? 0.5 * t * t * t : 0.5 * ((t -= 2) * t * t + 2),\n  easeInQuart: t => t * t * t * t,\n  easeOutQuart: t => -((t -= 1) * t * t * t - 1),\n  easeInOutQuart: t => (t /= 0.5) < 1 ? 0.5 * t * t * t * t : -0.5 * ((t -= 2) * t * t * t - 2),\n  easeInQuint: t => t * t * t * t * t,\n  easeOutQuint: t => (t -= 1) * t * t * t * t + 1,\n  easeInOutQuint: t => (t /= 0.5) < 1 ? 0.5 * t * t * t * t * t : 0.5 * ((t -= 2) * t * t * t * t + 2),\n  easeInSine: t => -Math.cos(t * HALF_PI) + 1,\n  easeOutSine: t => Math.sin(t * HALF_PI),\n  easeInOutSine: t => -0.5 * (Math.cos(PI * t) - 1),\n  easeInExpo: t => t === 0 ? 0 : Math.pow(2, 10 * (t - 1)),\n  easeOutExpo: t => t === 1 ? 1 : -Math.pow(2, -10 * t) + 1,\n  easeInOutExpo: t => atEdge(t) ? t : t < 0.5 ? 0.5 * Math.pow(2, 10 * (t * 2 - 1)) : 0.5 * (-Math.pow(2, -10 * (t * 2 - 1)) + 2),\n  easeInCirc: t => t >= 1 ? t : -(Math.sqrt(1 - t * t) - 1),\n  easeOutCirc: t => Math.sqrt(1 - (t -= 1) * t),\n  easeInOutCirc: t => (t /= 0.5) < 1 ? -0.5 * (Math.sqrt(1 - t * t) - 1) : 0.5 * (Math.sqrt(1 - (t -= 2) * t) + 1),\n  easeInElastic: t => atEdge(t) ? t : elasticIn(t, 0.075, 0.3),\n  easeOutElastic: t => atEdge(t) ? t : elasticOut(t, 0.075, 0.3),\n  easeInOutElastic(t) {\n    const s = 0.1125;\n    const p = 0.45;\n    return atEdge(t) ? t : t < 0.5 ? 0.5 * elasticIn(t * 2, s, p) : 0.5 + 0.5 * elasticOut(t * 2 - 1, s, p);\n  },\n  easeInBack(t) {\n    const s = 1.70158;\n    return t * t * ((s + 1) * t - s);\n  },\n  easeOutBack(t) {\n    const s = 1.70158;\n    return (t -= 1) * t * ((s + 1) * t + s) + 1;\n  },\n  easeInOutBack(t) {\n    let s = 1.70158;\n    if ((t /= 0.5) < 1) {\n      return 0.5 * (t * t * (((s *= 1.525) + 1) * t - s));\n    }\n    return 0.5 * ((t -= 2) * t * (((s *= 1.525) + 1) * t + s) + 2);\n  },\n  easeInBounce: t => 1 - effects.easeOutBounce(1 - t),\n  easeOutBounce(t) {\n    const m = 7.5625;\n    const d = 2.75;\n    if (t < 1 / d) {\n      return m * t * t;\n    }\n    if (t < 2 / d) {\n      return m * (t -= 1.5 / d) * t + 0.75;\n    }\n    if (t < 2.5 / d) {\n      return m * (t -= 2.25 / d) * t + 0.9375;\n    }\n    return m * (t -= 2.625 / d) * t + 0.984375;\n  },\n  easeInOutBounce: t => t < 0.5 ? effects.easeInBounce(t * 2) * 0.5 : effects.easeOutBounce(t * 2 - 1) * 0.5 + 0.5\n};\nfunction isPatternOrGradient(value) {\n  if (value && typeof value === 'object') {\n    const type = value.toString();\n    return type === '[object CanvasPattern]' || type === '[object CanvasGradient]';\n  }\n  return false;\n}\nfunction color(value) {\n  return isPatternOrGradient(value) ? value : new Color(value);\n}\nfunction getHoverColor(value) {\n  return isPatternOrGradient(value) ? value : new Color(value).saturate(0.5).darken(0.1).hexString();\n}\nconst numbers = ['x', 'y', 'borderWidth', 'radius', 'tension'];\nconst colors = ['color', 'borderColor', 'backgroundColor'];\nfunction applyAnimationsDefaults(defaults) {\n  defaults.set('animation', {\n    delay: undefined,\n    duration: 1000,\n    easing: 'easeOutQuart',\n    fn: undefined,\n    from: undefined,\n    loop: undefined,\n    to: undefined,\n    type: undefined\n  });\n  defaults.describe('animation', {\n    _fallback: false,\n    _indexable: false,\n    _scriptable: name => name !== 'onProgress' && name !== 'onComplete' && name !== 'fn'\n  });\n  defaults.set('animations', {\n    colors: {\n      type: 'color',\n      properties: colors\n    },\n    numbers: {\n      type: 'number',\n      properties: numbers\n    }\n  });\n  defaults.describe('animations', {\n    _fallback: 'animation'\n  });\n  defaults.set('transitions', {\n    active: {\n      animation: {\n        duration: 400\n      }\n    },\n    resize: {\n      animation: {\n        duration: 0\n      }\n    },\n    show: {\n      animations: {\n        colors: {\n          from: 'transparent'\n        },\n        visible: {\n          type: 'boolean',\n          duration: 0\n        }\n      }\n    },\n    hide: {\n      animations: {\n        colors: {\n          to: 'transparent'\n        },\n        visible: {\n          type: 'boolean',\n          easing: 'linear',\n          fn: v => v | 0\n        }\n      }\n    }\n  });\n}\nfunction applyLayoutsDefaults(defaults) {\n  defaults.set('layout', {\n    autoPadding: true,\n    padding: {\n      top: 0,\n      right: 0,\n      bottom: 0,\n      left: 0\n    }\n  });\n}\nconst intlCache = new Map();\nfunction getNumberFormat(locale, options) {\n  options = options || {};\n  const cacheKey = locale + JSON.stringify(options);\n  let formatter = intlCache.get(cacheKey);\n  if (!formatter) {\n    formatter = new Intl.NumberFormat(locale, options);\n    intlCache.set(cacheKey, formatter);\n  }\n  return formatter;\n}\nfunction formatNumber(num, locale, options) {\n  return getNumberFormat(locale, options).format(num);\n}\nconst formatters = {\n  values(value) {\n    return isArray(value) ? value : '' + value;\n  },\n  numeric(tickValue, index, ticks) {\n    if (tickValue === 0) {\n      return '0';\n    }\n    const locale = this.chart.options.locale;\n    let notation;\n    let delta = tickValue;\n    if (ticks.length > 1) {\n      const maxTick = Math.max(Math.abs(ticks[0].value), Math.abs(ticks[ticks.length - 1].value));\n      if (maxTick < 1e-4 || maxTick > 1e+15) {\n        notation = 'scientific';\n      }\n      delta = calculateDelta(tickValue, ticks);\n    }\n    const logDelta = log10(Math.abs(delta));\n    const numDecimal = isNaN(logDelta) ? 1 : Math.max(Math.min(-1 * Math.floor(logDelta), 20), 0);\n    const options = {\n      notation,\n      minimumFractionDigits: numDecimal,\n      maximumFractionDigits: numDecimal\n    };\n    Object.assign(options, this.options.ticks.format);\n    return formatNumber(tickValue, locale, options);\n  },\n  logarithmic(tickValue, index, ticks) {\n    if (tickValue === 0) {\n      return '0';\n    }\n    const remain = ticks[index].significand || tickValue / Math.pow(10, Math.floor(log10(tickValue)));\n    if ([1, 2, 3, 5, 10, 15].includes(remain) || index > 0.8 * ticks.length) {\n      return formatters.numeric.call(this, tickValue, index, ticks);\n    }\n    return '';\n  }\n};\nfunction calculateDelta(tickValue, ticks) {\n  let delta = ticks.length > 3 ? ticks[2].value - ticks[1].value : ticks[1].value - ticks[0].value;\n  if (Math.abs(delta) >= 1 && tickValue !== Math.floor(tickValue)) {\n    delta = tickValue - Math.floor(tickValue);\n  }\n  return delta;\n}\nvar Ticks = {\n  formatters\n};\nfunction applyScaleDefaults(defaults) {\n  defaults.set('scale', {\n    display: true,\n    offset: false,\n    reverse: false,\n    beginAtZero: false,\n    bounds: 'ticks',\n    grace: 0,\n    grid: {\n      display: true,\n      lineWidth: 1,\n      drawOnChartArea: true,\n      drawTicks: true,\n      tickLength: 8,\n      tickWidth: (_ctx, options) => options.lineWidth,\n      tickColor: (_ctx, options) => options.color,\n      offset: false\n    },\n    border: {\n      display: true,\n      dash: [],\n      dashOffset: 0.0,\n      width: 1\n    },\n    title: {\n      display: false,\n      text: '',\n      padding: {\n        top: 4,\n        bottom: 4\n      }\n    },\n    ticks: {\n      minRotation: 0,\n      maxRotation: 50,\n      mirror: false,\n      textStrokeWidth: 0,\n      textStrokeColor: '',\n      padding: 3,\n      display: true,\n      autoSkip: true,\n      autoSkipPadding: 3,\n      labelOffset: 0,\n      callback: Ticks.formatters.values,\n      minor: {},\n      major: {},\n      align: 'center',\n      crossAlign: 'near',\n      showLabelBackdrop: false,\n      backdropColor: 'rgba(255, 255, 255, 0.75)',\n      backdropPadding: 2\n    }\n  });\n  defaults.route('scale.ticks', 'color', '', 'color');\n  defaults.route('scale.grid', 'color', '', 'borderColor');\n  defaults.route('scale.border', 'color', '', 'borderColor');\n  defaults.route('scale.title', 'color', '', 'color');\n  defaults.describe('scale', {\n    _fallback: false,\n    _scriptable: name => !name.startsWith('before') && !name.startsWith('after') && name !== 'callback' && name !== 'parser',\n    _indexable: name => name !== 'borderDash' && name !== 'tickBorderDash' && name !== 'dash'\n  });\n  defaults.describe('scales', {\n    _fallback: 'scale'\n  });\n  defaults.describe('scale.ticks', {\n    _scriptable: name => name !== 'backdropPadding' && name !== 'callback',\n    _indexable: name => name !== 'backdropPadding'\n  });\n}\nconst overrides = Object.create(null);\nconst descriptors = Object.create(null);\nfunction getScope$1(node, key) {\n  if (!key) {\n    return node;\n  }\n  const keys = key.split('.');\n  for (let i = 0, n = keys.length; i < n; ++i) {\n    const k = keys[i];\n    node = node[k] || (node[k] = Object.create(null));\n  }\n  return node;\n}\nfunction set(root, scope, values) {\n  if (typeof scope === 'string') {\n    return merge(getScope$1(root, scope), values);\n  }\n  return merge(getScope$1(root, ''), scope);\n}\nclass Defaults {\n  constructor(_descriptors, _appliers) {\n    this.animation = undefined;\n    this.backgroundColor = 'rgba(0,0,0,0.1)';\n    this.borderColor = 'rgba(0,0,0,0.1)';\n    this.color = '#666';\n    this.datasets = {};\n    this.devicePixelRatio = context => context.chart.platform.getDevicePixelRatio();\n    this.elements = {};\n    this.events = ['mousemove', 'mouseout', 'click', 'touchstart', 'touchmove'];\n    this.font = {\n      family: \"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif\",\n      size: 12,\n      style: 'normal',\n      lineHeight: 1.2,\n      weight: null\n    };\n    this.hover = {};\n    this.hoverBackgroundColor = (ctx, options) => getHoverColor(options.backgroundColor);\n    this.hoverBorderColor = (ctx, options) => getHoverColor(options.borderColor);\n    this.hoverColor = (ctx, options) => getHoverColor(options.color);\n    this.indexAxis = 'x';\n    this.interaction = {\n      mode: 'nearest',\n      intersect: true,\n      includeInvisible: false\n    };\n    this.maintainAspectRatio = true;\n    this.onHover = null;\n    this.onClick = null;\n    this.parsing = true;\n    this.plugins = {};\n    this.responsive = true;\n    this.scale = undefined;\n    this.scales = {};\n    this.showLine = true;\n    this.drawActiveElementsOnTop = true;\n    this.describe(_descriptors);\n    this.apply(_appliers);\n  }\n  set(scope, values) {\n    return set(this, scope, values);\n  }\n  get(scope) {\n    return getScope$1(this, scope);\n  }\n  describe(scope, values) {\n    return set(descriptors, scope, values);\n  }\n  override(scope, values) {\n    return set(overrides, scope, values);\n  }\n  route(scope, name, targetScope, targetName) {\n    const scopeObject = getScope$1(this, scope);\n    const targetScopeObject = getScope$1(this, targetScope);\n    const privateName = '_' + name;\n    Object.defineProperties(scopeObject, {\n      [privateName]: {\n        value: scopeObject[name],\n        writable: true\n      },\n      [name]: {\n        enumerable: true,\n        get() {\n          const local = this[privateName];\n          const target = targetScopeObject[targetName];\n          if (isObject(local)) {\n            return Object.assign({}, target, local);\n          }\n          return valueOrDefault(local, target);\n        },\n        set(value) {\n          this[privateName] = value;\n        }\n      }\n    });\n  }\n  apply(appliers) {\n    appliers.forEach(apply => apply(this));\n  }\n}\nvar defaults = /* #__PURE__ */new Defaults({\n  _scriptable: name => !name.startsWith('on'),\n  _indexable: name => name !== 'events',\n  hover: {\n    _fallback: 'interaction'\n  },\n  interaction: {\n    _scriptable: false,\n    _indexable: false\n  }\n}, [applyAnimationsDefaults, applyLayoutsDefaults, applyScaleDefaults]);\n\n/**\n * Converts the given font object into a CSS font string.\n * @param font - A font object.\n * @return The CSS font string. See https://developer.mozilla.org/en-US/docs/Web/CSS/font\n * @private\n */\nfunction toFontString(font) {\n  if (!font || isNullOrUndef(font.size) || isNullOrUndef(font.family)) {\n    return null;\n  }\n  return (font.style ? font.style + ' ' : '') + (font.weight ? font.weight + ' ' : '') + font.size + 'px ' + font.family;\n}\n/**\n * @private\n */\nfunction _measureText(ctx, data, gc, longest, string) {\n  let textWidth = data[string];\n  if (!textWidth) {\n    textWidth = data[string] = ctx.measureText(string).width;\n    gc.push(string);\n  }\n  if (textWidth > longest) {\n    longest = textWidth;\n  }\n  return longest;\n}\n/**\n * @private\n */ // eslint-disable-next-line complexity\nfunction _longestText(ctx, font, arrayOfThings, cache) {\n  cache = cache || {};\n  let data = cache.data = cache.data || {};\n  let gc = cache.garbageCollect = cache.garbageCollect || [];\n  if (cache.font !== font) {\n    data = cache.data = {};\n    gc = cache.garbageCollect = [];\n    cache.font = font;\n  }\n  ctx.save();\n  ctx.font = font;\n  let longest = 0;\n  const ilen = arrayOfThings.length;\n  let i, j, jlen, thing, nestedThing;\n  for (i = 0; i < ilen; i++) {\n    thing = arrayOfThings[i];\n    // Undefined strings and arrays should not be measured\n    if (thing !== undefined && thing !== null && !isArray(thing)) {\n      longest = _measureText(ctx, data, gc, longest, thing);\n    } else if (isArray(thing)) {\n      // if it is an array lets measure each element\n      // to do maybe simplify this function a bit so we can do this more recursively?\n      for (j = 0, jlen = thing.length; j < jlen; j++) {\n        nestedThing = thing[j];\n        // Undefined strings and arrays should not be measured\n        if (nestedThing !== undefined && nestedThing !== null && !isArray(nestedThing)) {\n          longest = _measureText(ctx, data, gc, longest, nestedThing);\n        }\n      }\n    }\n  }\n  ctx.restore();\n  const gcLen = gc.length / 2;\n  if (gcLen > arrayOfThings.length) {\n    for (i = 0; i < gcLen; i++) {\n      delete data[gc[i]];\n    }\n    gc.splice(0, gcLen);\n  }\n  return longest;\n}\n/**\n * Returns the aligned pixel value to avoid anti-aliasing blur\n * @param chart - The chart instance.\n * @param pixel - A pixel value.\n * @param width - The width of the element.\n * @returns The aligned pixel value.\n * @private\n */\nfunction _alignPixel(chart, pixel, width) {\n  const devicePixelRatio = chart.currentDevicePixelRatio;\n  const halfWidth = width !== 0 ? Math.max(width / 2, 0.5) : 0;\n  return Math.round((pixel - halfWidth) * devicePixelRatio) / devicePixelRatio + halfWidth;\n}\n/**\n * Clears the entire canvas.\n */\nfunction clearCanvas(canvas, ctx) {\n  ctx = ctx || canvas.getContext('2d');\n  ctx.save();\n  // canvas.width and canvas.height do not consider the canvas transform,\n  // while clearRect does\n  ctx.resetTransform();\n  ctx.clearRect(0, 0, canvas.width, canvas.height);\n  ctx.restore();\n}\nfunction drawPoint(ctx, options, x, y) {\n  // eslint-disable-next-line @typescript-eslint/no-use-before-define\n  drawPointLegend(ctx, options, x, y, null);\n}\n// eslint-disable-next-line complexity\nfunction drawPointLegend(ctx, options, x, y, w) {\n  let type, xOffset, yOffset, size, cornerRadius, width, xOffsetW, yOffsetW;\n  const style = options.pointStyle;\n  const rotation = options.rotation;\n  const radius = options.radius;\n  let rad = (rotation || 0) * RAD_PER_DEG;\n  if (style && typeof style === 'object') {\n    type = style.toString();\n    if (type === '[object HTMLImageElement]' || type === '[object HTMLCanvasElement]') {\n      ctx.save();\n      ctx.translate(x, y);\n      ctx.rotate(rad);\n      ctx.drawImage(style, -style.width / 2, -style.height / 2, style.width, style.height);\n      ctx.restore();\n      return;\n    }\n  }\n  if (isNaN(radius) || radius <= 0) {\n    return;\n  }\n  ctx.beginPath();\n  switch (style) {\n    // Default includes circle\n    default:\n      if (w) {\n        ctx.ellipse(x, y, w / 2, radius, 0, 0, TAU);\n      } else {\n        ctx.arc(x, y, radius, 0, TAU);\n      }\n      ctx.closePath();\n      break;\n    case 'triangle':\n      width = w ? w / 2 : radius;\n      ctx.moveTo(x + Math.sin(rad) * width, y - Math.cos(rad) * radius);\n      rad += TWO_THIRDS_PI;\n      ctx.lineTo(x + Math.sin(rad) * width, y - Math.cos(rad) * radius);\n      rad += TWO_THIRDS_PI;\n      ctx.lineTo(x + Math.sin(rad) * width, y - Math.cos(rad) * radius);\n      ctx.closePath();\n      break;\n    case 'rectRounded':\n      // NOTE: the rounded rect implementation changed to use `arc` instead of\n      // `quadraticCurveTo` since it generates better results when rect is\n      // almost a circle. 0.516 (instead of 0.5) produces results with visually\n      // closer proportion to the previous impl and it is inscribed in the\n      // circle with `radius`. For more details, see the following PRs:\n      // https://github.com/chartjs/Chart.js/issues/5597\n      // https://github.com/chartjs/Chart.js/issues/5858\n      cornerRadius = radius * 0.516;\n      size = radius - cornerRadius;\n      xOffset = Math.cos(rad + QUARTER_PI) * size;\n      xOffsetW = Math.cos(rad + QUARTER_PI) * (w ? w / 2 - cornerRadius : size);\n      yOffset = Math.sin(rad + QUARTER_PI) * size;\n      yOffsetW = Math.sin(rad + QUARTER_PI) * (w ? w / 2 - cornerRadius : size);\n      ctx.arc(x - xOffsetW, y - yOffset, cornerRadius, rad - PI, rad - HALF_PI);\n      ctx.arc(x + yOffsetW, y - xOffset, cornerRadius, rad - HALF_PI, rad);\n      ctx.arc(x + xOffsetW, y + yOffset, cornerRadius, rad, rad + HALF_PI);\n      ctx.arc(x - yOffsetW, y + xOffset, cornerRadius, rad + HALF_PI, rad + PI);\n      ctx.closePath();\n      break;\n    case 'rect':\n      if (!rotation) {\n        size = Math.SQRT1_2 * radius;\n        width = w ? w / 2 : size;\n        ctx.rect(x - width, y - size, 2 * width, 2 * size);\n        break;\n      }\n      rad += QUARTER_PI;\n    /* falls through */\n    case 'rectRot':\n      xOffsetW = Math.cos(rad) * (w ? w / 2 : radius);\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      yOffsetW = Math.sin(rad) * (w ? w / 2 : radius);\n      ctx.moveTo(x - xOffsetW, y - yOffset);\n      ctx.lineTo(x + yOffsetW, y - xOffset);\n      ctx.lineTo(x + xOffsetW, y + yOffset);\n      ctx.lineTo(x - yOffsetW, y + xOffset);\n      ctx.closePath();\n      break;\n    case 'crossRot':\n      rad += QUARTER_PI;\n    /* falls through */\n    case 'cross':\n      xOffsetW = Math.cos(rad) * (w ? w / 2 : radius);\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      yOffsetW = Math.sin(rad) * (w ? w / 2 : radius);\n      ctx.moveTo(x - xOffsetW, y - yOffset);\n      ctx.lineTo(x + xOffsetW, y + yOffset);\n      ctx.moveTo(x + yOffsetW, y - xOffset);\n      ctx.lineTo(x - yOffsetW, y + xOffset);\n      break;\n    case 'star':\n      xOffsetW = Math.cos(rad) * (w ? w / 2 : radius);\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      yOffsetW = Math.sin(rad) * (w ? w / 2 : radius);\n      ctx.moveTo(x - xOffsetW, y - yOffset);\n      ctx.lineTo(x + xOffsetW, y + yOffset);\n      ctx.moveTo(x + yOffsetW, y - xOffset);\n      ctx.lineTo(x - yOffsetW, y + xOffset);\n      rad += QUARTER_PI;\n      xOffsetW = Math.cos(rad) * (w ? w / 2 : radius);\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      yOffsetW = Math.sin(rad) * (w ? w / 2 : radius);\n      ctx.moveTo(x - xOffsetW, y - yOffset);\n      ctx.lineTo(x + xOffsetW, y + yOffset);\n      ctx.moveTo(x + yOffsetW, y - xOffset);\n      ctx.lineTo(x - yOffsetW, y + xOffset);\n      break;\n    case 'line':\n      xOffset = w ? w / 2 : Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      ctx.moveTo(x - xOffset, y - yOffset);\n      ctx.lineTo(x + xOffset, y + yOffset);\n      break;\n    case 'dash':\n      ctx.moveTo(x, y);\n      ctx.lineTo(x + Math.cos(rad) * (w ? w / 2 : radius), y + Math.sin(rad) * radius);\n      break;\n    case false:\n      ctx.closePath();\n      break;\n  }\n  ctx.fill();\n  if (options.borderWidth > 0) {\n    ctx.stroke();\n  }\n}\n/**\n * Returns true if the point is inside the rectangle\n * @param point - The point to test\n * @param area - The rectangle\n * @param margin - allowed margin\n * @private\n */\nfunction _isPointInArea(point, area, margin) {\n  margin = margin || 0.5; // margin - default is to match rounded decimals\n  return !area || point && point.x > area.left - margin && point.x < area.right + margin && point.y > area.top - margin && point.y < area.bottom + margin;\n}\nfunction clipArea(ctx, area) {\n  ctx.save();\n  ctx.beginPath();\n  ctx.rect(area.left, area.top, area.right - area.left, area.bottom - area.top);\n  ctx.clip();\n}\nfunction unclipArea(ctx) {\n  ctx.restore();\n}\n/**\n * @private\n */\nfunction _steppedLineTo(ctx, previous, target, flip, mode) {\n  if (!previous) {\n    return ctx.lineTo(target.x, target.y);\n  }\n  if (mode === 'middle') {\n    const midpoint = (previous.x + target.x) / 2.0;\n    ctx.lineTo(midpoint, previous.y);\n    ctx.lineTo(midpoint, target.y);\n  } else if (mode === 'after' !== !!flip) {\n    ctx.lineTo(previous.x, target.y);\n  } else {\n    ctx.lineTo(target.x, previous.y);\n  }\n  ctx.lineTo(target.x, target.y);\n}\n/**\n * @private\n */\nfunction _bezierCurveTo(ctx, previous, target, flip) {\n  if (!previous) {\n    return ctx.lineTo(target.x, target.y);\n  }\n  ctx.bezierCurveTo(flip ? previous.cp1x : previous.cp2x, flip ? previous.cp1y : previous.cp2y, flip ? target.cp2x : target.cp1x, flip ? target.cp2y : target.cp1y, target.x, target.y);\n}\nfunction setRenderOpts(ctx, opts) {\n  if (opts.translation) {\n    ctx.translate(opts.translation[0], opts.translation[1]);\n  }\n  if (!isNullOrUndef(opts.rotation)) {\n    ctx.rotate(opts.rotation);\n  }\n  if (opts.color) {\n    ctx.fillStyle = opts.color;\n  }\n  if (opts.textAlign) {\n    ctx.textAlign = opts.textAlign;\n  }\n  if (opts.textBaseline) {\n    ctx.textBaseline = opts.textBaseline;\n  }\n}\nfunction decorateText(ctx, x, y, line, opts) {\n  if (opts.strikethrough || opts.underline) {\n    /**\n    * Now that IE11 support has been dropped, we can use more\n    * of the TextMetrics object. The actual bounding boxes\n    * are unflagged in Chrome, Firefox, Edge, and Safari so they\n    * can be safely used.\n    * See https://developer.mozilla.org/en-US/docs/Web/API/TextMetrics#Browser_compatibility\n    */\n    const metrics = ctx.measureText(line);\n    const left = x - metrics.actualBoundingBoxLeft;\n    const right = x + metrics.actualBoundingBoxRight;\n    const top = y - metrics.actualBoundingBoxAscent;\n    const bottom = y + metrics.actualBoundingBoxDescent;\n    const yDecoration = opts.strikethrough ? (top + bottom) / 2 : bottom;\n    ctx.strokeStyle = ctx.fillStyle;\n    ctx.beginPath();\n    ctx.lineWidth = opts.decorationWidth || 2;\n    ctx.moveTo(left, yDecoration);\n    ctx.lineTo(right, yDecoration);\n    ctx.stroke();\n  }\n}\nfunction drawBackdrop(ctx, opts) {\n  const oldColor = ctx.fillStyle;\n  ctx.fillStyle = opts.color;\n  ctx.fillRect(opts.left, opts.top, opts.width, opts.height);\n  ctx.fillStyle = oldColor;\n}\n/**\n * Render text onto the canvas\n */\nfunction renderText(ctx, text, x, y, font) {\n  let opts = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : {};\n  const lines = isArray(text) ? text : [text];\n  const stroke = opts.strokeWidth > 0 && opts.strokeColor !== '';\n  let i, line;\n  ctx.save();\n  ctx.font = font.string;\n  setRenderOpts(ctx, opts);\n  for (i = 0; i < lines.length; ++i) {\n    line = lines[i];\n    if (opts.backdrop) {\n      drawBackdrop(ctx, opts.backdrop);\n    }\n    if (stroke) {\n      if (opts.strokeColor) {\n        ctx.strokeStyle = opts.strokeColor;\n      }\n      if (!isNullOrUndef(opts.strokeWidth)) {\n        ctx.lineWidth = opts.strokeWidth;\n      }\n      ctx.strokeText(line, x, y, opts.maxWidth);\n    }\n    ctx.fillText(line, x, y, opts.maxWidth);\n    decorateText(ctx, x, y, line, opts);\n    y += Number(font.lineHeight);\n  }\n  ctx.restore();\n}\n/**\n * Add a path of a rectangle with rounded corners to the current sub-path\n * @param ctx - Context\n * @param rect - Bounding rect\n */\nfunction addRoundedRectPath(ctx, rect) {\n  const {\n    x,\n    y,\n    w,\n    h,\n    radius\n  } = rect;\n  // top left arc\n  ctx.arc(x + radius.topLeft, y + radius.topLeft, radius.topLeft, -HALF_PI, PI, true);\n  // line from top left to bottom left\n  ctx.lineTo(x, y + h - radius.bottomLeft);\n  // bottom left arc\n  ctx.arc(x + radius.bottomLeft, y + h - radius.bottomLeft, radius.bottomLeft, PI, HALF_PI, true);\n  // line from bottom left to bottom right\n  ctx.lineTo(x + w - radius.bottomRight, y + h);\n  // bottom right arc\n  ctx.arc(x + w - radius.bottomRight, y + h - radius.bottomRight, radius.bottomRight, HALF_PI, 0, true);\n  // line from bottom right to top right\n  ctx.lineTo(x + w, y + radius.topRight);\n  // top right arc\n  ctx.arc(x + w - radius.topRight, y + radius.topRight, radius.topRight, 0, -HALF_PI, true);\n  // line from top right to top left\n  ctx.lineTo(x + radius.topLeft, y);\n}\nconst LINE_HEIGHT = /^(normal|(\\d+(?:\\.\\d+)?)(px|em|%)?)$/;\nconst FONT_STYLE = /^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/;\n/**\n * @alias Chart.helpers.options\n * @namespace\n */ /**\n    * Converts the given line height `value` in pixels for a specific font `size`.\n    * @param value - The lineHeight to parse (eg. 1.6, '14px', '75%', '1.6em').\n    * @param size - The font size (in pixels) used to resolve relative `value`.\n    * @returns The effective line height in pixels (size * 1.2 if value is invalid).\n    * @see https://developer.mozilla.org/en-US/docs/Web/CSS/line-height\n    * @since 2.7.0\n    */\nfunction toLineHeight(value, size) {\n  const matches = ('' + value).match(LINE_HEIGHT);\n  if (!matches || matches[1] === 'normal') {\n    return size * 1.2;\n  }\n  value = +matches[2];\n  switch (matches[3]) {\n    case 'px':\n      return value;\n    case '%':\n      value /= 100;\n      break;\n  }\n  return size * value;\n}\nconst numberOrZero = v => +v || 0;\nfunction _readValueToProps(value, props) {\n  const ret = {};\n  const objProps = isObject(props);\n  const keys = objProps ? Object.keys(props) : props;\n  const read = isObject(value) ? objProps ? prop => valueOrDefault(value[prop], value[props[prop]]) : prop => value[prop] : () => value;\n  for (const prop of keys) {\n    ret[prop] = numberOrZero(read(prop));\n  }\n  return ret;\n}\n/**\n * Converts the given value into a TRBL object.\n * @param value - If a number, set the value to all TRBL component,\n *  else, if an object, use defined properties and sets undefined ones to 0.\n *  x / y are shorthands for same value for left/right and top/bottom.\n * @returns The padding values (top, right, bottom, left)\n * @since 3.0.0\n */\nfunction toTRBL(value) {\n  return _readValueToProps(value, {\n    top: 'y',\n    right: 'x',\n    bottom: 'y',\n    left: 'x'\n  });\n}\n/**\n * Converts the given value into a TRBL corners object (similar with css border-radius).\n * @param value - If a number, set the value to all TRBL corner components,\n *  else, if an object, use defined properties and sets undefined ones to 0.\n * @returns The TRBL corner values (topLeft, topRight, bottomLeft, bottomRight)\n * @since 3.0.0\n */\nfunction toTRBLCorners(value) {\n  return _readValueToProps(value, ['topLeft', 'topRight', 'bottomLeft', 'bottomRight']);\n}\n/**\n * Converts the given value into a padding object with pre-computed width/height.\n * @param value - If a number, set the value to all TRBL component,\n *  else, if an object, use defined properties and sets undefined ones to 0.\n *  x / y are shorthands for same value for left/right and top/bottom.\n * @returns The padding values (top, right, bottom, left, width, height)\n * @since 2.7.0\n */\nfunction toPadding(value) {\n  const obj = toTRBL(value);\n  obj.width = obj.left + obj.right;\n  obj.height = obj.top + obj.bottom;\n  return obj;\n}\n/**\n * Parses font options and returns the font object.\n * @param options - A object that contains font options to be parsed.\n * @param fallback - A object that contains fallback font options.\n * @return The font object.\n * @private\n */\nfunction toFont(options, fallback) {\n  options = options || {};\n  fallback = fallback || defaults.font;\n  let size = valueOrDefault(options.size, fallback.size);\n  if (typeof size === 'string') {\n    size = parseInt(size, 10);\n  }\n  let style = valueOrDefault(options.style, fallback.style);\n  if (style && !('' + style).match(FONT_STYLE)) {\n    console.warn('Invalid font style specified: \"' + style + '\"');\n    style = undefined;\n  }\n  const font = {\n    family: valueOrDefault(options.family, fallback.family),\n    lineHeight: toLineHeight(valueOrDefault(options.lineHeight, fallback.lineHeight), size),\n    size,\n    style,\n    weight: valueOrDefault(options.weight, fallback.weight),\n    string: ''\n  };\n  font.string = toFontString(font);\n  return font;\n}\n/**\n * Evaluates the given `inputs` sequentially and returns the first defined value.\n * @param inputs - An array of values, falling back to the last value.\n * @param context - If defined and the current value is a function, the value\n * is called with `context` as first argument and the result becomes the new input.\n * @param index - If defined and the current value is an array, the value\n * at `index` become the new input.\n * @param info - object to return information about resolution in\n * @param info.cacheable - Will be set to `false` if option is not cacheable.\n * @since 2.7.0\n */\nfunction resolve(inputs, context, index, info) {\n  let cacheable = true;\n  let i, ilen, value;\n  for (i = 0, ilen = inputs.length; i < ilen; ++i) {\n    value = inputs[i];\n    if (value === undefined) {\n      continue;\n    }\n    if (context !== undefined && typeof value === 'function') {\n      value = value(context);\n      cacheable = false;\n    }\n    if (index !== undefined && isArray(value)) {\n      value = value[index % value.length];\n      cacheable = false;\n    }\n    if (value !== undefined) {\n      if (info && !cacheable) {\n        info.cacheable = false;\n      }\n      return value;\n    }\n  }\n}\n/**\n * @param minmax\n * @param grace\n * @param beginAtZero\n * @private\n */\nfunction _addGrace(minmax, grace, beginAtZero) {\n  const {\n    min,\n    max\n  } = minmax;\n  const change = toDimension(grace, (max - min) / 2);\n  const keepZero = (value, add) => beginAtZero && value === 0 ? 0 : value + add;\n  return {\n    min: keepZero(min, -Math.abs(change)),\n    max: keepZero(max, change)\n  };\n}\nfunction createContext(parentContext, context) {\n  return Object.assign(Object.create(parentContext), context);\n}\n\n/**\n * Creates a Proxy for resolving raw values for options.\n * @param scopes - The option scopes to look for values, in resolution order\n * @param prefixes - The prefixes for values, in resolution order.\n * @param rootScopes - The root option scopes\n * @param fallback - Parent scopes fallback\n * @param getTarget - callback for getting the target for changed values\n * @returns Proxy\n * @private\n */\nfunction _createResolver(scopes) {\n  let prefixes = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [''];\n  let rootScopes = arguments.length > 2 ? arguments[2] : undefined;\n  let fallback = arguments.length > 3 ? arguments[3] : undefined;\n  let getTarget = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : () => scopes[0];\n  const finalRootScopes = rootScopes || scopes;\n  if (typeof fallback === 'undefined') {\n    fallback = _resolve('_fallback', scopes);\n  }\n  const cache = {\n    [Symbol.toStringTag]: 'Object',\n    _cacheable: true,\n    _scopes: scopes,\n    _rootScopes: finalRootScopes,\n    _fallback: fallback,\n    _getTarget: getTarget,\n    override: scope => _createResolver([scope, ...scopes], prefixes, finalRootScopes, fallback)\n  };\n  return new Proxy(cache, {\n    /**\n    * A trap for the delete operator.\n    */\n    deleteProperty(target, prop) {\n      delete target[prop]; // remove from cache\n      delete target._keys; // remove cached keys\n      delete scopes[0][prop]; // remove from top level scope\n      return true;\n    },\n    /**\n    * A trap for getting property values.\n    */\n    get(target, prop) {\n      return _cached(target, prop, () => _resolveWithPrefixes(prop, prefixes, scopes, target));\n    },\n    /**\n    * A trap for Object.getOwnPropertyDescriptor.\n    * Also used by Object.hasOwnProperty.\n    */\n    getOwnPropertyDescriptor(target, prop) {\n      return Reflect.getOwnPropertyDescriptor(target._scopes[0], prop);\n    },\n    /**\n    * A trap for Object.getPrototypeOf.\n    */\n    getPrototypeOf() {\n      return Reflect.getPrototypeOf(scopes[0]);\n    },\n    /**\n    * A trap for the in operator.\n    */\n    has(target, prop) {\n      return getKeysFromAllScopes(target).includes(prop);\n    },\n    /**\n    * A trap for Object.getOwnPropertyNames and Object.getOwnPropertySymbols.\n    */\n    ownKeys(target) {\n      return getKeysFromAllScopes(target);\n    },\n    /**\n    * A trap for setting property values.\n    */\n    set(target, prop, value) {\n      const storage = target._storage || (target._storage = getTarget());\n      target[prop] = storage[prop] = value; // set to top level scope + cache\n      delete target._keys; // remove cached keys\n      return true;\n    }\n  });\n}\n/**\n * Returns an Proxy for resolving option values with context.\n * @param proxy - The Proxy returned by `_createResolver`\n * @param context - Context object for scriptable/indexable options\n * @param subProxy - The proxy provided for scriptable options\n * @param descriptorDefaults - Defaults for descriptors\n * @private\n */\nfunction _attachContext(proxy, context, subProxy, descriptorDefaults) {\n  const cache = {\n    _cacheable: false,\n    _proxy: proxy,\n    _context: context,\n    _subProxy: subProxy,\n    _stack: new Set(),\n    _descriptors: _descriptors(proxy, descriptorDefaults),\n    setContext: ctx => _attachContext(proxy, ctx, subProxy, descriptorDefaults),\n    override: scope => _attachContext(proxy.override(scope), context, subProxy, descriptorDefaults)\n  };\n  return new Proxy(cache, {\n    /**\n    * A trap for the delete operator.\n    */\n    deleteProperty(target, prop) {\n      delete target[prop]; // remove from cache\n      delete proxy[prop]; // remove from proxy\n      return true;\n    },\n    /**\n    * A trap for getting property values.\n    */\n    get(target, prop, receiver) {\n      return _cached(target, prop, () => _resolveWithContext(target, prop, receiver));\n    },\n    /**\n    * A trap for Object.getOwnPropertyDescriptor.\n    * Also used by Object.hasOwnProperty.\n    */\n    getOwnPropertyDescriptor(target, prop) {\n      return target._descriptors.allKeys ? Reflect.has(proxy, prop) ? {\n        enumerable: true,\n        configurable: true\n      } : undefined : Reflect.getOwnPropertyDescriptor(proxy, prop);\n    },\n    /**\n    * A trap for Object.getPrototypeOf.\n    */\n    getPrototypeOf() {\n      return Reflect.getPrototypeOf(proxy);\n    },\n    /**\n    * A trap for the in operator.\n    */\n    has(target, prop) {\n      return Reflect.has(proxy, prop);\n    },\n    /**\n    * A trap for Object.getOwnPropertyNames and Object.getOwnPropertySymbols.\n    */\n    ownKeys() {\n      return Reflect.ownKeys(proxy);\n    },\n    /**\n    * A trap for setting property values.\n    */\n    set(target, prop, value) {\n      proxy[prop] = value; // set to proxy\n      delete target[prop]; // remove from cache\n      return true;\n    }\n  });\n}\n/**\n * @private\n */\nfunction _descriptors(proxy) {\n  let defaults = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n    scriptable: true,\n    indexable: true\n  };\n  const {\n    _scriptable = defaults.scriptable,\n    _indexable = defaults.indexable,\n    _allKeys = defaults.allKeys\n  } = proxy;\n  return {\n    allKeys: _allKeys,\n    scriptable: _scriptable,\n    indexable: _indexable,\n    isScriptable: isFunction(_scriptable) ? _scriptable : () => _scriptable,\n    isIndexable: isFunction(_indexable) ? _indexable : () => _indexable\n  };\n}\nconst readKey = (prefix, name) => prefix ? prefix + _capitalize(name) : name;\nconst needsSubResolver = (prop, value) => isObject(value) && prop !== 'adapters' && (Object.getPrototypeOf(value) === null || value.constructor === Object);\nfunction _cached(target, prop, resolve) {\n  if (Object.prototype.hasOwnProperty.call(target, prop)) {\n    return target[prop];\n  }\n  const value = resolve();\n  // cache the resolved value\n  target[prop] = value;\n  return value;\n}\nfunction _resolveWithContext(target, prop, receiver) {\n  const {\n    _proxy,\n    _context,\n    _subProxy,\n    _descriptors: descriptors\n  } = target;\n  let value = _proxy[prop]; // resolve from proxy\n  // resolve with context\n  if (isFunction(value) && descriptors.isScriptable(prop)) {\n    value = _resolveScriptable(prop, value, target, receiver);\n  }\n  if (isArray(value) && value.length) {\n    value = _resolveArray(prop, value, target, descriptors.isIndexable);\n  }\n  if (needsSubResolver(prop, value)) {\n    // if the resolved value is an object, create a sub resolver for it\n    value = _attachContext(value, _context, _subProxy && _subProxy[prop], descriptors);\n  }\n  return value;\n}\nfunction _resolveScriptable(prop, getValue, target, receiver) {\n  const {\n    _proxy,\n    _context,\n    _subProxy,\n    _stack\n  } = target;\n  if (_stack.has(prop)) {\n    throw new Error('Recursion detected: ' + Array.from(_stack).join('->') + '->' + prop);\n  }\n  _stack.add(prop);\n  let value = getValue(_context, _subProxy || receiver);\n  _stack.delete(prop);\n  if (needsSubResolver(prop, value)) {\n    // When scriptable option returns an object, create a resolver on that.\n    value = createSubResolver(_proxy._scopes, _proxy, prop, value);\n  }\n  return value;\n}\nfunction _resolveArray(prop, value, target, isIndexable) {\n  const {\n    _proxy,\n    _context,\n    _subProxy,\n    _descriptors: descriptors\n  } = target;\n  if (typeof _context.index !== 'undefined' && isIndexable(prop)) {\n    return value[_context.index % value.length];\n  } else if (isObject(value[0])) {\n    // Array of objects, return array or resolvers\n    const arr = value;\n    const scopes = _proxy._scopes.filter(s => s !== arr);\n    value = [];\n    for (const item of arr) {\n      const resolver = createSubResolver(scopes, _proxy, prop, item);\n      value.push(_attachContext(resolver, _context, _subProxy && _subProxy[prop], descriptors));\n    }\n  }\n  return value;\n}\nfunction resolveFallback(fallback, prop, value) {\n  return isFunction(fallback) ? fallback(prop, value) : fallback;\n}\nconst getScope = (key, parent) => key === true ? parent : typeof key === 'string' ? resolveObjectKey(parent, key) : undefined;\nfunction addScopes(set, parentScopes, key, parentFallback, value) {\n  for (const parent of parentScopes) {\n    const scope = getScope(key, parent);\n    if (scope) {\n      set.add(scope);\n      const fallback = resolveFallback(scope._fallback, key, value);\n      if (typeof fallback !== 'undefined' && fallback !== key && fallback !== parentFallback) {\n        // When we reach the descriptor that defines a new _fallback, return that.\n        // The fallback will resume to that new scope.\n        return fallback;\n      }\n    } else if (scope === false && typeof parentFallback !== 'undefined' && key !== parentFallback) {\n      // Fallback to `false` results to `false`, when falling back to different key.\n      // For example `interaction` from `hover` or `plugins.tooltip` and `animation` from `animations`\n      return null;\n    }\n  }\n  return false;\n}\nfunction createSubResolver(parentScopes, resolver, prop, value) {\n  const rootScopes = resolver._rootScopes;\n  const fallback = resolveFallback(resolver._fallback, prop, value);\n  const allScopes = [...parentScopes, ...rootScopes];\n  const set = new Set();\n  set.add(value);\n  let key = addScopesFromKey(set, allScopes, prop, fallback || prop, value);\n  if (key === null) {\n    return false;\n  }\n  if (typeof fallback !== 'undefined' && fallback !== prop) {\n    key = addScopesFromKey(set, allScopes, fallback, key, value);\n    if (key === null) {\n      return false;\n    }\n  }\n  return _createResolver(Array.from(set), [''], rootScopes, fallback, () => subGetTarget(resolver, prop, value));\n}\nfunction addScopesFromKey(set, allScopes, key, fallback, item) {\n  while (key) {\n    key = addScopes(set, allScopes, key, fallback, item);\n  }\n  return key;\n}\nfunction subGetTarget(resolver, prop, value) {\n  const parent = resolver._getTarget();\n  if (!(prop in parent)) {\n    parent[prop] = {};\n  }\n  const target = parent[prop];\n  if (isArray(target) && isObject(value)) {\n    // For array of objects, the object is used to store updated values\n    return value;\n  }\n  return target || {};\n}\nfunction _resolveWithPrefixes(prop, prefixes, scopes, proxy) {\n  let value;\n  for (const prefix of prefixes) {\n    value = _resolve(readKey(prefix, prop), scopes);\n    if (typeof value !== 'undefined') {\n      return needsSubResolver(prop, value) ? createSubResolver(scopes, proxy, prop, value) : value;\n    }\n  }\n}\nfunction _resolve(key, scopes) {\n  for (const scope of scopes) {\n    if (!scope) {\n      continue;\n    }\n    const value = scope[key];\n    if (typeof value !== 'undefined') {\n      return value;\n    }\n  }\n}\nfunction getKeysFromAllScopes(target) {\n  let keys = target._keys;\n  if (!keys) {\n    keys = target._keys = resolveKeysFromAllScopes(target._scopes);\n  }\n  return keys;\n}\nfunction resolveKeysFromAllScopes(scopes) {\n  const set = new Set();\n  for (const scope of scopes) {\n    for (const key of Object.keys(scope).filter(k => !k.startsWith('_'))) {\n      set.add(key);\n    }\n  }\n  return Array.from(set);\n}\nfunction _parseObjectDataRadialScale(meta, data, start, count) {\n  const {\n    iScale\n  } = meta;\n  const {\n    key = 'r'\n  } = this._parsing;\n  const parsed = new Array(count);\n  let i, ilen, index, item;\n  for (i = 0, ilen = count; i < ilen; ++i) {\n    index = i + start;\n    item = data[index];\n    parsed[i] = {\n      r: iScale.parse(resolveObjectKey(item, key), index)\n    };\n  }\n  return parsed;\n}\nconst EPSILON = Number.EPSILON || 1e-14;\nconst getPoint = (points, i) => i < points.length && !points[i].skip && points[i];\nconst getValueAxis = indexAxis => indexAxis === 'x' ? 'y' : 'x';\nfunction splineCurve(firstPoint, middlePoint, afterPoint, t) {\n  // Props to Rob Spencer at scaled innovation for his post on splining between points\n  // http://scaledinnovation.com/analytics/splines/aboutSplines.html\n  // This function must also respect \"skipped\" points\n  const previous = firstPoint.skip ? middlePoint : firstPoint;\n  const current = middlePoint;\n  const next = afterPoint.skip ? middlePoint : afterPoint;\n  const d01 = distanceBetweenPoints(current, previous);\n  const d12 = distanceBetweenPoints(next, current);\n  let s01 = d01 / (d01 + d12);\n  let s12 = d12 / (d01 + d12);\n  // If all points are the same, s01 & s02 will be inf\n  s01 = isNaN(s01) ? 0 : s01;\n  s12 = isNaN(s12) ? 0 : s12;\n  const fa = t * s01; // scaling factor for triangle Ta\n  const fb = t * s12;\n  return {\n    previous: {\n      x: current.x - fa * (next.x - previous.x),\n      y: current.y - fa * (next.y - previous.y)\n    },\n    next: {\n      x: current.x + fb * (next.x - previous.x),\n      y: current.y + fb * (next.y - previous.y)\n    }\n  };\n}\n/**\n * Adjust tangents to ensure monotonic properties\n */\nfunction monotoneAdjust(points, deltaK, mK) {\n  const pointsLen = points.length;\n  let alphaK, betaK, tauK, squaredMagnitude, pointCurrent;\n  let pointAfter = getPoint(points, 0);\n  for (let i = 0; i < pointsLen - 1; ++i) {\n    pointCurrent = pointAfter;\n    pointAfter = getPoint(points, i + 1);\n    if (!pointCurrent || !pointAfter) {\n      continue;\n    }\n    if (almostEquals(deltaK[i], 0, EPSILON)) {\n      mK[i] = mK[i + 1] = 0;\n      continue;\n    }\n    alphaK = mK[i] / deltaK[i];\n    betaK = mK[i + 1] / deltaK[i];\n    squaredMagnitude = Math.pow(alphaK, 2) + Math.pow(betaK, 2);\n    if (squaredMagnitude <= 9) {\n      continue;\n    }\n    tauK = 3 / Math.sqrt(squaredMagnitude);\n    mK[i] = alphaK * tauK * deltaK[i];\n    mK[i + 1] = betaK * tauK * deltaK[i];\n  }\n}\nfunction monotoneCompute(points, mK) {\n  let indexAxis = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'x';\n  const valueAxis = getValueAxis(indexAxis);\n  const pointsLen = points.length;\n  let delta, pointBefore, pointCurrent;\n  let pointAfter = getPoint(points, 0);\n  for (let i = 0; i < pointsLen; ++i) {\n    pointBefore = pointCurrent;\n    pointCurrent = pointAfter;\n    pointAfter = getPoint(points, i + 1);\n    if (!pointCurrent) {\n      continue;\n    }\n    const iPixel = pointCurrent[indexAxis];\n    const vPixel = pointCurrent[valueAxis];\n    if (pointBefore) {\n      delta = (iPixel - pointBefore[indexAxis]) / 3;\n      pointCurrent[`cp1${indexAxis}`] = iPixel - delta;\n      pointCurrent[`cp1${valueAxis}`] = vPixel - delta * mK[i];\n    }\n    if (pointAfter) {\n      delta = (pointAfter[indexAxis] - iPixel) / 3;\n      pointCurrent[`cp2${indexAxis}`] = iPixel + delta;\n      pointCurrent[`cp2${valueAxis}`] = vPixel + delta * mK[i];\n    }\n  }\n}\n/**\n * This function calculates Bézier control points in a similar way than |splineCurve|,\n * but preserves monotonicity of the provided data and ensures no local extremums are added\n * between the dataset discrete points due to the interpolation.\n * See : https://en.wikipedia.org/wiki/Monotone_cubic_interpolation\n */\nfunction splineCurveMonotone(points) {\n  let indexAxis = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'x';\n  const valueAxis = getValueAxis(indexAxis);\n  const pointsLen = points.length;\n  const deltaK = Array(pointsLen).fill(0);\n  const mK = Array(pointsLen);\n  // Calculate slopes (deltaK) and initialize tangents (mK)\n  let i, pointBefore, pointCurrent;\n  let pointAfter = getPoint(points, 0);\n  for (i = 0; i < pointsLen; ++i) {\n    pointBefore = pointCurrent;\n    pointCurrent = pointAfter;\n    pointAfter = getPoint(points, i + 1);\n    if (!pointCurrent) {\n      continue;\n    }\n    if (pointAfter) {\n      const slopeDelta = pointAfter[indexAxis] - pointCurrent[indexAxis];\n      // In the case of two points that appear at the same x pixel, slopeDeltaX is 0\n      deltaK[i] = slopeDelta !== 0 ? (pointAfter[valueAxis] - pointCurrent[valueAxis]) / slopeDelta : 0;\n    }\n    mK[i] = !pointBefore ? deltaK[i] : !pointAfter ? deltaK[i - 1] : sign(deltaK[i - 1]) !== sign(deltaK[i]) ? 0 : (deltaK[i - 1] + deltaK[i]) / 2;\n  }\n  monotoneAdjust(points, deltaK, mK);\n  monotoneCompute(points, mK, indexAxis);\n}\nfunction capControlPoint(pt, min, max) {\n  return Math.max(Math.min(pt, max), min);\n}\nfunction capBezierPoints(points, area) {\n  let i, ilen, point, inArea, inAreaPrev;\n  let inAreaNext = _isPointInArea(points[0], area);\n  for (i = 0, ilen = points.length; i < ilen; ++i) {\n    inAreaPrev = inArea;\n    inArea = inAreaNext;\n    inAreaNext = i < ilen - 1 && _isPointInArea(points[i + 1], area);\n    if (!inArea) {\n      continue;\n    }\n    point = points[i];\n    if (inAreaPrev) {\n      point.cp1x = capControlPoint(point.cp1x, area.left, area.right);\n      point.cp1y = capControlPoint(point.cp1y, area.top, area.bottom);\n    }\n    if (inAreaNext) {\n      point.cp2x = capControlPoint(point.cp2x, area.left, area.right);\n      point.cp2y = capControlPoint(point.cp2y, area.top, area.bottom);\n    }\n  }\n}\n/**\n * @private\n */\nfunction _updateBezierControlPoints(points, options, area, loop, indexAxis) {\n  let i, ilen, point, controlPoints;\n  // Only consider points that are drawn in case the spanGaps option is used\n  if (options.spanGaps) {\n    points = points.filter(pt => !pt.skip);\n  }\n  if (options.cubicInterpolationMode === 'monotone') {\n    splineCurveMonotone(points, indexAxis);\n  } else {\n    let prev = loop ? points[points.length - 1] : points[0];\n    for (i = 0, ilen = points.length; i < ilen; ++i) {\n      point = points[i];\n      controlPoints = splineCurve(prev, point, points[Math.min(i + 1, ilen - (loop ? 0 : 1)) % ilen], options.tension);\n      point.cp1x = controlPoints.previous.x;\n      point.cp1y = controlPoints.previous.y;\n      point.cp2x = controlPoints.next.x;\n      point.cp2y = controlPoints.next.y;\n      prev = point;\n    }\n  }\n  if (options.capBezierPoints) {\n    capBezierPoints(points, area);\n  }\n}\n\n/**\n * Note: typedefs are auto-exported, so use a made-up `dom` namespace where\n * necessary to avoid duplicates with `export * from './helpers`; see\n * https://github.com/microsoft/TypeScript/issues/46011\n * @typedef { import('../core/core.controller.js').default } dom.Chart\n * @typedef { import('../../types').ChartEvent } ChartEvent\n */ /**\n    * @private\n    */\nfunction _isDomSupported() {\n  return typeof window !== 'undefined' && typeof document !== 'undefined';\n}\n/**\n * @private\n */\nfunction _getParentNode(domNode) {\n  let parent = domNode.parentNode;\n  if (parent && parent.toString() === '[object ShadowRoot]') {\n    parent = parent.host;\n  }\n  return parent;\n}\n/**\n * convert max-width/max-height values that may be percentages into a number\n * @private\n */\nfunction parseMaxStyle(styleValue, node, parentProperty) {\n  let valueInPixels;\n  if (typeof styleValue === 'string') {\n    valueInPixels = parseInt(styleValue, 10);\n    if (styleValue.indexOf('%') !== -1) {\n      // percentage * size in dimension\n      valueInPixels = valueInPixels / 100 * node.parentNode[parentProperty];\n    }\n  } else {\n    valueInPixels = styleValue;\n  }\n  return valueInPixels;\n}\nconst getComputedStyle = element => element.ownerDocument.defaultView.getComputedStyle(element, null);\nfunction getStyle(el, property) {\n  return getComputedStyle(el).getPropertyValue(property);\n}\nconst positions = ['top', 'right', 'bottom', 'left'];\nfunction getPositionedStyle(styles, style, suffix) {\n  const result = {};\n  suffix = suffix ? '-' + suffix : '';\n  for (let i = 0; i < 4; i++) {\n    const pos = positions[i];\n    result[pos] = parseFloat(styles[style + '-' + pos + suffix]) || 0;\n  }\n  result.width = result.left + result.right;\n  result.height = result.top + result.bottom;\n  return result;\n}\nconst useOffsetPos = (x, y, target) => (x > 0 || y > 0) && (!target || !target.shadowRoot);\n/**\n * @param e\n * @param canvas\n * @returns Canvas position\n */\nfunction getCanvasPosition(e, canvas) {\n  const touches = e.touches;\n  const source = touches && touches.length ? touches[0] : e;\n  const {\n    offsetX,\n    offsetY\n  } = source;\n  let box = false;\n  let x, y;\n  if (useOffsetPos(offsetX, offsetY, e.target)) {\n    x = offsetX;\n    y = offsetY;\n  } else {\n    const rect = canvas.getBoundingClientRect();\n    x = source.clientX - rect.left;\n    y = source.clientY - rect.top;\n    box = true;\n  }\n  return {\n    x,\n    y,\n    box\n  };\n}\n/**\n * Gets an event's x, y coordinates, relative to the chart area\n * @param event\n * @param chart\n * @returns x and y coordinates of the event\n */\nfunction getRelativePosition(event, chart) {\n  if ('native' in event) {\n    return event;\n  }\n  const {\n    canvas,\n    currentDevicePixelRatio\n  } = chart;\n  const style = getComputedStyle(canvas);\n  const borderBox = style.boxSizing === 'border-box';\n  const paddings = getPositionedStyle(style, 'padding');\n  const borders = getPositionedStyle(style, 'border', 'width');\n  const {\n    x,\n    y,\n    box\n  } = getCanvasPosition(event, canvas);\n  const xOffset = paddings.left + (box && borders.left);\n  const yOffset = paddings.top + (box && borders.top);\n  let {\n    width,\n    height\n  } = chart;\n  if (borderBox) {\n    width -= paddings.width + borders.width;\n    height -= paddings.height + borders.height;\n  }\n  return {\n    x: Math.round((x - xOffset) / width * canvas.width / currentDevicePixelRatio),\n    y: Math.round((y - yOffset) / height * canvas.height / currentDevicePixelRatio)\n  };\n}\nfunction getContainerSize(canvas, width, height) {\n  let maxWidth, maxHeight;\n  if (width === undefined || height === undefined) {\n    const container = _getParentNode(canvas);\n    if (!container) {\n      width = canvas.clientWidth;\n      height = canvas.clientHeight;\n    } else {\n      const rect = container.getBoundingClientRect(); // this is the border box of the container\n      const containerStyle = getComputedStyle(container);\n      const containerBorder = getPositionedStyle(containerStyle, 'border', 'width');\n      const containerPadding = getPositionedStyle(containerStyle, 'padding');\n      width = rect.width - containerPadding.width - containerBorder.width;\n      height = rect.height - containerPadding.height - containerBorder.height;\n      maxWidth = parseMaxStyle(containerStyle.maxWidth, container, 'clientWidth');\n      maxHeight = parseMaxStyle(containerStyle.maxHeight, container, 'clientHeight');\n    }\n  }\n  return {\n    width,\n    height,\n    maxWidth: maxWidth || INFINITY,\n    maxHeight: maxHeight || INFINITY\n  };\n}\nconst round1 = v => Math.round(v * 10) / 10;\n// eslint-disable-next-line complexity\nfunction getMaximumSize(canvas, bbWidth, bbHeight, aspectRatio) {\n  const style = getComputedStyle(canvas);\n  const margins = getPositionedStyle(style, 'margin');\n  const maxWidth = parseMaxStyle(style.maxWidth, canvas, 'clientWidth') || INFINITY;\n  const maxHeight = parseMaxStyle(style.maxHeight, canvas, 'clientHeight') || INFINITY;\n  const containerSize = getContainerSize(canvas, bbWidth, bbHeight);\n  let {\n    width,\n    height\n  } = containerSize;\n  if (style.boxSizing === 'content-box') {\n    const borders = getPositionedStyle(style, 'border', 'width');\n    const paddings = getPositionedStyle(style, 'padding');\n    width -= paddings.width + borders.width;\n    height -= paddings.height + borders.height;\n  }\n  width = Math.max(0, width - margins.width);\n  height = Math.max(0, aspectRatio ? width / aspectRatio : height - margins.height);\n  width = round1(Math.min(width, maxWidth, containerSize.maxWidth));\n  height = round1(Math.min(height, maxHeight, containerSize.maxHeight));\n  if (width && !height) {\n    // https://github.com/chartjs/Chart.js/issues/4659\n    // If the canvas has width, but no height, default to aspectRatio of 2 (canvas default)\n    height = round1(width / 2);\n  }\n  const maintainHeight = bbWidth !== undefined || bbHeight !== undefined;\n  if (maintainHeight && aspectRatio && containerSize.height && height > containerSize.height) {\n    height = containerSize.height;\n    width = round1(Math.floor(height * aspectRatio));\n  }\n  return {\n    width,\n    height\n  };\n}\n/**\n * @param chart\n * @param forceRatio\n * @param forceStyle\n * @returns True if the canvas context size or transformation has changed.\n */\nfunction retinaScale(chart, forceRatio, forceStyle) {\n  const pixelRatio = forceRatio || 1;\n  const deviceHeight = Math.floor(chart.height * pixelRatio);\n  const deviceWidth = Math.floor(chart.width * pixelRatio);\n  chart.height = Math.floor(chart.height);\n  chart.width = Math.floor(chart.width);\n  const canvas = chart.canvas;\n  // If no style has been set on the canvas, the render size is used as display size,\n  // making the chart visually bigger, so let's enforce it to the \"correct\" values.\n  // See https://github.com/chartjs/Chart.js/issues/3575\n  if (canvas.style && (forceStyle || !canvas.style.height && !canvas.style.width)) {\n    canvas.style.height = `${chart.height}px`;\n    canvas.style.width = `${chart.width}px`;\n  }\n  if (chart.currentDevicePixelRatio !== pixelRatio || canvas.height !== deviceHeight || canvas.width !== deviceWidth) {\n    chart.currentDevicePixelRatio = pixelRatio;\n    canvas.height = deviceHeight;\n    canvas.width = deviceWidth;\n    chart.ctx.setTransform(pixelRatio, 0, 0, pixelRatio, 0, 0);\n    return true;\n  }\n  return false;\n}\n/**\n * Detects support for options object argument in addEventListener.\n * https://developer.mozilla.org/en-US/docs/Web/API/EventTarget/addEventListener#Safely_detecting_option_support\n * @private\n */\nconst supportsEventListenerOptions = function () {\n  let passiveSupported = false;\n  try {\n    const options = {\n      get passive() {\n        passiveSupported = true;\n        return false;\n      }\n    };\n    window.addEventListener('test', null, options);\n    window.removeEventListener('test', null, options);\n  } catch (e) {\n    // continue regardless of error\n  }\n  return passiveSupported;\n}();\n/**\n * The \"used\" size is the final value of a dimension property after all calculations have\n * been performed. This method uses the computed style of `element` but returns undefined\n * if the computed style is not expressed in pixels. That can happen in some cases where\n * `element` has a size relative to its parent and this last one is not yet displayed,\n * for example because of `display: none` on a parent node.\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/used_value\n * @returns Size in pixels or undefined if unknown.\n */\nfunction readUsedSize(element, property) {\n  const value = getStyle(element, property);\n  const matches = value && value.match(/^(\\d+)(\\.\\d+)?px$/);\n  return matches ? +matches[1] : undefined;\n}\n\n/**\n * @private\n */\nfunction _pointInLine(p1, p2, t, mode) {\n  return {\n    x: p1.x + t * (p2.x - p1.x),\n    y: p1.y + t * (p2.y - p1.y)\n  };\n}\n/**\n * @private\n */\nfunction _steppedInterpolation(p1, p2, t, mode) {\n  return {\n    x: p1.x + t * (p2.x - p1.x),\n    y: mode === 'middle' ? t < 0.5 ? p1.y : p2.y : mode === 'after' ? t < 1 ? p1.y : p2.y : t > 0 ? p2.y : p1.y\n  };\n}\n/**\n * @private\n */\nfunction _bezierInterpolation(p1, p2, t, mode) {\n  const cp1 = {\n    x: p1.cp2x,\n    y: p1.cp2y\n  };\n  const cp2 = {\n    x: p2.cp1x,\n    y: p2.cp1y\n  };\n  const a = _pointInLine(p1, cp1, t);\n  const b = _pointInLine(cp1, cp2, t);\n  const c = _pointInLine(cp2, p2, t);\n  const d = _pointInLine(a, b, t);\n  const e = _pointInLine(b, c, t);\n  return _pointInLine(d, e, t);\n}\nconst getRightToLeftAdapter = function (rectX, width) {\n  return {\n    x(x) {\n      return rectX + rectX + width - x;\n    },\n    setWidth(w) {\n      width = w;\n    },\n    textAlign(align) {\n      if (align === 'center') {\n        return align;\n      }\n      return align === 'right' ? 'left' : 'right';\n    },\n    xPlus(x, value) {\n      return x - value;\n    },\n    leftForLtr(x, itemWidth) {\n      return x - itemWidth;\n    }\n  };\n};\nconst getLeftToRightAdapter = function () {\n  return {\n    x(x) {\n      return x;\n    },\n    setWidth(w) {},\n    textAlign(align) {\n      return align;\n    },\n    xPlus(x, value) {\n      return x + value;\n    },\n    leftForLtr(x, _itemWidth) {\n      return x;\n    }\n  };\n};\nfunction getRtlAdapter(rtl, rectX, width) {\n  return rtl ? getRightToLeftAdapter(rectX, width) : getLeftToRightAdapter();\n}\nfunction overrideTextDirection(ctx, direction) {\n  let style, original;\n  if (direction === 'ltr' || direction === 'rtl') {\n    style = ctx.canvas.style;\n    original = [style.getPropertyValue('direction'), style.getPropertyPriority('direction')];\n    style.setProperty('direction', direction, 'important');\n    ctx.prevTextDirection = original;\n  }\n}\nfunction restoreTextDirection(ctx, original) {\n  if (original !== undefined) {\n    delete ctx.prevTextDirection;\n    ctx.canvas.style.setProperty('direction', original[0], original[1]);\n  }\n}\nfunction propertyFn(property) {\n  if (property === 'angle') {\n    return {\n      between: _angleBetween,\n      compare: _angleDiff,\n      normalize: _normalizeAngle\n    };\n  }\n  return {\n    between: _isBetween,\n    compare: (a, b) => a - b,\n    normalize: x => x\n  };\n}\nfunction normalizeSegment(_ref) {\n  let {\n    start,\n    end,\n    count,\n    loop,\n    style\n  } = _ref;\n  return {\n    start: start % count,\n    end: end % count,\n    loop: loop && (end - start + 1) % count === 0,\n    style\n  };\n}\nfunction getSegment(segment, points, bounds) {\n  const {\n    property,\n    start: startBound,\n    end: endBound\n  } = bounds;\n  const {\n    between,\n    normalize\n  } = propertyFn(property);\n  const count = points.length;\n  let {\n    start,\n    end,\n    loop\n  } = segment;\n  let i, ilen;\n  if (loop) {\n    start += count;\n    end += count;\n    for (i = 0, ilen = count; i < ilen; ++i) {\n      if (!between(normalize(points[start % count][property]), startBound, endBound)) {\n        break;\n      }\n      start--;\n      end--;\n    }\n    start %= count;\n    end %= count;\n  }\n  if (end < start) {\n    end += count;\n  }\n  return {\n    start,\n    end,\n    loop,\n    style: segment.style\n  };\n}\nfunction _boundSegment(segment, points, bounds) {\n  if (!bounds) {\n    return [segment];\n  }\n  const {\n    property,\n    start: startBound,\n    end: endBound\n  } = bounds;\n  const count = points.length;\n  const {\n    compare,\n    between,\n    normalize\n  } = propertyFn(property);\n  const {\n    start,\n    end,\n    loop,\n    style\n  } = getSegment(segment, points, bounds);\n  const result = [];\n  let inside = false;\n  let subStart = null;\n  let value, point, prevValue;\n  const startIsBefore = () => between(startBound, prevValue, value) && compare(startBound, prevValue) !== 0;\n  const endIsBefore = () => compare(endBound, value) === 0 || between(endBound, prevValue, value);\n  const shouldStart = () => inside || startIsBefore();\n  const shouldStop = () => !inside || endIsBefore();\n  for (let i = start, prev = start; i <= end; ++i) {\n    point = points[i % count];\n    if (point.skip) {\n      continue;\n    }\n    value = normalize(point[property]);\n    if (value === prevValue) {\n      continue;\n    }\n    inside = between(value, startBound, endBound);\n    if (subStart === null && shouldStart()) {\n      subStart = compare(value, startBound) === 0 ? i : prev;\n    }\n    if (subStart !== null && shouldStop()) {\n      result.push(normalizeSegment({\n        start: subStart,\n        end: i,\n        loop,\n        count,\n        style\n      }));\n      subStart = null;\n    }\n    prev = i;\n    prevValue = value;\n  }\n  if (subStart !== null) {\n    result.push(normalizeSegment({\n      start: subStart,\n      end,\n      loop,\n      count,\n      style\n    }));\n  }\n  return result;\n}\nfunction _boundSegments(line, bounds) {\n  const result = [];\n  const segments = line.segments;\n  for (let i = 0; i < segments.length; i++) {\n    const sub = _boundSegment(segments[i], line.points, bounds);\n    if (sub.length) {\n      result.push(...sub);\n    }\n  }\n  return result;\n}\nfunction findStartAndEnd(points, count, loop, spanGaps) {\n  let start = 0;\n  let end = count - 1;\n  if (loop && !spanGaps) {\n    while (start < count && !points[start].skip) {\n      start++;\n    }\n  }\n  while (start < count && points[start].skip) {\n    start++;\n  }\n  start %= count;\n  if (loop) {\n    end += start;\n  }\n  while (end > start && points[end % count].skip) {\n    end--;\n  }\n  end %= count;\n  return {\n    start,\n    end\n  };\n}\nfunction solidSegments(points, start, max, loop) {\n  const count = points.length;\n  const result = [];\n  let last = start;\n  let prev = points[start];\n  let end;\n  for (end = start + 1; end <= max; ++end) {\n    const cur = points[end % count];\n    if (cur.skip || cur.stop) {\n      if (!prev.skip) {\n        loop = false;\n        result.push({\n          start: start % count,\n          end: (end - 1) % count,\n          loop\n        });\n        start = last = cur.stop ? end : null;\n      }\n    } else {\n      last = end;\n      if (prev.skip) {\n        start = end;\n      }\n    }\n    prev = cur;\n  }\n  if (last !== null) {\n    result.push({\n      start: start % count,\n      end: last % count,\n      loop\n    });\n  }\n  return result;\n}\nfunction _computeSegments(line, segmentOptions) {\n  const points = line.points;\n  const spanGaps = line.options.spanGaps;\n  const count = points.length;\n  if (!count) {\n    return [];\n  }\n  const loop = !!line._loop;\n  const {\n    start,\n    end\n  } = findStartAndEnd(points, count, loop, spanGaps);\n  if (spanGaps === true) {\n    return splitByStyles(line, [{\n      start,\n      end,\n      loop\n    }], points, segmentOptions);\n  }\n  const max = end < start ? end + count : end;\n  const completeLoop = !!line._fullLoop && start === 0 && end === count - 1;\n  return splitByStyles(line, solidSegments(points, start, max, completeLoop), points, segmentOptions);\n}\nfunction splitByStyles(line, segments, points, segmentOptions) {\n  if (!segmentOptions || !segmentOptions.setContext || !points) {\n    return segments;\n  }\n  return doSplitByStyles(line, segments, points, segmentOptions);\n}\nfunction doSplitByStyles(line, segments, points, segmentOptions) {\n  const chartContext = line._chart.getContext();\n  const baseStyle = readStyle(line.options);\n  const {\n    _datasetIndex: datasetIndex,\n    options: {\n      spanGaps\n    }\n  } = line;\n  const count = points.length;\n  const result = [];\n  let prevStyle = baseStyle;\n  let start = segments[0].start;\n  let i = start;\n  function addStyle(s, e, l, st) {\n    const dir = spanGaps ? -1 : 1;\n    if (s === e) {\n      return;\n    }\n    s += count;\n    while (points[s % count].skip) {\n      s -= dir;\n    }\n    while (points[e % count].skip) {\n      e += dir;\n    }\n    if (s % count !== e % count) {\n      result.push({\n        start: s % count,\n        end: e % count,\n        loop: l,\n        style: st\n      });\n      prevStyle = st;\n      start = e % count;\n    }\n  }\n  for (const segment of segments) {\n    start = spanGaps ? start : segment.start;\n    let prev = points[start % count];\n    let style;\n    for (i = start + 1; i <= segment.end; i++) {\n      const pt = points[i % count];\n      style = readStyle(segmentOptions.setContext(createContext(chartContext, {\n        type: 'segment',\n        p0: prev,\n        p1: pt,\n        p0DataIndex: (i - 1) % count,\n        p1DataIndex: i % count,\n        datasetIndex\n      })));\n      if (styleChanged(style, prevStyle)) {\n        addStyle(start, i - 1, segment.loop, prevStyle);\n      }\n      prev = pt;\n      prevStyle = style;\n    }\n    if (start < i - 1) {\n      addStyle(start, i - 1, segment.loop, prevStyle);\n    }\n  }\n  return result;\n}\nfunction readStyle(options) {\n  return {\n    backgroundColor: options.backgroundColor,\n    borderCapStyle: options.borderCapStyle,\n    borderDash: options.borderDash,\n    borderDashOffset: options.borderDashOffset,\n    borderJoinStyle: options.borderJoinStyle,\n    borderWidth: options.borderWidth,\n    borderColor: options.borderColor\n  };\n}\nfunction styleChanged(style, prevStyle) {\n  if (!prevStyle) {\n    return false;\n  }\n  const cache = [];\n  const replacer = function (key, value) {\n    if (!isPatternOrGradient(value)) {\n      return value;\n    }\n    if (!cache.includes(value)) {\n      cache.push(value);\n    }\n    return cache.indexOf(value);\n  };\n  return JSON.stringify(style, replacer) !== JSON.stringify(prevStyle, replacer);\n}\nexport { unclipArea as $, _rlookupByKey as A, _lookupByKey as B, _isPointInArea as C, getAngleFromPoint as D, toPadding as E, each as F, getMaximumSize as G, HALF_PI as H, _getParentNode as I, readUsedSize as J, supportsEventListenerOptions as K, throttled as L, _isDomSupported as M, _factorize as N, finiteOrDefault as O, PI as P, callback as Q, _addGrace as R, _limitValue as S, TAU as T, toDegrees as U, _measureText as V, _int16Range as W, _alignPixel as X, clipArea as Y, renderText as Z, _arrayUnique as _, resolve as a, fontString as a$, toFont as a0, _toLeftRightCenter as a1, _alignStartEnd as a2, overrides as a3, merge as a4, _capitalize as a5, descriptors as a6, isFunction as a7, _attachContext as a8, _createResolver as a9, overrideTextDirection as aA, _textX as aB, restoreTextDirection as aC, drawPointLegend as aD, distanceBetweenPoints as aE, noop as aF, _setMinAndMaxByKey as aG, niceNum as aH, almostWhole as aI, almostEquals as aJ, _decimalPlaces as aK, Ticks as aL, log10 as aM, _longestText as aN, _filterBetween as aO, _lookup as aP, isPatternOrGradient as aQ, getHoverColor as aR, clone as aS, _merger as aT, _mergerIf as aU, _deprecated as aV, _splitKey as aW, toFontString as aX, splineCurve as aY, splineCurveMonotone as aZ, getStyle as a_, _descriptors as aa, mergeIf as ab, uid as ac, debounce as ad, retinaScale as ae, clearCanvas as af, setsEqual as ag, _elementsEqual as ah, _isClickEvent as ai, _isBetween as aj, _readValueToProps as ak, _updateBezierControlPoints as al, _computeSegments as am, _boundSegments as an, _steppedInterpolation as ao, _bezierInterpolation as ap, _pointInLine as aq, _steppedLineTo as ar, _bezierCurveTo as as, drawPoint as at, addRoundedRectPath as au, toTRBL as av, toTRBLCorners as aw, _boundSegment as ax, _normalizeAngle as ay, getRtlAdapter as az, isArray as b, toLineHeight as b0, PITAU as b1, INFINITY as b2, RAD_PER_DEG as b3, QUARTER_PI as b4, TWO_THIRDS_PI as b5, _angleDiff as b6, color as c, defaults as d, effects as e, resolveObjectKey as f, isNumberFinite as g, defined as h, isObject as i, createContext as j, isNullOrUndef as k, listenArrayEvents as l, toPercentage as m, toDimension as n, formatNumber as o, _angleBetween as p, _getStartAndCountOfVisiblePoints as q, requestAnimFrame as r, sign as s, toRadians as t, unlistenArrayEvents as u, valueOrDefault as v, _scaleRangesChanged as w, isNumber as x, _parseObjectDataRadialScale as y, getRelativePosition as z };", "map": {"version": 3, "names": ["noop", "uid", "id", "isNullOrUndef", "value", "isArray", "Array", "type", "Object", "prototype", "toString", "call", "slice", "isObject", "isNumberFinite", "Number", "isFinite", "finiteOrDefault", "defaultValue", "valueOrDefault", "toPercentage", "dimension", "endsWith", "parseFloat", "toDimension", "callback", "fn", "args", "thisArg", "apply", "each", "loopable", "reverse", "i", "len", "keys", "length", "_elementsEqual", "a0", "a1", "ilen", "v0", "v1", "datasetIndex", "index", "clone", "source", "map", "target", "create", "klen", "k", "is<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "indexOf", "_merger", "options", "tval", "sval", "merge", "sources", "merger", "current", "mergeIf", "_mergerIf", "hasOwnProperty", "_deprecated", "scope", "previous", "undefined", "console", "warn", "keyResolvers", "v", "x", "o", "y", "_splitKey", "parts", "split", "tmp", "part", "push", "_getKeyResolver", "obj", "resolveObjectKey", "resolver", "_capitalize", "str", "char<PERSON>t", "toUpperCase", "defined", "isFunction", "setsEqual", "a", "b", "size", "item", "has", "_isClickEvent", "e", "PI", "Math", "TAU", "PITAU", "INFINITY", "POSITIVE_INFINITY", "RAD_PER_DEG", "HALF_PI", "QUARTER_PI", "TWO_THIRDS_PI", "log10", "sign", "almostEquals", "epsilon", "abs", "niceNum", "range", "roundedRange", "round", "niceRange", "pow", "floor", "fraction", "niceFraction", "_factorize", "result", "sqrt", "sort", "pop", "isNumber", "n", "isNaN", "almostWhole", "rounded", "_setMinAndMaxByKey", "array", "property", "min", "max", "toRadians", "degrees", "toDegrees", "radians", "_decimalPlaces", "p", "getAngleFromPoint", "centrePoint", "anglePoint", "distanceFromXCenter", "distanceFromYCenter", "radialDistanceFromCenter", "angle", "atan2", "distance", "distanceBetweenPoints", "pt1", "pt2", "_angleDiff", "_normalizeAngle", "_angleBetween", "start", "end", "sameAngleIsFullCircle", "s", "angleToStart", "angleToEnd", "startToAngle", "endToAngle", "_limitValue", "_int16Range", "_isBetween", "arguments", "_lookup", "table", "cmp", "hi", "lo", "mid", "_lookup<PERSON><PERSON><PERSON><PERSON>", "last", "ti", "_rlookupByKey", "_filterBetween", "values", "arrayEvents", "listenArrayEvents", "listener", "_chartjs", "listeners", "defineProperty", "configurable", "enumerable", "for<PERSON>ach", "method", "base", "_len", "_key", "res", "object", "unlistenArrayEvents", "stub", "splice", "_arrayUnique", "items", "set", "Set", "from", "fontString", "pixelSize", "fontStyle", "fontFamily", "requestAnimFrame", "window", "requestAnimationFrame", "throttled", "argsToUse", "ticking", "_len2", "_key2", "debounce", "delay", "timeout", "_len3", "_key3", "clearTimeout", "setTimeout", "_toLeftRightCenter", "align", "_alignStartEnd", "_textX", "left", "right", "rtl", "check", "_getStartAndCountOfVisiblePoints", "meta", "points", "animationsDisabled", "pointCount", "count", "_sorted", "iScale", "_parsed", "axis", "minDefined", "maxDefined", "getUserBounds", "getPixelForValue", "_scaleRangesChanged", "xScale", "yScale", "_scaleRanges", "newRang<PERSON>", "xmin", "xmax", "ymin", "ymax", "changed", "assign", "atEdge", "t", "elasticIn", "sin", "elasticOut", "effects", "linear", "easeInQuad", "easeOutQuad", "easeInOutQuad", "easeInCubic", "easeOutCubic", "easeInOutCubic", "easeInQuart", "easeOutQuart", "easeInOutQuart", "easeInQuint", "easeOutQuint", "easeInOutQuint", "easeInSine", "cos", "easeOutSine", "easeInOutSine", "easeInExpo", "easeOutExpo", "easeInOutExpo", "easeInCirc", "easeOutCirc", "easeInOutCirc", "easeInElastic", "easeOutElastic", "easeInOutElastic", "easeInBack", "easeOutBack", "easeInOutBack", "easeInBounce", "easeOutBounce", "m", "d", "easeInOutBounce", "isPatternOrGradient", "color", "Color", "getHoverColor", "saturate", "darken", "hexString", "numbers", "colors", "applyAnimationsDefaults", "defaults", "duration", "easing", "loop", "to", "describe", "_fallback", "_indexable", "_scriptable", "name", "properties", "active", "animation", "resize", "show", "animations", "visible", "hide", "applyLayoutsDefaults", "autoPadding", "padding", "top", "bottom", "intlCache", "Map", "getNumberFormat", "locale", "cache<PERSON>ey", "JSON", "stringify", "formatter", "get", "Intl", "NumberFormat", "formatNumber", "num", "format", "formatters", "numeric", "tickValue", "ticks", "chart", "notation", "delta", "maxTick", "calculateDelta", "log<PERSON><PERSON><PERSON>", "numDecimal", "minimumFractionDigits", "maximumFractionDigits", "logarithmic", "remain", "significand", "includes", "Ticks", "applyScaleDefaults", "display", "offset", "beginAtZero", "bounds", "grace", "grid", "lineWidth", "drawOnChartArea", "drawTicks", "tick<PERSON><PERSON>th", "tickWidth", "_ctx", "tickColor", "border", "dash", "dashOffset", "width", "title", "text", "minRotation", "maxRotation", "mirror", "textStrokeWidth", "textStrokeColor", "autoSkip", "autoSkipPadding", "labelOffset", "minor", "major", "crossAlign", "showLabelBackdrop", "backdropColor", "backdropPadding", "route", "startsWith", "overrides", "descriptors", "getScope$1", "node", "root", "De<PERSON>ults", "constructor", "_descriptors", "_appliers", "backgroundColor", "borderColor", "datasets", "devicePixelRatio", "context", "platform", "getDevicePixelRatio", "elements", "events", "font", "family", "style", "lineHeight", "weight", "hover", "hoverBackgroundColor", "ctx", "hoverBorderColor", "hoverColor", "indexAxis", "interaction", "mode", "intersect", "includeInvisible", "maintainAspectRatio", "onHover", "onClick", "parsing", "plugins", "responsive", "scale", "scales", "showLine", "drawActiveElementsOnTop", "override", "targetScope", "targetName", "scopeObject", "targetScopeObject", "privateName", "defineProperties", "writable", "local", "appliers", "toFontString", "_measureText", "data", "gc", "longest", "string", "textWidth", "measureText", "_longestText", "arrayOfThings", "cache", "garbageCollect", "save", "j", "jlen", "thing", "nestedThing", "restore", "gcLen", "_alignPixel", "pixel", "currentDevicePixelRatio", "halfWidth", "clearCanvas", "canvas", "getContext", "resetTransform", "clearRect", "height", "drawPoint", "drawPointLegend", "w", "xOffset", "yOffset", "cornerRadius", "xOffsetW", "yOffsetW", "pointStyle", "rotation", "radius", "rad", "translate", "rotate", "drawImage", "beginPath", "ellipse", "arc", "closePath", "moveTo", "lineTo", "SQRT1_2", "rect", "fill", "borderWidth", "stroke", "_isPointInArea", "point", "area", "margin", "clipArea", "clip", "unclipArea", "_steppedLineTo", "flip", "midpoint", "_bezierCurveTo", "bezierCurveTo", "cp1x", "cp2x", "cp1y", "cp2y", "setRenderOpts", "opts", "translation", "fillStyle", "textAlign", "textBaseline", "decorateText", "line", "strikethrough", "underline", "metrics", "actualBoundingBoxLeft", "actualBoundingBoxRight", "actualBoundingBoxAscent", "actualBoundingBoxDescent", "yDecoration", "strokeStyle", "decorationWidth", "drawBackdrop", "oldColor", "fillRect", "renderText", "lines", "strokeWidth", "strokeColor", "backdrop", "strokeText", "max<PERSON><PERSON><PERSON>", "fillText", "addRoundedRectPath", "h", "topLeft", "bottomLeft", "bottomRight", "topRight", "LINE_HEIGHT", "FONT_STYLE", "toLineHeight", "matches", "match", "numberOrZero", "_readValueToProps", "props", "ret", "objProps", "read", "prop", "toTRBL", "toTRBLCorners", "toPadding", "toFont", "fallback", "parseInt", "resolve", "inputs", "info", "cacheable", "_addGrace", "minmax", "change", "keepZero", "add", "createContext", "parentContext", "_createResolver", "scopes", "prefixes", "rootScopes", "get<PERSON><PERSON><PERSON>", "finalRootScopes", "_resolve", "Symbol", "toStringTag", "_cacheable", "_scopes", "_rootScopes", "_getTarget", "Proxy", "deleteProperty", "_keys", "_cached", "_resolveWithPrefixes", "getOwnPropertyDescriptor", "Reflect", "getPrototypeOf", "getKeysFromAllScopes", "ownKeys", "storage", "_storage", "_attachContext", "proxy", "subProxy", "descriptor<PERSON><PERSON><PERSON><PERSON>", "_proxy", "_context", "_subProxy", "_stack", "setContext", "receiver", "_resolveWithContext", "allKeys", "scriptable", "indexable", "_allKeys", "isScriptable", "isIndexable", "read<PERSON><PERSON>", "prefix", "needsSubResolver", "_resolveScriptable", "_resolveArray", "getValue", "Error", "join", "delete", "createSubResolver", "arr", "filter", "<PERSON><PERSON><PERSON><PERSON>", "getScope", "parent", "addScopes", "parentScopes", "parentFallback", "allScopes", "addScopesFromKey", "subGetTarget", "resolveKeysFromAllScopes", "_parseObjectDataRadialScale", "_parsing", "parsed", "r", "parse", "EPSILON", "getPoint", "skip", "getValueAxis", "splineCurve", "firstPoint", "middlePoint", "afterPoint", "next", "d01", "d12", "s01", "s12", "fa", "fb", "monotoneAdjust", "deltaK", "mK", "pointsLen", "alphaK", "betaK", "tauK", "squaredMagnitude", "pointCurrent", "pointAfter", "monotoneCompute", "valueAxis", "pointBefore", "iPixel", "vPixel", "splineCurveMonotone", "slopeDel<PERSON>", "capControlPoint", "pt", "capBezierPoints", "inArea", "inAreaPrev", "inAreaNext", "_updateBezierControlPoints", "controlPoints", "spanGaps", "cubicInterpolationMode", "prev", "tension", "_isDomSupported", "document", "_getParentNode", "domNode", "parentNode", "host", "parseMaxStyle", "styleValue", "parentProperty", "valueInPixels", "getComputedStyle", "element", "ownerDocument", "defaultView", "getStyle", "el", "getPropertyValue", "positions", "getPositionedStyle", "styles", "suffix", "pos", "useOffsetPos", "shadowRoot", "getCanvasPosition", "touches", "offsetX", "offsetY", "box", "getBoundingClientRect", "clientX", "clientY", "getRelativePosition", "event", "borderBox", "boxSizing", "paddings", "borders", "getContainerSize", "maxHeight", "container", "clientWidth", "clientHeight", "containerStyle", "containerBorder", "containerPadding", "round1", "getMaximumSize", "bb<PERSON><PERSON><PERSON>", "bbHeight", "aspectRatio", "margins", "containerSize", "maintainHeight", "retinaScale", "forceRatio", "forceStyle", "pixelRatio", "deviceHeight", "deviceWidth", "setTransform", "supportsEventListenerOptions", "passiveSupported", "passive", "addEventListener", "removeEventListener", "readUsedSize", "_pointInLine", "p1", "p2", "_steppedInterpolation", "_bezierInterpolation", "cp1", "cp2", "c", "getRightToLeftAdapter", "rectX", "<PERSON><PERSON><PERSON><PERSON>", "xPlus", "leftForLtr", "itemWidth", "getLeftToRightAdapter", "_itemWidth", "getRtlAdapter", "overrideTextDirection", "direction", "original", "getPropertyPriority", "setProperty", "prevTextDirection", "restoreTextDirection", "propertyFn", "between", "compare", "normalize", "normalizeSegment", "_ref", "getSegment", "segment", "startBound", "endBound", "_boundSegment", "inside", "subStart", "prevValue", "startIsBefore", "endIsBefore", "shouldStart", "shouldStop", "_boundSegments", "segments", "sub", "findStartAndEnd", "solidSegments", "cur", "stop", "_computeSegments", "segmentOptions", "_loop", "splitByStyles", "completeLoop", "_fullLoop", "doSplitByStyles", "chartContext", "_chart", "baseStyle", "readStyle", "_datasetIndex", "prevStyle", "addStyle", "l", "st", "dir", "p0", "p0DataIndex", "p1DataIndex", "styleChanged", "borderCapStyle", "borderDash", "borderDashOffset", "borderJoinStyle", "replacer"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\chart.js\\src\\helpers\\helpers.core.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\chart.js\\src\\helpers\\helpers.math.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\chart.js\\src\\helpers\\helpers.collection.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\chart.js\\src\\helpers\\helpers.extras.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\chart.js\\src\\helpers\\helpers.easing.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\chart.js\\src\\helpers\\helpers.color.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\chart.js\\src\\core\\core.animations.defaults.js", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\chart.js\\src\\core\\core.layouts.defaults.js", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\chart.js\\src\\helpers\\helpers.intl.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\chart.js\\src\\core\\core.ticks.js", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\chart.js\\src\\core\\core.scale.defaults.js", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\chart.js\\src\\core\\core.defaults.js", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\chart.js\\src\\helpers\\helpers.canvas.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\chart.js\\src\\helpers\\helpers.options.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\chart.js\\src\\helpers\\helpers.config.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\chart.js\\src\\helpers\\helpers.curve.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\chart.js\\src\\helpers\\helpers.dom.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\chart.js\\src\\helpers\\helpers.interpolation.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\chart.js\\src\\helpers\\helpers.rtl.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\chart.js\\src\\helpers\\helpers.segment.js"], "sourcesContent": ["/**\n * @namespace Chart.helpers\n */\n\nimport type {AnyObject} from '../types/basic.js';\nimport type {ActiveDataPoint, ChartEvent} from '../types/index.js';\n\n/**\n * An empty function that can be used, for example, for optional callback.\n */\nexport function noop() {\n  /* noop */\n}\n\n/**\n * Returns a unique id, sequentially generated from a global variable.\n */\nexport const uid = (() => {\n  let id = 0;\n  return () => id++;\n})();\n\n/**\n * Returns true if `value` is neither null nor undefined, else returns false.\n * @param value - The value to test.\n * @since 2.7.0\n */\nexport function isNullOrUndef(value: unknown): value is null | undefined {\n  return value === null || typeof value === 'undefined';\n}\n\n/**\n * Returns true if `value` is an array (including typed arrays), else returns false.\n * @param value - The value to test.\n * @function\n */\nexport function isArray<T = unknown>(value: unknown): value is T[] {\n  if (Array.isArray && Array.isArray(value)) {\n    return true;\n  }\n  const type = Object.prototype.toString.call(value);\n  if (type.slice(0, 7) === '[object' && type.slice(-6) === 'Array]') {\n    return true;\n  }\n  return false;\n}\n\n/**\n * Returns true if `value` is an object (excluding null), else returns false.\n * @param value - The value to test.\n * @since 2.7.0\n */\nexport function isObject(value: unknown): value is AnyObject {\n  return value !== null && Object.prototype.toString.call(value) === '[object Object]';\n}\n\n/**\n * Returns true if `value` is a finite number, else returns false\n * @param value  - The value to test.\n */\nfunction isNumberFinite(value: unknown): value is number {\n  return (typeof value === 'number' || value instanceof Number) && isFinite(+value);\n}\nexport {\n  isNumberFinite as isFinite,\n};\n\n/**\n * Returns `value` if finite, else returns `defaultValue`.\n * @param value - The value to return if defined.\n * @param defaultValue - The value to return if `value` is not finite.\n */\nexport function finiteOrDefault(value: unknown, defaultValue: number) {\n  return isNumberFinite(value) ? value : defaultValue;\n}\n\n/**\n * Returns `value` if defined, else returns `defaultValue`.\n * @param value - The value to return if defined.\n * @param defaultValue - The value to return if `value` is undefined.\n */\nexport function valueOrDefault<T>(value: T | undefined, defaultValue: T) {\n  return typeof value === 'undefined' ? defaultValue : value;\n}\n\nexport const toPercentage = (value: number | string, dimension: number) =>\n  typeof value === 'string' && value.endsWith('%') ?\n    parseFloat(value) / 100\n    : +value / dimension;\n\nexport const toDimension = (value: number | string, dimension: number) =>\n  typeof value === 'string' && value.endsWith('%') ?\n    parseFloat(value) / 100 * dimension\n    : +value;\n\n/**\n * Calls `fn` with the given `args` in the scope defined by `thisArg` and returns the\n * value returned by `fn`. If `fn` is not a function, this method returns undefined.\n * @param fn - The function to call.\n * @param args - The arguments with which `fn` should be called.\n * @param [thisArg] - The value of `this` provided for the call to `fn`.\n */\nexport function callback<T extends (this: TA, ...restArgs: unknown[]) => R, TA, R>(\n  fn: T | undefined,\n  args: unknown[],\n  thisArg?: TA\n): R | undefined {\n  if (fn && typeof fn.call === 'function') {\n    return fn.apply(thisArg, args);\n  }\n}\n\n/**\n * Note(SB) for performance sake, this method should only be used when loopable type\n * is unknown or in none intensive code (not called often and small loopable). Else\n * it's preferable to use a regular for() loop and save extra function calls.\n * @param loopable - The object or array to be iterated.\n * @param fn - The function to call for each item.\n * @param [thisArg] - The value of `this` provided for the call to `fn`.\n * @param [reverse] - If true, iterates backward on the loopable.\n */\nexport function each<T, TA>(\n  loopable: Record<string, T>,\n  fn: (this: TA, v: T, i: string) => void,\n  thisArg?: TA,\n  reverse?: boolean\n): void;\nexport function each<T, TA>(\n  loopable: T[],\n  fn: (this: TA, v: T, i: number) => void,\n  thisArg?: TA,\n  reverse?: boolean\n): void;\nexport function each<T, TA>(\n  loopable: T[] | Record<string, T>,\n  fn: (this: TA, v: T, i: any) => void,\n  thisArg?: TA,\n  reverse?: boolean\n) {\n  let i: number, len: number, keys: string[];\n  if (isArray(loopable)) {\n    len = loopable.length;\n    if (reverse) {\n      for (i = len - 1; i >= 0; i--) {\n        fn.call(thisArg, loopable[i], i);\n      }\n    } else {\n      for (i = 0; i < len; i++) {\n        fn.call(thisArg, loopable[i], i);\n      }\n    }\n  } else if (isObject(loopable)) {\n    keys = Object.keys(loopable);\n    len = keys.length;\n    for (i = 0; i < len; i++) {\n      fn.call(thisArg, loopable[keys[i]], keys[i]);\n    }\n  }\n}\n\n/**\n * Returns true if the `a0` and `a1` arrays have the same content, else returns false.\n * @param a0 - The array to compare\n * @param a1 - The array to compare\n * @private\n */\nexport function _elementsEqual(a0: ActiveDataPoint[], a1: ActiveDataPoint[]) {\n  let i: number, ilen: number, v0: ActiveDataPoint, v1: ActiveDataPoint;\n\n  if (!a0 || !a1 || a0.length !== a1.length) {\n    return false;\n  }\n\n  for (i = 0, ilen = a0.length; i < ilen; ++i) {\n    v0 = a0[i];\n    v1 = a1[i];\n\n    if (v0.datasetIndex !== v1.datasetIndex || v0.index !== v1.index) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\n/**\n * Returns a deep copy of `source` without keeping references on objects and arrays.\n * @param source - The value to clone.\n */\nexport function clone<T>(source: T): T {\n  if (isArray(source)) {\n    return source.map(clone) as unknown as T;\n  }\n\n  if (isObject(source)) {\n    const target = Object.create(null);\n    const keys = Object.keys(source);\n    const klen = keys.length;\n    let k = 0;\n\n    for (; k < klen; ++k) {\n      target[keys[k]] = clone(source[keys[k]]);\n    }\n\n    return target;\n  }\n\n  return source;\n}\n\nfunction isValidKey(key: string) {\n  return ['__proto__', 'prototype', 'constructor'].indexOf(key) === -1;\n}\n\n/**\n * The default merger when Chart.helpers.merge is called without merger option.\n * Note(SB): also used by mergeConfig and mergeScaleConfig as fallback.\n * @private\n */\nexport function _merger(key: string, target: AnyObject, source: AnyObject, options: AnyObject) {\n  if (!isValidKey(key)) {\n    return;\n  }\n\n  const tval = target[key];\n  const sval = source[key];\n\n  if (isObject(tval) && isObject(sval)) {\n    // eslint-disable-next-line @typescript-eslint/no-use-before-define\n    merge(tval, sval, options);\n  } else {\n    target[key] = clone(sval);\n  }\n}\n\nexport interface MergeOptions {\n  merger?: (key: string, target: AnyObject, source: AnyObject, options?: AnyObject) => void;\n}\n\n/**\n * Recursively deep copies `source` properties into `target` with the given `options`.\n * IMPORTANT: `target` is not cloned and will be updated with `source` properties.\n * @param target - The target object in which all sources are merged into.\n * @param source - Object(s) to merge into `target`.\n * @param [options] - Merging options:\n * @param [options.merger] - The merge method (key, target, source, options)\n * @returns The `target` object.\n */\nexport function merge<T>(target: T, source: [], options?: MergeOptions): T;\nexport function merge<T, S1>(target: T, source: S1, options?: MergeOptions): T & S1;\nexport function merge<T, S1>(target: T, source: [S1], options?: MergeOptions): T & S1;\nexport function merge<T, S1, S2>(target: T, source: [S1, S2], options?: MergeOptions): T & S1 & S2;\nexport function merge<T, S1, S2, S3>(target: T, source: [S1, S2, S3], options?: MergeOptions): T & S1 & S2 & S3;\nexport function merge<T, S1, S2, S3, S4>(\n  target: T,\n  source: [S1, S2, S3, S4],\n  options?: MergeOptions\n): T & S1 & S2 & S3 & S4;\nexport function merge<T>(target: T, source: AnyObject[], options?: MergeOptions): AnyObject;\nexport function merge<T>(target: T, source: AnyObject[], options?: MergeOptions): AnyObject {\n  const sources = isArray(source) ? source : [source];\n  const ilen = sources.length;\n\n  if (!isObject(target)) {\n    return target as AnyObject;\n  }\n\n  options = options || {};\n  const merger = options.merger || _merger;\n  let current: AnyObject;\n\n  for (let i = 0; i < ilen; ++i) {\n    current = sources[i];\n    if (!isObject(current)) {\n      continue;\n    }\n\n    const keys = Object.keys(current);\n    for (let k = 0, klen = keys.length; k < klen; ++k) {\n      merger(keys[k], target, current, options as AnyObject);\n    }\n  }\n\n  return target;\n}\n\n/**\n * Recursively deep copies `source` properties into `target` *only* if not defined in target.\n * IMPORTANT: `target` is not cloned and will be updated with `source` properties.\n * @param target - The target object in which all sources are merged into.\n * @param source - Object(s) to merge into `target`.\n * @returns The `target` object.\n */\nexport function mergeIf<T>(target: T, source: []): T;\nexport function mergeIf<T, S1>(target: T, source: S1): T & S1;\nexport function mergeIf<T, S1>(target: T, source: [S1]): T & S1;\nexport function mergeIf<T, S1, S2>(target: T, source: [S1, S2]): T & S1 & S2;\nexport function mergeIf<T, S1, S2, S3>(target: T, source: [S1, S2, S3]): T & S1 & S2 & S3;\nexport function mergeIf<T, S1, S2, S3, S4>(target: T, source: [S1, S2, S3, S4]): T & S1 & S2 & S3 & S4;\nexport function mergeIf<T>(target: T, source: AnyObject[]): AnyObject;\nexport function mergeIf<T>(target: T, source: AnyObject[]): AnyObject {\n  // eslint-disable-next-line @typescript-eslint/no-use-before-define\n  return merge<T>(target, source, {merger: _mergerIf});\n}\n\n/**\n * Merges source[key] in target[key] only if target[key] is undefined.\n * @private\n */\nexport function _mergerIf(key: string, target: AnyObject, source: AnyObject) {\n  if (!isValidKey(key)) {\n    return;\n  }\n\n  const tval = target[key];\n  const sval = source[key];\n\n  if (isObject(tval) && isObject(sval)) {\n    mergeIf(tval, sval);\n  } else if (!Object.prototype.hasOwnProperty.call(target, key)) {\n    target[key] = clone(sval);\n  }\n}\n\n/**\n * @private\n */\nexport function _deprecated(scope: string, value: unknown, previous: string, current: string) {\n  if (value !== undefined) {\n    console.warn(scope + ': \"' + previous +\n      '\" is deprecated. Please use \"' + current + '\" instead');\n  }\n}\n\n// resolveObjectKey resolver cache\nconst keyResolvers = {\n  // Chart.helpers.core resolveObjectKey should resolve empty key to root object\n  '': v => v,\n  // default resolvers\n  x: o => o.x,\n  y: o => o.y\n};\n\n/**\n * @private\n */\nexport function _splitKey(key: string) {\n  const parts = key.split('.');\n  const keys: string[] = [];\n  let tmp = '';\n  for (const part of parts) {\n    tmp += part;\n    if (tmp.endsWith('\\\\')) {\n      tmp = tmp.slice(0, -1) + '.';\n    } else {\n      keys.push(tmp);\n      tmp = '';\n    }\n  }\n  return keys;\n}\n\nfunction _getKeyResolver(key: string) {\n  const keys = _splitKey(key);\n  return obj => {\n    for (const k of keys) {\n      if (k === '') {\n        // For backward compatibility:\n        // Chart.helpers.core resolveObjectKey should break at empty key\n        break;\n      }\n      obj = obj && obj[k];\n    }\n    return obj;\n  };\n}\n\nexport function resolveObjectKey(obj: AnyObject, key: string): any {\n  const resolver = keyResolvers[key] || (keyResolvers[key] = _getKeyResolver(key));\n  return resolver(obj);\n}\n\n/**\n * @private\n */\nexport function _capitalize(str: string) {\n  return str.charAt(0).toUpperCase() + str.slice(1);\n}\n\n\nexport const defined = (value: unknown) => typeof value !== 'undefined';\n\nexport const isFunction = (value: unknown): value is (...args: any[]) => any => typeof value === 'function';\n\n// Adapted from https://stackoverflow.com/questions/31128855/comparing-ecma6-sets-for-equality#31129384\nexport const setsEqual = <T>(a: Set<T>, b: Set<T>) => {\n  if (a.size !== b.size) {\n    return false;\n  }\n\n  for (const item of a) {\n    if (!b.has(item)) {\n      return false;\n    }\n  }\n\n  return true;\n};\n\n/**\n * @param e - The event\n * @private\n */\nexport function _isClickEvent(e: ChartEvent) {\n  return e.type === 'mouseup' || e.type === 'click' || e.type === 'contextmenu';\n}\n", "import type {Point} from '../types/geometric.js';\nimport {isFinite as isFiniteNumber} from './helpers.core.js';\n\n/**\n * @alias Chart.helpers.math\n * @namespace\n */\n\nexport const PI = Math.PI;\nexport const TAU = 2 * PI;\nexport const PITAU = TAU + PI;\nexport const INFINITY = Number.POSITIVE_INFINITY;\nexport const RAD_PER_DEG = PI / 180;\nexport const HALF_PI = PI / 2;\nexport const QUARTER_PI = PI / 4;\nexport const TWO_THIRDS_PI = PI * 2 / 3;\n\nexport const log10 = Math.log10;\nexport const sign = Math.sign;\n\nexport function almostEquals(x: number, y: number, epsilon: number) {\n  return Math.abs(x - y) < epsilon;\n}\n\n/**\n * Implementation of the nice number algorithm used in determining where axis labels will go\n */\nexport function niceNum(range: number) {\n  const roundedRange = Math.round(range);\n  range = almostEquals(range, roundedRange, range / 1000) ? roundedRange : range;\n  const niceRange = Math.pow(10, Math.floor(log10(range)));\n  const fraction = range / niceRange;\n  const niceFraction = fraction <= 1 ? 1 : fraction <= 2 ? 2 : fraction <= 5 ? 5 : 10;\n  return niceFraction * niceRange;\n}\n\n/**\n * Returns an array of factors sorted from 1 to sqrt(value)\n * @private\n */\nexport function _factorize(value: number) {\n  const result: number[] = [];\n  const sqrt = Math.sqrt(value);\n  let i: number;\n\n  for (i = 1; i < sqrt; i++) {\n    if (value % i === 0) {\n      result.push(i);\n      result.push(value / i);\n    }\n  }\n  if (sqrt === (sqrt | 0)) { // if value is a square number\n    result.push(sqrt);\n  }\n\n  result.sort((a, b) => a - b).pop();\n  return result;\n}\n\nexport function isNumber(n: unknown): n is number {\n  return !isNaN(parseFloat(n as string)) && isFinite(n as number);\n}\n\nexport function almostWhole(x: number, epsilon: number) {\n  const rounded = Math.round(x);\n  return ((rounded - epsilon) <= x) && ((rounded + epsilon) >= x);\n}\n\n/**\n * @private\n */\nexport function _setMinAndMaxByKey(\n  array: Record<string, number>[],\n  target: { min: number, max: number },\n  property: string\n) {\n  let i: number, ilen: number, value: number;\n\n  for (i = 0, ilen = array.length; i < ilen; i++) {\n    value = array[i][property];\n    if (!isNaN(value)) {\n      target.min = Math.min(target.min, value);\n      target.max = Math.max(target.max, value);\n    }\n  }\n}\n\nexport function toRadians(degrees: number) {\n  return degrees * (PI / 180);\n}\n\nexport function toDegrees(radians: number) {\n  return radians * (180 / PI);\n}\n\n/**\n * Returns the number of decimal places\n * i.e. the number of digits after the decimal point, of the value of this Number.\n * @param x - A number.\n * @returns The number of decimal places.\n * @private\n */\nexport function _decimalPlaces(x: number) {\n  if (!isFiniteNumber(x)) {\n    return;\n  }\n  let e = 1;\n  let p = 0;\n  while (Math.round(x * e) / e !== x) {\n    e *= 10;\n    p++;\n  }\n  return p;\n}\n\n// Gets the angle from vertical upright to the point about a centre.\nexport function getAngleFromPoint(\n  centrePoint: Point,\n  anglePoint: Point\n) {\n  const distanceFromXCenter = anglePoint.x - centrePoint.x;\n  const distanceFromYCenter = anglePoint.y - centrePoint.y;\n  const radialDistanceFromCenter = Math.sqrt(distanceFromXCenter * distanceFromXCenter + distanceFromYCenter * distanceFromYCenter);\n\n  let angle = Math.atan2(distanceFromYCenter, distanceFromXCenter);\n\n  if (angle < (-0.5 * PI)) {\n    angle += TAU; // make sure the returned angle is in the range of (-PI/2, 3PI/2]\n  }\n\n  return {\n    angle,\n    distance: radialDistanceFromCenter\n  };\n}\n\nexport function distanceBetweenPoints(pt1: Point, pt2: Point) {\n  return Math.sqrt(Math.pow(pt2.x - pt1.x, 2) + Math.pow(pt2.y - pt1.y, 2));\n}\n\n/**\n * Shortest distance between angles, in either direction.\n * @private\n */\nexport function _angleDiff(a: number, b: number) {\n  return (a - b + PITAU) % TAU - PI;\n}\n\n/**\n * Normalize angle to be between 0 and 2*PI\n * @private\n */\nexport function _normalizeAngle(a: number) {\n  return (a % TAU + TAU) % TAU;\n}\n\n/**\n * @private\n */\nexport function _angleBetween(angle: number, start: number, end: number, sameAngleIsFullCircle?: boolean) {\n  const a = _normalizeAngle(angle);\n  const s = _normalizeAngle(start);\n  const e = _normalizeAngle(end);\n  const angleToStart = _normalizeAngle(s - a);\n  const angleToEnd = _normalizeAngle(e - a);\n  const startToAngle = _normalizeAngle(a - s);\n  const endToAngle = _normalizeAngle(a - e);\n  return a === s || a === e || (sameAngleIsFullCircle && s === e)\n    || (angleToStart > angleToEnd && startToAngle < endToAngle);\n}\n\n/**\n * Limit `value` between `min` and `max`\n * @param value\n * @param min\n * @param max\n * @private\n */\nexport function _limitValue(value: number, min: number, max: number) {\n  return Math.max(min, Math.min(max, value));\n}\n\n/**\n * @param {number} value\n * @private\n */\nexport function _int16Range(value: number) {\n  return _limitValue(value, -32768, 32767);\n}\n\n/**\n * @param value\n * @param start\n * @param end\n * @param [epsilon]\n * @private\n */\nexport function _isBetween(value: number, start: number, end: number, epsilon = 1e-6) {\n  return value >= Math.min(start, end) - epsilon && value <= Math.max(start, end) + epsilon;\n}\n", "import {_capitalize} from './helpers.core.js';\n\n/**\n * Binary search\n * @param table - the table search. must be sorted!\n * @param value - value to find\n * @param cmp\n * @private\n */\nexport function _lookup(\n  table: number[],\n  value: number,\n  cmp?: (value: number) => boolean\n): {lo: number, hi: number};\nexport function _lookup<T>(\n  table: T[],\n  value: number,\n  cmp: (value: number) => boolean\n): {lo: number, hi: number};\nexport function _lookup(\n  table: unknown[],\n  value: number,\n  cmp?: (value: number) => boolean\n) {\n  cmp = cmp || ((index) => table[index] < value);\n  let hi = table.length - 1;\n  let lo = 0;\n  let mid: number;\n\n  while (hi - lo > 1) {\n    mid = (lo + hi) >> 1;\n    if (cmp(mid)) {\n      lo = mid;\n    } else {\n      hi = mid;\n    }\n  }\n\n  return {lo, hi};\n}\n\n/**\n * Binary search\n * @param table - the table search. must be sorted!\n * @param key - property name for the value in each entry\n * @param value - value to find\n * @param last - lookup last index\n * @private\n */\nexport const _lookupByKey = (\n  table: Record<string, number>[],\n  key: string,\n  value: number,\n  last?: boolean\n) =>\n  _lookup(table, value, last\n    ? index => {\n      const ti = table[index][key];\n      return ti < value || ti === value && table[index + 1][key] === value;\n    }\n    : index => table[index][key] < value);\n\n/**\n * Reverse binary search\n * @param table - the table search. must be sorted!\n * @param key - property name for the value in each entry\n * @param value - value to find\n * @private\n */\nexport const _rlookupByKey = (\n  table: Record<string, number>[],\n  key: string,\n  value: number\n) =>\n  _lookup(table, value, index => table[index][key] >= value);\n\n/**\n * Return subset of `values` between `min` and `max` inclusive.\n * Values are assumed to be in sorted order.\n * @param values - sorted array of values\n * @param min - min value\n * @param max - max value\n */\nexport function _filterBetween(values: number[], min: number, max: number) {\n  let start = 0;\n  let end = values.length;\n\n  while (start < end && values[start] < min) {\n    start++;\n  }\n  while (end > start && values[end - 1] > max) {\n    end--;\n  }\n\n  return start > 0 || end < values.length\n    ? values.slice(start, end)\n    : values;\n}\n\nconst arrayEvents = ['push', 'pop', 'shift', 'splice', 'unshift'] as const;\n\nexport interface ArrayListener<T> {\n  _onDataPush?(...item: T[]): void;\n  _onDataPop?(): void;\n  _onDataShift?(): void;\n  _onDataSplice?(index: number, deleteCount: number, ...items: T[]): void;\n  _onDataUnshift?(...item: T[]): void;\n}\n\n/**\n * Hooks the array methods that add or remove values ('push', pop', 'shift', 'splice',\n * 'unshift') and notify the listener AFTER the array has been altered. Listeners are\n * called on the '_onData*' callbacks (e.g. _onDataPush, etc.) with same arguments.\n */\nexport function listenArrayEvents<T>(array: T[], listener: ArrayListener<T>): void;\nexport function listenArrayEvents(array, listener) {\n  if (array._chartjs) {\n    array._chartjs.listeners.push(listener);\n    return;\n  }\n\n  Object.defineProperty(array, '_chartjs', {\n    configurable: true,\n    enumerable: false,\n    value: {\n      listeners: [listener]\n    }\n  });\n\n  arrayEvents.forEach((key) => {\n    const method = '_onData' + _capitalize(key);\n    const base = array[key];\n\n    Object.defineProperty(array, key, {\n      configurable: true,\n      enumerable: false,\n      value(...args) {\n        const res = base.apply(this, args);\n\n        array._chartjs.listeners.forEach((object) => {\n          if (typeof object[method] === 'function') {\n            object[method](...args);\n          }\n        });\n\n        return res;\n      }\n    });\n  });\n}\n\n\n/**\n * Removes the given array event listener and cleanup extra attached properties (such as\n * the _chartjs stub and overridden methods) if array doesn't have any more listeners.\n */\nexport function unlistenArrayEvents<T>(array: T[], listener: ArrayListener<T>): void;\nexport function unlistenArrayEvents(array, listener) {\n  const stub = array._chartjs;\n  if (!stub) {\n    return;\n  }\n\n  const listeners = stub.listeners;\n  const index = listeners.indexOf(listener);\n  if (index !== -1) {\n    listeners.splice(index, 1);\n  }\n\n  if (listeners.length > 0) {\n    return;\n  }\n\n  arrayEvents.forEach((key) => {\n    delete array[key];\n  });\n\n  delete array._chartjs;\n}\n\n/**\n * @param items\n */\nexport function _arrayUnique<T>(items: T[]) {\n  const set = new Set<T>(items);\n\n  if (set.size === items.length) {\n    return items;\n  }\n\n  return Array.from(set);\n}\n", "import type {ChartMeta, PointElement} from '../types/index.js';\n\nimport {_limitValue} from './helpers.math.js';\nimport {_lookupByKey} from './helpers.collection.js';\n\nexport function fontString(pixelSize: number, fontStyle: string, fontFamily: string) {\n  return fontStyle + ' ' + pixelSize + 'px ' + fontFamily;\n}\n\n/**\n* Request animation polyfill\n*/\nexport const requestAnimFrame = (function() {\n  if (typeof window === 'undefined') {\n    return function(callback) {\n      return callback();\n    };\n  }\n  return window.requestAnimationFrame;\n}());\n\n/**\n * Throttles calling `fn` once per animation frame\n * Latest arguments are used on the actual call\n */\nexport function throttled<TArgs extends Array<any>>(\n  fn: (...args: TArgs) => void,\n  thisArg: any,\n) {\n  let argsToUse = [] as TArgs;\n  let ticking = false;\n\n  return function(...args: TArgs) {\n    // Save the args for use later\n    argsToUse = args;\n    if (!ticking) {\n      ticking = true;\n      requestAnimFrame.call(window, () => {\n        ticking = false;\n        fn.apply(thisArg, argsToUse);\n      });\n    }\n  };\n}\n\n/**\n * Debounces calling `fn` for `delay` ms\n */\nexport function debounce<TArgs extends Array<any>>(fn: (...args: TArgs) => void, delay: number) {\n  let timeout;\n  return function(...args: TArgs) {\n    if (delay) {\n      clearTimeout(timeout);\n      timeout = setTimeout(fn, delay, args);\n    } else {\n      fn.apply(this, args);\n    }\n    return delay;\n  };\n}\n\n/**\n * Converts 'start' to 'left', 'end' to 'right' and others to 'center'\n * @private\n */\nexport const _toLeftRightCenter = (align: 'start' | 'end' | 'center') => align === 'start' ? 'left' : align === 'end' ? 'right' : 'center';\n\n/**\n * Returns `start`, `end` or `(start + end) / 2` depending on `align`. Defaults to `center`\n * @private\n */\nexport const _alignStartEnd = (align: 'start' | 'end' | 'center', start: number, end: number) => align === 'start' ? start : align === 'end' ? end : (start + end) / 2;\n\n/**\n * Returns `left`, `right` or `(left + right) / 2` depending on `align`. Defaults to `left`\n * @private\n */\nexport const _textX = (align: 'left' | 'right' | 'center', left: number, right: number, rtl: boolean) => {\n  const check = rtl ? 'left' : 'right';\n  return align === check ? right : align === 'center' ? (left + right) / 2 : left;\n};\n\n/**\n * Return start and count of visible points.\n * @private\n */\nexport function _getStartAndCountOfVisiblePoints(meta: ChartMeta<'line' | 'scatter'>, points: PointElement[], animationsDisabled: boolean) {\n  const pointCount = points.length;\n\n  let start = 0;\n  let count = pointCount;\n\n  if (meta._sorted) {\n    const {iScale, _parsed} = meta;\n    const axis = iScale.axis;\n    const {min, max, minDefined, maxDefined} = iScale.getUserBounds();\n\n    if (minDefined) {\n      start = _limitValue(Math.min(\n        // @ts-expect-error Need to type _parsed\n        _lookupByKey(_parsed, iScale.axis, min).lo,\n        // @ts-expect-error Need to fix types on _lookupByKey\n        animationsDisabled ? pointCount : _lookupByKey(points, axis, iScale.getPixelForValue(min)).lo),\n      0, pointCount - 1);\n    }\n    if (maxDefined) {\n      count = _limitValue(Math.max(\n        // @ts-expect-error Need to type _parsed\n        _lookupByKey(_parsed, iScale.axis, max, true).hi + 1,\n        // @ts-expect-error Need to fix types on _lookupByKey\n        animationsDisabled ? 0 : _lookupByKey(points, axis, iScale.getPixelForValue(max), true).hi + 1),\n      start, pointCount) - start;\n    } else {\n      count = pointCount - start;\n    }\n  }\n\n  return {start, count};\n}\n\n/**\n * Checks if the scale ranges have changed.\n * @param {object} meta - dataset meta.\n * @returns {boolean}\n * @private\n */\nexport function _scaleRangesChanged(meta) {\n  const {xScale, yScale, _scaleRanges} = meta;\n  const newRanges = {\n    xmin: xScale.min,\n    xmax: xScale.max,\n    ymin: yScale.min,\n    ymax: yScale.max\n  };\n  if (!_scaleRanges) {\n    meta._scaleRanges = newRanges;\n    return true;\n  }\n  const changed = _scaleRanges.xmin !== xScale.min\n\t\t|| _scaleRanges.xmax !== xScale.max\n\t\t|| _scaleRanges.ymin !== yScale.min\n\t\t|| _scaleRanges.ymax !== yScale.max;\n\n  Object.assign(_scaleRanges, newRanges);\n  return changed;\n}\n", "import {PI, TAU, HALF_PI} from './helpers.math.js';\n\nconst atEdge = (t: number) => t === 0 || t === 1;\nconst elasticIn = (t: number, s: number, p: number) => -(Math.pow(2, 10 * (t -= 1)) * Math.sin((t - s) * TAU / p));\nconst elasticOut = (t: number, s: number, p: number) => Math.pow(2, -10 * t) * Math.sin((t - s) * TAU / p) + 1;\n\n/**\n * Easing functions adapted from <PERSON>'s easing equations.\n * @namespace Chart.helpers.easing.effects\n * @see http://www.robertpenner.com/easing/\n */\nconst effects = {\n  linear: (t: number) => t,\n\n  easeInQuad: (t: number) => t * t,\n\n  easeOutQuad: (t: number) => -t * (t - 2),\n\n  easeInOutQuad: (t: number) => ((t /= 0.5) < 1)\n    ? 0.5 * t * t\n    : -0.5 * ((--t) * (t - 2) - 1),\n\n  easeInCubic: (t: number) => t * t * t,\n\n  easeOutCubic: (t: number) => (t -= 1) * t * t + 1,\n\n  easeInOutCubic: (t: number) => ((t /= 0.5) < 1)\n    ? 0.5 * t * t * t\n    : 0.5 * ((t -= 2) * t * t + 2),\n\n  easeInQuart: (t: number) => t * t * t * t,\n\n  easeOutQuart: (t: number) => -((t -= 1) * t * t * t - 1),\n\n  easeInOutQuart: (t: number) => ((t /= 0.5) < 1)\n    ? 0.5 * t * t * t * t\n    : -0.5 * ((t -= 2) * t * t * t - 2),\n\n  easeInQuint: (t: number) => t * t * t * t * t,\n\n  easeOutQuint: (t: number) => (t -= 1) * t * t * t * t + 1,\n\n  easeInOutQuint: (t: number) => ((t /= 0.5) < 1)\n    ? 0.5 * t * t * t * t * t\n    : 0.5 * ((t -= 2) * t * t * t * t + 2),\n\n  easeInSine: (t: number) => -Math.cos(t * HALF_PI) + 1,\n\n  easeOutSine: (t: number) => Math.sin(t * HALF_PI),\n\n  easeInOutSine: (t: number) => -0.5 * (Math.cos(PI * t) - 1),\n\n  easeInExpo: (t: number) => (t === 0) ? 0 : Math.pow(2, 10 * (t - 1)),\n\n  easeOutExpo: (t: number) => (t === 1) ? 1 : -Math.pow(2, -10 * t) + 1,\n\n  easeInOutExpo: (t: number) => atEdge(t) ? t : t < 0.5\n    ? 0.5 * Math.pow(2, 10 * (t * 2 - 1))\n    : 0.5 * (-Math.pow(2, -10 * (t * 2 - 1)) + 2),\n\n  easeInCirc: (t: number) => (t >= 1) ? t : -(Math.sqrt(1 - t * t) - 1),\n\n  easeOutCirc: (t: number) => Math.sqrt(1 - (t -= 1) * t),\n\n  easeInOutCirc: (t: number) => ((t /= 0.5) < 1)\n    ? -0.5 * (Math.sqrt(1 - t * t) - 1)\n    : 0.5 * (Math.sqrt(1 - (t -= 2) * t) + 1),\n\n  easeInElastic: (t: number) => atEdge(t) ? t : elasticIn(t, 0.075, 0.3),\n\n  easeOutElastic: (t: number) => atEdge(t) ? t : elasticOut(t, 0.075, 0.3),\n\n  easeInOutElastic(t: number) {\n    const s = 0.1125;\n    const p = 0.45;\n    return atEdge(t) ? t :\n      t < 0.5\n        ? 0.5 * elasticIn(t * 2, s, p)\n        : 0.5 + 0.5 * elasticOut(t * 2 - 1, s, p);\n  },\n\n  easeInBack(t: number) {\n    const s = 1.70158;\n    return t * t * ((s + 1) * t - s);\n  },\n\n  easeOutBack(t: number) {\n    const s = 1.70158;\n    return (t -= 1) * t * ((s + 1) * t + s) + 1;\n  },\n\n  easeInOutBack(t: number) {\n    let s = 1.70158;\n    if ((t /= 0.5) < 1) {\n      return 0.5 * (t * t * (((s *= (1.525)) + 1) * t - s));\n    }\n    return 0.5 * ((t -= 2) * t * (((s *= (1.525)) + 1) * t + s) + 2);\n  },\n\n  easeInBounce: (t: number) => 1 - effects.easeOutBounce(1 - t),\n\n  easeOutBounce(t: number) {\n    const m = 7.5625;\n    const d = 2.75;\n    if (t < (1 / d)) {\n      return m * t * t;\n    }\n    if (t < (2 / d)) {\n      return m * (t -= (1.5 / d)) * t + 0.75;\n    }\n    if (t < (2.5 / d)) {\n      return m * (t -= (2.25 / d)) * t + 0.9375;\n    }\n    return m * (t -= (2.625 / d)) * t + 0.984375;\n  },\n\n  easeInOutBounce: (t: number) => (t < 0.5)\n    ? effects.easeInBounce(t * 2) * 0.5\n    : effects.easeOutBounce(t * 2 - 1) * 0.5 + 0.5,\n} as const;\n\nexport type EasingFunction = keyof typeof effects\n\nexport default effects;\n", "import {Color} from '@kurkle/color';\n\nexport function isPatternOrGradient(value: unknown): value is CanvasPattern | CanvasGradient {\n  if (value && typeof value === 'object') {\n    const type = value.toString();\n    return type === '[object CanvasPattern]' || type === '[object CanvasGradient]';\n  }\n\n  return false;\n}\n\nexport function color(value: CanvasGradient): CanvasGradient;\nexport function color(value: CanvasPattern): CanvasPattern;\nexport function color(\n  value:\n  | string\n  | { r: number; g: number; b: number; a: number }\n  | [number, number, number]\n  | [number, number, number, number]\n): Color;\nexport function color(value) {\n  return isPatternOrGradient(value) ? value : new Color(value);\n}\n\nexport function getHoverColor(value: CanvasGradient): CanvasGradient;\nexport function getHoverColor(value: CanvasPattern): CanvasPattern;\nexport function getHoverColor(value: string): string;\nexport function getHoverColor(value) {\n  return isPatternOrGradient(value)\n    ? value\n    : new Color(value).saturate(0.5).darken(0.1).hexString();\n}\n", "const numbers = ['x', 'y', 'borderWidth', 'radius', 'tension'];\nconst colors = ['color', 'borderColor', 'backgroundColor'];\n\nexport function applyAnimationsDefaults(defaults) {\n  defaults.set('animation', {\n    delay: undefined,\n    duration: 1000,\n    easing: 'easeOutQuart',\n    fn: undefined,\n    from: undefined,\n    loop: undefined,\n    to: undefined,\n    type: undefined,\n  });\n\n  defaults.describe('animation', {\n    _fallback: false,\n    _indexable: false,\n    _scriptable: (name) => name !== 'onProgress' && name !== 'onComplete' && name !== 'fn',\n  });\n\n  defaults.set('animations', {\n    colors: {\n      type: 'color',\n      properties: colors\n    },\n    numbers: {\n      type: 'number',\n      properties: numbers\n    },\n  });\n\n  defaults.describe('animations', {\n    _fallback: 'animation',\n  });\n\n  defaults.set('transitions', {\n    active: {\n      animation: {\n        duration: 400\n      }\n    },\n    resize: {\n      animation: {\n        duration: 0\n      }\n    },\n    show: {\n      animations: {\n        colors: {\n          from: 'transparent'\n        },\n        visible: {\n          type: 'boolean',\n          duration: 0 // show immediately\n        },\n      }\n    },\n    hide: {\n      animations: {\n        colors: {\n          to: 'transparent'\n        },\n        visible: {\n          type: 'boolean',\n          easing: 'linear',\n          fn: v => v | 0 // for keeping the dataset visible all the way through the animation\n        },\n      }\n    }\n  });\n}\n", "export function applyLayoutsDefaults(defaults) {\n  defaults.set('layout', {\n    autoPadding: true,\n    padding: {\n      top: 0,\n      right: 0,\n      bottom: 0,\n      left: 0\n    }\n  });\n}\n", "\nconst intlCache = new Map<string, Intl.NumberFormat>();\n\nfunction getNumberFormat(locale: string, options?: Intl.NumberFormatOptions) {\n  options = options || {};\n  const cacheKey = locale + JSON.stringify(options);\n  let formatter = intlCache.get(cacheKey);\n  if (!formatter) {\n    formatter = new Intl.NumberFormat(locale, options);\n    intlCache.set(cacheKey, formatter);\n  }\n  return formatter;\n}\n\nexport function formatNumber(num: number, locale: string, options?: Intl.NumberFormatOptions) {\n  return getNumberFormat(locale, options).format(num);\n}\n", "import {isArray} from '../helpers/helpers.core.js';\nimport {formatNumber} from '../helpers/helpers.intl.js';\nimport {log10} from '../helpers/helpers.math.js';\n\n/**\n * Namespace to hold formatters for different types of ticks\n * @namespace Chart.Ticks.formatters\n */\nconst formatters = {\n  /**\n   * Formatter for value labels\n   * @method Chart.Ticks.formatters.values\n   * @param value the value to display\n   * @return {string|string[]} the label to display\n   */\n  values(value) {\n    return isArray(value) ? /** @type {string[]} */ (value) : '' + value;\n  },\n\n  /**\n   * Formatter for numeric ticks\n   * @method Chart.Ticks.formatters.numeric\n   * @param tickValue {number} the value to be formatted\n   * @param index {number} the position of the tickValue parameter in the ticks array\n   * @param ticks {object[]} the list of ticks being converted\n   * @return {string} string representation of the tickValue parameter\n   */\n  numeric(tickValue, index, ticks) {\n    if (tickValue === 0) {\n      return '0'; // never show decimal places for 0\n    }\n\n    const locale = this.chart.options.locale;\n    let notation;\n    let delta = tickValue; // This is used when there are less than 2 ticks as the tick interval.\n\n    if (ticks.length > 1) {\n      // all ticks are small or there huge numbers; use scientific notation\n      const maxTick = Math.max(Math.abs(ticks[0].value), Math.abs(ticks[ticks.length - 1].value));\n      if (maxTick < 1e-4 || maxTick > 1e+15) {\n        notation = 'scientific';\n      }\n\n      delta = calculateDelta(tickValue, ticks);\n    }\n\n    const logDelta = log10(Math.abs(delta));\n\n    // When datasets have values approaching Number.MAX_VALUE, the tick calculations might result in\n    // infinity and eventually NaN. Passing NaN for minimumFractionDigits or maximumFractionDigits\n    // will make the number formatter throw. So instead we check for isNaN and use a fallback value.\n    //\n    // toFixed has a max of 20 decimal places\n    const numDecimal = isNaN(logDelta) ? 1 : Math.max(Math.min(-1 * Math.floor(logDelta), 20), 0);\n\n    const options = {notation, minimumFractionDigits: numDecimal, maximumFractionDigits: numDecimal};\n    Object.assign(options, this.options.ticks.format);\n\n    return formatNumber(tickValue, locale, options);\n  },\n\n\n  /**\n   * Formatter for logarithmic ticks\n   * @method Chart.Ticks.formatters.logarithmic\n   * @param tickValue {number} the value to be formatted\n   * @param index {number} the position of the tickValue parameter in the ticks array\n   * @param ticks {object[]} the list of ticks being converted\n   * @return {string} string representation of the tickValue parameter\n   */\n  logarithmic(tickValue, index, ticks) {\n    if (tickValue === 0) {\n      return '0';\n    }\n    const remain = ticks[index].significand || (tickValue / (Math.pow(10, Math.floor(log10(tickValue)))));\n    if ([1, 2, 3, 5, 10, 15].includes(remain) || index > 0.8 * ticks.length) {\n      return formatters.numeric.call(this, tickValue, index, ticks);\n    }\n    return '';\n  }\n\n};\n\n\nfunction calculateDelta(tickValue, ticks) {\n  // Figure out how many digits to show\n  // The space between the first two ticks might be smaller than normal spacing\n  let delta = ticks.length > 3 ? ticks[2].value - ticks[1].value : ticks[1].value - ticks[0].value;\n\n  // If we have a number like 2.5 as the delta, figure out how many decimal places we need\n  if (Math.abs(delta) >= 1 && tickValue !== Math.floor(tickValue)) {\n    // not an integer\n    delta = tickValue - Math.floor(tickValue);\n  }\n  return delta;\n}\n\n/**\n * Namespace to hold static tick generation functions\n * @namespace Chart.Ticks\n */\nexport default {formatters};\n", "import Ticks from './core.ticks.js';\n\nexport function applyScaleDefaults(defaults) {\n  defaults.set('scale', {\n    display: true,\n    offset: false,\n    reverse: false,\n    beginAtZero: false,\n\n    /**\n     * Scale boundary strategy (bypassed by min/max time options)\n     * - `data`: make sure data are fully visible, ticks outside are removed\n     * - `ticks`: make sure ticks are fully visible, data outside are truncated\n     * @see https://github.com/chartjs/Chart.js/pull/4556\n     * @since 3.0.0\n     */\n    bounds: 'ticks',\n\n    /**\n     * Addition grace added to max and reduced from min data value.\n     * @since 3.0.0\n     */\n    grace: 0,\n\n    // grid line settings\n    grid: {\n      display: true,\n      lineWidth: 1,\n      drawOnChartArea: true,\n      drawTicks: true,\n      tickLength: 8,\n      tickWidth: (_ctx, options) => options.lineWidth,\n      tickColor: (_ctx, options) => options.color,\n      offset: false,\n    },\n\n    border: {\n      display: true,\n      dash: [],\n      dashOffset: 0.0,\n      width: 1\n    },\n\n    // scale title\n    title: {\n      // display property\n      display: false,\n\n      // actual label\n      text: '',\n\n      // top/bottom padding\n      padding: {\n        top: 4,\n        bottom: 4\n      }\n    },\n\n    // label settings\n    ticks: {\n      minRotation: 0,\n      maxRotation: 50,\n      mirror: false,\n      textStrokeWidth: 0,\n      textStrokeColor: '',\n      padding: 3,\n      display: true,\n      autoSkip: true,\n      autoSkipPadding: 3,\n      labelOffset: 0,\n      // We pass through arrays to be rendered as multiline labels, we convert Others to strings here.\n      callback: Ticks.formatters.values,\n      minor: {},\n      major: {},\n      align: 'center',\n      crossAlign: 'near',\n\n      showLabelBackdrop: false,\n      backdropColor: 'rgba(255, 255, 255, 0.75)',\n      backdropPadding: 2,\n    }\n  });\n\n  defaults.route('scale.ticks', 'color', '', 'color');\n  defaults.route('scale.grid', 'color', '', 'borderColor');\n  defaults.route('scale.border', 'color', '', 'borderColor');\n  defaults.route('scale.title', 'color', '', 'color');\n\n  defaults.describe('scale', {\n    _fallback: false,\n    _scriptable: (name) => !name.startsWith('before') && !name.startsWith('after') && name !== 'callback' && name !== 'parser',\n    _indexable: (name) => name !== 'borderDash' && name !== 'tickBorderDash' && name !== 'dash',\n  });\n\n  defaults.describe('scales', {\n    _fallback: 'scale',\n  });\n\n  defaults.describe('scale.ticks', {\n    _scriptable: (name) => name !== 'backdropPadding' && name !== 'callback',\n    _indexable: (name) => name !== 'backdropPadding',\n  });\n}\n", "import {getHoverColor} from '../helpers/helpers.color.js';\nimport {isObject, merge, valueOrDefault} from '../helpers/helpers.core.js';\nimport {applyAnimationsDefaults} from './core.animations.defaults.js';\nimport {applyLayoutsDefaults} from './core.layouts.defaults.js';\nimport {applyScaleDefaults} from './core.scale.defaults.js';\n\nexport const overrides = Object.create(null);\nexport const descriptors = Object.create(null);\n\n/**\n * @param {object} node\n * @param {string} key\n * @return {object}\n */\nfunction getScope(node, key) {\n  if (!key) {\n    return node;\n  }\n  const keys = key.split('.');\n  for (let i = 0, n = keys.length; i < n; ++i) {\n    const k = keys[i];\n    node = node[k] || (node[k] = Object.create(null));\n  }\n  return node;\n}\n\nfunction set(root, scope, values) {\n  if (typeof scope === 'string') {\n    return merge(getScope(root, scope), values);\n  }\n  return merge(getScope(root, ''), scope);\n}\n\n/**\n * Please use the module's default export which provides a singleton instance\n * Note: class is exported for typedoc\n */\nexport class Defaults {\n  constructor(_descriptors, _appliers) {\n    this.animation = undefined;\n    this.backgroundColor = 'rgba(0,0,0,0.1)';\n    this.borderColor = 'rgba(0,0,0,0.1)';\n    this.color = '#666';\n    this.datasets = {};\n    this.devicePixelRatio = (context) => context.chart.platform.getDevicePixelRatio();\n    this.elements = {};\n    this.events = [\n      'mousemove',\n      'mouseout',\n      'click',\n      'touchstart',\n      'touchmove'\n    ];\n    this.font = {\n      family: \"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif\",\n      size: 12,\n      style: 'normal',\n      lineHeight: 1.2,\n      weight: null\n    };\n    this.hover = {};\n    this.hoverBackgroundColor = (ctx, options) => getHoverColor(options.backgroundColor);\n    this.hoverBorderColor = (ctx, options) => getHoverColor(options.borderColor);\n    this.hoverColor = (ctx, options) => getHoverColor(options.color);\n    this.indexAxis = 'x';\n    this.interaction = {\n      mode: 'nearest',\n      intersect: true,\n      includeInvisible: false\n    };\n    this.maintainAspectRatio = true;\n    this.onHover = null;\n    this.onClick = null;\n    this.parsing = true;\n    this.plugins = {};\n    this.responsive = true;\n    this.scale = undefined;\n    this.scales = {};\n    this.showLine = true;\n    this.drawActiveElementsOnTop = true;\n\n    this.describe(_descriptors);\n    this.apply(_appliers);\n  }\n\n  /**\n\t * @param {string|object} scope\n\t * @param {object} [values]\n\t */\n  set(scope, values) {\n    return set(this, scope, values);\n  }\n\n  /**\n\t * @param {string} scope\n\t */\n  get(scope) {\n    return getScope(this, scope);\n  }\n\n  /**\n\t * @param {string|object} scope\n\t * @param {object} [values]\n\t */\n  describe(scope, values) {\n    return set(descriptors, scope, values);\n  }\n\n  override(scope, values) {\n    return set(overrides, scope, values);\n  }\n\n  /**\n\t * Routes the named defaults to fallback to another scope/name.\n\t * This routing is useful when those target values, like defaults.color, are changed runtime.\n\t * If the values would be copied, the runtime change would not take effect. By routing, the\n\t * fallback is evaluated at each access, so its always up to date.\n\t *\n\t * Example:\n\t *\n\t * \tdefaults.route('elements.arc', 'backgroundColor', '', 'color')\n\t *   - reads the backgroundColor from defaults.color when undefined locally\n\t *\n\t * @param {string} scope Scope this route applies to.\n\t * @param {string} name Property name that should be routed to different namespace when not defined here.\n\t * @param {string} targetScope The namespace where those properties should be routed to.\n\t * Empty string ('') is the root of defaults.\n\t * @param {string} targetName The target name in the target scope the property should be routed to.\n\t */\n  route(scope, name, targetScope, targetName) {\n    const scopeObject = getScope(this, scope);\n    const targetScopeObject = getScope(this, targetScope);\n    const privateName = '_' + name;\n\n    Object.defineProperties(scopeObject, {\n      // A private property is defined to hold the actual value, when this property is set in its scope (set in the setter)\n      [privateName]: {\n        value: scopeObject[name],\n        writable: true\n      },\n      // The actual property is defined as getter/setter so we can do the routing when value is not locally set.\n      [name]: {\n        enumerable: true,\n        get() {\n          const local = this[privateName];\n          const target = targetScopeObject[targetName];\n          if (isObject(local)) {\n            return Object.assign({}, target, local);\n          }\n          return valueOrDefault(local, target);\n        },\n        set(value) {\n          this[privateName] = value;\n        }\n      }\n    });\n  }\n\n  apply(appliers) {\n    appliers.forEach((apply) => apply(this));\n  }\n}\n\n// singleton instance\nexport default /* #__PURE__ */ new Defaults({\n  _scriptable: (name) => !name.startsWith('on'),\n  _indexable: (name) => name !== 'events',\n  hover: {\n    _fallback: 'interaction'\n  },\n  interaction: {\n    _scriptable: false,\n    _indexable: false,\n  }\n}, [applyAnimationsDefaults, applyLayoutsDefaults, applyScaleDefaults]);\n", "import type {\n  Chart,\n  Point,\n  FontSpec,\n  CanvasFontSpec,\n  PointStyle,\n  RenderTextOpts,\n  BackdropOptions\n} from '../types/index.js';\nimport type {\n  TRBL,\n  SplinePoint,\n  RoundedRect,\n  TRBLCorners\n} from '../types/geometric.js';\nimport {isArray, isNullOrUndef} from './helpers.core.js';\nimport {PI, TAU, HALF_PI, QUARTER_PI, TWO_THIRDS_PI, RAD_PER_DEG} from './helpers.math.js';\n\n/**\n * Converts the given font object into a CSS font string.\n * @param font - A font object.\n * @return The CSS font string. See https://developer.mozilla.org/en-US/docs/Web/CSS/font\n * @private\n */\nexport function toFontString(font: FontSpec) {\n  if (!font || isNullOrUndef(font.size) || isNullOrUndef(font.family)) {\n    return null;\n  }\n\n  return (font.style ? font.style + ' ' : '')\n\t\t+ (font.weight ? font.weight + ' ' : '')\n\t\t+ font.size + 'px '\n\t\t+ font.family;\n}\n\n/**\n * @private\n */\nexport function _measureText(\n  ctx: CanvasRenderingContext2D,\n  data: Record<string, number>,\n  gc: string[],\n  longest: number,\n  string: string\n) {\n  let textWidth = data[string];\n  if (!textWidth) {\n    textWidth = data[string] = ctx.measureText(string).width;\n    gc.push(string);\n  }\n  if (textWidth > longest) {\n    longest = textWidth;\n  }\n  return longest;\n}\n\ntype Thing = string | undefined | null\ntype Things = (Thing | Thing[])[]\n\n/**\n * @private\n */\n// eslint-disable-next-line complexity\nexport function _longestText(\n  ctx: CanvasRenderingContext2D,\n  font: string,\n  arrayOfThings: Things,\n  cache?: {data?: Record<string, number>, garbageCollect?: string[], font?: string}\n) {\n  cache = cache || {};\n  let data = cache.data = cache.data || {};\n  let gc = cache.garbageCollect = cache.garbageCollect || [];\n\n  if (cache.font !== font) {\n    data = cache.data = {};\n    gc = cache.garbageCollect = [];\n    cache.font = font;\n  }\n\n  ctx.save();\n\n  ctx.font = font;\n  let longest = 0;\n  const ilen = arrayOfThings.length;\n  let i: number, j: number, jlen: number, thing: Thing | Thing[], nestedThing: Thing | Thing[];\n  for (i = 0; i < ilen; i++) {\n    thing = arrayOfThings[i];\n\n    // Undefined strings and arrays should not be measured\n    if (thing !== undefined && thing !== null && !isArray(thing)) {\n      longest = _measureText(ctx, data, gc, longest, thing);\n    } else if (isArray(thing)) {\n      // if it is an array lets measure each element\n      // to do maybe simplify this function a bit so we can do this more recursively?\n      for (j = 0, jlen = thing.length; j < jlen; j++) {\n        nestedThing = thing[j];\n        // Undefined strings and arrays should not be measured\n        if (nestedThing !== undefined && nestedThing !== null && !isArray(nestedThing)) {\n          longest = _measureText(ctx, data, gc, longest, nestedThing);\n        }\n      }\n    }\n  }\n\n  ctx.restore();\n\n  const gcLen = gc.length / 2;\n  if (gcLen > arrayOfThings.length) {\n    for (i = 0; i < gcLen; i++) {\n      delete data[gc[i]];\n    }\n    gc.splice(0, gcLen);\n  }\n  return longest;\n}\n\n/**\n * Returns the aligned pixel value to avoid anti-aliasing blur\n * @param chart - The chart instance.\n * @param pixel - A pixel value.\n * @param width - The width of the element.\n * @returns The aligned pixel value.\n * @private\n */\nexport function _alignPixel(chart: Chart, pixel: number, width: number) {\n  const devicePixelRatio = chart.currentDevicePixelRatio;\n  const halfWidth = width !== 0 ? Math.max(width / 2, 0.5) : 0;\n  return Math.round((pixel - halfWidth) * devicePixelRatio) / devicePixelRatio + halfWidth;\n}\n\n/**\n * Clears the entire canvas.\n */\nexport function clearCanvas(canvas: HTMLCanvasElement, ctx?: CanvasRenderingContext2D) {\n  ctx = ctx || canvas.getContext('2d');\n\n  ctx.save();\n  // canvas.width and canvas.height do not consider the canvas transform,\n  // while clearRect does\n  ctx.resetTransform();\n  ctx.clearRect(0, 0, canvas.width, canvas.height);\n  ctx.restore();\n}\n\nexport interface DrawPointOptions {\n  pointStyle: PointStyle;\n  rotation?: number;\n  radius: number;\n  borderWidth: number;\n}\n\nexport function drawPoint(\n  ctx: CanvasRenderingContext2D,\n  options: DrawPointOptions,\n  x: number,\n  y: number\n) {\n  // eslint-disable-next-line @typescript-eslint/no-use-before-define\n  drawPointLegend(ctx, options, x, y, null);\n}\n\n// eslint-disable-next-line complexity\nexport function drawPointLegend(\n  ctx: CanvasRenderingContext2D,\n  options: DrawPointOptions,\n  x: number,\n  y: number,\n  w: number\n) {\n  let type: string, xOffset: number, yOffset: number, size: number, cornerRadius: number, width: number, xOffsetW: number, yOffsetW: number;\n  const style = options.pointStyle;\n  const rotation = options.rotation;\n  const radius = options.radius;\n  let rad = (rotation || 0) * RAD_PER_DEG;\n\n  if (style && typeof style === 'object') {\n    type = style.toString();\n    if (type === '[object HTMLImageElement]' || type === '[object HTMLCanvasElement]') {\n      ctx.save();\n      ctx.translate(x, y);\n      ctx.rotate(rad);\n      ctx.drawImage(style, -style.width / 2, -style.height / 2, style.width, style.height);\n      ctx.restore();\n      return;\n    }\n  }\n\n  if (isNaN(radius) || radius <= 0) {\n    return;\n  }\n\n  ctx.beginPath();\n\n  switch (style) {\n  // Default includes circle\n    default:\n      if (w) {\n        ctx.ellipse(x, y, w / 2, radius, 0, 0, TAU);\n      } else {\n        ctx.arc(x, y, radius, 0, TAU);\n      }\n      ctx.closePath();\n      break;\n    case 'triangle':\n      width = w ? w / 2 : radius;\n      ctx.moveTo(x + Math.sin(rad) * width, y - Math.cos(rad) * radius);\n      rad += TWO_THIRDS_PI;\n      ctx.lineTo(x + Math.sin(rad) * width, y - Math.cos(rad) * radius);\n      rad += TWO_THIRDS_PI;\n      ctx.lineTo(x + Math.sin(rad) * width, y - Math.cos(rad) * radius);\n      ctx.closePath();\n      break;\n    case 'rectRounded':\n    // NOTE: the rounded rect implementation changed to use `arc` instead of\n    // `quadraticCurveTo` since it generates better results when rect is\n    // almost a circle. 0.516 (instead of 0.5) produces results with visually\n    // closer proportion to the previous impl and it is inscribed in the\n    // circle with `radius`. For more details, see the following PRs:\n    // https://github.com/chartjs/Chart.js/issues/5597\n    // https://github.com/chartjs/Chart.js/issues/5858\n      cornerRadius = radius * 0.516;\n      size = radius - cornerRadius;\n      xOffset = Math.cos(rad + QUARTER_PI) * size;\n      xOffsetW = Math.cos(rad + QUARTER_PI) * (w ? w / 2 - cornerRadius : size);\n      yOffset = Math.sin(rad + QUARTER_PI) * size;\n      yOffsetW = Math.sin(rad + QUARTER_PI) * (w ? w / 2 - cornerRadius : size);\n      ctx.arc(x - xOffsetW, y - yOffset, cornerRadius, rad - PI, rad - HALF_PI);\n      ctx.arc(x + yOffsetW, y - xOffset, cornerRadius, rad - HALF_PI, rad);\n      ctx.arc(x + xOffsetW, y + yOffset, cornerRadius, rad, rad + HALF_PI);\n      ctx.arc(x - yOffsetW, y + xOffset, cornerRadius, rad + HALF_PI, rad + PI);\n      ctx.closePath();\n      break;\n    case 'rect':\n      if (!rotation) {\n        size = Math.SQRT1_2 * radius;\n        width = w ? w / 2 : size;\n        ctx.rect(x - width, y - size, 2 * width, 2 * size);\n        break;\n      }\n      rad += QUARTER_PI;\n    /* falls through */\n    case 'rectRot':\n      xOffsetW = Math.cos(rad) * (w ? w / 2 : radius);\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      yOffsetW = Math.sin(rad) * (w ? w / 2 : radius);\n      ctx.moveTo(x - xOffsetW, y - yOffset);\n      ctx.lineTo(x + yOffsetW, y - xOffset);\n      ctx.lineTo(x + xOffsetW, y + yOffset);\n      ctx.lineTo(x - yOffsetW, y + xOffset);\n      ctx.closePath();\n      break;\n    case 'crossRot':\n      rad += QUARTER_PI;\n    /* falls through */\n    case 'cross':\n      xOffsetW = Math.cos(rad) * (w ? w / 2 : radius);\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      yOffsetW = Math.sin(rad) * (w ? w / 2 : radius);\n      ctx.moveTo(x - xOffsetW, y - yOffset);\n      ctx.lineTo(x + xOffsetW, y + yOffset);\n      ctx.moveTo(x + yOffsetW, y - xOffset);\n      ctx.lineTo(x - yOffsetW, y + xOffset);\n      break;\n    case 'star':\n      xOffsetW = Math.cos(rad) * (w ? w / 2 : radius);\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      yOffsetW = Math.sin(rad) * (w ? w / 2 : radius);\n      ctx.moveTo(x - xOffsetW, y - yOffset);\n      ctx.lineTo(x + xOffsetW, y + yOffset);\n      ctx.moveTo(x + yOffsetW, y - xOffset);\n      ctx.lineTo(x - yOffsetW, y + xOffset);\n      rad += QUARTER_PI;\n      xOffsetW = Math.cos(rad) * (w ? w / 2 : radius);\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      yOffsetW = Math.sin(rad) * (w ? w / 2 : radius);\n      ctx.moveTo(x - xOffsetW, y - yOffset);\n      ctx.lineTo(x + xOffsetW, y + yOffset);\n      ctx.moveTo(x + yOffsetW, y - xOffset);\n      ctx.lineTo(x - yOffsetW, y + xOffset);\n      break;\n    case 'line':\n      xOffset = w ? w / 2 : Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      ctx.moveTo(x - xOffset, y - yOffset);\n      ctx.lineTo(x + xOffset, y + yOffset);\n      break;\n    case 'dash':\n      ctx.moveTo(x, y);\n      ctx.lineTo(x + Math.cos(rad) * (w ? w / 2 : radius), y + Math.sin(rad) * radius);\n      break;\n    case false:\n      ctx.closePath();\n      break;\n  }\n\n  ctx.fill();\n  if (options.borderWidth > 0) {\n    ctx.stroke();\n  }\n}\n\n/**\n * Returns true if the point is inside the rectangle\n * @param point - The point to test\n * @param area - The rectangle\n * @param margin - allowed margin\n * @private\n */\nexport function _isPointInArea(\n  point: Point,\n  area: TRBL,\n  margin?: number\n) {\n  margin = margin || 0.5; // margin - default is to match rounded decimals\n\n  return !area || (point && point.x > area.left - margin && point.x < area.right + margin &&\n\t\tpoint.y > area.top - margin && point.y < area.bottom + margin);\n}\n\nexport function clipArea(ctx: CanvasRenderingContext2D, area: TRBL) {\n  ctx.save();\n  ctx.beginPath();\n  ctx.rect(area.left, area.top, area.right - area.left, area.bottom - area.top);\n  ctx.clip();\n}\n\nexport function unclipArea(ctx: CanvasRenderingContext2D) {\n  ctx.restore();\n}\n\n/**\n * @private\n */\nexport function _steppedLineTo(\n  ctx: CanvasRenderingContext2D,\n  previous: Point,\n  target: Point,\n  flip?: boolean,\n  mode?: string\n) {\n  if (!previous) {\n    return ctx.lineTo(target.x, target.y);\n  }\n  if (mode === 'middle') {\n    const midpoint = (previous.x + target.x) / 2.0;\n    ctx.lineTo(midpoint, previous.y);\n    ctx.lineTo(midpoint, target.y);\n  } else if (mode === 'after' !== !!flip) {\n    ctx.lineTo(previous.x, target.y);\n  } else {\n    ctx.lineTo(target.x, previous.y);\n  }\n  ctx.lineTo(target.x, target.y);\n}\n\n/**\n * @private\n */\nexport function _bezierCurveTo(\n  ctx: CanvasRenderingContext2D,\n  previous: SplinePoint,\n  target: SplinePoint,\n  flip?: boolean\n) {\n  if (!previous) {\n    return ctx.lineTo(target.x, target.y);\n  }\n  ctx.bezierCurveTo(\n    flip ? previous.cp1x : previous.cp2x,\n    flip ? previous.cp1y : previous.cp2y,\n    flip ? target.cp2x : target.cp1x,\n    flip ? target.cp2y : target.cp1y,\n    target.x,\n    target.y);\n}\n\nfunction setRenderOpts(ctx: CanvasRenderingContext2D, opts: RenderTextOpts) {\n  if (opts.translation) {\n    ctx.translate(opts.translation[0], opts.translation[1]);\n  }\n\n  if (!isNullOrUndef(opts.rotation)) {\n    ctx.rotate(opts.rotation);\n  }\n\n  if (opts.color) {\n    ctx.fillStyle = opts.color;\n  }\n\n  if (opts.textAlign) {\n    ctx.textAlign = opts.textAlign;\n  }\n\n  if (opts.textBaseline) {\n    ctx.textBaseline = opts.textBaseline;\n  }\n}\n\nfunction decorateText(\n  ctx: CanvasRenderingContext2D,\n  x: number,\n  y: number,\n  line: string,\n  opts: RenderTextOpts\n) {\n  if (opts.strikethrough || opts.underline) {\n    /**\n     * Now that IE11 support has been dropped, we can use more\n     * of the TextMetrics object. The actual bounding boxes\n     * are unflagged in Chrome, Firefox, Edge, and Safari so they\n     * can be safely used.\n     * See https://developer.mozilla.org/en-US/docs/Web/API/TextMetrics#Browser_compatibility\n     */\n    const metrics = ctx.measureText(line);\n    const left = x - metrics.actualBoundingBoxLeft;\n    const right = x + metrics.actualBoundingBoxRight;\n    const top = y - metrics.actualBoundingBoxAscent;\n    const bottom = y + metrics.actualBoundingBoxDescent;\n    const yDecoration = opts.strikethrough ? (top + bottom) / 2 : bottom;\n\n    ctx.strokeStyle = ctx.fillStyle;\n    ctx.beginPath();\n    ctx.lineWidth = opts.decorationWidth || 2;\n    ctx.moveTo(left, yDecoration);\n    ctx.lineTo(right, yDecoration);\n    ctx.stroke();\n  }\n}\n\nfunction drawBackdrop(ctx: CanvasRenderingContext2D, opts: BackdropOptions) {\n  const oldColor = ctx.fillStyle;\n\n  ctx.fillStyle = opts.color as string;\n  ctx.fillRect(opts.left, opts.top, opts.width, opts.height);\n  ctx.fillStyle = oldColor;\n}\n\n/**\n * Render text onto the canvas\n */\nexport function renderText(\n  ctx: CanvasRenderingContext2D,\n  text: string | string[],\n  x: number,\n  y: number,\n  font: CanvasFontSpec,\n  opts: RenderTextOpts = {}\n) {\n  const lines = isArray(text) ? text : [text];\n  const stroke = opts.strokeWidth > 0 && opts.strokeColor !== '';\n  let i: number, line: string;\n\n  ctx.save();\n  ctx.font = font.string;\n  setRenderOpts(ctx, opts);\n\n  for (i = 0; i < lines.length; ++i) {\n    line = lines[i];\n\n    if (opts.backdrop) {\n      drawBackdrop(ctx, opts.backdrop);\n    }\n\n    if (stroke) {\n      if (opts.strokeColor) {\n        ctx.strokeStyle = opts.strokeColor;\n      }\n\n      if (!isNullOrUndef(opts.strokeWidth)) {\n        ctx.lineWidth = opts.strokeWidth;\n      }\n\n      ctx.strokeText(line, x, y, opts.maxWidth);\n    }\n\n    ctx.fillText(line, x, y, opts.maxWidth);\n    decorateText(ctx, x, y, line, opts);\n\n    y += Number(font.lineHeight);\n  }\n\n  ctx.restore();\n}\n\n/**\n * Add a path of a rectangle with rounded corners to the current sub-path\n * @param ctx - Context\n * @param rect - Bounding rect\n */\nexport function addRoundedRectPath(\n  ctx: CanvasRenderingContext2D,\n  rect: RoundedRect & { radius: TRBLCorners }\n) {\n  const {x, y, w, h, radius} = rect;\n\n  // top left arc\n  ctx.arc(x + radius.topLeft, y + radius.topLeft, radius.topLeft, -HALF_PI, PI, true);\n\n  // line from top left to bottom left\n  ctx.lineTo(x, y + h - radius.bottomLeft);\n\n  // bottom left arc\n  ctx.arc(x + radius.bottomLeft, y + h - radius.bottomLeft, radius.bottomLeft, PI, HALF_PI, true);\n\n  // line from bottom left to bottom right\n  ctx.lineTo(x + w - radius.bottomRight, y + h);\n\n  // bottom right arc\n  ctx.arc(x + w - radius.bottomRight, y + h - radius.bottomRight, radius.bottomRight, HALF_PI, 0, true);\n\n  // line from bottom right to top right\n  ctx.lineTo(x + w, y + radius.topRight);\n\n  // top right arc\n  ctx.arc(x + w - radius.topRight, y + radius.topRight, radius.topRight, 0, -HALF_PI, true);\n\n  // line from top right to top left\n  ctx.lineTo(x + radius.topLeft, y);\n}\n", "import defaults from '../core/core.defaults.js';\nimport {isArray, isObject, toDimension, valueOrDefault} from './helpers.core.js';\nimport {toFontString} from './helpers.canvas.js';\nimport type {ChartArea, FontSpec, Point} from '../types/index.js';\nimport type {TRBL, TRBLCorners} from '../types/geometric.js';\n\nconst LINE_HEIGHT = /^(normal|(\\d+(?:\\.\\d+)?)(px|em|%)?)$/;\nconst FONT_STYLE = /^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/;\n\n/**\n * @alias Chart.helpers.options\n * @namespace\n */\n/**\n * Converts the given line height `value` in pixels for a specific font `size`.\n * @param value - The lineHeight to parse (eg. 1.6, '14px', '75%', '1.6em').\n * @param size - The font size (in pixels) used to resolve relative `value`.\n * @returns The effective line height in pixels (size * 1.2 if value is invalid).\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/line-height\n * @since 2.7.0\n */\nexport function toLineHeight(value: number | string, size: number): number {\n  const matches = ('' + value).match(LINE_HEIGHT);\n  if (!matches || matches[1] === 'normal') {\n    return size * 1.2;\n  }\n\n  value = +matches[2];\n\n  switch (matches[3]) {\n    case 'px':\n      return value;\n    case '%':\n      value /= 100;\n      break;\n    default:\n      break;\n  }\n\n  return size * value;\n}\n\nconst numberOrZero = (v: unknown) => +v || 0;\n\n/**\n * @param value\n * @param props\n */\nexport function _readValueToProps<K extends string>(value: number | Record<K, number>, props: K[]): Record<K, number>;\nexport function _readValueToProps<K extends string, T extends string>(value: number | Record<K & T, number>, props: Record<T, K>): Record<T, number>;\nexport function _readValueToProps(value: number | Record<string, number>, props: string[] | Record<string, string>) {\n  const ret = {};\n  const objProps = isObject(props);\n  const keys = objProps ? Object.keys(props) : props;\n  const read = isObject(value)\n    ? objProps\n      ? prop => valueOrDefault(value[prop], value[props[prop]])\n      : prop => value[prop]\n    : () => value;\n\n  for (const prop of keys) {\n    ret[prop] = numberOrZero(read(prop));\n  }\n  return ret;\n}\n\n/**\n * Converts the given value into a TRBL object.\n * @param value - If a number, set the value to all TRBL component,\n *  else, if an object, use defined properties and sets undefined ones to 0.\n *  x / y are shorthands for same value for left/right and top/bottom.\n * @returns The padding values (top, right, bottom, left)\n * @since 3.0.0\n */\nexport function toTRBL(value: number | TRBL | Point) {\n  return _readValueToProps(value, {top: 'y', right: 'x', bottom: 'y', left: 'x'});\n}\n\n/**\n * Converts the given value into a TRBL corners object (similar with css border-radius).\n * @param value - If a number, set the value to all TRBL corner components,\n *  else, if an object, use defined properties and sets undefined ones to 0.\n * @returns The TRBL corner values (topLeft, topRight, bottomLeft, bottomRight)\n * @since 3.0.0\n */\nexport function toTRBLCorners(value: number | TRBLCorners) {\n  return _readValueToProps(value, ['topLeft', 'topRight', 'bottomLeft', 'bottomRight']);\n}\n\n/**\n * Converts the given value into a padding object with pre-computed width/height.\n * @param value - If a number, set the value to all TRBL component,\n *  else, if an object, use defined properties and sets undefined ones to 0.\n *  x / y are shorthands for same value for left/right and top/bottom.\n * @returns The padding values (top, right, bottom, left, width, height)\n * @since 2.7.0\n */\nexport function toPadding(value?: number | TRBL): ChartArea {\n  const obj = toTRBL(value) as ChartArea;\n\n  obj.width = obj.left + obj.right;\n  obj.height = obj.top + obj.bottom;\n\n  return obj;\n}\n\n/**\n * Parses font options and returns the font object.\n * @param options - A object that contains font options to be parsed.\n * @param fallback - A object that contains fallback font options.\n * @return The font object.\n * @private\n */\n\nexport function toFont(options: Partial<FontSpec>, fallback?: Partial<FontSpec>) {\n  options = options || {};\n  fallback = fallback || defaults.font as FontSpec;\n\n  let size = valueOrDefault(options.size, fallback.size);\n\n  if (typeof size === 'string') {\n    size = parseInt(size, 10);\n  }\n  let style = valueOrDefault(options.style, fallback.style);\n  if (style && !('' + style).match(FONT_STYLE)) {\n    console.warn('Invalid font style specified: \"' + style + '\"');\n    style = undefined;\n  }\n\n  const font = {\n    family: valueOrDefault(options.family, fallback.family),\n    lineHeight: toLineHeight(valueOrDefault(options.lineHeight, fallback.lineHeight), size),\n    size,\n    style,\n    weight: valueOrDefault(options.weight, fallback.weight),\n    string: ''\n  };\n\n  font.string = toFontString(font);\n  return font;\n}\n\n/**\n * Evaluates the given `inputs` sequentially and returns the first defined value.\n * @param inputs - An array of values, falling back to the last value.\n * @param context - If defined and the current value is a function, the value\n * is called with `context` as first argument and the result becomes the new input.\n * @param index - If defined and the current value is an array, the value\n * at `index` become the new input.\n * @param info - object to return information about resolution in\n * @param info.cacheable - Will be set to `false` if option is not cacheable.\n * @since 2.7.0\n */\nexport function resolve(inputs: Array<unknown>, context?: object, index?: number, info?: { cacheable: boolean }) {\n  let cacheable = true;\n  let i: number, ilen: number, value: unknown;\n\n  for (i = 0, ilen = inputs.length; i < ilen; ++i) {\n    value = inputs[i];\n    if (value === undefined) {\n      continue;\n    }\n    if (context !== undefined && typeof value === 'function') {\n      value = value(context);\n      cacheable = false;\n    }\n    if (index !== undefined && isArray(value)) {\n      value = value[index % value.length];\n      cacheable = false;\n    }\n    if (value !== undefined) {\n      if (info && !cacheable) {\n        info.cacheable = false;\n      }\n      return value;\n    }\n  }\n}\n\n/**\n * @param minmax\n * @param grace\n * @param beginAtZero\n * @private\n */\nexport function _addGrace(minmax: { min: number; max: number; }, grace: number | string, beginAtZero: boolean) {\n  const {min, max} = minmax;\n  const change = toDimension(grace, (max - min) / 2);\n  const keepZero = (value: number, add: number) => beginAtZero && value === 0 ? 0 : value + add;\n  return {\n    min: keepZero(min, -Math.abs(change)),\n    max: keepZero(max, change)\n  };\n}\n\n/**\n * Create a context inheriting parentContext\n * @param parentContext\n * @param context\n * @returns\n */\nexport function createContext<T extends object>(parentContext: null, context: T): T;\nexport function createContext<T extends object, P extends T>(parentContext: P, context: T): P & T;\nexport function createContext(parentContext: object, context: object) {\n  return Object.assign(Object.create(parentContext), context);\n}\n", "/* eslint-disable @typescript-eslint/no-use-before-define */\nimport type {AnyObject} from '../types/basic.js';\nimport type {ChartMeta} from '../types/index.js';\nimport type {\n  ResolverObjectKey,\n  ResolverCache,\n  ResolverProxy,\n  DescriptorDefaults,\n  Descriptor,\n  ContextCache,\n  ContextProxy\n} from './helpers.config.types.js';\nimport {isArray, isFunction, isObject, resolveObjectKey, _capitalize} from './helpers.core.js';\n\nexport * from './helpers.config.types.js';\n\n/**\n * Creates a Proxy for resolving raw values for options.\n * @param scopes - The option scopes to look for values, in resolution order\n * @param prefixes - The prefixes for values, in resolution order.\n * @param rootScopes - The root option scopes\n * @param fallback - Parent scopes fallback\n * @param getTarget - callback for getting the target for changed values\n * @returns Proxy\n * @private\n */\nexport function _createResolver<\n  T extends AnyObject[] = AnyObject[],\n  R extends AnyObject[] = T\n>(\n  scopes: T,\n  prefixes = [''],\n  rootScopes?: R,\n  fallback?: ResolverObjectKey,\n  getTarget = () => scopes[0]\n) {\n  const finalRootScopes = rootScopes || scopes;\n  if (typeof fallback === 'undefined') {\n    fallback = _resolve('_fallback', scopes);\n  }\n  const cache: ResolverCache<T, R> = {\n    [Symbol.toStringTag]: 'Object',\n    _cacheable: true,\n    _scopes: scopes,\n    _rootScopes: finalRootScopes,\n    _fallback: fallback,\n    _getTarget: getTarget,\n    override: (scope: AnyObject) => _createResolver([scope, ...scopes], prefixes, finalRootScopes, fallback),\n  };\n  return new Proxy(cache, {\n    /**\n     * A trap for the delete operator.\n     */\n    deleteProperty(target, prop: string) {\n      delete target[prop]; // remove from cache\n      delete target._keys; // remove cached keys\n      delete scopes[0][prop]; // remove from top level scope\n      return true;\n    },\n\n    /**\n     * A trap for getting property values.\n     */\n    get(target, prop: string) {\n      return _cached(target, prop,\n        () => _resolveWithPrefixes(prop, prefixes, scopes, target));\n    },\n\n    /**\n     * A trap for Object.getOwnPropertyDescriptor.\n     * Also used by Object.hasOwnProperty.\n     */\n    getOwnPropertyDescriptor(target, prop) {\n      return Reflect.getOwnPropertyDescriptor(target._scopes[0], prop);\n    },\n\n    /**\n     * A trap for Object.getPrototypeOf.\n     */\n    getPrototypeOf() {\n      return Reflect.getPrototypeOf(scopes[0]);\n    },\n\n    /**\n     * A trap for the in operator.\n     */\n    has(target, prop: string) {\n      return getKeysFromAllScopes(target).includes(prop);\n    },\n\n    /**\n     * A trap for Object.getOwnPropertyNames and Object.getOwnPropertySymbols.\n     */\n    ownKeys(target) {\n      return getKeysFromAllScopes(target);\n    },\n\n    /**\n     * A trap for setting property values.\n     */\n    set(target, prop: string, value) {\n      const storage = target._storage || (target._storage = getTarget());\n      target[prop] = storage[prop] = value; // set to top level scope + cache\n      delete target._keys; // remove cached keys\n      return true;\n    }\n  }) as ResolverProxy<T, R>;\n}\n\n/**\n * Returns an Proxy for resolving option values with context.\n * @param proxy - The Proxy returned by `_createResolver`\n * @param context - Context object for scriptable/indexable options\n * @param subProxy - The proxy provided for scriptable options\n * @param descriptorDefaults - Defaults for descriptors\n * @private\n */\nexport function _attachContext<\n  T extends AnyObject[] = AnyObject[],\n  R extends AnyObject[] = T\n>(\n  proxy: ResolverProxy<T, R>,\n  context: AnyObject,\n  subProxy?: ResolverProxy<T, R>,\n  descriptorDefaults?: DescriptorDefaults\n) {\n  const cache: ContextCache<T, R> = {\n    _cacheable: false,\n    _proxy: proxy,\n    _context: context,\n    _subProxy: subProxy,\n    _stack: new Set(),\n    _descriptors: _descriptors(proxy, descriptorDefaults),\n    setContext: (ctx: AnyObject) => _attachContext(proxy, ctx, subProxy, descriptorDefaults),\n    override: (scope: AnyObject) => _attachContext(proxy.override(scope), context, subProxy, descriptorDefaults)\n  };\n  return new Proxy(cache, {\n    /**\n     * A trap for the delete operator.\n     */\n    deleteProperty(target, prop) {\n      delete target[prop]; // remove from cache\n      delete proxy[prop]; // remove from proxy\n      return true;\n    },\n\n    /**\n     * A trap for getting property values.\n     */\n    get(target, prop: string, receiver) {\n      return _cached(target, prop,\n        () => _resolveWithContext(target, prop, receiver));\n    },\n\n    /**\n     * A trap for Object.getOwnPropertyDescriptor.\n     * Also used by Object.hasOwnProperty.\n     */\n    getOwnPropertyDescriptor(target, prop) {\n      return target._descriptors.allKeys\n        ? Reflect.has(proxy, prop) ? {enumerable: true, configurable: true} : undefined\n        : Reflect.getOwnPropertyDescriptor(proxy, prop);\n    },\n\n    /**\n     * A trap for Object.getPrototypeOf.\n     */\n    getPrototypeOf() {\n      return Reflect.getPrototypeOf(proxy);\n    },\n\n    /**\n     * A trap for the in operator.\n     */\n    has(target, prop) {\n      return Reflect.has(proxy, prop);\n    },\n\n    /**\n     * A trap for Object.getOwnPropertyNames and Object.getOwnPropertySymbols.\n     */\n    ownKeys() {\n      return Reflect.ownKeys(proxy);\n    },\n\n    /**\n     * A trap for setting property values.\n     */\n    set(target, prop, value) {\n      proxy[prop] = value; // set to proxy\n      delete target[prop]; // remove from cache\n      return true;\n    }\n  }) as ContextProxy<T, R>;\n}\n\n/**\n * @private\n */\nexport function _descriptors(\n  proxy: ResolverCache,\n  defaults: DescriptorDefaults = {scriptable: true, indexable: true}\n): Descriptor {\n  const {_scriptable = defaults.scriptable, _indexable = defaults.indexable, _allKeys = defaults.allKeys} = proxy;\n  return {\n    allKeys: _allKeys,\n    scriptable: _scriptable,\n    indexable: _indexable,\n    isScriptable: isFunction(_scriptable) ? _scriptable : () => _scriptable,\n    isIndexable: isFunction(_indexable) ? _indexable : () => _indexable\n  };\n}\n\nconst readKey = (prefix: string, name: string) => prefix ? prefix + _capitalize(name) : name;\nconst needsSubResolver = (prop: string, value: unknown) => isObject(value) && prop !== 'adapters' &&\n  (Object.getPrototypeOf(value) === null || value.constructor === Object);\n\nfunction _cached(\n  target: AnyObject,\n  prop: string,\n  resolve: () => unknown\n) {\n  if (Object.prototype.hasOwnProperty.call(target, prop)) {\n    return target[prop];\n  }\n\n  const value = resolve();\n  // cache the resolved value\n  target[prop] = value;\n  return value;\n}\n\nfunction _resolveWithContext(\n  target: ContextCache,\n  prop: string,\n  receiver: AnyObject\n) {\n  const {_proxy, _context, _subProxy, _descriptors: descriptors} = target;\n  let value = _proxy[prop]; // resolve from proxy\n\n  // resolve with context\n  if (isFunction(value) && descriptors.isScriptable(prop)) {\n    value = _resolveScriptable(prop, value, target, receiver);\n  }\n  if (isArray(value) && value.length) {\n    value = _resolveArray(prop, value, target, descriptors.isIndexable);\n  }\n  if (needsSubResolver(prop, value)) {\n    // if the resolved value is an object, create a sub resolver for it\n    value = _attachContext(value, _context, _subProxy && _subProxy[prop], descriptors);\n  }\n  return value;\n}\n\nfunction _resolveScriptable(\n  prop: string,\n  getValue: (ctx: AnyObject, sub: AnyObject) => unknown,\n  target: ContextCache,\n  receiver: AnyObject\n) {\n  const {_proxy, _context, _subProxy, _stack} = target;\n  if (_stack.has(prop)) {\n    throw new Error('Recursion detected: ' + Array.from(_stack).join('->') + '->' + prop);\n  }\n  _stack.add(prop);\n  let value = getValue(_context, _subProxy || receiver);\n  _stack.delete(prop);\n  if (needsSubResolver(prop, value)) {\n    // When scriptable option returns an object, create a resolver on that.\n    value = createSubResolver(_proxy._scopes, _proxy, prop, value);\n  }\n  return value;\n}\n\nfunction _resolveArray(\n  prop: string,\n  value: unknown[],\n  target: ContextCache,\n  isIndexable: (key: string) => boolean\n) {\n  const {_proxy, _context, _subProxy, _descriptors: descriptors} = target;\n\n  if (typeof _context.index !== 'undefined' && isIndexable(prop)) {\n    return value[_context.index % value.length];\n  } else if (isObject(value[0])) {\n    // Array of objects, return array or resolvers\n    const arr = value;\n    const scopes = _proxy._scopes.filter(s => s !== arr);\n    value = [];\n    for (const item of arr) {\n      const resolver = createSubResolver(scopes, _proxy, prop, item);\n      value.push(_attachContext(resolver, _context, _subProxy && _subProxy[prop], descriptors));\n    }\n  }\n  return value;\n}\n\nfunction resolveFallback(\n  fallback: ResolverObjectKey | ((prop: ResolverObjectKey, value: unknown) => ResolverObjectKey),\n  prop: ResolverObjectKey,\n  value: unknown\n) {\n  return isFunction(fallback) ? fallback(prop, value) : fallback;\n}\n\nconst getScope = (key: ResolverObjectKey, parent: AnyObject) => key === true ? parent\n  : typeof key === 'string' ? resolveObjectKey(parent, key) : undefined;\n\nfunction addScopes(\n  set: Set<AnyObject>,\n  parentScopes: AnyObject[],\n  key: ResolverObjectKey,\n  parentFallback: ResolverObjectKey,\n  value: unknown\n) {\n  for (const parent of parentScopes) {\n    const scope = getScope(key, parent);\n    if (scope) {\n      set.add(scope);\n      const fallback = resolveFallback(scope._fallback, key, value);\n      if (typeof fallback !== 'undefined' && fallback !== key && fallback !== parentFallback) {\n        // When we reach the descriptor that defines a new _fallback, return that.\n        // The fallback will resume to that new scope.\n        return fallback;\n      }\n    } else if (scope === false && typeof parentFallback !== 'undefined' && key !== parentFallback) {\n      // Fallback to `false` results to `false`, when falling back to different key.\n      // For example `interaction` from `hover` or `plugins.tooltip` and `animation` from `animations`\n      return null;\n    }\n  }\n  return false;\n}\n\nfunction createSubResolver(\n  parentScopes: AnyObject[],\n  resolver: ResolverCache,\n  prop: ResolverObjectKey,\n  value: unknown\n) {\n  const rootScopes = resolver._rootScopes;\n  const fallback = resolveFallback(resolver._fallback, prop, value);\n  const allScopes = [...parentScopes, ...rootScopes];\n  const set = new Set<AnyObject>();\n  set.add(value);\n  let key = addScopesFromKey(set, allScopes, prop, fallback || prop, value);\n  if (key === null) {\n    return false;\n  }\n  if (typeof fallback !== 'undefined' && fallback !== prop) {\n    key = addScopesFromKey(set, allScopes, fallback, key, value);\n    if (key === null) {\n      return false;\n    }\n  }\n  return _createResolver(Array.from(set), [''], rootScopes, fallback,\n    () => subGetTarget(resolver, prop as string, value));\n}\n\nfunction addScopesFromKey(\n  set: Set<AnyObject>,\n  allScopes: AnyObject[],\n  key: ResolverObjectKey,\n  fallback: ResolverObjectKey,\n  item: unknown\n) {\n  while (key) {\n    key = addScopes(set, allScopes, key, fallback, item);\n  }\n  return key;\n}\n\nfunction subGetTarget(\n  resolver: ResolverCache,\n  prop: string,\n  value: unknown\n) {\n  const parent = resolver._getTarget();\n  if (!(prop in parent)) {\n    parent[prop] = {};\n  }\n  const target = parent[prop];\n  if (isArray(target) && isObject(value)) {\n    // For array of objects, the object is used to store updated values\n    return value;\n  }\n  return target || {};\n}\n\nfunction _resolveWithPrefixes(\n  prop: string,\n  prefixes: string[],\n  scopes: AnyObject[],\n  proxy: ResolverProxy\n) {\n  let value: unknown;\n  for (const prefix of prefixes) {\n    value = _resolve(readKey(prefix, prop), scopes);\n    if (typeof value !== 'undefined') {\n      return needsSubResolver(prop, value)\n        ? createSubResolver(scopes, proxy, prop, value)\n        : value;\n    }\n  }\n}\n\nfunction _resolve(key: string, scopes: AnyObject[]) {\n  for (const scope of scopes) {\n    if (!scope) {\n      continue;\n    }\n    const value = scope[key];\n    if (typeof value !== 'undefined') {\n      return value;\n    }\n  }\n}\n\nfunction getKeysFromAllScopes(target: ResolverCache) {\n  let keys = target._keys;\n  if (!keys) {\n    keys = target._keys = resolveKeysFromAllScopes(target._scopes);\n  }\n  return keys;\n}\n\nfunction resolveKeysFromAllScopes(scopes: AnyObject[]) {\n  const set = new Set<string>();\n  for (const scope of scopes) {\n    for (const key of Object.keys(scope).filter(k => !k.startsWith('_'))) {\n      set.add(key);\n    }\n  }\n  return Array.from(set);\n}\n\nexport function _parseObjectDataRadialScale(\n  meta: ChartMeta<'line' | 'scatter'>,\n  data: AnyObject[],\n  start: number,\n  count: number\n) {\n  const {iScale} = meta;\n  const {key = 'r'} = this._parsing;\n  const parsed = new Array<{r: unknown}>(count);\n  let i: number, ilen: number, index: number, item: AnyObject;\n\n  for (i = 0, ilen = count; i < ilen; ++i) {\n    index = i + start;\n    item = data[index];\n    parsed[i] = {\n      r: iScale.parse(resolveObjectKey(item, key), index)\n    };\n  }\n  return parsed;\n}\n", "import {almostEquals, distanceBetweenPoints, sign} from './helpers.math.js';\nimport {_isPointInArea} from './helpers.canvas.js';\nimport type {ChartArea} from '../types/index.js';\nimport type {SplinePoint} from '../types/geometric.js';\n\nconst EPSILON = Number.EPSILON || 1e-14;\n\ntype OptionalSplinePoint = SplinePoint | false\nconst getPoint = (points: SplinePoint[], i: number): OptionalSplinePoint => i < points.length && !points[i].skip && points[i];\nconst getValueAxis = (indexAxis: 'x' | 'y') => indexAxis === 'x' ? 'y' : 'x';\n\nexport function splineCurve(\n  firstPoint: SplinePoint,\n  middlePoint: SplinePoint,\n  afterPoint: SplinePoint,\n  t: number\n): {\n    previous: SplinePoint\n    next: SplinePoint\n  } {\n  // Props to <PERSON> at scaled innovation for his post on splining between points\n  // http://scaledinnovation.com/analytics/splines/aboutSplines.html\n\n  // This function must also respect \"skipped\" points\n\n  const previous = firstPoint.skip ? middlePoint : firstPoint;\n  const current = middlePoint;\n  const next = afterPoint.skip ? middlePoint : afterPoint;\n  const d01 = distanceBetweenPoints(current, previous);\n  const d12 = distanceBetweenPoints(next, current);\n\n  let s01 = d01 / (d01 + d12);\n  let s12 = d12 / (d01 + d12);\n\n  // If all points are the same, s01 & s02 will be inf\n  s01 = isNaN(s01) ? 0 : s01;\n  s12 = isNaN(s12) ? 0 : s12;\n\n  const fa = t * s01; // scaling factor for triangle Ta\n  const fb = t * s12;\n\n  return {\n    previous: {\n      x: current.x - fa * (next.x - previous.x),\n      y: current.y - fa * (next.y - previous.y)\n    },\n    next: {\n      x: current.x + fb * (next.x - previous.x),\n      y: current.y + fb * (next.y - previous.y)\n    }\n  };\n}\n\n/**\n * Adjust tangents to ensure monotonic properties\n */\nfunction monotoneAdjust(points: SplinePoint[], deltaK: number[], mK: number[]) {\n  const pointsLen = points.length;\n\n  let alphaK: number, betaK: number, tauK: number, squaredMagnitude: number, pointCurrent: OptionalSplinePoint;\n  let pointAfter = getPoint(points, 0);\n  for (let i = 0; i < pointsLen - 1; ++i) {\n    pointCurrent = pointAfter;\n    pointAfter = getPoint(points, i + 1);\n    if (!pointCurrent || !pointAfter) {\n      continue;\n    }\n\n    if (almostEquals(deltaK[i], 0, EPSILON)) {\n      mK[i] = mK[i + 1] = 0;\n      continue;\n    }\n\n    alphaK = mK[i] / deltaK[i];\n    betaK = mK[i + 1] / deltaK[i];\n    squaredMagnitude = Math.pow(alphaK, 2) + Math.pow(betaK, 2);\n    if (squaredMagnitude <= 9) {\n      continue;\n    }\n\n    tauK = 3 / Math.sqrt(squaredMagnitude);\n    mK[i] = alphaK * tauK * deltaK[i];\n    mK[i + 1] = betaK * tauK * deltaK[i];\n  }\n}\n\nfunction monotoneCompute(points: SplinePoint[], mK: number[], indexAxis: 'x' | 'y' = 'x') {\n  const valueAxis = getValueAxis(indexAxis);\n  const pointsLen = points.length;\n  let delta: number, pointBefore: OptionalSplinePoint, pointCurrent: OptionalSplinePoint;\n  let pointAfter = getPoint(points, 0);\n\n  for (let i = 0; i < pointsLen; ++i) {\n    pointBefore = pointCurrent;\n    pointCurrent = pointAfter;\n    pointAfter = getPoint(points, i + 1);\n    if (!pointCurrent) {\n      continue;\n    }\n\n    const iPixel = pointCurrent[indexAxis];\n    const vPixel = pointCurrent[valueAxis];\n    if (pointBefore) {\n      delta = (iPixel - pointBefore[indexAxis]) / 3;\n      pointCurrent[`cp1${indexAxis}`] = iPixel - delta;\n      pointCurrent[`cp1${valueAxis}`] = vPixel - delta * mK[i];\n    }\n    if (pointAfter) {\n      delta = (pointAfter[indexAxis] - iPixel) / 3;\n      pointCurrent[`cp2${indexAxis}`] = iPixel + delta;\n      pointCurrent[`cp2${valueAxis}`] = vPixel + delta * mK[i];\n    }\n  }\n}\n\n/**\n * This function calculates Bézier control points in a similar way than |splineCurve|,\n * but preserves monotonicity of the provided data and ensures no local extremums are added\n * between the dataset discrete points due to the interpolation.\n * See : https://en.wikipedia.org/wiki/Monotone_cubic_interpolation\n */\nexport function splineCurveMonotone(points: SplinePoint[], indexAxis: 'x' | 'y' = 'x') {\n  const valueAxis = getValueAxis(indexAxis);\n  const pointsLen = points.length;\n  const deltaK: number[] = Array(pointsLen).fill(0);\n  const mK: number[] = Array(pointsLen);\n\n  // Calculate slopes (deltaK) and initialize tangents (mK)\n  let i, pointBefore: OptionalSplinePoint, pointCurrent: OptionalSplinePoint;\n  let pointAfter = getPoint(points, 0);\n\n  for (i = 0; i < pointsLen; ++i) {\n    pointBefore = pointCurrent;\n    pointCurrent = pointAfter;\n    pointAfter = getPoint(points, i + 1);\n    if (!pointCurrent) {\n      continue;\n    }\n\n    if (pointAfter) {\n      const slopeDelta = pointAfter[indexAxis] - pointCurrent[indexAxis];\n\n      // In the case of two points that appear at the same x pixel, slopeDeltaX is 0\n      deltaK[i] = slopeDelta !== 0 ? (pointAfter[valueAxis] - pointCurrent[valueAxis]) / slopeDelta : 0;\n    }\n    mK[i] = !pointBefore ? deltaK[i]\n      : !pointAfter ? deltaK[i - 1]\n        : (sign(deltaK[i - 1]) !== sign(deltaK[i])) ? 0\n          : (deltaK[i - 1] + deltaK[i]) / 2;\n  }\n\n  monotoneAdjust(points, deltaK, mK);\n\n  monotoneCompute(points, mK, indexAxis);\n}\n\nfunction capControlPoint(pt: number, min: number, max: number) {\n  return Math.max(Math.min(pt, max), min);\n}\n\nfunction capBezierPoints(points: SplinePoint[], area: ChartArea) {\n  let i, ilen, point, inArea, inAreaPrev;\n  let inAreaNext = _isPointInArea(points[0], area);\n  for (i = 0, ilen = points.length; i < ilen; ++i) {\n    inAreaPrev = inArea;\n    inArea = inAreaNext;\n    inAreaNext = i < ilen - 1 && _isPointInArea(points[i + 1], area);\n    if (!inArea) {\n      continue;\n    }\n    point = points[i];\n    if (inAreaPrev) {\n      point.cp1x = capControlPoint(point.cp1x, area.left, area.right);\n      point.cp1y = capControlPoint(point.cp1y, area.top, area.bottom);\n    }\n    if (inAreaNext) {\n      point.cp2x = capControlPoint(point.cp2x, area.left, area.right);\n      point.cp2y = capControlPoint(point.cp2y, area.top, area.bottom);\n    }\n  }\n}\n\n/**\n * @private\n */\nexport function _updateBezierControlPoints(\n  points: SplinePoint[],\n  options,\n  area: ChartArea,\n  loop: boolean,\n  indexAxis: 'x' | 'y'\n) {\n  let i: number, ilen: number, point: SplinePoint, controlPoints: ReturnType<typeof splineCurve>;\n\n  // Only consider points that are drawn in case the spanGaps option is used\n  if (options.spanGaps) {\n    points = points.filter((pt) => !pt.skip);\n  }\n\n  if (options.cubicInterpolationMode === 'monotone') {\n    splineCurveMonotone(points, indexAxis);\n  } else {\n    let prev = loop ? points[points.length - 1] : points[0];\n    for (i = 0, ilen = points.length; i < ilen; ++i) {\n      point = points[i];\n      controlPoints = splineCurve(\n        prev,\n        point,\n        points[Math.min(i + 1, ilen - (loop ? 0 : 1)) % ilen],\n        options.tension\n      );\n      point.cp1x = controlPoints.previous.x;\n      point.cp1y = controlPoints.previous.y;\n      point.cp2x = controlPoints.next.x;\n      point.cp2y = controlPoints.next.y;\n      prev = point;\n    }\n  }\n\n  if (options.capBezierPoints) {\n    capBezierPoints(points, area);\n  }\n}\n", "import type {ChartArea, Scale} from '../types/index.js';\nimport type Chart from '../core/core.controller.js';\nimport type {ChartEvent} from '../types.js';\nimport {INFINITY} from './helpers.math.js';\n\n/**\n * Note: typedefs are auto-exported, so use a made-up `dom` namespace where\n * necessary to avoid duplicates with `export * from './helpers`; see\n * https://github.com/microsoft/TypeScript/issues/46011\n * @typedef { import('../core/core.controller.js').default } dom.Chart\n * @typedef { import('../../types').ChartEvent } ChartEvent\n */\n\n/**\n * @private\n */\nexport function _isDomSupported(): boolean {\n  return typeof window !== 'undefined' && typeof document !== 'undefined';\n}\n\n/**\n * @private\n */\nexport function _getParentNode(domNode: HTMLCanvasElement): HTMLCanvasElement {\n  let parent = domNode.parentNode;\n  if (parent && parent.toString() === '[object ShadowRoot]') {\n    parent = (parent as ShadowRoot).host;\n  }\n  return parent as HTMLCanvasElement;\n}\n\n/**\n * convert max-width/max-height values that may be percentages into a number\n * @private\n */\n\nfunction parseMaxStyle(styleValue: string | number, node: HTMLElement, parentProperty: string) {\n  let valueInPixels: number;\n  if (typeof styleValue === 'string') {\n    valueInPixels = parseInt(styleValue, 10);\n\n    if (styleValue.indexOf('%') !== -1) {\n      // percentage * size in dimension\n      valueInPixels = (valueInPixels / 100) * node.parentNode[parentProperty];\n    }\n  } else {\n    valueInPixels = styleValue;\n  }\n\n  return valueInPixels;\n}\n\nconst getComputedStyle = (element: HTMLElement): CSSStyleDeclaration =>\n  element.ownerDocument.defaultView.getComputedStyle(element, null);\n\nexport function getStyle(el: HTMLElement, property: string): string {\n  return getComputedStyle(el).getPropertyValue(property);\n}\n\nconst positions = ['top', 'right', 'bottom', 'left'];\nfunction getPositionedStyle(styles: CSSStyleDeclaration, style: string, suffix?: string): ChartArea {\n  const result = {} as ChartArea;\n  suffix = suffix ? '-' + suffix : '';\n  for (let i = 0; i < 4; i++) {\n    const pos = positions[i];\n    result[pos] = parseFloat(styles[style + '-' + pos + suffix]) || 0;\n  }\n  result.width = result.left + result.right;\n  result.height = result.top + result.bottom;\n  return result;\n}\n\nconst useOffsetPos = (x: number, y: number, target: HTMLElement | EventTarget) =>\n  (x > 0 || y > 0) && (!target || !(target as HTMLElement).shadowRoot);\n\n/**\n * @param e\n * @param canvas\n * @returns Canvas position\n */\nfunction getCanvasPosition(\n  e: Event | TouchEvent | MouseEvent,\n  canvas: HTMLCanvasElement\n): {\n    x: number;\n    y: number;\n    box: boolean;\n  } {\n  const touches = (e as TouchEvent).touches;\n  const source = (touches && touches.length ? touches[0] : e) as MouseEvent;\n  const {offsetX, offsetY} = source as MouseEvent;\n  let box = false;\n  let x, y;\n  if (useOffsetPos(offsetX, offsetY, e.target)) {\n    x = offsetX;\n    y = offsetY;\n  } else {\n    const rect = canvas.getBoundingClientRect();\n    x = source.clientX - rect.left;\n    y = source.clientY - rect.top;\n    box = true;\n  }\n  return {x, y, box};\n}\n\n/**\n * Gets an event's x, y coordinates, relative to the chart area\n * @param event\n * @param chart\n * @returns x and y coordinates of the event\n */\n\nexport function getRelativePosition(\n  event: Event | ChartEvent | TouchEvent | MouseEvent,\n  chart: Chart\n): { x: number; y: number } {\n  if ('native' in event) {\n    return event;\n  }\n\n  const {canvas, currentDevicePixelRatio} = chart;\n  const style = getComputedStyle(canvas);\n  const borderBox = style.boxSizing === 'border-box';\n  const paddings = getPositionedStyle(style, 'padding');\n  const borders = getPositionedStyle(style, 'border', 'width');\n  const {x, y, box} = getCanvasPosition(event, canvas);\n  const xOffset = paddings.left + (box && borders.left);\n  const yOffset = paddings.top + (box && borders.top);\n\n  let {width, height} = chart;\n  if (borderBox) {\n    width -= paddings.width + borders.width;\n    height -= paddings.height + borders.height;\n  }\n  return {\n    x: Math.round((x - xOffset) / width * canvas.width / currentDevicePixelRatio),\n    y: Math.round((y - yOffset) / height * canvas.height / currentDevicePixelRatio)\n  };\n}\n\nfunction getContainerSize(canvas: HTMLCanvasElement, width: number, height: number): Partial<Scale> {\n  let maxWidth: number, maxHeight: number;\n\n  if (width === undefined || height === undefined) {\n    const container = _getParentNode(canvas);\n    if (!container) {\n      width = canvas.clientWidth;\n      height = canvas.clientHeight;\n    } else {\n      const rect = container.getBoundingClientRect(); // this is the border box of the container\n      const containerStyle = getComputedStyle(container);\n      const containerBorder = getPositionedStyle(containerStyle, 'border', 'width');\n      const containerPadding = getPositionedStyle(containerStyle, 'padding');\n      width = rect.width - containerPadding.width - containerBorder.width;\n      height = rect.height - containerPadding.height - containerBorder.height;\n      maxWidth = parseMaxStyle(containerStyle.maxWidth, container, 'clientWidth');\n      maxHeight = parseMaxStyle(containerStyle.maxHeight, container, 'clientHeight');\n    }\n  }\n  return {\n    width,\n    height,\n    maxWidth: maxWidth || INFINITY,\n    maxHeight: maxHeight || INFINITY\n  };\n}\n\nconst round1 = (v: number) => Math.round(v * 10) / 10;\n\n// eslint-disable-next-line complexity\nexport function getMaximumSize(\n  canvas: HTMLCanvasElement,\n  bbWidth?: number,\n  bbHeight?: number,\n  aspectRatio?: number\n): { width: number; height: number } {\n  const style = getComputedStyle(canvas);\n  const margins = getPositionedStyle(style, 'margin');\n  const maxWidth = parseMaxStyle(style.maxWidth, canvas, 'clientWidth') || INFINITY;\n  const maxHeight = parseMaxStyle(style.maxHeight, canvas, 'clientHeight') || INFINITY;\n  const containerSize = getContainerSize(canvas, bbWidth, bbHeight);\n  let {width, height} = containerSize;\n\n  if (style.boxSizing === 'content-box') {\n    const borders = getPositionedStyle(style, 'border', 'width');\n    const paddings = getPositionedStyle(style, 'padding');\n    width -= paddings.width + borders.width;\n    height -= paddings.height + borders.height;\n  }\n  width = Math.max(0, width - margins.width);\n  height = Math.max(0, aspectRatio ? width / aspectRatio : height - margins.height);\n  width = round1(Math.min(width, maxWidth, containerSize.maxWidth));\n  height = round1(Math.min(height, maxHeight, containerSize.maxHeight));\n  if (width && !height) {\n    // https://github.com/chartjs/Chart.js/issues/4659\n    // If the canvas has width, but no height, default to aspectRatio of 2 (canvas default)\n    height = round1(width / 2);\n  }\n\n  const maintainHeight = bbWidth !== undefined || bbHeight !== undefined;\n\n  if (maintainHeight && aspectRatio && containerSize.height && height > containerSize.height) {\n    height = containerSize.height;\n    width = round1(Math.floor(height * aspectRatio));\n  }\n\n  return {width, height};\n}\n\n/**\n * @param chart\n * @param forceRatio\n * @param forceStyle\n * @returns True if the canvas context size or transformation has changed.\n */\nexport function retinaScale(\n  chart: Chart,\n  forceRatio: number,\n  forceStyle?: boolean\n): boolean | void {\n  const pixelRatio = forceRatio || 1;\n  const deviceHeight = Math.floor(chart.height * pixelRatio);\n  const deviceWidth = Math.floor(chart.width * pixelRatio);\n\n  chart.height = Math.floor(chart.height);\n  chart.width = Math.floor(chart.width);\n\n  const canvas = chart.canvas;\n\n  // If no style has been set on the canvas, the render size is used as display size,\n  // making the chart visually bigger, so let's enforce it to the \"correct\" values.\n  // See https://github.com/chartjs/Chart.js/issues/3575\n  if (canvas.style && (forceStyle || (!canvas.style.height && !canvas.style.width))) {\n    canvas.style.height = `${chart.height}px`;\n    canvas.style.width = `${chart.width}px`;\n  }\n\n  if (chart.currentDevicePixelRatio !== pixelRatio\n      || canvas.height !== deviceHeight\n      || canvas.width !== deviceWidth) {\n    chart.currentDevicePixelRatio = pixelRatio;\n    canvas.height = deviceHeight;\n    canvas.width = deviceWidth;\n    chart.ctx.setTransform(pixelRatio, 0, 0, pixelRatio, 0, 0);\n    return true;\n  }\n  return false;\n}\n\n/**\n * Detects support for options object argument in addEventListener.\n * https://developer.mozilla.org/en-US/docs/Web/API/EventTarget/addEventListener#Safely_detecting_option_support\n * @private\n */\nexport const supportsEventListenerOptions = (function() {\n  let passiveSupported = false;\n  try {\n    const options = {\n      get passive() { // This function will be called when the browser attempts to access the passive property.\n        passiveSupported = true;\n        return false;\n      }\n    } as EventListenerOptions;\n\n    window.addEventListener('test', null, options);\n    window.removeEventListener('test', null, options);\n  } catch (e) {\n    // continue regardless of error\n  }\n  return passiveSupported;\n}());\n\n/**\n * The \"used\" size is the final value of a dimension property after all calculations have\n * been performed. This method uses the computed style of `element` but returns undefined\n * if the computed style is not expressed in pixels. That can happen in some cases where\n * `element` has a size relative to its parent and this last one is not yet displayed,\n * for example because of `display: none` on a parent node.\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/used_value\n * @returns Size in pixels or undefined if unknown.\n */\n\nexport function readUsedSize(\n  element: HTMLElement,\n  property: 'width' | 'height'\n): number | undefined {\n  const value = getStyle(element, property);\n  const matches = value && value.match(/^(\\d+)(\\.\\d+)?px$/);\n  return matches ? +matches[1] : undefined;\n}\n", "import type {Point, SplinePoint} from '../types/geometric.js';\n\n/**\n * @private\n */\nexport function _pointInLine(p1: Point, p2: Point, t: number, mode?) { // eslint-disable-line @typescript-eslint/no-unused-vars\n  return {\n    x: p1.x + t * (p2.x - p1.x),\n    y: p1.y + t * (p2.y - p1.y)\n  };\n}\n\n/**\n * @private\n */\nexport function _steppedInterpolation(\n  p1: Point,\n  p2: Point,\n  t: number, mode: 'middle' | 'after' | unknown\n) {\n  return {\n    x: p1.x + t * (p2.x - p1.x),\n    y: mode === 'middle' ? t < 0.5 ? p1.y : p2.y\n      : mode === 'after' ? t < 1 ? p1.y : p2.y\n        : t > 0 ? p2.y : p1.y\n  };\n}\n\n/**\n * @private\n */\nexport function _bezierInterpolation(p1: SplinePoint, p2: SplinePoint, t: number, mode?) { // eslint-disable-line @typescript-eslint/no-unused-vars\n  const cp1 = {x: p1.cp2x, y: p1.cp2y};\n  const cp2 = {x: p2.cp1x, y: p2.cp1y};\n  const a = _pointInLine(p1, cp1, t);\n  const b = _pointInLine(cp1, cp2, t);\n  const c = _pointInLine(cp2, p2, t);\n  const d = _pointInLine(a, b, t);\n  const e = _pointInLine(b, c, t);\n  return _pointInLine(d, e, t);\n}\n", "export interface RTLAdapter {\n  x(x: number): number;\n  setWidth(w: number): void;\n  textAlign(align: 'center' | 'left' | 'right'): 'center' | 'left' | 'right';\n  xPlus(x: number, value: number): number;\n  leftForLtr(x: number, itemWidth: number): number;\n}\n\nconst getRightToLeftAdapter = function(rectX: number, width: number): RTLAdapter {\n  return {\n    x(x) {\n      return rectX + rectX + width - x;\n    },\n    setWidth(w) {\n      width = w;\n    },\n    textAlign(align) {\n      if (align === 'center') {\n        return align;\n      }\n      return align === 'right' ? 'left' : 'right';\n    },\n    xPlus(x, value) {\n      return x - value;\n    },\n    leftForLtr(x, itemWidth) {\n      return x - itemWidth;\n    },\n  };\n};\n\nconst getLeftToRightAdapter = function(): RTLAdapter {\n  return {\n    x(x) {\n      return x;\n    },\n    setWidth(w) { // eslint-disable-line no-unused-vars\n    },\n    textAlign(align) {\n      return align;\n    },\n    xPlus(x, value) {\n      return x + value;\n    },\n    leftForLtr(x, _itemWidth) { // eslint-disable-line @typescript-eslint/no-unused-vars\n      return x;\n    },\n  };\n};\n\nexport function getRtlAdapter(rtl: boolean, rectX: number, width: number) {\n  return rtl ? getRightToLeftAdapter(rectX, width) : getLeftToRightAdapter();\n}\n\nexport function overrideTextDirection(ctx: CanvasRenderingContext2D, direction: 'ltr' | 'rtl') {\n  let style: CSSStyleDeclaration, original: [string, string];\n  if (direction === 'ltr' || direction === 'rtl') {\n    style = ctx.canvas.style;\n    original = [\n      style.getPropertyValue('direction'),\n      style.getPropertyPriority('direction'),\n    ];\n\n    style.setProperty('direction', direction, 'important');\n    (ctx as { prevTextDirection?: [string, string] }).prevTextDirection = original;\n  }\n}\n\nexport function restoreTextDirection(ctx: CanvasRenderingContext2D, original?: [string, string]) {\n  if (original !== undefined) {\n    delete (ctx as { prevTextDirection?: [string, string] }).prevTextDirection;\n    ctx.canvas.style.setProperty('direction', original[0], original[1]);\n  }\n}\n", "import {_angleBetween, _angleDiff, _isBetween, _normalizeAngle} from './helpers.math.js';\nimport {createContext} from './helpers.options.js';\nimport {isPatternOrGradient} from './helpers.color.js';\n\n/**\n * @typedef { import('../elements/element.line.js').default } LineElement\n * @typedef { import('../elements/element.point.js').default } PointElement\n * @typedef {{start: number, end: number, loop: boolean, style?: any}} Segment\n */\n\nfunction propertyFn(property) {\n  if (property === 'angle') {\n    return {\n      between: _angleBetween,\n      compare: _angleDiff,\n      normalize: _normalizeAngle,\n    };\n  }\n  return {\n    between: _isBetween,\n    compare: (a, b) => a - b,\n    normalize: x => x\n  };\n}\n\nfunction normalizeSegment({start, end, count, loop, style}) {\n  return {\n    start: start % count,\n    end: end % count,\n    loop: loop && (end - start + 1) % count === 0,\n    style\n  };\n}\n\nfunction getSegment(segment, points, bounds) {\n  const {property, start: startBound, end: endBound} = bounds;\n  const {between, normalize} = propertyFn(property);\n  const count = points.length;\n  // eslint-disable-next-line prefer-const\n  let {start, end, loop} = segment;\n  let i, ilen;\n\n  if (loop) {\n    start += count;\n    end += count;\n    for (i = 0, ilen = count; i < ilen; ++i) {\n      if (!between(normalize(points[start % count][property]), startBound, endBound)) {\n        break;\n      }\n      start--;\n      end--;\n    }\n    start %= count;\n    end %= count;\n  }\n\n  if (end < start) {\n    end += count;\n  }\n  return {start, end, loop, style: segment.style};\n}\n\n/**\n * Returns the sub-segment(s) of a line segment that fall in the given bounds\n * @param {object} segment\n * @param {number} segment.start - start index of the segment, referring the points array\n * @param {number} segment.end - end index of the segment, referring the points array\n * @param {boolean} segment.loop - indicates that the segment is a loop\n * @param {object} [segment.style] - segment style\n * @param {PointElement[]} points - the points that this segment refers to\n * @param {object} [bounds]\n * @param {string} bounds.property - the property of a `PointElement` we are bounding. `x`, `y` or `angle`.\n * @param {number} bounds.start - start value of the property\n * @param {number} bounds.end - end value of the property\n * @private\n **/\nexport function _boundSegment(segment, points, bounds) {\n  if (!bounds) {\n    return [segment];\n  }\n\n  const {property, start: startBound, end: endBound} = bounds;\n  const count = points.length;\n  const {compare, between, normalize} = propertyFn(property);\n  const {start, end, loop, style} = getSegment(segment, points, bounds);\n\n  const result = [];\n  let inside = false;\n  let subStart = null;\n  let value, point, prevValue;\n\n  const startIsBefore = () => between(startBound, prevValue, value) && compare(startBound, prevValue) !== 0;\n  const endIsBefore = () => compare(endBound, value) === 0 || between(endBound, prevValue, value);\n  const shouldStart = () => inside || startIsBefore();\n  const shouldStop = () => !inside || endIsBefore();\n\n  for (let i = start, prev = start; i <= end; ++i) {\n    point = points[i % count];\n\n    if (point.skip) {\n      continue;\n    }\n\n    value = normalize(point[property]);\n\n    if (value === prevValue) {\n      continue;\n    }\n\n    inside = between(value, startBound, endBound);\n\n    if (subStart === null && shouldStart()) {\n      subStart = compare(value, startBound) === 0 ? i : prev;\n    }\n\n    if (subStart !== null && shouldStop()) {\n      result.push(normalizeSegment({start: subStart, end: i, loop, count, style}));\n      subStart = null;\n    }\n    prev = i;\n    prevValue = value;\n  }\n\n  if (subStart !== null) {\n    result.push(normalizeSegment({start: subStart, end, loop, count, style}));\n  }\n\n  return result;\n}\n\n\n/**\n * Returns the segments of the line that are inside given bounds\n * @param {LineElement} line\n * @param {object} [bounds]\n * @param {string} bounds.property - the property we are bounding with. `x`, `y` or `angle`.\n * @param {number} bounds.start - start value of the `property`\n * @param {number} bounds.end - end value of the `property`\n * @private\n */\nexport function _boundSegments(line, bounds) {\n  const result = [];\n  const segments = line.segments;\n\n  for (let i = 0; i < segments.length; i++) {\n    const sub = _boundSegment(segments[i], line.points, bounds);\n    if (sub.length) {\n      result.push(...sub);\n    }\n  }\n  return result;\n}\n\n/**\n * Find start and end index of a line.\n */\nfunction findStartAndEnd(points, count, loop, spanGaps) {\n  let start = 0;\n  let end = count - 1;\n\n  if (loop && !spanGaps) {\n    // loop and not spanning gaps, first find a gap to start from\n    while (start < count && !points[start].skip) {\n      start++;\n    }\n  }\n\n  // find first non skipped point (after the first gap possibly)\n  while (start < count && points[start].skip) {\n    start++;\n  }\n\n  // if we looped to count, start needs to be 0\n  start %= count;\n\n  if (loop) {\n    // loop will go past count, if start > 0\n    end += start;\n  }\n\n  while (end > start && points[end % count].skip) {\n    end--;\n  }\n\n  // end could be more than count, normalize\n  end %= count;\n\n  return {start, end};\n}\n\n/**\n * Compute solid segments from Points, when spanGaps === false\n * @param {PointElement[]} points - the points\n * @param {number} start - start index\n * @param {number} max - max index (can go past count on a loop)\n * @param {boolean} loop - boolean indicating that this would be a loop if no gaps are found\n */\nfunction solidSegments(points, start, max, loop) {\n  const count = points.length;\n  const result = [];\n  let last = start;\n  let prev = points[start];\n  let end;\n\n  for (end = start + 1; end <= max; ++end) {\n    const cur = points[end % count];\n    if (cur.skip || cur.stop) {\n      if (!prev.skip) {\n        loop = false;\n        result.push({start: start % count, end: (end - 1) % count, loop});\n        // @ts-ignore\n        start = last = cur.stop ? end : null;\n      }\n    } else {\n      last = end;\n      if (prev.skip) {\n        start = end;\n      }\n    }\n    prev = cur;\n  }\n\n  if (last !== null) {\n    result.push({start: start % count, end: last % count, loop});\n  }\n\n  return result;\n}\n\n/**\n * Compute the continuous segments that define the whole line\n * There can be skipped points within a segment, if spanGaps is true.\n * @param {LineElement} line\n * @param {object} [segmentOptions]\n * @return {Segment[]}\n * @private\n */\nexport function _computeSegments(line, segmentOptions) {\n  const points = line.points;\n  const spanGaps = line.options.spanGaps;\n  const count = points.length;\n\n  if (!count) {\n    return [];\n  }\n\n  const loop = !!line._loop;\n  const {start, end} = findStartAndEnd(points, count, loop, spanGaps);\n\n  if (spanGaps === true) {\n    return splitByStyles(line, [{start, end, loop}], points, segmentOptions);\n  }\n\n  const max = end < start ? end + count : end;\n  const completeLoop = !!line._fullLoop && start === 0 && end === count - 1;\n  return splitByStyles(line, solidSegments(points, start, max, completeLoop), points, segmentOptions);\n}\n\n/**\n * @param {Segment[]} segments\n * @param {PointElement[]} points\n * @param {object} [segmentOptions]\n * @return {Segment[]}\n */\nfunction splitByStyles(line, segments, points, segmentOptions) {\n  if (!segmentOptions || !segmentOptions.setContext || !points) {\n    return segments;\n  }\n  return doSplitByStyles(line, segments, points, segmentOptions);\n}\n\n/**\n * @param {LineElement} line\n * @param {Segment[]} segments\n * @param {PointElement[]} points\n * @param {object} [segmentOptions]\n * @return {Segment[]}\n */\nfunction doSplitByStyles(line, segments, points, segmentOptions) {\n  const chartContext = line._chart.getContext();\n  const baseStyle = readStyle(line.options);\n  const {_datasetIndex: datasetIndex, options: {spanGaps}} = line;\n  const count = points.length;\n  const result = [];\n  let prevStyle = baseStyle;\n  let start = segments[0].start;\n  let i = start;\n\n  function addStyle(s, e, l, st) {\n    const dir = spanGaps ? -1 : 1;\n    if (s === e) {\n      return;\n    }\n    // Style can not start/end on a skipped point, adjust indices accordingly\n    s += count;\n    while (points[s % count].skip) {\n      s -= dir;\n    }\n    while (points[e % count].skip) {\n      e += dir;\n    }\n    if (s % count !== e % count) {\n      result.push({start: s % count, end: e % count, loop: l, style: st});\n      prevStyle = st;\n      start = e % count;\n    }\n  }\n\n  for (const segment of segments) {\n    start = spanGaps ? start : segment.start;\n    let prev = points[start % count];\n    let style;\n    for (i = start + 1; i <= segment.end; i++) {\n      const pt = points[i % count];\n      style = readStyle(segmentOptions.setContext(createContext(chartContext, {\n        type: 'segment',\n        p0: prev,\n        p1: pt,\n        p0DataIndex: (i - 1) % count,\n        p1DataIndex: i % count,\n        datasetIndex\n      })));\n      if (styleChanged(style, prevStyle)) {\n        addStyle(start, i - 1, segment.loop, prevStyle);\n      }\n      prev = pt;\n      prevStyle = style;\n    }\n    if (start < i - 1) {\n      addStyle(start, i - 1, segment.loop, prevStyle);\n    }\n  }\n\n  return result;\n}\n\nfunction readStyle(options) {\n  return {\n    backgroundColor: options.backgroundColor,\n    borderCapStyle: options.borderCapStyle,\n    borderDash: options.borderDash,\n    borderDashOffset: options.borderDashOffset,\n    borderJoinStyle: options.borderJoinStyle,\n    borderWidth: options.borderWidth,\n    borderColor: options.borderColor\n  };\n}\n\nfunction styleChanged(style, prevStyle) {\n  if (!prevStyle) {\n    return false;\n  }\n  const cache = [];\n  const replacer = function(key, value) {\n    if (!isPatternOrGradient(value)) {\n      return value;\n    }\n    if (!cache.includes(value)) {\n      cache.push(value);\n    }\n    return cache.indexOf(value);\n  };\n  return JSON.stringify(style, replacer) !== JSON.stringify(prevStyle, replacer);\n}\n"], "mappings": ";;;;;;;;AAAA;;GAAA,C;;;AAUO,SAASA,IAAOA,CAAA;EACrB;AAGF;;AAEC;AACM,MAAMC,GAAM,GAAC,OAAM;EACxB,IAAIC,EAAK;EACT,OAAO,MAAMA,EAAA;AACf;AAEA;;;;AAIC;AACM,SAASC,aAAcA,CAAAC,KAAc,EAA6B;EACvE,OAAOA,KAAU,SAAI,IAAI,OAAOA,KAAU;AAC5C;AAEA;;;;AAIC;AACM,SAASC,OAAqBA,CAAAD,KAAc,EAAgB;EACjE,IAAIE,KAAA,CAAMD,OAAO,IAAIC,KAAM,CAAAD,OAAO,CAACD,KAAQ;IACzC,OAAO,IAAI;;EAEb,MAAMG,IAAA,GAAOC,MAAO,CAAAC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACP,KAAA;EAC5C,IAAIG,IAAA,CAAKK,KAAK,CAAC,CAAG,SAAO,SAAa,IAAAL,IAAA,CAAKK,KAAK,CAAC,CAAC,OAAO,QAAU;IACjE,OAAO,IAAI;;EAEb,OAAO,KAAK;AACd;AAEA;;;;AAIC;AACM,SAASC,QAASA,CAAAT,KAAc,EAAsB;EAC3D,OAAOA,KAAA,KAAU,IAAI,IAAII,MAAO,CAAAC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACP,KAAW;AACrE;AAEA;;;;AAIA,SAASU,cAAeA,CAAAV,KAAc,EAAmB;EACvD,OAAQ,QAAOA,KAAA,KAAU,YAAYA,KAAiB,YAAAW,MAAK,KAAMC,QAAA,CAAS,CAACZ,KAAA;AAC7E;AAKA;;;;AAIC;AACM,SAASa,gBAAgBb,KAAc,EAAEc,YAAoB,EAAE;EACpE,OAAOJ,cAAA,CAAeV,KAAS,IAAAA,KAAA,GAAQc,YAAY;AACrD;AAEA;;;;AAIC;AACM,SAASC,eAAkBf,KAAoB,EAAEc,YAAe,EAAE;EACvE,OAAO,OAAOd,KAAA,KAAU,WAAc,GAAAc,YAAA,GAAed,KAAK;AAC5D;MAEagB,YAAe,GAAAA,CAAChB,KAAA,EAAwBiB,SACnD,YAAOjB,KAAA,KAAU,QAAY,IAAAA,KAAA,CAAMkB,QAAQ,CAAC,OAC1CC,UAAW,CAAAnB,KAAA,IAAS,MAClB,CAACA,KAAA,GAAQiB,SAAA;MAEFG,WAAc,GAAAA,CAACpB,KAAA,EAAwBiB,SAClD,YAAOjB,KAAA,KAAU,QAAY,IAAAA,KAAA,CAAMkB,QAAQ,CAAC,OAC1CC,UAAW,CAAAnB,KAAA,IAAS,MAAMiB,SACxB,IAACjB,KAAA;AAEP;;;;;;;AAOO,SAASqB,QACdA,CAAAC,EAAiB,EACjBC,IAAe,EACfC,OAAY,EACG;EACf,IAAIF,EAAM,WAAOA,EAAG,CAAAf,IAAI,KAAK,UAAY;IACvC,OAAOe,EAAA,CAAGG,KAAK,CAACD,OAAS,EAAAD,IAAA;;AAE7B;AAuBO,SAASG,KACdC,QAAiC,EACjCL,EAAoC,EACpCE,OAAY,EACZI,OAAiB,EACjB;EACA,IAAIC,CAAA,EAAWC,GAAa,EAAAC,IAAA;EAC5B,IAAI9B,OAAA,CAAQ0B,QAAW;IACrBG,GAAA,GAAMH,QAAA,CAASK,MAAM;IACrB,IAAIJ,OAAS;MACX,KAAKC,CAAI,GAAAC,GAAA,GAAM,CAAG,EAAAD,CAAA,IAAK,GAAGA,CAAK;QAC7BP,EAAA,CAAGf,IAAI,CAACiB,OAAA,EAASG,QAAQ,CAACE,CAAA,CAAE,EAAEA,CAAA;MAChC;KACK;MACL,KAAKA,CAAI,MAAGA,CAAI,GAAAC,GAAA,EAAKD,CAAK;QACxBP,EAAA,CAAGf,IAAI,CAACiB,OAAA,EAASG,QAAQ,CAACE,CAAA,CAAE,EAAEA,CAAA;MAChC;;GAEG,UAAIpB,QAAA,CAASkB,QAAW;IAC7BI,IAAO,GAAA3B,MAAA,CAAO2B,IAAI,CAACJ,QAAA;IACnBG,GAAA,GAAMC,IAAA,CAAKC,MAAM;IACjB,KAAKH,CAAI,MAAGA,CAAI,GAAAC,GAAA,EAAKD,CAAK;MACxBP,EAAA,CAAGf,IAAI,CAACiB,OAAS,EAAAG,QAAQ,CAACI,IAAI,CAACF,CAAA,CAAE,CAAC,EAAEE,IAAI,CAACF,CAAE;IAC7C;;AAEJ;AAEA;;;;;AAKC;AACM,SAASI,eAAeC,EAAqB,EAAEC,EAAqB,EAAE;EAC3E,IAAIN,CAAA,EAAWO,IAAA,EAAcC,EAAqB,EAAAC,EAAA;EAElD,IAAI,CAACJ,EAAA,IAAM,CAACC,EAAA,IAAMD,EAAA,CAAGF,MAAM,KAAKG,EAAG,CAAAH,MAAM,EAAE;IACzC,OAAO,KAAK;;EAGd,KAAKH,CAAA,GAAI,GAAGO,IAAO,GAAAF,EAAA,CAAGF,MAAM,EAAEH,CAAA,GAAIO,IAAM,IAAEP,CAAG;IAC3CQ,EAAK,GAAAH,EAAE,CAACL,CAAE;IACVS,EAAK,GAAAH,EAAE,CAACN,CAAE;IAEV,IAAIQ,EAAA,CAAGE,YAAY,KAAKD,EAAG,CAAAC,YAAY,IAAIF,EAAA,CAAGG,KAAK,KAAKF,EAAG,CAAAE,KAAK,EAAE;MAChE,OAAO,KAAK;;EAEhB;EAEA,OAAO,IAAI;AACb;AAEA;;;AAGC;AACM,SAASC,KAASA,CAAAC,MAAS,EAAK;EACrC,IAAIzC,OAAA,CAAQyC,MAAS;IACnB,OAAOA,MAAA,CAAOC,GAAG,CAACF,KAAA;;EAGpB,IAAIhC,QAAA,CAASiC,MAAS;IACpB,MAAME,MAAS,GAAAxC,MAAA,CAAOyC,MAAM,CAAC,IAAI;IACjC,MAAMd,IAAA,GAAO3B,MAAO,CAAA2B,IAAI,CAACW,MAAA;IACzB,MAAMI,IAAA,GAAOf,IAAA,CAAKC,MAAM;IACxB,IAAIe,CAAI;IAER,OAAOA,CAAA,GAAID,IAAM,IAAEC,CAAG;MACpBH,MAAM,CAACb,IAAI,CAACgB,CAAA,CAAE,CAAC,GAAGN,KAAM,CAAAC,MAAM,CAACX,IAAI,CAACgB,CAAA,CAAE,CAAC;IACzC;IAEA,OAAOH,MAAA;;EAGT,OAAOF,MAAA;AACT;AAEA,SAASM,WAAWC,GAAW,EAAE;EAC/B,OAAO,CAAC,aAAa,aAAa,cAAc,CAACC,OAAO,CAACD,GAAA,MAAS,CAAC;AACrE;AAEA;;;;;AAKO,SAASE,QAAQF,GAAW,EAAEL,MAAiB,EAAEF,MAAiB,EAAEU,OAAkB,EAAE;EAC7F,IAAI,CAACJ,UAAA,CAAWC,GAAM;IACpB;;EAGF,MAAMI,IAAA,GAAOT,MAAM,CAACK,GAAI;EACxB,MAAMK,IAAA,GAAOZ,MAAM,CAACO,GAAI;EAExB,IAAIxC,QAAA,CAAS4C,IAAS,KAAA5C,QAAA,CAAS6C,IAAO;;IAEpCC,KAAA,CAAMF,IAAA,EAAMC,IAAM,EAAAF,OAAA;GACb;IACLR,MAAM,CAACK,GAAI,IAAGR,KAAM,CAAAa,IAAA;;AAExB;AA0BO,SAASC,KAASA,CAAAX,MAAS,EAAEF,MAAmB,EAAEU,OAAsB,EAAa;EAC1F,MAAMI,OAAA,GAAUvD,OAAQ,CAAAyC,MAAA,IAAUA,MAAS,IAACA,MAAA,CAAO;EACnD,MAAMN,IAAA,GAAOoB,OAAA,CAAQxB,MAAM;EAE3B,IAAI,CAACvB,QAAA,CAASmC,MAAS;IACrB,OAAOA,MAAA;;EAGTQ,OAAA,GAAUA,OAAA,IAAW,EAAC;EACtB,MAAMK,MAAA,GAASL,OAAQ,CAAAK,MAAM,IAAIN,OAAA;EACjC,IAAIO,OAAA;EAEJ,KAAK,IAAI7B,CAAI,MAAGA,CAAI,GAAAO,IAAA,EAAM,EAAEP,CAAG;IAC7B6B,OAAU,GAAAF,OAAO,CAAC3B,CAAE;IACpB,IAAI,CAACpB,QAAA,CAASiD,OAAU;MACtB;;IAGF,MAAM3B,IAAA,GAAO3B,MAAO,CAAA2B,IAAI,CAAC2B,OAAA;IACzB,KAAK,IAAIX,CAAI,MAAGD,IAAO,GAAAf,IAAA,CAAKC,MAAM,EAAEe,CAAA,GAAID,IAAM,IAAEC,CAAG;MACjDU,MAAA,CAAO1B,IAAI,CAACgB,CAAE,GAAEH,MAAA,EAAQc,OAAS,EAAAN,OAAA;IACnC;EACF;EAEA,OAAOR,MAAA;AACT;AAgBO,SAASe,QAAWf,MAAS,EAAEF,MAAmB,EAAa;;EAEpE,OAAOa,KAAA,CAASX,MAAA,EAAQF,MAAQ;IAACe,MAAQ,EAAAG;EAAS;AACpD;AAEA;;;;AAIO,SAASA,SAAUA,CAAAX,GAAW,EAAEL,MAAiB,EAAEF,MAAiB,EAAE;EAC3E,IAAI,CAACM,UAAA,CAAWC,GAAM;IACpB;;EAGF,MAAMI,IAAA,GAAOT,MAAM,CAACK,GAAI;EACxB,MAAMK,IAAA,GAAOZ,MAAM,CAACO,GAAI;EAExB,IAAIxC,QAAA,CAAS4C,IAAS,KAAA5C,QAAA,CAAS6C,IAAO;IACpCK,OAAA,CAAQN,IAAM,EAAAC,IAAA;GACT,UAAI,CAAClD,MAAA,CAAOC,SAAS,CAACwD,cAAc,CAACtD,IAAI,CAACqC,MAAA,EAAQK,GAAM;IAC7DL,MAAM,CAACK,GAAI,IAAGR,KAAM,CAAAa,IAAA;;AAExB;AAEA;;;AAGO,SAASQ,YAAYC,KAAa,EAAE/D,KAAc,EAAEgE,QAAgB,EAAEN,OAAe,EAAE;EAC5F,IAAI1D,KAAA,KAAUiE,SAAW;IACvBC,OAAA,CAAQC,IAAI,CAACJ,KAAA,GAAQ,KAAQ,GAAAC,QAAA,GAC3B,kCAAkCN,OAAU;;AAElD;AAEA;AACA,MAAMU,YAAe;;EAEnB,IAAIC,CAAK,IAAAA,CAAA;;EAETC,CAAG,EAAAC,CAAK,IAAAA,CAAA,CAAED,CAAC;EACXE,CAAG,EAAAD,CAAK,IAAAA,CAAA,CAAEC;AACZ;AAEA;;AAEC;AACM,SAASC,SAAUA,CAAAxB,GAAW,EAAE;EACrC,MAAMyB,KAAA,GAAQzB,GAAI,CAAA0B,KAAK,CAAC;EACxB,MAAM5C,IAAA,GAAiB,EAAE;EACzB,IAAI6C,GAAM;EACV,KAAK,MAAMC,IAAA,IAAQH,KAAO;IACxBE,GAAO,IAAAC,IAAA;IACP,IAAID,GAAA,CAAI1D,QAAQ,CAAC,IAAO;MACtB0D,GAAA,GAAMA,GAAI,CAAApE,KAAK,CAAC,GAAG,CAAC,CAAK;KACpB;MACLuB,IAAA,CAAK+C,IAAI,CAACF,GAAA;MACVA,GAAM;;EAEV;EACA,OAAO7C,IAAA;AACT;AAEA,SAASgD,gBAAgB9B,GAAW,EAAE;EACpC,MAAMlB,IAAA,GAAO0C,SAAU,CAAAxB,GAAA;EACvB,OAAO+B,GAAO;IACZ,KAAK,MAAMjC,CAAA,IAAKhB,IAAM;MACpB,IAAIgB,CAAA,KAAM,EAAI;QAGZ;;MAEFiC,GAAM,GAAAA,GAAA,IAAOA,GAAG,CAACjC,CAAE;IACrB;IACA,OAAOiC,GAAA;EACT;AACF;AAEO,SAASC,iBAAiBD,GAAc,EAAE/B,GAAW,EAAO;EACjE,MAAMiC,QAAA,GAAWd,YAAY,CAACnB,GAAI,MAAKmB,YAAY,CAACnB,GAAA,CAAI,GAAG8B,eAAA,CAAgB9B,GAAG;EAC9E,OAAOiC,QAAS,CAAAF,GAAA;AAClB;AAEA;;AAEC;AACM,SAASG,WAAYA,CAAAC,GAAW,EAAE;EACvC,OAAOA,GAAA,CAAIC,MAAM,CAAC,GAAGC,WAAW,EAAK,GAAAF,GAAA,CAAI5E,KAAK,CAAC;AACjD;MAGa+E,OAAU,GAACvF,KAAmB,WAAOA,KAAA,KAAU;MAE/CwF,UAAa,GAACxF,KAAqD,WAAOA,KAAA,KAAU;AAEjG;AACa,MAAAyF,SAAA,GAAYA,CAAIC,CAAA,EAAWC,CAAc;EACpD,IAAID,CAAE,CAAAE,IAAI,KAAKD,CAAA,CAAEC,IAAI,EAAE;IACrB,OAAO,KAAK;;EAGd,KAAK,MAAMC,IAAA,IAAQH,CAAG;IACpB,IAAI,CAACC,CAAA,CAAEG,GAAG,CAACD,IAAO;MAChB,OAAO,KAAK;;EAEhB;EAEA,OAAO,IAAI;AACb;AAEA;;;AAGC;AACM,SAASE,aAAcA,CAAAC,CAAa,EAAE;EAC3C,OAAOA,CAAA,CAAE7F,IAAI,KAAK,SAAa,IAAA6F,CAAA,CAAE7F,IAAI,KAAK,WAAW6F,CAAE,CAAA7F,IAAI,KAAK;AAClE;;AC5ZA;;;AAGC;AAEM,MAAM8F,EAAK,GAAAC,IAAA,CAAKD,EAAA;AAChB,MAAME,GAAM,OAAIF,EAAA;AAChB,MAAMG,KAAQ,GAAAD,GAAA,GAAMF,EAAA;AACd,MAAAI,QAAA,GAAW1F,MAAO,CAAA2F,iBAAA;AACxB,MAAMC,WAAc,GAAAN,EAAA,GAAK;AACzB,MAAMO,OAAU,GAAAP,EAAA,GAAK;AACrB,MAAMQ,UAAa,GAAAR,EAAA,GAAK;AAClB,MAAAS,aAAA,GAAgBT,EAAK,OAAI;AAEzB,MAAAU,KAAA,GAAQT,IAAK,CAAAS,KAAA;AACb,MAAAC,IAAA,GAAOV,IAAK,CAAAU,IAAA;AAElB,SAASC,YAAaA,CAAAvC,CAAS,EAAEE,CAAS,EAAEsC,OAAe,EAAE;EAClE,OAAOZ,IAAK,CAAAa,GAAG,CAACzC,CAAA,GAAIE,CAAK,IAAAsC,OAAA;AAC3B;AAEA;;AAEC;AACM,SAASE,OAAQA,CAAAC,KAAa,EAAE;EACrC,MAAMC,YAAA,GAAehB,IAAK,CAAAiB,KAAK,CAACF,KAAA;EAChCA,KAAA,GAAQJ,YAAA,CAAaI,KAAO,EAAAC,YAAA,EAAcD,KAAQ,WAAQC,YAAA,GAAeD,KAAK;EAC9E,MAAMG,SAAA,GAAYlB,IAAA,CAAKmB,GAAG,CAAC,IAAInB,IAAK,CAAAoB,KAAK,CAACX,KAAM,CAAAM,KAAA;EAChD,MAAMM,QAAA,GAAWN,KAAQ,GAAAG,SAAA;EACzB,MAAMI,YAAA,GAAeD,QAAY,QAAI,CAAI,GAAAA,QAAA,IAAY,IAAI,CAAI,GAAAA,QAAA,IAAY,CAAI,OAAI,EAAE;EACnF,OAAOC,YAAe,GAAAJ,SAAA;AACxB;AAEA;;;AAGC;AACM,SAASK,UAAWA,CAAAzH,KAAa,EAAE;EACxC,MAAM0H,MAAA,GAAmB,EAAE;EAC3B,MAAMC,IAAA,GAAOzB,IAAK,CAAAyB,IAAI,CAAC3H,KAAA;EACvB,IAAI6B,CAAA;EAEJ,KAAKA,CAAI,MAAGA,CAAI,GAAA8F,IAAA,EAAM9F,CAAK;IACzB,IAAI7B,KAAA,GAAQ6B,CAAA,KAAM,CAAG;MACnB6F,MAAA,CAAO5C,IAAI,CAACjD,CAAA;MACZ6F,MAAO,CAAA5C,IAAI,CAAC9E,KAAQ,GAAA6B,CAAA;;EAExB;EACA,IAAI8F,IAAU,MAAAA,IAAO,KAAI;IACvBD,MAAA,CAAO5C,IAAI,CAAC6C,IAAA;;EAGdD,MAAA,CAAOE,IAAI,CAAC,CAAClC,CAAA,EAAGC,CAAM,KAAAD,CAAA,GAAIC,CAAA,EAAGkC,GAAG;EAChC,OAAOH,MAAA;AACT;AAEO,SAASI,QAASA,CAAAC,CAAU,EAAe;EAChD,OAAO,CAACC,KAAA,CAAM7G,UAAW,CAAA4G,CAAA,MAAiBnH,QAAS,CAAAmH,CAAA;AACrD;AAEO,SAASE,YAAY3D,CAAS,EAAEwC,OAAe,EAAE;EACtD,MAAMoB,OAAA,GAAUhC,IAAK,CAAAiB,KAAK,CAAC7C,CAAA;EAC3B,OAAO4D,OAAY,GAAApB,OAAA,IAAYxC,CAAO,IAAC4D,OAAA,GAAUpB,OAAY,IAAAxC,CAAA;AAC/D;AAEA;;;AAGO,SAAS6D,kBACdA,CAAAC,KAA+B,EAC/BxF,MAAoC,EACpCyF,QAAgB,EAChB;EACA,IAAIxG,CAAA,EAAWO,IAAc,EAAApC,KAAA;EAE7B,KAAK6B,CAAA,GAAI,GAAGO,IAAO,GAAAgG,KAAA,CAAMpG,MAAM,EAAEH,CAAA,GAAIO,IAAA,EAAMP,CAAK;IAC9C7B,KAAA,GAAQoI,KAAK,CAACvG,CAAE,EAACwG,QAAS;IAC1B,IAAI,CAACL,KAAA,CAAMhI,KAAQ;MACjB4C,MAAA,CAAO0F,GAAG,GAAGpC,IAAA,CAAKoC,GAAG,CAAC1F,MAAA,CAAO0F,GAAG,EAAEtI,KAAA;MAClC4C,MAAA,CAAO2F,GAAG,GAAGrC,IAAA,CAAKqC,GAAG,CAAC3F,MAAA,CAAO2F,GAAG,EAAEvI,KAAA;;EAEtC;AACF;AAEO,SAASwI,SAAUA,CAAAC,OAAe,EAAE;EACzC,OAAOA,OAAA,IAAWxC,EAAA,GAAK,GAAE;AAC3B;AAEO,SAASyC,SAAUA,CAAAC,OAAe,EAAE;EACzC,OAAOA,OAAA,IAAW,MAAM1C,EAAC;AAC3B;AAEA;;;;;;AAMC;AACM,SAAS2C,cAAeA,CAAAtE,CAAS,EAAE;EACxC,IAAI,CAAC5D,cAAA,CAAe4D,CAAI;IACtB;;EAEF,IAAI0B,CAAI;EACR,IAAI6C,CAAI;EACR,OAAO3C,IAAA,CAAKiB,KAAK,CAAC7C,CAAI,GAAA0B,CAAA,IAAKA,CAAA,KAAM1B,CAAG;IAClC0B,CAAK;IACL6C,CAAA;EACF;EACA,OAAOA,CAAA;AACT;AAEA;AACO,SAASC,kBACdC,WAAkB,EAClBC,UAAiB,EACjB;EACA,MAAMC,mBAAsB,GAAAD,UAAA,CAAW1E,CAAC,GAAGyE,WAAA,CAAYzE,CAAC;EACxD,MAAM4E,mBAAsB,GAAAF,UAAA,CAAWxE,CAAC,GAAGuE,WAAA,CAAYvE,CAAC;EACxD,MAAM2E,wBAAA,GAA2BjD,IAAK,CAAAyB,IAAI,CAACsB,mBAAA,GAAsBA,mBAAA,GAAsBC,mBAAsB,GAAAA,mBAAA;EAE7G,IAAIE,KAAQ,GAAAlD,IAAA,CAAKmD,KAAK,CAACH,mBAAqB,EAAAD,mBAAA;EAE5C,IAAIG,KAAA,GAAS,CAAC,MAAMnD,EAAK;IACvBmD,KAAA,IAASjD,GAAA;;;EAGX,OAAO;IACLiD,KAAA;IACAE,QAAU,EAAAH;EACZ;AACF;AAEO,SAASI,sBAAsBC,GAAU,EAAEC,GAAU,EAAE;EAC5D,OAAOvD,IAAA,CAAKyB,IAAI,CAACzB,IAAA,CAAKmB,GAAG,CAACoC,GAAA,CAAInF,CAAC,GAAGkF,GAAA,CAAIlF,CAAC,EAAE,KAAK4B,IAAA,CAAKmB,GAAG,CAACoC,GAAA,CAAIjF,CAAC,GAAGgF,GAAI,CAAAhF,CAAC,EAAE;AACxE;AAEA;;;AAGC;AACM,SAASkF,WAAWhE,CAAS,EAAEC,CAAS,EAAE;EAC/C,OAAO,CAACD,CAAA,GAAIC,CAAI,GAAAS,KAAI,IAAKD,GAAM,GAAAF,EAAA;AACjC;AAEA;;;AAGC;AACM,SAAS0D,eAAgBA,CAAAjE,CAAS,EAAE;EACzC,OAAO,CAACA,CAAI,GAAAS,GAAA,GAAMA,GAAE,IAAKA,GAAA;AAC3B;AAEA;;;AAGO,SAASyD,cAAcR,KAAa,EAAES,KAAa,EAAEC,GAAW,EAAEC,qBAA+B,EAAE;EACxG,MAAMrE,CAAA,GAAIiE,eAAgB,CAAAP,KAAA;EAC1B,MAAMY,CAAA,GAAIL,eAAgB,CAAAE,KAAA;EAC1B,MAAM7D,CAAA,GAAI2D,eAAgB,CAAAG,GAAA;EAC1B,MAAMG,YAAA,GAAeN,eAAA,CAAgBK,CAAI,GAAAtE,CAAA;EACzC,MAAMwE,UAAA,GAAaP,eAAA,CAAgB3D,CAAI,GAAAN,CAAA;EACvC,MAAMyE,YAAA,GAAeR,eAAA,CAAgBjE,CAAI,GAAAsE,CAAA;EACzC,MAAMI,UAAA,GAAaT,eAAA,CAAgBjE,CAAI,GAAAM,CAAA;EACvC,OAAON,CAAA,KAAMsE,CAAA,IAAKtE,CAAM,KAAAM,CAAA,IAAM+D,qBAAA,IAAyBC,CAAM,KAAAhE,CAAA,IACvDiE,YAAe,GAAAC,UAAA,IAAcC,YAAe,GAAAC,UAAA;AACpD;AAEA;;;;;;;AAOO,SAASC,WAAYA,CAAArK,KAAa,EAAEsI,GAAW,EAAEC,GAAW,EAAE;EACnE,OAAOrC,IAAA,CAAKqC,GAAG,CAACD,GAAA,EAAKpC,IAAK,CAAAoC,GAAG,CAACC,GAAK,EAAAvI,KAAA;AACrC;AAEA;;;AAGC;AACM,SAASsK,WAAYA,CAAAtK,KAAa,EAAE;EACzC,OAAOqK,WAAA,CAAYrK,KAAO,GAAC,KAAO;AACpC;AAEA;;;;;;;AAOO,SAASuK,WAAWvK,KAAa,EAAE6J,KAAa,EAAEC,GAAW,EAAkB;EAAA,IAAhBhD,OAAU,GAAA0D,SAAA,CAAAxI,MAAA,QAAAwI,SAAA,QAAAvG,SAAA,GAAAuG,SAAA,UAAI;EAClF,OAAOxK,KAAS,IAAAkG,IAAA,CAAKoC,GAAG,CAACuB,KAAO,EAAAC,GAAA,IAAOhD,OAAW,IAAA9G,KAAA,IAASkG,IAAK,CAAAqC,GAAG,CAACsB,KAAA,EAAOC,GAAO,IAAAhD,OAAA;AACpF;ACpLO,SAAS2D,OACdA,CAAAC,KAAgB,EAChB1K,KAAa,EACb2K,GAAgC,EAChC;EACAA,GAAM,GAAAA,GAAA,KAASnI,KAAA,IAAUkI,KAAK,CAAClI,KAAA,CAAM,GAAGxC,KAAI;EAC5C,IAAI4K,EAAA,GAAKF,KAAM,CAAA1I,MAAM,GAAG;EACxB,IAAI6I,EAAK;EACT,IAAIC,GAAA;EAEJ,OAAOF,EAAA,GAAKC,EAAA,GAAK,CAAG;IAClBC,GAAM,GAACD,EAAA,GAAKD,EAAO;IACnB,IAAID,GAAA,CAAIG,GAAM;MACZD,EAAK,GAAAC,GAAA;KACA;MACLF,EAAK,GAAAE,GAAA;;EAET;EAEA,OAAO;IAACD,EAAA;IAAID;EAAE;AAChB;AAEA;;;;;;;AAOC;AACM,MAAMG,YAAe,GAAAA,CAC1BL,KACA,EAAAzH,GAAA,EACAjD,KACA,EAAAgL,IAAA,KAEAP,OAAQ,CAAAC,KAAA,EAAO1K,KAAO,EAAAgL,IAAA,GAClBxI,KAAS;EACT,MAAMyI,EAAK,GAAAP,KAAK,CAAClI,KAAA,CAAM,CAACS,GAAI;EAC5B,OAAOgI,EAAA,GAAKjL,KAAS,IAAAiL,EAAA,KAAOjL,KAAS,IAAA0K,KAAK,CAAClI,KAAQ,KAAE,CAACS,GAAA,CAAI,KAAKjD,KAAA;CAE/D,GAAAwC,KAAA,IAASkI,KAAK,CAAClI,KAAA,CAAM,CAACS,GAAA,CAAI,GAAGjD,KAAK;AAExC;;;;;;AAMC;AACY,MAAAkL,aAAA,GAAgBA,CAC3BR,KACA,EAAAzH,GAAA,EACAjD,KAAA,KAEAyK,OAAQ,CAAAC,KAAA,EAAO1K,KAAO,EAAAwC,KAAA,IAASkI,KAAK,CAAClI,KAAA,CAAM,CAACS,GAAA,CAAI,IAAIjD,KAAO;AAE7D;;;;;;;AAOO,SAASmL,cAAeA,CAAAC,MAAgB,EAAE9C,GAAW,EAAEC,GAAW,EAAE;EACzE,IAAIsB,KAAQ;EACZ,IAAIC,GAAA,GAAMsB,MAAA,CAAOpJ,MAAM;EAEvB,OAAO6H,KAAA,GAAQC,GAAO,IAAAsB,MAAM,CAACvB,KAAA,CAAM,GAAGvB,GAAK;IACzCuB,KAAA;EACF;EACA,OAAOC,GAAA,GAAMD,KAAS,IAAAuB,MAAM,CAACtB,GAAM,KAAE,GAAGvB,GAAK;IAC3CuB,GAAA;EACF;EAEA,OAAOD,KAAA,GAAQ,CAAK,IAAAC,GAAA,GAAMsB,MAAO,CAAApJ,MAAM,GACnCoJ,MAAA,CAAO5K,KAAK,CAACqJ,KAAO,EAAAC,GAAA,IACpBsB,MAAM;AACZ;AAEA,MAAMC,WAAc,IAAC,QAAQ,OAAO,SAAS,UAAU,UAAU;AAgB1D,SAASC,kBAAkBlD,KAAK,EAAEmD,QAAQ,EAAE;EACjD,IAAInD,KAAA,CAAMoD,QAAQ,EAAE;IAClBpD,KAAA,CAAMoD,QAAQ,CAACC,SAAS,CAAC3G,IAAI,CAACyG,QAAA;IAC9B;;EAGFnL,MAAO,CAAAsL,cAAc,CAACtD,KAAA,EAAO,UAAY;IACvCuD,YAAA,EAAc,IAAI;IAClBC,UAAA,EAAY,KAAK;IACjB5L,KAAO;MACLyL,SAAW,GAACF,QAAA;IACd;EACF;EAEAF,WAAY,CAAAQ,OAAO,CAAE5I,GAAQ;IAC3B,MAAM6I,MAAA,GAAS,YAAY3G,WAAY,CAAAlC,GAAA;IACvC,MAAM8I,IAAA,GAAO3D,KAAK,CAACnF,GAAI;IAEvB7C,MAAO,CAAAsL,cAAc,CAACtD,KAAA,EAAOnF,GAAK;MAChC0I,YAAA,EAAc,IAAI;MAClBC,UAAA,EAAY,KAAK;MACjB5L,KAAMA,CAAA,EAAS;QAAA,SAAAgM,IAAA,GAAAxB,SAAA,CAAAxI,MAAA,EAANT,IAAI,OAAArB,KAAA,CAAA8L,IAAA,GAAAC,IAAA,MAAAA,IAAA,GAAAD,IAAA,EAAAC,IAAA;UAAJ1K,IAAI,CAAA0K,IAAA,IAAAzB,SAAA,CAAAyB,IAAA;QAAA;QACX,MAAMC,GAAM,GAAAH,IAAA,CAAKtK,KAAK,CAAC,IAAI,EAAEF,IAAA;QAE7B6G,KAAA,CAAMoD,QAAQ,CAACC,SAAS,CAACI,OAAO,CAAEM,MAAW;UAC3C,IAAI,OAAOA,MAAM,CAACL,MAAA,CAAO,KAAK,UAAY;YACxCK,MAAM,CAACL,MAAA,CAAO,CAAI,GAAAvK,IAAA;;QAEtB;QAEA,OAAO2K,GAAA;MACT;IACF;EACF;AACF;AAQO,SAASE,oBAAoBhE,KAAK,EAAEmD,QAAQ,EAAE;EACnD,MAAMc,IAAA,GAAOjE,KAAA,CAAMoD,QAAQ;EAC3B,IAAI,CAACa,IAAM;IACT;;EAGF,MAAMZ,SAAA,GAAYY,IAAA,CAAKZ,SAAS;EAChC,MAAMjJ,KAAA,GAAQiJ,SAAU,CAAAvI,OAAO,CAACqI,QAAA;EAChC,IAAI/I,KAAA,KAAU,CAAC,CAAG;IAChBiJ,SAAU,CAAAa,MAAM,CAAC9J,KAAO;;EAG1B,IAAIiJ,SAAA,CAAUzJ,MAAM,GAAG,CAAG;IACxB;;EAGFqJ,WAAY,CAAAQ,OAAO,CAAE5I,GAAQ;IAC3B,OAAOmF,KAAK,CAACnF,GAAI;EACnB;EAEA,OAAOmF,KAAA,CAAMoD,QAAQ;AACvB;AAEA;;AAEC;AACM,SAASe,YAAgBA,CAAAC,KAAU,EAAE;EAC1C,MAAMC,GAAA,GAAM,IAAIC,GAAO,CAAAF,KAAA;EAEvB,IAAIC,GAAI,CAAA7G,IAAI,KAAK4G,KAAA,CAAMxK,MAAM,EAAE;IAC7B,OAAOwK,KAAA;;EAGT,OAAOtM,KAAA,CAAMyM,IAAI,CAACF,GAAA;AACpB;AC1LO,SAASG,UAAWA,CAAAC,SAAiB,EAAEC,SAAiB,EAAEC,UAAkB,EAAE;EACnF,OAAOD,SAAA,GAAY,GAAM,GAAAD,SAAA,GAAY,KAAQ,GAAAE,UAAA;AAC/C;AAEA;;AAEA;AACa,MAAAC,gBAAA,GAAoB,YAAW;EAC1C,IAAI,OAAOC,MAAA,KAAW,WAAa;IACjC,OAAO,UAAS5L,QAAQ,EAAE;MACxB,OAAOA,QAAA;IACT;;EAEF,OAAO4L,MAAA,CAAOC,qBAAqB;AACrC,CAAK;AAEL;;;AAGC;AACM,SAASC,UACd7L,EAA4B,EAC5BE,OAAY,EACZ;EACA,IAAI4L,SAAA,GAAY,EAAE;EAClB,IAAIC,OAAA,GAAU,KAAK;EAEnB,OAAO,YAAyB;IAAA,SAAAC,KAAA,GAAA9C,SAAA,CAAAxI,MAAA,EAAbT,IAAW,OAAArB,KAAA,CAAAoN,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAXhM,IAAW,CAAAgM,KAAA,IAAA/C,SAAA,CAAA+C,KAAA;IAAA;;IAE5BH,SAAY,GAAA7L,IAAA;IACZ,IAAI,CAAC8L,OAAS;MACZA,OAAA,GAAU,IAAI;MACdL,gBAAiB,CAAAzM,IAAI,CAAC0M,MAAA,EAAQ,MAAM;QAClCI,OAAA,GAAU,KAAK;QACf/L,EAAG,CAAAG,KAAK,CAACD,OAAS,EAAA4L,SAAA;MACpB;;EAEJ;AACF;AAEA;;AAEC;AACM,SAASI,SAAmClM,EAA4B,EAAEmM,KAAa,EAAE;EAC9F,IAAIC,OAAA;EACJ,OAAO,YAAyB;IAAA,SAAAC,KAAA,GAAAnD,SAAA,CAAAxI,MAAA,EAAbT,IAAW,OAAArB,KAAA,CAAAyN,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAXrM,IAAW,CAAAqM,KAAA,IAAApD,SAAA,CAAAoD,KAAA;IAAA;IAC5B,IAAIH,KAAO;MACTI,YAAa,CAAAH,OAAA;MACbA,OAAU,GAAAI,UAAA,CAAWxM,EAAA,EAAImM,KAAO,EAAAlM,IAAA;KAC3B;MACLD,EAAG,CAAAG,KAAK,CAAC,IAAI,EAAEF,IAAA;;IAEjB,OAAOkM,KAAA;EACT;AACF;AAEA;;;AAGC;AACM,MAAMM,kBAAqB,GAACC,KAAsC,IAAAA,KAAA,KAAU,OAAU,YAASA,KAAU,aAAQ,OAAU;AAElI;;;AAGC;AACY,MAAAC,cAAA,GAAiBA,CAACD,KAAmC,EAAAnE,KAAA,EAAeC,GAAA,KAAgBkE,KAAU,eAAUnE,KAAA,GAAQmE,KAAU,aAAQlE,GAAA,GAAM,CAACD,KAAA,GAAQC,GAAE,IAAK;AAErK;;;AAGC;AACY,MAAAoE,MAAA,GAASA,CAACF,KAAoC,EAAAG,IAAA,EAAcC,KAAA,EAAeC,GAAiB;EACvG,MAAMC,KAAA,GAAQD,GAAM,YAAS,OAAO;EACpC,OAAOL,KAAA,KAAUM,KAAQ,GAAAF,KAAA,GAAQJ,KAAU,gBAAW,CAACG,IAAO,GAAAC,KAAI,IAAK,IAAID,IAAI;AACjF;AAEA;;;;AAIO,SAASI,gCAAiCA,CAAAC,IAAmC,EAAEC,MAAsB,EAAEC,kBAA2B,EAAE;EACzI,MAAMC,UAAA,GAAaF,MAAA,CAAOzM,MAAM;EAEhC,IAAI6H,KAAQ;EACZ,IAAI+E,KAAQ,GAAAD,UAAA;EAEZ,IAAIH,IAAA,CAAKK,OAAO,EAAE;IAChB,MAAM;MAACC,MAAA;MAAQC;IAAA,CAAQ,GAAGP,IAAA;IAC1B,MAAMQ,IAAA,GAAOF,MAAA,CAAOE,IAAI;IACxB,MAAM;MAAC1G,GAAG;MAAEC,GAAG;MAAE0G,UAAU;MAAEC;IAAU,CAAC,GAAGJ,MAAA,CAAOK,aAAa;IAE/D,IAAIF,UAAY;MACdpF,KAAA,GAAQQ,WAAY,CAAAnE,IAAA,CAAKoC,GAAG;MAAA;MAE1ByC,YAAA,CAAagE,OAAA,EAASD,MAAO,CAAAE,IAAI,EAAE1G,GAAK,EAAAuC,EAAE;MAAA;MAE1C6D,kBAAqB,GAAAC,UAAA,GAAa5D,YAAa,CAAA0D,MAAA,EAAQO,IAAM,EAAAF,MAAA,CAAOM,gBAAgB,CAAC9G,GAAM,GAAAuC,EAAE,CAC/F,KAAG8D,UAAa;;IAElB,IAAIO,UAAY;MACdN,KAAA,GAAQvE,WAAY,CAAAnE,IAAA,CAAKqC,GAAG;MAAA;MAE1BwC,YAAa,CAAAgE,OAAA,EAASD,MAAO,CAAAE,IAAI,EAAEzG,GAAA,EAAK,IAAI,CAAE,CAAAqC,EAAE,GAAG;MAAA;MAEnD8D,kBAAA,GAAqB,CAAI,GAAA3D,YAAA,CAAa0D,MAAQ,EAAAO,IAAA,EAAMF,MAAA,CAAOM,gBAAgB,CAAC7G,GAAM,OAAI,EAAEqC,EAAE,GAAG,CAAC,GAChGf,KAAA,EAAO8E,UAAc,IAAA9E,KAAA;KAChB;MACL+E,KAAA,GAAQD,UAAa,GAAA9E,KAAA;;;EAIzB,OAAO;IAACA,KAAA;IAAO+E;EAAK;AACtB;AAEA;;;;;AAKC;AACM,SAASS,mBAAoBA,CAAAb,IAAI,EAAE;EACxC,MAAM;IAACc,MAAM;IAAEC,MAAA;IAAQC;EAAA,CAAa,GAAGhB,IAAA;EACvC,MAAMiB,SAAY;IAChBC,IAAA,EAAMJ,MAAA,CAAOhH,GAAG;IAChBqH,IAAA,EAAML,MAAA,CAAO/G,GAAG;IAChBqH,IAAA,EAAML,MAAA,CAAOjH,GAAG;IAChBuH,IAAA,EAAMN,MAAA,CAAOhH;EACf;EACA,IAAI,CAACiH,YAAc;IACjBhB,IAAA,CAAKgB,YAAY,GAAGC,SAAA;IACpB,OAAO,IAAI;;EAEb,MAAMK,OAAA,GAAUN,YAAA,CAAaE,IAAI,KAAKJ,MAAA,CAAOhH,GAAG,IAC7CkH,YAAa,CAAAG,IAAI,KAAKL,MAAA,CAAO/G,GAAG,IAChCiH,YAAA,CAAaI,IAAI,KAAKL,MAAO,CAAAjH,GAAG,IAChCkH,YAAa,CAAAK,IAAI,KAAKN,MAAA,CAAOhH,GAAG;EAEnCnI,MAAO,CAAA2P,MAAM,CAACP,YAAc,EAAAC,SAAA;EAC5B,OAAOK,OAAA;AACT;AC/IA,MAAME,MAAS,GAACC,CAAc,IAAAA,CAAA,KAAM,KAAKA,CAAM;AAC/C,MAAMC,SAAA,GAAYA,CAACD,CAAA,EAAWjG,CAAW,EAAAnB,CAAA,KAAc,EAAE3C,IAAA,CAAKmB,GAAG,CAAC,CAAG,QAAM4I,CAAK,MAAM,IAAA/J,IAAA,CAAKiK,GAAG,CAAC,CAACF,CAAI,GAAAjG,CAAA,IAAK7D,GAAA,GAAM0C,CAAC;AAChH,MAAMuH,UAAA,GAAaA,CAACH,CAAW,EAAAjG,CAAA,EAAWnB,CAAA,KAAc3C,IAAK,CAAAmB,GAAG,CAAC,CAAG,GAAC,KAAK4I,CAAK,IAAA/J,IAAA,CAAKiK,GAAG,CAAE,CAAAF,CAAI,GAAAjG,CAAA,IAAK7D,GAAA,GAAM0C,CAAK;AAE7G;;;;AAIC;AAAA,MACKwH,OAAU;EACdC,MAAA,EAASL,CAAc,IAAAA,CAAA;EAEvBM,UAAY,EAACN,CAAA,IAAcA,CAAI,GAAAA,CAAA;EAE/BO,WAAA,EAAcP,CAAc,KAACA,CAAK,IAAAA,CAAA,GAAI;EAEtCQ,aAAe,EAACR,CAAA,IAAgB,CAAAA,CAAK,OAAE,IAAK,IACxC,GAAM,GAAAA,CAAA,GAAIA,CAAA,GACV,CAAC,OAAQ,EAAEA,CAAA,IAAMA,CAAI,KAAK,KAAE;EAEhCS,WAAa,EAACT,CAAc,IAAAA,CAAA,GAAIA,CAAI,GAAAA,CAAA;EAEpCU,YAAc,EAACV,CAAA,IAAc,CAACA,CAAA,IAAK,KAAKA,CAAA,GAAIA,CAAI;EAEhDW,cAAgB,EAACX,CAAA,IAAgB,CAAAA,CAAK,OAAE,IAAK,IACzC,GAAM,GAAAA,CAAA,GAAIA,CAAA,GAAIA,CACd,UAAQ,CAAAA,CAAA,IAAK,KAAKA,CAAA,GAAIA,CAAI,KAAE;EAEhCY,WAAA,EAAcZ,CAAA,IAAcA,CAAI,GAAAA,CAAA,GAAIA,CAAI,GAAAA,CAAA;EAExCa,YAAA,EAAeb,CAAA,IAAc,EAAE,CAACA,CAAK,SAAKA,CAAA,GAAIA,CAAI,GAAAA,CAAA,GAAI;EAEtDc,cAAgB,EAACd,CAAc,IAAC,CAACA,CAAK,OAAE,IAAK,IACzC,GAAM,GAAAA,CAAA,GAAIA,CAAI,GAAAA,CAAA,GAAIA,CAAA,GAClB,CAAC,OAAQ,CAAAA,CAAA,IAAK,KAAKA,CAAI,GAAAA,CAAA,GAAIA,CAAI,KAAE;EAErCe,WAAA,EAAcf,CAAA,IAAcA,CAAI,GAAAA,CAAA,GAAIA,CAAA,GAAIA,CAAI,GAAAA,CAAA;EAE5CgB,YAAc,EAAChB,CAAc,IAAC,CAAAA,CAAA,IAAK,KAAKA,CAAA,GAAIA,CAAI,GAAAA,CAAA,GAAIA,CAAI;EAExDiB,cAAgB,EAACjB,CAAc,IAAC,CAACA,CAAK,OAAE,IAAK,IACzC,GAAM,GAAAA,CAAA,GAAIA,CAAI,GAAAA,CAAA,GAAIA,CAAI,GAAAA,CAAA,GACtB,GAAO,KAACA,CAAK,SAAKA,CAAA,GAAIA,CAAI,GAAAA,CAAA,GAAIA,CAAI,KAAE;EAExCkB,UAAA,EAAalB,CAAc,KAAC/J,IAAA,CAAKkL,GAAG,CAACnB,CAAA,GAAIzJ,OAAW;EAEpD6K,WAAA,EAAcpB,CAAA,IAAc/J,IAAK,CAAAiK,GAAG,CAACF,CAAI,GAAAzJ,OAAA;EAEzC8K,aAAe,EAACrB,CAAc,KAAC,GAAO,IAAA/J,IAAA,CAAKkL,GAAG,CAACnL,EAAK,GAAAgK,CAAA,IAAK;EAEzDsB,UAAA,EAAatB,CAAA,IAAcA,CAAC,KAAM,IAAK,CAAI,GAAA/J,IAAA,CAAKmB,GAAG,CAAC,CAAG,QAAM4I,CAAA,GAAI,EAAG;EAEpEuB,WAAA,EAAcvB,CAAA,IAAcA,CAAC,KAAM,IAAK,CAAI,IAAC/J,IAAK,CAAAmB,GAAG,CAAC,GAAG,CAAC,KAAK4I,CAAA,IAAK,CAAC;EAErEwB,aAAA,EAAgBxB,CAAA,IAAcD,MAAO,CAAAC,CAAA,IAAKA,CAAA,GAAIA,CAAI,SAC9C,GAAM,GAAA/J,IAAA,CAAKmB,GAAG,CAAC,CAAG,QAAM4I,CAAI,OAAI,MAChC,GAAO,KAAC/J,IAAA,CAAKmB,GAAG,CAAC,GAAG,CAAC,MAAM4I,CAAI,OAAI,MAAM,EAAE;EAE/CyB,UAAA,EAAazB,CAAA,IAAcA,CAAC,IAAK,IAAKA,CAAI,KAAE/J,IAAA,CAAKyB,IAAI,CAAC,IAAIsI,CAAI,GAAAA,CAAA,IAAK,EAAE;EAErE0B,WAAa,EAAC1B,CAAc,IAAA/J,IAAA,CAAKyB,IAAI,CAAC,IAAI,CAACsI,CAAK,SAAKA,CAAA;EAErD2B,aAAA,EAAgB3B,CAAA,IAAc,CAAEA,CAAK,OAAE,IAAK,IACxC,CAAC,OAAO/J,IAAA,CAAKyB,IAAI,CAAC,IAAIsI,CAAI,GAAAA,CAAA,IAAK,KAC/B,GAAO,IAAA/J,IAAA,CAAKyB,IAAI,CAAC,CAAI,GAAC,CAAAsI,CAAK,SAAKA,CAAA,IAAK,EAAE;EAE3C4B,aAAe,EAAC5B,CAAA,IAAcD,MAAO,CAAAC,CAAA,IAAKA,CAAA,GAAIC,SAAU,CAAAD,CAAA,EAAG,OAAO,GAAI;EAEtE6B,cAAgB,EAAC7B,CAAA,IAAcD,MAAO,CAAAC,CAAA,IAAKA,CAAA,GAAIG,UAAW,CAAAH,CAAA,EAAG,OAAO,GAAI;EAExE8B,iBAAiB9B,CAAS,EAAE;IAC1B,MAAMjG,CAAI;IACV,MAAMnB,CAAI;IACV,OAAOmH,MAAA,CAAOC,CAAK,IAAAA,CAAA,GACjBA,CAAA,GAAI,GACA,SAAMC,SAAA,CAAUD,CAAI,MAAGjG,CAAG,EAAAnB,CAAA,IAC1B,MAAM,GAAM,GAAAuH,UAAA,CAAWH,CAAA,GAAI,CAAI,MAAGjG,CAAA,EAAGnB,CAAE;EAC/C;EAEAmJ,WAAW/B,CAAS,EAAE;IACpB,MAAMjG,CAAI;IACV,OAAOiG,CAAA,GAAIA,CAAA,IAAM,CAAAjG,CAAI,QAAKiG,CAAA,GAAIjG,CAAA;EAChC;EAEAiI,YAAYhC,CAAS,EAAE;IACrB,MAAMjG,CAAI;IACV,OAAO,CAACiG,CAAK,SAAKA,CAAK,KAACjG,CAAI,QAAKiG,CAAA,GAAIjG,CAAA,CAAK;EAC5C;EAEAkI,cAAcjC,CAAS,EAAE;IACvB,IAAIjG,CAAI;IACR,IAAI,CAACiG,CAAK,OAAE,IAAK,CAAG;MAClB,OAAO,OAAOA,CAAA,GAAIA,CAAK,KAAE,CAAAjG,CAAA,IAAM,KAAK,IAAK,KAAKiG,CAAA,GAAIjG,CAAA,CAAC;;IAErD,OAAO,OAAO,CAACiG,CAAA,IAAK,KAAKA,CAAA,IAAM,EAACjG,CAAA,IAAM,KAAK,IAAK,KAAKiG,CAAA,GAAIjG,CAAA,IAAK;EAChE;EAEAmI,YAAA,EAAelC,CAAc,QAAII,OAAQ,CAAA+B,aAAa,CAAC,CAAI,GAAAnC,CAAA;EAE3DmC,cAAcnC,CAAS,EAAE;IACvB,MAAMoC,CAAI;IACV,MAAMC,CAAI;IACV,IAAIrC,CAAA,GAAK,IAAIqC,CAAI;MACf,OAAOD,CAAA,GAAIpC,CAAI,GAAAA,CAAA;;IAEjB,IAAIA,CAAA,GAAK,IAAIqC,CAAI;MACf,OAAOD,CAAA,IAAKpC,CAAA,IAAM,GAAM,GAAAqC,CAAC,IAAKrC,CAAI;;IAEpC,IAAIA,CAAA,GAAK,MAAMqC,CAAI;MACjB,OAAOD,CAAA,IAAKpC,CAAA,IAAM,IAAO,GAAAqC,CAAC,IAAKrC,CAAI;;IAErC,OAAOoC,CAAA,IAAKpC,CAAA,IAAM,KAAQ,GAAAqC,CAAC,IAAKrC,CAAI;EACtC;EAEAsC,eAAA,EAAkBtC,CAAc,IAACA,CAAA,GAAI,GACjC,GAAAI,OAAA,CAAQ8B,YAAY,CAAClC,CAAA,GAAI,CAAK,UAC9BI,OAAA,CAAQ+B,aAAa,CAACnC,CAAA,GAAI,CAAI,QAAK,MAAM;AAC/C;ACrHO,SAASuC,mBAAoBA,CAAAxS,KAAc,EAA2C;EAC3F,IAAIA,KAAA,IAAS,OAAOA,KAAA,KAAU,QAAU;IACtC,MAAMG,IAAA,GAAOH,KAAA,CAAMM,QAAQ;IAC3B,OAAOH,IAAA,KAAS,4BAA4BA,IAAS;;EAGvD,OAAO,KAAK;AACd;AAWO,SAASsS,KAAMA,CAAAzS,KAAK,EAAE;EAC3B,OAAOwS,mBAAoB,CAAAxS,KAAA,IAASA,KAAQ,OAAI0S,KAAA,CAAM1S,KAAM;AAC9D;AAKO,SAAS2S,aAAcA,CAAA3S,KAAK,EAAE;EACnC,OAAOwS,mBAAoB,CAAAxS,KAAA,IACvBA,KACA,OAAI0S,KAAM,CAAA1S,KAAA,EAAO4S,QAAQ,CAAC,GAAK,EAAAC,MAAM,CAAC,KAAKC,SAAS,EAAE;AAC5D;AC/BA,MAAMC,OAAU,IAAC,KAAK,KAAK,eAAe,UAAU,UAAU;AAC9D,MAAMC,MAAS,IAAC,SAAS,eAAe,kBAAkB;AAEnD,SAASC,uBAAwBA,CAAAC,QAAQ,EAAE;EAChDA,QAAS,CAAAzG,GAAG,CAAC,WAAa;IACxBgB,KAAO,EAAAxJ,SAAA;IACPkP,QAAU;IACVC,MAAQ;IACR9R,EAAI,EAAA2C,SAAA;IACJ0I,IAAM,EAAA1I,SAAA;IACNoP,IAAM,EAAApP,SAAA;IACNqP,EAAI,EAAArP,SAAA;IACJ9D,IAAM,EAAA8D;EACR;EAEAiP,QAAS,CAAAK,QAAQ,CAAC,WAAa;IAC7BC,SAAA,EAAW,KAAK;IAChBC,UAAA,EAAY,KAAK;IACjBC,WAAA,EAAcC,IAAS,IAAAA,IAAA,KAAS,YAAgB,IAAAA,IAAA,KAAS,gBAAgBA,IAAS;EACpF;EAEAT,QAAS,CAAAzG,GAAG,CAAC,YAAc;IACzBuG,MAAQ;MACN7S,IAAM;MACNyT,UAAY,EAAAZ;IACd;IACAD,OAAS;MACP5S,IAAM;MACNyT,UAAY,EAAAb;IACd;EACF;EAEAG,QAAS,CAAAK,QAAQ,CAAC,YAAc;IAC9BC,SAAW;EACb;EAEAN,QAAS,CAAAzG,GAAG,CAAC,aAAe;IAC1BoH,MAAQ;MACNC,SAAW;QACTX,QAAU;MACZ;IACF;IACAY,MAAQ;MACND,SAAW;QACTX,QAAU;MACZ;IACF;IACAa,IAAM;MACJC,UAAY;QACVjB,MAAQ;UACNrG,IAAM;QACR;QACAuH,OAAS;UACP/T,IAAM;UACNgT,QAAA,EAAU;QACZ;MACF;IACF;IACAgB,IAAM;MACJF,UAAY;QACVjB,MAAQ;UACNM,EAAI;QACN;QACAY,OAAS;UACP/T,IAAM;UACNiT,MAAQ;UACR9R,EAAA,EAAI+C,CAAA,IAAKA,CAAI;QACf;MACF;IACF;EACF;AACF;ACvEO,SAAS+P,oBAAqBA,CAAAlB,QAAQ,EAAE;EAC7CA,QAAS,CAAAzG,GAAG,CAAC,QAAU;IACrB4H,WAAA,EAAa,IAAI;IACjBC,OAAS;MACPC,GAAK;MACLnG,KAAO;MACPoG,MAAQ;MACRrG,IAAM;IACR;EACF;AACF;ACTA,MAAMsG,SAAA,GAAY,IAAIC,GAAA;AAEtB,SAASC,eAAgBA,CAAAC,MAAc,EAAExR,OAAkC,EAAE;EAC3EA,OAAA,GAAUA,OAAA,IAAW,EAAC;EACtB,MAAMyR,QAAW,GAAAD,MAAA,GAASE,IAAK,CAAAC,SAAS,CAAC3R,OAAA;EACzC,IAAI4R,SAAA,GAAYP,SAAU,CAAAQ,GAAG,CAACJ,QAAA;EAC9B,IAAI,CAACG,SAAW;IACdA,SAAA,GAAY,IAAIE,IAAA,CAAKC,YAAY,CAACP,MAAQ,EAAAxR,OAAA;IAC1CqR,SAAU,CAAAhI,GAAG,CAACoI,QAAU,EAAAG,SAAA;;EAE1B,OAAOA,SAAA;AACT;AAEO,SAASI,YAAaA,CAAAC,GAAW,EAAET,MAAc,EAAExR,OAAkC,EAAE;EAC5F,OAAOuR,eAAgB,CAAAC,MAAA,EAAQxR,OAAS,EAAAkS,MAAM,CAACD,GAAA;AACjD;ACRA,MAAME,UAAa;EAOjBnK,OAAOpL,KAAK,EAAE;IACZ,OAAOC,OAAA,CAAQD,KAAS,IAAyBA,KAAA,GAAS,KAAKA,KAAK;EACtE;EAUAwV,QAAQC,SAAS,EAAEjT,KAAK,EAAEkT,KAAK,EAAE;IAC/B,IAAID,SAAA,KAAc,CAAG;MACnB,OAAO;;IAGT,MAAMb,MAAA,GAAS,IAAI,CAACe,KAAK,CAACvS,OAAO,CAACwR,MAAM;IACxC,IAAIgB,QAAA;IACJ,IAAIC,KAAA,GAAQJ,SAAA;IAEZ,IAAIC,KAAA,CAAM1T,MAAM,GAAG,CAAG;MAEpB,MAAM8T,OAAA,GAAU5P,IAAA,CAAKqC,GAAG,CAACrC,IAAA,CAAKa,GAAG,CAAC2O,KAAK,CAAC,CAAE,EAAC1V,KAAK,CAAG,EAAAkG,IAAA,CAAKa,GAAG,CAAC2O,KAAK,CAACA,KAAA,CAAM1T,MAAM,GAAG,CAAE,EAAChC,KAAK;MACzF,IAAI8V,OAAA,GAAU,IAAQ,IAAAA,OAAA,GAAU,KAAO;QACrCF,QAAW;;MAGbC,KAAA,GAAQE,cAAA,CAAeN,SAAW,EAAAC,KAAA;;IAGpC,MAAMM,QAAW,GAAArP,KAAA,CAAMT,IAAK,CAAAa,GAAG,CAAC8O,KAAA;IAOhC,MAAMI,UAAA,GAAajO,KAAM,CAAAgO,QAAA,IAAY,CAAI,GAAA9P,IAAA,CAAKqC,GAAG,CAACrC,IAAA,CAAKoC,GAAG,CAAC,CAAC,CAAI,GAAApC,IAAA,CAAKoB,KAAK,CAAC0O,QAAA,GAAW,KAAK,CAAE;IAE7F,MAAM5S,OAAU;MAACwS,QAAA;MAAUM,qBAAuB,EAAAD,UAAA;MAAYE,qBAAuB,EAAAF;IAAU;IAC/F7V,MAAO,CAAA2P,MAAM,CAAC3M,OAAS,MAAI,CAACA,OAAO,CAACsS,KAAK,CAACJ,MAAM;IAEhD,OAAOF,YAAA,CAAaK,SAAA,EAAWb,MAAQ,EAAAxR,OAAA;EACzC;EAWAgT,YAAYX,SAAS,EAAEjT,KAAK,EAAEkT,KAAK,EAAE;IACnC,IAAID,SAAA,KAAc,CAAG;MACnB,OAAO;;IAET,MAAMY,MAAS,GAAAX,KAAK,CAAClT,KAAA,CAAM,CAAC8T,WAAW,IAAKb,SAAa,GAAAvP,IAAA,CAAKmB,GAAG,CAAC,IAAInB,IAAK,CAAAoB,KAAK,CAACX,KAAM,CAAA8O,SAAA;IACvF,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,CAACc,QAAQ,CAACF,MAAA,KAAW7T,KAAA,GAAQ,GAAM,GAAAkT,KAAA,CAAM1T,MAAM,EAAE;MACvE,OAAOuT,UAAA,CAAWC,OAAO,CAACjV,IAAI,CAAC,IAAI,EAAEkV,SAAA,EAAWjT,KAAO,EAAAkT,KAAA;;IAEzD,OAAO;EACT;AAEF;AAGA,SAASK,cAAeA,CAAAN,SAAS,EAAEC,KAAK,EAAE;EAGxC,IAAIG,KAAA,GAAQH,KAAM,CAAA1T,MAAM,GAAG,IAAI0T,KAAK,CAAC,CAAE,EAAC1V,KAAK,GAAG0V,KAAK,CAAC,CAAE,EAAC1V,KAAK,GAAG0V,KAAK,CAAC,CAAE,EAAC1V,KAAK,GAAG0V,KAAK,CAAC,CAAE,EAAC1V,KAAK;EAGhG,IAAIkG,IAAA,CAAKa,GAAG,CAAC8O,KAAA,KAAU,KAAKJ,SAAc,KAAAvP,IAAA,CAAKoB,KAAK,CAACmO,SAAY;IAE/DI,KAAQ,GAAAJ,SAAA,GAAYvP,IAAK,CAAAoB,KAAK,CAACmO,SAAA;;EAEjC,OAAOI,KAAA;AACT;AAMA,IAAAW,KAAA,GAAe;EAACjB;AAAU,CAAE;ACnGrB,SAASkB,kBAAmBA,CAAAvD,QAAQ,EAAE;EAC3CA,QAAS,CAAAzG,GAAG,CAAC,OAAS;IACpBiK,OAAA,EAAS,IAAI;IACbC,MAAA,EAAQ,KAAK;IACb/U,OAAA,EAAS,KAAK;IACdgV,WAAA,EAAa,KAAK;IASlBC,MAAQ;IAMRC,KAAO;IAGPC,IAAM;MACJL,OAAA,EAAS,IAAI;MACbM,SAAW;MACXC,eAAA,EAAiB,IAAI;MACrBC,SAAA,EAAW,IAAI;MACfC,UAAY;MACZC,SAAA,EAAWA,CAACC,IAAA,EAAMjU,OAAY,KAAAA,OAAA,CAAQ4T,SAAS;MAC/CM,SAAA,EAAWA,CAACD,IAAA,EAAMjU,OAAY,KAAAA,OAAA,CAAQqP,KAAK;MAC3CkE,MAAA,EAAQ;IACV;IAEAY,MAAQ;MACNb,OAAA,EAAS,IAAI;MACbc,IAAA,EAAM,EAAE;MACRC,UAAY;MACZC,KAAO;IACT;IAGAC,KAAO;MAELjB,OAAA,EAAS,KAAK;MAGdkB,IAAM;MAGNtD,OAAS;QACPC,GAAK;QACLC,MAAQ;MACV;IACF;IAGAkB,KAAO;MACLmC,WAAa;MACbC,WAAa;MACbC,MAAA,EAAQ,KAAK;MACbC,eAAiB;MACjBC,eAAiB;MACjB3D,OAAS;MACToC,OAAA,EAAS,IAAI;MACbwB,QAAA,EAAU,IAAI;MACdC,eAAiB;MACjBC,WAAa;MAEb/W,QAAU,EAAAmV,KAAA,CAAMjB,UAAU,CAACnK,MAAM;MACjCiN,KAAA,EAAO,EAAC;MACRC,KAAA,EAAO,EAAC;MACRtK,KAAO;MACPuK,UAAY;MAEZC,iBAAA,EAAmB,KAAK;MACxBC,aAAe;MACfC,eAAiB;IACnB;EACF;EAEAxF,QAAA,CAASyF,KAAK,CAAC,aAAe,WAAS,EAAI;EAC3CzF,QAAA,CAASyF,KAAK,CAAC,YAAc,WAAS,EAAI;EAC1CzF,QAAA,CAASyF,KAAK,CAAC,cAAgB,WAAS,EAAI;EAC5CzF,QAAA,CAASyF,KAAK,CAAC,aAAe,WAAS,EAAI;EAE3CzF,QAAS,CAAAK,QAAQ,CAAC,OAAS;IACzBC,SAAA,EAAW,KAAK;IAChBE,WAAA,EAAcC,IAAA,IAAS,CAACA,IAAA,CAAKiF,UAAU,CAAC,aAAa,CAACjF,IAAA,CAAKiF,UAAU,CAAC,OAAY,KAAAjF,IAAA,KAAS,cAAcA,IAAS;IAClHF,UAAA,EAAaE,IAAS,IAAAA,IAAA,KAAS,YAAgB,IAAAA,IAAA,KAAS,oBAAoBA,IAAS;EACvF;EAEAT,QAAS,CAAAK,QAAQ,CAAC,QAAU;IAC1BC,SAAW;EACb;EAEAN,QAAS,CAAAK,QAAQ,CAAC,aAAe;IAC/BG,WAAA,EAAcC,IAAA,IAASA,IAAS,0BAAqBA,IAAS;IAC9DF,UAAY,EAACE,IAAA,IAASA,IAAS;EACjC;AACF;MChGakF,SAAY,GAAAzY,MAAA,CAAOyC,MAAM,CAAC,IAAI;MAC9BiW,WAAc,GAAA1Y,MAAA,CAAOyC,MAAM,CAAC,IAAI;AAO7C,SAASkW,WAASC,IAAI,EAAE/V,GAAG,EAAE;EAC3B,IAAI,CAACA,GAAK;IACR,OAAO+V,IAAA;;EAET,MAAMjX,IAAA,GAAOkB,GAAI,CAAA0B,KAAK,CAAC;EACvB,KAAK,IAAI9C,CAAI,MAAGkG,CAAI,GAAAhG,IAAA,CAAKC,MAAM,EAAEH,CAAA,GAAIkG,CAAG,IAAElG,CAAG;IAC3C,MAAMkB,CAAA,GAAIhB,IAAI,CAACF,CAAE;IACjBmX,IAAA,GAAOA,IAAI,CAACjW,CAAE,MAAKiW,IAAI,CAACjW,CAAA,CAAE,GAAG3C,MAAA,CAAOyC,MAAM,CAAC,IAAI;EACjD;EACA,OAAOmW,IAAA;AACT;AAEA,SAASvM,IAAIwM,IAAI,EAAElV,KAAK,EAAEqH,MAAM,EAAE;EAChC,IAAI,OAAOrH,KAAA,KAAU,QAAU;IAC7B,OAAOR,KAAA,CAAMwV,UAAS,CAAAE,IAAA,EAAMlV,KAAQ,GAAAqH,MAAA;;EAEtC,OAAO7H,KAAA,CAAMwV,UAAS,CAAAE,IAAA,EAAM,EAAK,GAAAlV,KAAA;AACnC;AAMO,MAAMmV,QAAA;EACXC,WAAYA,CAAAC,YAAY,EAAEC,SAAS,EAAE;IACnC,IAAI,CAACvF,SAAS,GAAG7P,SAAA;IACjB,IAAI,CAACqV,eAAe,GAAG;IACvB,IAAI,CAACC,WAAW,GAAG;IACnB,IAAI,CAAC9G,KAAK,GAAG;IACb,IAAI,CAAC+G,QAAQ,GAAG,EAAC;IACjB,IAAI,CAACC,gBAAgB,GAAIC,OAAA,IAAYA,OAAA,CAAQ/D,KAAK,CAACgE,QAAQ,CAACC,mBAAmB;IAC/E,IAAI,CAACC,QAAQ,GAAG,EAAC;IACjB,IAAI,CAACC,MAAM,GAAG,CACZ,aACA,YACA,SACA,cACA,YACD;IACD,IAAI,CAACC,IAAI,GAAG;MACVC,MAAQ;MACRpU,IAAM;MACNqU,KAAO;MACPC,UAAY;MACZC,MAAA,EAAQ;IACV;IACA,IAAI,CAACC,KAAK,GAAG,EAAC;IACd,IAAI,CAACC,oBAAoB,GAAG,CAACC,GAAA,EAAKlX,OAAY,KAAAuP,aAAA,CAAcvP,OAAA,CAAQkW,eAAe;IACnF,IAAI,CAACiB,gBAAgB,GAAG,CAACD,GAAA,EAAKlX,OAAY,KAAAuP,aAAA,CAAcvP,OAAA,CAAQmW,WAAW;IAC3E,IAAI,CAACiB,UAAU,GAAG,CAACF,GAAA,EAAKlX,OAAY,KAAAuP,aAAA,CAAcvP,OAAA,CAAQqP,KAAK;IAC/D,IAAI,CAACgI,SAAS,GAAG;IACjB,IAAI,CAACC,WAAW,GAAG;MACjBC,IAAM;MACNC,SAAA,EAAW,IAAI;MACfC,gBAAA,EAAkB;IACpB;IACA,IAAI,CAACC,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,OAAO,GAAG,EAAC;IAChB,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,KAAK,GAAGnX,SAAA;IACb,IAAI,CAACoX,MAAM,GAAG,EAAC;IACf,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,uBAAuB,GAAG,IAAI;IAEnC,IAAI,CAAChI,QAAQ,CAAC6F,YAAA;IACd,IAAI,CAAC3X,KAAK,CAAC4X,SAAA;EACb;EAMA5M,GAAIA,CAAA1I,KAAK,EAAEqH,MAAM,EAAE;IACjB,OAAOqB,GAAA,CAAI,IAAI,EAAE1I,KAAO,EAAAqH,MAAA;EAC1B;EAKA6J,IAAIlR,KAAK,EAAE;IACT,OAAOgV,UAAA,CAAS,IAAI,EAAEhV,KAAA;EACxB;EAMAwP,QAASA,CAAAxP,KAAK,EAAEqH,MAAM,EAAE;IACtB,OAAOqB,GAAA,CAAIqM,WAAA,EAAa/U,KAAO,EAAAqH,MAAA;EACjC;EAEAoQ,QAASA,CAAAzX,KAAK,EAAEqH,MAAM,EAAE;IACtB,OAAOqB,GAAA,CAAIoM,SAAA,EAAW9U,KAAO,EAAAqH,MAAA;EAC/B;EAmBAuN,MAAM5U,KAAK,EAAE4P,IAAI,EAAE8H,WAAW,EAAEC,UAAU,EAAE;IAC1C,MAAMC,WAAA,GAAc5C,UAAS,KAAI,EAAEhV,KAAA;IACnC,MAAM6X,iBAAA,GAAoB7C,UAAS,KAAI,EAAE0C,WAAA;IACzC,MAAMI,WAAA,GAAc,GAAM,GAAAlI,IAAA;IAE1BvT,MAAO,CAAA0b,gBAAgB,CAACH,WAAa;MAEnC,CAACE,WAAA,GAAc;QACb7b,KAAO,EAAA2b,WAAW,CAAChI,IAAK;QACxBoI,QAAA,EAAU;MACZ;MAEA,CAACpI,IAAA,GAAO;QACN/H,UAAA,EAAY,IAAI;QAChBqJ,GAAMA,CAAA;UACJ,MAAM+G,KAAA,GAAQ,IAAI,CAACH,WAAY;UAC/B,MAAMjZ,MAAA,GAASgZ,iBAAiB,CAACF,UAAW;UAC5C,IAAIjb,QAAA,CAASub,KAAQ;YACnB,OAAO5b,MAAO,CAAA2P,MAAM,CAAC,IAAInN,MAAQ,EAAAoZ,KAAA;;UAEnC,OAAOjb,cAAA,CAAeib,KAAO,EAAApZ,MAAA;QAC/B;QACA6J,IAAIzM,KAAK,EAAE;UACT,IAAI,CAAC6b,WAAA,CAAY,GAAG7b,KAAA;QACtB;MACF;IACF;EACF;EAEAyB,MAAMwa,QAAQ,EAAE;IACdA,QAAA,CAASpQ,OAAO,CAAEpK,KAAA,IAAUA,KAAA,CAAM,IAAI;EACxC;AACF;AAGA,IAAAyR,QAAA,GAAe,eAAgB,IAAIgG,QAAS;EAC1CxF,WAAA,EAAcC,IAAA,IAAS,CAACA,IAAA,CAAKiF,UAAU,CAAC;EACxCnF,UAAY,EAACE,IAAA,IAASA,IAAS;EAC/ByG,KAAO;IACL5G,SAAW;EACb;EACAkH,WAAa;IACXhH,WAAA,EAAa,KAAK;IAClBD,UAAA,EAAY;EACd;AACF,CAAG,GAACR,uBAAA,EAAyBmB,oBAAA,EAAsBqC,kBAAA,CAAmB,CAAE;;AC5JxE;;;;;AAKC;AACM,SAASyF,YAAaA,CAAAnC,IAAc,EAAE;EAC3C,IAAI,CAACA,IAAA,IAAQha,aAAc,CAAAga,IAAA,CAAKnU,IAAI,CAAK,IAAA7F,aAAA,CAAcga,IAAK,CAAAC,MAAM,CAAG;IACnE,OAAO,IAAI;;EAGb,OAAO,CAACD,IAAK,CAAAE,KAAK,GAAGF,IAAA,CAAKE,KAAK,GAAG,GAAM,KAAE,KACvCF,IAAA,CAAKI,MAAM,GAAGJ,IAAK,CAAAI,MAAM,GAAG,MAAM,EAAC,CACpC,GAAAJ,IAAA,CAAKnU,IAAI,GAAG,KACZ,GAAAmU,IAAA,CAAKC,MAAM;AACf;AAEA;;AAEC;AACM,SAASmC,YACdA,CAAA7B,GAA6B,EAC7B8B,IAA4B,EAC5BC,EAAY,EACZC,OAAe,EACfC,MAAc,EACd;EACA,IAAIC,SAAA,GAAYJ,IAAI,CAACG,MAAO;EAC5B,IAAI,CAACC,SAAW;IACdA,SAAY,GAAAJ,IAAI,CAACG,MAAO,IAAGjC,GAAA,CAAImC,WAAW,CAACF,MAAA,EAAQ7E,KAAK;IACxD2E,EAAA,CAAGvX,IAAI,CAACyX,MAAA;;EAEV,IAAIC,SAAA,GAAYF,OAAS;IACvBA,OAAU,GAAAE,SAAA;;EAEZ,OAAOF,OAAA;AACT;AAKA;;AAEC,GAFD,CAEC;AAEM,SAASI,aACdpC,GAA6B,EAC7BP,IAAY,EACZ4C,aAAqB,EACrBC,KAAiF,EACjF;EACAA,KAAA,GAAQA,KAAA,IAAS,EAAC;EAClB,IAAIR,IAAA,GAAOQ,KAAM,CAAAR,IAAI,GAAGQ,KAAM,CAAAR,IAAI,IAAI,EAAC;EACvC,IAAIC,EAAA,GAAKO,KAAM,CAAAC,cAAc,GAAGD,KAAM,CAAAC,cAAc,IAAI,EAAE;EAE1D,IAAID,KAAA,CAAM7C,IAAI,KAAKA,IAAM;IACvBqC,IAAO,GAAAQ,KAAA,CAAMR,IAAI,GAAG,EAAC;IACrBC,EAAK,GAAAO,KAAA,CAAMC,cAAc,GAAG,EAAE;IAC9BD,KAAA,CAAM7C,IAAI,GAAGA,IAAA;;EAGfO,GAAA,CAAIwC,IAAI;EAERxC,GAAA,CAAIP,IAAI,GAAGA,IAAA;EACX,IAAIuC,OAAU;EACd,MAAMla,IAAA,GAAOua,aAAA,CAAc3a,MAAM;EACjC,IAAIH,CAAA,EAAWkb,CAAW,EAAAC,IAAA,EAAcC,KAAwB,EAAAC,WAAA;EAChE,KAAKrb,CAAI,MAAGA,CAAI,GAAAO,IAAA,EAAMP,CAAK;IACzBob,KAAQ,GAAAN,aAAa,CAAC9a,CAAE;;IAGxB,IAAIob,KAAA,KAAUhZ,SAAa,IAAAgZ,KAAA,KAAU,IAAI,IAAI,CAAChd,OAAA,CAAQgd,KAAQ;MAC5DX,OAAA,GAAUH,YAAa,CAAA7B,GAAA,EAAK8B,IAAM,EAAAC,EAAA,EAAIC,OAAS,EAAAW,KAAA;KAC1C,UAAIhd,OAAA,CAAQgd,KAAQ;;;MAGzB,KAAKF,CAAA,GAAI,GAAGC,IAAO,GAAAC,KAAA,CAAMjb,MAAM,EAAE+a,CAAA,GAAIC,IAAA,EAAMD,CAAK;QAC9CG,WAAc,GAAAD,KAAK,CAACF,CAAE;;QAEtB,IAAIG,WAAA,KAAgBjZ,SAAa,IAAAiZ,WAAA,KAAgB,IAAI,IAAI,CAACjd,OAAA,CAAQid,WAAc;UAC9EZ,OAAA,GAAUH,YAAa,CAAA7B,GAAA,EAAK8B,IAAM,EAAAC,EAAA,EAAIC,OAAS,EAAAY,WAAA;;MAEnD;;EAEJ;EAEA5C,GAAA,CAAI6C,OAAO;EAEX,MAAMC,KAAA,GAAQf,EAAG,CAAAra,MAAM,GAAG;EAC1B,IAAIob,KAAA,GAAQT,aAAc,CAAA3a,MAAM,EAAE;IAChC,KAAKH,CAAI,MAAGA,CAAI,GAAAub,KAAA,EAAOvb,CAAK;MAC1B,OAAOua,IAAI,CAACC,EAAE,CAACxa,CAAA,CAAE,CAAC;IACpB;IACAwa,EAAG,CAAA/P,MAAM,CAAC,CAAG,EAAA8Q,KAAA;;EAEf,OAAOd,OAAA;AACT;AAEA;;;;;;;;AAQO,SAASe,WAAYA,CAAA1H,KAAY,EAAE2H,KAAa,EAAE5F,KAAa,EAAE;EACtE,MAAM+B,gBAAA,GAAmB9D,KAAA,CAAM4H,uBAAuB;EACtD,MAAMC,SAAA,GAAY9F,KAAA,KAAU,CAAI,GAAAxR,IAAA,CAAKqC,GAAG,CAACmP,KAAA,GAAQ,CAAG,SAAO,CAAC;EAC5D,OAAOxR,IAAA,CAAKiB,KAAK,CAAE,CAAAmW,KAAQ,GAAAE,SAAQ,IAAK/D,gBAAA,IAAoBA,gBAAmB,GAAA+D,SAAA;AACjF;AAEA;;AAEC;AACM,SAASC,YAAYC,MAAyB,EAAEpD,GAA8B,EAAE;EACrFA,GAAM,GAAAA,GAAA,IAAOoD,MAAO,CAAAC,UAAU,CAAC;EAE/BrD,GAAA,CAAIwC,IAAI;;;EAGRxC,GAAA,CAAIsD,cAAc;EAClBtD,GAAI,CAAAuD,SAAS,CAAC,CAAG,KAAGH,MAAA,CAAOhG,KAAK,EAAEgG,MAAA,CAAOI,MAAM;EAC/CxD,GAAA,CAAI6C,OAAO;AACb;AASO,SAASY,UACdzD,GAA6B,EAC7BlX,OAAyB,EACzBkB,CAAS,EACTE,CAAS,EACT;;EAEAwZ,eAAA,CAAgB1D,GAAK,EAAAlX,OAAA,EAASkB,CAAG,EAAAE,CAAA,EAAG,IAAI;AAC1C;AAEA;AACO,SAASwZ,eACdA,CAAA1D,GAA6B,EAC7BlX,OAAyB,EACzBkB,CAAS,EACTE,CAAS,EACTyZ,CAAS,EACT;EACA,IAAI9d,IAAA,EAAc+d,OAAiB,EAAAC,OAAA,EAAiBvY,IAAc,EAAAwY,YAAA,EAAsB1G,KAAA,EAAe2G,QAAkB,EAAAC,QAAA;EACzH,MAAMrE,KAAA,GAAQ7W,OAAA,CAAQmb,UAAU;EAChC,MAAMC,QAAA,GAAWpb,OAAA,CAAQob,QAAQ;EACjC,MAAMC,MAAA,GAASrb,OAAA,CAAQqb,MAAM;EAC7B,IAAIC,GAAM,GAAC,CAAAF,QAAA,IAAY,KAAKjY,WAAA;EAE5B,IAAI0T,KAAA,IAAS,OAAOA,KAAA,KAAU,QAAU;IACtC9Z,IAAA,GAAO8Z,KAAA,CAAM3Z,QAAQ;IACrB,IAAIH,IAAA,KAAS,2BAA+B,IAAAA,IAAA,KAAS,4BAA8B;MACjFma,GAAA,CAAIwC,IAAI;MACRxC,GAAI,CAAAqE,SAAS,CAACra,CAAG,EAAAE,CAAA;MACjB8V,GAAA,CAAIsE,MAAM,CAACF,GAAA;MACXpE,GAAA,CAAIuE,SAAS,CAAC5E,KAAA,EAAO,CAACA,KAAA,CAAMvC,KAAK,GAAG,GAAG,CAACuC,KAAA,CAAM6D,MAAM,GAAG,GAAG7D,KAAA,CAAMvC,KAAK,EAAEuC,KAAA,CAAM6D,MAAM;MACnFxD,GAAA,CAAI6C,OAAO;MACX;;;EAIJ,IAAInV,KAAA,CAAMyW,MAAW,KAAAA,MAAA,IAAU,CAAG;IAChC;;EAGFnE,GAAA,CAAIwE,SAAS;EAEb,QAAQ7E,KAAA;;IAEN;MACE,IAAIgE,CAAG;QACL3D,GAAI,CAAAyE,OAAO,CAACza,CAAG,EAAAE,CAAA,EAAGyZ,CAAA,GAAI,CAAG,EAAAQ,MAAA,EAAQ,GAAG,CAAG,EAAAtY,GAAA;OAClC;QACLmU,GAAA,CAAI0E,GAAG,CAAC1a,CAAG,EAAAE,CAAA,EAAGia,MAAA,EAAQ,CAAG,EAAAtY,GAAA;;MAE3BmU,GAAA,CAAI2E,SAAS;MACb;IACF,KAAK;MACHvH,KAAQ,GAAAuG,CAAA,GAAIA,CAAI,OAAIQ,MAAM;MAC1BnE,GAAA,CAAI4E,MAAM,CAAC5a,CAAI,GAAA4B,IAAA,CAAKiK,GAAG,CAACuO,GAAO,IAAAhH,KAAA,EAAOlT,CAAI,GAAA0B,IAAA,CAAKkL,GAAG,CAACsN,GAAO,IAAAD,MAAA;MAC1DC,GAAO,IAAAhY,aAAA;MACP4T,GAAA,CAAI6E,MAAM,CAAC7a,CAAI,GAAA4B,IAAA,CAAKiK,GAAG,CAACuO,GAAO,IAAAhH,KAAA,EAAOlT,CAAI,GAAA0B,IAAA,CAAKkL,GAAG,CAACsN,GAAO,IAAAD,MAAA;MAC1DC,GAAO,IAAAhY,aAAA;MACP4T,GAAA,CAAI6E,MAAM,CAAC7a,CAAI,GAAA4B,IAAA,CAAKiK,GAAG,CAACuO,GAAO,IAAAhH,KAAA,EAAOlT,CAAI,GAAA0B,IAAA,CAAKkL,GAAG,CAACsN,GAAO,IAAAD,MAAA;MAC1DnE,GAAA,CAAI2E,SAAS;MACb;IACF,KAAK;;;;;;;;MAQHb,YAAA,GAAeK,MAAS;MACxB7Y,IAAA,GAAO6Y,MAAS,GAAAL,YAAA;MAChBF,OAAA,GAAUhY,IAAK,CAAAkL,GAAG,CAACsN,GAAA,GAAMjY,UAAc,IAAAb,IAAA;MACvCyY,QAAW,GAAAnY,IAAA,CAAKkL,GAAG,CAACsN,GAAM,GAAAjY,UAAA,KAAewX,CAAA,GAAIA,CAAI,OAAIG,YAAe,GAAAxY,IAAI,CAAD;MACvEuY,OAAA,GAAUjY,IAAK,CAAAiK,GAAG,CAACuO,GAAA,GAAMjY,UAAc,IAAAb,IAAA;MACvC0Y,QAAW,GAAApY,IAAA,CAAKiK,GAAG,CAACuO,GAAM,GAAAjY,UAAA,KAAewX,CAAA,GAAIA,CAAI,OAAIG,YAAe,GAAAxY,IAAI,CAAD;MACvE0U,GAAI,CAAA0E,GAAG,CAAC1a,CAAI,GAAA+Z,QAAA,EAAU7Z,CAAA,GAAI2Z,OAAS,EAAAC,YAAA,EAAcM,GAAM,GAAAzY,EAAA,EAAIyY,GAAM,GAAAlY,OAAA;MACjE8T,GAAI,CAAA0E,GAAG,CAAC1a,CAAI,GAAAga,QAAA,EAAU9Z,CAAA,GAAI0Z,OAAS,EAAAE,YAAA,EAAcM,GAAA,GAAMlY,OAAS,EAAAkY,GAAA;MAChEpE,GAAI,CAAA0E,GAAG,CAAC1a,CAAI,GAAA+Z,QAAA,EAAU7Z,CAAA,GAAI2Z,OAAS,EAAAC,YAAA,EAAcM,GAAA,EAAKA,GAAM,GAAAlY,OAAA;MAC5D8T,GAAI,CAAA0E,GAAG,CAAC1a,CAAI,GAAAga,QAAA,EAAU9Z,CAAA,GAAI0Z,OAAS,EAAAE,YAAA,EAAcM,GAAM,GAAAlY,OAAA,EAASkY,GAAM,GAAAzY,EAAA;MACtEqU,GAAA,CAAI2E,SAAS;MACb;IACF,KAAK;MACH,IAAI,CAACT,QAAU;QACb5Y,IAAO,GAAAM,IAAA,CAAKkZ,OAAO,GAAGX,MAAA;QACtB/G,KAAQ,GAAAuG,CAAA,GAAIA,CAAI,OAAIrY,IAAI;QACxB0U,GAAI,CAAA+E,IAAI,CAAC/a,CAAI,GAAAoT,KAAA,EAAOlT,CAAA,GAAIoB,IAAM,MAAI8R,KAAA,EAAO,CAAI,GAAA9R,IAAA;QAC7C;;MAEF8Y,GAAO,IAAAjY,UAAA;IACT;IACA,KAAK;MACH4X,QAAW,GAAAnY,IAAA,CAAKkL,GAAG,CAACsN,GAAA,KAAQT,CAAI,GAAAA,CAAA,GAAI,CAAI,GAAAQ,MAAM,CAAD;MAC7CP,OAAU,GAAAhY,IAAA,CAAKkL,GAAG,CAACsN,GAAO,IAAAD,MAAA;MAC1BN,OAAU,GAAAjY,IAAA,CAAKiK,GAAG,CAACuO,GAAO,IAAAD,MAAA;MAC1BH,QAAW,GAAApY,IAAA,CAAKiK,GAAG,CAACuO,GAAA,KAAQT,CAAI,GAAAA,CAAA,GAAI,CAAI,GAAAQ,MAAM,CAAD;MAC7CnE,GAAA,CAAI4E,MAAM,CAAC5a,CAAI,GAAA+Z,QAAA,EAAU7Z,CAAI,GAAA2Z,OAAA;MAC7B7D,GAAA,CAAI6E,MAAM,CAAC7a,CAAI,GAAAga,QAAA,EAAU9Z,CAAI,GAAA0Z,OAAA;MAC7B5D,GAAA,CAAI6E,MAAM,CAAC7a,CAAI,GAAA+Z,QAAA,EAAU7Z,CAAI,GAAA2Z,OAAA;MAC7B7D,GAAA,CAAI6E,MAAM,CAAC7a,CAAI,GAAAga,QAAA,EAAU9Z,CAAI,GAAA0Z,OAAA;MAC7B5D,GAAA,CAAI2E,SAAS;MACb;IACF,KAAK;MACHP,GAAO,IAAAjY,UAAA;IACT;IACA,KAAK;MACH4X,QAAW,GAAAnY,IAAA,CAAKkL,GAAG,CAACsN,GAAA,KAAQT,CAAI,GAAAA,CAAA,GAAI,CAAI,GAAAQ,MAAM,CAAD;MAC7CP,OAAU,GAAAhY,IAAA,CAAKkL,GAAG,CAACsN,GAAO,IAAAD,MAAA;MAC1BN,OAAU,GAAAjY,IAAA,CAAKiK,GAAG,CAACuO,GAAO,IAAAD,MAAA;MAC1BH,QAAW,GAAApY,IAAA,CAAKiK,GAAG,CAACuO,GAAA,KAAQT,CAAI,GAAAA,CAAA,GAAI,CAAI,GAAAQ,MAAM,CAAD;MAC7CnE,GAAA,CAAI4E,MAAM,CAAC5a,CAAI,GAAA+Z,QAAA,EAAU7Z,CAAI,GAAA2Z,OAAA;MAC7B7D,GAAA,CAAI6E,MAAM,CAAC7a,CAAI,GAAA+Z,QAAA,EAAU7Z,CAAI,GAAA2Z,OAAA;MAC7B7D,GAAA,CAAI4E,MAAM,CAAC5a,CAAI,GAAAga,QAAA,EAAU9Z,CAAI,GAAA0Z,OAAA;MAC7B5D,GAAA,CAAI6E,MAAM,CAAC7a,CAAI,GAAAga,QAAA,EAAU9Z,CAAI,GAAA0Z,OAAA;MAC7B;IACF,KAAK;MACHG,QAAW,GAAAnY,IAAA,CAAKkL,GAAG,CAACsN,GAAA,KAAQT,CAAI,GAAAA,CAAA,GAAI,CAAI,GAAAQ,MAAM,CAAD;MAC7CP,OAAU,GAAAhY,IAAA,CAAKkL,GAAG,CAACsN,GAAO,IAAAD,MAAA;MAC1BN,OAAU,GAAAjY,IAAA,CAAKiK,GAAG,CAACuO,GAAO,IAAAD,MAAA;MAC1BH,QAAW,GAAApY,IAAA,CAAKiK,GAAG,CAACuO,GAAA,KAAQT,CAAI,GAAAA,CAAA,GAAI,CAAI,GAAAQ,MAAM,CAAD;MAC7CnE,GAAA,CAAI4E,MAAM,CAAC5a,CAAI,GAAA+Z,QAAA,EAAU7Z,CAAI,GAAA2Z,OAAA;MAC7B7D,GAAA,CAAI6E,MAAM,CAAC7a,CAAI,GAAA+Z,QAAA,EAAU7Z,CAAI,GAAA2Z,OAAA;MAC7B7D,GAAA,CAAI4E,MAAM,CAAC5a,CAAI,GAAAga,QAAA,EAAU9Z,CAAI,GAAA0Z,OAAA;MAC7B5D,GAAA,CAAI6E,MAAM,CAAC7a,CAAI,GAAAga,QAAA,EAAU9Z,CAAI,GAAA0Z,OAAA;MAC7BQ,GAAO,IAAAjY,UAAA;MACP4X,QAAW,GAAAnY,IAAA,CAAKkL,GAAG,CAACsN,GAAA,KAAQT,CAAI,GAAAA,CAAA,GAAI,CAAI,GAAAQ,MAAM,CAAD;MAC7CP,OAAU,GAAAhY,IAAA,CAAKkL,GAAG,CAACsN,GAAO,IAAAD,MAAA;MAC1BN,OAAU,GAAAjY,IAAA,CAAKiK,GAAG,CAACuO,GAAO,IAAAD,MAAA;MAC1BH,QAAW,GAAApY,IAAA,CAAKiK,GAAG,CAACuO,GAAA,KAAQT,CAAI,GAAAA,CAAA,GAAI,CAAI,GAAAQ,MAAM,CAAD;MAC7CnE,GAAA,CAAI4E,MAAM,CAAC5a,CAAI,GAAA+Z,QAAA,EAAU7Z,CAAI,GAAA2Z,OAAA;MAC7B7D,GAAA,CAAI6E,MAAM,CAAC7a,CAAI,GAAA+Z,QAAA,EAAU7Z,CAAI,GAAA2Z,OAAA;MAC7B7D,GAAA,CAAI4E,MAAM,CAAC5a,CAAI,GAAAga,QAAA,EAAU9Z,CAAI,GAAA0Z,OAAA;MAC7B5D,GAAA,CAAI6E,MAAM,CAAC7a,CAAI,GAAAga,QAAA,EAAU9Z,CAAI,GAAA0Z,OAAA;MAC7B;IACF,KAAK;MACHA,OAAA,GAAUD,CAAA,GAAIA,CAAI,OAAI/X,IAAA,CAAKkL,GAAG,CAACsN,GAAA,IAAOD,MAAM;MAC5CN,OAAU,GAAAjY,IAAA,CAAKiK,GAAG,CAACuO,GAAO,IAAAD,MAAA;MAC1BnE,GAAA,CAAI4E,MAAM,CAAC5a,CAAI,GAAA4Z,OAAA,EAAS1Z,CAAI,GAAA2Z,OAAA;MAC5B7D,GAAA,CAAI6E,MAAM,CAAC7a,CAAI,GAAA4Z,OAAA,EAAS1Z,CAAI,GAAA2Z,OAAA;MAC5B;IACF,KAAK;MACH7D,GAAI,CAAA4E,MAAM,CAAC5a,CAAG,EAAAE,CAAA;MACd8V,GAAA,CAAI6E,MAAM,CAAC7a,CAAA,GAAI4B,IAAA,CAAKkL,GAAG,CAACsN,GAAA,KAAQT,CAAA,GAAIA,CAAI,OAAIQ,MAAM,CAAD,EAAIja,CAAA,GAAI0B,IAAK,CAAAiK,GAAG,CAACuO,GAAO,IAAAD,MAAA;MACzE;IACF,KAAK,KAAK;MACRnE,GAAA,CAAI2E,SAAS;MACb;EAAM;EAGV3E,GAAA,CAAIgF,IAAI;EACR,IAAIlc,OAAA,CAAQmc,WAAW,GAAG,CAAG;IAC3BjF,GAAA,CAAIkF,MAAM;;AAEd;AAEA;;;;;;;AAOO,SAASC,cACdA,CAAAC,KAAY,EACZC,IAAU,EACVC,MAAe,EACf;EACAA,MAAS,GAAAA,MAAA,IAAU;EAEnB,OAAO,CAACD,IAAA,IAASD,KAAS,IAAAA,KAAA,CAAMpb,CAAC,GAAGqb,IAAK,CAAAxR,IAAI,GAAGyR,MAAA,IAAUF,KAAM,CAAApb,CAAC,GAAGqb,IAAA,CAAKvR,KAAK,GAAGwR,MACjF,IAAAF,KAAA,CAAMlb,CAAC,GAAGmb,IAAK,CAAApL,GAAG,GAAGqL,MAAA,IAAUF,KAAM,CAAAlb,CAAC,GAAGmb,IAAA,CAAKnL,MAAM,GAAGoL,MAAA;AACzD;AAEO,SAASC,SAASvF,GAA6B,EAAEqF,IAAU,EAAE;EAClErF,GAAA,CAAIwC,IAAI;EACRxC,GAAA,CAAIwE,SAAS;EACbxE,GAAA,CAAI+E,IAAI,CAACM,IAAA,CAAKxR,IAAI,EAAEwR,IAAA,CAAKpL,GAAG,EAAEoL,IAAA,CAAKvR,KAAK,GAAGuR,IAAA,CAAKxR,IAAI,EAAEwR,IAAA,CAAKnL,MAAM,GAAGmL,IAAA,CAAKpL,GAAG;EAC5E+F,GAAA,CAAIwF,IAAI;AACV;AAEO,SAASC,UAAWA,CAAAzF,GAA6B,EAAE;EACxDA,GAAA,CAAI6C,OAAO;AACb;AAEA;;AAEC;AACM,SAAS6C,cACdA,CAAA1F,GAA6B,EAC7BtW,QAAe,EACfpB,MAAa,EACbqd,IAAc,EACdtF,IAAa,EACb;EACA,IAAI,CAAC3W,QAAU;IACb,OAAOsW,GAAA,CAAI6E,MAAM,CAACvc,MAAA,CAAO0B,CAAC,EAAE1B,MAAA,CAAO4B,CAAC;;EAEtC,IAAImW,IAAA,KAAS,QAAU;IACrB,MAAMuF,QAAA,GAAW,CAAClc,QAAA,CAASM,CAAC,GAAG1B,MAAA,CAAO0B,CAAA,IAAK;IAC3CgW,GAAA,CAAI6E,MAAM,CAACe,QAAU,EAAAlc,QAAA,CAASQ,CAAC;IAC/B8V,GAAA,CAAI6E,MAAM,CAACe,QAAU,EAAAtd,MAAA,CAAO4B,CAAC;EAC/B,OAAO,IAAImW,IAAA,KAAS,OAAY,MAAC,CAACsF,IAAM;IACtC3F,GAAA,CAAI6E,MAAM,CAACnb,QAAA,CAASM,CAAC,EAAE1B,MAAA,CAAO4B,CAAC;GAC1B;IACL8V,GAAA,CAAI6E,MAAM,CAACvc,MAAA,CAAO0B,CAAC,EAAEN,QAAA,CAASQ,CAAC;;EAEjC8V,GAAA,CAAI6E,MAAM,CAACvc,MAAA,CAAO0B,CAAC,EAAE1B,MAAA,CAAO4B,CAAC;AAC/B;AAEA;;;AAGO,SAAS2b,eACd7F,GAA6B,EAC7BtW,QAAqB,EACrBpB,MAAmB,EACnBqd,IAAc,EACd;EACA,IAAI,CAACjc,QAAU;IACb,OAAOsW,GAAA,CAAI6E,MAAM,CAACvc,MAAA,CAAO0B,CAAC,EAAE1B,MAAA,CAAO4B,CAAC;;EAEtC8V,GAAA,CAAI8F,aAAa,CACfH,IAAO,GAAAjc,QAAA,CAASqc,IAAI,GAAGrc,QAAA,CAASsc,IAAI,EACpCL,IAAA,GAAOjc,QAAS,CAAAuc,IAAI,GAAGvc,QAAA,CAASwc,IAAI,EACpCP,IAAA,GAAOrd,MAAO,CAAA0d,IAAI,GAAG1d,MAAA,CAAOyd,IAAI,EAChCJ,IAAA,GAAOrd,MAAO,CAAA4d,IAAI,GAAG5d,MAAA,CAAO2d,IAAI,EAChC3d,MAAA,CAAO0B,CAAC,EACR1B,MAAA,CAAO4B,CAAC;AACZ;AAEA,SAASic,aAAcA,CAAAnG,GAA6B,EAAEoG,IAAoB,EAAE;EAC1E,IAAIA,IAAA,CAAKC,WAAW,EAAE;IACpBrG,GAAI,CAAAqE,SAAS,CAAC+B,IAAA,CAAKC,WAAW,CAAC,EAAE,EAAED,IAAA,CAAKC,WAAW,CAAC,CAAE;;EAGxD,IAAI,CAAC5gB,aAAA,CAAc2gB,IAAK,CAAAlC,QAAQ,CAAG;IACjClE,GAAI,CAAAsE,MAAM,CAAC8B,IAAA,CAAKlC,QAAQ;;EAG1B,IAAIkC,IAAA,CAAKjO,KAAK,EAAE;IACd6H,GAAI,CAAAsG,SAAS,GAAGF,IAAA,CAAKjO,KAAK;;EAG5B,IAAIiO,IAAA,CAAKG,SAAS,EAAE;IAClBvG,GAAI,CAAAuG,SAAS,GAAGH,IAAA,CAAKG,SAAS;;EAGhC,IAAIH,IAAA,CAAKI,YAAY,EAAE;IACrBxG,GAAI,CAAAwG,YAAY,GAAGJ,IAAA,CAAKI,YAAY;;AAExC;AAEA,SAASC,aACPzG,GAA6B,EAC7BhW,CAAS,EACTE,CAAS,EACTwc,IAAY,EACZN,IAAoB,EACpB;EACA,IAAIA,IAAK,CAAAO,aAAa,IAAIP,IAAA,CAAKQ,SAAS,EAAE;IACxC;;;;;;AAMC;IACD,MAAMC,OAAA,GAAU7G,GAAI,CAAAmC,WAAW,CAACuE,IAAA;IAChC,MAAM7S,IAAA,GAAO7J,CAAI,GAAA6c,OAAA,CAAQC,qBAAqB;IAC9C,MAAMhT,KAAA,GAAQ9J,CAAI,GAAA6c,OAAA,CAAQE,sBAAsB;IAChD,MAAM9M,GAAA,GAAM/P,CAAI,GAAA2c,OAAA,CAAQG,uBAAuB;IAC/C,MAAM9M,MAAA,GAAShQ,CAAI,GAAA2c,OAAA,CAAQI,wBAAwB;IACnD,MAAMC,WAAA,GAAcd,IAAK,CAAAO,aAAa,GAAI,CAAA1M,GAAM,GAAAC,MAAK,IAAK,IAAIA,MAAM;IAEpE8F,GAAI,CAAAmH,WAAW,GAAGnH,GAAA,CAAIsG,SAAS;IAC/BtG,GAAA,CAAIwE,SAAS;IACbxE,GAAA,CAAItD,SAAS,GAAG0J,IAAK,CAAAgB,eAAe,IAAI;IACxCpH,GAAI,CAAA4E,MAAM,CAAC/Q,IAAM,EAAAqT,WAAA;IACjBlH,GAAI,CAAA6E,MAAM,CAAC/Q,KAAO,EAAAoT,WAAA;IAClBlH,GAAA,CAAIkF,MAAM;;AAEd;AAEA,SAASmC,YAAaA,CAAArH,GAA6B,EAAEoG,IAAqB,EAAE;EAC1E,MAAMkB,QAAA,GAAWtH,GAAA,CAAIsG,SAAS;EAE9BtG,GAAI,CAAAsG,SAAS,GAAGF,IAAA,CAAKjO,KAAK;EAC1B6H,GAAA,CAAIuH,QAAQ,CAACnB,IAAK,CAAAvS,IAAI,EAAEuS,IAAA,CAAKnM,GAAG,EAAEmM,IAAK,CAAAhJ,KAAK,EAAEgJ,IAAA,CAAK5C,MAAM;EACzDxD,GAAA,CAAIsG,SAAS,GAAGgB,QAAA;AAClB;AAEA;;AAEC;AACM,SAASE,WACdxH,GAA6B,EAC7B1C,IAAuB,EACvBtT,CAAS,EACTE,CAAS,EACTuV,IAAoB,EAEpB;EAAA,IADA2G,IAAuB,GAAAlW,SAAA,CAAAxI,MAAA,QAAAwI,SAAA,QAAAvG,SAAA,GAAAuG,SAAA,QAAE;EAEzB,MAAMuX,KAAA,GAAQ9hB,OAAQ,CAAA2X,IAAA,IAAQA,IAAO,IAACA,IAAA,CAAK;EAC3C,MAAM4H,MAAA,GAASkB,IAAK,CAAAsB,WAAW,GAAG,CAAK,IAAAtB,IAAA,CAAKuB,WAAW,KAAK;EAC5D,IAAIpgB,CAAW,EAAAmf,IAAA;EAEf1G,GAAA,CAAIwC,IAAI;EACRxC,GAAI,CAAAP,IAAI,GAAGA,IAAA,CAAKwC,MAAM;EACtBkE,aAAA,CAAcnG,GAAK,EAAAoG,IAAA;EAEnB,KAAK7e,CAAA,GAAI,CAAG,EAAAA,CAAA,GAAIkgB,KAAA,CAAM/f,MAAM,EAAE,EAAEH,CAAG;IACjCmf,IAAO,GAAAe,KAAK,CAAClgB,CAAE;IAEf,IAAI6e,IAAA,CAAKwB,QAAQ,EAAE;MACjBP,YAAa,CAAArH,GAAA,EAAKoG,IAAA,CAAKwB,QAAQ;;IAGjC,IAAI1C,MAAQ;MACV,IAAIkB,IAAA,CAAKuB,WAAW,EAAE;QACpB3H,GAAI,CAAAmH,WAAW,GAAGf,IAAA,CAAKuB,WAAW;;MAGpC,IAAI,CAACliB,aAAA,CAAc2gB,IAAK,CAAAsB,WAAW,CAAG;QACpC1H,GAAI,CAAAtD,SAAS,GAAG0J,IAAA,CAAKsB,WAAW;;MAGlC1H,GAAA,CAAI6H,UAAU,CAACnB,IAAA,EAAM1c,CAAG,EAAAE,CAAA,EAAGkc,IAAA,CAAK0B,QAAQ;;IAG1C9H,GAAA,CAAI+H,QAAQ,CAACrB,IAAA,EAAM1c,CAAG,EAAAE,CAAA,EAAGkc,IAAA,CAAK0B,QAAQ;IACtCrB,YAAa,CAAAzG,GAAA,EAAKhW,CAAG,EAAAE,CAAA,EAAGwc,IAAM,EAAAN,IAAA;IAE9Blc,CAAK,IAAA7D,MAAA,CAAOoZ,IAAA,CAAKG,UAAU;EAC7B;EAEAI,GAAA,CAAI6C,OAAO;AACb;AAEA;;;;AAIC;AACM,SAASmF,mBACdhI,GAA6B,EAC7B+E,IAA2C,EAC3C;EACA,MAAM;IAAC/a,CAAC;IAAEE,CAAC;IAAEyZ,CAAC;IAAEsE,CAAC;IAAE9D;EAAM,CAAC,GAAGY,IAAA;;EAG7B/E,GAAA,CAAI0E,GAAG,CAAC1a,CAAA,GAAIma,MAAO,CAAA+D,OAAO,EAAEhe,CAAI,GAAAia,MAAA,CAAO+D,OAAO,EAAE/D,MAAA,CAAO+D,OAAO,EAAE,CAAChc,OAAA,EAASP,EAAA,EAAI,IAAI;;EAGlFqU,GAAA,CAAI6E,MAAM,CAAC7a,CAAA,EAAGE,CAAI,GAAA+d,CAAA,GAAI9D,MAAA,CAAOgE,UAAU;;EAGvCnI,GAAA,CAAI0E,GAAG,CAAC1a,CAAA,GAAIma,MAAO,CAAAgE,UAAU,EAAEje,CAAI,GAAA+d,CAAA,GAAI9D,MAAO,CAAAgE,UAAU,EAAEhE,MAAO,CAAAgE,UAAU,EAAExc,EAAA,EAAIO,OAAA,EAAS,IAAI;;EAG9F8T,GAAA,CAAI6E,MAAM,CAAC7a,CAAA,GAAI2Z,CAAA,GAAIQ,MAAO,CAAAiE,WAAW,EAAEle,CAAI,GAAA+d,CAAA;;EAG3CjI,GAAA,CAAI0E,GAAG,CAAC1a,CAAA,GAAI2Z,CAAI,GAAAQ,MAAA,CAAOiE,WAAW,EAAEle,CAAA,GAAI+d,CAAI,GAAA9D,MAAA,CAAOiE,WAAW,EAAEjE,MAAA,CAAOiE,WAAW,EAAElc,OAAA,EAAS,GAAG,IAAI;;EAGpG8T,GAAA,CAAI6E,MAAM,CAAC7a,CAAA,GAAI2Z,CAAG,EAAAzZ,CAAA,GAAIia,MAAA,CAAOkE,QAAQ;;EAGrCrI,GAAA,CAAI0E,GAAG,CAAC1a,CAAA,GAAI2Z,CAAI,GAAAQ,MAAA,CAAOkE,QAAQ,EAAEne,CAAA,GAAIia,MAAO,CAAAkE,QAAQ,EAAElE,MAAO,CAAAkE,QAAQ,EAAE,CAAG,GAACnc,OAAA,EAAS,IAAI;;EAGxF8T,GAAA,CAAI6E,MAAM,CAAC7a,CAAI,GAAAma,MAAA,CAAO+D,OAAO,EAAEhe,CAAA;AACjC;ACpgBA,MAAMoe,WAAc;AACpB,MAAMC,UAAa;AAEnB;;;GAAA,C;;;;;;;AAWC;AACM,SAASC,aAAa9iB,KAAsB,EAAE4F,IAAY,EAAU;EACzE,MAAMmd,OAAA,GAAU,CAAC,KAAK/iB,KAAI,EAAGgjB,KAAK,CAACJ,WAAA;EACnC,IAAI,CAACG,OAAW,IAAAA,OAAO,CAAC,EAAE,KAAK,QAAU;IACvC,OAAOnd,IAAO;;EAGhB5F,KAAQ,IAAC+iB,OAAO,CAAC,CAAE;EAEnB,QAAQA,OAAO,CAAC,CAAE;IAChB,KAAK;MACH,OAAO/iB,KAAA;IACT,KAAK;MACHA,KAAS;MACT;EAAM;EAKV,OAAO4F,IAAO,GAAA5F,KAAA;AAChB;AAEA,MAAMijB,YAAe,GAAC5e,CAAe,KAACA,CAAK;AAQpC,SAAS6e,kBAAkBljB,KAAsC,EAAEmjB,KAAwC,EAAE;EAClH,MAAMC,GAAA,GAAM,EAAC;EACb,MAAMC,QAAA,GAAW5iB,QAAS,CAAA0iB,KAAA;EAC1B,MAAMphB,IAAA,GAAOshB,QAAW,GAAAjjB,MAAA,CAAO2B,IAAI,CAACohB,KAAA,IAASA,KAAK;EAClD,MAAMG,IAAA,GAAO7iB,QAAS,CAAAT,KAAA,IAClBqjB,QACE,GAAAE,IAAA,IAAQxiB,cAAe,CAAAf,KAAK,CAACujB,IAAA,CAAK,EAAEvjB,KAAK,CAACmjB,KAAK,CAACI,IAAK,EAAC,CACtD,GAAAA,IAAQ,IAAAvjB,KAAK,CAACujB,IAAA,CAAK,GACrB,MAAMvjB,KAAK;EAEf,KAAK,MAAMujB,IAAA,IAAQxhB,IAAM;IACvBqhB,GAAG,CAACG,IAAA,CAAK,GAAGN,YAAA,CAAaK,IAAK,CAAAC,IAAA;EAChC;EACA,OAAOH,GAAA;AACT;AAEA;;;;;;;AAOC;AACM,SAASI,MAAOA,CAAAxjB,KAA4B,EAAE;EACnD,OAAOkjB,iBAAA,CAAkBljB,KAAO;IAACuU,GAAK;IAAKnG,KAAO;IAAKoG,MAAQ;IAAKrG,IAAM;EAAG;AAC/E;AAEA;;;;;;AAMC;AACM,SAASsV,aAAcA,CAAAzjB,KAA2B,EAAE;EACzD,OAAOkjB,iBAAA,CAAkBljB,KAAO,GAAC,WAAW,YAAY,cAAc,cAAc;AACtF;AAEA;;;;;;;AAOC;AACM,SAAS0jB,SAAUA,CAAA1jB,KAAqB,EAAa;EAC1D,MAAMgF,GAAA,GAAMwe,MAAO,CAAAxjB,KAAA;EAEnBgF,GAAA,CAAI0S,KAAK,GAAG1S,GAAA,CAAImJ,IAAI,GAAGnJ,GAAA,CAAIoJ,KAAK;EAChCpJ,GAAA,CAAI8Y,MAAM,GAAG9Y,GAAA,CAAIuP,GAAG,GAAGvP,GAAA,CAAIwP,MAAM;EAEjC,OAAOxP,GAAA;AACT;AAEA;;;;;;AAMC;AAEM,SAAS2e,OAAOvgB,OAA0B,EAAEwgB,QAA4B,EAAE;EAC/ExgB,OAAA,GAAUA,OAAA,IAAW,EAAC;EACtBwgB,QAAW,GAAAA,QAAA,IAAY1Q,QAAA,CAAS6G,IAAI;EAEpC,IAAInU,IAAA,GAAO7E,cAAe,CAAAqC,OAAA,CAAQwC,IAAI,EAAEge,QAAA,CAAShe,IAAI;EAErD,IAAI,OAAOA,IAAA,KAAS,QAAU;IAC5BA,IAAA,GAAOie,QAAA,CAASje,IAAM;;EAExB,IAAIqU,KAAA,GAAQlZ,cAAe,CAAAqC,OAAA,CAAQ6W,KAAK,EAAE2J,QAAA,CAAS3J,KAAK;EACxD,IAAIA,KAAA,IAAS,CAAC,CAAC,KAAKA,KAAI,EAAG+I,KAAK,CAACH,UAAa;IAC5C3e,OAAQ,CAAAC,IAAI,CAAC,oCAAoC8V,KAAQ;IACzDA,KAAQ,GAAAhW,SAAA;;EAGV,MAAM8V,IAAO;IACXC,MAAA,EAAQjZ,cAAe,CAAAqC,OAAA,CAAQ4W,MAAM,EAAE4J,QAAA,CAAS5J,MAAM;IACtDE,UAAA,EAAY4I,YAAA,CAAa/hB,cAAe,CAAAqC,OAAA,CAAQ8W,UAAU,EAAE0J,QAAA,CAAS1J,UAAU,CAAG,EAAAtU,IAAA;IAClFA,IAAA;IACAqU,KAAA;IACAE,MAAA,EAAQpZ,cAAe,CAAAqC,OAAA,CAAQ+W,MAAM,EAAEyJ,QAAA,CAASzJ,MAAM;IACtDoC,MAAQ;EACV;EAEAxC,IAAK,CAAAwC,MAAM,GAAGL,YAAa,CAAAnC,IAAA;EAC3B,OAAOA,IAAA;AACT;AAEA;;;;;;;;;;;AAWO,SAAS+J,QAAQC,MAAsB,EAAErK,OAAgB,EAAElX,KAAc,EAAEwhB,IAA6B,EAAE;EAC/G,IAAIC,SAAA,GAAY,IAAI;EACpB,IAAIpiB,CAAA,EAAWO,IAAc,EAAApC,KAAA;EAE7B,KAAK6B,CAAA,GAAI,GAAGO,IAAO,GAAA2hB,MAAA,CAAO/hB,MAAM,EAAEH,CAAA,GAAIO,IAAM,IAAEP,CAAG;IAC/C7B,KAAQ,GAAA+jB,MAAM,CAACliB,CAAE;IACjB,IAAI7B,KAAA,KAAUiE,SAAW;MACvB;;IAEF,IAAIyV,OAAY,KAAAzV,SAAA,IAAa,OAAOjE,KAAA,KAAU,UAAY;MACxDA,KAAA,GAAQA,KAAM,CAAA0Z,OAAA;MACduK,SAAA,GAAY,KAAK;;IAEnB,IAAIzhB,KAAA,KAAUyB,SAAa,IAAAhE,OAAA,CAAQD,KAAQ;MACzCA,KAAA,GAAQA,KAAK,CAACwC,KAAQ,GAAAxC,KAAA,CAAMgC,MAAM,CAAC;MACnCiiB,SAAA,GAAY,KAAK;;IAEnB,IAAIjkB,KAAA,KAAUiE,SAAW;MACvB,IAAI+f,IAAA,IAAQ,CAACC,SAAW;QACtBD,IAAK,CAAAC,SAAS,GAAG,KAAK;;MAExB,OAAOjkB,KAAA;;EAEX;AACF;AAEA;;;;;;AAMO,SAASkkB,SAAUA,CAAAC,MAAqC,EAAErN,KAAsB,EAAEF,WAAoB,EAAE;EAC7G,MAAM;IAACtO,GAAA;IAAKC;EAAA,CAAI,GAAG4b,MAAA;EACnB,MAAMC,MAAA,GAAShjB,WAAY,CAAA0V,KAAA,EAAO,CAACvO,GAAA,GAAMD,GAAE,IAAK;EAChD,MAAM+b,QAAA,GAAWA,CAACrkB,KAAe,EAAAskB,GAAA,KAAgB1N,WAAA,IAAe5W,KAAU,SAAI,CAAI,GAAAA,KAAA,GAAQskB,GAAG;EAC7F,OAAO;IACLhc,GAAA,EAAK+b,QAAS,CAAA/b,GAAA,EAAK,CAACpC,IAAA,CAAKa,GAAG,CAACqd,MAAA;IAC7B7b,GAAA,EAAK8b,QAAA,CAAS9b,GAAK,EAAA6b,MAAA;EACrB;AACF;AAUO,SAASG,cAAcC,aAAqB,EAAE9K,OAAe,EAAE;EACpE,OAAOtZ,MAAA,CAAO2P,MAAM,CAAC3P,MAAO,CAAAyC,MAAM,CAAC2hB,aAAgB,GAAA9K,OAAA;AACrD;;AC7LA;;;;;;;;;AASC;AACM,SAAS+K,eAIdA,CAAAC,MAAS,EAKT;EAAA,IAJAC,QAAW,GAAAna,SAAA,CAAAxI,MAAA,QAAAwI,SAAA,QAAAvG,SAAA,GAAAuG,SAAA,OAAC,GAAG;EAAA,IACfoa,UAAc,GAAApa,SAAA,CAAAxI,MAAA,OAAAwI,SAAA,MAAAvG,SAAA;EAAA,IACd2f,QAA4B,GAAApZ,SAAA,CAAAxI,MAAA,OAAAwI,SAAA,MAAAvG,SAAA;EAAA,IAC5B4gB,SAAA,GAAAra,SAAA,CAAAxI,MAAA,QAAAwI,SAAA,QAAAvG,SAAA,GAAAuG,SAAA,MAAY,MAAMka,MAAM,CAAC,EAAE;EAE3B,MAAMI,eAAA,GAAkBF,UAAc,IAAAF,MAAA;EACtC,IAAI,OAAOd,QAAA,KAAa,WAAa;IACnCA,QAAA,GAAWmB,QAAA,CAAS,WAAa,EAAAL,MAAA;;EAEnC,MAAM9H,KAA6B;IACjC,CAACoI,MAAA,CAAOC,WAAW,GAAG;IACtBC,UAAA,EAAY,IAAI;IAChBC,OAAS,EAAAT,MAAA;IACTU,WAAa,EAAAN,eAAA;IACbtR,SAAW,EAAAoQ,QAAA;IACXyB,UAAY,EAAAR,SAAA;IACZrJ,QAAU,EAACzX,KAAA,IAAqB0gB,eAAgB,EAAC1gB,KAAA,EAAU,GAAA2gB,MAAA,CAAO,EAAEC,QAAA,EAAUG,eAAiB,EAAAlB,QAAA;EACjG;EACA,OAAO,IAAI0B,KAAA,CAAM1I,KAAO;IACtB;;AAEC;IACD2I,cAAeA,CAAA3iB,MAAM,EAAE2gB,IAAY,EAAE;MACnC,OAAO3gB,MAAM,CAAC2gB,IAAK;MACnB,OAAO3gB,MAAA,CAAO4iB,KAAK;MACnB,OAAOd,MAAM,CAAC,EAAE,CAACnB,IAAA,CAAK;MACtB,OAAO,IAAI;IACb;IAEA;;AAEC;IACDtO,GAAIA,CAAArS,MAAM,EAAE2gB,IAAY,EAAE;MACxB,OAAOkC,OAAA,CAAQ7iB,MAAQ,EAAA2gB,IAAA,EACrB,MAAMmC,oBAAqB,CAAAnC,IAAA,EAAMoB,QAAA,EAAUD,MAAQ,EAAA9hB,MAAA;IACvD;IAEA;;;AAGC;IACD+iB,wBAAyBA,CAAA/iB,MAAM,EAAE2gB,IAAI,EAAE;MACrC,OAAOqC,OAAA,CAAQD,wBAAwB,CAAC/iB,MAAA,CAAOuiB,OAAO,CAAC,EAAE,EAAE5B,IAAA;IAC7D;IAEA;;AAEC;IACDsC,cAAiBA,CAAA;MACf,OAAOD,OAAQ,CAAAC,cAAc,CAACnB,MAAM,CAAC,CAAE;IACzC;IAEA;;AAEC;IACD5e,GAAIA,CAAAlD,MAAM,EAAE2gB,IAAY,EAAE;MACxB,OAAOuC,oBAAA,CAAqBljB,MAAQ,EAAA2T,QAAQ,CAACgN,IAAA;IAC/C;IAEA;;;IAGAwC,QAAQnjB,MAAM,EAAE;MACd,OAAOkjB,oBAAqB,CAAAljB,MAAA;IAC9B;IAEA;;AAEC;IACD6J,IAAI7J,MAAM,EAAE2gB,IAAY,EAAEvjB,KAAK,EAAE;MAC/B,MAAMgmB,OAAA,GAAUpjB,MAAA,CAAOqjB,QAAQ,KAAKrjB,MAAO,CAAAqjB,QAAQ,GAAGpB,SAAU;MAChEjiB,MAAM,CAAC2gB,IAAA,CAAK,GAAGyC,OAAO,CAACzC,IAAK,IAAGvjB,KAAA;MAC/B,OAAO4C,MAAA,CAAO4iB,KAAK;MACnB,OAAO,IAAI;IACb;EACF;AACF;AAEA;;;;;;;;AAQO,SAASU,eAIdC,KAA0B,EAC1BzM,OAAkB,EAClB0M,QAA8B,EAC9BC,kBAAuC,EACvC;EACA,MAAMzJ,KAA4B;IAChCsI,UAAA,EAAY,KAAK;IACjBoB,MAAQ,EAAAH,KAAA;IACRI,QAAU,EAAA7M,OAAA;IACV8M,SAAW,EAAAJ,QAAA;IACXK,MAAA,EAAQ,IAAI/Z,GAAA;IACZ0M,YAAA,EAAcA,YAAA,CAAa+M,KAAO,EAAAE,kBAAA;IAClCK,UAAA,EAAapM,GAAA,IAAmB4L,cAAe,CAAAC,KAAA,EAAO7L,GAAA,EAAK8L,QAAU,EAAAC,kBAAA;IACrE7K,QAAU,EAACzX,KAAA,IAAqBmiB,cAAe,CAAAC,KAAA,CAAM3K,QAAQ,CAACzX,KAAA,GAAQ2V,OAAA,EAAS0M,QAAU,EAAAC,kBAAA;EAC3F;EACA,OAAO,IAAIf,KAAA,CAAM1I,KAAO;IACtB;;AAEC;IACD2I,cAAeA,CAAA3iB,MAAM,EAAE2gB,IAAI,EAAE;MAC3B,OAAO3gB,MAAM,CAAC2gB,IAAK;MACnB,OAAO4C,KAAK,CAAC5C,IAAK;MAClB,OAAO,IAAI;IACb;IAEA;;AAEC;IACDtO,IAAIrS,MAAM,EAAE2gB,IAAY,EAAEoD,QAAQ,EAAE;MAClC,OAAOlB,OAAA,CAAQ7iB,MAAQ,EAAA2gB,IAAA,EACrB,MAAMqD,mBAAA,CAAoBhkB,MAAA,EAAQ2gB,IAAM,EAAAoD,QAAA;IAC5C;IAEA;;;AAGC;IACDhB,wBAAyBA,CAAA/iB,MAAM,EAAE2gB,IAAI,EAAE;MACrC,OAAO3gB,MAAA,CAAOwW,YAAY,CAACyN,OAAO,GAC9BjB,OAAQ,CAAA9f,GAAG,CAACqgB,KAAA,EAAO5C,IAAQ;QAAC3X,UAAA,EAAY,IAAI;QAAED,YAAA,EAAc;MAAI,IAAI1H,SAAS,GAC7E2hB,OAAA,CAAQD,wBAAwB,CAACQ,KAAA,EAAO5C,IAAK;IACnD;IAEA;;AAEC;IACDsC,cAAiBA,CAAA;MACf,OAAOD,OAAA,CAAQC,cAAc,CAACM,KAAA;IAChC;IAEA;;AAEC;IACDrgB,GAAIA,CAAAlD,MAAM,EAAE2gB,IAAI,EAAE;MAChB,OAAOqC,OAAA,CAAQ9f,GAAG,CAACqgB,KAAO,EAAA5C,IAAA;IAC5B;IAEA;;AAEC;IACDwC,OAAUA,CAAA;MACR,OAAOH,OAAA,CAAQG,OAAO,CAACI,KAAA;IACzB;IAEA;;AAEC;IACD1Z,IAAI7J,MAAM,EAAE2gB,IAAI,EAAEvjB,KAAK,EAAE;MACvBmmB,KAAK,CAAC5C,IAAA,CAAK,GAAGvjB,KAAA;MACd,OAAO4C,MAAM,CAAC2gB,IAAK;MACnB,OAAO,IAAI;IACb;EACF;AACF;AAEA;;AAEC;AACM,SAASnK,YACdA,CAAA+M,KAAoB,EAER;EAAA,IADZjT,QAA+B,GAAA1I,SAAA,CAAAxI,MAAA,QAAAwI,SAAA,QAAAvG,SAAA,GAAAuG,SAAA;IAACsc,UAAA,EAAY,IAAI;IAAEC,SAAA,EAAW;EAAI,CAAC;EAElE,MAAM;IAACrT,WAAc,GAAAR,QAAA,CAAS4T,UAAU;IAAErT,UAAa,GAAAP,QAAA,CAAS6T,SAAS;IAAEC,QAAW,GAAA9T,QAAA,CAAS2T;EAAO,CAAC,GAAGV,KAAA;EAC1G,OAAO;IACLU,OAAS,EAAAG,QAAA;IACTF,UAAY,EAAApT,WAAA;IACZqT,SAAW,EAAAtT,UAAA;IACXwT,YAAA,EAAczhB,UAAW,CAAAkO,WAAA,IAAeA,WAAc,SAAMA,WAAW;IACvEwT,WAAA,EAAa1hB,UAAW,CAAAiO,UAAA,IAAcA,UAAa,SAAMA;EAC3D;AACF;AAEA,MAAM0T,OAAA,GAAUA,CAACC,MAAgB,EAAAzT,IAAA,KAAiByT,MAAA,GAASA,MAAS,GAAAjiB,WAAA,CAAYwO,IAAA,IAAQA,IAAI;AAC5F,MAAM0T,gBAAA,GAAmBA,CAAC9D,IAAA,EAAcvjB,KAAA,KAAmBS,QAAS,CAAAT,KAAA,KAAUujB,IAAA,KAAS,UACpF,KAAAnjB,MAAO,CAAAylB,cAAc,CAAC7lB,KAAW,UAAI,IAAIA,KAAM,CAAAmZ,WAAW,KAAK/Y,MAAK;AAEvE,SAASqlB,QACP7iB,MAAiB,EACjB2gB,IAAY,EACZO,OAAsB,EACtB;EACA,IAAI1jB,MAAA,CAAOC,SAAS,CAACwD,cAAc,CAACtD,IAAI,CAACqC,MAAA,EAAQ2gB,IAAO;IACtD,OAAO3gB,MAAM,CAAC2gB,IAAK;;EAGrB,MAAMvjB,KAAQ,GAAA8jB,OAAA;;EAEdlhB,MAAM,CAAC2gB,IAAA,CAAK,GAAGvjB,KAAA;EACf,OAAOA,KAAA;AACT;AAEA,SAAS4mB,oBACPhkB,MAAoB,EACpB2gB,IAAY,EACZoD,QAAmB,EACnB;EACA,MAAM;IAACL,MAAM;IAAEC,QAAQ;IAAEC,SAAA;IAAWpN,YAAA,EAAcN;EAAW,CAAC,GAAGlW,MAAA;EACjE,IAAI5C,KAAQ,GAAAsmB,MAAM,CAAC/C,IAAA,CAAK;;EAGxB,IAAI/d,UAAW,CAAAxF,KAAA,KAAU8Y,WAAY,CAAAmO,YAAY,CAAC1D,IAAO;IACvDvjB,KAAQ,GAAAsnB,kBAAA,CAAmB/D,IAAM,EAAAvjB,KAAA,EAAO4C,MAAQ,EAAA+jB,QAAA;;EAElD,IAAI1mB,OAAQ,CAAAD,KAAA,KAAUA,KAAM,CAAAgC,MAAM,EAAE;IAClChC,KAAA,GAAQunB,aAAc,CAAAhE,IAAA,EAAMvjB,KAAO,EAAA4C,MAAA,EAAQkW,WAAA,CAAYoO,WAAW;;EAEpE,IAAIG,gBAAA,CAAiB9D,IAAA,EAAMvjB,KAAQ;;IAEjCA,KAAA,GAAQkmB,cAAA,CAAelmB,KAAO,EAAAumB,QAAA,EAAUC,SAAA,IAAaA,SAAS,CAACjD,IAAA,CAAK,EAAEzK,WAAA;;EAExE,OAAO9Y,KAAA;AACT;AAEA,SAASsnB,mBACP/D,IAAY,EACZiE,QAAqD,EACrD5kB,MAAoB,EACpB+jB,QAAmB,EACnB;EACA,MAAM;IAACL,MAAA;IAAQC,QAAA;IAAUC,SAAS;IAAEC;EAAM,CAAC,GAAG7jB,MAAA;EAC9C,IAAI6jB,MAAA,CAAO3gB,GAAG,CAACyd,IAAO;IACpB,MAAM,IAAIkE,KAAM,0BAAyBvnB,KAAM,CAAAyM,IAAI,CAAC8Z,MAAA,EAAQiB,IAAI,CAAC,IAAQ,WAAOnE,IAAM;;EAExFkD,MAAA,CAAOnC,GAAG,CAACf,IAAA;EACX,IAAIvjB,KAAA,GAAQwnB,QAAS,CAAAjB,QAAA,EAAUC,SAAa,IAAAG,QAAA;EAC5CF,MAAA,CAAOkB,MAAM,CAACpE,IAAA;EACd,IAAI8D,gBAAA,CAAiB9D,IAAA,EAAMvjB,KAAQ;;IAEjCA,KAAA,GAAQ4nB,iBAAkB,CAAAtB,MAAA,CAAOnB,OAAO,EAAEmB,MAAA,EAAQ/C,IAAM,EAAAvjB,KAAA;;EAE1D,OAAOA,KAAA;AACT;AAEA,SAASunB,cACPhE,IAAY,EACZvjB,KAAgB,EAChB4C,MAAoB,EACpBskB,WAAqC,EACrC;EACA,MAAM;IAACZ,MAAM;IAAEC,QAAQ;IAAEC,SAAA;IAAWpN,YAAA,EAAcN;EAAW,CAAC,GAAGlW,MAAA;EAEjE,IAAI,OAAO2jB,QAAS,CAAA/jB,KAAK,KAAK,eAAe0kB,WAAA,CAAY3D,IAAO;IAC9D,OAAOvjB,KAAK,CAACumB,QAAA,CAAS/jB,KAAK,GAAGxC,KAAA,CAAMgC,MAAM,CAAC;EAC7C,OAAO,IAAIvB,QAAA,CAAST,KAAK,CAAC,EAAE,CAAG;;IAE7B,MAAM6nB,GAAM,GAAA7nB,KAAA;IACZ,MAAM0kB,MAAA,GAAS4B,MAAA,CAAOnB,OAAO,CAAC2C,MAAM,CAAC9d,CAAA,IAAKA,CAAM,KAAA6d,GAAA;IAChD7nB,KAAA,GAAQ,EAAE;IACV,KAAK,MAAM6F,IAAA,IAAQgiB,GAAK;MACtB,MAAM3iB,QAAW,GAAA0iB,iBAAA,CAAkBlD,MAAQ,EAAA4B,MAAA,EAAQ/C,IAAM,EAAA1d,IAAA;MACzD7F,KAAM,CAAA8E,IAAI,CAACohB,cAAe,CAAAhhB,QAAA,EAAUqhB,QAAA,EAAUC,SAAa,IAAAA,SAAS,CAACjD,IAAA,CAAK,EAAEzK,WAAA;IAC9E;;EAEF,OAAO9Y,KAAA;AACT;AAEA,SAAS+nB,gBACPnE,QAA8F,EAC9FL,IAAuB,EACvBvjB,KAAc,EACd;EACA,OAAOwF,UAAW,CAAAoe,QAAA,IAAYA,QAAS,CAAAL,IAAA,EAAMvjB,KAAA,IAAS4jB,QAAQ;AAChE;AAEA,MAAMoE,QAAW,GAAAA,CAAC/kB,GAAwB,EAAAglB,MAAA,KAAsBhlB,GAAA,KAAQ,IAAI,GAAGglB,MAC3E,UAAOhlB,GAAQ,gBAAWgC,gBAAiB,CAAAgjB,MAAA,EAAQhlB,GAAA,IAAOgB,SAAS;AAEvE,SAASikB,UACPzb,GAAmB,EACnB0b,YAAyB,EACzBllB,GAAsB,EACtBmlB,cAAiC,EACjCpoB,KAAc,EACd;EACA,KAAK,MAAMioB,MAAA,IAAUE,YAAc;IACjC,MAAMpkB,KAAA,GAAQikB,QAAA,CAAS/kB,GAAK,EAAAglB,MAAA;IAC5B,IAAIlkB,KAAO;MACT0I,GAAA,CAAI6X,GAAG,CAACvgB,KAAA;MACR,MAAM6f,QAAW,GAAAmE,eAAA,CAAgBhkB,KAAM,CAAAyP,SAAS,EAAEvQ,GAAK,EAAAjD,KAAA;MACvD,IAAI,OAAO4jB,QAAa,oBAAeA,QAAa,KAAA3gB,GAAA,IAAO2gB,QAAA,KAAawE,cAAgB;;;QAGtF,OAAOxE,QAAA;;KAEJ,UAAI7f,KAAA,KAAU,KAAK,IAAI,OAAOqkB,cAAmB,oBAAenlB,GAAA,KAAQmlB,cAAgB;;;MAG7F,OAAO,IAAI;;EAEf;EACA,OAAO,KAAK;AACd;AAEA,SAASR,kBACPO,YAAyB,EACzBjjB,QAAuB,EACvBqe,IAAuB,EACvBvjB,KAAc,EACd;EACA,MAAM4kB,UAAA,GAAa1f,QAAA,CAASkgB,WAAW;EACvC,MAAMxB,QAAW,GAAAmE,eAAA,CAAgB7iB,QAAS,CAAAsO,SAAS,EAAE+P,IAAM,EAAAvjB,KAAA;EAC3D,MAAMqoB,SAAY,IAAI,GAAAF,YAAA,EAAiB,GAAAvD,UAAA,CAAW;EAClD,MAAMnY,GAAA,GAAM,IAAIC,GAAA;EAChBD,GAAA,CAAI6X,GAAG,CAACtkB,KAAA;EACR,IAAIiD,GAAA,GAAMqlB,gBAAiB,CAAA7b,GAAA,EAAK4b,SAAW,EAAA9E,IAAA,EAAMK,QAAA,IAAYL,IAAM,EAAAvjB,KAAA;EACnE,IAAIiD,GAAA,KAAQ,IAAI,EAAE;IAChB,OAAO,KAAK;;EAEd,IAAI,OAAO2gB,QAAA,KAAa,WAAe,IAAAA,QAAA,KAAaL,IAAM;IACxDtgB,GAAA,GAAMqlB,gBAAiB,CAAA7b,GAAA,EAAK4b,SAAW,EAAAzE,QAAA,EAAU3gB,GAAK,EAAAjD,KAAA;IACtD,IAAIiD,GAAA,KAAQ,IAAI,EAAE;MAChB,OAAO,KAAK;;;EAGhB,OAAOwhB,eAAgB,CAAAvkB,KAAA,CAAMyM,IAAI,CAACF,GAAM,IAAC,GAAG,EAAEmY,UAAY,EAAAhB,QAAA,EACxD,MAAM2E,YAAA,CAAarjB,QAAA,EAAUqe,IAAgB,EAAAvjB,KAAA;AACjD;AAEA,SAASsoB,iBACP7b,GAAmB,EACnB4b,SAAsB,EACtBplB,GAAsB,EACtB2gB,QAA2B,EAC3B/d,IAAa,EACb;EACA,OAAO5C,GAAK;IACVA,GAAA,GAAMilB,SAAU,CAAAzb,GAAA,EAAK4b,SAAW,EAAAplB,GAAA,EAAK2gB,QAAU,EAAA/d,IAAA;EACjD;EACA,OAAO5C,GAAA;AACT;AAEA,SAASslB,aACPrjB,QAAuB,EACvBqe,IAAY,EACZvjB,KAAc,EACd;EACA,MAAMioB,MAAA,GAAS/iB,QAAA,CAASmgB,UAAU;EAClC,IAAI,EAAE9B,IAAQ,IAAA0E,MAAK,CAAI;IACrBA,MAAM,CAAC1E,IAAK,IAAG,EAAC;;EAElB,MAAM3gB,MAAA,GAASqlB,MAAM,CAAC1E,IAAK;EAC3B,IAAItjB,OAAA,CAAQ2C,MAAW,KAAAnC,QAAA,CAAST,KAAQ;;IAEtC,OAAOA,KAAA;;EAET,OAAO4C,MAAA,IAAU,EAAC;AACpB;AAEA,SAAS8iB,qBACPnC,IAAY,EACZoB,QAAkB,EAClBD,MAAmB,EACnByB,KAAoB,EACpB;EACA,IAAInmB,KAAA;EACJ,KAAK,MAAMonB,MAAA,IAAUzC,QAAU;IAC7B3kB,KAAQ,GAAA+kB,QAAA,CAASoC,OAAQ,CAAAC,MAAA,EAAQ7D,IAAO,GAAAmB,MAAA;IACxC,IAAI,OAAO1kB,KAAA,KAAU,WAAa;MAChC,OAAOqnB,gBAAA,CAAiB9D,IAAA,EAAMvjB,KAC1B,IAAA4nB,iBAAA,CAAkBlD,MAAA,EAAQyB,KAAO,EAAA5C,IAAA,EAAMvjB,KAAA,IACvCA,KAAK;;EAEb;AACF;AAEA,SAAS+kB,QAASA,CAAA9hB,GAAW,EAAEyhB,MAAmB,EAAE;EAClD,KAAK,MAAM3gB,KAAA,IAAS2gB,MAAQ;IAC1B,IAAI,CAAC3gB,KAAO;MACV;;IAEF,MAAM/D,KAAA,GAAQ+D,KAAK,CAACd,GAAI;IACxB,IAAI,OAAOjD,KAAA,KAAU,WAAa;MAChC,OAAOA,KAAA;;EAEX;AACF;AAEA,SAAS8lB,qBAAqBljB,MAAqB,EAAE;EACnD,IAAIb,IAAA,GAAOa,MAAA,CAAO4iB,KAAK;EACvB,IAAI,CAACzjB,IAAM;IACTA,IAAA,GAAOa,MAAO,CAAA4iB,KAAK,GAAGgD,wBAAA,CAAyB5lB,MAAA,CAAOuiB,OAAO;;EAE/D,OAAOpjB,IAAA;AACT;AAEA,SAASymB,yBAAyB9D,MAAmB,EAAE;EACrD,MAAMjY,GAAA,GAAM,IAAIC,GAAA;EAChB,KAAK,MAAM3I,KAAA,IAAS2gB,MAAQ;IAC1B,KAAK,MAAMzhB,GAAA,IAAO7C,MAAO,CAAA2B,IAAI,CAACgC,KAAO,EAAA+jB,MAAM,CAAC/kB,CAAK,KAACA,CAAE,CAAA6V,UAAU,CAAC,GAAO;MACpEnM,GAAA,CAAI6X,GAAG,CAACrhB,GAAA;IACV;EACF;EACA,OAAO/C,KAAA,CAAMyM,IAAI,CAACF,GAAA;AACpB;AAEO,SAASgc,4BACdja,IAAmC,EACnC4N,IAAiB,EACjBvS,KAAa,EACb+E,KAAa,EACb;EACA,MAAM;IAACE;EAAM,CAAC,GAAGN,IAAA;EACjB,MAAM;IAACvL,GAAM;EAAA,CAAI,GAAG,IAAI,CAACylB,QAAQ;EACjC,MAAMC,MAAA,GAAS,IAAIzoB,KAAoB,CAAA0O,KAAA;EACvC,IAAI/M,CAAA,EAAWO,IAAA,EAAcI,KAAe,EAAAqD,IAAA;EAE5C,KAAKhE,CAAA,GAAI,GAAGO,IAAO,GAAAwM,KAAK,EAAE/M,CAAI,GAAAO,IAAA,EAAM,EAAEP,CAAG;IACvCW,KAAA,GAAQX,CAAI,GAAAgI,KAAA;IACZhE,IAAO,GAAAuW,IAAI,CAAC5Z,KAAM;IAClBmmB,MAAM,CAAC9mB,CAAA,CAAE,GAAG;MACV+mB,CAAA,EAAG9Z,MAAO,CAAA+Z,KAAK,CAAC5jB,gBAAA,CAAiBY,IAAA,EAAM5C,GAAM,GAAAT,KAAA;IAC/C;EACF;EACA,OAAOmmB,MAAA;AACT;AClcA,MAAMG,OAAA,GAAUnoB,MAAO,CAAAmoB,OAAO,IAAI;AAGlC,MAAMC,QAAA,GAAWA,CAACta,MAAA,EAAuB5M,CAAmC,KAAAA,CAAA,GAAI4M,MAAA,CAAOzM,MAAM,IAAI,CAACyM,MAAM,CAAC5M,CAAE,EAACmnB,IAAI,IAAIva,MAAM,CAAC5M,CAAE;AAC7H,MAAMonB,YAAA,GAAgBxO,SAAA,IAAyBA,SAAc,WAAM,MAAM,GAAG;AAErE,SAASyO,YACdC,UAAuB,EACvBC,WAAwB,EACxBC,UAAuB,EACvBpZ,CAAS,EAIP;;;;EAMF,MAAMjM,QAAW,GAAAmlB,UAAA,CAAWH,IAAI,GAAGI,WAAA,GAAcD,UAAU;EAC3D,MAAMzlB,OAAU,GAAA0lB,WAAA;EAChB,MAAME,IAAO,GAAAD,UAAA,CAAWL,IAAI,GAAGI,WAAA,GAAcC,UAAU;EACvD,MAAME,GAAA,GAAMhgB,qBAAA,CAAsB7F,OAAS,EAAAM,QAAA;EAC3C,MAAMwlB,GAAA,GAAMjgB,qBAAA,CAAsB+f,IAAM,EAAA5lB,OAAA;EAExC,IAAI+lB,GAAM,GAAAF,GAAA,IAAOA,GAAA,GAAMC,GAAE;EACzB,IAAIE,GAAM,GAAAF,GAAA,IAAOD,GAAA,GAAMC,GAAE;;EAGzBC,GAAM,GAAAzhB,KAAA,CAAMyhB,GAAO,QAAIA,GAAG;EAC1BC,GAAM,GAAA1hB,KAAA,CAAM0hB,GAAO,QAAIA,GAAG;EAE1B,MAAMC,EAAA,GAAK1Z,CAAI,GAAAwZ,GAAA;EACf,MAAMG,EAAA,GAAK3Z,CAAI,GAAAyZ,GAAA;EAEf,OAAO;IACL1lB,QAAU;MACRM,CAAG,EAAAZ,OAAA,CAAQY,CAAC,GAAGqlB,EAAM,IAAAL,IAAA,CAAKhlB,CAAC,GAAGN,QAAS,CAAAM,CAAC,CAAD;MACvCE,CAAG,EAAAd,OAAA,CAAQc,CAAC,GAAGmlB,EAAM,IAAAL,IAAA,CAAK9kB,CAAC,GAAGR,QAAS,CAAAQ,CAAC;IAC1C;IACA8kB,IAAM;MACJhlB,CAAG,EAAAZ,OAAA,CAAQY,CAAC,GAAGslB,EAAM,IAAAN,IAAA,CAAKhlB,CAAC,GAAGN,QAAS,CAAAM,CAAC,CAAD;MACvCE,CAAG,EAAAd,OAAA,CAAQc,CAAC,GAAGolB,EAAM,IAAAN,IAAA,CAAK9kB,CAAC,GAAGR,QAAS,CAAAQ,CAAC;IAC1C;EACF;AACF;AAEA;;AAEC;AACD,SAASqlB,cAAeA,CAAApb,MAAqB,EAAEqb,MAAgB,EAAEC,EAAY,EAAE;EAC7E,MAAMC,SAAA,GAAYvb,MAAA,CAAOzM,MAAM;EAE/B,IAAIioB,MAAA,EAAgBC,KAAe,EAAAC,IAAA,EAAcC,gBAA0B,EAAAC,YAAA;EAC3E,IAAIC,UAAA,GAAavB,QAAA,CAASta,MAAQ;EAClC,KAAK,IAAI5M,CAAI,MAAGA,CAAA,GAAImoB,SAAY,MAAG,EAAEnoB,CAAG;IACtCwoB,YAAe,GAAAC,UAAA;IACfA,UAAa,GAAAvB,QAAA,CAASta,MAAA,EAAQ5M,CAAI;IAClC,IAAI,CAACwoB,YAAgB,KAACC,UAAY;MAChC;;IAGF,IAAIzjB,YAAA,CAAaijB,MAAM,CAACjoB,CAAE,GAAE,GAAGinB,OAAU;MACvCiB,EAAE,CAACloB,CAAE,IAAGkoB,EAAE,CAACloB,CAAA,GAAI,EAAE,GAAG;MACpB;;IAGFooB,MAAA,GAASF,EAAE,CAACloB,CAAA,CAAE,GAAGioB,MAAM,CAACjoB,CAAE;IAC1BqoB,KAAA,GAAQH,EAAE,CAACloB,CAAA,GAAI,EAAE,GAAGioB,MAAM,CAACjoB,CAAE;IAC7BuoB,gBAAmB,GAAAlkB,IAAA,CAAKmB,GAAG,CAAC4iB,MAAA,EAAQ,KAAK/jB,IAAK,CAAAmB,GAAG,CAAC6iB,KAAO;IACzD,IAAIE,gBAAA,IAAoB,CAAG;MACzB;;IAGFD,IAAO,OAAIjkB,IAAK,CAAAyB,IAAI,CAACyiB,gBAAA;IACrBL,EAAE,CAACloB,CAAE,IAAGooB,MAAA,GAASE,IAAO,GAAAL,MAAM,CAACjoB,CAAE;IACjCkoB,EAAE,CAACloB,CAAA,GAAI,CAAE,IAAGqoB,KAAA,GAAQC,IAAO,GAAAL,MAAM,CAACjoB,CAAE;EACtC;AACF;AAEA,SAAS0oB,gBAAgB9b,MAAqB,EAAEsb,EAAY,EAA8B;EAAA,IAA5BtP,SAAA,GAAAjQ,SAAA,CAAAxI,MAAA,QAAAwI,SAAA,QAAAvG,SAAA,GAAAuG,SAAA,MAAuB,GAAG;EACtF,MAAMggB,SAAA,GAAYvB,YAAa,CAAAxO,SAAA;EAC/B,MAAMuP,SAAA,GAAYvb,MAAA,CAAOzM,MAAM;EAC/B,IAAI6T,KAAA,EAAe4U,WAAkC,EAAAJ,YAAA;EACrD,IAAIC,UAAA,GAAavB,QAAA,CAASta,MAAQ;EAElC,KAAK,IAAI5M,CAAI,MAAGA,CAAI,GAAAmoB,SAAA,EAAW,EAAEnoB,CAAG;IAClC4oB,WAAc,GAAAJ,YAAA;IACdA,YAAe,GAAAC,UAAA;IACfA,UAAa,GAAAvB,QAAA,CAASta,MAAA,EAAQ5M,CAAI;IAClC,IAAI,CAACwoB,YAAc;MACjB;;IAGF,MAAMK,MAAA,GAASL,YAAY,CAAC5P,SAAU;IACtC,MAAMkQ,MAAA,GAASN,YAAY,CAACG,SAAU;IACtC,IAAIC,WAAa;MACf5U,KAAA,GAAQ,CAAC6U,MAAA,GAASD,WAAW,CAAChQ,SAAA,CAAU,IAAI;MAC5C4P,YAAY,CAAE,MAAK5P,SAAU,EAAC,CAAC,GAAGiQ,MAAS,GAAA7U,KAAA;MAC3CwU,YAAY,CAAE,MAAKG,SAAU,EAAC,CAAC,GAAGG,MAAS,GAAA9U,KAAA,GAAQkU,EAAE,CAACloB,CAAE;;IAE1D,IAAIyoB,UAAY;MACdzU,KAAA,GAAQ,CAACyU,UAAU,CAAC7P,SAAU,IAAGiQ,MAAK,IAAK;MAC3CL,YAAY,CAAE,MAAK5P,SAAU,EAAC,CAAC,GAAGiQ,MAAS,GAAA7U,KAAA;MAC3CwU,YAAY,CAAE,MAAKG,SAAU,EAAC,CAAC,GAAGG,MAAS,GAAA9U,KAAA,GAAQkU,EAAE,CAACloB,CAAE;;EAE5D;AACF;AAEA;;;;;AAKC;AACM,SAAS+oB,oBAAoBnc,MAAqB,EAA8B;EAAA,IAA5BgM,SAAA,GAAAjQ,SAAA,CAAAxI,MAAA,QAAAwI,SAAA,QAAAvG,SAAA,GAAAuG,SAAA,MAAuB,GAAG;EACnF,MAAMggB,SAAA,GAAYvB,YAAa,CAAAxO,SAAA;EAC/B,MAAMuP,SAAA,GAAYvb,MAAA,CAAOzM,MAAM;EAC/B,MAAM8nB,MAAmB,GAAA5pB,KAAA,CAAM8pB,SAAW,EAAA1K,IAAI,CAAC;EAC/C,MAAMyK,EAAA,GAAe7pB,KAAM,CAAA8pB,SAAA;;EAG3B,IAAInoB,CAAA,EAAG4oB,WAAkC,EAAAJ,YAAA;EACzC,IAAIC,UAAA,GAAavB,QAAA,CAASta,MAAQ;EAElC,KAAK5M,CAAI,MAAGA,CAAI,GAAAmoB,SAAA,EAAW,EAAEnoB,CAAG;IAC9B4oB,WAAc,GAAAJ,YAAA;IACdA,YAAe,GAAAC,UAAA;IACfA,UAAa,GAAAvB,QAAA,CAASta,MAAA,EAAQ5M,CAAI;IAClC,IAAI,CAACwoB,YAAc;MACjB;;IAGF,IAAIC,UAAY;MACd,MAAMO,UAAA,GAAaP,UAAU,CAAC7P,SAAA,CAAU,GAAG4P,YAAY,CAAC5P,SAAU;;MAGlEqP,MAAM,CAACjoB,CAAE,IAAGgpB,UAAe,SAAI,CAACP,UAAU,CAACE,SAAA,CAAU,GAAGH,YAAY,CAACG,SAAA,CAAU,IAAIK,UAAA,GAAa,CAAC;;IAEnGd,EAAE,CAACloB,CAAE,IAAG,CAAC4oB,WAAc,GAAAX,MAAM,CAACjoB,CAAE,IAC5B,CAACyoB,UAAA,GAAaR,MAAM,CAACjoB,CAAA,GAAI,EAAE,GACxB+E,IAAA,CAAKkjB,MAAM,CAACjoB,CAAI,KAAE,MAAM+E,IAAK,CAAAkjB,MAAM,CAACjoB,CAAE,KAAK,IAC1C,CAACioB,MAAM,CAACjoB,CAAA,GAAI,EAAE,GAAGioB,MAAM,CAACjoB,CAAE,CAAD,IAAK,CAAC;EACzC;EAEAgoB,cAAA,CAAepb,MAAA,EAAQqb,MAAQ,EAAAC,EAAA;EAE/BQ,eAAA,CAAgB9b,MAAA,EAAQsb,EAAI,EAAAtP,SAAA;AAC9B;AAEA,SAASqQ,gBAAgBC,EAAU,EAAEziB,GAAW,EAAEC,GAAW,EAAE;EAC7D,OAAOrC,IAAA,CAAKqC,GAAG,CAACrC,IAAA,CAAKoC,GAAG,CAACyiB,EAAA,EAAIxiB,GAAM,GAAAD,GAAA;AACrC;AAEA,SAAS0iB,eAAgBA,CAAAvc,MAAqB,EAAEkR,IAAe,EAAE;EAC/D,IAAI9d,CAAA,EAAGO,IAAM,EAAAsd,KAAA,EAAOuL,MAAQ,EAAAC,UAAA;EAC5B,IAAIC,UAAa,GAAA1L,cAAA,CAAehR,MAAM,CAAC,EAAE,EAAEkR,IAAA;EAC3C,KAAK9d,CAAA,GAAI,GAAGO,IAAO,GAAAqM,MAAA,CAAOzM,MAAM,EAAEH,CAAA,GAAIO,IAAM,IAAEP,CAAG;IAC/CqpB,UAAa,GAAAD,MAAA;IACbA,MAAS,GAAAE,UAAA;IACTA,UAAa,GAAAtpB,CAAA,GAAIO,IAAA,GAAO,CAAK,IAAAqd,cAAA,CAAehR,MAAM,CAAC5M,CAAA,GAAI,EAAE,EAAE8d,IAAA;IAC3D,IAAI,CAACsL,MAAQ;MACX;;IAEFvL,KAAQ,GAAAjR,MAAM,CAAC5M,CAAE;IACjB,IAAIqpB,UAAY;MACdxL,KAAM,CAAAW,IAAI,GAAGyK,eAAA,CAAgBpL,KAAM,CAAAW,IAAI,EAAEV,IAAK,CAAAxR,IAAI,EAAEwR,IAAA,CAAKvR,KAAK;MAC9DsR,KAAM,CAAAa,IAAI,GAAGuK,eAAA,CAAgBpL,KAAM,CAAAa,IAAI,EAAEZ,IAAK,CAAApL,GAAG,EAAEoL,IAAA,CAAKnL,MAAM;;IAEhE,IAAI2W,UAAY;MACdzL,KAAM,CAAAY,IAAI,GAAGwK,eAAA,CAAgBpL,KAAM,CAAAY,IAAI,EAAEX,IAAK,CAAAxR,IAAI,EAAEwR,IAAA,CAAKvR,KAAK;MAC9DsR,KAAM,CAAAc,IAAI,GAAGsK,eAAA,CAAgBpL,KAAM,CAAAc,IAAI,EAAEb,IAAK,CAAApL,GAAG,EAAEoL,IAAA,CAAKnL,MAAM;;EAElE;AACF;AAEA;;AAEC;AACM,SAAS4W,0BACdA,CAAA3c,MAAqB,EACrBrL,OAAO,EACPuc,IAAe,EACftM,IAAa,EACboH,SAAoB,EACpB;EACA,IAAI5Y,CAAA,EAAWO,IAAA,EAAcsd,KAAoB,EAAA2L,aAAA;;EAGjD,IAAIjoB,OAAA,CAAQkoB,QAAQ,EAAE;IACpB7c,MAAA,GAASA,MAAA,CAAOqZ,MAAM,CAAEiD,EAAO,KAACA,EAAA,CAAG/B,IAAI;;EAGzC,IAAI5lB,OAAA,CAAQmoB,sBAAsB,KAAK,UAAY;IACjDX,mBAAA,CAAoBnc,MAAQ,EAAAgM,SAAA;GACvB;IACL,IAAI+Q,IAAA,GAAOnY,IAAO,GAAA5E,MAAM,CAACA,MAAA,CAAOzM,MAAM,GAAG,CAAE,IAAGyM,MAAM,CAAC,CAAE;IACvD,KAAK5M,CAAA,GAAI,GAAGO,IAAO,GAAAqM,MAAA,CAAOzM,MAAM,EAAEH,CAAA,GAAIO,IAAM,IAAEP,CAAG;MAC/C6d,KAAQ,GAAAjR,MAAM,CAAC5M,CAAE;MACjBwpB,aAAgB,GAAAnC,WAAA,CACdsC,IAAA,EACA9L,KACA,EAAAjR,MAAM,CAACvI,IAAK,CAAAoC,GAAG,CAACzG,CAAI,MAAGO,IAAA,IAAQiR,IAAA,GAAO,IAAI,MAAMjR,IAAK,GACrDgB,OAAA,CAAQqoB,OAAO;MAEjB/L,KAAA,CAAMW,IAAI,GAAGgL,aAAc,CAAArnB,QAAQ,CAACM,CAAC;MACrCob,KAAA,CAAMa,IAAI,GAAG8K,aAAc,CAAArnB,QAAQ,CAACQ,CAAC;MACrCkb,KAAA,CAAMY,IAAI,GAAG+K,aAAc,CAAA/B,IAAI,CAAChlB,CAAC;MACjCob,KAAA,CAAMc,IAAI,GAAG6K,aAAc,CAAA/B,IAAI,CAAC9kB,CAAC;MACjCgnB,IAAO,GAAA9L,KAAA;IACT;;EAGF,IAAItc,OAAA,CAAQ4nB,eAAe,EAAE;IAC3BA,eAAA,CAAgBvc,MAAQ,EAAAkR,IAAA;;AAE5B;;ACzNA;;;;;;GAAA,C;;;AAWO,SAAS+L,eAA2BA,CAAA;EACzC,OAAO,OAAOze,MAAA,KAAW,WAAe,WAAO0e,QAAa;AAC9D;AAEA;;AAEC;AACM,SAASC,cAAeA,CAAAC,OAA0B,EAAqB;EAC5E,IAAI5D,MAAA,GAAS4D,OAAA,CAAQC,UAAU;EAC/B,IAAI7D,MAAU,IAAAA,MAAA,CAAO3nB,QAAQ,OAAO,qBAAuB;IACzD2nB,MAAS,GAACA,MAAA,CAAsB8D,IAAI;;EAEtC,OAAO9D,MAAA;AACT;AAEA;;;AAGC;AAED,SAAS+D,aAAcA,CAAAC,UAA2B,EAAEjT,IAAiB,EAAEkT,cAAsB,EAAE;EAC7F,IAAIC,aAAA;EACJ,IAAI,OAAOF,UAAA,KAAe,QAAU;IAClCE,aAAA,GAAgBtI,QAAA,CAASoI,UAAY;IAErC,IAAIA,UAAW,CAAA/oB,OAAO,CAAC,SAAS,CAAC,CAAG;;MAElCipB,aAAA,GAAgBA,aAAiB,SAAOnT,IAAK,CAAA8S,UAAU,CAACI,cAAe;;GAEpE;IACLC,aAAgB,GAAAF,UAAA;;EAGlB,OAAOE,aAAA;AACT;AAEA,MAAMC,gBAAA,GAAoBC,OAAA,IACxBA,OAAQ,CAAAC,aAAa,CAACC,WAAW,CAACH,gBAAgB,CAACC,OAAA,EAAS,IAAI;AAE3D,SAASG,SAASC,EAAe,EAAEpkB,QAAgB,EAAU;EAClE,OAAO+jB,gBAAA,CAAiBK,EAAI,EAAAC,gBAAgB,CAACrkB,QAAA;AAC/C;AAEA,MAAMskB,SAAY,IAAC,OAAO,SAAS,UAAU,OAAO;AACpD,SAASC,mBAAmBC,MAA2B,EAAE5S,KAAa,EAAE6S,MAAe,EAAa;EAClG,MAAMplB,MAAA,GAAS,EAAC;EAChBolB,MAAS,GAAAA,MAAA,GAAS,GAAM,GAAAA,MAAA,GAAS,EAAE;EACnC,KAAK,IAAIjrB,CAAA,GAAI,CAAG,EAAAA,CAAA,GAAI,GAAGA,CAAK;IAC1B,MAAMkrB,GAAA,GAAMJ,SAAS,CAAC9qB,CAAE;IACxB6F,MAAM,CAACqlB,GAAI,IAAG5rB,UAAW,CAAA0rB,MAAM,CAAC5S,KAAQ,SAAM8S,GAAM,GAAAD,MAAA,CAAO,CAAK;EAClE;EACAplB,MAAA,CAAOgQ,KAAK,GAAGhQ,MAAA,CAAOyG,IAAI,GAAGzG,MAAA,CAAO0G,KAAK;EACzC1G,MAAA,CAAOoW,MAAM,GAAGpW,MAAA,CAAO6M,GAAG,GAAG7M,MAAA,CAAO8M,MAAM;EAC1C,OAAO9M,MAAA;AACT;AAEA,MAAMslB,YAAA,GAAeA,CAAC1oB,CAAA,EAAWE,CAAA,EAAW5B,MAC1C,KAAC,CAAA0B,CAAI,QAAKE,CAAA,GAAI,OAAO,CAAC5B,MAAA,IAAU,CAACA,MAAC,CAAuBqqB,UAAU,CAAD;AAEpE;;;;AAIC;AACD,SAASC,kBACPlnB,CAAkC,EAClC0X,MAAyB,EAKvB;EACF,MAAMyP,OAAA,GAAUnnB,CAAC,CAAiBmnB,OAAO;EACzC,MAAMzqB,MAAA,GAAUyqB,OAAA,IAAWA,OAAQ,CAAAnrB,MAAM,GAAGmrB,OAAO,CAAC,CAAE,IAAGnnB,CAAC;EAC1D,MAAM;IAAConB,OAAA;IAASC;EAAA,CAAQ,GAAG3qB,MAAA;EAC3B,IAAI4qB,GAAA,GAAM,KAAK;EACf,IAAIhpB,CAAG,EAAAE,CAAA;EACP,IAAIwoB,YAAa,CAAAI,OAAA,EAASC,OAAS,EAAArnB,CAAA,CAAEpD,MAAM,CAAG;IAC5C0B,CAAI,GAAA8oB,OAAA;IACJ5oB,CAAI,GAAA6oB,OAAA;GACC;IACL,MAAMhO,IAAA,GAAO3B,MAAA,CAAO6P,qBAAqB;IACzCjpB,CAAA,GAAI5B,MAAO,CAAA8qB,OAAO,GAAGnO,IAAA,CAAKlR,IAAI;IAC9B3J,CAAA,GAAI9B,MAAO,CAAA+qB,OAAO,GAAGpO,IAAA,CAAK9K,GAAG;IAC7B+Y,GAAA,GAAM,IAAI;;EAEZ,OAAO;IAAChpB,CAAA;IAAGE,CAAA;IAAG8oB;EAAG;AACnB;AAEA;;;;;AAKC;AAEM,SAASI,oBACdC,KAAmD,EACnDhY,KAAY,EACc;EAC1B,IAAI,YAAYgY,KAAO;IACrB,OAAOA,KAAA;;EAGT,MAAM;IAACjQ,MAAA;IAAQH;EAAA,CAAwB,GAAG5H,KAAA;EAC1C,MAAMsE,KAAA,GAAQmS,gBAAiB,CAAA1O,MAAA;EAC/B,MAAMkQ,SAAA,GAAY3T,KAAM,CAAA4T,SAAS,KAAK;EACtC,MAAMC,QAAA,GAAWlB,kBAAA,CAAmB3S,KAAO;EAC3C,MAAM8T,OAAA,GAAUnB,kBAAmB,CAAA3S,KAAA,EAAO,QAAU;EACpD,MAAM;IAAC3V,CAAA;IAAGE,CAAA;IAAG8oB;EAAG,CAAC,GAAGJ,iBAAA,CAAkBS,KAAO,EAAAjQ,MAAA;EAC7C,MAAMQ,OAAA,GAAU4P,QAAA,CAAS3f,IAAI,IAAImf,GAAO,IAAAS,OAAA,CAAQ5f,IAAI,CAAD;EACnD,MAAMgQ,OAAA,GAAU2P,QAAA,CAASvZ,GAAG,IAAI+Y,GAAO,IAAAS,OAAA,CAAQxZ,GAAG,CAAD;EAEjD,IAAI;IAACmD,KAAA;IAAOoG;EAAA,CAAO,GAAGnI,KAAA;EACtB,IAAIiY,SAAW;IACblW,KAAA,IAASoW,QAAS,CAAApW,KAAK,GAAGqW,OAAA,CAAQrW,KAAK;IACvCoG,MAAA,IAAUgQ,QAAS,CAAAhQ,MAAM,GAAGiQ,OAAA,CAAQjQ,MAAM;;EAE5C,OAAO;IACLxZ,CAAG,EAAA4B,IAAA,CAAKiB,KAAK,CAAC,CAAC7C,CAAI,GAAA4Z,OAAM,IAAKxG,KAAA,GAAQgG,MAAO,CAAAhG,KAAK,GAAG6F,uBAAA;IACrD/Y,CAAG,EAAA0B,IAAA,CAAKiB,KAAK,CAAC,CAAC3C,CAAI,GAAA2Z,OAAM,IAAKL,MAAA,GAASJ,MAAO,CAAAI,MAAM,GAAGP,uBAAA;EACzD;AACF;AAEA,SAASyQ,iBAAiBtQ,MAAyB,EAAEhG,KAAa,EAAEoG,MAAc,EAAkB;EAClG,IAAIsE,QAAkB,EAAA6L,SAAA;EAEtB,IAAIvW,KAAA,KAAUzT,SAAa,IAAA6Z,MAAA,KAAW7Z,SAAW;IAC/C,MAAMiqB,SAAA,GAAYtC,cAAe,CAAAlO,MAAA;IACjC,IAAI,CAACwQ,SAAW;MACdxW,KAAA,GAAQgG,MAAA,CAAOyQ,WAAW;MAC1BrQ,MAAA,GAASJ,MAAA,CAAO0Q,YAAY;KACvB;MACL,MAAM/O,IAAO,GAAA6O,SAAA,CAAUX,qBAAqB;MAC5C,MAAMc,cAAA,GAAiBjC,gBAAiB,CAAA8B,SAAA;MACxC,MAAMI,eAAA,GAAkB1B,kBAAmB,CAAAyB,cAAA,EAAgB,QAAU;MACrE,MAAME,gBAAA,GAAmB3B,kBAAA,CAAmByB,cAAgB;MAC5D3W,KAAA,GAAQ2H,IAAA,CAAK3H,KAAK,GAAG6W,gBAAA,CAAiB7W,KAAK,GAAG4W,eAAA,CAAgB5W,KAAK;MACnEoG,MAAA,GAASuB,IAAA,CAAKvB,MAAM,GAAGyQ,gBAAA,CAAiBzQ,MAAM,GAAGwQ,eAAA,CAAgBxQ,MAAM;MACvEsE,QAAA,GAAW4J,aAAc,CAAAqC,cAAA,CAAejM,QAAQ,EAAE8L,SAAW;MAC7DD,SAAA,GAAYjC,aAAc,CAAAqC,cAAA,CAAeJ,SAAS,EAAEC,SAAW;;;EAGnE,OAAO;IACLxW,KAAA;IACAoG,MAAA;IACAsE,QAAA,EAAUA,QAAY,IAAA/b,QAAA;IACtB4nB,SAAA,EAAWA,SAAa,IAAA5nB;EAC1B;AACF;AAEA,MAAMmoB,MAAA,GAAUnqB,CAAA,IAAc6B,IAAA,CAAKiB,KAAK,CAAC9C,CAAA,GAAI,EAAM;AAEnD;AACO,SAASoqB,eACd/Q,MAAyB,EACzBgR,OAAgB,EAChBC,QAAiB,EACjBC,WAAoB,EACe;EACnC,MAAM3U,KAAA,GAAQmS,gBAAiB,CAAA1O,MAAA;EAC/B,MAAMmR,OAAA,GAAUjC,kBAAA,CAAmB3S,KAAO;EAC1C,MAAMmI,QAAA,GAAW4J,aAAc,CAAA/R,KAAA,CAAMmI,QAAQ,EAAE1E,MAAA,EAAQ,aAAkB,KAAArX,QAAA;EACzE,MAAM4nB,SAAA,GAAYjC,aAAc,CAAA/R,KAAA,CAAMgU,SAAS,EAAEvQ,MAAA,EAAQ,cAAmB,KAAArX,QAAA;EAC5E,MAAMyoB,aAAA,GAAgBd,gBAAiB,CAAAtQ,MAAA,EAAQgR,OAAS,EAAAC,QAAA;EACxD,IAAI;IAACjX,KAAA;IAAOoG;EAAA,CAAO,GAAGgR,aAAA;EAEtB,IAAI7U,KAAA,CAAM4T,SAAS,KAAK,aAAe;IACrC,MAAME,OAAA,GAAUnB,kBAAmB,CAAA3S,KAAA,EAAO,QAAU;IACpD,MAAM6T,QAAA,GAAWlB,kBAAA,CAAmB3S,KAAO;IAC3CvC,KAAA,IAASoW,QAAS,CAAApW,KAAK,GAAGqW,OAAA,CAAQrW,KAAK;IACvCoG,MAAA,IAAUgQ,QAAS,CAAAhQ,MAAM,GAAGiQ,OAAA,CAAQjQ,MAAM;;EAE5CpG,KAAA,GAAQxR,IAAA,CAAKqC,GAAG,CAAC,CAAG,EAAAmP,KAAA,GAAQmX,OAAA,CAAQnX,KAAK;EACzCoG,MAAS,GAAA5X,IAAA,CAAKqC,GAAG,CAAC,GAAGqmB,WAAA,GAAclX,KAAQ,GAAAkX,WAAA,GAAc9Q,MAAS,GAAA+Q,OAAA,CAAQ/Q,MAAM;EAChFpG,KAAA,GAAQ8W,MAAA,CAAOtoB,IAAK,CAAAoC,GAAG,CAACoP,KAAO,EAAA0K,QAAA,EAAU0M,aAAA,CAAc1M,QAAQ;EAC/DtE,MAAA,GAAS0Q,MAAA,CAAOtoB,IAAK,CAAAoC,GAAG,CAACwV,MAAQ,EAAAmQ,SAAA,EAAWa,aAAA,CAAcb,SAAS;EACnE,IAAIvW,KAAA,IAAS,CAACoG,MAAQ;;;IAGpBA,MAAA,GAAS0Q,MAAA,CAAO9W,KAAQ;;EAG1B,MAAMqX,cAAA,GAAiBL,OAAY,KAAAzqB,SAAA,IAAa0qB,QAAa,KAAA1qB,SAAA;EAE7D,IAAI8qB,cAAA,IAAkBH,WAAA,IAAeE,aAAc,CAAAhR,MAAM,IAAIA,MAAS,GAAAgR,aAAA,CAAchR,MAAM,EAAE;IAC1FA,MAAA,GAASgR,aAAA,CAAchR,MAAM;IAC7BpG,KAAA,GAAQ8W,MAAO,CAAAtoB,IAAA,CAAKoB,KAAK,CAACwW,MAAS,GAAA8Q,WAAA;;EAGrC,OAAO;IAAClX,KAAA;IAAOoG;EAAM;AACvB;AAEA;;;;;;AAMO,SAASkR,WACdA,CAAArZ,KAAY,EACZsZ,UAAkB,EAClBC,UAAoB,EACJ;EAChB,MAAMC,UAAA,GAAaF,UAAc;EACjC,MAAMG,YAAA,GAAelpB,IAAK,CAAAoB,KAAK,CAACqO,KAAA,CAAMmI,MAAM,GAAGqR,UAAA;EAC/C,MAAME,WAAA,GAAcnpB,IAAK,CAAAoB,KAAK,CAACqO,KAAA,CAAM+B,KAAK,GAAGyX,UAAA;EAE7CxZ,KAAA,CAAMmI,MAAM,GAAG5X,IAAA,CAAKoB,KAAK,CAACqO,KAAA,CAAMmI,MAAM;EACtCnI,KAAA,CAAM+B,KAAK,GAAGxR,IAAA,CAAKoB,KAAK,CAACqO,KAAA,CAAM+B,KAAK;EAEpC,MAAMgG,MAAA,GAAS/H,KAAA,CAAM+H,MAAM;;;;EAK3B,IAAIA,MAAA,CAAOzD,KAAK,KAAKiV,UAAA,IAAe,CAACxR,MAAO,CAAAzD,KAAK,CAAC6D,MAAM,IAAI,CAACJ,MAAA,CAAOzD,KAAK,CAACvC,KAAK,CAAI;IACjFgG,MAAO,CAAAzD,KAAK,CAAC6D,MAAM,GAAI,GAAEnI,KAAM,CAAAmI,MAAO,IAAG;IACzCJ,MAAO,CAAAzD,KAAK,CAACvC,KAAK,GAAI,GAAE/B,KAAM,CAAA+B,KAAM,IAAG;;EAGzC,IAAI/B,KAAA,CAAM4H,uBAAuB,KAAK4R,UAC/B,IAAAzR,MAAA,CAAOI,MAAM,KAAKsR,YAClB,IAAA1R,MAAA,CAAOhG,KAAK,KAAK2X,WAAa;IACnC1Z,KAAA,CAAM4H,uBAAuB,GAAG4R,UAAA;IAChCzR,MAAA,CAAOI,MAAM,GAAGsR,YAAA;IAChB1R,MAAA,CAAOhG,KAAK,GAAG2X,WAAA;IACf1Z,KAAM,CAAA2E,GAAG,CAACgV,YAAY,CAACH,UAAA,EAAY,CAAG,KAAGA,UAAA,EAAY,CAAG;IACxD,OAAO,IAAI;;EAEb,OAAO,KAAK;AACd;AAEA;;;;;AAKa,MAAAI,4BAAA,GAAgC,YAAW;EACtD,IAAIC,gBAAA,GAAmB,KAAK;EAC5B,IAAI;IACF,MAAMpsB,OAAU;MACd,IAAIqsB,OAAUA,CAAA;QACZD,gBAAA,GAAmB,IAAI;QACvB,OAAO,KAAK;MACd;IACF;IAEAviB,MAAA,CAAOyiB,gBAAgB,CAAC,MAAQ,MAAI,EAAEtsB,OAAA;IACtC6J,MAAA,CAAO0iB,mBAAmB,CAAC,MAAQ,MAAI,EAAEvsB,OAAA;EAC3C,EAAE,OAAO4C,CAAG;;;EAGZ,OAAOwpB,gBAAA;AACT,CAAK;AAEL;;;;;;;;AAQC;AAEM,SAASI,aACdvD,OAAoB,EACpBhkB,QAA4B,EACR;EACpB,MAAMrI,KAAA,GAAQwsB,QAAA,CAASH,OAAS,EAAAhkB,QAAA;EAChC,MAAM0a,OAAU,GAAA/iB,KAAA,IAASA,KAAM,CAAAgjB,KAAK,CAAC;EACrC,OAAOD,OAAA,GAAU,CAACA,OAAO,CAAC,EAAE,GAAG9e,SAAS;AAC1C;;AC/RA;;;AAGO,SAAS4rB,aAAaC,EAAS,EAAEC,EAAS,EAAE9f,CAAS,EAAE0K,IAAK,EAAE;EACnE,OAAO;IACLrW,CAAG,EAAAwrB,EAAA,CAAGxrB,CAAC,GAAG2L,CAAK,IAAA8f,EAAA,CAAGzrB,CAAC,GAAGwrB,EAAG,CAAAxrB,CAAC,CAAD;IACzBE,CAAG,EAAAsrB,EAAA,CAAGtrB,CAAC,GAAGyL,CAAK,IAAA8f,EAAA,CAAGvrB,CAAC,GAAGsrB,EAAG,CAAAtrB,CAAC;EAC5B;AACF;AAEA;;;AAGO,SAASwrB,sBACdF,EAAS,EACTC,EAAS,EACT9f,CAAS,EAAE0K,IAAkC,EAC7C;EACA,OAAO;IACLrW,CAAG,EAAAwrB,EAAA,CAAGxrB,CAAC,GAAG2L,CAAK,IAAA8f,EAAA,CAAGzrB,CAAC,GAAGwrB,EAAG,CAAAxrB,CAAC,CAAD;IACzBE,CAAG,EAAAmW,IAAA,KAAS,QAAW,GAAA1K,CAAA,GAAI,GAAM,GAAA6f,EAAA,CAAGtrB,CAAC,GAAGurB,EAAG,CAAAvrB,CAAC,GACxCmW,IAAA,KAAS,OAAU,GAAA1K,CAAA,GAAI,IAAI6f,EAAG,CAAAtrB,CAAC,GAAGurB,EAAA,CAAGvrB,CAAC,GACpCyL,CAAI,OAAI8f,EAAG,CAAAvrB,CAAC,GAAGsrB,EAAA,CAAGtrB;EAC1B;AACF;AAEA;;;AAGO,SAASyrB,qBAAqBH,EAAe,EAAEC,EAAe,EAAE9f,CAAS,EAAE0K,IAAK,EAAE;EACvF,MAAMuV,GAAM;IAAC5rB,CAAA,EAAGwrB,EAAA,CAAGxP,IAAI;IAAE9b,CAAA,EAAGsrB,EAAA,CAAGtP;EAAI;EACnC,MAAM2P,GAAM;IAAC7rB,CAAA,EAAGyrB,EAAA,CAAG1P,IAAI;IAAE7b,CAAA,EAAGurB,EAAA,CAAGxP;EAAI;EACnC,MAAM7a,CAAA,GAAImqB,YAAa,CAAAC,EAAA,EAAII,GAAK,EAAAjgB,CAAA;EAChC,MAAMtK,CAAA,GAAIkqB,YAAa,CAAAK,GAAA,EAAKC,GAAK,EAAAlgB,CAAA;EACjC,MAAMmgB,CAAA,GAAIP,YAAa,CAAAM,GAAA,EAAKJ,EAAI,EAAA9f,CAAA;EAChC,MAAMqC,CAAA,GAAIud,YAAa,CAAAnqB,CAAA,EAAGC,CAAG,EAAAsK,CAAA;EAC7B,MAAMjK,CAAA,GAAI6pB,YAAa,CAAAlqB,CAAA,EAAGyqB,CAAG,EAAAngB,CAAA;EAC7B,OAAO4f,YAAA,CAAavd,CAAA,EAAGtM,CAAG,EAAAiK,CAAA;AAC5B;AChCA,MAAMogB,qBAAwB,YAAAA,CAASC,KAAa,EAAE5Y,KAAa,EAAc;EAC/E,OAAO;IACLpT,EAAEA,CAAC,EAAE;MACH,OAAOgsB,KAAA,GAAQA,KAAA,GAAQ5Y,KAAQ,GAAApT,CAAA;IACjC;IACAisB,SAAStS,CAAC,EAAE;MACVvG,KAAQ,GAAAuG,CAAA;IACV;IACA4C,UAAU7S,KAAK,EAAE;MACf,IAAIA,KAAA,KAAU,QAAU;QACtB,OAAOA,KAAA;;MAET,OAAOA,KAAA,KAAU,OAAU,YAAS,OAAO;IAC7C;IACAwiB,KAAMA,CAAAlsB,CAAC,EAAEtE,KAAK,EAAE;MACd,OAAOsE,CAAI,GAAAtE,KAAA;IACb;IACAywB,UAAWA,CAAAnsB,CAAC,EAAEosB,SAAS,EAAE;MACvB,OAAOpsB,CAAI,GAAAosB,SAAA;IACb;EACF;AACF;AAEA,MAAMC,qBAAA,GAAwB,SAAAA,CAAA,EAAuB;EACnD,OAAO;IACLrsB,EAAEA,CAAC,EAAE;MACH,OAAOA,CAAA;IACT;IACAisB,QAASA,CAAAtS,CAAC,EAAE,EACZ;IACA4C,UAAU7S,KAAK,EAAE;MACf,OAAOA,KAAA;IACT;IACAwiB,KAAMA,CAAAlsB,CAAC,EAAEtE,KAAK,EAAE;MACd,OAAOsE,CAAI,GAAAtE,KAAA;IACb;IACAywB,UAAWA,CAAAnsB,CAAC,EAAEssB,UAAU,EAAE;MACxB,OAAOtsB,CAAA;IACT;EACF;AACF;AAEO,SAASusB,aAAcA,CAAAxiB,GAAY,EAAEiiB,KAAa,EAAE5Y,KAAa,EAAE;EACxE,OAAOrJ,GAAM,GAAAgiB,qBAAA,CAAsBC,KAAO,EAAA5Y,KAAA,IAASiZ,qBAAuB;AAC5E;AAEO,SAASG,sBAAsBxW,GAA6B,EAAEyW,SAAwB,EAAE;EAC7F,IAAI9W,KAA4B,EAAA+W,QAAA;EAChC,IAAID,SAAA,KAAc,KAAS,IAAAA,SAAA,KAAc,KAAO;IAC9C9W,KAAQ,GAAAK,GAAA,CAAIoD,MAAM,CAACzD,KAAK;IACxB+W,QAAW,IACT/W,KAAA,CAAMyS,gBAAgB,CAAC,cACvBzS,KAAA,CAAMgX,mBAAmB,CAAC,aAC3B;IAEDhX,KAAM,CAAAiX,WAAW,CAAC,aAAaH,SAAW;IACzCzW,GAAA,CAAiD6W,iBAAiB,GAAGH,QAAA;;AAE1E;AAEO,SAASI,qBAAqB9W,GAA6B,EAAE0W,QAA2B,EAAE;EAC/F,IAAIA,QAAA,KAAa/sB,SAAW;IAC1B,OAAQqW,GAAA,CAAiD6W,iBAAiB;IAC1E7W,GAAA,CAAIoD,MAAM,CAACzD,KAAK,CAACiX,WAAW,CAAC,WAAa,EAAAF,QAAQ,CAAC,EAAE,EAAEA,QAAQ,CAAC,CAAE;;AAEtE;AC/DA,SAASK,UAAWA,CAAAhpB,QAAQ,EAAE;EAC5B,IAAIA,QAAA,KAAa,OAAS;IACxB,OAAO;MACLipB,OAAS,EAAA1nB,aAAA;MACT2nB,OAAS,EAAA7nB,UAAA;MACT8nB,SAAW,EAAA7nB;IACb;;EAEF,OAAO;IACL2nB,OAAS,EAAA/mB,UAAA;IACTgnB,OAAS,EAAAA,CAAC7rB,CAAG,EAAAC,CAAA,KAAMD,CAAI,GAAAC,CAAA;IACvB6rB,SAAA,EAAWltB,CAAK,IAAAA;EAClB;AACF;AAEA,SAASmtB,gBAAiBA,CAAAC,IAAA,EAAkC;EAAA,IAAlC;IAAC7nB,KAAK;IAAEC,GAAG;IAAE8E,KAAK;IAAEyE,IAAI;IAAE4G;EAAK,CAAC,GAAAyX,IAAA;EACxD,OAAO;IACL7nB,KAAA,EAAOA,KAAQ,GAAA+E,KAAA;IACf9E,GAAA,EAAKA,GAAM,GAAA8E,KAAA;IACXyE,IAAA,EAAMA,IAAA,IAAQ,CAACvJ,GAAA,GAAMD,KAAQ,QAAK+E,KAAU;IAC5CqL;EACF;AACF;AAEA,SAAS0X,WAAWC,OAAO,EAAEnjB,MAAM,EAAEoI,MAAM,EAAE;EAC3C,MAAM;IAACxO,QAAA;IAAUwB,KAAA,EAAOgoB,UAAA;IAAY/nB,GAAA,EAAKgoB;EAAQ,CAAC,GAAGjb,MAAA;EACrD,MAAM;IAACya,OAAO;IAAEE;EAAS,CAAC,GAAGH,UAAW,CAAAhpB,QAAA;EACxC,MAAMuG,KAAA,GAAQH,MAAA,CAAOzM,MAAM;EAE3B,IAAI;IAAC6H,KAAK;IAAEC,GAAA;IAAKuJ;EAAA,CAAK,GAAGue,OAAA;EACzB,IAAI/vB,CAAG,EAAAO,IAAA;EAEP,IAAIiR,IAAM;IACRxJ,KAAS,IAAA+E,KAAA;IACT9E,GAAO,IAAA8E,KAAA;IACP,KAAK/M,CAAA,GAAI,GAAGO,IAAO,GAAAwM,KAAK,EAAE/M,CAAI,GAAAO,IAAA,EAAM,EAAEP,CAAG;MACvC,IAAI,CAACyvB,OAAQ,CAAAE,SAAA,CAAU/iB,MAAM,CAAC5E,KAAQ,GAAA+E,KAAA,CAAM,CAACvG,QAAA,CAAS,CAAG,EAAAwpB,UAAA,EAAYC,QAAW;QAC9E;;MAEFjoB,KAAA;MACAC,GAAA;IACF;IACAD,KAAS,IAAA+E,KAAA;IACT9E,GAAO,IAAA8E,KAAA;;EAGT,IAAI9E,GAAA,GAAMD,KAAO;IACfC,GAAO,IAAA8E,KAAA;;EAET,OAAO;IAAC/E,KAAA;IAAOC,GAAA;IAAKuJ,IAAA;IAAM4G,KAAA,EAAO2X,OAAA,CAAQ3X;EAAK;AAChD;AAgBO,SAAS8X,aAAcA,CAAAH,OAAO,EAAEnjB,MAAM,EAAEoI,MAAM,EAAE;EACrD,IAAI,CAACA,MAAQ;IACX,OAAO,CAAC+a,OAAA,CAAQ;;EAGlB,MAAM;IAACvpB,QAAA;IAAUwB,KAAA,EAAOgoB,UAAA;IAAY/nB,GAAA,EAAKgoB;EAAQ,CAAC,GAAGjb,MAAA;EACrD,MAAMjI,KAAA,GAAQH,MAAA,CAAOzM,MAAM;EAC3B,MAAM;IAACuvB,OAAA;IAASD,OAAA;IAASE;EAAS,CAAC,GAAGH,UAAW,CAAAhpB,QAAA;EACjD,MAAM;IAACwB,KAAA;IAAOC,GAAA;IAAKuJ,IAAA;IAAM4G;EAAA,CAAM,GAAG0X,UAAW,CAAAC,OAAA,EAASnjB,MAAQ,EAAAoI,MAAA;EAE9D,MAAMnP,MAAA,GAAS,EAAE;EACjB,IAAIsqB,MAAA,GAAS,KAAK;EAClB,IAAIC,QAAA,GAAW,IAAI;EACnB,IAAIjyB,KAAA,EAAO0f,KAAO,EAAAwS,SAAA;EAElB,MAAMC,aAAA,GAAgBA,CAAA,KAAMb,OAAQ,CAAAO,UAAA,EAAYK,SAAA,EAAWlyB,KAAU,KAAAuxB,OAAA,CAAQM,UAAA,EAAYK,SAAe;EACxG,MAAME,WAAA,GAAcA,CAAA,KAAMb,OAAQ,CAAAO,QAAA,EAAU9xB,KAAA,MAAW,CAAK,IAAAsxB,OAAA,CAAQQ,QAAA,EAAUI,SAAW,EAAAlyB,KAAA;EACzF,MAAMqyB,WAAA,GAAcA,CAAA,KAAML,MAAU,IAAAG,aAAA;EACpC,MAAMG,UAAA,GAAaA,CAAA,KAAM,CAACN,MAAU,IAAAI,WAAA;EAEpC,KAAK,IAAIvwB,CAAA,GAAIgI,KAAO,EAAA2hB,IAAA,GAAO3hB,KAAA,EAAOhI,CAAK,IAAAiI,GAAA,EAAK,EAAEjI,CAAG;IAC/C6d,KAAQ,GAAAjR,MAAM,CAAC5M,CAAA,GAAI+M,KAAM;IAEzB,IAAI8Q,KAAA,CAAMsJ,IAAI,EAAE;MACd;;IAGFhpB,KAAQ,GAAAwxB,SAAA,CAAU9R,KAAK,CAACrX,QAAS;IAEjC,IAAIrI,KAAA,KAAUkyB,SAAW;MACvB;;IAGFF,MAAS,GAAAV,OAAA,CAAQtxB,KAAA,EAAO6xB,UAAY,EAAAC,QAAA;IAEpC,IAAIG,QAAA,KAAa,IAAI,IAAII,WAAe;MACtCJ,QAAA,GAAWV,OAAQ,CAAAvxB,KAAA,EAAO6xB,UAAgB,UAAIhwB,CAAA,GAAI2pB,IAAI;;IAGxD,IAAIyG,QAAA,KAAa,IAAI,IAAIK,UAAc;MACrC5qB,MAAO,CAAA5C,IAAI,CAAC2sB,gBAAiB;QAAC5nB,KAAO,EAAAooB,QAAA;QAAUnoB,GAAK,EAAAjI,CAAA;QAAGwR,IAAA;QAAMzE,KAAA;QAAOqL;MAAK;MACzEgY,QAAA,GAAW,IAAI;;IAEjBzG,IAAO,GAAA3pB,CAAA;IACPqwB,SAAY,GAAAlyB,KAAA;EACd;EAEA,IAAIiyB,QAAA,KAAa,IAAI,EAAE;IACrBvqB,MAAO,CAAA5C,IAAI,CAAC2sB,gBAAiB;MAAC5nB,KAAO,EAAAooB,QAAA;MAAUnoB,GAAA;MAAKuJ,IAAA;MAAMzE,KAAA;MAAOqL;IAAK;;EAGxE,OAAOvS,MAAA;AACT;AAYO,SAAS6qB,eAAevR,IAAI,EAAEnK,MAAM,EAAE;EAC3C,MAAMnP,MAAA,GAAS,EAAE;EACjB,MAAM8qB,QAAA,GAAWxR,IAAA,CAAKwR,QAAQ;EAE9B,KAAK,IAAI3wB,CAAI,MAAGA,CAAA,GAAI2wB,QAAS,CAAAxwB,MAAM,EAAEH,CAAK;IACxC,MAAM4wB,GAAA,GAAMV,aAAA,CAAcS,QAAQ,CAAC3wB,CAAA,CAAE,EAAEmf,IAAA,CAAKvS,MAAM,EAAEoI,MAAA;IACpD,IAAI4b,GAAA,CAAIzwB,MAAM,EAAE;MACd0F,MAAA,CAAO5C,IAAI,CAAI,GAAA2tB,GAAA;;EAEnB;EACA,OAAO/qB,MAAA;AACT;AAKA,SAASgrB,gBAAgBjkB,MAAM,EAAEG,KAAK,EAAEyE,IAAI,EAAEiY,QAAQ,EAAE;EACtD,IAAIzhB,KAAQ;EACZ,IAAIC,GAAA,GAAM8E,KAAQ;EAElB,IAAIyE,IAAA,IAAQ,CAACiY,QAAU;IAErB,OAAOzhB,KAAA,GAAQ+E,KAAA,IAAS,CAACH,MAAM,CAAC5E,KAAM,EAACmf,IAAI,EAAE;MAC3Cnf,KAAA;IACF;;EAIF,OAAOA,KAAA,GAAQ+E,KAAS,IAAAH,MAAM,CAAC5E,KAAM,EAACmf,IAAI,EAAE;IAC1Cnf,KAAA;EACF;EAGAA,KAAS,IAAA+E,KAAA;EAET,IAAIyE,IAAM;IAERvJ,GAAO,IAAAD,KAAA;;EAGT,OAAOC,GAAA,GAAMD,KAAA,IAAS4E,MAAM,CAAC3E,GAAA,GAAM8E,KAAM,EAACoa,IAAI,EAAE;IAC9Clf,GAAA;EACF;EAGAA,GAAO,IAAA8E,KAAA;EAEP,OAAO;IAAC/E,KAAA;IAAOC;EAAG;AACpB;AASA,SAAS6oB,cAAclkB,MAAM,EAAE5E,KAAK,EAAEtB,GAAG,EAAE8K,IAAI,EAAE;EAC/C,MAAMzE,KAAA,GAAQH,MAAA,CAAOzM,MAAM;EAC3B,MAAM0F,MAAA,GAAS,EAAE;EACjB,IAAIsD,IAAO,GAAAnB,KAAA;EACX,IAAI2hB,IAAA,GAAO/c,MAAM,CAAC5E,KAAM;EACxB,IAAIC,GAAA;EAEJ,KAAKA,GAAA,GAAMD,KAAQ,MAAGC,GAAO,IAAAvB,GAAA,EAAK,EAAEuB,GAAK;IACvC,MAAM8oB,GAAM,GAAAnkB,MAAM,CAAC3E,GAAA,GAAM8E,KAAM;IAC/B,IAAIgkB,GAAI,CAAA5J,IAAI,IAAI4J,GAAA,CAAIC,IAAI,EAAE;MACxB,IAAI,CAACrH,IAAK,CAAAxC,IAAI,EAAE;QACd3V,IAAA,GAAO,KAAK;QACZ3L,MAAA,CAAO5C,IAAI,CAAC;UAAC+E,KAAA,EAAOA,KAAQ,GAAA+E,KAAA;UAAO9E,GAAA,EAAK,CAACA,GAAM,QAAK8E,KAAA;UAAOyE;QAAI;QAE/DxJ,KAAA,GAAQmB,IAAO,GAAA4nB,GAAA,CAAIC,IAAI,GAAG/oB,GAAA,GAAM,IAAI;;KAEjC;MACLkB,IAAO,GAAAlB,GAAA;MACP,IAAI0hB,IAAA,CAAKxC,IAAI,EAAE;QACbnf,KAAQ,GAAAC,GAAA;;;IAGZ0hB,IAAO,GAAAoH,GAAA;EACT;EAEA,IAAI5nB,IAAA,KAAS,IAAI,EAAE;IACjBtD,MAAA,CAAO5C,IAAI,CAAC;MAAC+E,KAAA,EAAOA,KAAQ,GAAA+E,KAAA;MAAO9E,GAAA,EAAKkB,IAAO,GAAA4D,KAAA;MAAOyE;IAAI;;EAG5D,OAAO3L,MAAA;AACT;AAUO,SAASorB,iBAAiB9R,IAAI,EAAE+R,cAAc,EAAE;EACrD,MAAMtkB,MAAA,GAASuS,IAAA,CAAKvS,MAAM;EAC1B,MAAM6c,QAAW,GAAAtK,IAAA,CAAK5d,OAAO,CAACkoB,QAAQ;EACtC,MAAM1c,KAAA,GAAQH,MAAA,CAAOzM,MAAM;EAE3B,IAAI,CAAC4M,KAAO;IACV,OAAO,EAAE;;EAGX,MAAMyE,IAAO,IAAC,CAAC2N,IAAA,CAAKgS,KAAK;EACzB,MAAM;IAACnpB,KAAA;IAAOC;EAAA,CAAI,GAAG4oB,eAAA,CAAgBjkB,MAAQ,EAAAG,KAAA,EAAOyE,IAAM,EAAAiY,QAAA;EAE1D,IAAIA,QAAA,KAAa,IAAI,EAAE;IACrB,OAAO2H,aAAA,CAAcjS,IAAM,GAAC;MAACnX,KAAA;MAAOC,GAAA;MAAKuJ;IAAI,EAAE,EAAE5E,MAAQ,EAAAskB,cAAA;;EAG3D,MAAMxqB,GAAM,GAAAuB,GAAA,GAAMD,KAAQ,GAAAC,GAAA,GAAM8E,KAAA,GAAQ9E,GAAG;EAC3C,MAAMopB,YAAA,GAAe,CAAC,CAAClS,IAAA,CAAKmS,SAAS,IAAItpB,KAAA,KAAU,CAAK,IAAAC,GAAA,KAAQ8E,KAAQ;EACxE,OAAOqkB,aAAA,CAAcjS,IAAM,EAAA2R,aAAA,CAAclkB,MAAA,EAAQ5E,KAAO,EAAAtB,GAAA,EAAK2qB,YAAA,GAAezkB,MAAQ,EAAAskB,cAAA;AACtF;AAQA,SAASE,cAAcjS,IAAI,EAAEwR,QAAQ,EAAE/jB,MAAM,EAAEskB,cAAc,EAAE;EAC7D,IAAI,CAACA,cAAkB,KAACA,cAAA,CAAerM,UAAU,IAAI,CAACjY,MAAQ;IAC5D,OAAO+jB,QAAA;;EAET,OAAOY,eAAA,CAAgBpS,IAAM,EAAAwR,QAAA,EAAU/jB,MAAQ,EAAAskB,cAAA;AACjD;AASA,SAASK,gBAAgBpS,IAAI,EAAEwR,QAAQ,EAAE/jB,MAAM,EAAEskB,cAAc,EAAE;EAC/D,MAAMM,YAAe,GAAArS,IAAA,CAAKsS,MAAM,CAAC3V,UAAU;EAC3C,MAAM4V,SAAA,GAAYC,SAAU,CAAAxS,IAAA,CAAK5d,OAAO;EACxC,MAAM;IAACqwB,aAAe,EAAAlxB,YAAA;IAAca,OAAA,EAAS;MAACkoB;IAAQ;EAAC,CAAC,GAAGtK,IAAA;EAC3D,MAAMpS,KAAA,GAAQH,MAAA,CAAOzM,MAAM;EAC3B,MAAM0F,MAAA,GAAS,EAAE;EACjB,IAAIgsB,SAAY,GAAAH,SAAA;EAChB,IAAI1pB,KAAQ,GAAA2oB,QAAQ,CAAC,EAAE,CAAC3oB,KAAK;EAC7B,IAAIhI,CAAI,GAAAgI,KAAA;EAER,SAAS8pB,SAAS3pB,CAAC,EAAEhE,CAAC,EAAE4tB,CAAC,EAAEC,EAAE,EAAE;IAC7B,MAAMC,GAAM,GAAAxI,QAAA,GAAW,CAAC,IAAI,CAAC;IAC7B,IAAIthB,CAAA,KAAMhE,CAAG;MACX;;IAGFgE,CAAK,IAAA4E,KAAA;IACL,OAAOH,MAAM,CAACzE,CAAA,GAAI4E,KAAM,EAACoa,IAAI,EAAE;MAC7Bhf,CAAK,IAAA8pB,GAAA;IACP;IACA,OAAOrlB,MAAM,CAACzI,CAAA,GAAI4I,KAAM,EAACoa,IAAI,EAAE;MAC7BhjB,CAAK,IAAA8tB,GAAA;IACP;IACA,IAAI9pB,CAAA,GAAI4E,KAAU,KAAA5I,CAAA,GAAI4I,KAAO;MAC3BlH,MAAA,CAAO5C,IAAI,CAAC;QAAC+E,KAAA,EAAOG,CAAI,GAAA4E,KAAA;QAAO9E,GAAA,EAAK9D,CAAI,GAAA4I,KAAA;QAAOyE,IAAM,EAAAugB,CAAA;QAAG3Z,KAAO,EAAA4Z;MAAE;MACjEH,SAAY,GAAAG,EAAA;MACZhqB,KAAA,GAAQ7D,CAAI,GAAA4I,KAAA;;EAEhB;EAEA,KAAK,MAAMgjB,OAAA,IAAWY,QAAU;IAC9B3oB,KAAQ,GAAAyhB,QAAA,GAAWzhB,KAAQ,GAAA+nB,OAAA,CAAQ/nB,KAAK;IACxC,IAAI2hB,IAAO,GAAA/c,MAAM,CAAC5E,KAAA,GAAQ+E,KAAM;IAChC,IAAIqL,KAAA;IACJ,KAAKpY,CAAA,GAAIgI,KAAQ,MAAGhI,CAAA,IAAK+vB,OAAQ,CAAA9nB,GAAG,EAAEjI,CAAK;MACzC,MAAMkpB,EAAK,GAAAtc,MAAM,CAAC5M,CAAA,GAAI+M,KAAM;MAC5BqL,KAAA,GAAQuZ,SAAU,CAAAT,cAAA,CAAerM,UAAU,CAACnC,aAAA,CAAc8O,YAAc;QACtElzB,IAAM;QACN4zB,EAAI,EAAAvI,IAAA;QACJsE,EAAI,EAAA/E,EAAA;QACJiJ,WAAA,EAAa,CAACnyB,CAAI,QAAK+M,KAAA;QACvBqlB,WAAA,EAAapyB,CAAI,GAAA+M,KAAA;QACjBrM;MACF;MACA,IAAI2xB,YAAA,CAAaja,KAAA,EAAOyZ,SAAY;QAClCC,QAAA,CAAS9pB,KAAO,EAAAhI,CAAA,GAAI,CAAG,EAAA+vB,OAAA,CAAQve,IAAI,EAAEqgB,SAAA;;MAEvClI,IAAO,GAAAT,EAAA;MACP2I,SAAY,GAAAzZ,KAAA;IACd;IACA,IAAIpQ,KAAA,GAAQhI,CAAA,GAAI,CAAG;MACjB8xB,QAAA,CAAS9pB,KAAO,EAAAhI,CAAA,GAAI,CAAG,EAAA+vB,OAAA,CAAQve,IAAI,EAAEqgB,SAAA;;EAEzC;EAEA,OAAOhsB,MAAA;AACT;AAEA,SAAS8rB,UAAUpwB,OAAO,EAAE;EAC1B,OAAO;IACLkW,eAAA,EAAiBlW,OAAA,CAAQkW,eAAe;IACxC6a,cAAA,EAAgB/wB,OAAA,CAAQ+wB,cAAc;IACtCC,UAAA,EAAYhxB,OAAA,CAAQgxB,UAAU;IAC9BC,gBAAA,EAAkBjxB,OAAA,CAAQixB,gBAAgB;IAC1CC,eAAA,EAAiBlxB,OAAA,CAAQkxB,eAAe;IACxC/U,WAAA,EAAanc,OAAA,CAAQmc,WAAW;IAChChG,WAAA,EAAanW,OAAA,CAAQmW;EACvB;AACF;AAEA,SAAS2a,YAAaA,CAAAja,KAAK,EAAEyZ,SAAS,EAAE;EACtC,IAAI,CAACA,SAAW;IACd,OAAO,KAAK;;EAEd,MAAM9W,KAAA,GAAQ,EAAE;EAChB,MAAM2X,QAAW,YAAAA,CAAStxB,GAAG,EAAEjD,KAAK,EAAE;IACpC,IAAI,CAACwS,mBAAA,CAAoBxS,KAAQ;MAC/B,OAAOA,KAAA;;IAET,IAAI,CAAC4c,KAAA,CAAMrG,QAAQ,CAACvW,KAAQ;MAC1B4c,KAAA,CAAM9X,IAAI,CAAC9E,KAAA;;IAEb,OAAO4c,KAAA,CAAM1Z,OAAO,CAAClD,KAAA;EACvB;EACA,OAAO8U,IAAA,CAAKC,SAAS,CAACkF,KAAA,EAAOsa,QAAA,MAAczf,IAAK,CAAAC,SAAS,CAAC2e,SAAW,EAAAa,QAAA;AACvE"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}