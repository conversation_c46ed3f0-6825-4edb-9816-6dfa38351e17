{"ast": null, "code": "import { getWindow } from 'ssr-window';\nimport { getSupport } from './get-support.js';\nlet deviceCached;\nfunction calcDevice() {\n  let {\n    userAgent\n  } = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  const support = getSupport();\n  const window = getWindow();\n  const platform = window.navigator.platform;\n  const ua = userAgent || window.navigator.userAgent;\n  const device = {\n    ios: false,\n    android: false\n  };\n  const screenWidth = window.screen.width;\n  const screenHeight = window.screen.height;\n  const android = ua.match(/(Android);?[\\s\\/]+([\\d.]+)?/); // eslint-disable-line\n  let ipad = ua.match(/(iPad).*OS\\s([\\d_]+)/);\n  const ipod = ua.match(/(iPod)(.*OS\\s([\\d_]+))?/);\n  const iphone = !ipad && ua.match(/(iPhone\\sOS|iOS)\\s([\\d_]+)/);\n  const windows = platform === 'Win32';\n  let macos = platform === 'MacIntel';\n\n  // iPadOs 13 fix\n  const iPadScreens = ['1024x1366', '1366x1024', '834x1194', '1194x834', '834x1112', '1112x834', '768x1024', '1024x768', '820x1180', '1180x820', '810x1080', '1080x810'];\n  if (!ipad && macos && support.touch && iPadScreens.indexOf(`${screenWidth}x${screenHeight}`) >= 0) {\n    ipad = ua.match(/(Version)\\/([\\d.]+)/);\n    if (!ipad) ipad = [0, 1, '13_0_0'];\n    macos = false;\n  }\n\n  // Android\n  if (android && !windows) {\n    device.os = 'android';\n    device.android = true;\n  }\n  if (ipad || iphone || ipod) {\n    device.os = 'ios';\n    device.ios = true;\n  }\n\n  // Export object\n  return device;\n}\nfunction getDevice() {\n  let overrides = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  if (!deviceCached) {\n    deviceCached = calcDevice(overrides);\n  }\n  return deviceCached;\n}\nexport { getDevice };", "map": {"version": 3, "names": ["getWindow", "getSupport", "deviceCached", "calcDevice", "userAgent", "arguments", "length", "undefined", "support", "window", "platform", "navigator", "ua", "device", "ios", "android", "screenWidth", "screen", "width", "screenHeight", "height", "match", "ipad", "ipod", "iphone", "windows", "macos", "iPadScreens", "touch", "indexOf", "os", "getDevice", "overrides"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/shared/get-device.js"], "sourcesContent": ["import { getWindow } from 'ssr-window';\nimport { getSupport } from './get-support.js';\nlet deviceCached;\nfunction calcDevice({\n  userAgent\n} = {}) {\n  const support = getSupport();\n  const window = getWindow();\n  const platform = window.navigator.platform;\n  const ua = userAgent || window.navigator.userAgent;\n  const device = {\n    ios: false,\n    android: false\n  };\n  const screenWidth = window.screen.width;\n  const screenHeight = window.screen.height;\n  const android = ua.match(/(Android);?[\\s\\/]+([\\d.]+)?/); // eslint-disable-line\n  let ipad = ua.match(/(iPad).*OS\\s([\\d_]+)/);\n  const ipod = ua.match(/(iPod)(.*OS\\s([\\d_]+))?/);\n  const iphone = !ipad && ua.match(/(iPhone\\sOS|iOS)\\s([\\d_]+)/);\n  const windows = platform === 'Win32';\n  let macos = platform === 'MacIntel';\n\n  // iPadOs 13 fix\n  const iPadScreens = ['1024x1366', '1366x1024', '834x1194', '1194x834', '834x1112', '1112x834', '768x1024', '1024x768', '820x1180', '1180x820', '810x1080', '1080x810'];\n  if (!ipad && macos && support.touch && iPadScreens.indexOf(`${screenWidth}x${screenHeight}`) >= 0) {\n    ipad = ua.match(/(Version)\\/([\\d.]+)/);\n    if (!ipad) ipad = [0, 1, '13_0_0'];\n    macos = false;\n  }\n\n  // Android\n  if (android && !windows) {\n    device.os = 'android';\n    device.android = true;\n  }\n  if (ipad || iphone || ipod) {\n    device.os = 'ios';\n    device.ios = true;\n  }\n\n  // Export object\n  return device;\n}\nfunction getDevice(overrides = {}) {\n  if (!deviceCached) {\n    deviceCached = calcDevice(overrides);\n  }\n  return deviceCached;\n}\nexport { getDevice };"], "mappings": "AAAA,SAASA,SAAS,QAAQ,YAAY;AACtC,SAASC,UAAU,QAAQ,kBAAkB;AAC7C,IAAIC,YAAY;AAChB,SAASC,UAAUA,CAAA,EAEX;EAAA,IAFY;IAClBC;EACF,CAAC,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EACJ,MAAMG,OAAO,GAAGP,UAAU,EAAE;EAC5B,MAAMQ,MAAM,GAAGT,SAAS,EAAE;EAC1B,MAAMU,QAAQ,GAAGD,MAAM,CAACE,SAAS,CAACD,QAAQ;EAC1C,MAAME,EAAE,GAAGR,SAAS,IAAIK,MAAM,CAACE,SAAS,CAACP,SAAS;EAClD,MAAMS,MAAM,GAAG;IACbC,GAAG,EAAE,KAAK;IACVC,OAAO,EAAE;EACX,CAAC;EACD,MAAMC,WAAW,GAAGP,MAAM,CAACQ,MAAM,CAACC,KAAK;EACvC,MAAMC,YAAY,GAAGV,MAAM,CAACQ,MAAM,CAACG,MAAM;EACzC,MAAML,OAAO,GAAGH,EAAE,CAACS,KAAK,CAAC,6BAA6B,CAAC,CAAC,CAAC;EACzD,IAAIC,IAAI,GAAGV,EAAE,CAACS,KAAK,CAAC,sBAAsB,CAAC;EAC3C,MAAME,IAAI,GAAGX,EAAE,CAACS,KAAK,CAAC,yBAAyB,CAAC;EAChD,MAAMG,MAAM,GAAG,CAACF,IAAI,IAAIV,EAAE,CAACS,KAAK,CAAC,4BAA4B,CAAC;EAC9D,MAAMI,OAAO,GAAGf,QAAQ,KAAK,OAAO;EACpC,IAAIgB,KAAK,GAAGhB,QAAQ,KAAK,UAAU;;EAEnC;EACA,MAAMiB,WAAW,GAAG,CAAC,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC;EACtK,IAAI,CAACL,IAAI,IAAII,KAAK,IAAIlB,OAAO,CAACoB,KAAK,IAAID,WAAW,CAACE,OAAO,CAAE,GAAEb,WAAY,IAAGG,YAAa,EAAC,CAAC,IAAI,CAAC,EAAE;IACjGG,IAAI,GAAGV,EAAE,CAACS,KAAK,CAAC,qBAAqB,CAAC;IACtC,IAAI,CAACC,IAAI,EAAEA,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC;IAClCI,KAAK,GAAG,KAAK;EACf;;EAEA;EACA,IAAIX,OAAO,IAAI,CAACU,OAAO,EAAE;IACvBZ,MAAM,CAACiB,EAAE,GAAG,SAAS;IACrBjB,MAAM,CAACE,OAAO,GAAG,IAAI;EACvB;EACA,IAAIO,IAAI,IAAIE,MAAM,IAAID,IAAI,EAAE;IAC1BV,MAAM,CAACiB,EAAE,GAAG,KAAK;IACjBjB,MAAM,CAACC,GAAG,GAAG,IAAI;EACnB;;EAEA;EACA,OAAOD,MAAM;AACf;AACA,SAASkB,SAASA,CAAA,EAAiB;EAAA,IAAhBC,SAAS,GAAA3B,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAC/B,IAAI,CAACH,YAAY,EAAE;IACjBA,YAAY,GAAGC,UAAU,CAAC6B,SAAS,CAAC;EACtC;EACA,OAAO9B,YAAY;AACrB;AACA,SAAS6B,SAAS"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}