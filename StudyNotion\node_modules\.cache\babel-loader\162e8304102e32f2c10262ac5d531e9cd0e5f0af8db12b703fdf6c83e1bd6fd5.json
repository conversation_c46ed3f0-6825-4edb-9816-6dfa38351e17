{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\temp\\\\StudyNotion\\\\src\\\\components\\\\core\\\\Dashboard\\\\AddCourse\\\\PublishCourse\\\\index.jsx\",\n  _s = $RefreshSig$();\nimport { useEffect, useState } from \"react\";\nimport { useForm } from \"react-hook-form\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useNavigate } from \"react-router-dom\";\nimport { editCourseDetails } from \"../../../../../services/operations/courseDetailsAPI\";\nimport { resetCourseState, setStep } from \"../../../../../slices/courseSlice\";\nimport { COURSE_STATUS } from \"../../../../../utils/constants\";\nimport IconBtn from \"../../../../common/IconBtn\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function PublishCourse() {\n  _s();\n  const {\n    register,\n    handleSubmit,\n    setValue,\n    getValues\n  } = useForm();\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const {\n    token\n  } = useSelector(state => state.auth);\n  const {\n    course\n  } = useSelector(state => state.course);\n  const [loading, setLoading] = useState(false);\n  useEffect(() => {\n    if ((course === null || course === void 0 ? void 0 : course.status) === COURSE_STATUS.PUBLISHED) {\n      setValue(\"public\", true);\n    }\n  }, []);\n  const goBack = () => {\n    dispatch(setStep(2));\n  };\n  const goToCourses = () => {\n    dispatch(resetCourseState());\n    navigate(\"/dashboard/my-courses\");\n  };\n  const handleCoursePublish = async () => {\n    // check if form has been updated or not\n    if ((course === null || course === void 0 ? void 0 : course.status) === COURSE_STATUS.PUBLISHED && getValues(\"public\") === true || (course === null || course === void 0 ? void 0 : course.status) === COURSE_STATUS.DRAFT && getValues(\"public\") === false) {\n      // form has not been updated\n      // no need to make api call\n      goToCourses();\n      return;\n    }\n    const formData = new FormData();\n    formData.append(\"courseId\", course._id);\n    const courseStatus = getValues(\"public\") ? COURSE_STATUS.PUBLISHED : COURSE_STATUS.DRAFT;\n    formData.append(\"status\", courseStatus);\n    setLoading(true);\n    const result = await editCourseDetails(formData, token);\n    if (result) {\n      goToCourses();\n    }\n    setLoading(false);\n  };\n  const onSubmit = data => {\n    // console.log(data)\n    handleCoursePublish();\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"rounded-md border-[1px] border-richblack-700 bg-richblack-800 p-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"text-2xl font-semibold text-richblack-5\",\n      children: \"Publish Settings\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit(onSubmit),\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"my-6 mb-8\",\n        children: /*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"public\",\n          className: \"inline-flex items-center text-lg\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            id: \"public\",\n            ...register(\"public\"),\n            className: \"border-gray-300 h-4 w-4 rounded bg-richblack-500 text-richblack-400 focus:ring-2 focus:ring-richblack-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"ml-2 text-richblack-400\",\n            children: \"Make this course as public\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"ml-auto flex max-w-max items-center gap-x-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          disabled: loading,\n          type: \"button\",\n          onClick: goBack,\n          className: \"flex cursor-pointer items-center gap-x-2 rounded-md bg-richblack-300 py-[8px] px-[20px] font-semibold text-richblack-900\",\n          children: \"Back\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconBtn, {\n          disabled: loading,\n          text: \"Save Changes\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 67,\n    columnNumber: 5\n  }, this);\n}\n_s(PublishCourse, \"EnWwoe0p7mKpzP2auZ62KaNo320=\", false, function () {\n  return [useForm, useDispatch, useNavigate, useSelector, useSelector];\n});\n_c = PublishCourse;\nvar _c;\n$RefreshReg$(_c, \"PublishCourse\");", "map": {"version": 3, "names": ["useEffect", "useState", "useForm", "useDispatch", "useSelector", "useNavigate", "editCourseDetails", "resetCourseState", "setStep", "COURSE_STATUS", "IconBtn", "jsxDEV", "_jsxDEV", "PublishCourse", "_s", "register", "handleSubmit", "setValue", "getV<PERSON>ues", "dispatch", "navigate", "token", "state", "auth", "course", "loading", "setLoading", "status", "PUBLISHED", "goBack", "goToCourses", "handleCoursePublish", "DRAFT", "formData", "FormData", "append", "_id", "courseStatus", "result", "onSubmit", "data", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "htmlFor", "type", "id", "disabled", "onClick", "text", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/src/components/core/Dashboard/AddCourse/PublishCourse/index.jsx"], "sourcesContent": ["import { useEffect, useState } from \"react\"\r\nimport { useForm } from \"react-hook-form\"\r\nimport { useDispatch, useSelector } from \"react-redux\"\r\nimport { useNavigate } from \"react-router-dom\"\r\n\r\nimport { editCourseDetails } from \"../../../../../services/operations/courseDetailsAPI\"\r\nimport { resetCourseState, setStep } from \"../../../../../slices/courseSlice\"\r\nimport { COURSE_STATUS } from \"../../../../../utils/constants\"\r\nimport IconBtn from \"../../../../common/IconBtn\"\r\n\r\nexport default function PublishCourse() {\r\n  const { register, handleSubmit, setValue, getValues } = useForm()\r\n\r\n  const dispatch = useDispatch()\r\n  const navigate = useNavigate()\r\n  const { token } = useSelector((state) => state.auth)\r\n  const { course } = useSelector((state) => state.course)\r\n  const [loading, setLoading] = useState(false)\r\n\r\n  useEffect(() => {\r\n    if (course?.status === COURSE_STATUS.PUBLISHED) {\r\n      setValue(\"public\", true)\r\n    }\r\n  }, [])\r\n\r\n  const goBack = () => {\r\n    dispatch(setStep(2))\r\n  }\r\n\r\n  const goToCourses = () => {\r\n    dispatch(resetCourseState())\r\n    navigate(\"/dashboard/my-courses\")\r\n  }\r\n\r\n  const handleCoursePublish = async () => {\r\n    // check if form has been updated or not\r\n    if (\r\n      (course?.status === COURSE_STATUS.PUBLISHED &&\r\n        getValues(\"public\") === true) ||\r\n      (course?.status === COURSE_STATUS.DRAFT && getValues(\"public\") === false)\r\n    ) {\r\n      // form has not been updated\r\n      // no need to make api call\r\n      goToCourses()\r\n      return\r\n    }\r\n    const formData = new FormData()\r\n    formData.append(\"courseId\", course._id)\r\n    const courseStatus = getValues(\"public\")\r\n      ? COURSE_STATUS.PUBLISHED\r\n      : COURSE_STATUS.DRAFT\r\n    formData.append(\"status\", courseStatus)\r\n    setLoading(true)\r\n    const result = await editCourseDetails(formData, token)\r\n    if (result) {\r\n      goToCourses()\r\n    }\r\n    setLoading(false)\r\n  }\r\n\r\n  const onSubmit = (data) => {\r\n    // console.log(data)\r\n    handleCoursePublish()\r\n  }\r\n\r\n  return (\r\n    <div className=\"rounded-md border-[1px] border-richblack-700 bg-richblack-800 p-6\">\r\n      <p className=\"text-2xl font-semibold text-richblack-5\">\r\n        Publish Settings\r\n      </p>\r\n      <form onSubmit={handleSubmit(onSubmit)}>\r\n        {/* Checkbox */}\r\n        <div className=\"my-6 mb-8\">\r\n          <label htmlFor=\"public\" className=\"inline-flex items-center text-lg\">\r\n            <input\r\n              type=\"checkbox\"\r\n              id=\"public\"\r\n              {...register(\"public\")}\r\n              className=\"border-gray-300 h-4 w-4 rounded bg-richblack-500 text-richblack-400 focus:ring-2 focus:ring-richblack-5\"\r\n            />\r\n            <span className=\"ml-2 text-richblack-400\">\r\n              Make this course as public\r\n            </span>\r\n          </label>\r\n        </div>\r\n\r\n        {/* Next Prev Button */}\r\n        <div className=\"ml-auto flex max-w-max items-center gap-x-4\">\r\n          <button\r\n            disabled={loading}\r\n            type=\"button\"\r\n            onClick={goBack}\r\n            className=\"flex cursor-pointer items-center gap-x-2 rounded-md bg-richblack-300 py-[8px] px-[20px] font-semibold text-richblack-900\"\r\n          >\r\n            Back\r\n          </button>\r\n          <IconBtn disabled={loading} text=\"Save Changes\" />\r\n        </div>\r\n      </form>\r\n    </div>\r\n  )\r\n}"], "mappings": ";;AAAA,SAASA,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,SAASC,OAAO,QAAQ,iBAAiB;AACzC,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,QAAQ,kBAAkB;AAE9C,SAASC,iBAAiB,QAAQ,qDAAqD;AACvF,SAASC,gBAAgB,EAAEC,OAAO,QAAQ,mCAAmC;AAC7E,SAASC,aAAa,QAAQ,gCAAgC;AAC9D,OAAOC,OAAO,MAAM,4BAA4B;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEhD,eAAe,SAASC,aAAaA,CAAA,EAAG;EAAAC,EAAA;EACtC,MAAM;IAAEC,QAAQ;IAAEC,YAAY;IAAEC,QAAQ;IAAEC;EAAU,CAAC,GAAGhB,OAAO,EAAE;EAEjE,MAAMiB,QAAQ,GAAGhB,WAAW,EAAE;EAC9B,MAAMiB,QAAQ,GAAGf,WAAW,EAAE;EAC9B,MAAM;IAAEgB;EAAM,CAAC,GAAGjB,WAAW,CAAEkB,KAAK,IAAKA,KAAK,CAACC,IAAI,CAAC;EACpD,MAAM;IAAEC;EAAO,CAAC,GAAGpB,WAAW,CAAEkB,KAAK,IAAKA,KAAK,CAACE,MAAM,CAAC;EACvD,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EAE7CD,SAAS,CAAC,MAAM;IACd,IAAI,CAAAwB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEG,MAAM,MAAKlB,aAAa,CAACmB,SAAS,EAAE;MAC9CX,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC;IAC1B;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMY,MAAM,GAAGA,CAAA,KAAM;IACnBV,QAAQ,CAACX,OAAO,CAAC,CAAC,CAAC,CAAC;EACtB,CAAC;EAED,MAAMsB,WAAW,GAAGA,CAAA,KAAM;IACxBX,QAAQ,CAACZ,gBAAgB,EAAE,CAAC;IAC5Ba,QAAQ,CAAC,uBAAuB,CAAC;EACnC,CAAC;EAED,MAAMW,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC;IACA,IACG,CAAAP,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEG,MAAM,MAAKlB,aAAa,CAACmB,SAAS,IACzCV,SAAS,CAAC,QAAQ,CAAC,KAAK,IAAI,IAC7B,CAAAM,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEG,MAAM,MAAKlB,aAAa,CAACuB,KAAK,IAAId,SAAS,CAAC,QAAQ,CAAC,KAAK,KAAM,EACzE;MACA;MACA;MACAY,WAAW,EAAE;MACb;IACF;IACA,MAAMG,QAAQ,GAAG,IAAIC,QAAQ,EAAE;IAC/BD,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEX,MAAM,CAACY,GAAG,CAAC;IACvC,MAAMC,YAAY,GAAGnB,SAAS,CAAC,QAAQ,CAAC,GACpCT,aAAa,CAACmB,SAAS,GACvBnB,aAAa,CAACuB,KAAK;IACvBC,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAEE,YAAY,CAAC;IACvCX,UAAU,CAAC,IAAI,CAAC;IAChB,MAAMY,MAAM,GAAG,MAAMhC,iBAAiB,CAAC2B,QAAQ,EAAEZ,KAAK,CAAC;IACvD,IAAIiB,MAAM,EAAE;MACVR,WAAW,EAAE;IACf;IACAJ,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,MAAMa,QAAQ,GAAIC,IAAI,IAAK;IACzB;IACAT,mBAAmB,EAAE;EACvB,CAAC;EAED,oBACEnB,OAAA;IAAK6B,SAAS,EAAC,mEAAmE;IAAAC,QAAA,gBAChF9B,OAAA;MAAG6B,SAAS,EAAC,yCAAyC;MAAAC,QAAA,EAAC;IAEvD;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAI,eACJlC,OAAA;MAAM2B,QAAQ,EAAEvB,YAAY,CAACuB,QAAQ,CAAE;MAAAG,QAAA,gBAErC9B,OAAA;QAAK6B,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxB9B,OAAA;UAAOmC,OAAO,EAAC,QAAQ;UAACN,SAAS,EAAC,kCAAkC;UAAAC,QAAA,gBAClE9B,OAAA;YACEoC,IAAI,EAAC,UAAU;YACfC,EAAE,EAAC,QAAQ;YAAA,GACPlC,QAAQ,CAAC,QAAQ,CAAC;YACtB0B,SAAS,EAAC;UAAyG;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACnH,eACFlC,OAAA;YAAM6B,SAAS,EAAC,yBAAyB;YAAAC,QAAA,EAAC;UAE1C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAAO;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MACD;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACJ,eAGNlC,OAAA;QAAK6B,SAAS,EAAC,6CAA6C;QAAAC,QAAA,gBAC1D9B,OAAA;UACEsC,QAAQ,EAAEzB,OAAQ;UAClBuB,IAAI,EAAC,QAAQ;UACbG,OAAO,EAAEtB,MAAO;UAChBY,SAAS,EAAC,0HAA0H;UAAAC,QAAA,EACrI;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAS,eACTlC,OAAA,CAACF,OAAO;UAACwC,QAAQ,EAAEzB,OAAQ;UAAC2B,IAAI,EAAC;QAAc;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAG;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAC9C;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACD;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACH;AAEV;AAAChC,EAAA,CA3FuBD,aAAa;EAAA,QACqBX,OAAO,EAE9CC,WAAW,EACXE,WAAW,EACVD,WAAW,EACVA,WAAW;AAAA;AAAAiD,EAAA,GANRxC,aAAa;AAAA,IAAAwC,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}