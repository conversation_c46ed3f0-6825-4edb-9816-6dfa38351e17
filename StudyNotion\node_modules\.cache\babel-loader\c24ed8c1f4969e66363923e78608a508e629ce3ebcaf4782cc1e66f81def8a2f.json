{"ast": null, "code": "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Text} Text\n * @typedef {import('mdast').Break} Break\n * @typedef {import('../state.js').State} State\n */\n\n/**\n * Turn an mdast `break` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Break} node\n *   mdast node.\n * @returns {Array<Element | Text>}\n *   hast element content.\n */\nexport function hardBreak(state, node) {\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'br',\n    properties: {},\n    children: []\n  };\n  state.patch(node, result);\n  return [state.applyData(node, result), {\n    type: 'text',\n    value: '\\n'\n  }];\n}", "map": {"version": 3, "names": ["hardBreak", "state", "node", "result", "type", "tagName", "properties", "children", "patch", "applyData", "value"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/mdast-util-to-hast/lib/handlers/break.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Text} Text\n * @typedef {import('mdast').Break} Break\n * @typedef {import('../state.js').State} State\n */\n\n/**\n * Turn an mdast `break` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Break} node\n *   mdast node.\n * @returns {Array<Element | Text>}\n *   hast element content.\n */\nexport function hardBreak(state, node) {\n  /** @type {Element} */\n  const result = {type: 'element', tagName: 'br', properties: {}, children: []}\n  state.patch(node, result)\n  return [state.applyData(node, result), {type: 'text', value: '\\n'}]\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,SAASA,CAACC,KAAK,EAAEC,IAAI,EAAE;EACrC;EACA,MAAMC,MAAM,GAAG;IAACC,IAAI,EAAE,SAAS;IAAEC,OAAO,EAAE,IAAI;IAAEC,UAAU,EAAE,CAAC,CAAC;IAAEC,QAAQ,EAAE;EAAE,CAAC;EAC7EN,KAAK,CAACO,KAAK,CAACN,IAAI,EAAEC,MAAM,CAAC;EACzB,OAAO,CAACF,KAAK,CAACQ,SAAS,CAACP,IAAI,EAAEC,MAAM,CAAC,EAAE;IAACC,IAAI,EAAE,MAAM;IAAEM,KAAK,EAAE;EAAI,CAAC,CAAC;AACrE"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}