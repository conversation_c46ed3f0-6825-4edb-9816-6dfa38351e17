{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\");\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nvar _classCallCheck2 = _interopRequireDefault(require(\"@babel/runtime/helpers/classCallCheck\"));\nvar _createClass2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createClass\"));\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"@babel/runtime/helpers/possibleConstructorReturn\"));\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"@babel/runtime/helpers/getPrototypeOf\"));\nvar _assertThisInitialized2 = _interopRequireDefault(require(\"@babel/runtime/helpers/assertThisInitialized\"));\nvar _inherits2 = _interopRequireDefault(require(\"@babel/runtime/helpers/inherits\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _react = _interopRequireWildcard(require(\"react\"));\nvar _classnames = _interopRequireDefault(require(\"classnames\"));\nvar _Slider = _interopRequireDefault(require(\"../Slider\"));\nvar _PlayProgressBar = _interopRequireDefault(require(\"./PlayProgressBar\"));\nvar _LoadProgressBar = _interopRequireDefault(require(\"./LoadProgressBar\"));\nvar _MouseTimeDisplay = _interopRequireDefault(require(\"./MouseTimeDisplay\"));\nvar _utils = require(\"../../utils\");\nvar propTypes = {\n  player: _propTypes[\"default\"].object,\n  mouseTime: _propTypes[\"default\"].object,\n  actions: _propTypes[\"default\"].object,\n  className: _propTypes[\"default\"].string\n};\nvar SeekBar = /*#__PURE__*/\nfunction (_Component) {\n  (0, _inherits2[\"default\"])(SeekBar, _Component);\n  function SeekBar(props, context) {\n    var _this;\n    (0, _classCallCheck2[\"default\"])(this, SeekBar);\n    _this = (0, _possibleConstructorReturn2[\"default\"])(this, (0, _getPrototypeOf2[\"default\"])(SeekBar).call(this, props, context));\n    _this.getPercent = _this.getPercent.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.getNewTime = _this.getNewTime.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.stepForward = _this.stepForward.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.stepBack = _this.stepBack.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleMouseDown = _this.handleMouseDown.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleMouseMove = _this.handleMouseMove.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleMouseUp = _this.handleMouseUp.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    return _this;\n  }\n  (0, _createClass2[\"default\"])(SeekBar, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {}\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate() {}\n    /**\n     * Get percentage of video played\n     *\n     * @return {Number} Percentage played\n     * @method getPercent\n     */\n  }, {\n    key: \"getPercent\",\n    value: function getPercent() {\n      var _this$props$player = this.props.player,\n        currentTime = _this$props$player.currentTime,\n        seekingTime = _this$props$player.seekingTime,\n        duration = _this$props$player.duration;\n      var time = seekingTime || currentTime;\n      var percent = time / duration;\n      return percent >= 1 ? 1 : percent;\n    }\n  }, {\n    key: \"getNewTime\",\n    value: function getNewTime(event) {\n      var duration = this.props.player.duration;\n      var distance = this.slider.calculateDistance(event);\n      var newTime = distance * duration; // Don't let video end while scrubbing.\n\n      return newTime === duration ? newTime - 0.1 : newTime;\n    }\n  }, {\n    key: \"handleMouseDown\",\n    value: function handleMouseDown() {}\n  }, {\n    key: \"handleMouseUp\",\n    value: function handleMouseUp(event) {\n      var actions = this.props.actions;\n      var newTime = this.getNewTime(event); // Set new time (tell video to seek to new time)\n\n      actions.seek(newTime);\n      actions.handleEndSeeking(newTime);\n    }\n  }, {\n    key: \"handleMouseMove\",\n    value: function handleMouseMove(event) {\n      var actions = this.props.actions;\n      var newTime = this.getNewTime(event);\n      actions.handleSeekingTime(newTime);\n    }\n  }, {\n    key: \"stepForward\",\n    value: function stepForward() {\n      var actions = this.props.actions;\n      actions.forward(5);\n    }\n  }, {\n    key: \"stepBack\",\n    value: function stepBack() {\n      var actions = this.props.actions;\n      actions.replay(5);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this2 = this;\n      var _this$props = this.props,\n        _this$props$player2 = _this$props.player,\n        currentTime = _this$props$player2.currentTime,\n        seekingTime = _this$props$player2.seekingTime,\n        duration = _this$props$player2.duration,\n        buffered = _this$props$player2.buffered,\n        mouseTime = _this$props.mouseTime;\n      var time = seekingTime || currentTime;\n      return _react[\"default\"].createElement(_Slider[\"default\"], {\n        ref: function ref(input) {\n          _this2.slider = input;\n        },\n        label: \"video progress bar\",\n        className: (0, _classnames[\"default\"])('video-react-progress-holder', this.props.className),\n        valuenow: (this.getPercent() * 100).toFixed(2),\n        valuetext: (0, _utils.formatTime)(time, duration),\n        onMouseDown: this.handleMouseDown,\n        onMouseMove: this.handleMouseMove,\n        onMouseUp: this.handleMouseUp,\n        getPercent: this.getPercent,\n        stepForward: this.stepForward,\n        stepBack: this.stepBack\n      }, _react[\"default\"].createElement(_LoadProgressBar[\"default\"], {\n        buffered: buffered,\n        currentTime: time,\n        duration: duration\n      }), _react[\"default\"].createElement(_MouseTimeDisplay[\"default\"], {\n        duration: duration,\n        mouseTime: mouseTime\n      }), _react[\"default\"].createElement(_PlayProgressBar[\"default\"], {\n        currentTime: time,\n        duration: duration\n      }));\n    }\n  }]);\n  return SeekBar;\n}(_react.Component);\nexports[\"default\"] = SeekBar;\nSeekBar.propTypes = propTypes;\nSeekBar.displayName = 'SeekBar';", "map": {"version": 3, "names": ["_interopRequireWildcard", "require", "_interopRequireDefault", "Object", "defineProperty", "exports", "value", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_assertThisInitialized2", "_inherits2", "_propTypes", "_react", "_classnames", "_<PERSON><PERSON><PERSON>", "_PlayProgressBar", "_LoadProgressBar", "_MouseTimeDisplay", "_utils", "propTypes", "player", "object", "mouseTime", "actions", "className", "string", "SeekBar", "_Component", "props", "context", "_this", "call", "getPercent", "bind", "getNewTime", "stepForward", "stepBack", "handleMouseDown", "handleMouseMove", "handleMouseUp", "key", "componentDidMount", "componentDidUpdate", "_this$props$player", "currentTime", "seekingTime", "duration", "time", "percent", "event", "distance", "slider", "calculateDistance", "newTime", "seek", "handleEndSeeking", "handleSeekingTime", "forward", "replay", "render", "_this2", "_this$props", "_this$props$player2", "buffered", "createElement", "ref", "input", "label", "valuenow", "toFixed", "valuetext", "formatTime", "onMouseDown", "onMouseMove", "onMouseUp", "Component", "displayName"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/video-react/lib/components/control-bar/SeekBar.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\");\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _classCallCheck2 = _interopRequireDefault(require(\"@babel/runtime/helpers/classCallCheck\"));\n\nvar _createClass2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createClass\"));\n\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"@babel/runtime/helpers/possibleConstructorReturn\"));\n\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"@babel/runtime/helpers/getPrototypeOf\"));\n\nvar _assertThisInitialized2 = _interopRequireDefault(require(\"@babel/runtime/helpers/assertThisInitialized\"));\n\nvar _inherits2 = _interopRequireDefault(require(\"@babel/runtime/helpers/inherits\"));\n\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\n\nvar _react = _interopRequireWildcard(require(\"react\"));\n\nvar _classnames = _interopRequireDefault(require(\"classnames\"));\n\nvar _Slider = _interopRequireDefault(require(\"../Slider\"));\n\nvar _PlayProgressBar = _interopRequireDefault(require(\"./PlayProgressBar\"));\n\nvar _LoadProgressBar = _interopRequireDefault(require(\"./LoadProgressBar\"));\n\nvar _MouseTimeDisplay = _interopRequireDefault(require(\"./MouseTimeDisplay\"));\n\nvar _utils = require(\"../../utils\");\n\nvar propTypes = {\n  player: _propTypes[\"default\"].object,\n  mouseTime: _propTypes[\"default\"].object,\n  actions: _propTypes[\"default\"].object,\n  className: _propTypes[\"default\"].string\n};\n\nvar SeekBar =\n/*#__PURE__*/\nfunction (_Component) {\n  (0, _inherits2[\"default\"])(SeekBar, _Component);\n\n  function SeekBar(props, context) {\n    var _this;\n\n    (0, _classCallCheck2[\"default\"])(this, SeekBar);\n    _this = (0, _possibleConstructorReturn2[\"default\"])(this, (0, _getPrototypeOf2[\"default\"])(SeekBar).call(this, props, context));\n    _this.getPercent = _this.getPercent.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.getNewTime = _this.getNewTime.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.stepForward = _this.stepForward.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.stepBack = _this.stepBack.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleMouseDown = _this.handleMouseDown.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleMouseMove = _this.handleMouseMove.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleMouseUp = _this.handleMouseUp.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    return _this;\n  }\n\n  (0, _createClass2[\"default\"])(SeekBar, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {}\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate() {}\n    /**\n     * Get percentage of video played\n     *\n     * @return {Number} Percentage played\n     * @method getPercent\n     */\n\n  }, {\n    key: \"getPercent\",\n    value: function getPercent() {\n      var _this$props$player = this.props.player,\n          currentTime = _this$props$player.currentTime,\n          seekingTime = _this$props$player.seekingTime,\n          duration = _this$props$player.duration;\n      var time = seekingTime || currentTime;\n      var percent = time / duration;\n      return percent >= 1 ? 1 : percent;\n    }\n  }, {\n    key: \"getNewTime\",\n    value: function getNewTime(event) {\n      var duration = this.props.player.duration;\n      var distance = this.slider.calculateDistance(event);\n      var newTime = distance * duration; // Don't let video end while scrubbing.\n\n      return newTime === duration ? newTime - 0.1 : newTime;\n    }\n  }, {\n    key: \"handleMouseDown\",\n    value: function handleMouseDown() {}\n  }, {\n    key: \"handleMouseUp\",\n    value: function handleMouseUp(event) {\n      var actions = this.props.actions;\n      var newTime = this.getNewTime(event); // Set new time (tell video to seek to new time)\n\n      actions.seek(newTime);\n      actions.handleEndSeeking(newTime);\n    }\n  }, {\n    key: \"handleMouseMove\",\n    value: function handleMouseMove(event) {\n      var actions = this.props.actions;\n      var newTime = this.getNewTime(event);\n      actions.handleSeekingTime(newTime);\n    }\n  }, {\n    key: \"stepForward\",\n    value: function stepForward() {\n      var actions = this.props.actions;\n      actions.forward(5);\n    }\n  }, {\n    key: \"stepBack\",\n    value: function stepBack() {\n      var actions = this.props.actions;\n      actions.replay(5);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this2 = this;\n\n      var _this$props = this.props,\n          _this$props$player2 = _this$props.player,\n          currentTime = _this$props$player2.currentTime,\n          seekingTime = _this$props$player2.seekingTime,\n          duration = _this$props$player2.duration,\n          buffered = _this$props$player2.buffered,\n          mouseTime = _this$props.mouseTime;\n      var time = seekingTime || currentTime;\n      return _react[\"default\"].createElement(_Slider[\"default\"], {\n        ref: function ref(input) {\n          _this2.slider = input;\n        },\n        label: \"video progress bar\",\n        className: (0, _classnames[\"default\"])('video-react-progress-holder', this.props.className),\n        valuenow: (this.getPercent() * 100).toFixed(2),\n        valuetext: (0, _utils.formatTime)(time, duration),\n        onMouseDown: this.handleMouseDown,\n        onMouseMove: this.handleMouseMove,\n        onMouseUp: this.handleMouseUp,\n        getPercent: this.getPercent,\n        stepForward: this.stepForward,\n        stepBack: this.stepBack\n      }, _react[\"default\"].createElement(_LoadProgressBar[\"default\"], {\n        buffered: buffered,\n        currentTime: time,\n        duration: duration\n      }), _react[\"default\"].createElement(_MouseTimeDisplay[\"default\"], {\n        duration: duration,\n        mouseTime: mouseTime\n      }), _react[\"default\"].createElement(_PlayProgressBar[\"default\"], {\n        currentTime: time,\n        duration: duration\n      }));\n    }\n  }]);\n  return SeekBar;\n}(_react.Component);\n\nexports[\"default\"] = SeekBar;\nSeekBar.propTypes = propTypes;\nSeekBar.displayName = 'SeekBar';"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,uBAAuB,GAAGC,OAAO,CAAC,+CAA+C,CAAC;AAEtF,IAAIC,sBAAsB,GAAGD,OAAO,CAAC,8CAA8C,CAAC;AAEpFE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;AAE3B,IAAIE,gBAAgB,GAAGL,sBAAsB,CAACD,OAAO,CAAC,uCAAuC,CAAC,CAAC;AAE/F,IAAIO,aAAa,GAAGN,sBAAsB,CAACD,OAAO,CAAC,oCAAoC,CAAC,CAAC;AAEzF,IAAIQ,2BAA2B,GAAGP,sBAAsB,CAACD,OAAO,CAAC,kDAAkD,CAAC,CAAC;AAErH,IAAIS,gBAAgB,GAAGR,sBAAsB,CAACD,OAAO,CAAC,uCAAuC,CAAC,CAAC;AAE/F,IAAIU,uBAAuB,GAAGT,sBAAsB,CAACD,OAAO,CAAC,8CAA8C,CAAC,CAAC;AAE7G,IAAIW,UAAU,GAAGV,sBAAsB,CAACD,OAAO,CAAC,iCAAiC,CAAC,CAAC;AAEnF,IAAIY,UAAU,GAAGX,sBAAsB,CAACD,OAAO,CAAC,YAAY,CAAC,CAAC;AAE9D,IAAIa,MAAM,GAAGd,uBAAuB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AAEtD,IAAIc,WAAW,GAAGb,sBAAsB,CAACD,OAAO,CAAC,YAAY,CAAC,CAAC;AAE/D,IAAIe,OAAO,GAAGd,sBAAsB,CAACD,OAAO,CAAC,WAAW,CAAC,CAAC;AAE1D,IAAIgB,gBAAgB,GAAGf,sBAAsB,CAACD,OAAO,CAAC,mBAAmB,CAAC,CAAC;AAE3E,IAAIiB,gBAAgB,GAAGhB,sBAAsB,CAACD,OAAO,CAAC,mBAAmB,CAAC,CAAC;AAE3E,IAAIkB,iBAAiB,GAAGjB,sBAAsB,CAACD,OAAO,CAAC,oBAAoB,CAAC,CAAC;AAE7E,IAAImB,MAAM,GAAGnB,OAAO,CAAC,aAAa,CAAC;AAEnC,IAAIoB,SAAS,GAAG;EACdC,MAAM,EAAET,UAAU,CAAC,SAAS,CAAC,CAACU,MAAM;EACpCC,SAAS,EAAEX,UAAU,CAAC,SAAS,CAAC,CAACU,MAAM;EACvCE,OAAO,EAAEZ,UAAU,CAAC,SAAS,CAAC,CAACU,MAAM;EACrCG,SAAS,EAAEb,UAAU,CAAC,SAAS,CAAC,CAACc;AACnC,CAAC;AAED,IAAIC,OAAO,GACX;AACA,UAAUC,UAAU,EAAE;EACpB,CAAC,CAAC,EAAEjB,UAAU,CAAC,SAAS,CAAC,EAAEgB,OAAO,EAAEC,UAAU,CAAC;EAE/C,SAASD,OAAOA,CAACE,KAAK,EAAEC,OAAO,EAAE;IAC/B,IAAIC,KAAK;IAET,CAAC,CAAC,EAAEzB,gBAAgB,CAAC,SAAS,CAAC,EAAE,IAAI,EAAEqB,OAAO,CAAC;IAC/CI,KAAK,GAAG,CAAC,CAAC,EAAEvB,2BAA2B,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAEC,gBAAgB,CAAC,SAAS,CAAC,EAAEkB,OAAO,CAAC,CAACK,IAAI,CAAC,IAAI,EAAEH,KAAK,EAAEC,OAAO,CAAC,CAAC;IAC/HC,KAAK,CAACE,UAAU,GAAGF,KAAK,CAACE,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAExB,uBAAuB,CAAC,SAAS,CAAC,EAAEqB,KAAK,CAAC,CAAC;IACxFA,KAAK,CAACI,UAAU,GAAGJ,KAAK,CAACI,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAExB,uBAAuB,CAAC,SAAS,CAAC,EAAEqB,KAAK,CAAC,CAAC;IACxFA,KAAK,CAACK,WAAW,GAAGL,KAAK,CAACK,WAAW,CAACF,IAAI,CAAC,CAAC,CAAC,EAAExB,uBAAuB,CAAC,SAAS,CAAC,EAAEqB,KAAK,CAAC,CAAC;IAC1FA,KAAK,CAACM,QAAQ,GAAGN,KAAK,CAACM,QAAQ,CAACH,IAAI,CAAC,CAAC,CAAC,EAAExB,uBAAuB,CAAC,SAAS,CAAC,EAAEqB,KAAK,CAAC,CAAC;IACpFA,KAAK,CAACO,eAAe,GAAGP,KAAK,CAACO,eAAe,CAACJ,IAAI,CAAC,CAAC,CAAC,EAAExB,uBAAuB,CAAC,SAAS,CAAC,EAAEqB,KAAK,CAAC,CAAC;IAClGA,KAAK,CAACQ,eAAe,GAAGR,KAAK,CAACQ,eAAe,CAACL,IAAI,CAAC,CAAC,CAAC,EAAExB,uBAAuB,CAAC,SAAS,CAAC,EAAEqB,KAAK,CAAC,CAAC;IAClGA,KAAK,CAACS,aAAa,GAAGT,KAAK,CAACS,aAAa,CAACN,IAAI,CAAC,CAAC,CAAC,EAAExB,uBAAuB,CAAC,SAAS,CAAC,EAAEqB,KAAK,CAAC,CAAC;IAC9F,OAAOA,KAAK;EACd;EAEA,CAAC,CAAC,EAAExB,aAAa,CAAC,SAAS,CAAC,EAAEoB,OAAO,EAAE,CAAC;IACtCc,GAAG,EAAE,mBAAmB;IACxBpC,KAAK,EAAE,SAASqC,iBAAiBA,CAAA,EAAG,CAAC;EACvC,CAAC,EAAE;IACDD,GAAG,EAAE,oBAAoB;IACzBpC,KAAK,EAAE,SAASsC,kBAAkBA,CAAA,EAAG,CAAC;IACtC;AACJ;AACA;AACA;AACA;AACA;EAEE,CAAC,EAAE;IACDF,GAAG,EAAE,YAAY;IACjBpC,KAAK,EAAE,SAAS4B,UAAUA,CAAA,EAAG;MAC3B,IAAIW,kBAAkB,GAAG,IAAI,CAACf,KAAK,CAACR,MAAM;QACtCwB,WAAW,GAAGD,kBAAkB,CAACC,WAAW;QAC5CC,WAAW,GAAGF,kBAAkB,CAACE,WAAW;QAC5CC,QAAQ,GAAGH,kBAAkB,CAACG,QAAQ;MAC1C,IAAIC,IAAI,GAAGF,WAAW,IAAID,WAAW;MACrC,IAAII,OAAO,GAAGD,IAAI,GAAGD,QAAQ;MAC7B,OAAOE,OAAO,IAAI,CAAC,GAAG,CAAC,GAAGA,OAAO;IACnC;EACF,CAAC,EAAE;IACDR,GAAG,EAAE,YAAY;IACjBpC,KAAK,EAAE,SAAS8B,UAAUA,CAACe,KAAK,EAAE;MAChC,IAAIH,QAAQ,GAAG,IAAI,CAAClB,KAAK,CAACR,MAAM,CAAC0B,QAAQ;MACzC,IAAII,QAAQ,GAAG,IAAI,CAACC,MAAM,CAACC,iBAAiB,CAACH,KAAK,CAAC;MACnD,IAAII,OAAO,GAAGH,QAAQ,GAAGJ,QAAQ,CAAC,CAAC;;MAEnC,OAAOO,OAAO,KAAKP,QAAQ,GAAGO,OAAO,GAAG,GAAG,GAAGA,OAAO;IACvD;EACF,CAAC,EAAE;IACDb,GAAG,EAAE,iBAAiB;IACtBpC,KAAK,EAAE,SAASiC,eAAeA,CAAA,EAAG,CAAC;EACrC,CAAC,EAAE;IACDG,GAAG,EAAE,eAAe;IACpBpC,KAAK,EAAE,SAASmC,aAAaA,CAACU,KAAK,EAAE;MACnC,IAAI1B,OAAO,GAAG,IAAI,CAACK,KAAK,CAACL,OAAO;MAChC,IAAI8B,OAAO,GAAG,IAAI,CAACnB,UAAU,CAACe,KAAK,CAAC,CAAC,CAAC;;MAEtC1B,OAAO,CAAC+B,IAAI,CAACD,OAAO,CAAC;MACrB9B,OAAO,CAACgC,gBAAgB,CAACF,OAAO,CAAC;IACnC;EACF,CAAC,EAAE;IACDb,GAAG,EAAE,iBAAiB;IACtBpC,KAAK,EAAE,SAASkC,eAAeA,CAACW,KAAK,EAAE;MACrC,IAAI1B,OAAO,GAAG,IAAI,CAACK,KAAK,CAACL,OAAO;MAChC,IAAI8B,OAAO,GAAG,IAAI,CAACnB,UAAU,CAACe,KAAK,CAAC;MACpC1B,OAAO,CAACiC,iBAAiB,CAACH,OAAO,CAAC;IACpC;EACF,CAAC,EAAE;IACDb,GAAG,EAAE,aAAa;IAClBpC,KAAK,EAAE,SAAS+B,WAAWA,CAAA,EAAG;MAC5B,IAAIZ,OAAO,GAAG,IAAI,CAACK,KAAK,CAACL,OAAO;MAChCA,OAAO,CAACkC,OAAO,CAAC,CAAC,CAAC;IACpB;EACF,CAAC,EAAE;IACDjB,GAAG,EAAE,UAAU;IACfpC,KAAK,EAAE,SAASgC,QAAQA,CAAA,EAAG;MACzB,IAAIb,OAAO,GAAG,IAAI,CAACK,KAAK,CAACL,OAAO;MAChCA,OAAO,CAACmC,MAAM,CAAC,CAAC,CAAC;IACnB;EACF,CAAC,EAAE;IACDlB,GAAG,EAAE,QAAQ;IACbpC,KAAK,EAAE,SAASuD,MAAMA,CAAA,EAAG;MACvB,IAAIC,MAAM,GAAG,IAAI;MAEjB,IAAIC,WAAW,GAAG,IAAI,CAACjC,KAAK;QACxBkC,mBAAmB,GAAGD,WAAW,CAACzC,MAAM;QACxCwB,WAAW,GAAGkB,mBAAmB,CAAClB,WAAW;QAC7CC,WAAW,GAAGiB,mBAAmB,CAACjB,WAAW;QAC7CC,QAAQ,GAAGgB,mBAAmB,CAAChB,QAAQ;QACvCiB,QAAQ,GAAGD,mBAAmB,CAACC,QAAQ;QACvCzC,SAAS,GAAGuC,WAAW,CAACvC,SAAS;MACrC,IAAIyB,IAAI,GAAGF,WAAW,IAAID,WAAW;MACrC,OAAOhC,MAAM,CAAC,SAAS,CAAC,CAACoD,aAAa,CAAClD,OAAO,CAAC,SAAS,CAAC,EAAE;QACzDmD,GAAG,EAAE,SAASA,GAAGA,CAACC,KAAK,EAAE;UACvBN,MAAM,CAACT,MAAM,GAAGe,KAAK;QACvB,CAAC;QACDC,KAAK,EAAE,oBAAoB;QAC3B3C,SAAS,EAAE,CAAC,CAAC,EAAEX,WAAW,CAAC,SAAS,CAAC,EAAE,6BAA6B,EAAE,IAAI,CAACe,KAAK,CAACJ,SAAS,CAAC;QAC3F4C,QAAQ,EAAE,CAAC,IAAI,CAACpC,UAAU,EAAE,GAAG,GAAG,EAAEqC,OAAO,CAAC,CAAC,CAAC;QAC9CC,SAAS,EAAE,CAAC,CAAC,EAAEpD,MAAM,CAACqD,UAAU,EAAExB,IAAI,EAAED,QAAQ,CAAC;QACjD0B,WAAW,EAAE,IAAI,CAACnC,eAAe;QACjCoC,WAAW,EAAE,IAAI,CAACnC,eAAe;QACjCoC,SAAS,EAAE,IAAI,CAACnC,aAAa;QAC7BP,UAAU,EAAE,IAAI,CAACA,UAAU;QAC3BG,WAAW,EAAE,IAAI,CAACA,WAAW;QAC7BC,QAAQ,EAAE,IAAI,CAACA;MACjB,CAAC,EAAExB,MAAM,CAAC,SAAS,CAAC,CAACoD,aAAa,CAAChD,gBAAgB,CAAC,SAAS,CAAC,EAAE;QAC9D+C,QAAQ,EAAEA,QAAQ;QAClBnB,WAAW,EAAEG,IAAI;QACjBD,QAAQ,EAAEA;MACZ,CAAC,CAAC,EAAElC,MAAM,CAAC,SAAS,CAAC,CAACoD,aAAa,CAAC/C,iBAAiB,CAAC,SAAS,CAAC,EAAE;QAChE6B,QAAQ,EAAEA,QAAQ;QAClBxB,SAAS,EAAEA;MACb,CAAC,CAAC,EAAEV,MAAM,CAAC,SAAS,CAAC,CAACoD,aAAa,CAACjD,gBAAgB,CAAC,SAAS,CAAC,EAAE;QAC/D6B,WAAW,EAAEG,IAAI;QACjBD,QAAQ,EAAEA;MACZ,CAAC,CAAC,CAAC;IACL;EACF,CAAC,CAAC,CAAC;EACH,OAAOpB,OAAO;AAChB,CAAC,CAACd,MAAM,CAAC+D,SAAS,CAAC;AAEnBxE,OAAO,CAAC,SAAS,CAAC,GAAGuB,OAAO;AAC5BA,OAAO,CAACP,SAAS,GAAGA,SAAS;AAC7BO,OAAO,CAACkD,WAAW,GAAG,SAAS"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}