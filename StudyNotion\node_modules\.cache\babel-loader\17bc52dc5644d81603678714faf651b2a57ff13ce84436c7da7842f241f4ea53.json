{"ast": null, "code": "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Heading} Heading\n * @typedef {import('../state.js').State} State\n */\n\n/**\n * Turn an mdast `heading` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Heading} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function heading(state, node) {\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'h' + node.depth,\n    properties: {},\n    children: state.all(node)\n  };\n  state.patch(node, result);\n  return state.applyData(node, result);\n}", "map": {"version": 3, "names": ["heading", "state", "node", "result", "type", "tagName", "depth", "properties", "children", "all", "patch", "applyData"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/mdast-util-to-hast/lib/handlers/heading.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Heading} Heading\n * @typedef {import('../state.js').State} State\n */\n\n/**\n * Turn an mdast `heading` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Heading} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function heading(state, node) {\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'h' + node.depth,\n    properties: {},\n    children: state.all(node)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,OAAOA,CAACC,KAAK,EAAEC,IAAI,EAAE;EACnC;EACA,MAAMC,MAAM,GAAG;IACbC,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,GAAG,GAAGH,IAAI,CAACI,KAAK;IACzBC,UAAU,EAAE,CAAC,CAAC;IACdC,QAAQ,EAAEP,KAAK,CAACQ,GAAG,CAACP,IAAI;EAC1B,CAAC;EACDD,KAAK,CAACS,KAAK,CAACR,IAAI,EAAEC,MAAM,CAAC;EACzB,OAAOF,KAAK,CAACU,SAAS,CAACT,IAAI,EAAEC,MAAM,CAAC;AACtC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}