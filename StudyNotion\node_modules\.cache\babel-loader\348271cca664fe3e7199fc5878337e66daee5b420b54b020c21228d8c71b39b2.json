{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\");\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar _classCallCheck2 = _interopRequireDefault(require(\"@babel/runtime/helpers/classCallCheck\"));\nvar _createClass2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createClass\"));\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"@babel/runtime/helpers/possibleConstructorReturn\"));\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"@babel/runtime/helpers/getPrototypeOf\"));\nvar _assertThisInitialized2 = _interopRequireDefault(require(\"@babel/runtime/helpers/assertThisInitialized\"));\nvar _inherits2 = _interopRequireDefault(require(\"@babel/runtime/helpers/inherits\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _react = _interopRequireWildcard(require(\"react\"));\nvar _classnames = _interopRequireDefault(require(\"classnames\"));\nvar _PopupButton = _interopRequireDefault(require(\"../popup/PopupButton\"));\nvar _VolumeBar = _interopRequireDefault(require(\"../volume-control/VolumeBar\"));\nvar propTypes = {\n  player: _propTypes[\"default\"].object,\n  actions: _propTypes[\"default\"].object,\n  vertical: _propTypes[\"default\"].bool,\n  className: _propTypes[\"default\"].string,\n  alwaysShowVolume: _propTypes[\"default\"].bool\n};\nvar defaultProps = {\n  vertical: false\n};\nvar VolumeMenuButton = /*#__PURE__*/\nfunction (_Component) {\n  (0, _inherits2[\"default\"])(VolumeMenuButton, _Component);\n  function VolumeMenuButton(props, context) {\n    var _this;\n    (0, _classCallCheck2[\"default\"])(this, VolumeMenuButton);\n    _this = (0, _possibleConstructorReturn2[\"default\"])(this, (0, _getPrototypeOf2[\"default\"])(VolumeMenuButton).call(this, props, context));\n    _this.state = {\n      active: false\n    };\n    _this.handleClick = _this.handleClick.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleFocus = _this.handleFocus.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleBlur = _this.handleBlur.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    return _this;\n  }\n  (0, _createClass2[\"default\"])(VolumeMenuButton, [{\n    key: \"handleClick\",\n    value: function handleClick() {\n      var _this$props = this.props,\n        player = _this$props.player,\n        actions = _this$props.actions;\n      actions.mute(!player.muted);\n    }\n  }, {\n    key: \"handleFocus\",\n    value: function handleFocus() {\n      this.setState({\n        active: true\n      });\n    }\n  }, {\n    key: \"handleBlur\",\n    value: function handleBlur() {\n      this.setState({\n        active: false\n      });\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props2 = this.props,\n        vertical = _this$props2.vertical,\n        player = _this$props2.player,\n        className = _this$props2.className;\n      var inline = !vertical;\n      var level = this.volumeLevel;\n      return _react[\"default\"].createElement(_PopupButton[\"default\"], {\n        className: (0, _classnames[\"default\"])(className, {\n          'video-react-volume-menu-button-vertical': vertical,\n          'video-react-volume-menu-button-horizontal': !vertical,\n          'video-react-vol-muted': player.muted,\n          'video-react-vol-0': level === 0 && !player.muted,\n          'video-react-vol-1': level === 1,\n          'video-react-vol-2': level === 2,\n          'video-react-vol-3': level === 3,\n          'video-react-slider-active': this.props.alwaysShowVolume || this.state.active,\n          'video-react-lock-showing': this.props.alwaysShowVolume || this.state.active\n        }, 'video-react-volume-menu-button'),\n        onClick: this.handleClick,\n        inline: inline\n      }, _react[\"default\"].createElement(_VolumeBar[\"default\"], (0, _extends2[\"default\"])({\n        onFocus: this.handleFocus,\n        onBlur: this.handleBlur\n      }, this.props)));\n    }\n  }, {\n    key: \"volumeLevel\",\n    get: function get() {\n      var _this$props$player = this.props.player,\n        volume = _this$props$player.volume,\n        muted = _this$props$player.muted;\n      var level = 3;\n      if (volume === 0 || muted) {\n        level = 0;\n      } else if (volume < 0.33) {\n        level = 1;\n      } else if (volume < 0.67) {\n        level = 2;\n      }\n      return level;\n    }\n  }]);\n  return VolumeMenuButton;\n}(_react.Component);\nVolumeMenuButton.propTypes = propTypes;\nVolumeMenuButton.defaultProps = defaultProps;\nVolumeMenuButton.displayName = 'VolumeMenuButton';\nvar _default = VolumeMenuButton;\nexports[\"default\"] = _default;", "map": {"version": 3, "names": ["_interopRequireWildcard", "require", "_interopRequireDefault", "Object", "defineProperty", "exports", "value", "_extends2", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_assertThisInitialized2", "_inherits2", "_propTypes", "_react", "_classnames", "_PopupButton", "_VolumeBar", "propTypes", "player", "object", "actions", "vertical", "bool", "className", "string", "alwaysShowVolume", "defaultProps", "VolumeMenuButton", "_Component", "props", "context", "_this", "call", "state", "active", "handleClick", "bind", "handleFocus", "handleBlur", "key", "_this$props", "mute", "muted", "setState", "render", "_this$props2", "inline", "level", "volumeLevel", "createElement", "onClick", "onFocus", "onBlur", "get", "_this$props$player", "volume", "Component", "displayName", "_default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/video-react/lib/components/control-bar/VolumeMenuButton.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\");\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\n\nvar _classCallCheck2 = _interopRequireDefault(require(\"@babel/runtime/helpers/classCallCheck\"));\n\nvar _createClass2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createClass\"));\n\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"@babel/runtime/helpers/possibleConstructorReturn\"));\n\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"@babel/runtime/helpers/getPrototypeOf\"));\n\nvar _assertThisInitialized2 = _interopRequireDefault(require(\"@babel/runtime/helpers/assertThisInitialized\"));\n\nvar _inherits2 = _interopRequireDefault(require(\"@babel/runtime/helpers/inherits\"));\n\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\n\nvar _react = _interopRequireWildcard(require(\"react\"));\n\nvar _classnames = _interopRequireDefault(require(\"classnames\"));\n\nvar _PopupButton = _interopRequireDefault(require(\"../popup/PopupButton\"));\n\nvar _VolumeBar = _interopRequireDefault(require(\"../volume-control/VolumeBar\"));\n\nvar propTypes = {\n  player: _propTypes[\"default\"].object,\n  actions: _propTypes[\"default\"].object,\n  vertical: _propTypes[\"default\"].bool,\n  className: _propTypes[\"default\"].string,\n  alwaysShowVolume: _propTypes[\"default\"].bool\n};\nvar defaultProps = {\n  vertical: false\n};\n\nvar VolumeMenuButton =\n/*#__PURE__*/\nfunction (_Component) {\n  (0, _inherits2[\"default\"])(VolumeMenuButton, _Component);\n\n  function VolumeMenuButton(props, context) {\n    var _this;\n\n    (0, _classCallCheck2[\"default\"])(this, VolumeMenuButton);\n    _this = (0, _possibleConstructorReturn2[\"default\"])(this, (0, _getPrototypeOf2[\"default\"])(VolumeMenuButton).call(this, props, context));\n    _this.state = {\n      active: false\n    };\n    _this.handleClick = _this.handleClick.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleFocus = _this.handleFocus.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleBlur = _this.handleBlur.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    return _this;\n  }\n\n  (0, _createClass2[\"default\"])(VolumeMenuButton, [{\n    key: \"handleClick\",\n    value: function handleClick() {\n      var _this$props = this.props,\n          player = _this$props.player,\n          actions = _this$props.actions;\n      actions.mute(!player.muted);\n    }\n  }, {\n    key: \"handleFocus\",\n    value: function handleFocus() {\n      this.setState({\n        active: true\n      });\n    }\n  }, {\n    key: \"handleBlur\",\n    value: function handleBlur() {\n      this.setState({\n        active: false\n      });\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props2 = this.props,\n          vertical = _this$props2.vertical,\n          player = _this$props2.player,\n          className = _this$props2.className;\n      var inline = !vertical;\n      var level = this.volumeLevel;\n      return _react[\"default\"].createElement(_PopupButton[\"default\"], {\n        className: (0, _classnames[\"default\"])(className, {\n          'video-react-volume-menu-button-vertical': vertical,\n          'video-react-volume-menu-button-horizontal': !vertical,\n          'video-react-vol-muted': player.muted,\n          'video-react-vol-0': level === 0 && !player.muted,\n          'video-react-vol-1': level === 1,\n          'video-react-vol-2': level === 2,\n          'video-react-vol-3': level === 3,\n          'video-react-slider-active': this.props.alwaysShowVolume || this.state.active,\n          'video-react-lock-showing': this.props.alwaysShowVolume || this.state.active\n        }, 'video-react-volume-menu-button'),\n        onClick: this.handleClick,\n        inline: inline\n      }, _react[\"default\"].createElement(_VolumeBar[\"default\"], (0, _extends2[\"default\"])({\n        onFocus: this.handleFocus,\n        onBlur: this.handleBlur\n      }, this.props)));\n    }\n  }, {\n    key: \"volumeLevel\",\n    get: function get() {\n      var _this$props$player = this.props.player,\n          volume = _this$props$player.volume,\n          muted = _this$props$player.muted;\n      var level = 3;\n\n      if (volume === 0 || muted) {\n        level = 0;\n      } else if (volume < 0.33) {\n        level = 1;\n      } else if (volume < 0.67) {\n        level = 2;\n      }\n\n      return level;\n    }\n  }]);\n  return VolumeMenuButton;\n}(_react.Component);\n\nVolumeMenuButton.propTypes = propTypes;\nVolumeMenuButton.defaultProps = defaultProps;\nVolumeMenuButton.displayName = 'VolumeMenuButton';\nvar _default = VolumeMenuButton;\nexports[\"default\"] = _default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,uBAAuB,GAAGC,OAAO,CAAC,+CAA+C,CAAC;AAEtF,IAAIC,sBAAsB,GAAGD,OAAO,CAAC,8CAA8C,CAAC;AAEpFE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;AAE3B,IAAIE,SAAS,GAAGL,sBAAsB,CAACD,OAAO,CAAC,gCAAgC,CAAC,CAAC;AAEjF,IAAIO,gBAAgB,GAAGN,sBAAsB,CAACD,OAAO,CAAC,uCAAuC,CAAC,CAAC;AAE/F,IAAIQ,aAAa,GAAGP,sBAAsB,CAACD,OAAO,CAAC,oCAAoC,CAAC,CAAC;AAEzF,IAAIS,2BAA2B,GAAGR,sBAAsB,CAACD,OAAO,CAAC,kDAAkD,CAAC,CAAC;AAErH,IAAIU,gBAAgB,GAAGT,sBAAsB,CAACD,OAAO,CAAC,uCAAuC,CAAC,CAAC;AAE/F,IAAIW,uBAAuB,GAAGV,sBAAsB,CAACD,OAAO,CAAC,8CAA8C,CAAC,CAAC;AAE7G,IAAIY,UAAU,GAAGX,sBAAsB,CAACD,OAAO,CAAC,iCAAiC,CAAC,CAAC;AAEnF,IAAIa,UAAU,GAAGZ,sBAAsB,CAACD,OAAO,CAAC,YAAY,CAAC,CAAC;AAE9D,IAAIc,MAAM,GAAGf,uBAAuB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AAEtD,IAAIe,WAAW,GAAGd,sBAAsB,CAACD,OAAO,CAAC,YAAY,CAAC,CAAC;AAE/D,IAAIgB,YAAY,GAAGf,sBAAsB,CAACD,OAAO,CAAC,sBAAsB,CAAC,CAAC;AAE1E,IAAIiB,UAAU,GAAGhB,sBAAsB,CAACD,OAAO,CAAC,6BAA6B,CAAC,CAAC;AAE/E,IAAIkB,SAAS,GAAG;EACdC,MAAM,EAAEN,UAAU,CAAC,SAAS,CAAC,CAACO,MAAM;EACpCC,OAAO,EAAER,UAAU,CAAC,SAAS,CAAC,CAACO,MAAM;EACrCE,QAAQ,EAAET,UAAU,CAAC,SAAS,CAAC,CAACU,IAAI;EACpCC,SAAS,EAAEX,UAAU,CAAC,SAAS,CAAC,CAACY,MAAM;EACvCC,gBAAgB,EAAEb,UAAU,CAAC,SAAS,CAAC,CAACU;AAC1C,CAAC;AACD,IAAII,YAAY,GAAG;EACjBL,QAAQ,EAAE;AACZ,CAAC;AAED,IAAIM,gBAAgB,GACpB;AACA,UAAUC,UAAU,EAAE;EACpB,CAAC,CAAC,EAAEjB,UAAU,CAAC,SAAS,CAAC,EAAEgB,gBAAgB,EAAEC,UAAU,CAAC;EAExD,SAASD,gBAAgBA,CAACE,KAAK,EAAEC,OAAO,EAAE;IACxC,IAAIC,KAAK;IAET,CAAC,CAAC,EAAEzB,gBAAgB,CAAC,SAAS,CAAC,EAAE,IAAI,EAAEqB,gBAAgB,CAAC;IACxDI,KAAK,GAAG,CAAC,CAAC,EAAEvB,2BAA2B,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAEC,gBAAgB,CAAC,SAAS,CAAC,EAAEkB,gBAAgB,CAAC,CAACK,IAAI,CAAC,IAAI,EAAEH,KAAK,EAAEC,OAAO,CAAC,CAAC;IACxIC,KAAK,CAACE,KAAK,GAAG;MACZC,MAAM,EAAE;IACV,CAAC;IACDH,KAAK,CAACI,WAAW,GAAGJ,KAAK,CAACI,WAAW,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE1B,uBAAuB,CAAC,SAAS,CAAC,EAAEqB,KAAK,CAAC,CAAC;IAC1FA,KAAK,CAACM,WAAW,GAAGN,KAAK,CAACM,WAAW,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE1B,uBAAuB,CAAC,SAAS,CAAC,EAAEqB,KAAK,CAAC,CAAC;IAC1FA,KAAK,CAACO,UAAU,GAAGP,KAAK,CAACO,UAAU,CAACF,IAAI,CAAC,CAAC,CAAC,EAAE1B,uBAAuB,CAAC,SAAS,CAAC,EAAEqB,KAAK,CAAC,CAAC;IACxF,OAAOA,KAAK;EACd;EAEA,CAAC,CAAC,EAAExB,aAAa,CAAC,SAAS,CAAC,EAAEoB,gBAAgB,EAAE,CAAC;IAC/CY,GAAG,EAAE,aAAa;IAClBnC,KAAK,EAAE,SAAS+B,WAAWA,CAAA,EAAG;MAC5B,IAAIK,WAAW,GAAG,IAAI,CAACX,KAAK;QACxBX,MAAM,GAAGsB,WAAW,CAACtB,MAAM;QAC3BE,OAAO,GAAGoB,WAAW,CAACpB,OAAO;MACjCA,OAAO,CAACqB,IAAI,CAAC,CAACvB,MAAM,CAACwB,KAAK,CAAC;IAC7B;EACF,CAAC,EAAE;IACDH,GAAG,EAAE,aAAa;IAClBnC,KAAK,EAAE,SAASiC,WAAWA,CAAA,EAAG;MAC5B,IAAI,CAACM,QAAQ,CAAC;QACZT,MAAM,EAAE;MACV,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDK,GAAG,EAAE,YAAY;IACjBnC,KAAK,EAAE,SAASkC,UAAUA,CAAA,EAAG;MAC3B,IAAI,CAACK,QAAQ,CAAC;QACZT,MAAM,EAAE;MACV,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDK,GAAG,EAAE,QAAQ;IACbnC,KAAK,EAAE,SAASwC,MAAMA,CAAA,EAAG;MACvB,IAAIC,YAAY,GAAG,IAAI,CAAChB,KAAK;QACzBR,QAAQ,GAAGwB,YAAY,CAACxB,QAAQ;QAChCH,MAAM,GAAG2B,YAAY,CAAC3B,MAAM;QAC5BK,SAAS,GAAGsB,YAAY,CAACtB,SAAS;MACtC,IAAIuB,MAAM,GAAG,CAACzB,QAAQ;MACtB,IAAI0B,KAAK,GAAG,IAAI,CAACC,WAAW;MAC5B,OAAOnC,MAAM,CAAC,SAAS,CAAC,CAACoC,aAAa,CAAClC,YAAY,CAAC,SAAS,CAAC,EAAE;QAC9DQ,SAAS,EAAE,CAAC,CAAC,EAAET,WAAW,CAAC,SAAS,CAAC,EAAES,SAAS,EAAE;UAChD,yCAAyC,EAAEF,QAAQ;UACnD,2CAA2C,EAAE,CAACA,QAAQ;UACtD,uBAAuB,EAAEH,MAAM,CAACwB,KAAK;UACrC,mBAAmB,EAAEK,KAAK,KAAK,CAAC,IAAI,CAAC7B,MAAM,CAACwB,KAAK;UACjD,mBAAmB,EAAEK,KAAK,KAAK,CAAC;UAChC,mBAAmB,EAAEA,KAAK,KAAK,CAAC;UAChC,mBAAmB,EAAEA,KAAK,KAAK,CAAC;UAChC,2BAA2B,EAAE,IAAI,CAAClB,KAAK,CAACJ,gBAAgB,IAAI,IAAI,CAACQ,KAAK,CAACC,MAAM;UAC7E,0BAA0B,EAAE,IAAI,CAACL,KAAK,CAACJ,gBAAgB,IAAI,IAAI,CAACQ,KAAK,CAACC;QACxE,CAAC,EAAE,gCAAgC,CAAC;QACpCgB,OAAO,EAAE,IAAI,CAACf,WAAW;QACzBW,MAAM,EAAEA;MACV,CAAC,EAAEjC,MAAM,CAAC,SAAS,CAAC,CAACoC,aAAa,CAACjC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAEX,SAAS,CAAC,SAAS,CAAC,EAAE;QAClF8C,OAAO,EAAE,IAAI,CAACd,WAAW;QACzBe,MAAM,EAAE,IAAI,CAACd;MACf,CAAC,EAAE,IAAI,CAACT,KAAK,CAAC,CAAC,CAAC;IAClB;EACF,CAAC,EAAE;IACDU,GAAG,EAAE,aAAa;IAClBc,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;MAClB,IAAIC,kBAAkB,GAAG,IAAI,CAACzB,KAAK,CAACX,MAAM;QACtCqC,MAAM,GAAGD,kBAAkB,CAACC,MAAM;QAClCb,KAAK,GAAGY,kBAAkB,CAACZ,KAAK;MACpC,IAAIK,KAAK,GAAG,CAAC;MAEb,IAAIQ,MAAM,KAAK,CAAC,IAAIb,KAAK,EAAE;QACzBK,KAAK,GAAG,CAAC;MACX,CAAC,MAAM,IAAIQ,MAAM,GAAG,IAAI,EAAE;QACxBR,KAAK,GAAG,CAAC;MACX,CAAC,MAAM,IAAIQ,MAAM,GAAG,IAAI,EAAE;QACxBR,KAAK,GAAG,CAAC;MACX;MAEA,OAAOA,KAAK;IACd;EACF,CAAC,CAAC,CAAC;EACH,OAAOpB,gBAAgB;AACzB,CAAC,CAACd,MAAM,CAAC2C,SAAS,CAAC;AAEnB7B,gBAAgB,CAACV,SAAS,GAAGA,SAAS;AACtCU,gBAAgB,CAACD,YAAY,GAAGA,YAAY;AAC5CC,gBAAgB,CAAC8B,WAAW,GAAG,kBAAkB;AACjD,IAAIC,QAAQ,GAAG/B,gBAAgB;AAC/BxB,OAAO,CAAC,SAAS,CAAC,GAAGuD,QAAQ"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}