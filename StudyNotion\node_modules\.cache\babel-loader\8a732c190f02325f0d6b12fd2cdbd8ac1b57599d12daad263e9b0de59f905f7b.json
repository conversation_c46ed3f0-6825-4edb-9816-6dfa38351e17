{"ast": null, "code": "export const updateOnVirtualData = swiper => {\n  if (!swiper || swiper.destroyed || !swiper.params.virtual || swiper.params.virtual && !swiper.params.virtual.enabled) return;\n  swiper.updateSlides();\n  swiper.updateProgress();\n  swiper.updateSlidesClasses();\n  if (swiper.parallax && swiper.params.parallax && swiper.params.parallax.enabled) {\n    swiper.parallax.setTranslate();\n  }\n};", "map": {"version": 3, "names": ["updateOnVirtualData", "swiper", "destroyed", "params", "virtual", "enabled", "updateSlides", "updateProgress", "updateSlidesClasses", "parallax", "setTranslate"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/components-shared/update-on-virtual-data.js"], "sourcesContent": ["export const updateOnVirtualData = swiper => {\n  if (!swiper || swiper.destroyed || !swiper.params.virtual || swiper.params.virtual && !swiper.params.virtual.enabled) return;\n  swiper.updateSlides();\n  swiper.updateProgress();\n  swiper.updateSlidesClasses();\n  if (swiper.parallax && swiper.params.parallax && swiper.params.parallax.enabled) {\n    swiper.parallax.setTranslate();\n  }\n};"], "mappings": "AAAA,OAAO,MAAMA,mBAAmB,GAAGC,MAAM,IAAI;EAC3C,IAAI,CAACA,MAAM,IAAIA,MAAM,CAACC,SAAS,IAAI,CAACD,MAAM,CAACE,MAAM,CAACC,OAAO,IAAIH,MAAM,CAACE,MAAM,CAACC,OAAO,IAAI,CAACH,MAAM,CAACE,MAAM,CAACC,OAAO,CAACC,OAAO,EAAE;EACtHJ,MAAM,CAACK,YAAY,EAAE;EACrBL,MAAM,CAACM,cAAc,EAAE;EACvBN,MAAM,CAACO,mBAAmB,EAAE;EAC5B,IAAIP,MAAM,CAACQ,QAAQ,IAAIR,MAAM,CAACE,MAAM,CAACM,QAAQ,IAAIR,MAAM,CAACE,MAAM,CAACM,QAAQ,CAACJ,OAAO,EAAE;IAC/EJ,MAAM,CAACQ,QAAQ,CAACC,YAAY,EAAE;EAChC;AACF,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}