{"ast": null, "code": "/**\n * @typedef {import('micromark-util-types').Construct} Construct\n * @typedef {import('micromark-util-types').Exiter} Exiter\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Tokenizer} Tokenizer\n */\n\nimport { factorySpace } from 'micromark-factory-space';\nimport { markdownSpace } from 'micromark-util-character';\nimport { codes } from 'micromark-util-symbol/codes.js';\nimport { constants } from 'micromark-util-symbol/constants.js';\nimport { types } from 'micromark-util-symbol/types.js';\nimport { ok as assert } from 'uvu/assert';\n\n/** @type {Construct} */\nexport const blockQuote = {\n  name: 'blockQuote',\n  tokenize: tokenizeBlockQuoteStart,\n  continuation: {\n    tokenize: tokenizeBlockQuoteContinuation\n  },\n  exit\n};\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeBlockQuoteStart(effects, ok, nok) {\n  const self = this;\n  return start;\n\n  /**\n   * Start of block quote.\n   *\n   * ```markdown\n   * > | > a\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    if (code === codes.greaterThan) {\n      const state = self.containerState;\n      assert(state, 'expected `containerState` to be defined in container');\n      if (!state.open) {\n        effects.enter(types.blockQuote, {\n          _container: true\n        });\n        state.open = true;\n      }\n      effects.enter(types.blockQuotePrefix);\n      effects.enter(types.blockQuoteMarker);\n      effects.consume(code);\n      effects.exit(types.blockQuoteMarker);\n      return after;\n    }\n    return nok(code);\n  }\n\n  /**\n   * After `>`, before optional whitespace.\n   *\n   * ```markdown\n   * > | > a\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    if (markdownSpace(code)) {\n      effects.enter(types.blockQuotePrefixWhitespace);\n      effects.consume(code);\n      effects.exit(types.blockQuotePrefixWhitespace);\n      effects.exit(types.blockQuotePrefix);\n      return ok;\n    }\n    effects.exit(types.blockQuotePrefix);\n    return ok(code);\n  }\n}\n\n/**\n * Start of block quote continuation.\n *\n * ```markdown\n *   | > a\n * > | > b\n *     ^\n * ```\n *\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeBlockQuoteContinuation(effects, ok, nok) {\n  const self = this;\n  return contStart;\n\n  /**\n   * Start of block quote continuation.\n   *\n   * Also used to parse the first block quote opening.\n   *\n   * ```markdown\n   *   | > a\n   * > | > b\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function contStart(code) {\n    if (markdownSpace(code)) {\n      // Always populated by defaults.\n      assert(self.parser.constructs.disable.null, 'expected `disable.null` to be populated');\n      return factorySpace(effects, contBefore, types.linePrefix, self.parser.constructs.disable.null.includes('codeIndented') ? undefined : constants.tabSize)(code);\n    }\n    return contBefore(code);\n  }\n\n  /**\n   * At `>`, after optional whitespace.\n   *\n   * Also used to parse the first block quote opening.\n   *\n   * ```markdown\n   *   | > a\n   * > | > b\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function contBefore(code) {\n    return effects.attempt(blockQuote, ok, nok)(code);\n  }\n}\n\n/** @type {Exiter} */\nfunction exit(effects) {\n  effects.exit(types.blockQuote);\n}", "map": {"version": 3, "names": ["factorySpace", "markdownSpace", "codes", "constants", "types", "ok", "assert", "blockQuote", "name", "tokenize", "tokenizeBlockQuoteStart", "continuation", "tokenizeBlockQuoteContinuation", "exit", "effects", "nok", "self", "start", "code", "greaterThan", "state", "containerState", "open", "enter", "_container", "blockQuotePrefix", "blockQuoteMarker", "consume", "after", "blockQuotePrefixWhitespace", "contStart", "parser", "constructs", "disable", "null", "contBefore", "linePrefix", "includes", "undefined", "tabSize", "attempt"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/micromark-core-commonmark/dev/lib/block-quote.js"], "sourcesContent": ["/**\n * @typedef {import('micromark-util-types').Construct} Construct\n * @typedef {import('micromark-util-types').Exiter} Exiter\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Tokenizer} Tokenizer\n */\n\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownSpace} from 'micromark-util-character'\nimport {codes} from 'micromark-util-symbol/codes.js'\nimport {constants} from 'micromark-util-symbol/constants.js'\nimport {types} from 'micromark-util-symbol/types.js'\nimport {ok as assert} from 'uvu/assert'\n\n/** @type {Construct} */\nexport const blockQuote = {\n  name: 'blockQuote',\n  tokenize: tokenizeBlockQuoteStart,\n  continuation: {tokenize: tokenizeBlockQuoteContinuation},\n  exit\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeBlockQuoteStart(effects, ok, nok) {\n  const self = this\n\n  return start\n\n  /**\n   * Start of block quote.\n   *\n   * ```markdown\n   * > | > a\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    if (code === codes.greaterThan) {\n      const state = self.containerState\n\n      assert(state, 'expected `containerState` to be defined in container')\n\n      if (!state.open) {\n        effects.enter(types.blockQuote, {_container: true})\n        state.open = true\n      }\n\n      effects.enter(types.blockQuotePrefix)\n      effects.enter(types.blockQuoteMarker)\n      effects.consume(code)\n      effects.exit(types.blockQuoteMarker)\n      return after\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After `>`, before optional whitespace.\n   *\n   * ```markdown\n   * > | > a\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    if (markdownSpace(code)) {\n      effects.enter(types.blockQuotePrefixWhitespace)\n      effects.consume(code)\n      effects.exit(types.blockQuotePrefixWhitespace)\n      effects.exit(types.blockQuotePrefix)\n      return ok\n    }\n\n    effects.exit(types.blockQuotePrefix)\n    return ok(code)\n  }\n}\n\n/**\n * Start of block quote continuation.\n *\n * ```markdown\n *   | > a\n * > | > b\n *     ^\n * ```\n *\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeBlockQuoteContinuation(effects, ok, nok) {\n  const self = this\n\n  return contStart\n\n  /**\n   * Start of block quote continuation.\n   *\n   * Also used to parse the first block quote opening.\n   *\n   * ```markdown\n   *   | > a\n   * > | > b\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function contStart(code) {\n    if (markdownSpace(code)) {\n      // Always populated by defaults.\n      assert(\n        self.parser.constructs.disable.null,\n        'expected `disable.null` to be populated'\n      )\n\n      return factorySpace(\n        effects,\n        contBefore,\n        types.linePrefix,\n        self.parser.constructs.disable.null.includes('codeIndented')\n          ? undefined\n          : constants.tabSize\n      )(code)\n    }\n\n    return contBefore(code)\n  }\n\n  /**\n   * At `>`, after optional whitespace.\n   *\n   * Also used to parse the first block quote opening.\n   *\n   * ```markdown\n   *   | > a\n   * > | > b\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function contBefore(code) {\n    return effects.attempt(blockQuote, ok, nok)(code)\n  }\n}\n\n/** @type {Exiter} */\nfunction exit(effects) {\n  effects.exit(types.blockQuote)\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,YAAY,QAAO,yBAAyB;AACpD,SAAQC,aAAa,QAAO,0BAA0B;AACtD,SAAQC,KAAK,QAAO,gCAAgC;AACpD,SAAQC,SAAS,QAAO,oCAAoC;AAC5D,SAAQC,KAAK,QAAO,gCAAgC;AACpD,SAAQC,EAAE,IAAIC,MAAM,QAAO,YAAY;;AAEvC;AACA,OAAO,MAAMC,UAAU,GAAG;EACxBC,IAAI,EAAE,YAAY;EAClBC,QAAQ,EAAEC,uBAAuB;EACjCC,YAAY,EAAE;IAACF,QAAQ,EAAEG;EAA8B,CAAC;EACxDC;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA,SAASH,uBAAuBA,CAACI,OAAO,EAAET,EAAE,EAAEU,GAAG,EAAE;EACjD,MAAMC,IAAI,GAAG,IAAI;EAEjB,OAAOC,KAAK;;EAEZ;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,KAAKA,CAACC,IAAI,EAAE;IACnB,IAAIA,IAAI,KAAKhB,KAAK,CAACiB,WAAW,EAAE;MAC9B,MAAMC,KAAK,GAAGJ,IAAI,CAACK,cAAc;MAEjCf,MAAM,CAACc,KAAK,EAAE,sDAAsD,CAAC;MAErE,IAAI,CAACA,KAAK,CAACE,IAAI,EAAE;QACfR,OAAO,CAACS,KAAK,CAACnB,KAAK,CAACG,UAAU,EAAE;UAACiB,UAAU,EAAE;QAAI,CAAC,CAAC;QACnDJ,KAAK,CAACE,IAAI,GAAG,IAAI;MACnB;MAEAR,OAAO,CAACS,KAAK,CAACnB,KAAK,CAACqB,gBAAgB,CAAC;MACrCX,OAAO,CAACS,KAAK,CAACnB,KAAK,CAACsB,gBAAgB,CAAC;MACrCZ,OAAO,CAACa,OAAO,CAACT,IAAI,CAAC;MACrBJ,OAAO,CAACD,IAAI,CAACT,KAAK,CAACsB,gBAAgB,CAAC;MACpC,OAAOE,KAAK;IACd;IAEA,OAAOb,GAAG,CAACG,IAAI,CAAC;EAClB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASU,KAAKA,CAACV,IAAI,EAAE;IACnB,IAAIjB,aAAa,CAACiB,IAAI,CAAC,EAAE;MACvBJ,OAAO,CAACS,KAAK,CAACnB,KAAK,CAACyB,0BAA0B,CAAC;MAC/Cf,OAAO,CAACa,OAAO,CAACT,IAAI,CAAC;MACrBJ,OAAO,CAACD,IAAI,CAACT,KAAK,CAACyB,0BAA0B,CAAC;MAC9Cf,OAAO,CAACD,IAAI,CAACT,KAAK,CAACqB,gBAAgB,CAAC;MACpC,OAAOpB,EAAE;IACX;IAEAS,OAAO,CAACD,IAAI,CAACT,KAAK,CAACqB,gBAAgB,CAAC;IACpC,OAAOpB,EAAE,CAACa,IAAI,CAAC;EACjB;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASN,8BAA8BA,CAACE,OAAO,EAAET,EAAE,EAAEU,GAAG,EAAE;EACxD,MAAMC,IAAI,GAAG,IAAI;EAEjB,OAAOc,SAAS;;EAEhB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,SAASA,CAACZ,IAAI,EAAE;IACvB,IAAIjB,aAAa,CAACiB,IAAI,CAAC,EAAE;MACvB;MACAZ,MAAM,CACJU,IAAI,CAACe,MAAM,CAACC,UAAU,CAACC,OAAO,CAACC,IAAI,EACnC,yCAAyC,CAC1C;MAED,OAAOlC,YAAY,CACjBc,OAAO,EACPqB,UAAU,EACV/B,KAAK,CAACgC,UAAU,EAChBpB,IAAI,CAACe,MAAM,CAACC,UAAU,CAACC,OAAO,CAACC,IAAI,CAACG,QAAQ,CAAC,cAAc,CAAC,GACxDC,SAAS,GACTnC,SAAS,CAACoC,OAAO,CACtB,CAACrB,IAAI,CAAC;IACT;IAEA,OAAOiB,UAAU,CAACjB,IAAI,CAAC;EACzB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASiB,UAAUA,CAACjB,IAAI,EAAE;IACxB,OAAOJ,OAAO,CAAC0B,OAAO,CAACjC,UAAU,EAAEF,EAAE,EAAEU,GAAG,CAAC,CAACG,IAAI,CAAC;EACnD;AACF;;AAEA;AACA,SAASL,IAAIA,CAACC,OAAO,EAAE;EACrBA,OAAO,CAACD,IAAI,CAACT,KAAK,CAACG,UAAU,CAAC;AAChC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}