{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = operation;\nvar _objectSpread2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectSpread\"));\nvar _player = require(\"../actions/player\");\nvar initialState = {\n  count: 0,\n  operation: {\n    action: '',\n    source: ''\n  }\n};\nfunction operation() {\n  var state = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : initialState;\n  var action = arguments.length > 1 ? arguments[1] : undefined;\n  switch (action.type) {\n    case _player.OPERATE:\n      return (0, _objectSpread2[\"default\"])({}, state, {\n        count: state.count + 1,\n        operation: (0, _objectSpread2[\"default\"])({}, state.operation, action.operation)\n      });\n    default:\n      return state;\n  }\n}", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "operation", "_objectSpread2", "_player", "initialState", "count", "action", "source", "state", "arguments", "length", "undefined", "type", "OPERATE"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/video-react/lib/reducers/operation.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = operation;\n\nvar _objectSpread2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectSpread\"));\n\nvar _player = require(\"../actions/player\");\n\nvar initialState = {\n  count: 0,\n  operation: {\n    action: '',\n    source: ''\n  }\n};\n\nfunction operation() {\n  var state = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : initialState;\n  var action = arguments.length > 1 ? arguments[1] : undefined;\n\n  switch (action.type) {\n    case _player.OPERATE:\n      return (0, _objectSpread2[\"default\"])({}, state, {\n        count: state.count + 1,\n        operation: (0, _objectSpread2[\"default\"])({}, state.operation, action.operation)\n      });\n\n    default:\n      return state;\n  }\n}"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC;AAEpFC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAAC,SAAS,CAAC,GAAGE,SAAS;AAE9B,IAAIC,cAAc,GAAGP,sBAAsB,CAACC,OAAO,CAAC,qCAAqC,CAAC,CAAC;AAE3F,IAAIO,OAAO,GAAGP,OAAO,CAAC,mBAAmB,CAAC;AAE1C,IAAIQ,YAAY,GAAG;EACjBC,KAAK,EAAE,CAAC;EACRJ,SAAS,EAAE;IACTK,MAAM,EAAE,EAAE;IACVC,MAAM,EAAE;EACV;AACF,CAAC;AAED,SAASN,SAASA,CAAA,EAAG;EACnB,IAAIO,KAAK,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAGL,YAAY;EAC5F,IAAIE,MAAM,GAAGG,SAAS,CAACC,MAAM,GAAG,CAAC,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAGE,SAAS;EAE5D,QAAQL,MAAM,CAACM,IAAI;IACjB,KAAKT,OAAO,CAACU,OAAO;MAClB,OAAO,CAAC,CAAC,EAAEX,cAAc,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAEM,KAAK,EAAE;QAC/CH,KAAK,EAAEG,KAAK,CAACH,KAAK,GAAG,CAAC;QACtBJ,SAAS,EAAE,CAAC,CAAC,EAAEC,cAAc,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAEM,KAAK,CAACP,SAAS,EAAEK,MAAM,CAACL,SAAS;MACjF,CAAC,CAAC;IAEJ;MACE,OAAOO,KAAK;EAAC;AAEnB"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}