{"ast": null, "code": "import transitionEmit from './transitionEmit.js';\nexport default function transitionEnd() {\n  let runCallbacks = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;\n  let direction = arguments.length > 1 ? arguments[1] : undefined;\n  const swiper = this;\n  const {\n    params\n  } = swiper;\n  swiper.animating = false;\n  if (params.cssMode) return;\n  swiper.setTransition(0);\n  transitionEmit({\n    swiper,\n    runCallbacks,\n    direction,\n    step: 'End'\n  });\n}", "map": {"version": 3, "names": ["transitionEmit", "transitionEnd", "runCallbacks", "arguments", "length", "undefined", "direction", "swiper", "params", "animating", "cssMode", "setTransition", "step"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/core/transition/transitionEnd.js"], "sourcesContent": ["import transitionEmit from './transitionEmit.js';\nexport default function transitionEnd(runCallbacks = true, direction) {\n  const swiper = this;\n  const {\n    params\n  } = swiper;\n  swiper.animating = false;\n  if (params.cssMode) return;\n  swiper.setTransition(0);\n  transitionEmit({\n    swiper,\n    runCallbacks,\n    direction,\n    step: 'End'\n  });\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,qBAAqB;AAChD,eAAe,SAASC,aAAaA,CAAA,EAAiC;EAAA,IAAhCC,YAAY,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;EAAA,IAAEG,SAAS,GAAAH,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAClE,MAAME,MAAM,GAAG,IAAI;EACnB,MAAM;IACJC;EACF,CAAC,GAAGD,MAAM;EACVA,MAAM,CAACE,SAAS,GAAG,KAAK;EACxB,IAAID,MAAM,CAACE,OAAO,EAAE;EACpBH,MAAM,CAACI,aAAa,CAAC,CAAC,CAAC;EACvBX,cAAc,CAAC;IACbO,MAAM;IACNL,YAAY;IACZI,SAAS;IACTM,IAAI,EAAE;EACR,CAAC,CAAC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}