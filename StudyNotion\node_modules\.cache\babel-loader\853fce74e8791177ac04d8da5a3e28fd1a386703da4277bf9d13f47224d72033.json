{"ast": null, "code": "import { elementChildren, elementOuterSize, elementStyle, setCSSProperty } from '../../shared/utils.js';\nexport default function updateSlides() {\n  const swiper = this;\n  function getDirectionLabel(property) {\n    if (swiper.isHorizontal()) {\n      return property;\n    }\n    // prettier-ignore\n    return {\n      'width': 'height',\n      'margin-top': 'margin-left',\n      'margin-bottom ': 'margin-right',\n      'margin-left': 'margin-top',\n      'margin-right': 'margin-bottom',\n      'padding-left': 'padding-top',\n      'padding-right': 'padding-bottom',\n      'marginRight': 'marginBottom'\n    }[property];\n  }\n  function getDirectionPropertyValue(node, label) {\n    return parseFloat(node.getPropertyValue(getDirectionLabel(label)) || 0);\n  }\n  const params = swiper.params;\n  const {\n    wrapperEl,\n    slidesEl,\n    size: swiperSize,\n    rtlTranslate: rtl,\n    wrongRTL\n  } = swiper;\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  const previousSlidesLength = isVirtual ? swiper.virtual.slides.length : swiper.slides.length;\n  const slides = elementChildren(slidesEl, `.${swiper.params.slideClass}, swiper-slide`);\n  const slidesLength = isVirtual ? swiper.virtual.slides.length : slides.length;\n  let snapGrid = [];\n  const slidesGrid = [];\n  const slidesSizesGrid = [];\n  let offsetBefore = params.slidesOffsetBefore;\n  if (typeof offsetBefore === 'function') {\n    offsetBefore = params.slidesOffsetBefore.call(swiper);\n  }\n  let offsetAfter = params.slidesOffsetAfter;\n  if (typeof offsetAfter === 'function') {\n    offsetAfter = params.slidesOffsetAfter.call(swiper);\n  }\n  const previousSnapGridLength = swiper.snapGrid.length;\n  const previousSlidesGridLength = swiper.slidesGrid.length;\n  let spaceBetween = params.spaceBetween;\n  let slidePosition = -offsetBefore;\n  let prevSlideSize = 0;\n  let index = 0;\n  if (typeof swiperSize === 'undefined') {\n    return;\n  }\n  if (typeof spaceBetween === 'string' && spaceBetween.indexOf('%') >= 0) {\n    spaceBetween = parseFloat(spaceBetween.replace('%', '')) / 100 * swiperSize;\n  } else if (typeof spaceBetween === 'string') {\n    spaceBetween = parseFloat(spaceBetween);\n  }\n  swiper.virtualSize = -spaceBetween;\n\n  // reset margins\n  slides.forEach(slideEl => {\n    if (rtl) {\n      slideEl.style.marginLeft = '';\n    } else {\n      slideEl.style.marginRight = '';\n    }\n    slideEl.style.marginBottom = '';\n    slideEl.style.marginTop = '';\n  });\n\n  // reset cssMode offsets\n  if (params.centeredSlides && params.cssMode) {\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-before', '');\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-after', '');\n  }\n  const gridEnabled = params.grid && params.grid.rows > 1 && swiper.grid;\n  if (gridEnabled) {\n    swiper.grid.initSlides(slidesLength);\n  }\n\n  // Calc slides\n  let slideSize;\n  const shouldResetSlideSize = params.slidesPerView === 'auto' && params.breakpoints && Object.keys(params.breakpoints).filter(key => {\n    return typeof params.breakpoints[key].slidesPerView !== 'undefined';\n  }).length > 0;\n  for (let i = 0; i < slidesLength; i += 1) {\n    slideSize = 0;\n    let slide;\n    if (slides[i]) slide = slides[i];\n    if (gridEnabled) {\n      swiper.grid.updateSlide(i, slide, slidesLength, getDirectionLabel);\n    }\n    if (slides[i] && elementStyle(slide, 'display') === 'none') continue; // eslint-disable-line\n\n    if (params.slidesPerView === 'auto') {\n      if (shouldResetSlideSize) {\n        slides[i].style[getDirectionLabel('width')] = ``;\n      }\n      const slideStyles = getComputedStyle(slide);\n      const currentTransform = slide.style.transform;\n      const currentWebKitTransform = slide.style.webkitTransform;\n      if (currentTransform) {\n        slide.style.transform = 'none';\n      }\n      if (currentWebKitTransform) {\n        slide.style.webkitTransform = 'none';\n      }\n      if (params.roundLengths) {\n        slideSize = swiper.isHorizontal() ? elementOuterSize(slide, 'width', true) : elementOuterSize(slide, 'height', true);\n      } else {\n        // eslint-disable-next-line\n        const width = getDirectionPropertyValue(slideStyles, 'width');\n        const paddingLeft = getDirectionPropertyValue(slideStyles, 'padding-left');\n        const paddingRight = getDirectionPropertyValue(slideStyles, 'padding-right');\n        const marginLeft = getDirectionPropertyValue(slideStyles, 'margin-left');\n        const marginRight = getDirectionPropertyValue(slideStyles, 'margin-right');\n        const boxSizing = slideStyles.getPropertyValue('box-sizing');\n        if (boxSizing && boxSizing === 'border-box') {\n          slideSize = width + marginLeft + marginRight;\n        } else {\n          const {\n            clientWidth,\n            offsetWidth\n          } = slide;\n          slideSize = width + paddingLeft + paddingRight + marginLeft + marginRight + (offsetWidth - clientWidth);\n        }\n      }\n      if (currentTransform) {\n        slide.style.transform = currentTransform;\n      }\n      if (currentWebKitTransform) {\n        slide.style.webkitTransform = currentWebKitTransform;\n      }\n      if (params.roundLengths) slideSize = Math.floor(slideSize);\n    } else {\n      slideSize = (swiperSize - (params.slidesPerView - 1) * spaceBetween) / params.slidesPerView;\n      if (params.roundLengths) slideSize = Math.floor(slideSize);\n      if (slides[i]) {\n        slides[i].style[getDirectionLabel('width')] = `${slideSize}px`;\n      }\n    }\n    if (slides[i]) {\n      slides[i].swiperSlideSize = slideSize;\n    }\n    slidesSizesGrid.push(slideSize);\n    if (params.centeredSlides) {\n      slidePosition = slidePosition + slideSize / 2 + prevSlideSize / 2 + spaceBetween;\n      if (prevSlideSize === 0 && i !== 0) slidePosition = slidePosition - swiperSize / 2 - spaceBetween;\n      if (i === 0) slidePosition = slidePosition - swiperSize / 2 - spaceBetween;\n      if (Math.abs(slidePosition) < 1 / 1000) slidePosition = 0;\n      if (params.roundLengths) slidePosition = Math.floor(slidePosition);\n      if (index % params.slidesPerGroup === 0) snapGrid.push(slidePosition);\n      slidesGrid.push(slidePosition);\n    } else {\n      if (params.roundLengths) slidePosition = Math.floor(slidePosition);\n      if ((index - Math.min(swiper.params.slidesPerGroupSkip, index)) % swiper.params.slidesPerGroup === 0) snapGrid.push(slidePosition);\n      slidesGrid.push(slidePosition);\n      slidePosition = slidePosition + slideSize + spaceBetween;\n    }\n    swiper.virtualSize += slideSize + spaceBetween;\n    prevSlideSize = slideSize;\n    index += 1;\n  }\n  swiper.virtualSize = Math.max(swiper.virtualSize, swiperSize) + offsetAfter;\n  if (rtl && wrongRTL && (params.effect === 'slide' || params.effect === 'coverflow')) {\n    wrapperEl.style.width = `${swiper.virtualSize + spaceBetween}px`;\n  }\n  if (params.setWrapperSize) {\n    wrapperEl.style[getDirectionLabel('width')] = `${swiper.virtualSize + spaceBetween}px`;\n  }\n  if (gridEnabled) {\n    swiper.grid.updateWrapperSize(slideSize, snapGrid, getDirectionLabel);\n  }\n\n  // Remove last grid elements depending on width\n  if (!params.centeredSlides) {\n    const newSlidesGrid = [];\n    for (let i = 0; i < snapGrid.length; i += 1) {\n      let slidesGridItem = snapGrid[i];\n      if (params.roundLengths) slidesGridItem = Math.floor(slidesGridItem);\n      if (snapGrid[i] <= swiper.virtualSize - swiperSize) {\n        newSlidesGrid.push(slidesGridItem);\n      }\n    }\n    snapGrid = newSlidesGrid;\n    if (Math.floor(swiper.virtualSize - swiperSize) - Math.floor(snapGrid[snapGrid.length - 1]) > 1) {\n      snapGrid.push(swiper.virtualSize - swiperSize);\n    }\n  }\n  if (isVirtual && params.loop) {\n    const size = slidesSizesGrid[0] + spaceBetween;\n    if (params.slidesPerGroup > 1) {\n      const groups = Math.ceil((swiper.virtual.slidesBefore + swiper.virtual.slidesAfter) / params.slidesPerGroup);\n      const groupSize = size * params.slidesPerGroup;\n      for (let i = 0; i < groups; i += 1) {\n        snapGrid.push(snapGrid[snapGrid.length - 1] + groupSize);\n      }\n    }\n    for (let i = 0; i < swiper.virtual.slidesBefore + swiper.virtual.slidesAfter; i += 1) {\n      if (params.slidesPerGroup === 1) {\n        snapGrid.push(snapGrid[snapGrid.length - 1] + size);\n      }\n      slidesGrid.push(slidesGrid[slidesGrid.length - 1] + size);\n      swiper.virtualSize += size;\n    }\n  }\n  if (snapGrid.length === 0) snapGrid = [0];\n  if (spaceBetween !== 0) {\n    const key = swiper.isHorizontal() && rtl ? 'marginLeft' : getDirectionLabel('marginRight');\n    slides.filter((_, slideIndex) => {\n      if (!params.cssMode || params.loop) return true;\n      if (slideIndex === slides.length - 1) {\n        return false;\n      }\n      return true;\n    }).forEach(slideEl => {\n      slideEl.style[key] = `${spaceBetween}px`;\n    });\n  }\n  if (params.centeredSlides && params.centeredSlidesBounds) {\n    let allSlidesSize = 0;\n    slidesSizesGrid.forEach(slideSizeValue => {\n      allSlidesSize += slideSizeValue + (spaceBetween || 0);\n    });\n    allSlidesSize -= spaceBetween;\n    const maxSnap = allSlidesSize - swiperSize;\n    snapGrid = snapGrid.map(snap => {\n      if (snap <= 0) return -offsetBefore;\n      if (snap > maxSnap) return maxSnap + offsetAfter;\n      return snap;\n    });\n  }\n  if (params.centerInsufficientSlides) {\n    let allSlidesSize = 0;\n    slidesSizesGrid.forEach(slideSizeValue => {\n      allSlidesSize += slideSizeValue + (spaceBetween || 0);\n    });\n    allSlidesSize -= spaceBetween;\n    if (allSlidesSize < swiperSize) {\n      const allSlidesOffset = (swiperSize - allSlidesSize) / 2;\n      snapGrid.forEach((snap, snapIndex) => {\n        snapGrid[snapIndex] = snap - allSlidesOffset;\n      });\n      slidesGrid.forEach((snap, snapIndex) => {\n        slidesGrid[snapIndex] = snap + allSlidesOffset;\n      });\n    }\n  }\n  Object.assign(swiper, {\n    slides,\n    snapGrid,\n    slidesGrid,\n    slidesSizesGrid\n  });\n  if (params.centeredSlides && params.cssMode && !params.centeredSlidesBounds) {\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-before', `${-snapGrid[0]}px`);\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-after', `${swiper.size / 2 - slidesSizesGrid[slidesSizesGrid.length - 1] / 2}px`);\n    const addToSnapGrid = -swiper.snapGrid[0];\n    const addToSlidesGrid = -swiper.slidesGrid[0];\n    swiper.snapGrid = swiper.snapGrid.map(v => v + addToSnapGrid);\n    swiper.slidesGrid = swiper.slidesGrid.map(v => v + addToSlidesGrid);\n  }\n  if (slidesLength !== previousSlidesLength) {\n    swiper.emit('slidesLengthChange');\n  }\n  if (snapGrid.length !== previousSnapGridLength) {\n    if (swiper.params.watchOverflow) swiper.checkOverflow();\n    swiper.emit('snapGridLengthChange');\n  }\n  if (slidesGrid.length !== previousSlidesGridLength) {\n    swiper.emit('slidesGridLengthChange');\n  }\n  if (params.watchSlidesProgress) {\n    swiper.updateSlidesOffset();\n  }\n  if (!isVirtual && !params.cssMode && (params.effect === 'slide' || params.effect === 'fade')) {\n    const backFaceHiddenClass = `${params.containerModifierClass}backface-hidden`;\n    const hasClassBackfaceClassAdded = swiper.el.classList.contains(backFaceHiddenClass);\n    if (slidesLength <= params.maxBackfaceHiddenSlides) {\n      if (!hasClassBackfaceClassAdded) swiper.el.classList.add(backFaceHiddenClass);\n    } else if (hasClassBackfaceClassAdded) {\n      swiper.el.classList.remove(backFaceHiddenClass);\n    }\n  }\n}", "map": {"version": 3, "names": ["elementChildren", "elementOuterSize", "elementStyle", "setCSSProperty", "updateSlides", "swiper", "getDirectionLabel", "property", "isHorizontal", "getDirectionPropertyValue", "node", "label", "parseFloat", "getPropertyValue", "params", "wrapperEl", "slidesEl", "size", "swiperSize", "rtlTranslate", "rtl", "wrongRTL", "isVirtual", "virtual", "enabled", "previousSlidesLength", "slides", "length", "slideClass", "<PERSON><PERSON><PERSON><PERSON>", "snapGrid", "slidesGrid", "slidesSizesGrid", "offsetBefore", "slidesOffsetBefore", "call", "offsetAfter", "slidesOffsetAfter", "previousSnapGridLength", "previousSlidesGridLength", "spaceBetween", "slidePosition", "prevSlideSize", "index", "indexOf", "replace", "virtualSize", "for<PERSON>ach", "slideEl", "style", "marginLeft", "marginRight", "marginBottom", "marginTop", "centeredSlides", "cssMode", "gridEnabled", "grid", "rows", "initSlides", "slideSize", "shouldResetSlideSize", "<PERSON><PERSON><PERSON><PERSON>iew", "breakpoints", "Object", "keys", "filter", "key", "i", "slide", "updateSlide", "slideStyles", "getComputedStyle", "currentTransform", "transform", "currentWebKitTransform", "webkitTransform", "roundLengths", "width", "paddingLeft", "paddingRight", "boxSizing", "clientWidth", "offsetWidth", "Math", "floor", "swiperSlideSize", "push", "abs", "slidesPerGroup", "min", "slidesPerGroupSkip", "max", "effect", "setWrapperSize", "updateWrapperSize", "newSlidesGrid", "slidesGridItem", "loop", "groups", "ceil", "slidesBefore", "slidesAfter", "groupSize", "_", "slideIndex", "centeredSlidesBounds", "allSlidesSize", "slideSizeValue", "maxSnap", "map", "snap", "centerInsufficientSlides", "allSlidesOffset", "snapIndex", "assign", "addToSnapGrid", "addToSlidesGrid", "v", "emit", "watchOverflow", "checkOverflow", "watchSlidesProgress", "updateSlidesOffset", "backFaceHiddenClass", "containerModifierClass", "hasClassBackfaceClassAdded", "el", "classList", "contains", "maxBackfaceHiddenSlides", "add", "remove"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/core/update/updateSlides.js"], "sourcesContent": ["import { elementChildren, elementOuterSize, elementStyle, setCSSProperty } from '../../shared/utils.js';\nexport default function updateSlides() {\n  const swiper = this;\n  function getDirectionLabel(property) {\n    if (swiper.isHorizontal()) {\n      return property;\n    }\n    // prettier-ignore\n    return {\n      'width': 'height',\n      'margin-top': 'margin-left',\n      'margin-bottom ': 'margin-right',\n      'margin-left': 'margin-top',\n      'margin-right': 'margin-bottom',\n      'padding-left': 'padding-top',\n      'padding-right': 'padding-bottom',\n      'marginRight': 'marginBottom'\n    }[property];\n  }\n  function getDirectionPropertyValue(node, label) {\n    return parseFloat(node.getPropertyValue(getDirectionLabel(label)) || 0);\n  }\n  const params = swiper.params;\n  const {\n    wrapperEl,\n    slidesEl,\n    size: swiperSize,\n    rtlTranslate: rtl,\n    wrongRTL\n  } = swiper;\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  const previousSlidesLength = isVirtual ? swiper.virtual.slides.length : swiper.slides.length;\n  const slides = elementChildren(slidesEl, `.${swiper.params.slideClass}, swiper-slide`);\n  const slidesLength = isVirtual ? swiper.virtual.slides.length : slides.length;\n  let snapGrid = [];\n  const slidesGrid = [];\n  const slidesSizesGrid = [];\n  let offsetBefore = params.slidesOffsetBefore;\n  if (typeof offsetBefore === 'function') {\n    offsetBefore = params.slidesOffsetBefore.call(swiper);\n  }\n  let offsetAfter = params.slidesOffsetAfter;\n  if (typeof offsetAfter === 'function') {\n    offsetAfter = params.slidesOffsetAfter.call(swiper);\n  }\n  const previousSnapGridLength = swiper.snapGrid.length;\n  const previousSlidesGridLength = swiper.slidesGrid.length;\n  let spaceBetween = params.spaceBetween;\n  let slidePosition = -offsetBefore;\n  let prevSlideSize = 0;\n  let index = 0;\n  if (typeof swiperSize === 'undefined') {\n    return;\n  }\n  if (typeof spaceBetween === 'string' && spaceBetween.indexOf('%') >= 0) {\n    spaceBetween = parseFloat(spaceBetween.replace('%', '')) / 100 * swiperSize;\n  } else if (typeof spaceBetween === 'string') {\n    spaceBetween = parseFloat(spaceBetween);\n  }\n  swiper.virtualSize = -spaceBetween;\n\n  // reset margins\n  slides.forEach(slideEl => {\n    if (rtl) {\n      slideEl.style.marginLeft = '';\n    } else {\n      slideEl.style.marginRight = '';\n    }\n    slideEl.style.marginBottom = '';\n    slideEl.style.marginTop = '';\n  });\n\n  // reset cssMode offsets\n  if (params.centeredSlides && params.cssMode) {\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-before', '');\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-after', '');\n  }\n  const gridEnabled = params.grid && params.grid.rows > 1 && swiper.grid;\n  if (gridEnabled) {\n    swiper.grid.initSlides(slidesLength);\n  }\n\n  // Calc slides\n  let slideSize;\n  const shouldResetSlideSize = params.slidesPerView === 'auto' && params.breakpoints && Object.keys(params.breakpoints).filter(key => {\n    return typeof params.breakpoints[key].slidesPerView !== 'undefined';\n  }).length > 0;\n  for (let i = 0; i < slidesLength; i += 1) {\n    slideSize = 0;\n    let slide;\n    if (slides[i]) slide = slides[i];\n    if (gridEnabled) {\n      swiper.grid.updateSlide(i, slide, slidesLength, getDirectionLabel);\n    }\n    if (slides[i] && elementStyle(slide, 'display') === 'none') continue; // eslint-disable-line\n\n    if (params.slidesPerView === 'auto') {\n      if (shouldResetSlideSize) {\n        slides[i].style[getDirectionLabel('width')] = ``;\n      }\n      const slideStyles = getComputedStyle(slide);\n      const currentTransform = slide.style.transform;\n      const currentWebKitTransform = slide.style.webkitTransform;\n      if (currentTransform) {\n        slide.style.transform = 'none';\n      }\n      if (currentWebKitTransform) {\n        slide.style.webkitTransform = 'none';\n      }\n      if (params.roundLengths) {\n        slideSize = swiper.isHorizontal() ? elementOuterSize(slide, 'width', true) : elementOuterSize(slide, 'height', true);\n      } else {\n        // eslint-disable-next-line\n        const width = getDirectionPropertyValue(slideStyles, 'width');\n        const paddingLeft = getDirectionPropertyValue(slideStyles, 'padding-left');\n        const paddingRight = getDirectionPropertyValue(slideStyles, 'padding-right');\n        const marginLeft = getDirectionPropertyValue(slideStyles, 'margin-left');\n        const marginRight = getDirectionPropertyValue(slideStyles, 'margin-right');\n        const boxSizing = slideStyles.getPropertyValue('box-sizing');\n        if (boxSizing && boxSizing === 'border-box') {\n          slideSize = width + marginLeft + marginRight;\n        } else {\n          const {\n            clientWidth,\n            offsetWidth\n          } = slide;\n          slideSize = width + paddingLeft + paddingRight + marginLeft + marginRight + (offsetWidth - clientWidth);\n        }\n      }\n      if (currentTransform) {\n        slide.style.transform = currentTransform;\n      }\n      if (currentWebKitTransform) {\n        slide.style.webkitTransform = currentWebKitTransform;\n      }\n      if (params.roundLengths) slideSize = Math.floor(slideSize);\n    } else {\n      slideSize = (swiperSize - (params.slidesPerView - 1) * spaceBetween) / params.slidesPerView;\n      if (params.roundLengths) slideSize = Math.floor(slideSize);\n      if (slides[i]) {\n        slides[i].style[getDirectionLabel('width')] = `${slideSize}px`;\n      }\n    }\n    if (slides[i]) {\n      slides[i].swiperSlideSize = slideSize;\n    }\n    slidesSizesGrid.push(slideSize);\n    if (params.centeredSlides) {\n      slidePosition = slidePosition + slideSize / 2 + prevSlideSize / 2 + spaceBetween;\n      if (prevSlideSize === 0 && i !== 0) slidePosition = slidePosition - swiperSize / 2 - spaceBetween;\n      if (i === 0) slidePosition = slidePosition - swiperSize / 2 - spaceBetween;\n      if (Math.abs(slidePosition) < 1 / 1000) slidePosition = 0;\n      if (params.roundLengths) slidePosition = Math.floor(slidePosition);\n      if (index % params.slidesPerGroup === 0) snapGrid.push(slidePosition);\n      slidesGrid.push(slidePosition);\n    } else {\n      if (params.roundLengths) slidePosition = Math.floor(slidePosition);\n      if ((index - Math.min(swiper.params.slidesPerGroupSkip, index)) % swiper.params.slidesPerGroup === 0) snapGrid.push(slidePosition);\n      slidesGrid.push(slidePosition);\n      slidePosition = slidePosition + slideSize + spaceBetween;\n    }\n    swiper.virtualSize += slideSize + spaceBetween;\n    prevSlideSize = slideSize;\n    index += 1;\n  }\n  swiper.virtualSize = Math.max(swiper.virtualSize, swiperSize) + offsetAfter;\n  if (rtl && wrongRTL && (params.effect === 'slide' || params.effect === 'coverflow')) {\n    wrapperEl.style.width = `${swiper.virtualSize + spaceBetween}px`;\n  }\n  if (params.setWrapperSize) {\n    wrapperEl.style[getDirectionLabel('width')] = `${swiper.virtualSize + spaceBetween}px`;\n  }\n  if (gridEnabled) {\n    swiper.grid.updateWrapperSize(slideSize, snapGrid, getDirectionLabel);\n  }\n\n  // Remove last grid elements depending on width\n  if (!params.centeredSlides) {\n    const newSlidesGrid = [];\n    for (let i = 0; i < snapGrid.length; i += 1) {\n      let slidesGridItem = snapGrid[i];\n      if (params.roundLengths) slidesGridItem = Math.floor(slidesGridItem);\n      if (snapGrid[i] <= swiper.virtualSize - swiperSize) {\n        newSlidesGrid.push(slidesGridItem);\n      }\n    }\n    snapGrid = newSlidesGrid;\n    if (Math.floor(swiper.virtualSize - swiperSize) - Math.floor(snapGrid[snapGrid.length - 1]) > 1) {\n      snapGrid.push(swiper.virtualSize - swiperSize);\n    }\n  }\n  if (isVirtual && params.loop) {\n    const size = slidesSizesGrid[0] + spaceBetween;\n    if (params.slidesPerGroup > 1) {\n      const groups = Math.ceil((swiper.virtual.slidesBefore + swiper.virtual.slidesAfter) / params.slidesPerGroup);\n      const groupSize = size * params.slidesPerGroup;\n      for (let i = 0; i < groups; i += 1) {\n        snapGrid.push(snapGrid[snapGrid.length - 1] + groupSize);\n      }\n    }\n    for (let i = 0; i < swiper.virtual.slidesBefore + swiper.virtual.slidesAfter; i += 1) {\n      if (params.slidesPerGroup === 1) {\n        snapGrid.push(snapGrid[snapGrid.length - 1] + size);\n      }\n      slidesGrid.push(slidesGrid[slidesGrid.length - 1] + size);\n      swiper.virtualSize += size;\n    }\n  }\n  if (snapGrid.length === 0) snapGrid = [0];\n  if (spaceBetween !== 0) {\n    const key = swiper.isHorizontal() && rtl ? 'marginLeft' : getDirectionLabel('marginRight');\n    slides.filter((_, slideIndex) => {\n      if (!params.cssMode || params.loop) return true;\n      if (slideIndex === slides.length - 1) {\n        return false;\n      }\n      return true;\n    }).forEach(slideEl => {\n      slideEl.style[key] = `${spaceBetween}px`;\n    });\n  }\n  if (params.centeredSlides && params.centeredSlidesBounds) {\n    let allSlidesSize = 0;\n    slidesSizesGrid.forEach(slideSizeValue => {\n      allSlidesSize += slideSizeValue + (spaceBetween || 0);\n    });\n    allSlidesSize -= spaceBetween;\n    const maxSnap = allSlidesSize - swiperSize;\n    snapGrid = snapGrid.map(snap => {\n      if (snap <= 0) return -offsetBefore;\n      if (snap > maxSnap) return maxSnap + offsetAfter;\n      return snap;\n    });\n  }\n  if (params.centerInsufficientSlides) {\n    let allSlidesSize = 0;\n    slidesSizesGrid.forEach(slideSizeValue => {\n      allSlidesSize += slideSizeValue + (spaceBetween || 0);\n    });\n    allSlidesSize -= spaceBetween;\n    if (allSlidesSize < swiperSize) {\n      const allSlidesOffset = (swiperSize - allSlidesSize) / 2;\n      snapGrid.forEach((snap, snapIndex) => {\n        snapGrid[snapIndex] = snap - allSlidesOffset;\n      });\n      slidesGrid.forEach((snap, snapIndex) => {\n        slidesGrid[snapIndex] = snap + allSlidesOffset;\n      });\n    }\n  }\n  Object.assign(swiper, {\n    slides,\n    snapGrid,\n    slidesGrid,\n    slidesSizesGrid\n  });\n  if (params.centeredSlides && params.cssMode && !params.centeredSlidesBounds) {\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-before', `${-snapGrid[0]}px`);\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-after', `${swiper.size / 2 - slidesSizesGrid[slidesSizesGrid.length - 1] / 2}px`);\n    const addToSnapGrid = -swiper.snapGrid[0];\n    const addToSlidesGrid = -swiper.slidesGrid[0];\n    swiper.snapGrid = swiper.snapGrid.map(v => v + addToSnapGrid);\n    swiper.slidesGrid = swiper.slidesGrid.map(v => v + addToSlidesGrid);\n  }\n  if (slidesLength !== previousSlidesLength) {\n    swiper.emit('slidesLengthChange');\n  }\n  if (snapGrid.length !== previousSnapGridLength) {\n    if (swiper.params.watchOverflow) swiper.checkOverflow();\n    swiper.emit('snapGridLengthChange');\n  }\n  if (slidesGrid.length !== previousSlidesGridLength) {\n    swiper.emit('slidesGridLengthChange');\n  }\n  if (params.watchSlidesProgress) {\n    swiper.updateSlidesOffset();\n  }\n  if (!isVirtual && !params.cssMode && (params.effect === 'slide' || params.effect === 'fade')) {\n    const backFaceHiddenClass = `${params.containerModifierClass}backface-hidden`;\n    const hasClassBackfaceClassAdded = swiper.el.classList.contains(backFaceHiddenClass);\n    if (slidesLength <= params.maxBackfaceHiddenSlides) {\n      if (!hasClassBackfaceClassAdded) swiper.el.classList.add(backFaceHiddenClass);\n    } else if (hasClassBackfaceClassAdded) {\n      swiper.el.classList.remove(backFaceHiddenClass);\n    }\n  }\n}"], "mappings": "AAAA,SAASA,eAAe,EAAEC,gBAAgB,EAAEC,YAAY,EAAEC,cAAc,QAAQ,uBAAuB;AACvG,eAAe,SAASC,YAAYA,CAAA,EAAG;EACrC,MAAMC,MAAM,GAAG,IAAI;EACnB,SAASC,iBAAiBA,CAACC,QAAQ,EAAE;IACnC,IAAIF,MAAM,CAACG,YAAY,EAAE,EAAE;MACzB,OAAOD,QAAQ;IACjB;IACA;IACA,OAAO;MACL,OAAO,EAAE,QAAQ;MACjB,YAAY,EAAE,aAAa;MAC3B,gBAAgB,EAAE,cAAc;MAChC,aAAa,EAAE,YAAY;MAC3B,cAAc,EAAE,eAAe;MAC/B,cAAc,EAAE,aAAa;MAC7B,eAAe,EAAE,gBAAgB;MACjC,aAAa,EAAE;IACjB,CAAC,CAACA,QAAQ,CAAC;EACb;EACA,SAASE,yBAAyBA,CAACC,IAAI,EAAEC,KAAK,EAAE;IAC9C,OAAOC,UAAU,CAACF,IAAI,CAACG,gBAAgB,CAACP,iBAAiB,CAACK,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC;EACzE;EACA,MAAMG,MAAM,GAAGT,MAAM,CAACS,MAAM;EAC5B,MAAM;IACJC,SAAS;IACTC,QAAQ;IACRC,IAAI,EAAEC,UAAU;IAChBC,YAAY,EAAEC,GAAG;IACjBC;EACF,CAAC,GAAGhB,MAAM;EACV,MAAMiB,SAAS,GAAGjB,MAAM,CAACkB,OAAO,IAAIT,MAAM,CAACS,OAAO,CAACC,OAAO;EAC1D,MAAMC,oBAAoB,GAAGH,SAAS,GAAGjB,MAAM,CAACkB,OAAO,CAACG,MAAM,CAACC,MAAM,GAAGtB,MAAM,CAACqB,MAAM,CAACC,MAAM;EAC5F,MAAMD,MAAM,GAAG1B,eAAe,CAACgB,QAAQ,EAAG,IAAGX,MAAM,CAACS,MAAM,CAACc,UAAW,gBAAe,CAAC;EACtF,MAAMC,YAAY,GAAGP,SAAS,GAAGjB,MAAM,CAACkB,OAAO,CAACG,MAAM,CAACC,MAAM,GAAGD,MAAM,CAACC,MAAM;EAC7E,IAAIG,QAAQ,GAAG,EAAE;EACjB,MAAMC,UAAU,GAAG,EAAE;EACrB,MAAMC,eAAe,GAAG,EAAE;EAC1B,IAAIC,YAAY,GAAGnB,MAAM,CAACoB,kBAAkB;EAC5C,IAAI,OAAOD,YAAY,KAAK,UAAU,EAAE;IACtCA,YAAY,GAAGnB,MAAM,CAACoB,kBAAkB,CAACC,IAAI,CAAC9B,MAAM,CAAC;EACvD;EACA,IAAI+B,WAAW,GAAGtB,MAAM,CAACuB,iBAAiB;EAC1C,IAAI,OAAOD,WAAW,KAAK,UAAU,EAAE;IACrCA,WAAW,GAAGtB,MAAM,CAACuB,iBAAiB,CAACF,IAAI,CAAC9B,MAAM,CAAC;EACrD;EACA,MAAMiC,sBAAsB,GAAGjC,MAAM,CAACyB,QAAQ,CAACH,MAAM;EACrD,MAAMY,wBAAwB,GAAGlC,MAAM,CAAC0B,UAAU,CAACJ,MAAM;EACzD,IAAIa,YAAY,GAAG1B,MAAM,CAAC0B,YAAY;EACtC,IAAIC,aAAa,GAAG,CAACR,YAAY;EACjC,IAAIS,aAAa,GAAG,CAAC;EACrB,IAAIC,KAAK,GAAG,CAAC;EACb,IAAI,OAAOzB,UAAU,KAAK,WAAW,EAAE;IACrC;EACF;EACA,IAAI,OAAOsB,YAAY,KAAK,QAAQ,IAAIA,YAAY,CAACI,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;IACtEJ,YAAY,GAAG5B,UAAU,CAAC4B,YAAY,CAACK,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,GAAG,GAAG3B,UAAU;EAC7E,CAAC,MAAM,IAAI,OAAOsB,YAAY,KAAK,QAAQ,EAAE;IAC3CA,YAAY,GAAG5B,UAAU,CAAC4B,YAAY,CAAC;EACzC;EACAnC,MAAM,CAACyC,WAAW,GAAG,CAACN,YAAY;;EAElC;EACAd,MAAM,CAACqB,OAAO,CAACC,OAAO,IAAI;IACxB,IAAI5B,GAAG,EAAE;MACP4B,OAAO,CAACC,KAAK,CAACC,UAAU,GAAG,EAAE;IAC/B,CAAC,MAAM;MACLF,OAAO,CAACC,KAAK,CAACE,WAAW,GAAG,EAAE;IAChC;IACAH,OAAO,CAACC,KAAK,CAACG,YAAY,GAAG,EAAE;IAC/BJ,OAAO,CAACC,KAAK,CAACI,SAAS,GAAG,EAAE;EAC9B,CAAC,CAAC;;EAEF;EACA,IAAIvC,MAAM,CAACwC,cAAc,IAAIxC,MAAM,CAACyC,OAAO,EAAE;IAC3CpD,cAAc,CAACY,SAAS,EAAE,iCAAiC,EAAE,EAAE,CAAC;IAChEZ,cAAc,CAACY,SAAS,EAAE,gCAAgC,EAAE,EAAE,CAAC;EACjE;EACA,MAAMyC,WAAW,GAAG1C,MAAM,CAAC2C,IAAI,IAAI3C,MAAM,CAAC2C,IAAI,CAACC,IAAI,GAAG,CAAC,IAAIrD,MAAM,CAACoD,IAAI;EACtE,IAAID,WAAW,EAAE;IACfnD,MAAM,CAACoD,IAAI,CAACE,UAAU,CAAC9B,YAAY,CAAC;EACtC;;EAEA;EACA,IAAI+B,SAAS;EACb,MAAMC,oBAAoB,GAAG/C,MAAM,CAACgD,aAAa,KAAK,MAAM,IAAIhD,MAAM,CAACiD,WAAW,IAAIC,MAAM,CAACC,IAAI,CAACnD,MAAM,CAACiD,WAAW,CAAC,CAACG,MAAM,CAACC,GAAG,IAAI;IAClI,OAAO,OAAOrD,MAAM,CAACiD,WAAW,CAACI,GAAG,CAAC,CAACL,aAAa,KAAK,WAAW;EACrE,CAAC,CAAC,CAACnC,MAAM,GAAG,CAAC;EACb,KAAK,IAAIyC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvC,YAAY,EAAEuC,CAAC,IAAI,CAAC,EAAE;IACxCR,SAAS,GAAG,CAAC;IACb,IAAIS,KAAK;IACT,IAAI3C,MAAM,CAAC0C,CAAC,CAAC,EAAEC,KAAK,GAAG3C,MAAM,CAAC0C,CAAC,CAAC;IAChC,IAAIZ,WAAW,EAAE;MACfnD,MAAM,CAACoD,IAAI,CAACa,WAAW,CAACF,CAAC,EAAEC,KAAK,EAAExC,YAAY,EAAEvB,iBAAiB,CAAC;IACpE;IACA,IAAIoB,MAAM,CAAC0C,CAAC,CAAC,IAAIlE,YAAY,CAACmE,KAAK,EAAE,SAAS,CAAC,KAAK,MAAM,EAAE,SAAS,CAAC;;IAEtE,IAAIvD,MAAM,CAACgD,aAAa,KAAK,MAAM,EAAE;MACnC,IAAID,oBAAoB,EAAE;QACxBnC,MAAM,CAAC0C,CAAC,CAAC,CAACnB,KAAK,CAAC3C,iBAAiB,CAAC,OAAO,CAAC,CAAC,GAAI,EAAC;MAClD;MACA,MAAMiE,WAAW,GAAGC,gBAAgB,CAACH,KAAK,CAAC;MAC3C,MAAMI,gBAAgB,GAAGJ,KAAK,CAACpB,KAAK,CAACyB,SAAS;MAC9C,MAAMC,sBAAsB,GAAGN,KAAK,CAACpB,KAAK,CAAC2B,eAAe;MAC1D,IAAIH,gBAAgB,EAAE;QACpBJ,KAAK,CAACpB,KAAK,CAACyB,SAAS,GAAG,MAAM;MAChC;MACA,IAAIC,sBAAsB,EAAE;QAC1BN,KAAK,CAACpB,KAAK,CAAC2B,eAAe,GAAG,MAAM;MACtC;MACA,IAAI9D,MAAM,CAAC+D,YAAY,EAAE;QACvBjB,SAAS,GAAGvD,MAAM,CAACG,YAAY,EAAE,GAAGP,gBAAgB,CAACoE,KAAK,EAAE,OAAO,EAAE,IAAI,CAAC,GAAGpE,gBAAgB,CAACoE,KAAK,EAAE,QAAQ,EAAE,IAAI,CAAC;MACtH,CAAC,MAAM;QACL;QACA,MAAMS,KAAK,GAAGrE,yBAAyB,CAAC8D,WAAW,EAAE,OAAO,CAAC;QAC7D,MAAMQ,WAAW,GAAGtE,yBAAyB,CAAC8D,WAAW,EAAE,cAAc,CAAC;QAC1E,MAAMS,YAAY,GAAGvE,yBAAyB,CAAC8D,WAAW,EAAE,eAAe,CAAC;QAC5E,MAAMrB,UAAU,GAAGzC,yBAAyB,CAAC8D,WAAW,EAAE,aAAa,CAAC;QACxE,MAAMpB,WAAW,GAAG1C,yBAAyB,CAAC8D,WAAW,EAAE,cAAc,CAAC;QAC1E,MAAMU,SAAS,GAAGV,WAAW,CAAC1D,gBAAgB,CAAC,YAAY,CAAC;QAC5D,IAAIoE,SAAS,IAAIA,SAAS,KAAK,YAAY,EAAE;UAC3CrB,SAAS,GAAGkB,KAAK,GAAG5B,UAAU,GAAGC,WAAW;QAC9C,CAAC,MAAM;UACL,MAAM;YACJ+B,WAAW;YACXC;UACF,CAAC,GAAGd,KAAK;UACTT,SAAS,GAAGkB,KAAK,GAAGC,WAAW,GAAGC,YAAY,GAAG9B,UAAU,GAAGC,WAAW,IAAIgC,WAAW,GAAGD,WAAW,CAAC;QACzG;MACF;MACA,IAAIT,gBAAgB,EAAE;QACpBJ,KAAK,CAACpB,KAAK,CAACyB,SAAS,GAAGD,gBAAgB;MAC1C;MACA,IAAIE,sBAAsB,EAAE;QAC1BN,KAAK,CAACpB,KAAK,CAAC2B,eAAe,GAAGD,sBAAsB;MACtD;MACA,IAAI7D,MAAM,CAAC+D,YAAY,EAAEjB,SAAS,GAAGwB,IAAI,CAACC,KAAK,CAACzB,SAAS,CAAC;IAC5D,CAAC,MAAM;MACLA,SAAS,GAAG,CAAC1C,UAAU,GAAG,CAACJ,MAAM,CAACgD,aAAa,GAAG,CAAC,IAAItB,YAAY,IAAI1B,MAAM,CAACgD,aAAa;MAC3F,IAAIhD,MAAM,CAAC+D,YAAY,EAAEjB,SAAS,GAAGwB,IAAI,CAACC,KAAK,CAACzB,SAAS,CAAC;MAC1D,IAAIlC,MAAM,CAAC0C,CAAC,CAAC,EAAE;QACb1C,MAAM,CAAC0C,CAAC,CAAC,CAACnB,KAAK,CAAC3C,iBAAiB,CAAC,OAAO,CAAC,CAAC,GAAI,GAAEsD,SAAU,IAAG;MAChE;IACF;IACA,IAAIlC,MAAM,CAAC0C,CAAC,CAAC,EAAE;MACb1C,MAAM,CAAC0C,CAAC,CAAC,CAACkB,eAAe,GAAG1B,SAAS;IACvC;IACA5B,eAAe,CAACuD,IAAI,CAAC3B,SAAS,CAAC;IAC/B,IAAI9C,MAAM,CAACwC,cAAc,EAAE;MACzBb,aAAa,GAAGA,aAAa,GAAGmB,SAAS,GAAG,CAAC,GAAGlB,aAAa,GAAG,CAAC,GAAGF,YAAY;MAChF,IAAIE,aAAa,KAAK,CAAC,IAAI0B,CAAC,KAAK,CAAC,EAAE3B,aAAa,GAAGA,aAAa,GAAGvB,UAAU,GAAG,CAAC,GAAGsB,YAAY;MACjG,IAAI4B,CAAC,KAAK,CAAC,EAAE3B,aAAa,GAAGA,aAAa,GAAGvB,UAAU,GAAG,CAAC,GAAGsB,YAAY;MAC1E,IAAI4C,IAAI,CAACI,GAAG,CAAC/C,aAAa,CAAC,GAAG,CAAC,GAAG,IAAI,EAAEA,aAAa,GAAG,CAAC;MACzD,IAAI3B,MAAM,CAAC+D,YAAY,EAAEpC,aAAa,GAAG2C,IAAI,CAACC,KAAK,CAAC5C,aAAa,CAAC;MAClE,IAAIE,KAAK,GAAG7B,MAAM,CAAC2E,cAAc,KAAK,CAAC,EAAE3D,QAAQ,CAACyD,IAAI,CAAC9C,aAAa,CAAC;MACrEV,UAAU,CAACwD,IAAI,CAAC9C,aAAa,CAAC;IAChC,CAAC,MAAM;MACL,IAAI3B,MAAM,CAAC+D,YAAY,EAAEpC,aAAa,GAAG2C,IAAI,CAACC,KAAK,CAAC5C,aAAa,CAAC;MAClE,IAAI,CAACE,KAAK,GAAGyC,IAAI,CAACM,GAAG,CAACrF,MAAM,CAACS,MAAM,CAAC6E,kBAAkB,EAAEhD,KAAK,CAAC,IAAItC,MAAM,CAACS,MAAM,CAAC2E,cAAc,KAAK,CAAC,EAAE3D,QAAQ,CAACyD,IAAI,CAAC9C,aAAa,CAAC;MAClIV,UAAU,CAACwD,IAAI,CAAC9C,aAAa,CAAC;MAC9BA,aAAa,GAAGA,aAAa,GAAGmB,SAAS,GAAGpB,YAAY;IAC1D;IACAnC,MAAM,CAACyC,WAAW,IAAIc,SAAS,GAAGpB,YAAY;IAC9CE,aAAa,GAAGkB,SAAS;IACzBjB,KAAK,IAAI,CAAC;EACZ;EACAtC,MAAM,CAACyC,WAAW,GAAGsC,IAAI,CAACQ,GAAG,CAACvF,MAAM,CAACyC,WAAW,EAAE5B,UAAU,CAAC,GAAGkB,WAAW;EAC3E,IAAIhB,GAAG,IAAIC,QAAQ,KAAKP,MAAM,CAAC+E,MAAM,KAAK,OAAO,IAAI/E,MAAM,CAAC+E,MAAM,KAAK,WAAW,CAAC,EAAE;IACnF9E,SAAS,CAACkC,KAAK,CAAC6B,KAAK,GAAI,GAAEzE,MAAM,CAACyC,WAAW,GAAGN,YAAa,IAAG;EAClE;EACA,IAAI1B,MAAM,CAACgF,cAAc,EAAE;IACzB/E,SAAS,CAACkC,KAAK,CAAC3C,iBAAiB,CAAC,OAAO,CAAC,CAAC,GAAI,GAAED,MAAM,CAACyC,WAAW,GAAGN,YAAa,IAAG;EACxF;EACA,IAAIgB,WAAW,EAAE;IACfnD,MAAM,CAACoD,IAAI,CAACsC,iBAAiB,CAACnC,SAAS,EAAE9B,QAAQ,EAAExB,iBAAiB,CAAC;EACvE;;EAEA;EACA,IAAI,CAACQ,MAAM,CAACwC,cAAc,EAAE;IAC1B,MAAM0C,aAAa,GAAG,EAAE;IACxB,KAAK,IAAI5B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtC,QAAQ,CAACH,MAAM,EAAEyC,CAAC,IAAI,CAAC,EAAE;MAC3C,IAAI6B,cAAc,GAAGnE,QAAQ,CAACsC,CAAC,CAAC;MAChC,IAAItD,MAAM,CAAC+D,YAAY,EAAEoB,cAAc,GAAGb,IAAI,CAACC,KAAK,CAACY,cAAc,CAAC;MACpE,IAAInE,QAAQ,CAACsC,CAAC,CAAC,IAAI/D,MAAM,CAACyC,WAAW,GAAG5B,UAAU,EAAE;QAClD8E,aAAa,CAACT,IAAI,CAACU,cAAc,CAAC;MACpC;IACF;IACAnE,QAAQ,GAAGkE,aAAa;IACxB,IAAIZ,IAAI,CAACC,KAAK,CAAChF,MAAM,CAACyC,WAAW,GAAG5B,UAAU,CAAC,GAAGkE,IAAI,CAACC,KAAK,CAACvD,QAAQ,CAACA,QAAQ,CAACH,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;MAC/FG,QAAQ,CAACyD,IAAI,CAAClF,MAAM,CAACyC,WAAW,GAAG5B,UAAU,CAAC;IAChD;EACF;EACA,IAAII,SAAS,IAAIR,MAAM,CAACoF,IAAI,EAAE;IAC5B,MAAMjF,IAAI,GAAGe,eAAe,CAAC,CAAC,CAAC,GAAGQ,YAAY;IAC9C,IAAI1B,MAAM,CAAC2E,cAAc,GAAG,CAAC,EAAE;MAC7B,MAAMU,MAAM,GAAGf,IAAI,CAACgB,IAAI,CAAC,CAAC/F,MAAM,CAACkB,OAAO,CAAC8E,YAAY,GAAGhG,MAAM,CAACkB,OAAO,CAAC+E,WAAW,IAAIxF,MAAM,CAAC2E,cAAc,CAAC;MAC5G,MAAMc,SAAS,GAAGtF,IAAI,GAAGH,MAAM,CAAC2E,cAAc;MAC9C,KAAK,IAAIrB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+B,MAAM,EAAE/B,CAAC,IAAI,CAAC,EAAE;QAClCtC,QAAQ,CAACyD,IAAI,CAACzD,QAAQ,CAACA,QAAQ,CAACH,MAAM,GAAG,CAAC,CAAC,GAAG4E,SAAS,CAAC;MAC1D;IACF;IACA,KAAK,IAAInC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG/D,MAAM,CAACkB,OAAO,CAAC8E,YAAY,GAAGhG,MAAM,CAACkB,OAAO,CAAC+E,WAAW,EAAElC,CAAC,IAAI,CAAC,EAAE;MACpF,IAAItD,MAAM,CAAC2E,cAAc,KAAK,CAAC,EAAE;QAC/B3D,QAAQ,CAACyD,IAAI,CAACzD,QAAQ,CAACA,QAAQ,CAACH,MAAM,GAAG,CAAC,CAAC,GAAGV,IAAI,CAAC;MACrD;MACAc,UAAU,CAACwD,IAAI,CAACxD,UAAU,CAACA,UAAU,CAACJ,MAAM,GAAG,CAAC,CAAC,GAAGV,IAAI,CAAC;MACzDZ,MAAM,CAACyC,WAAW,IAAI7B,IAAI;IAC5B;EACF;EACA,IAAIa,QAAQ,CAACH,MAAM,KAAK,CAAC,EAAEG,QAAQ,GAAG,CAAC,CAAC,CAAC;EACzC,IAAIU,YAAY,KAAK,CAAC,EAAE;IACtB,MAAM2B,GAAG,GAAG9D,MAAM,CAACG,YAAY,EAAE,IAAIY,GAAG,GAAG,YAAY,GAAGd,iBAAiB,CAAC,aAAa,CAAC;IAC1FoB,MAAM,CAACwC,MAAM,CAAC,CAACsC,CAAC,EAAEC,UAAU,KAAK;MAC/B,IAAI,CAAC3F,MAAM,CAACyC,OAAO,IAAIzC,MAAM,CAACoF,IAAI,EAAE,OAAO,IAAI;MAC/C,IAAIO,UAAU,KAAK/E,MAAM,CAACC,MAAM,GAAG,CAAC,EAAE;QACpC,OAAO,KAAK;MACd;MACA,OAAO,IAAI;IACb,CAAC,CAAC,CAACoB,OAAO,CAACC,OAAO,IAAI;MACpBA,OAAO,CAACC,KAAK,CAACkB,GAAG,CAAC,GAAI,GAAE3B,YAAa,IAAG;IAC1C,CAAC,CAAC;EACJ;EACA,IAAI1B,MAAM,CAACwC,cAAc,IAAIxC,MAAM,CAAC4F,oBAAoB,EAAE;IACxD,IAAIC,aAAa,GAAG,CAAC;IACrB3E,eAAe,CAACe,OAAO,CAAC6D,cAAc,IAAI;MACxCD,aAAa,IAAIC,cAAc,IAAIpE,YAAY,IAAI,CAAC,CAAC;IACvD,CAAC,CAAC;IACFmE,aAAa,IAAInE,YAAY;IAC7B,MAAMqE,OAAO,GAAGF,aAAa,GAAGzF,UAAU;IAC1CY,QAAQ,GAAGA,QAAQ,CAACgF,GAAG,CAACC,IAAI,IAAI;MAC9B,IAAIA,IAAI,IAAI,CAAC,EAAE,OAAO,CAAC9E,YAAY;MACnC,IAAI8E,IAAI,GAAGF,OAAO,EAAE,OAAOA,OAAO,GAAGzE,WAAW;MAChD,OAAO2E,IAAI;IACb,CAAC,CAAC;EACJ;EACA,IAAIjG,MAAM,CAACkG,wBAAwB,EAAE;IACnC,IAAIL,aAAa,GAAG,CAAC;IACrB3E,eAAe,CAACe,OAAO,CAAC6D,cAAc,IAAI;MACxCD,aAAa,IAAIC,cAAc,IAAIpE,YAAY,IAAI,CAAC,CAAC;IACvD,CAAC,CAAC;IACFmE,aAAa,IAAInE,YAAY;IAC7B,IAAImE,aAAa,GAAGzF,UAAU,EAAE;MAC9B,MAAM+F,eAAe,GAAG,CAAC/F,UAAU,GAAGyF,aAAa,IAAI,CAAC;MACxD7E,QAAQ,CAACiB,OAAO,CAAC,CAACgE,IAAI,EAAEG,SAAS,KAAK;QACpCpF,QAAQ,CAACoF,SAAS,CAAC,GAAGH,IAAI,GAAGE,eAAe;MAC9C,CAAC,CAAC;MACFlF,UAAU,CAACgB,OAAO,CAAC,CAACgE,IAAI,EAAEG,SAAS,KAAK;QACtCnF,UAAU,CAACmF,SAAS,CAAC,GAAGH,IAAI,GAAGE,eAAe;MAChD,CAAC,CAAC;IACJ;EACF;EACAjD,MAAM,CAACmD,MAAM,CAAC9G,MAAM,EAAE;IACpBqB,MAAM;IACNI,QAAQ;IACRC,UAAU;IACVC;EACF,CAAC,CAAC;EACF,IAAIlB,MAAM,CAACwC,cAAc,IAAIxC,MAAM,CAACyC,OAAO,IAAI,CAACzC,MAAM,CAAC4F,oBAAoB,EAAE;IAC3EvG,cAAc,CAACY,SAAS,EAAE,iCAAiC,EAAG,GAAE,CAACe,QAAQ,CAAC,CAAC,CAAE,IAAG,CAAC;IACjF3B,cAAc,CAACY,SAAS,EAAE,gCAAgC,EAAG,GAAEV,MAAM,CAACY,IAAI,GAAG,CAAC,GAAGe,eAAe,CAACA,eAAe,CAACL,MAAM,GAAG,CAAC,CAAC,GAAG,CAAE,IAAG,CAAC;IACrI,MAAMyF,aAAa,GAAG,CAAC/G,MAAM,CAACyB,QAAQ,CAAC,CAAC,CAAC;IACzC,MAAMuF,eAAe,GAAG,CAAChH,MAAM,CAAC0B,UAAU,CAAC,CAAC,CAAC;IAC7C1B,MAAM,CAACyB,QAAQ,GAAGzB,MAAM,CAACyB,QAAQ,CAACgF,GAAG,CAACQ,CAAC,IAAIA,CAAC,GAAGF,aAAa,CAAC;IAC7D/G,MAAM,CAAC0B,UAAU,GAAG1B,MAAM,CAAC0B,UAAU,CAAC+E,GAAG,CAACQ,CAAC,IAAIA,CAAC,GAAGD,eAAe,CAAC;EACrE;EACA,IAAIxF,YAAY,KAAKJ,oBAAoB,EAAE;IACzCpB,MAAM,CAACkH,IAAI,CAAC,oBAAoB,CAAC;EACnC;EACA,IAAIzF,QAAQ,CAACH,MAAM,KAAKW,sBAAsB,EAAE;IAC9C,IAAIjC,MAAM,CAACS,MAAM,CAAC0G,aAAa,EAAEnH,MAAM,CAACoH,aAAa,EAAE;IACvDpH,MAAM,CAACkH,IAAI,CAAC,sBAAsB,CAAC;EACrC;EACA,IAAIxF,UAAU,CAACJ,MAAM,KAAKY,wBAAwB,EAAE;IAClDlC,MAAM,CAACkH,IAAI,CAAC,wBAAwB,CAAC;EACvC;EACA,IAAIzG,MAAM,CAAC4G,mBAAmB,EAAE;IAC9BrH,MAAM,CAACsH,kBAAkB,EAAE;EAC7B;EACA,IAAI,CAACrG,SAAS,IAAI,CAACR,MAAM,CAACyC,OAAO,KAAKzC,MAAM,CAAC+E,MAAM,KAAK,OAAO,IAAI/E,MAAM,CAAC+E,MAAM,KAAK,MAAM,CAAC,EAAE;IAC5F,MAAM+B,mBAAmB,GAAI,GAAE9G,MAAM,CAAC+G,sBAAuB,iBAAgB;IAC7E,MAAMC,0BAA0B,GAAGzH,MAAM,CAAC0H,EAAE,CAACC,SAAS,CAACC,QAAQ,CAACL,mBAAmB,CAAC;IACpF,IAAI/F,YAAY,IAAIf,MAAM,CAACoH,uBAAuB,EAAE;MAClD,IAAI,CAACJ,0BAA0B,EAAEzH,MAAM,CAAC0H,EAAE,CAACC,SAAS,CAACG,GAAG,CAACP,mBAAmB,CAAC;IAC/E,CAAC,MAAM,IAAIE,0BAA0B,EAAE;MACrCzH,MAAM,CAAC0H,EAAE,CAACC,SAAS,CAACI,MAAM,CAACR,mBAAmB,CAAC;IACjD;EACF;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}