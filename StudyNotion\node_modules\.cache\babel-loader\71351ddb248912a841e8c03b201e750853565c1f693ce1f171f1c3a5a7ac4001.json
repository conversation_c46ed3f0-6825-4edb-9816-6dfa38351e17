{"ast": null, "code": "/**\n * Check if the given value is *inter-element whitespace*.\n *\n * @param {unknown} thing\n *   Thing to check (typically `Node` or `string`).\n * @returns {boolean}\n *   Whether the `value` is inter-element whitespace (`boolean`): consisting of\n *   zero or more of space, tab (`\\t`), line feed (`\\n`), carriage return\n *   (`\\r`), or form feed (`\\f`).\n *   If a node is passed it must be a `Text` node, whose `value` field is\n *   checked.\n */\nexport function whitespace(thing) {\n  /** @type {string} */\n  const value =\n  // @ts-expect-error looks like a node.\n  thing && typeof thing === 'object' && thing.type === 'text' ?\n  // @ts-expect-error looks like a text.\n  thing.value || '' : thing;\n\n  // HTML whitespace expression.\n  // See <https://infra.spec.whatwg.org/#ascii-whitespace>.\n  return typeof value === 'string' && value.replace(/[ \\t\\n\\f\\r]/g, '') === '';\n}", "map": {"version": 3, "names": ["whitespace", "thing", "value", "type", "replace"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/hast-util-whitespace/index.js"], "sourcesContent": ["/**\n * Check if the given value is *inter-element whitespace*.\n *\n * @param {unknown} thing\n *   Thing to check (typically `Node` or `string`).\n * @returns {boolean}\n *   Whether the `value` is inter-element whitespace (`boolean`): consisting of\n *   zero or more of space, tab (`\\t`), line feed (`\\n`), carriage return\n *   (`\\r`), or form feed (`\\f`).\n *   If a node is passed it must be a `Text` node, whose `value` field is\n *   checked.\n */\nexport function whitespace(thing) {\n  /** @type {string} */\n  const value =\n    // @ts-expect-error looks like a node.\n    thing && typeof thing === 'object' && thing.type === 'text'\n      ? // @ts-expect-error looks like a text.\n        thing.value || ''\n      : thing\n\n  // HTML whitespace expression.\n  // See <https://infra.spec.whatwg.org/#ascii-whitespace>.\n  return typeof value === 'string' && value.replace(/[ \\t\\n\\f\\r]/g, '') === ''\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,UAAUA,CAACC,KAAK,EAAE;EAChC;EACA,MAAMC,KAAK;EACT;EACAD,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACE,IAAI,KAAK,MAAM;EACvD;EACAF,KAAK,CAACC,KAAK,IAAI,EAAE,GACjBD,KAAK;;EAEX;EACA;EACA,OAAO,OAAOC,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACE,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC,KAAK,EAAE;AAC9E"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}