{"ast": null, "code": "export default function Grid(_ref) {\n  let {\n    swiper,\n    extendParams\n  } = _ref;\n  extendParams({\n    grid: {\n      rows: 1,\n      fill: 'column'\n    }\n  });\n  let slidesNumberEvenToRows;\n  let slidesPerRow;\n  let numFullColumns;\n  const getSpaceBetween = () => {\n    let spaceBetween = swiper.params.spaceBetween;\n    if (typeof spaceBetween === 'string' && spaceBetween.indexOf('%') >= 0) {\n      spaceBetween = parseFloat(spaceBetween.replace('%', '')) / 100 * swiper.size;\n    } else if (typeof spaceBetween === 'string') {\n      spaceBetween = parseFloat(spaceBetween);\n    }\n    return spaceBetween;\n  };\n  const initSlides = slidesLength => {\n    const {\n      slidesPerView\n    } = swiper.params;\n    const {\n      rows,\n      fill\n    } = swiper.params.grid;\n    numFullColumns = Math.floor(slidesLength / rows);\n    if (Math.floor(slidesLength / rows) === slidesLength / rows) {\n      slidesNumberEvenToRows = slidesLength;\n    } else {\n      slidesNumberEvenToRows = Math.ceil(slidesLength / rows) * rows;\n    }\n    if (slidesPerView !== 'auto' && fill === 'row') {\n      slidesNumberEvenToRows = Math.max(slidesNumberEvenToRows, slidesPerView * rows);\n    }\n    slidesPerRow = slidesNumberEvenToRows / rows;\n  };\n  const updateSlide = (i, slide, slidesLength, getDirectionLabel) => {\n    const {\n      slidesPerGroup\n    } = swiper.params;\n    const spaceBetween = getSpaceBetween();\n    const {\n      rows,\n      fill\n    } = swiper.params.grid;\n    // Set slides order\n    let newSlideOrderIndex;\n    let column;\n    let row;\n    if (fill === 'row' && slidesPerGroup > 1) {\n      const groupIndex = Math.floor(i / (slidesPerGroup * rows));\n      const slideIndexInGroup = i - rows * slidesPerGroup * groupIndex;\n      const columnsInGroup = groupIndex === 0 ? slidesPerGroup : Math.min(Math.ceil((slidesLength - groupIndex * rows * slidesPerGroup) / rows), slidesPerGroup);\n      row = Math.floor(slideIndexInGroup / columnsInGroup);\n      column = slideIndexInGroup - row * columnsInGroup + groupIndex * slidesPerGroup;\n      newSlideOrderIndex = column + row * slidesNumberEvenToRows / rows;\n      slide.style.order = newSlideOrderIndex;\n    } else if (fill === 'column') {\n      column = Math.floor(i / rows);\n      row = i - column * rows;\n      if (column > numFullColumns || column === numFullColumns && row === rows - 1) {\n        row += 1;\n        if (row >= rows) {\n          row = 0;\n          column += 1;\n        }\n      }\n    } else {\n      row = Math.floor(i / slidesPerRow);\n      column = i - row * slidesPerRow;\n    }\n    slide.row = row;\n    slide.column = column;\n    slide.style[getDirectionLabel('margin-top')] = row !== 0 ? spaceBetween && `${spaceBetween}px` : '';\n  };\n  const updateWrapperSize = (slideSize, snapGrid, getDirectionLabel) => {\n    const {\n      centeredSlides,\n      roundLengths\n    } = swiper.params;\n    const spaceBetween = getSpaceBetween();\n    const {\n      rows\n    } = swiper.params.grid;\n    swiper.virtualSize = (slideSize + spaceBetween) * slidesNumberEvenToRows;\n    swiper.virtualSize = Math.ceil(swiper.virtualSize / rows) - spaceBetween;\n    swiper.wrapperEl.style[getDirectionLabel('width')] = `${swiper.virtualSize + spaceBetween}px`;\n    if (centeredSlides) {\n      const newSlidesGrid = [];\n      for (let i = 0; i < snapGrid.length; i += 1) {\n        let slidesGridItem = snapGrid[i];\n        if (roundLengths) slidesGridItem = Math.floor(slidesGridItem);\n        if (snapGrid[i] < swiper.virtualSize + snapGrid[0]) newSlidesGrid.push(slidesGridItem);\n      }\n      snapGrid.splice(0, snapGrid.length);\n      snapGrid.push(...newSlidesGrid);\n    }\n  };\n  swiper.grid = {\n    initSlides,\n    updateSlide,\n    updateWrapperSize\n  };\n}", "map": {"version": 3, "names": ["Grid", "_ref", "swiper", "extendParams", "grid", "rows", "fill", "slidesNumberEvenToRows", "slidesPerRow", "numFullColumns", "getSpaceBetween", "spaceBetween", "params", "indexOf", "parseFloat", "replace", "size", "initSlides", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>iew", "Math", "floor", "ceil", "max", "updateSlide", "i", "slide", "getDirectionLabel", "slidesPerGroup", "newSlideOrderIndex", "column", "row", "groupIndex", "slideIndexInGroup", "columnsInGroup", "min", "style", "order", "updateWrapperSize", "slideSize", "snapGrid", "centeredSlides", "roundLengths", "virtualSize", "wrapperEl", "newSlidesGrid", "length", "slidesGridItem", "push", "splice"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/modules/grid/grid.js"], "sourcesContent": ["export default function Grid({\n  swiper,\n  extendParams\n}) {\n  extendParams({\n    grid: {\n      rows: 1,\n      fill: 'column'\n    }\n  });\n  let slidesNumberEvenToRows;\n  let slidesPerRow;\n  let numFullColumns;\n  const getSpaceBetween = () => {\n    let spaceBetween = swiper.params.spaceBetween;\n    if (typeof spaceBetween === 'string' && spaceBetween.indexOf('%') >= 0) {\n      spaceBetween = parseFloat(spaceBetween.replace('%', '')) / 100 * swiper.size;\n    } else if (typeof spaceBetween === 'string') {\n      spaceBetween = parseFloat(spaceBetween);\n    }\n    return spaceBetween;\n  };\n  const initSlides = slidesLength => {\n    const {\n      slidesPerView\n    } = swiper.params;\n    const {\n      rows,\n      fill\n    } = swiper.params.grid;\n    numFullColumns = Math.floor(slidesLength / rows);\n    if (Math.floor(slidesLength / rows) === slidesLength / rows) {\n      slidesNumberEvenToRows = slidesLength;\n    } else {\n      slidesNumberEvenToRows = Math.ceil(slidesLength / rows) * rows;\n    }\n    if (slidesPerView !== 'auto' && fill === 'row') {\n      slidesNumberEvenToRows = Math.max(slidesNumberEvenToRows, slidesPerView * rows);\n    }\n    slidesPerRow = slidesNumberEvenToRows / rows;\n  };\n  const updateSlide = (i, slide, slidesLength, getDirectionLabel) => {\n    const {\n      slidesPerGroup\n    } = swiper.params;\n    const spaceBetween = getSpaceBetween();\n    const {\n      rows,\n      fill\n    } = swiper.params.grid;\n    // Set slides order\n    let newSlideOrderIndex;\n    let column;\n    let row;\n    if (fill === 'row' && slidesPerGroup > 1) {\n      const groupIndex = Math.floor(i / (slidesPerGroup * rows));\n      const slideIndexInGroup = i - rows * slidesPerGroup * groupIndex;\n      const columnsInGroup = groupIndex === 0 ? slidesPerGroup : Math.min(Math.ceil((slidesLength - groupIndex * rows * slidesPerGroup) / rows), slidesPerGroup);\n      row = Math.floor(slideIndexInGroup / columnsInGroup);\n      column = slideIndexInGroup - row * columnsInGroup + groupIndex * slidesPerGroup;\n      newSlideOrderIndex = column + row * slidesNumberEvenToRows / rows;\n      slide.style.order = newSlideOrderIndex;\n    } else if (fill === 'column') {\n      column = Math.floor(i / rows);\n      row = i - column * rows;\n      if (column > numFullColumns || column === numFullColumns && row === rows - 1) {\n        row += 1;\n        if (row >= rows) {\n          row = 0;\n          column += 1;\n        }\n      }\n    } else {\n      row = Math.floor(i / slidesPerRow);\n      column = i - row * slidesPerRow;\n    }\n    slide.row = row;\n    slide.column = column;\n    slide.style[getDirectionLabel('margin-top')] = row !== 0 ? spaceBetween && `${spaceBetween}px` : '';\n  };\n  const updateWrapperSize = (slideSize, snapGrid, getDirectionLabel) => {\n    const {\n      centeredSlides,\n      roundLengths\n    } = swiper.params;\n    const spaceBetween = getSpaceBetween();\n    const {\n      rows\n    } = swiper.params.grid;\n    swiper.virtualSize = (slideSize + spaceBetween) * slidesNumberEvenToRows;\n    swiper.virtualSize = Math.ceil(swiper.virtualSize / rows) - spaceBetween;\n    swiper.wrapperEl.style[getDirectionLabel('width')] = `${swiper.virtualSize + spaceBetween}px`;\n    if (centeredSlides) {\n      const newSlidesGrid = [];\n      for (let i = 0; i < snapGrid.length; i += 1) {\n        let slidesGridItem = snapGrid[i];\n        if (roundLengths) slidesGridItem = Math.floor(slidesGridItem);\n        if (snapGrid[i] < swiper.virtualSize + snapGrid[0]) newSlidesGrid.push(slidesGridItem);\n      }\n      snapGrid.splice(0, snapGrid.length);\n      snapGrid.push(...newSlidesGrid);\n    }\n  };\n  swiper.grid = {\n    initSlides,\n    updateSlide,\n    updateWrapperSize\n  };\n}"], "mappings": "AAAA,eAAe,SAASA,IAAIA,CAAAC,IAAA,EAGzB;EAAA,IAH0B;IAC3BC,MAAM;IACNC;EACF,CAAC,GAAAF,IAAA;EACCE,YAAY,CAAC;IACXC,IAAI,EAAE;MACJC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE;IACR;EACF,CAAC,CAAC;EACF,IAAIC,sBAAsB;EAC1B,IAAIC,YAAY;EAChB,IAAIC,cAAc;EAClB,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAIC,YAAY,GAAGT,MAAM,CAACU,MAAM,CAACD,YAAY;IAC7C,IAAI,OAAOA,YAAY,KAAK,QAAQ,IAAIA,YAAY,CAACE,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;MACtEF,YAAY,GAAGG,UAAU,CAACH,YAAY,CAACI,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,GAAG,GAAGb,MAAM,CAACc,IAAI;IAC9E,CAAC,MAAM,IAAI,OAAOL,YAAY,KAAK,QAAQ,EAAE;MAC3CA,YAAY,GAAGG,UAAU,CAACH,YAAY,CAAC;IACzC;IACA,OAAOA,YAAY;EACrB,CAAC;EACD,MAAMM,UAAU,GAAGC,YAAY,IAAI;IACjC,MAAM;MACJC;IACF,CAAC,GAAGjB,MAAM,CAACU,MAAM;IACjB,MAAM;MACJP,IAAI;MACJC;IACF,CAAC,GAAGJ,MAAM,CAACU,MAAM,CAACR,IAAI;IACtBK,cAAc,GAAGW,IAAI,CAACC,KAAK,CAACH,YAAY,GAAGb,IAAI,CAAC;IAChD,IAAIe,IAAI,CAACC,KAAK,CAACH,YAAY,GAAGb,IAAI,CAAC,KAAKa,YAAY,GAAGb,IAAI,EAAE;MAC3DE,sBAAsB,GAAGW,YAAY;IACvC,CAAC,MAAM;MACLX,sBAAsB,GAAGa,IAAI,CAACE,IAAI,CAACJ,YAAY,GAAGb,IAAI,CAAC,GAAGA,IAAI;IAChE;IACA,IAAIc,aAAa,KAAK,MAAM,IAAIb,IAAI,KAAK,KAAK,EAAE;MAC9CC,sBAAsB,GAAGa,IAAI,CAACG,GAAG,CAAChB,sBAAsB,EAAEY,aAAa,GAAGd,IAAI,CAAC;IACjF;IACAG,YAAY,GAAGD,sBAAsB,GAAGF,IAAI;EAC9C,CAAC;EACD,MAAMmB,WAAW,GAAGA,CAACC,CAAC,EAAEC,KAAK,EAAER,YAAY,EAAES,iBAAiB,KAAK;IACjE,MAAM;MACJC;IACF,CAAC,GAAG1B,MAAM,CAACU,MAAM;IACjB,MAAMD,YAAY,GAAGD,eAAe,EAAE;IACtC,MAAM;MACJL,IAAI;MACJC;IACF,CAAC,GAAGJ,MAAM,CAACU,MAAM,CAACR,IAAI;IACtB;IACA,IAAIyB,kBAAkB;IACtB,IAAIC,MAAM;IACV,IAAIC,GAAG;IACP,IAAIzB,IAAI,KAAK,KAAK,IAAIsB,cAAc,GAAG,CAAC,EAAE;MACxC,MAAMI,UAAU,GAAGZ,IAAI,CAACC,KAAK,CAACI,CAAC,IAAIG,cAAc,GAAGvB,IAAI,CAAC,CAAC;MAC1D,MAAM4B,iBAAiB,GAAGR,CAAC,GAAGpB,IAAI,GAAGuB,cAAc,GAAGI,UAAU;MAChE,MAAME,cAAc,GAAGF,UAAU,KAAK,CAAC,GAAGJ,cAAc,GAAGR,IAAI,CAACe,GAAG,CAACf,IAAI,CAACE,IAAI,CAAC,CAACJ,YAAY,GAAGc,UAAU,GAAG3B,IAAI,GAAGuB,cAAc,IAAIvB,IAAI,CAAC,EAAEuB,cAAc,CAAC;MAC1JG,GAAG,GAAGX,IAAI,CAACC,KAAK,CAACY,iBAAiB,GAAGC,cAAc,CAAC;MACpDJ,MAAM,GAAGG,iBAAiB,GAAGF,GAAG,GAAGG,cAAc,GAAGF,UAAU,GAAGJ,cAAc;MAC/EC,kBAAkB,GAAGC,MAAM,GAAGC,GAAG,GAAGxB,sBAAsB,GAAGF,IAAI;MACjEqB,KAAK,CAACU,KAAK,CAACC,KAAK,GAAGR,kBAAkB;IACxC,CAAC,MAAM,IAAIvB,IAAI,KAAK,QAAQ,EAAE;MAC5BwB,MAAM,GAAGV,IAAI,CAACC,KAAK,CAACI,CAAC,GAAGpB,IAAI,CAAC;MAC7B0B,GAAG,GAAGN,CAAC,GAAGK,MAAM,GAAGzB,IAAI;MACvB,IAAIyB,MAAM,GAAGrB,cAAc,IAAIqB,MAAM,KAAKrB,cAAc,IAAIsB,GAAG,KAAK1B,IAAI,GAAG,CAAC,EAAE;QAC5E0B,GAAG,IAAI,CAAC;QACR,IAAIA,GAAG,IAAI1B,IAAI,EAAE;UACf0B,GAAG,GAAG,CAAC;UACPD,MAAM,IAAI,CAAC;QACb;MACF;IACF,CAAC,MAAM;MACLC,GAAG,GAAGX,IAAI,CAACC,KAAK,CAACI,CAAC,GAAGjB,YAAY,CAAC;MAClCsB,MAAM,GAAGL,CAAC,GAAGM,GAAG,GAAGvB,YAAY;IACjC;IACAkB,KAAK,CAACK,GAAG,GAAGA,GAAG;IACfL,KAAK,CAACI,MAAM,GAAGA,MAAM;IACrBJ,KAAK,CAACU,KAAK,CAACT,iBAAiB,CAAC,YAAY,CAAC,CAAC,GAAGI,GAAG,KAAK,CAAC,GAAGpB,YAAY,IAAK,GAAEA,YAAa,IAAG,GAAG,EAAE;EACrG,CAAC;EACD,MAAM2B,iBAAiB,GAAGA,CAACC,SAAS,EAAEC,QAAQ,EAAEb,iBAAiB,KAAK;IACpE,MAAM;MACJc,cAAc;MACdC;IACF,CAAC,GAAGxC,MAAM,CAACU,MAAM;IACjB,MAAMD,YAAY,GAAGD,eAAe,EAAE;IACtC,MAAM;MACJL;IACF,CAAC,GAAGH,MAAM,CAACU,MAAM,CAACR,IAAI;IACtBF,MAAM,CAACyC,WAAW,GAAG,CAACJ,SAAS,GAAG5B,YAAY,IAAIJ,sBAAsB;IACxEL,MAAM,CAACyC,WAAW,GAAGvB,IAAI,CAACE,IAAI,CAACpB,MAAM,CAACyC,WAAW,GAAGtC,IAAI,CAAC,GAAGM,YAAY;IACxET,MAAM,CAAC0C,SAAS,CAACR,KAAK,CAACT,iBAAiB,CAAC,OAAO,CAAC,CAAC,GAAI,GAAEzB,MAAM,CAACyC,WAAW,GAAGhC,YAAa,IAAG;IAC7F,IAAI8B,cAAc,EAAE;MAClB,MAAMI,aAAa,GAAG,EAAE;MACxB,KAAK,IAAIpB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGe,QAAQ,CAACM,MAAM,EAAErB,CAAC,IAAI,CAAC,EAAE;QAC3C,IAAIsB,cAAc,GAAGP,QAAQ,CAACf,CAAC,CAAC;QAChC,IAAIiB,YAAY,EAAEK,cAAc,GAAG3B,IAAI,CAACC,KAAK,CAAC0B,cAAc,CAAC;QAC7D,IAAIP,QAAQ,CAACf,CAAC,CAAC,GAAGvB,MAAM,CAACyC,WAAW,GAAGH,QAAQ,CAAC,CAAC,CAAC,EAAEK,aAAa,CAACG,IAAI,CAACD,cAAc,CAAC;MACxF;MACAP,QAAQ,CAACS,MAAM,CAAC,CAAC,EAAET,QAAQ,CAACM,MAAM,CAAC;MACnCN,QAAQ,CAACQ,IAAI,CAAC,GAAGH,aAAa,CAAC;IACjC;EACF,CAAC;EACD3C,MAAM,CAACE,IAAI,GAAG;IACZa,UAAU;IACVO,WAAW;IACXc;EACF,CAAC;AACH"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}