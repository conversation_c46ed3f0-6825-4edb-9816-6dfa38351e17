{"ast": null, "code": "/**\n * @license React\n * use-sync-external-store-shim.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function () {\n    'use strict';\n\n    /* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\n    if (typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' && typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart === 'function') {\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(new Error());\n    }\n    var React = require('react');\n    var ReactSharedInternals = React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n    function error(format) {\n      {\n        {\n          for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n            args[_key2 - 1] = arguments[_key2];\n          }\n          printWarning('error', format, args);\n        }\n      }\n    }\n    function printWarning(level, format, args) {\n      // When changing this logic, you might want to also\n      // update consoleWithStackDev.www.js as well.\n      {\n        var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n        var stack = ReactDebugCurrentFrame.getStackAddendum();\n        if (stack !== '') {\n          format += '%s';\n          args = args.concat([stack]);\n        } // eslint-disable-next-line react-internal/safe-string-coercion\n\n        var argsWithFormat = args.map(function (item) {\n          return String(item);\n        }); // Careful: RN currently depends on this prefix\n\n        argsWithFormat.unshift('Warning: ' + format); // We intentionally don't use spread (or .apply) directly because it\n        // breaks IE9: https://github.com/facebook/react/issues/13610\n        // eslint-disable-next-line react-internal/no-production-logging\n\n        Function.prototype.apply.call(console[level], console, argsWithFormat);\n      }\n    }\n\n    /**\n     * inlined Object.is polyfill to avoid requiring consumers ship their own\n     * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\n     */\n    function is(x, y) {\n      return x === y && (x !== 0 || 1 / x === 1 / y) || x !== x && y !== y // eslint-disable-line no-self-compare\n      ;\n    }\n\n    var objectIs = typeof Object.is === 'function' ? Object.is : is;\n\n    // dispatch for CommonJS interop named imports.\n\n    var useState = React.useState,\n      useEffect = React.useEffect,\n      useLayoutEffect = React.useLayoutEffect,\n      useDebugValue = React.useDebugValue;\n    var didWarnOld18Alpha = false;\n    var didWarnUncachedGetSnapshot = false; // Disclaimer: This shim breaks many of the rules of React, and only works\n    // because of a very particular set of implementation details and assumptions\n    // -- change any one of them and it will break. The most important assumption\n    // is that updates are always synchronous, because concurrent rendering is\n    // only available in versions of React that also have a built-in\n    // useSyncExternalStore API. And we only use this shim when the built-in API\n    // does not exist.\n    //\n    // Do not assume that the clever hacks used by this hook also work in general.\n    // The point of this shim is to replace the need for hacks by other libraries.\n\n    function useSyncExternalStore(subscribe, getSnapshot,\n    // Note: The shim does not use getServerSnapshot, because pre-18 versions of\n    // React do not expose a way to check if we're hydrating. So users of the shim\n    // will need to track that themselves and return the correct value\n    // from `getSnapshot`.\n    getServerSnapshot) {\n      {\n        if (!didWarnOld18Alpha) {\n          if (React.startTransition !== undefined) {\n            didWarnOld18Alpha = true;\n            error('You are using an outdated, pre-release alpha of React 18 that ' + 'does not support useSyncExternalStore. The ' + 'use-sync-external-store shim will not work correctly. Upgrade ' + 'to a newer pre-release.');\n          }\n        }\n      } // Read the current snapshot from the store on every render. Again, this\n      // breaks the rules of React, and only works here because of specific\n      // implementation details, most importantly that updates are\n      // always synchronous.\n\n      var value = getSnapshot();\n      {\n        if (!didWarnUncachedGetSnapshot) {\n          var cachedValue = getSnapshot();\n          if (!objectIs(value, cachedValue)) {\n            error('The result of getSnapshot should be cached to avoid an infinite loop');\n            didWarnUncachedGetSnapshot = true;\n          }\n        }\n      } // Because updates are synchronous, we don't queue them. Instead we force a\n      // re-render whenever the subscribed state changes by updating an some\n      // arbitrary useState hook. Then, during render, we call getSnapshot to read\n      // the current value.\n      //\n      // Because we don't actually use the state returned by the useState hook, we\n      // can save a bit of memory by storing other stuff in that slot.\n      //\n      // To implement the early bailout, we need to track some things on a mutable\n      // object. Usually, we would put that in a useRef hook, but we can stash it in\n      // our useState hook instead.\n      //\n      // To force a re-render, we call forceUpdate({inst}). That works because the\n      // new object always fails an equality check.\n\n      var _useState = useState({\n          inst: {\n            value: value,\n            getSnapshot: getSnapshot\n          }\n        }),\n        inst = _useState[0].inst,\n        forceUpdate = _useState[1]; // Track the latest getSnapshot function with a ref. This needs to be updated\n      // in the layout phase so we can access it during the tearing check that\n      // happens on subscribe.\n\n      useLayoutEffect(function () {\n        inst.value = value;\n        inst.getSnapshot = getSnapshot; // Whenever getSnapshot or subscribe changes, we need to check in the\n        // commit phase if there was an interleaved mutation. In concurrent mode\n        // this can happen all the time, but even in synchronous mode, an earlier\n        // effect may have mutated the store.\n\n        if (checkIfSnapshotChanged(inst)) {\n          // Force a re-render.\n          forceUpdate({\n            inst: inst\n          });\n        }\n      }, [subscribe, value, getSnapshot]);\n      useEffect(function () {\n        // Check for changes right before subscribing. Subsequent changes will be\n        // detected in the subscription handler.\n        if (checkIfSnapshotChanged(inst)) {\n          // Force a re-render.\n          forceUpdate({\n            inst: inst\n          });\n        }\n        var handleStoreChange = function () {\n          // TODO: Because there is no cross-renderer API for batching updates, it's\n          // up to the consumer of this library to wrap their subscription event\n          // with unstable_batchedUpdates. Should we try to detect when this isn't\n          // the case and print a warning in development?\n          // The store changed. Check if the snapshot changed since the last time we\n          // read from the store.\n          if (checkIfSnapshotChanged(inst)) {\n            // Force a re-render.\n            forceUpdate({\n              inst: inst\n            });\n          }\n        }; // Subscribe to the store and return a clean-up function.\n\n        return subscribe(handleStoreChange);\n      }, [subscribe]);\n      useDebugValue(value);\n      return value;\n    }\n    function checkIfSnapshotChanged(inst) {\n      var latestGetSnapshot = inst.getSnapshot;\n      var prevValue = inst.value;\n      try {\n        var nextValue = latestGetSnapshot();\n        return !objectIs(prevValue, nextValue);\n      } catch (error) {\n        return true;\n      }\n    }\n    function useSyncExternalStore$1(subscribe, getSnapshot, getServerSnapshot) {\n      // Note: The shim does not use getServerSnapshot, because pre-18 versions of\n      // React do not expose a way to check if we're hydrating. So users of the shim\n      // will need to track that themselves and return the correct value\n      // from `getSnapshot`.\n      return getSnapshot();\n    }\n    var canUseDOM = !!(typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined');\n    var isServerEnvironment = !canUseDOM;\n    var shim = isServerEnvironment ? useSyncExternalStore$1 : useSyncExternalStore;\n    var useSyncExternalStore$2 = React.useSyncExternalStore !== undefined ? React.useSyncExternalStore : shim;\n    exports.useSyncExternalStore = useSyncExternalStore$2;\n    /* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\n    if (typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' && typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop === 'function') {\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(new Error());\n    }\n  })();\n}", "map": {"version": 3, "names": ["process", "env", "NODE_ENV", "__REACT_DEVTOOLS_GLOBAL_HOOK__", "registerInternalModuleStart", "Error", "React", "require", "ReactSharedInternals", "__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED", "error", "format", "_len2", "arguments", "length", "args", "Array", "_key2", "printWarning", "level", "ReactDebugCurrentFrame", "stack", "getStackAddendum", "concat", "argsWithFormat", "map", "item", "String", "unshift", "Function", "prototype", "apply", "call", "console", "is", "x", "y", "objectIs", "Object", "useState", "useEffect", "useLayoutEffect", "useDebugValue", "didWarnOld18Alpha", "didWarnUncachedGetSnapshot", "useSyncExternalStore", "subscribe", "getSnapshot", "getServerSnapshot", "startTransition", "undefined", "value", "cachedValue", "_useState", "inst", "forceUpdate", "checkIfSnapshotChanged", "handleStoreChange", "latestGetSnapshot", "prevValue", "nextValue", "useSyncExternalStore$1", "canUseDOM", "window", "document", "createElement", "isServerEnvironment", "shim", "useSyncExternalStore$2", "exports", "registerInternalModuleStop"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/use-sync-external-store/cjs/use-sync-external-store-shim.development.js"], "sourcesContent": ["/**\n * @license React\n * use-sync-external-store-shim.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function() {\n\n          'use strict';\n\n/* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\nif (\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' &&\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart ===\n    'function'\n) {\n  __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(new Error());\n}\n          var React = require('react');\n\nvar ReactSharedInternals = React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n\nfunction error(format) {\n  {\n    {\n      for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n        args[_key2 - 1] = arguments[_key2];\n      }\n\n      printWarning('error', format, args);\n    }\n  }\n}\n\nfunction printWarning(level, format, args) {\n  // When changing this logic, you might want to also\n  // update consoleWithStackDev.www.js as well.\n  {\n    var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n    var stack = ReactDebugCurrentFrame.getStackAddendum();\n\n    if (stack !== '') {\n      format += '%s';\n      args = args.concat([stack]);\n    } // eslint-disable-next-line react-internal/safe-string-coercion\n\n\n    var argsWithFormat = args.map(function (item) {\n      return String(item);\n    }); // Careful: RN currently depends on this prefix\n\n    argsWithFormat.unshift('Warning: ' + format); // We intentionally don't use spread (or .apply) directly because it\n    // breaks IE9: https://github.com/facebook/react/issues/13610\n    // eslint-disable-next-line react-internal/no-production-logging\n\n    Function.prototype.apply.call(console[level], console, argsWithFormat);\n  }\n}\n\n/**\n * inlined Object.is polyfill to avoid requiring consumers ship their own\n * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\n */\nfunction is(x, y) {\n  return x === y && (x !== 0 || 1 / x === 1 / y) || x !== x && y !== y // eslint-disable-line no-self-compare\n  ;\n}\n\nvar objectIs = typeof Object.is === 'function' ? Object.is : is;\n\n// dispatch for CommonJS interop named imports.\n\nvar useState = React.useState,\n    useEffect = React.useEffect,\n    useLayoutEffect = React.useLayoutEffect,\n    useDebugValue = React.useDebugValue;\nvar didWarnOld18Alpha = false;\nvar didWarnUncachedGetSnapshot = false; // Disclaimer: This shim breaks many of the rules of React, and only works\n// because of a very particular set of implementation details and assumptions\n// -- change any one of them and it will break. The most important assumption\n// is that updates are always synchronous, because concurrent rendering is\n// only available in versions of React that also have a built-in\n// useSyncExternalStore API. And we only use this shim when the built-in API\n// does not exist.\n//\n// Do not assume that the clever hacks used by this hook also work in general.\n// The point of this shim is to replace the need for hacks by other libraries.\n\nfunction useSyncExternalStore(subscribe, getSnapshot, // Note: The shim does not use getServerSnapshot, because pre-18 versions of\n// React do not expose a way to check if we're hydrating. So users of the shim\n// will need to track that themselves and return the correct value\n// from `getSnapshot`.\ngetServerSnapshot) {\n  {\n    if (!didWarnOld18Alpha) {\n      if (React.startTransition !== undefined) {\n        didWarnOld18Alpha = true;\n\n        error('You are using an outdated, pre-release alpha of React 18 that ' + 'does not support useSyncExternalStore. The ' + 'use-sync-external-store shim will not work correctly. Upgrade ' + 'to a newer pre-release.');\n      }\n    }\n  } // Read the current snapshot from the store on every render. Again, this\n  // breaks the rules of React, and only works here because of specific\n  // implementation details, most importantly that updates are\n  // always synchronous.\n\n\n  var value = getSnapshot();\n\n  {\n    if (!didWarnUncachedGetSnapshot) {\n      var cachedValue = getSnapshot();\n\n      if (!objectIs(value, cachedValue)) {\n        error('The result of getSnapshot should be cached to avoid an infinite loop');\n\n        didWarnUncachedGetSnapshot = true;\n      }\n    }\n  } // Because updates are synchronous, we don't queue them. Instead we force a\n  // re-render whenever the subscribed state changes by updating an some\n  // arbitrary useState hook. Then, during render, we call getSnapshot to read\n  // the current value.\n  //\n  // Because we don't actually use the state returned by the useState hook, we\n  // can save a bit of memory by storing other stuff in that slot.\n  //\n  // To implement the early bailout, we need to track some things on a mutable\n  // object. Usually, we would put that in a useRef hook, but we can stash it in\n  // our useState hook instead.\n  //\n  // To force a re-render, we call forceUpdate({inst}). That works because the\n  // new object always fails an equality check.\n\n\n  var _useState = useState({\n    inst: {\n      value: value,\n      getSnapshot: getSnapshot\n    }\n  }),\n      inst = _useState[0].inst,\n      forceUpdate = _useState[1]; // Track the latest getSnapshot function with a ref. This needs to be updated\n  // in the layout phase so we can access it during the tearing check that\n  // happens on subscribe.\n\n\n  useLayoutEffect(function () {\n    inst.value = value;\n    inst.getSnapshot = getSnapshot; // Whenever getSnapshot or subscribe changes, we need to check in the\n    // commit phase if there was an interleaved mutation. In concurrent mode\n    // this can happen all the time, but even in synchronous mode, an earlier\n    // effect may have mutated the store.\n\n    if (checkIfSnapshotChanged(inst)) {\n      // Force a re-render.\n      forceUpdate({\n        inst: inst\n      });\n    }\n  }, [subscribe, value, getSnapshot]);\n  useEffect(function () {\n    // Check for changes right before subscribing. Subsequent changes will be\n    // detected in the subscription handler.\n    if (checkIfSnapshotChanged(inst)) {\n      // Force a re-render.\n      forceUpdate({\n        inst: inst\n      });\n    }\n\n    var handleStoreChange = function () {\n      // TODO: Because there is no cross-renderer API for batching updates, it's\n      // up to the consumer of this library to wrap their subscription event\n      // with unstable_batchedUpdates. Should we try to detect when this isn't\n      // the case and print a warning in development?\n      // The store changed. Check if the snapshot changed since the last time we\n      // read from the store.\n      if (checkIfSnapshotChanged(inst)) {\n        // Force a re-render.\n        forceUpdate({\n          inst: inst\n        });\n      }\n    }; // Subscribe to the store and return a clean-up function.\n\n\n    return subscribe(handleStoreChange);\n  }, [subscribe]);\n  useDebugValue(value);\n  return value;\n}\n\nfunction checkIfSnapshotChanged(inst) {\n  var latestGetSnapshot = inst.getSnapshot;\n  var prevValue = inst.value;\n\n  try {\n    var nextValue = latestGetSnapshot();\n    return !objectIs(prevValue, nextValue);\n  } catch (error) {\n    return true;\n  }\n}\n\nfunction useSyncExternalStore$1(subscribe, getSnapshot, getServerSnapshot) {\n  // Note: The shim does not use getServerSnapshot, because pre-18 versions of\n  // React do not expose a way to check if we're hydrating. So users of the shim\n  // will need to track that themselves and return the correct value\n  // from `getSnapshot`.\n  return getSnapshot();\n}\n\nvar canUseDOM = !!(typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined');\n\nvar isServerEnvironment = !canUseDOM;\n\nvar shim = isServerEnvironment ? useSyncExternalStore$1 : useSyncExternalStore;\nvar useSyncExternalStore$2 = React.useSyncExternalStore !== undefined ? React.useSyncExternalStore : shim;\n\nexports.useSyncExternalStore = useSyncExternalStore$2;\n          /* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\nif (\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' &&\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop ===\n    'function'\n) {\n  __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(new Error());\n}\n        \n  })();\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZ,IAAIA,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzC,CAAC,YAAW;IAEJ,YAAY;;IAEtB;IACA,IACE,OAAOC,8BAA8B,KAAK,WAAW,IACrD,OAAOA,8BAA8B,CAACC,2BAA2B,KAC/D,UAAU,EACZ;MACAD,8BAA8B,CAACC,2BAA2B,CAAC,IAAIC,KAAK,EAAE,CAAC;IACzE;IACU,IAAIC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC;IAEtC,IAAIC,oBAAoB,GAAGF,KAAK,CAACG,kDAAkD;IAEnF,SAASC,KAAKA,CAACC,MAAM,EAAE;MACrB;QACE;UACE,KAAK,IAAIC,KAAK,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,KAAK,GAAG,CAAC,GAAGA,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEK,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGL,KAAK,EAAEK,KAAK,EAAE,EAAE;YACjHF,IAAI,CAACE,KAAK,GAAG,CAAC,CAAC,GAAGJ,SAAS,CAACI,KAAK,CAAC;UACpC;UAEAC,YAAY,CAAC,OAAO,EAAEP,MAAM,EAAEI,IAAI,CAAC;QACrC;MACF;IACF;IAEA,SAASG,YAAYA,CAACC,KAAK,EAAER,MAAM,EAAEI,IAAI,EAAE;MACzC;MACA;MACA;QACE,IAAIK,sBAAsB,GAAGZ,oBAAoB,CAACY,sBAAsB;QACxE,IAAIC,KAAK,GAAGD,sBAAsB,CAACE,gBAAgB,EAAE;QAErD,IAAID,KAAK,KAAK,EAAE,EAAE;UAChBV,MAAM,IAAI,IAAI;UACdI,IAAI,GAAGA,IAAI,CAACQ,MAAM,CAAC,CAACF,KAAK,CAAC,CAAC;QAC7B,CAAC,CAAC;;QAGF,IAAIG,cAAc,GAAGT,IAAI,CAACU,GAAG,CAAC,UAAUC,IAAI,EAAE;UAC5C,OAAOC,MAAM,CAACD,IAAI,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC;;QAEJF,cAAc,CAACI,OAAO,CAAC,WAAW,GAAGjB,MAAM,CAAC,CAAC,CAAC;QAC9C;QACA;;QAEAkB,QAAQ,CAACC,SAAS,CAACC,KAAK,CAACC,IAAI,CAACC,OAAO,CAACd,KAAK,CAAC,EAAEc,OAAO,EAAET,cAAc,CAAC;MACxE;IACF;;IAEA;AACA;AACA;AACA;IACA,SAASU,EAAEA,CAACC,CAAC,EAAEC,CAAC,EAAE;MAChB,OAAOD,CAAC,KAAKC,CAAC,KAAKD,CAAC,KAAK,CAAC,IAAI,CAAC,GAAGA,CAAC,KAAK,CAAC,GAAGC,CAAC,CAAC,IAAID,CAAC,KAAKA,CAAC,IAAIC,CAAC,KAAKA,CAAC,CAAC;MAAA;IAEvE;;IAEA,IAAIC,QAAQ,GAAG,OAAOC,MAAM,CAACJ,EAAE,KAAK,UAAU,GAAGI,MAAM,CAACJ,EAAE,GAAGA,EAAE;;IAE/D;;IAEA,IAAIK,QAAQ,GAAGjC,KAAK,CAACiC,QAAQ;MACzBC,SAAS,GAAGlC,KAAK,CAACkC,SAAS;MAC3BC,eAAe,GAAGnC,KAAK,CAACmC,eAAe;MACvCC,aAAa,GAAGpC,KAAK,CAACoC,aAAa;IACvC,IAAIC,iBAAiB,GAAG,KAAK;IAC7B,IAAIC,0BAA0B,GAAG,KAAK,CAAC,CAAC;IACxC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA,SAASC,oBAAoBA,CAACC,SAAS,EAAEC,WAAW;IAAE;IACtD;IACA;IACA;IACAC,iBAAiB,EAAE;MACjB;QACE,IAAI,CAACL,iBAAiB,EAAE;UACtB,IAAIrC,KAAK,CAAC2C,eAAe,KAAKC,SAAS,EAAE;YACvCP,iBAAiB,GAAG,IAAI;YAExBjC,KAAK,CAAC,gEAAgE,GAAG,6CAA6C,GAAG,gEAAgE,GAAG,yBAAyB,CAAC;UACxN;QACF;MACF,CAAC,CAAC;MACF;MACA;MACA;;MAGA,IAAIyC,KAAK,GAAGJ,WAAW,EAAE;MAEzB;QACE,IAAI,CAACH,0BAA0B,EAAE;UAC/B,IAAIQ,WAAW,GAAGL,WAAW,EAAE;UAE/B,IAAI,CAACV,QAAQ,CAACc,KAAK,EAAEC,WAAW,CAAC,EAAE;YACjC1C,KAAK,CAAC,sEAAsE,CAAC;YAE7EkC,0BAA0B,GAAG,IAAI;UACnC;QACF;MACF,CAAC,CAAC;MACF;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAGA,IAAIS,SAAS,GAAGd,QAAQ,CAAC;UACvBe,IAAI,EAAE;YACJH,KAAK,EAAEA,KAAK;YACZJ,WAAW,EAAEA;UACf;QACF,CAAC,CAAC;QACEO,IAAI,GAAGD,SAAS,CAAC,CAAC,CAAC,CAACC,IAAI;QACxBC,WAAW,GAAGF,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAChC;MACA;;MAGAZ,eAAe,CAAC,YAAY;QAC1Ba,IAAI,CAACH,KAAK,GAAGA,KAAK;QAClBG,IAAI,CAACP,WAAW,GAAGA,WAAW,CAAC,CAAC;QAChC;QACA;QACA;;QAEA,IAAIS,sBAAsB,CAACF,IAAI,CAAC,EAAE;UAChC;UACAC,WAAW,CAAC;YACVD,IAAI,EAAEA;UACR,CAAC,CAAC;QACJ;MACF,CAAC,EAAE,CAACR,SAAS,EAAEK,KAAK,EAAEJ,WAAW,CAAC,CAAC;MACnCP,SAAS,CAAC,YAAY;QACpB;QACA;QACA,IAAIgB,sBAAsB,CAACF,IAAI,CAAC,EAAE;UAChC;UACAC,WAAW,CAAC;YACVD,IAAI,EAAEA;UACR,CAAC,CAAC;QACJ;QAEA,IAAIG,iBAAiB,GAAG,SAAAA,CAAA,EAAY;UAClC;UACA;UACA;UACA;UACA;UACA;UACA,IAAID,sBAAsB,CAACF,IAAI,CAAC,EAAE;YAChC;YACAC,WAAW,CAAC;cACVD,IAAI,EAAEA;YACR,CAAC,CAAC;UACJ;QACF,CAAC,CAAC,CAAC;;QAGH,OAAOR,SAAS,CAACW,iBAAiB,CAAC;MACrC,CAAC,EAAE,CAACX,SAAS,CAAC,CAAC;MACfJ,aAAa,CAACS,KAAK,CAAC;MACpB,OAAOA,KAAK;IACd;IAEA,SAASK,sBAAsBA,CAACF,IAAI,EAAE;MACpC,IAAII,iBAAiB,GAAGJ,IAAI,CAACP,WAAW;MACxC,IAAIY,SAAS,GAAGL,IAAI,CAACH,KAAK;MAE1B,IAAI;QACF,IAAIS,SAAS,GAAGF,iBAAiB,EAAE;QACnC,OAAO,CAACrB,QAAQ,CAACsB,SAAS,EAAEC,SAAS,CAAC;MACxC,CAAC,CAAC,OAAOlD,KAAK,EAAE;QACd,OAAO,IAAI;MACb;IACF;IAEA,SAASmD,sBAAsBA,CAACf,SAAS,EAAEC,WAAW,EAAEC,iBAAiB,EAAE;MACzE;MACA;MACA;MACA;MACA,OAAOD,WAAW,EAAE;IACtB;IAEA,IAAIe,SAAS,GAAG,CAAC,EAAE,OAAOC,MAAM,KAAK,WAAW,IAAI,OAAOA,MAAM,CAACC,QAAQ,KAAK,WAAW,IAAI,OAAOD,MAAM,CAACC,QAAQ,CAACC,aAAa,KAAK,WAAW,CAAC;IAEnJ,IAAIC,mBAAmB,GAAG,CAACJ,SAAS;IAEpC,IAAIK,IAAI,GAAGD,mBAAmB,GAAGL,sBAAsB,GAAGhB,oBAAoB;IAC9E,IAAIuB,sBAAsB,GAAG9D,KAAK,CAACuC,oBAAoB,KAAKK,SAAS,GAAG5C,KAAK,CAACuC,oBAAoB,GAAGsB,IAAI;IAEzGE,OAAO,CAACxB,oBAAoB,GAAGuB,sBAAsB;IAC3C;IACV,IACE,OAAOjE,8BAA8B,KAAK,WAAW,IACrD,OAAOA,8BAA8B,CAACmE,0BAA0B,KAC9D,UAAU,EACZ;MACAnE,8BAA8B,CAACmE,0BAA0B,CAAC,IAAIjE,KAAK,EAAE,CAAC;IACxE;EAEE,CAAC,GAAG;AACN"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}