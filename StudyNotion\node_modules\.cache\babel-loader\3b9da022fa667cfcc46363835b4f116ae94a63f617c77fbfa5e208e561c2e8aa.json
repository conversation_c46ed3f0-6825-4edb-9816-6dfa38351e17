{"ast": null, "code": "/**\n * @typedef {import('micromark-util-types').Chunk} Chunk\n * @typedef {import('micromark-util-types').Code} Code\n * @typedef {import('micromark-util-types').Encoding} Encoding\n * @typedef {import('micromark-util-types').Value} Value\n */\n\n/**\n * @callback Preprocessor\n * @param {Value} value\n * @param {Encoding | null | undefined} [encoding]\n * @param {boolean | null | undefined} [end=false]\n * @returns {Array<Chunk>}\n */\n\nimport { codes } from 'micromark-util-symbol/codes.js';\nimport { constants } from 'micromark-util-symbol/constants.js';\nconst search = /[\\0\\t\\n\\r]/g;\n\n/**\n * @returns {Preprocessor}\n */\nexport function preprocess() {\n  let column = 1;\n  let buffer = '';\n  /** @type {boolean | undefined} */\n  let start = true;\n  /** @type {boolean | undefined} */\n  let atCarriageReturn;\n  return preprocessor;\n\n  /** @type {Preprocessor} */\n  function preprocessor(value, encoding, end) {\n    /** @type {Array<Chunk>} */\n    const chunks = [];\n    /** @type {RegExpMatchArray | null} */\n    let match;\n    /** @type {number} */\n    let next;\n    /** @type {number} */\n    let startPosition;\n    /** @type {number} */\n    let endPosition;\n    /** @type {Code} */\n    let code;\n\n    // @ts-expect-error `Buffer` does allow an encoding.\n    value = buffer + value.toString(encoding);\n    startPosition = 0;\n    buffer = '';\n    if (start) {\n      // To do: `markdown-rs` actually parses BOMs (byte order mark).\n      if (value.charCodeAt(0) === codes.byteOrderMarker) {\n        startPosition++;\n      }\n      start = undefined;\n    }\n    while (startPosition < value.length) {\n      search.lastIndex = startPosition;\n      match = search.exec(value);\n      endPosition = match && match.index !== undefined ? match.index : value.length;\n      code = value.charCodeAt(endPosition);\n      if (!match) {\n        buffer = value.slice(startPosition);\n        break;\n      }\n      if (code === codes.lf && startPosition === endPosition && atCarriageReturn) {\n        chunks.push(codes.carriageReturnLineFeed);\n        atCarriageReturn = undefined;\n      } else {\n        if (atCarriageReturn) {\n          chunks.push(codes.carriageReturn);\n          atCarriageReturn = undefined;\n        }\n        if (startPosition < endPosition) {\n          chunks.push(value.slice(startPosition, endPosition));\n          column += endPosition - startPosition;\n        }\n        switch (code) {\n          case codes.nul:\n            {\n              chunks.push(codes.replacementCharacter);\n              column++;\n              break;\n            }\n          case codes.ht:\n            {\n              next = Math.ceil(column / constants.tabSize) * constants.tabSize;\n              chunks.push(codes.horizontalTab);\n              while (column++ < next) chunks.push(codes.virtualSpace);\n              break;\n            }\n          case codes.lf:\n            {\n              chunks.push(codes.lineFeed);\n              column = 1;\n              break;\n            }\n          default:\n            {\n              atCarriageReturn = true;\n              column = 1;\n            }\n        }\n      }\n      startPosition = endPosition + 1;\n    }\n    if (end) {\n      if (atCarriageReturn) chunks.push(codes.carriageReturn);\n      if (buffer) chunks.push(buffer);\n      chunks.push(codes.eof);\n    }\n    return chunks;\n  }\n}", "map": {"version": 3, "names": ["codes", "constants", "search", "preprocess", "column", "buffer", "start", "atCarriageReturn", "preprocessor", "value", "encoding", "end", "chunks", "match", "next", "startPosition", "endPosition", "code", "toString", "charCodeAt", "byteOrderMarker", "undefined", "length", "lastIndex", "exec", "index", "slice", "lf", "push", "carriageReturnLineFeed", "carriageReturn", "nul", "replacementCharacter", "ht", "Math", "ceil", "tabSize", "horizontalTab", "virtualSpace", "lineFeed", "eof"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/micromark/dev/lib/preprocess.js"], "sourcesContent": ["/**\n * @typedef {import('micromark-util-types').Chunk} Chunk\n * @typedef {import('micromark-util-types').Code} Code\n * @typedef {import('micromark-util-types').Encoding} Encoding\n * @typedef {import('micromark-util-types').Value} Value\n */\n\n/**\n * @callback Preprocessor\n * @param {Value} value\n * @param {Encoding | null | undefined} [encoding]\n * @param {boolean | null | undefined} [end=false]\n * @returns {Array<Chunk>}\n */\n\nimport {codes} from 'micromark-util-symbol/codes.js'\nimport {constants} from 'micromark-util-symbol/constants.js'\n\nconst search = /[\\0\\t\\n\\r]/g\n\n/**\n * @returns {Preprocessor}\n */\nexport function preprocess() {\n  let column = 1\n  let buffer = ''\n  /** @type {boolean | undefined} */\n  let start = true\n  /** @type {boolean | undefined} */\n  let atCarriageReturn\n\n  return preprocessor\n\n  /** @type {Preprocessor} */\n  function preprocessor(value, encoding, end) {\n    /** @type {Array<Chunk>} */\n    const chunks = []\n    /** @type {RegExpMatchArray | null} */\n    let match\n    /** @type {number} */\n    let next\n    /** @type {number} */\n    let startPosition\n    /** @type {number} */\n    let endPosition\n    /** @type {Code} */\n    let code\n\n    // @ts-expect-error `Buffer` does allow an encoding.\n    value = buffer + value.toString(encoding)\n    startPosition = 0\n    buffer = ''\n\n    if (start) {\n      // To do: `markdown-rs` actually parses BOMs (byte order mark).\n      if (value.charCodeAt(0) === codes.byteOrderMarker) {\n        startPosition++\n      }\n\n      start = undefined\n    }\n\n    while (startPosition < value.length) {\n      search.lastIndex = startPosition\n      match = search.exec(value)\n      endPosition =\n        match && match.index !== undefined ? match.index : value.length\n      code = value.charCodeAt(endPosition)\n\n      if (!match) {\n        buffer = value.slice(startPosition)\n        break\n      }\n\n      if (\n        code === codes.lf &&\n        startPosition === endPosition &&\n        atCarriageReturn\n      ) {\n        chunks.push(codes.carriageReturnLineFeed)\n        atCarriageReturn = undefined\n      } else {\n        if (atCarriageReturn) {\n          chunks.push(codes.carriageReturn)\n          atCarriageReturn = undefined\n        }\n\n        if (startPosition < endPosition) {\n          chunks.push(value.slice(startPosition, endPosition))\n          column += endPosition - startPosition\n        }\n\n        switch (code) {\n          case codes.nul: {\n            chunks.push(codes.replacementCharacter)\n            column++\n\n            break\n          }\n\n          case codes.ht: {\n            next = Math.ceil(column / constants.tabSize) * constants.tabSize\n            chunks.push(codes.horizontalTab)\n            while (column++ < next) chunks.push(codes.virtualSpace)\n\n            break\n          }\n\n          case codes.lf: {\n            chunks.push(codes.lineFeed)\n            column = 1\n\n            break\n          }\n\n          default: {\n            atCarriageReturn = true\n            column = 1\n          }\n        }\n      }\n\n      startPosition = endPosition + 1\n    }\n\n    if (end) {\n      if (atCarriageReturn) chunks.push(codes.carriageReturn)\n      if (buffer) chunks.push(buffer)\n      chunks.push(codes.eof)\n    }\n\n    return chunks\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,KAAK,QAAO,gCAAgC;AACpD,SAAQC,SAAS,QAAO,oCAAoC;AAE5D,MAAMC,MAAM,GAAG,aAAa;;AAE5B;AACA;AACA;AACA,OAAO,SAASC,UAAUA,CAAA,EAAG;EAC3B,IAAIC,MAAM,GAAG,CAAC;EACd,IAAIC,MAAM,GAAG,EAAE;EACf;EACA,IAAIC,KAAK,GAAG,IAAI;EAChB;EACA,IAAIC,gBAAgB;EAEpB,OAAOC,YAAY;;EAEnB;EACA,SAASA,YAAYA,CAACC,KAAK,EAAEC,QAAQ,EAAEC,GAAG,EAAE;IAC1C;IACA,MAAMC,MAAM,GAAG,EAAE;IACjB;IACA,IAAIC,KAAK;IACT;IACA,IAAIC,IAAI;IACR;IACA,IAAIC,aAAa;IACjB;IACA,IAAIC,WAAW;IACf;IACA,IAAIC,IAAI;;IAER;IACAR,KAAK,GAAGJ,MAAM,GAAGI,KAAK,CAACS,QAAQ,CAACR,QAAQ,CAAC;IACzCK,aAAa,GAAG,CAAC;IACjBV,MAAM,GAAG,EAAE;IAEX,IAAIC,KAAK,EAAE;MACT;MACA,IAAIG,KAAK,CAACU,UAAU,CAAC,CAAC,CAAC,KAAKnB,KAAK,CAACoB,eAAe,EAAE;QACjDL,aAAa,EAAE;MACjB;MAEAT,KAAK,GAAGe,SAAS;IACnB;IAEA,OAAON,aAAa,GAAGN,KAAK,CAACa,MAAM,EAAE;MACnCpB,MAAM,CAACqB,SAAS,GAAGR,aAAa;MAChCF,KAAK,GAAGX,MAAM,CAACsB,IAAI,CAACf,KAAK,CAAC;MAC1BO,WAAW,GACTH,KAAK,IAAIA,KAAK,CAACY,KAAK,KAAKJ,SAAS,GAAGR,KAAK,CAACY,KAAK,GAAGhB,KAAK,CAACa,MAAM;MACjEL,IAAI,GAAGR,KAAK,CAACU,UAAU,CAACH,WAAW,CAAC;MAEpC,IAAI,CAACH,KAAK,EAAE;QACVR,MAAM,GAAGI,KAAK,CAACiB,KAAK,CAACX,aAAa,CAAC;QACnC;MACF;MAEA,IACEE,IAAI,KAAKjB,KAAK,CAAC2B,EAAE,IACjBZ,aAAa,KAAKC,WAAW,IAC7BT,gBAAgB,EAChB;QACAK,MAAM,CAACgB,IAAI,CAAC5B,KAAK,CAAC6B,sBAAsB,CAAC;QACzCtB,gBAAgB,GAAGc,SAAS;MAC9B,CAAC,MAAM;QACL,IAAId,gBAAgB,EAAE;UACpBK,MAAM,CAACgB,IAAI,CAAC5B,KAAK,CAAC8B,cAAc,CAAC;UACjCvB,gBAAgB,GAAGc,SAAS;QAC9B;QAEA,IAAIN,aAAa,GAAGC,WAAW,EAAE;UAC/BJ,MAAM,CAACgB,IAAI,CAACnB,KAAK,CAACiB,KAAK,CAACX,aAAa,EAAEC,WAAW,CAAC,CAAC;UACpDZ,MAAM,IAAIY,WAAW,GAAGD,aAAa;QACvC;QAEA,QAAQE,IAAI;UACV,KAAKjB,KAAK,CAAC+B,GAAG;YAAE;cACdnB,MAAM,CAACgB,IAAI,CAAC5B,KAAK,CAACgC,oBAAoB,CAAC;cACvC5B,MAAM,EAAE;cAER;YACF;UAEA,KAAKJ,KAAK,CAACiC,EAAE;YAAE;cACbnB,IAAI,GAAGoB,IAAI,CAACC,IAAI,CAAC/B,MAAM,GAAGH,SAAS,CAACmC,OAAO,CAAC,GAAGnC,SAAS,CAACmC,OAAO;cAChExB,MAAM,CAACgB,IAAI,CAAC5B,KAAK,CAACqC,aAAa,CAAC;cAChC,OAAOjC,MAAM,EAAE,GAAGU,IAAI,EAAEF,MAAM,CAACgB,IAAI,CAAC5B,KAAK,CAACsC,YAAY,CAAC;cAEvD;YACF;UAEA,KAAKtC,KAAK,CAAC2B,EAAE;YAAE;cACbf,MAAM,CAACgB,IAAI,CAAC5B,KAAK,CAACuC,QAAQ,CAAC;cAC3BnC,MAAM,GAAG,CAAC;cAEV;YACF;UAEA;YAAS;cACPG,gBAAgB,GAAG,IAAI;cACvBH,MAAM,GAAG,CAAC;YACZ;QAAC;MAEL;MAEAW,aAAa,GAAGC,WAAW,GAAG,CAAC;IACjC;IAEA,IAAIL,GAAG,EAAE;MACP,IAAIJ,gBAAgB,EAAEK,MAAM,CAACgB,IAAI,CAAC5B,KAAK,CAAC8B,cAAc,CAAC;MACvD,IAAIzB,MAAM,EAAEO,MAAM,CAACgB,IAAI,CAACvB,MAAM,CAAC;MAC/BO,MAAM,CAACgB,IAAI,CAAC5B,KAAK,CAACwC,GAAG,CAAC;IACxB;IAEA,OAAO5B,MAAM;EACf;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}