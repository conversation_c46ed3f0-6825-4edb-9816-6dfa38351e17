{"ast": null, "code": "import { elementChildren } from '../../shared/utils.js';\nexport default function Parallax(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on\n  } = _ref;\n  extendParams({\n    parallax: {\n      enabled: false\n    }\n  });\n  const setTransform = (el, progress) => {\n    const {\n      rtl\n    } = swiper;\n    const rtlFactor = rtl ? -1 : 1;\n    const p = el.getAttribute('data-swiper-parallax') || '0';\n    let x = el.getAttribute('data-swiper-parallax-x');\n    let y = el.getAttribute('data-swiper-parallax-y');\n    const scale = el.getAttribute('data-swiper-parallax-scale');\n    const opacity = el.getAttribute('data-swiper-parallax-opacity');\n    const rotate = el.getAttribute('data-swiper-parallax-rotate');\n    if (x || y) {\n      x = x || '0';\n      y = y || '0';\n    } else if (swiper.isHorizontal()) {\n      x = p;\n      y = '0';\n    } else {\n      y = p;\n      x = '0';\n    }\n    if (x.indexOf('%') >= 0) {\n      x = `${parseInt(x, 10) * progress * rtlFactor}%`;\n    } else {\n      x = `${x * progress * rtlFactor}px`;\n    }\n    if (y.indexOf('%') >= 0) {\n      y = `${parseInt(y, 10) * progress}%`;\n    } else {\n      y = `${y * progress}px`;\n    }\n    if (typeof opacity !== 'undefined' && opacity !== null) {\n      const currentOpacity = opacity - (opacity - 1) * (1 - Math.abs(progress));\n      el.style.opacity = currentOpacity;\n    }\n    let transform = `translate3d(${x}, ${y}, 0px)`;\n    if (typeof scale !== 'undefined' && scale !== null) {\n      const currentScale = scale - (scale - 1) * (1 - Math.abs(progress));\n      transform += ` scale(${currentScale})`;\n    }\n    if (rotate && typeof rotate !== 'undefined' && rotate !== null) {\n      const currentRotate = rotate * progress * -1;\n      transform += ` rotate(${currentRotate}deg)`;\n    }\n    el.style.transform = transform;\n  };\n  const setTranslate = () => {\n    const {\n      el,\n      slides,\n      progress,\n      snapGrid\n    } = swiper;\n    elementChildren(el, '[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y], [data-swiper-parallax-opacity], [data-swiper-parallax-scale]').forEach(subEl => {\n      setTransform(subEl, progress);\n    });\n    slides.forEach((slideEl, slideIndex) => {\n      let slideProgress = slideEl.progress;\n      if (swiper.params.slidesPerGroup > 1 && swiper.params.slidesPerView !== 'auto') {\n        slideProgress += Math.ceil(slideIndex / 2) - progress * (snapGrid.length - 1);\n      }\n      slideProgress = Math.min(Math.max(slideProgress, -1), 1);\n      slideEl.querySelectorAll('[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y], [data-swiper-parallax-opacity], [data-swiper-parallax-scale], [data-swiper-parallax-rotate]').forEach(subEl => {\n        setTransform(subEl, slideProgress);\n      });\n    });\n  };\n  const setTransition = function () {\n    let duration = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : swiper.params.speed;\n    const {\n      el\n    } = swiper;\n    el.querySelectorAll('[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y], [data-swiper-parallax-opacity], [data-swiper-parallax-scale]').forEach(parallaxEl => {\n      let parallaxDuration = parseInt(parallaxEl.getAttribute('data-swiper-parallax-duration'), 10) || duration;\n      if (duration === 0) parallaxDuration = 0;\n      parallaxEl.style.transitionDuration = `${parallaxDuration}ms`;\n    });\n  };\n  on('beforeInit', () => {\n    if (!swiper.params.parallax.enabled) return;\n    swiper.params.watchSlidesProgress = true;\n    swiper.originalParams.watchSlidesProgress = true;\n  });\n  on('init', () => {\n    if (!swiper.params.parallax.enabled) return;\n    setTranslate();\n  });\n  on('setTranslate', () => {\n    if (!swiper.params.parallax.enabled) return;\n    setTranslate();\n  });\n  on('setTransition', (_swiper, duration) => {\n    if (!swiper.params.parallax.enabled) return;\n    setTransition(duration);\n  });\n}", "map": {"version": 3, "names": ["elementChildren", "Parallax", "_ref", "swiper", "extendParams", "on", "parallax", "enabled", "setTransform", "el", "progress", "rtl", "rtlFactor", "p", "getAttribute", "x", "y", "scale", "opacity", "rotate", "isHorizontal", "indexOf", "parseInt", "currentOpacity", "Math", "abs", "style", "transform", "currentScale", "currentRotate", "setTranslate", "slides", "snapGrid", "for<PERSON>ach", "subEl", "slideEl", "slideIndex", "slideProgress", "params", "slidesPerGroup", "<PERSON><PERSON><PERSON><PERSON>iew", "ceil", "length", "min", "max", "querySelectorAll", "setTransition", "duration", "arguments", "undefined", "speed", "parallaxEl", "parallaxDuration", "transitionDuration", "watchSlidesProgress", "originalParams", "_swiper"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/modules/parallax/parallax.js"], "sourcesContent": ["import { elementChildren } from '../../shared/utils.js';\nexport default function Parallax({\n  swiper,\n  extendParams,\n  on\n}) {\n  extendParams({\n    parallax: {\n      enabled: false\n    }\n  });\n  const setTransform = (el, progress) => {\n    const {\n      rtl\n    } = swiper;\n    const rtlFactor = rtl ? -1 : 1;\n    const p = el.getAttribute('data-swiper-parallax') || '0';\n    let x = el.getAttribute('data-swiper-parallax-x');\n    let y = el.getAttribute('data-swiper-parallax-y');\n    const scale = el.getAttribute('data-swiper-parallax-scale');\n    const opacity = el.getAttribute('data-swiper-parallax-opacity');\n    const rotate = el.getAttribute('data-swiper-parallax-rotate');\n    if (x || y) {\n      x = x || '0';\n      y = y || '0';\n    } else if (swiper.isHorizontal()) {\n      x = p;\n      y = '0';\n    } else {\n      y = p;\n      x = '0';\n    }\n    if (x.indexOf('%') >= 0) {\n      x = `${parseInt(x, 10) * progress * rtlFactor}%`;\n    } else {\n      x = `${x * progress * rtlFactor}px`;\n    }\n    if (y.indexOf('%') >= 0) {\n      y = `${parseInt(y, 10) * progress}%`;\n    } else {\n      y = `${y * progress}px`;\n    }\n    if (typeof opacity !== 'undefined' && opacity !== null) {\n      const currentOpacity = opacity - (opacity - 1) * (1 - Math.abs(progress));\n      el.style.opacity = currentOpacity;\n    }\n    let transform = `translate3d(${x}, ${y}, 0px)`;\n    if (typeof scale !== 'undefined' && scale !== null) {\n      const currentScale = scale - (scale - 1) * (1 - Math.abs(progress));\n      transform += ` scale(${currentScale})`;\n    }\n    if (rotate && typeof rotate !== 'undefined' && rotate !== null) {\n      const currentRotate = rotate * progress * -1;\n      transform += ` rotate(${currentRotate}deg)`;\n    }\n    el.style.transform = transform;\n  };\n  const setTranslate = () => {\n    const {\n      el,\n      slides,\n      progress,\n      snapGrid\n    } = swiper;\n    elementChildren(el, '[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y], [data-swiper-parallax-opacity], [data-swiper-parallax-scale]').forEach(subEl => {\n      setTransform(subEl, progress);\n    });\n    slides.forEach((slideEl, slideIndex) => {\n      let slideProgress = slideEl.progress;\n      if (swiper.params.slidesPerGroup > 1 && swiper.params.slidesPerView !== 'auto') {\n        slideProgress += Math.ceil(slideIndex / 2) - progress * (snapGrid.length - 1);\n      }\n      slideProgress = Math.min(Math.max(slideProgress, -1), 1);\n      slideEl.querySelectorAll('[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y], [data-swiper-parallax-opacity], [data-swiper-parallax-scale], [data-swiper-parallax-rotate]').forEach(subEl => {\n        setTransform(subEl, slideProgress);\n      });\n    });\n  };\n  const setTransition = (duration = swiper.params.speed) => {\n    const {\n      el\n    } = swiper;\n    el.querySelectorAll('[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y], [data-swiper-parallax-opacity], [data-swiper-parallax-scale]').forEach(parallaxEl => {\n      let parallaxDuration = parseInt(parallaxEl.getAttribute('data-swiper-parallax-duration'), 10) || duration;\n      if (duration === 0) parallaxDuration = 0;\n      parallaxEl.style.transitionDuration = `${parallaxDuration}ms`;\n    });\n  };\n  on('beforeInit', () => {\n    if (!swiper.params.parallax.enabled) return;\n    swiper.params.watchSlidesProgress = true;\n    swiper.originalParams.watchSlidesProgress = true;\n  });\n  on('init', () => {\n    if (!swiper.params.parallax.enabled) return;\n    setTranslate();\n  });\n  on('setTranslate', () => {\n    if (!swiper.params.parallax.enabled) return;\n    setTranslate();\n  });\n  on('setTransition', (_swiper, duration) => {\n    if (!swiper.params.parallax.enabled) return;\n    setTransition(duration);\n  });\n}"], "mappings": "AAAA,SAASA,eAAe,QAAQ,uBAAuB;AACvD,eAAe,SAASC,QAAQA,CAAAC,IAAA,EAI7B;EAAA,IAJ8B;IAC/BC,MAAM;IACNC,YAAY;IACZC;EACF,CAAC,GAAAH,IAAA;EACCE,YAAY,CAAC;IACXE,QAAQ,EAAE;MACRC,OAAO,EAAE;IACX;EACF,CAAC,CAAC;EACF,MAAMC,YAAY,GAAGA,CAACC,EAAE,EAAEC,QAAQ,KAAK;IACrC,MAAM;MACJC;IACF,CAAC,GAAGR,MAAM;IACV,MAAMS,SAAS,GAAGD,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC;IAC9B,MAAME,CAAC,GAAGJ,EAAE,CAACK,YAAY,CAAC,sBAAsB,CAAC,IAAI,GAAG;IACxD,IAAIC,CAAC,GAAGN,EAAE,CAACK,YAAY,CAAC,wBAAwB,CAAC;IACjD,IAAIE,CAAC,GAAGP,EAAE,CAACK,YAAY,CAAC,wBAAwB,CAAC;IACjD,MAAMG,KAAK,GAAGR,EAAE,CAACK,YAAY,CAAC,4BAA4B,CAAC;IAC3D,MAAMI,OAAO,GAAGT,EAAE,CAACK,YAAY,CAAC,8BAA8B,CAAC;IAC/D,MAAMK,MAAM,GAAGV,EAAE,CAACK,YAAY,CAAC,6BAA6B,CAAC;IAC7D,IAAIC,CAAC,IAAIC,CAAC,EAAE;MACVD,CAAC,GAAGA,CAAC,IAAI,GAAG;MACZC,CAAC,GAAGA,CAAC,IAAI,GAAG;IACd,CAAC,MAAM,IAAIb,MAAM,CAACiB,YAAY,EAAE,EAAE;MAChCL,CAAC,GAAGF,CAAC;MACLG,CAAC,GAAG,GAAG;IACT,CAAC,MAAM;MACLA,CAAC,GAAGH,CAAC;MACLE,CAAC,GAAG,GAAG;IACT;IACA,IAAIA,CAAC,CAACM,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;MACvBN,CAAC,GAAI,GAAEO,QAAQ,CAACP,CAAC,EAAE,EAAE,CAAC,GAAGL,QAAQ,GAAGE,SAAU,GAAE;IAClD,CAAC,MAAM;MACLG,CAAC,GAAI,GAAEA,CAAC,GAAGL,QAAQ,GAAGE,SAAU,IAAG;IACrC;IACA,IAAII,CAAC,CAACK,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;MACvBL,CAAC,GAAI,GAAEM,QAAQ,CAACN,CAAC,EAAE,EAAE,CAAC,GAAGN,QAAS,GAAE;IACtC,CAAC,MAAM;MACLM,CAAC,GAAI,GAAEA,CAAC,GAAGN,QAAS,IAAG;IACzB;IACA,IAAI,OAAOQ,OAAO,KAAK,WAAW,IAAIA,OAAO,KAAK,IAAI,EAAE;MACtD,MAAMK,cAAc,GAAGL,OAAO,GAAG,CAACA,OAAO,GAAG,CAAC,KAAK,CAAC,GAAGM,IAAI,CAACC,GAAG,CAACf,QAAQ,CAAC,CAAC;MACzED,EAAE,CAACiB,KAAK,CAACR,OAAO,GAAGK,cAAc;IACnC;IACA,IAAII,SAAS,GAAI,eAAcZ,CAAE,KAAIC,CAAE,QAAO;IAC9C,IAAI,OAAOC,KAAK,KAAK,WAAW,IAAIA,KAAK,KAAK,IAAI,EAAE;MAClD,MAAMW,YAAY,GAAGX,KAAK,GAAG,CAACA,KAAK,GAAG,CAAC,KAAK,CAAC,GAAGO,IAAI,CAACC,GAAG,CAACf,QAAQ,CAAC,CAAC;MACnEiB,SAAS,IAAK,UAASC,YAAa,GAAE;IACxC;IACA,IAAIT,MAAM,IAAI,OAAOA,MAAM,KAAK,WAAW,IAAIA,MAAM,KAAK,IAAI,EAAE;MAC9D,MAAMU,aAAa,GAAGV,MAAM,GAAGT,QAAQ,GAAG,CAAC,CAAC;MAC5CiB,SAAS,IAAK,WAAUE,aAAc,MAAK;IAC7C;IACApB,EAAE,CAACiB,KAAK,CAACC,SAAS,GAAGA,SAAS;EAChC,CAAC;EACD,MAAMG,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAM;MACJrB,EAAE;MACFsB,MAAM;MACNrB,QAAQ;MACRsB;IACF,CAAC,GAAG7B,MAAM;IACVH,eAAe,CAACS,EAAE,EAAE,0IAA0I,CAAC,CAACwB,OAAO,CAACC,KAAK,IAAI;MAC/K1B,YAAY,CAAC0B,KAAK,EAAExB,QAAQ,CAAC;IAC/B,CAAC,CAAC;IACFqB,MAAM,CAACE,OAAO,CAAC,CAACE,OAAO,EAAEC,UAAU,KAAK;MACtC,IAAIC,aAAa,GAAGF,OAAO,CAACzB,QAAQ;MACpC,IAAIP,MAAM,CAACmC,MAAM,CAACC,cAAc,GAAG,CAAC,IAAIpC,MAAM,CAACmC,MAAM,CAACE,aAAa,KAAK,MAAM,EAAE;QAC9EH,aAAa,IAAIb,IAAI,CAACiB,IAAI,CAACL,UAAU,GAAG,CAAC,CAAC,GAAG1B,QAAQ,IAAIsB,QAAQ,CAACU,MAAM,GAAG,CAAC,CAAC;MAC/E;MACAL,aAAa,GAAGb,IAAI,CAACmB,GAAG,CAACnB,IAAI,CAACoB,GAAG,CAACP,aAAa,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACxDF,OAAO,CAACU,gBAAgB,CAAC,yKAAyK,CAAC,CAACZ,OAAO,CAACC,KAAK,IAAI;QACnN1B,YAAY,CAAC0B,KAAK,EAAEG,aAAa,CAAC;MACpC,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EACD,MAAMS,aAAa,GAAG,SAAAA,CAAA,EAAoC;IAAA,IAAnCC,QAAQ,GAAAC,SAAA,CAAAN,MAAA,QAAAM,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG7C,MAAM,CAACmC,MAAM,CAACY,KAAK;IACnD,MAAM;MACJzC;IACF,CAAC,GAAGN,MAAM;IACVM,EAAE,CAACoC,gBAAgB,CAAC,0IAA0I,CAAC,CAACZ,OAAO,CAACkB,UAAU,IAAI;MACpL,IAAIC,gBAAgB,GAAG9B,QAAQ,CAAC6B,UAAU,CAACrC,YAAY,CAAC,+BAA+B,CAAC,EAAE,EAAE,CAAC,IAAIiC,QAAQ;MACzG,IAAIA,QAAQ,KAAK,CAAC,EAAEK,gBAAgB,GAAG,CAAC;MACxCD,UAAU,CAACzB,KAAK,CAAC2B,kBAAkB,GAAI,GAAED,gBAAiB,IAAG;IAC/D,CAAC,CAAC;EACJ,CAAC;EACD/C,EAAE,CAAC,YAAY,EAAE,MAAM;IACrB,IAAI,CAACF,MAAM,CAACmC,MAAM,CAAChC,QAAQ,CAACC,OAAO,EAAE;IACrCJ,MAAM,CAACmC,MAAM,CAACgB,mBAAmB,GAAG,IAAI;IACxCnD,MAAM,CAACoD,cAAc,CAACD,mBAAmB,GAAG,IAAI;EAClD,CAAC,CAAC;EACFjD,EAAE,CAAC,MAAM,EAAE,MAAM;IACf,IAAI,CAACF,MAAM,CAACmC,MAAM,CAAChC,QAAQ,CAACC,OAAO,EAAE;IACrCuB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFzB,EAAE,CAAC,cAAc,EAAE,MAAM;IACvB,IAAI,CAACF,MAAM,CAACmC,MAAM,CAAChC,QAAQ,CAACC,OAAO,EAAE;IACrCuB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFzB,EAAE,CAAC,eAAe,EAAE,CAACmD,OAAO,EAAET,QAAQ,KAAK;IACzC,IAAI,CAAC5C,MAAM,CAACmC,MAAM,CAAChC,QAAQ,CAACC,OAAO,EAAE;IACrCuC,aAAa,CAACC,QAAQ,CAAC;EACzB,CAAC,CAAC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}