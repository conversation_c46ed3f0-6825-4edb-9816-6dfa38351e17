{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = PlayProgressBar;\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _react = _interopRequireDefault(require(\"react\"));\nvar _classnames = _interopRequireDefault(require(\"classnames\"));\nvar _utils = require(\"../../utils\");\nvar propTypes = {\n  currentTime: _propTypes[\"default\"].number,\n  duration: _propTypes[\"default\"].number,\n  percentage: _propTypes[\"default\"].string,\n  className: _propTypes[\"default\"].string\n}; // Shows play progress\n\nfunction PlayProgressBar(_ref) {\n  var currentTime = _ref.currentTime,\n    duration = _ref.duration,\n    percentage = _ref.percentage,\n    className = _ref.className;\n  return _react[\"default\"].createElement(\"div\", {\n    \"data-current-time\": (0, _utils.formatTime)(currentTime, duration),\n    className: (0, _classnames[\"default\"])('video-react-play-progress video-react-slider-bar', className),\n    style: {\n      width: percentage\n    }\n  }, _react[\"default\"].createElement(\"span\", {\n    className: \"video-react-control-text\"\n  }, \"Progress: \".concat(percentage)));\n}\nPlayProgressBar.propTypes = propTypes;\nPlayProgressBar.displayName = 'PlayProgressBar';", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "PlayProgressBar", "_propTypes", "_react", "_classnames", "_utils", "propTypes", "currentTime", "number", "duration", "percentage", "string", "className", "_ref", "createElement", "formatTime", "style", "width", "concat", "displayName"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/video-react/lib/components/control-bar/PlayProgressBar.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = PlayProgressBar;\n\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\n\nvar _react = _interopRequireDefault(require(\"react\"));\n\nvar _classnames = _interopRequireDefault(require(\"classnames\"));\n\nvar _utils = require(\"../../utils\");\n\nvar propTypes = {\n  currentTime: _propTypes[\"default\"].number,\n  duration: _propTypes[\"default\"].number,\n  percentage: _propTypes[\"default\"].string,\n  className: _propTypes[\"default\"].string\n}; // Shows play progress\n\nfunction PlayProgressBar(_ref) {\n  var currentTime = _ref.currentTime,\n      duration = _ref.duration,\n      percentage = _ref.percentage,\n      className = _ref.className;\n  return _react[\"default\"].createElement(\"div\", {\n    \"data-current-time\": (0, _utils.formatTime)(currentTime, duration),\n    className: (0, _classnames[\"default\"])('video-react-play-progress video-react-slider-bar', className),\n    style: {\n      width: percentage\n    }\n  }, _react[\"default\"].createElement(\"span\", {\n    className: \"video-react-control-text\"\n  }, \"Progress: \".concat(percentage)));\n}\n\nPlayProgressBar.propTypes = propTypes;\nPlayProgressBar.displayName = 'PlayProgressBar';"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC;AAEpFC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAAC,SAAS,CAAC,GAAGE,eAAe;AAEpC,IAAIC,UAAU,GAAGP,sBAAsB,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;AAE9D,IAAIO,MAAM,GAAGR,sBAAsB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AAErD,IAAIQ,WAAW,GAAGT,sBAAsB,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;AAE/D,IAAIS,MAAM,GAAGT,OAAO,CAAC,aAAa,CAAC;AAEnC,IAAIU,SAAS,GAAG;EACdC,WAAW,EAAEL,UAAU,CAAC,SAAS,CAAC,CAACM,MAAM;EACzCC,QAAQ,EAAEP,UAAU,CAAC,SAAS,CAAC,CAACM,MAAM;EACtCE,UAAU,EAAER,UAAU,CAAC,SAAS,CAAC,CAACS,MAAM;EACxCC,SAAS,EAAEV,UAAU,CAAC,SAAS,CAAC,CAACS;AACnC,CAAC,CAAC,CAAC;;AAEH,SAASV,eAAeA,CAACY,IAAI,EAAE;EAC7B,IAAIN,WAAW,GAAGM,IAAI,CAACN,WAAW;IAC9BE,QAAQ,GAAGI,IAAI,CAACJ,QAAQ;IACxBC,UAAU,GAAGG,IAAI,CAACH,UAAU;IAC5BE,SAAS,GAAGC,IAAI,CAACD,SAAS;EAC9B,OAAOT,MAAM,CAAC,SAAS,CAAC,CAACW,aAAa,CAAC,KAAK,EAAE;IAC5C,mBAAmB,EAAE,CAAC,CAAC,EAAET,MAAM,CAACU,UAAU,EAAER,WAAW,EAAEE,QAAQ,CAAC;IAClEG,SAAS,EAAE,CAAC,CAAC,EAAER,WAAW,CAAC,SAAS,CAAC,EAAE,kDAAkD,EAAEQ,SAAS,CAAC;IACrGI,KAAK,EAAE;MACLC,KAAK,EAAEP;IACT;EACF,CAAC,EAAEP,MAAM,CAAC,SAAS,CAAC,CAACW,aAAa,CAAC,MAAM,EAAE;IACzCF,SAAS,EAAE;EACb,CAAC,EAAE,YAAY,CAACM,MAAM,CAACR,UAAU,CAAC,CAAC,CAAC;AACtC;AAEAT,eAAe,CAACK,SAAS,GAAGA,SAAS;AACrCL,eAAe,CAACkB,WAAW,GAAG,iBAAiB"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}