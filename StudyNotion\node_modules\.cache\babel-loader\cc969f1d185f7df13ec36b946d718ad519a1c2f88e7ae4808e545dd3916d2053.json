{"ast": null, "code": "function prepareClasses(entries, prefix) {\n  const resultClasses = [];\n  entries.forEach(item => {\n    if (typeof item === 'object') {\n      Object.keys(item).forEach(classNames => {\n        if (item[classNames]) {\n          resultClasses.push(prefix + classNames);\n        }\n      });\n    } else if (typeof item === 'string') {\n      resultClasses.push(prefix + item);\n    }\n  });\n  return resultClasses;\n}\nexport default function addClasses() {\n  const swiper = this;\n  const {\n    classNames,\n    params,\n    rtl,\n    el,\n    device\n  } = swiper;\n  // prettier-ignore\n  const suffixes = prepareClasses(['initialized', params.direction, {\n    'free-mode': swiper.params.freeMode && params.freeMode.enabled\n  }, {\n    'autoheight': params.autoHeight\n  }, {\n    'rtl': rtl\n  }, {\n    'grid': params.grid && params.grid.rows > 1\n  }, {\n    'grid-column': params.grid && params.grid.rows > 1 && params.grid.fill === 'column'\n  }, {\n    'android': device.android\n  }, {\n    'ios': device.ios\n  }, {\n    'css-mode': params.cssMode\n  }, {\n    'centered': params.cssMode && params.centeredSlides\n  }, {\n    'watch-progress': params.watchSlidesProgress\n  }], params.containerModifierClass);\n  classNames.push(...suffixes);\n  el.classList.add(...classNames);\n  swiper.emitContainerClasses();\n}", "map": {"version": 3, "names": ["prepareClasses", "entries", "prefix", "resultClasses", "for<PERSON>ach", "item", "Object", "keys", "classNames", "push", "addClasses", "swiper", "params", "rtl", "el", "device", "suffixes", "direction", "freeMode", "enabled", "autoHeight", "grid", "rows", "fill", "android", "ios", "cssMode", "centeredSlides", "watchSlidesProgress", "containerModifierClass", "classList", "add", "emitContainerClasses"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/core/classes/addClasses.js"], "sourcesContent": ["function prepareClasses(entries, prefix) {\n  const resultClasses = [];\n  entries.forEach(item => {\n    if (typeof item === 'object') {\n      Object.keys(item).forEach(classNames => {\n        if (item[classNames]) {\n          resultClasses.push(prefix + classNames);\n        }\n      });\n    } else if (typeof item === 'string') {\n      resultClasses.push(prefix + item);\n    }\n  });\n  return resultClasses;\n}\nexport default function addClasses() {\n  const swiper = this;\n  const {\n    classNames,\n    params,\n    rtl,\n    el,\n    device\n  } = swiper;\n  // prettier-ignore\n  const suffixes = prepareClasses(['initialized', params.direction, {\n    'free-mode': swiper.params.freeMode && params.freeMode.enabled\n  }, {\n    'autoheight': params.autoHeight\n  }, {\n    'rtl': rtl\n  }, {\n    'grid': params.grid && params.grid.rows > 1\n  }, {\n    'grid-column': params.grid && params.grid.rows > 1 && params.grid.fill === 'column'\n  }, {\n    'android': device.android\n  }, {\n    'ios': device.ios\n  }, {\n    'css-mode': params.cssMode\n  }, {\n    'centered': params.cssMode && params.centeredSlides\n  }, {\n    'watch-progress': params.watchSlidesProgress\n  }], params.containerModifierClass);\n  classNames.push(...suffixes);\n  el.classList.add(...classNames);\n  swiper.emitContainerClasses();\n}"], "mappings": "AAAA,SAASA,cAAcA,CAACC,OAAO,EAAEC,MAAM,EAAE;EACvC,MAAMC,aAAa,GAAG,EAAE;EACxBF,OAAO,CAACG,OAAO,CAACC,IAAI,IAAI;IACtB,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;MAC5BC,MAAM,CAACC,IAAI,CAACF,IAAI,CAAC,CAACD,OAAO,CAACI,UAAU,IAAI;QACtC,IAAIH,IAAI,CAACG,UAAU,CAAC,EAAE;UACpBL,aAAa,CAACM,IAAI,CAACP,MAAM,GAAGM,UAAU,CAAC;QACzC;MACF,CAAC,CAAC;IACJ,CAAC,MAAM,IAAI,OAAOH,IAAI,KAAK,QAAQ,EAAE;MACnCF,aAAa,CAACM,IAAI,CAACP,MAAM,GAAGG,IAAI,CAAC;IACnC;EACF,CAAC,CAAC;EACF,OAAOF,aAAa;AACtB;AACA,eAAe,SAASO,UAAUA,CAAA,EAAG;EACnC,MAAMC,MAAM,GAAG,IAAI;EACnB,MAAM;IACJH,UAAU;IACVI,MAAM;IACNC,GAAG;IACHC,EAAE;IACFC;EACF,CAAC,GAAGJ,MAAM;EACV;EACA,MAAMK,QAAQ,GAAGhB,cAAc,CAAC,CAAC,aAAa,EAAEY,MAAM,CAACK,SAAS,EAAE;IAChE,WAAW,EAAEN,MAAM,CAACC,MAAM,CAACM,QAAQ,IAAIN,MAAM,CAACM,QAAQ,CAACC;EACzD,CAAC,EAAE;IACD,YAAY,EAAEP,MAAM,CAACQ;EACvB,CAAC,EAAE;IACD,KAAK,EAAEP;EACT,CAAC,EAAE;IACD,MAAM,EAAED,MAAM,CAACS,IAAI,IAAIT,MAAM,CAACS,IAAI,CAACC,IAAI,GAAG;EAC5C,CAAC,EAAE;IACD,aAAa,EAAEV,MAAM,CAACS,IAAI,IAAIT,MAAM,CAACS,IAAI,CAACC,IAAI,GAAG,CAAC,IAAIV,MAAM,CAACS,IAAI,CAACE,IAAI,KAAK;EAC7E,CAAC,EAAE;IACD,SAAS,EAAER,MAAM,CAACS;EACpB,CAAC,EAAE;IACD,KAAK,EAAET,MAAM,CAACU;EAChB,CAAC,EAAE;IACD,UAAU,EAAEb,MAAM,CAACc;EACrB,CAAC,EAAE;IACD,UAAU,EAAEd,MAAM,CAACc,OAAO,IAAId,MAAM,CAACe;EACvC,CAAC,EAAE;IACD,gBAAgB,EAAEf,MAAM,CAACgB;EAC3B,CAAC,CAAC,EAAEhB,MAAM,CAACiB,sBAAsB,CAAC;EAClCrB,UAAU,CAACC,IAAI,CAAC,GAAGO,QAAQ,CAAC;EAC5BF,EAAE,CAACgB,SAAS,CAACC,GAAG,CAAC,GAAGvB,UAAU,CAAC;EAC/BG,MAAM,CAACqB,oBAAoB,EAAE;AAC/B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}