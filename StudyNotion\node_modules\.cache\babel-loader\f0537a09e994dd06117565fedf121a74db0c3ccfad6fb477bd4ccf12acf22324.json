{"ast": null, "code": "export default function appendSlide(slides) {\n  const swiper = this;\n  const {\n    params,\n    slidesEl\n  } = swiper;\n  if (params.loop) {\n    swiper.loopDestroy();\n  }\n  const appendElement = slideEl => {\n    if (typeof slideEl === 'string') {\n      const tempDOM = document.createElement('div');\n      tempDOM.innerHTML = slideEl;\n      slidesEl.append(tempDOM.children[0]);\n      tempDOM.innerHTML = '';\n    } else {\n      slidesEl.append(slideEl);\n    }\n  };\n  if (typeof slides === 'object' && 'length' in slides) {\n    for (let i = 0; i < slides.length; i += 1) {\n      if (slides[i]) appendElement(slides[i]);\n    }\n  } else {\n    appendElement(slides);\n  }\n  swiper.recalcSlides();\n  if (params.loop) {\n    swiper.loopCreate();\n  }\n  if (!params.observer || swiper.isElement) {\n    swiper.update();\n  }\n}", "map": {"version": 3, "names": ["appendSlide", "slides", "swiper", "params", "slidesEl", "loop", "loop<PERSON><PERSON><PERSON>", "appendElement", "slideEl", "tempDOM", "document", "createElement", "innerHTML", "append", "children", "i", "length", "recalcSlides", "loopCreate", "observer", "isElement", "update"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/modules/manipulation/methods/appendSlide.js"], "sourcesContent": ["export default function appendSlide(slides) {\n  const swiper = this;\n  const {\n    params,\n    slidesEl\n  } = swiper;\n  if (params.loop) {\n    swiper.loopDestroy();\n  }\n  const appendElement = slideEl => {\n    if (typeof slideEl === 'string') {\n      const tempDOM = document.createElement('div');\n      tempDOM.innerHTML = slideEl;\n      slidesEl.append(tempDOM.children[0]);\n      tempDOM.innerHTML = '';\n    } else {\n      slidesEl.append(slideEl);\n    }\n  };\n  if (typeof slides === 'object' && 'length' in slides) {\n    for (let i = 0; i < slides.length; i += 1) {\n      if (slides[i]) appendElement(slides[i]);\n    }\n  } else {\n    appendElement(slides);\n  }\n  swiper.recalcSlides();\n  if (params.loop) {\n    swiper.loopCreate();\n  }\n  if (!params.observer || swiper.isElement) {\n    swiper.update();\n  }\n}"], "mappings": "AAAA,eAAe,SAASA,WAAWA,CAACC,MAAM,EAAE;EAC1C,MAAMC,MAAM,GAAG,IAAI;EACnB,MAAM;IACJC,MAAM;IACNC;EACF,CAAC,GAAGF,MAAM;EACV,IAAIC,MAAM,CAACE,IAAI,EAAE;IACfH,MAAM,CAACI,WAAW,EAAE;EACtB;EACA,MAAMC,aAAa,GAAGC,OAAO,IAAI;IAC/B,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;MAC/B,MAAMC,OAAO,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAC7CF,OAAO,CAACG,SAAS,GAAGJ,OAAO;MAC3BJ,QAAQ,CAACS,MAAM,CAACJ,OAAO,CAACK,QAAQ,CAAC,CAAC,CAAC,CAAC;MACpCL,OAAO,CAACG,SAAS,GAAG,EAAE;IACxB,CAAC,MAAM;MACLR,QAAQ,CAACS,MAAM,CAACL,OAAO,CAAC;IAC1B;EACF,CAAC;EACD,IAAI,OAAOP,MAAM,KAAK,QAAQ,IAAI,QAAQ,IAAIA,MAAM,EAAE;IACpD,KAAK,IAAIc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGd,MAAM,CAACe,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;MACzC,IAAId,MAAM,CAACc,CAAC,CAAC,EAAER,aAAa,CAACN,MAAM,CAACc,CAAC,CAAC,CAAC;IACzC;EACF,CAAC,MAAM;IACLR,aAAa,CAACN,MAAM,CAAC;EACvB;EACAC,MAAM,CAACe,YAAY,EAAE;EACrB,IAAId,MAAM,CAACE,IAAI,EAAE;IACfH,MAAM,CAACgB,UAAU,EAAE;EACrB;EACA,IAAI,CAACf,MAAM,CAACgB,QAAQ,IAAIjB,MAAM,CAACkB,SAAS,EAAE;IACxClB,MAAM,CAACmB,MAAM,EAAE;EACjB;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}