{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _react = _interopRequireDefault(require(\"react\"));\nvar _classnames = _interopRequireDefault(require(\"classnames\"));\nvar _utils = require(\"../../utils\");\nvar propTypes = {\n  player: _propTypes[\"default\"].object,\n  className: _propTypes[\"default\"].string\n};\nfunction RemainingTimeDisplay(_ref) {\n  var _ref$player = _ref.player,\n    currentTime = _ref$player.currentTime,\n    duration = _ref$player.duration,\n    className = _ref.className;\n  var remainingTime = duration - currentTime;\n  var formattedTime = (0, _utils.formatTime)(remainingTime);\n  return _react[\"default\"].createElement(\"div\", {\n    className: (0, _classnames[\"default\"])('video-react-remaining-time video-react-time-control video-react-control', className)\n  }, _react[\"default\"].createElement(\"div\", {\n    className: \"video-react-remaining-time-display\",\n    \"aria-live\": \"off\"\n  }, _react[\"default\"].createElement(\"span\", {\n    className: \"video-react-control-text\"\n  }, \"Remaining Time \"), \"-\".concat(formattedTime)));\n}\nRemainingTimeDisplay.propTypes = propTypes;\nRemainingTimeDisplay.displayName = 'RemainingTimeDisplay';\nvar _default = RemainingTimeDisplay;\nexports[\"default\"] = _default;", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "_propTypes", "_react", "_classnames", "_utils", "propTypes", "player", "object", "className", "string", "RemainingTimeDisplay", "_ref", "_ref$player", "currentTime", "duration", "remainingTime", "formattedTime", "formatTime", "createElement", "concat", "displayName", "_default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/video-react/lib/components/time-controls/RemainingTimeDisplay.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\n\nvar _react = _interopRequireDefault(require(\"react\"));\n\nvar _classnames = _interopRequireDefault(require(\"classnames\"));\n\nvar _utils = require(\"../../utils\");\n\nvar propTypes = {\n  player: _propTypes[\"default\"].object,\n  className: _propTypes[\"default\"].string\n};\n\nfunction RemainingTimeDisplay(_ref) {\n  var _ref$player = _ref.player,\n      currentTime = _ref$player.currentTime,\n      duration = _ref$player.duration,\n      className = _ref.className;\n  var remainingTime = duration - currentTime;\n  var formattedTime = (0, _utils.formatTime)(remainingTime);\n  return _react[\"default\"].createElement(\"div\", {\n    className: (0, _classnames[\"default\"])('video-react-remaining-time video-react-time-control video-react-control', className)\n  }, _react[\"default\"].createElement(\"div\", {\n    className: \"video-react-remaining-time-display\",\n    \"aria-live\": \"off\"\n  }, _react[\"default\"].createElement(\"span\", {\n    className: \"video-react-control-text\"\n  }, \"Remaining Time \"), \"-\".concat(formattedTime)));\n}\n\nRemainingTimeDisplay.propTypes = propTypes;\nRemainingTimeDisplay.displayName = 'RemainingTimeDisplay';\nvar _default = RemainingTimeDisplay;\nexports[\"default\"] = _default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC;AAEpFC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;AAE3B,IAAIE,UAAU,GAAGN,sBAAsB,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;AAE9D,IAAIM,MAAM,GAAGP,sBAAsB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AAErD,IAAIO,WAAW,GAAGR,sBAAsB,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;AAE/D,IAAIQ,MAAM,GAAGR,OAAO,CAAC,aAAa,CAAC;AAEnC,IAAIS,SAAS,GAAG;EACdC,MAAM,EAAEL,UAAU,CAAC,SAAS,CAAC,CAACM,MAAM;EACpCC,SAAS,EAAEP,UAAU,CAAC,SAAS,CAAC,CAACQ;AACnC,CAAC;AAED,SAASC,oBAAoBA,CAACC,IAAI,EAAE;EAClC,IAAIC,WAAW,GAAGD,IAAI,CAACL,MAAM;IACzBO,WAAW,GAAGD,WAAW,CAACC,WAAW;IACrCC,QAAQ,GAAGF,WAAW,CAACE,QAAQ;IAC/BN,SAAS,GAAGG,IAAI,CAACH,SAAS;EAC9B,IAAIO,aAAa,GAAGD,QAAQ,GAAGD,WAAW;EAC1C,IAAIG,aAAa,GAAG,CAAC,CAAC,EAAEZ,MAAM,CAACa,UAAU,EAAEF,aAAa,CAAC;EACzD,OAAOb,MAAM,CAAC,SAAS,CAAC,CAACgB,aAAa,CAAC,KAAK,EAAE;IAC5CV,SAAS,EAAE,CAAC,CAAC,EAAEL,WAAW,CAAC,SAAS,CAAC,EAAE,yEAAyE,EAAEK,SAAS;EAC7H,CAAC,EAAEN,MAAM,CAAC,SAAS,CAAC,CAACgB,aAAa,CAAC,KAAK,EAAE;IACxCV,SAAS,EAAE,oCAAoC;IAC/C,WAAW,EAAE;EACf,CAAC,EAAEN,MAAM,CAAC,SAAS,CAAC,CAACgB,aAAa,CAAC,MAAM,EAAE;IACzCV,SAAS,EAAE;EACb,CAAC,EAAE,iBAAiB,CAAC,EAAE,GAAG,CAACW,MAAM,CAACH,aAAa,CAAC,CAAC,CAAC;AACpD;AAEAN,oBAAoB,CAACL,SAAS,GAAGA,SAAS;AAC1CK,oBAAoB,CAACU,WAAW,GAAG,sBAAsB;AACzD,IAAIC,QAAQ,GAAGX,oBAAoB;AACnCX,OAAO,CAAC,SAAS,CAAC,GAAGsB,QAAQ"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}