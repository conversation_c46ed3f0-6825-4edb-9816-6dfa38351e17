{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\");\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nvar _classCallCheck2 = _interopRequireDefault(require(\"@babel/runtime/helpers/classCallCheck\"));\nvar _createClass2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createClass\"));\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"@babel/runtime/helpers/possibleConstructorReturn\"));\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"@babel/runtime/helpers/getPrototypeOf\"));\nvar _assertThisInitialized2 = _interopRequireDefault(require(\"@babel/runtime/helpers/assertThisInitialized\"));\nvar _inherits2 = _interopRequireDefault(require(\"@babel/runtime/helpers/inherits\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _react = _interopRequireWildcard(require(\"react\"));\nvar _classnames = _interopRequireDefault(require(\"classnames\"));\nvar Dom = _interopRequireWildcard(require(\"../utils/dom\"));\nvar propTypes = {\n  className: _propTypes[\"default\"].string,\n  onMouseDown: _propTypes[\"default\"].func,\n  onMouseMove: _propTypes[\"default\"].func,\n  stepForward: _propTypes[\"default\"].func,\n  stepBack: _propTypes[\"default\"].func,\n  sliderActive: _propTypes[\"default\"].func,\n  sliderInactive: _propTypes[\"default\"].func,\n  onMouseUp: _propTypes[\"default\"].func,\n  onFocus: _propTypes[\"default\"].func,\n  onBlur: _propTypes[\"default\"].func,\n  onClick: _propTypes[\"default\"].func,\n  getPercent: _propTypes[\"default\"].func,\n  vertical: _propTypes[\"default\"].bool,\n  children: _propTypes[\"default\"].node,\n  label: _propTypes[\"default\"].string,\n  valuenow: _propTypes[\"default\"].string,\n  valuetext: _propTypes[\"default\"].string\n};\nvar Slider = /*#__PURE__*/\nfunction (_Component) {\n  (0, _inherits2[\"default\"])(Slider, _Component);\n  function Slider(props, context) {\n    var _this;\n    (0, _classCallCheck2[\"default\"])(this, Slider);\n    _this = (0, _possibleConstructorReturn2[\"default\"])(this, (0, _getPrototypeOf2[\"default\"])(Slider).call(this, props, context));\n    _this.handleMouseDown = _this.handleMouseDown.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleMouseMove = _this.handleMouseMove.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleMouseUp = _this.handleMouseUp.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleFocus = _this.handleFocus.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleBlur = _this.handleBlur.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleClick = _this.handleClick.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleKeyPress = _this.handleKeyPress.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.stepForward = _this.stepForward.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.stepBack = _this.stepBack.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.calculateDistance = _this.calculateDistance.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.getProgress = _this.getProgress.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.renderChildren = _this.renderChildren.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.state = {\n      active: false\n    };\n    return _this;\n  }\n  (0, _createClass2[\"default\"])(Slider, [{\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      document.removeEventListener('mousemove', this.handleMouseMove, true);\n      document.removeEventListener('mouseup', this.handleMouseUp, true);\n      document.removeEventListener('touchmove', this.handleMouseMove, true);\n      document.removeEventListener('touchend', this.handleMouseUp, true);\n      document.removeEventListener('keydown', this.handleKeyPress, true);\n    }\n  }, {\n    key: \"getProgress\",\n    value: function getProgress() {\n      var getPercent = this.props.getPercent;\n      if (!getPercent) {\n        return 0;\n      }\n      var progress = getPercent(); // Protect against no duration and other division issues\n\n      if (typeof progress !== 'number' || progress < 0 || progress === Infinity) {\n        progress = 0;\n      }\n      return progress;\n    }\n  }, {\n    key: \"handleMouseDown\",\n    value: function handleMouseDown(event) {\n      var onMouseDown = this.props.onMouseDown; // event.preventDefault();\n      // event.stopPropagation();\n\n      document.addEventListener('mousemove', this.handleMouseMove, true);\n      document.addEventListener('mouseup', this.handleMouseUp, true);\n      document.addEventListener('touchmove', this.handleMouseMove, true);\n      document.addEventListener('touchend', this.handleMouseUp, true);\n      this.setState({\n        active: true\n      });\n      if (this.props.sliderActive) {\n        this.props.sliderActive(event);\n      }\n      this.handleMouseMove(event);\n      if (onMouseDown) {\n        onMouseDown(event);\n      }\n    }\n  }, {\n    key: \"handleMouseMove\",\n    value: function handleMouseMove(event) {\n      var onMouseMove = this.props.onMouseMove;\n      if (onMouseMove) {\n        onMouseMove(event);\n      }\n    }\n  }, {\n    key: \"handleMouseUp\",\n    value: function handleMouseUp(event) {\n      // On iOS safari, a subsequent mouseup event will be fired after touchend.\n      // Its weird event positions make the player seek a wrong time.\n      // calling preventDefault (at touchend phase) will prevent the mouseup event\n      event.preventDefault();\n      var onMouseUp = this.props.onMouseUp;\n      document.removeEventListener('mousemove', this.handleMouseMove, true);\n      document.removeEventListener('mouseup', this.handleMouseUp, true);\n      document.removeEventListener('touchmove', this.handleMouseMove, true);\n      document.removeEventListener('touchend', this.handleMouseUp, true);\n      this.setState({\n        active: false\n      });\n      if (this.props.sliderInactive) {\n        this.props.sliderInactive(event);\n      }\n      if (onMouseUp) {\n        onMouseUp(event);\n      }\n    }\n  }, {\n    key: \"handleFocus\",\n    value: function handleFocus(e) {\n      document.addEventListener('keydown', this.handleKeyPress, true);\n      if (this.props.onFocus) {\n        this.props.onFocus(e);\n      }\n    }\n  }, {\n    key: \"handleBlur\",\n    value: function handleBlur(e) {\n      document.removeEventListener('keydown', this.handleKeyPress, true);\n      if (this.props.onBlur) {\n        this.props.onBlur(e);\n      }\n    }\n  }, {\n    key: \"handleClick\",\n    value: function handleClick(event) {\n      event.preventDefault(); // event.stopPropagation();\n\n      if (this.props.onClick) {\n        this.props.onClick(event);\n      }\n    }\n  }, {\n    key: \"handleKeyPress\",\n    value: function handleKeyPress(event) {\n      if (event.which === 37 || event.which === 40) {\n        // Left and Down Arrows\n        event.preventDefault();\n        event.stopPropagation();\n        this.stepBack();\n      } else if (event.which === 38 || event.which === 39) {\n        // Up and Right Arrows\n        event.preventDefault();\n        event.stopPropagation();\n        this.stepForward();\n      }\n    }\n  }, {\n    key: \"stepForward\",\n    value: function stepForward() {\n      if (this.props.stepForward) {\n        this.props.stepForward();\n      }\n    }\n  }, {\n    key: \"stepBack\",\n    value: function stepBack() {\n      if (this.props.stepBack) {\n        this.props.stepBack();\n      }\n    }\n  }, {\n    key: \"calculateDistance\",\n    value: function calculateDistance(event) {\n      var node = this.slider;\n      var position = Dom.getPointerPosition(node, event);\n      if (this.props.vertical) {\n        return position.y;\n      }\n      return position.x;\n    }\n  }, {\n    key: \"renderChildren\",\n    value: function renderChildren() {\n      var progress = this.getProgress();\n      var percentage = \"\".concat((progress * 100).toFixed(2), \"%\");\n      return _react[\"default\"].Children.map(this.props.children, function (child) {\n        return _react[\"default\"].cloneElement(child, {\n          progress: progress,\n          percentage: percentage\n        });\n      });\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this2 = this;\n      var _this$props = this.props,\n        vertical = _this$props.vertical,\n        label = _this$props.label,\n        valuenow = _this$props.valuenow,\n        valuetext = _this$props.valuetext;\n      return _react[\"default\"].createElement(\"div\", {\n        className: (0, _classnames[\"default\"])(this.props.className, {\n          'video-react-slider-vertical': vertical,\n          'video-react-slider-horizontal': !vertical,\n          'video-react-sliding': this.state.active\n        }, 'video-react-slider'),\n        ref: function ref(c) {\n          _this2.slider = c;\n        },\n        tabIndex: \"0\",\n        role: \"slider\",\n        onMouseDown: this.handleMouseDown,\n        onTouchStart: this.handleMouseDown,\n        onFocus: this.handleFocus,\n        onBlur: this.handleBlur,\n        onClick: this.handleClick,\n        \"aria-label\": label || '',\n        \"aria-valuenow\": valuenow || '',\n        \"aria-valuetext\": valuetext || '',\n        \"aria-valuemin\": 0,\n        \"aria-valuemax\": 100\n      }, this.renderChildren());\n    }\n  }]);\n  return Slider;\n}(_react.Component);\nexports[\"default\"] = Slider;\nSlider.propTypes = propTypes;\nSlider.displayName = 'Slider';", "map": {"version": 3, "names": ["_interopRequireWildcard", "require", "_interopRequireDefault", "Object", "defineProperty", "exports", "value", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_assertThisInitialized2", "_inherits2", "_propTypes", "_react", "_classnames", "Dom", "propTypes", "className", "string", "onMouseDown", "func", "onMouseMove", "stepForward", "stepBack", "sliderActive", "sliderInactive", "onMouseUp", "onFocus", "onBlur", "onClick", "getPercent", "vertical", "bool", "children", "node", "label", "valuenow", "valuetext", "Slide<PERSON>", "_Component", "props", "context", "_this", "call", "handleMouseDown", "bind", "handleMouseMove", "handleMouseUp", "handleFocus", "handleBlur", "handleClick", "handleKeyPress", "calculateDistance", "getProgress", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "state", "active", "key", "componentWillUnmount", "document", "removeEventListener", "progress", "Infinity", "event", "addEventListener", "setState", "preventDefault", "e", "which", "stopPropagation", "slider", "position", "getPointerPosition", "y", "x", "percentage", "concat", "toFixed", "Children", "map", "child", "cloneElement", "render", "_this2", "_this$props", "createElement", "ref", "c", "tabIndex", "role", "onTouchStart", "Component", "displayName"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/video-react/lib/components/Slider.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\");\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _classCallCheck2 = _interopRequireDefault(require(\"@babel/runtime/helpers/classCallCheck\"));\n\nvar _createClass2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createClass\"));\n\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"@babel/runtime/helpers/possibleConstructorReturn\"));\n\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"@babel/runtime/helpers/getPrototypeOf\"));\n\nvar _assertThisInitialized2 = _interopRequireDefault(require(\"@babel/runtime/helpers/assertThisInitialized\"));\n\nvar _inherits2 = _interopRequireDefault(require(\"@babel/runtime/helpers/inherits\"));\n\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\n\nvar _react = _interopRequireWildcard(require(\"react\"));\n\nvar _classnames = _interopRequireDefault(require(\"classnames\"));\n\nvar Dom = _interopRequireWildcard(require(\"../utils/dom\"));\n\nvar propTypes = {\n  className: _propTypes[\"default\"].string,\n  onMouseDown: _propTypes[\"default\"].func,\n  onMouseMove: _propTypes[\"default\"].func,\n  stepForward: _propTypes[\"default\"].func,\n  stepBack: _propTypes[\"default\"].func,\n  sliderActive: _propTypes[\"default\"].func,\n  sliderInactive: _propTypes[\"default\"].func,\n  onMouseUp: _propTypes[\"default\"].func,\n  onFocus: _propTypes[\"default\"].func,\n  onBlur: _propTypes[\"default\"].func,\n  onClick: _propTypes[\"default\"].func,\n  getPercent: _propTypes[\"default\"].func,\n  vertical: _propTypes[\"default\"].bool,\n  children: _propTypes[\"default\"].node,\n  label: _propTypes[\"default\"].string,\n  valuenow: _propTypes[\"default\"].string,\n  valuetext: _propTypes[\"default\"].string\n};\n\nvar Slider =\n/*#__PURE__*/\nfunction (_Component) {\n  (0, _inherits2[\"default\"])(Slider, _Component);\n\n  function Slider(props, context) {\n    var _this;\n\n    (0, _classCallCheck2[\"default\"])(this, Slider);\n    _this = (0, _possibleConstructorReturn2[\"default\"])(this, (0, _getPrototypeOf2[\"default\"])(Slider).call(this, props, context));\n    _this.handleMouseDown = _this.handleMouseDown.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleMouseMove = _this.handleMouseMove.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleMouseUp = _this.handleMouseUp.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleFocus = _this.handleFocus.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleBlur = _this.handleBlur.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleClick = _this.handleClick.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleKeyPress = _this.handleKeyPress.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.stepForward = _this.stepForward.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.stepBack = _this.stepBack.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.calculateDistance = _this.calculateDistance.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.getProgress = _this.getProgress.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.renderChildren = _this.renderChildren.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.state = {\n      active: false\n    };\n    return _this;\n  }\n\n  (0, _createClass2[\"default\"])(Slider, [{\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      document.removeEventListener('mousemove', this.handleMouseMove, true);\n      document.removeEventListener('mouseup', this.handleMouseUp, true);\n      document.removeEventListener('touchmove', this.handleMouseMove, true);\n      document.removeEventListener('touchend', this.handleMouseUp, true);\n      document.removeEventListener('keydown', this.handleKeyPress, true);\n    }\n  }, {\n    key: \"getProgress\",\n    value: function getProgress() {\n      var getPercent = this.props.getPercent;\n\n      if (!getPercent) {\n        return 0;\n      }\n\n      var progress = getPercent(); // Protect against no duration and other division issues\n\n      if (typeof progress !== 'number' || progress < 0 || progress === Infinity) {\n        progress = 0;\n      }\n\n      return progress;\n    }\n  }, {\n    key: \"handleMouseDown\",\n    value: function handleMouseDown(event) {\n      var onMouseDown = this.props.onMouseDown; // event.preventDefault();\n      // event.stopPropagation();\n\n      document.addEventListener('mousemove', this.handleMouseMove, true);\n      document.addEventListener('mouseup', this.handleMouseUp, true);\n      document.addEventListener('touchmove', this.handleMouseMove, true);\n      document.addEventListener('touchend', this.handleMouseUp, true);\n      this.setState({\n        active: true\n      });\n\n      if (this.props.sliderActive) {\n        this.props.sliderActive(event);\n      }\n\n      this.handleMouseMove(event);\n\n      if (onMouseDown) {\n        onMouseDown(event);\n      }\n    }\n  }, {\n    key: \"handleMouseMove\",\n    value: function handleMouseMove(event) {\n      var onMouseMove = this.props.onMouseMove;\n\n      if (onMouseMove) {\n        onMouseMove(event);\n      }\n    }\n  }, {\n    key: \"handleMouseUp\",\n    value: function handleMouseUp(event) {\n      // On iOS safari, a subsequent mouseup event will be fired after touchend.\n      // Its weird event positions make the player seek a wrong time.\n      // calling preventDefault (at touchend phase) will prevent the mouseup event\n      event.preventDefault();\n      var onMouseUp = this.props.onMouseUp;\n      document.removeEventListener('mousemove', this.handleMouseMove, true);\n      document.removeEventListener('mouseup', this.handleMouseUp, true);\n      document.removeEventListener('touchmove', this.handleMouseMove, true);\n      document.removeEventListener('touchend', this.handleMouseUp, true);\n      this.setState({\n        active: false\n      });\n\n      if (this.props.sliderInactive) {\n        this.props.sliderInactive(event);\n      }\n\n      if (onMouseUp) {\n        onMouseUp(event);\n      }\n    }\n  }, {\n    key: \"handleFocus\",\n    value: function handleFocus(e) {\n      document.addEventListener('keydown', this.handleKeyPress, true);\n\n      if (this.props.onFocus) {\n        this.props.onFocus(e);\n      }\n    }\n  }, {\n    key: \"handleBlur\",\n    value: function handleBlur(e) {\n      document.removeEventListener('keydown', this.handleKeyPress, true);\n\n      if (this.props.onBlur) {\n        this.props.onBlur(e);\n      }\n    }\n  }, {\n    key: \"handleClick\",\n    value: function handleClick(event) {\n      event.preventDefault(); // event.stopPropagation();\n\n      if (this.props.onClick) {\n        this.props.onClick(event);\n      }\n    }\n  }, {\n    key: \"handleKeyPress\",\n    value: function handleKeyPress(event) {\n      if (event.which === 37 || event.which === 40) {\n        // Left and Down Arrows\n        event.preventDefault();\n        event.stopPropagation();\n        this.stepBack();\n      } else if (event.which === 38 || event.which === 39) {\n        // Up and Right Arrows\n        event.preventDefault();\n        event.stopPropagation();\n        this.stepForward();\n      }\n    }\n  }, {\n    key: \"stepForward\",\n    value: function stepForward() {\n      if (this.props.stepForward) {\n        this.props.stepForward();\n      }\n    }\n  }, {\n    key: \"stepBack\",\n    value: function stepBack() {\n      if (this.props.stepBack) {\n        this.props.stepBack();\n      }\n    }\n  }, {\n    key: \"calculateDistance\",\n    value: function calculateDistance(event) {\n      var node = this.slider;\n      var position = Dom.getPointerPosition(node, event);\n\n      if (this.props.vertical) {\n        return position.y;\n      }\n\n      return position.x;\n    }\n  }, {\n    key: \"renderChildren\",\n    value: function renderChildren() {\n      var progress = this.getProgress();\n      var percentage = \"\".concat((progress * 100).toFixed(2), \"%\");\n      return _react[\"default\"].Children.map(this.props.children, function (child) {\n        return _react[\"default\"].cloneElement(child, {\n          progress: progress,\n          percentage: percentage\n        });\n      });\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this2 = this;\n\n      var _this$props = this.props,\n          vertical = _this$props.vertical,\n          label = _this$props.label,\n          valuenow = _this$props.valuenow,\n          valuetext = _this$props.valuetext;\n      return _react[\"default\"].createElement(\"div\", {\n        className: (0, _classnames[\"default\"])(this.props.className, {\n          'video-react-slider-vertical': vertical,\n          'video-react-slider-horizontal': !vertical,\n          'video-react-sliding': this.state.active\n        }, 'video-react-slider'),\n        ref: function ref(c) {\n          _this2.slider = c;\n        },\n        tabIndex: \"0\",\n        role: \"slider\",\n        onMouseDown: this.handleMouseDown,\n        onTouchStart: this.handleMouseDown,\n        onFocus: this.handleFocus,\n        onBlur: this.handleBlur,\n        onClick: this.handleClick,\n        \"aria-label\": label || '',\n        \"aria-valuenow\": valuenow || '',\n        \"aria-valuetext\": valuetext || '',\n        \"aria-valuemin\": 0,\n        \"aria-valuemax\": 100\n      }, this.renderChildren());\n    }\n  }]);\n  return Slider;\n}(_react.Component);\n\nexports[\"default\"] = Slider;\nSlider.propTypes = propTypes;\nSlider.displayName = 'Slider';"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,uBAAuB,GAAGC,OAAO,CAAC,+CAA+C,CAAC;AAEtF,IAAIC,sBAAsB,GAAGD,OAAO,CAAC,8CAA8C,CAAC;AAEpFE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;AAE3B,IAAIE,gBAAgB,GAAGL,sBAAsB,CAACD,OAAO,CAAC,uCAAuC,CAAC,CAAC;AAE/F,IAAIO,aAAa,GAAGN,sBAAsB,CAACD,OAAO,CAAC,oCAAoC,CAAC,CAAC;AAEzF,IAAIQ,2BAA2B,GAAGP,sBAAsB,CAACD,OAAO,CAAC,kDAAkD,CAAC,CAAC;AAErH,IAAIS,gBAAgB,GAAGR,sBAAsB,CAACD,OAAO,CAAC,uCAAuC,CAAC,CAAC;AAE/F,IAAIU,uBAAuB,GAAGT,sBAAsB,CAACD,OAAO,CAAC,8CAA8C,CAAC,CAAC;AAE7G,IAAIW,UAAU,GAAGV,sBAAsB,CAACD,OAAO,CAAC,iCAAiC,CAAC,CAAC;AAEnF,IAAIY,UAAU,GAAGX,sBAAsB,CAACD,OAAO,CAAC,YAAY,CAAC,CAAC;AAE9D,IAAIa,MAAM,GAAGd,uBAAuB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AAEtD,IAAIc,WAAW,GAAGb,sBAAsB,CAACD,OAAO,CAAC,YAAY,CAAC,CAAC;AAE/D,IAAIe,GAAG,GAAGhB,uBAAuB,CAACC,OAAO,CAAC,cAAc,CAAC,CAAC;AAE1D,IAAIgB,SAAS,GAAG;EACdC,SAAS,EAAEL,UAAU,CAAC,SAAS,CAAC,CAACM,MAAM;EACvCC,WAAW,EAAEP,UAAU,CAAC,SAAS,CAAC,CAACQ,IAAI;EACvCC,WAAW,EAAET,UAAU,CAAC,SAAS,CAAC,CAACQ,IAAI;EACvCE,WAAW,EAAEV,UAAU,CAAC,SAAS,CAAC,CAACQ,IAAI;EACvCG,QAAQ,EAAEX,UAAU,CAAC,SAAS,CAAC,CAACQ,IAAI;EACpCI,YAAY,EAAEZ,UAAU,CAAC,SAAS,CAAC,CAACQ,IAAI;EACxCK,cAAc,EAAEb,UAAU,CAAC,SAAS,CAAC,CAACQ,IAAI;EAC1CM,SAAS,EAAEd,UAAU,CAAC,SAAS,CAAC,CAACQ,IAAI;EACrCO,OAAO,EAAEf,UAAU,CAAC,SAAS,CAAC,CAACQ,IAAI;EACnCQ,MAAM,EAAEhB,UAAU,CAAC,SAAS,CAAC,CAACQ,IAAI;EAClCS,OAAO,EAAEjB,UAAU,CAAC,SAAS,CAAC,CAACQ,IAAI;EACnCU,UAAU,EAAElB,UAAU,CAAC,SAAS,CAAC,CAACQ,IAAI;EACtCW,QAAQ,EAAEnB,UAAU,CAAC,SAAS,CAAC,CAACoB,IAAI;EACpCC,QAAQ,EAAErB,UAAU,CAAC,SAAS,CAAC,CAACsB,IAAI;EACpCC,KAAK,EAAEvB,UAAU,CAAC,SAAS,CAAC,CAACM,MAAM;EACnCkB,QAAQ,EAAExB,UAAU,CAAC,SAAS,CAAC,CAACM,MAAM;EACtCmB,SAAS,EAAEzB,UAAU,CAAC,SAAS,CAAC,CAACM;AACnC,CAAC;AAED,IAAIoB,MAAM,GACV;AACA,UAAUC,UAAU,EAAE;EACpB,CAAC,CAAC,EAAE5B,UAAU,CAAC,SAAS,CAAC,EAAE2B,MAAM,EAAEC,UAAU,CAAC;EAE9C,SAASD,MAAMA,CAACE,KAAK,EAAEC,OAAO,EAAE;IAC9B,IAAIC,KAAK;IAET,CAAC,CAAC,EAAEpC,gBAAgB,CAAC,SAAS,CAAC,EAAE,IAAI,EAAEgC,MAAM,CAAC;IAC9CI,KAAK,GAAG,CAAC,CAAC,EAAElC,2BAA2B,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAEC,gBAAgB,CAAC,SAAS,CAAC,EAAE6B,MAAM,CAAC,CAACK,IAAI,CAAC,IAAI,EAAEH,KAAK,EAAEC,OAAO,CAAC,CAAC;IAC9HC,KAAK,CAACE,eAAe,GAAGF,KAAK,CAACE,eAAe,CAACC,IAAI,CAAC,CAAC,CAAC,EAAEnC,uBAAuB,CAAC,SAAS,CAAC,EAAEgC,KAAK,CAAC,CAAC;IAClGA,KAAK,CAACI,eAAe,GAAGJ,KAAK,CAACI,eAAe,CAACD,IAAI,CAAC,CAAC,CAAC,EAAEnC,uBAAuB,CAAC,SAAS,CAAC,EAAEgC,KAAK,CAAC,CAAC;IAClGA,KAAK,CAACK,aAAa,GAAGL,KAAK,CAACK,aAAa,CAACF,IAAI,CAAC,CAAC,CAAC,EAAEnC,uBAAuB,CAAC,SAAS,CAAC,EAAEgC,KAAK,CAAC,CAAC;IAC9FA,KAAK,CAACM,WAAW,GAAGN,KAAK,CAACM,WAAW,CAACH,IAAI,CAAC,CAAC,CAAC,EAAEnC,uBAAuB,CAAC,SAAS,CAAC,EAAEgC,KAAK,CAAC,CAAC;IAC1FA,KAAK,CAACO,UAAU,GAAGP,KAAK,CAACO,UAAU,CAACJ,IAAI,CAAC,CAAC,CAAC,EAAEnC,uBAAuB,CAAC,SAAS,CAAC,EAAEgC,KAAK,CAAC,CAAC;IACxFA,KAAK,CAACQ,WAAW,GAAGR,KAAK,CAACQ,WAAW,CAACL,IAAI,CAAC,CAAC,CAAC,EAAEnC,uBAAuB,CAAC,SAAS,CAAC,EAAEgC,KAAK,CAAC,CAAC;IAC1FA,KAAK,CAACS,cAAc,GAAGT,KAAK,CAACS,cAAc,CAACN,IAAI,CAAC,CAAC,CAAC,EAAEnC,uBAAuB,CAAC,SAAS,CAAC,EAAEgC,KAAK,CAAC,CAAC;IAChGA,KAAK,CAACpB,WAAW,GAAGoB,KAAK,CAACpB,WAAW,CAACuB,IAAI,CAAC,CAAC,CAAC,EAAEnC,uBAAuB,CAAC,SAAS,CAAC,EAAEgC,KAAK,CAAC,CAAC;IAC1FA,KAAK,CAACnB,QAAQ,GAAGmB,KAAK,CAACnB,QAAQ,CAACsB,IAAI,CAAC,CAAC,CAAC,EAAEnC,uBAAuB,CAAC,SAAS,CAAC,EAAEgC,KAAK,CAAC,CAAC;IACpFA,KAAK,CAACU,iBAAiB,GAAGV,KAAK,CAACU,iBAAiB,CAACP,IAAI,CAAC,CAAC,CAAC,EAAEnC,uBAAuB,CAAC,SAAS,CAAC,EAAEgC,KAAK,CAAC,CAAC;IACtGA,KAAK,CAACW,WAAW,GAAGX,KAAK,CAACW,WAAW,CAACR,IAAI,CAAC,CAAC,CAAC,EAAEnC,uBAAuB,CAAC,SAAS,CAAC,EAAEgC,KAAK,CAAC,CAAC;IAC1FA,KAAK,CAACY,cAAc,GAAGZ,KAAK,CAACY,cAAc,CAACT,IAAI,CAAC,CAAC,CAAC,EAAEnC,uBAAuB,CAAC,SAAS,CAAC,EAAEgC,KAAK,CAAC,CAAC;IAChGA,KAAK,CAACa,KAAK,GAAG;MACZC,MAAM,EAAE;IACV,CAAC;IACD,OAAOd,KAAK;EACd;EAEA,CAAC,CAAC,EAAEnC,aAAa,CAAC,SAAS,CAAC,EAAE+B,MAAM,EAAE,CAAC;IACrCmB,GAAG,EAAE,sBAAsB;IAC3BpD,KAAK,EAAE,SAASqD,oBAAoBA,CAAA,EAAG;MACrCC,QAAQ,CAACC,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAACd,eAAe,EAAE,IAAI,CAAC;MACrEa,QAAQ,CAACC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAACb,aAAa,EAAE,IAAI,CAAC;MACjEY,QAAQ,CAACC,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAACd,eAAe,EAAE,IAAI,CAAC;MACrEa,QAAQ,CAACC,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAACb,aAAa,EAAE,IAAI,CAAC;MAClEY,QAAQ,CAACC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAACT,cAAc,EAAE,IAAI,CAAC;IACpE;EACF,CAAC,EAAE;IACDM,GAAG,EAAE,aAAa;IAClBpD,KAAK,EAAE,SAASgD,WAAWA,CAAA,EAAG;MAC5B,IAAIvB,UAAU,GAAG,IAAI,CAACU,KAAK,CAACV,UAAU;MAEtC,IAAI,CAACA,UAAU,EAAE;QACf,OAAO,CAAC;MACV;MAEA,IAAI+B,QAAQ,GAAG/B,UAAU,EAAE,CAAC,CAAC;;MAE7B,IAAI,OAAO+B,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,GAAG,CAAC,IAAIA,QAAQ,KAAKC,QAAQ,EAAE;QACzED,QAAQ,GAAG,CAAC;MACd;MAEA,OAAOA,QAAQ;IACjB;EACF,CAAC,EAAE;IACDJ,GAAG,EAAE,iBAAiB;IACtBpD,KAAK,EAAE,SAASuC,eAAeA,CAACmB,KAAK,EAAE;MACrC,IAAI5C,WAAW,GAAG,IAAI,CAACqB,KAAK,CAACrB,WAAW,CAAC,CAAC;MAC1C;;MAEAwC,QAAQ,CAACK,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAClB,eAAe,EAAE,IAAI,CAAC;MAClEa,QAAQ,CAACK,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAACjB,aAAa,EAAE,IAAI,CAAC;MAC9DY,QAAQ,CAACK,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAClB,eAAe,EAAE,IAAI,CAAC;MAClEa,QAAQ,CAACK,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAACjB,aAAa,EAAE,IAAI,CAAC;MAC/D,IAAI,CAACkB,QAAQ,CAAC;QACZT,MAAM,EAAE;MACV,CAAC,CAAC;MAEF,IAAI,IAAI,CAAChB,KAAK,CAAChB,YAAY,EAAE;QAC3B,IAAI,CAACgB,KAAK,CAAChB,YAAY,CAACuC,KAAK,CAAC;MAChC;MAEA,IAAI,CAACjB,eAAe,CAACiB,KAAK,CAAC;MAE3B,IAAI5C,WAAW,EAAE;QACfA,WAAW,CAAC4C,KAAK,CAAC;MACpB;IACF;EACF,CAAC,EAAE;IACDN,GAAG,EAAE,iBAAiB;IACtBpD,KAAK,EAAE,SAASyC,eAAeA,CAACiB,KAAK,EAAE;MACrC,IAAI1C,WAAW,GAAG,IAAI,CAACmB,KAAK,CAACnB,WAAW;MAExC,IAAIA,WAAW,EAAE;QACfA,WAAW,CAAC0C,KAAK,CAAC;MACpB;IACF;EACF,CAAC,EAAE;IACDN,GAAG,EAAE,eAAe;IACpBpD,KAAK,EAAE,SAAS0C,aAAaA,CAACgB,KAAK,EAAE;MACnC;MACA;MACA;MACAA,KAAK,CAACG,cAAc,EAAE;MACtB,IAAIxC,SAAS,GAAG,IAAI,CAACc,KAAK,CAACd,SAAS;MACpCiC,QAAQ,CAACC,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAACd,eAAe,EAAE,IAAI,CAAC;MACrEa,QAAQ,CAACC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAACb,aAAa,EAAE,IAAI,CAAC;MACjEY,QAAQ,CAACC,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAACd,eAAe,EAAE,IAAI,CAAC;MACrEa,QAAQ,CAACC,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAACb,aAAa,EAAE,IAAI,CAAC;MAClE,IAAI,CAACkB,QAAQ,CAAC;QACZT,MAAM,EAAE;MACV,CAAC,CAAC;MAEF,IAAI,IAAI,CAAChB,KAAK,CAACf,cAAc,EAAE;QAC7B,IAAI,CAACe,KAAK,CAACf,cAAc,CAACsC,KAAK,CAAC;MAClC;MAEA,IAAIrC,SAAS,EAAE;QACbA,SAAS,CAACqC,KAAK,CAAC;MAClB;IACF;EACF,CAAC,EAAE;IACDN,GAAG,EAAE,aAAa;IAClBpD,KAAK,EAAE,SAAS2C,WAAWA,CAACmB,CAAC,EAAE;MAC7BR,QAAQ,CAACK,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAACb,cAAc,EAAE,IAAI,CAAC;MAE/D,IAAI,IAAI,CAACX,KAAK,CAACb,OAAO,EAAE;QACtB,IAAI,CAACa,KAAK,CAACb,OAAO,CAACwC,CAAC,CAAC;MACvB;IACF;EACF,CAAC,EAAE;IACDV,GAAG,EAAE,YAAY;IACjBpD,KAAK,EAAE,SAAS4C,UAAUA,CAACkB,CAAC,EAAE;MAC5BR,QAAQ,CAACC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAACT,cAAc,EAAE,IAAI,CAAC;MAElE,IAAI,IAAI,CAACX,KAAK,CAACZ,MAAM,EAAE;QACrB,IAAI,CAACY,KAAK,CAACZ,MAAM,CAACuC,CAAC,CAAC;MACtB;IACF;EACF,CAAC,EAAE;IACDV,GAAG,EAAE,aAAa;IAClBpD,KAAK,EAAE,SAAS6C,WAAWA,CAACa,KAAK,EAAE;MACjCA,KAAK,CAACG,cAAc,EAAE,CAAC,CAAC;;MAExB,IAAI,IAAI,CAAC1B,KAAK,CAACX,OAAO,EAAE;QACtB,IAAI,CAACW,KAAK,CAACX,OAAO,CAACkC,KAAK,CAAC;MAC3B;IACF;EACF,CAAC,EAAE;IACDN,GAAG,EAAE,gBAAgB;IACrBpD,KAAK,EAAE,SAAS8C,cAAcA,CAACY,KAAK,EAAE;MACpC,IAAIA,KAAK,CAACK,KAAK,KAAK,EAAE,IAAIL,KAAK,CAACK,KAAK,KAAK,EAAE,EAAE;QAC5C;QACAL,KAAK,CAACG,cAAc,EAAE;QACtBH,KAAK,CAACM,eAAe,EAAE;QACvB,IAAI,CAAC9C,QAAQ,EAAE;MACjB,CAAC,MAAM,IAAIwC,KAAK,CAACK,KAAK,KAAK,EAAE,IAAIL,KAAK,CAACK,KAAK,KAAK,EAAE,EAAE;QACnD;QACAL,KAAK,CAACG,cAAc,EAAE;QACtBH,KAAK,CAACM,eAAe,EAAE;QACvB,IAAI,CAAC/C,WAAW,EAAE;MACpB;IACF;EACF,CAAC,EAAE;IACDmC,GAAG,EAAE,aAAa;IAClBpD,KAAK,EAAE,SAASiB,WAAWA,CAAA,EAAG;MAC5B,IAAI,IAAI,CAACkB,KAAK,CAAClB,WAAW,EAAE;QAC1B,IAAI,CAACkB,KAAK,CAAClB,WAAW,EAAE;MAC1B;IACF;EACF,CAAC,EAAE;IACDmC,GAAG,EAAE,UAAU;IACfpD,KAAK,EAAE,SAASkB,QAAQA,CAAA,EAAG;MACzB,IAAI,IAAI,CAACiB,KAAK,CAACjB,QAAQ,EAAE;QACvB,IAAI,CAACiB,KAAK,CAACjB,QAAQ,EAAE;MACvB;IACF;EACF,CAAC,EAAE;IACDkC,GAAG,EAAE,mBAAmB;IACxBpD,KAAK,EAAE,SAAS+C,iBAAiBA,CAACW,KAAK,EAAE;MACvC,IAAI7B,IAAI,GAAG,IAAI,CAACoC,MAAM;MACtB,IAAIC,QAAQ,GAAGxD,GAAG,CAACyD,kBAAkB,CAACtC,IAAI,EAAE6B,KAAK,CAAC;MAElD,IAAI,IAAI,CAACvB,KAAK,CAACT,QAAQ,EAAE;QACvB,OAAOwC,QAAQ,CAACE,CAAC;MACnB;MAEA,OAAOF,QAAQ,CAACG,CAAC;IACnB;EACF,CAAC,EAAE;IACDjB,GAAG,EAAE,gBAAgB;IACrBpD,KAAK,EAAE,SAASiD,cAAcA,CAAA,EAAG;MAC/B,IAAIO,QAAQ,GAAG,IAAI,CAACR,WAAW,EAAE;MACjC,IAAIsB,UAAU,GAAG,EAAE,CAACC,MAAM,CAAC,CAACf,QAAQ,GAAG,GAAG,EAAEgB,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC;MAC5D,OAAOhE,MAAM,CAAC,SAAS,CAAC,CAACiE,QAAQ,CAACC,GAAG,CAAC,IAAI,CAACvC,KAAK,CAACP,QAAQ,EAAE,UAAU+C,KAAK,EAAE;QAC1E,OAAOnE,MAAM,CAAC,SAAS,CAAC,CAACoE,YAAY,CAACD,KAAK,EAAE;UAC3CnB,QAAQ,EAAEA,QAAQ;UAClBc,UAAU,EAAEA;QACd,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDlB,GAAG,EAAE,QAAQ;IACbpD,KAAK,EAAE,SAAS6E,MAAMA,CAAA,EAAG;MACvB,IAAIC,MAAM,GAAG,IAAI;MAEjB,IAAIC,WAAW,GAAG,IAAI,CAAC5C,KAAK;QACxBT,QAAQ,GAAGqD,WAAW,CAACrD,QAAQ;QAC/BI,KAAK,GAAGiD,WAAW,CAACjD,KAAK;QACzBC,QAAQ,GAAGgD,WAAW,CAAChD,QAAQ;QAC/BC,SAAS,GAAG+C,WAAW,CAAC/C,SAAS;MACrC,OAAOxB,MAAM,CAAC,SAAS,CAAC,CAACwE,aAAa,CAAC,KAAK,EAAE;QAC5CpE,SAAS,EAAE,CAAC,CAAC,EAAEH,WAAW,CAAC,SAAS,CAAC,EAAE,IAAI,CAAC0B,KAAK,CAACvB,SAAS,EAAE;UAC3D,6BAA6B,EAAEc,QAAQ;UACvC,+BAA+B,EAAE,CAACA,QAAQ;UAC1C,qBAAqB,EAAE,IAAI,CAACwB,KAAK,CAACC;QACpC,CAAC,EAAE,oBAAoB,CAAC;QACxB8B,GAAG,EAAE,SAASA,GAAGA,CAACC,CAAC,EAAE;UACnBJ,MAAM,CAACb,MAAM,GAAGiB,CAAC;QACnB,CAAC;QACDC,QAAQ,EAAE,GAAG;QACbC,IAAI,EAAE,QAAQ;QACdtE,WAAW,EAAE,IAAI,CAACyB,eAAe;QACjC8C,YAAY,EAAE,IAAI,CAAC9C,eAAe;QAClCjB,OAAO,EAAE,IAAI,CAACqB,WAAW;QACzBpB,MAAM,EAAE,IAAI,CAACqB,UAAU;QACvBpB,OAAO,EAAE,IAAI,CAACqB,WAAW;QACzB,YAAY,EAAEf,KAAK,IAAI,EAAE;QACzB,eAAe,EAAEC,QAAQ,IAAI,EAAE;QAC/B,gBAAgB,EAAEC,SAAS,IAAI,EAAE;QACjC,eAAe,EAAE,CAAC;QAClB,eAAe,EAAE;MACnB,CAAC,EAAE,IAAI,CAACiB,cAAc,EAAE,CAAC;IAC3B;EACF,CAAC,CAAC,CAAC;EACH,OAAOhB,MAAM;AACf,CAAC,CAACzB,MAAM,CAAC8E,SAAS,CAAC;AAEnBvF,OAAO,CAAC,SAAS,CAAC,GAAGkC,MAAM;AAC3BA,MAAM,CAACtB,SAAS,GAAGA,SAAS;AAC5BsB,MAAM,CAACsD,WAAW,GAAG,QAAQ"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}