{"ast": null, "code": "export { pointEnd, pointStart, position } from './lib/index.js';", "map": {"version": 3, "names": ["pointEnd", "pointStart", "position"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/unist-util-position/index.js"], "sourcesContent": ["export {pointEnd, pointStart, position} from './lib/index.js'\n"], "mappings": "AAAA,SAAQA,QAAQ,EAAEC,UAAU,EAAEC,QAAQ,QAAO,gBAAgB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}