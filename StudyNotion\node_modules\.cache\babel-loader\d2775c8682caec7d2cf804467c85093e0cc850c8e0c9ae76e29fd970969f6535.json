{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar _react = _interopRequireDefault(require(\"react\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _allowed = _interopRequireDefault(require(\"../utils/allowed\"));\nvar TdInner = function TdInner(props) {\n  var headers = props.headers,\n    children = props.children,\n    columnKey = props.columnKey,\n    className = props.className,\n    colSpan = props.colSpan;\n  var classes = \"\".concat(className || '', \" pivoted\");\n  if (colSpan) {\n    return /*#__PURE__*/_react[\"default\"].createElement(\"td\", (0, _extends2[\"default\"])({\n      \"data-testid\": \"td\"\n    }, (0, _allowed[\"default\"])(props)));\n  }\n  return /*#__PURE__*/_react[\"default\"].createElement(\"td\", (0, _extends2[\"default\"])({\n    \"data-testid\": \"td\"\n  }, (0, _allowed[\"default\"])(props), {\n    className: classes\n  }), /*#__PURE__*/_react[\"default\"].createElement(\"div\", {\n    \"data-testid\": \"td-before\",\n    className: \"tdBefore\"\n  }, headers[columnKey]), children !== null && children !== void 0 ? children : /*#__PURE__*/_react[\"default\"].createElement(\"div\", null, \"\\xA0\"));\n};\nTdInner.propTypes = {\n  children: _propTypes[\"default\"].node,\n  headers: _propTypes[\"default\"].shape({}),\n  columnKey: _propTypes[\"default\"].number,\n  className: _propTypes[\"default\"].string,\n  colSpan: _propTypes[\"default\"].oneOfType([_propTypes[\"default\"].number, _propTypes[\"default\"].string])\n};\nTdInner.defaultProps = {\n  children: undefined,\n  headers: undefined,\n  columnKey: undefined,\n  className: undefined,\n  colSpan: undefined\n};\nvar _default = TdInner;\nexports[\"default\"] = _default;", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "_extends2", "_react", "_propTypes", "_allowed", "TdInner", "props", "headers", "children", "column<PERSON>ey", "className", "colSpan", "classes", "concat", "createElement", "propTypes", "node", "shape", "number", "string", "oneOfType", "defaultProps", "undefined", "_default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/react-super-responsive-table/dist/components/TdInner.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\n\nvar _react = _interopRequireDefault(require(\"react\"));\n\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\n\nvar _allowed = _interopRequireDefault(require(\"../utils/allowed\"));\n\nvar TdInner = function TdInner(props) {\n  var headers = props.headers,\n      children = props.children,\n      columnKey = props.columnKey,\n      className = props.className,\n      colSpan = props.colSpan;\n  var classes = \"\".concat(className || '', \" pivoted\");\n\n  if (colSpan) {\n    return /*#__PURE__*/_react[\"default\"].createElement(\"td\", (0, _extends2[\"default\"])({\n      \"data-testid\": \"td\"\n    }, (0, _allowed[\"default\"])(props)));\n  }\n\n  return /*#__PURE__*/_react[\"default\"].createElement(\"td\", (0, _extends2[\"default\"])({\n    \"data-testid\": \"td\"\n  }, (0, _allowed[\"default\"])(props), {\n    className: classes\n  }), /*#__PURE__*/_react[\"default\"].createElement(\"div\", {\n    \"data-testid\": \"td-before\",\n    className: \"tdBefore\"\n  }, headers[columnKey]), children !== null && children !== void 0 ? children : /*#__PURE__*/_react[\"default\"].createElement(\"div\", null, \"\\xA0\"));\n};\n\nTdInner.propTypes = {\n  children: _propTypes[\"default\"].node,\n  headers: _propTypes[\"default\"].shape({}),\n  columnKey: _propTypes[\"default\"].number,\n  className: _propTypes[\"default\"].string,\n  colSpan: _propTypes[\"default\"].oneOfType([_propTypes[\"default\"].number, _propTypes[\"default\"].string])\n};\nTdInner.defaultProps = {\n  children: undefined,\n  headers: undefined,\n  columnKey: undefined,\n  className: undefined,\n  colSpan: undefined\n};\nvar _default = TdInner;\nexports[\"default\"] = _default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC;AAEpFC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;AAE3B,IAAIE,SAAS,GAAGN,sBAAsB,CAACC,OAAO,CAAC,gCAAgC,CAAC,CAAC;AAEjF,IAAIM,MAAM,GAAGP,sBAAsB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AAErD,IAAIO,UAAU,GAAGR,sBAAsB,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;AAE9D,IAAIQ,QAAQ,GAAGT,sBAAsB,CAACC,OAAO,CAAC,kBAAkB,CAAC,CAAC;AAElE,IAAIS,OAAO,GAAG,SAASA,OAAOA,CAACC,KAAK,EAAE;EACpC,IAAIC,OAAO,GAAGD,KAAK,CAACC,OAAO;IACvBC,QAAQ,GAAGF,KAAK,CAACE,QAAQ;IACzBC,SAAS,GAAGH,KAAK,CAACG,SAAS;IAC3BC,SAAS,GAAGJ,KAAK,CAACI,SAAS;IAC3BC,OAAO,GAAGL,KAAK,CAACK,OAAO;EAC3B,IAAIC,OAAO,GAAG,EAAE,CAACC,MAAM,CAACH,SAAS,IAAI,EAAE,EAAE,UAAU,CAAC;EAEpD,IAAIC,OAAO,EAAE;IACX,OAAO,aAAaT,MAAM,CAAC,SAAS,CAAC,CAACY,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC,EAAEb,SAAS,CAAC,SAAS,CAAC,EAAE;MAClF,aAAa,EAAE;IACjB,CAAC,EAAE,CAAC,CAAC,EAAEG,QAAQ,CAAC,SAAS,CAAC,EAAEE,KAAK,CAAC,CAAC,CAAC;EACtC;EAEA,OAAO,aAAaJ,MAAM,CAAC,SAAS,CAAC,CAACY,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC,EAAEb,SAAS,CAAC,SAAS,CAAC,EAAE;IAClF,aAAa,EAAE;EACjB,CAAC,EAAE,CAAC,CAAC,EAAEG,QAAQ,CAAC,SAAS,CAAC,EAAEE,KAAK,CAAC,EAAE;IAClCI,SAAS,EAAEE;EACb,CAAC,CAAC,EAAE,aAAaV,MAAM,CAAC,SAAS,CAAC,CAACY,aAAa,CAAC,KAAK,EAAE;IACtD,aAAa,EAAE,WAAW;IAC1BJ,SAAS,EAAE;EACb,CAAC,EAAEH,OAAO,CAACE,SAAS,CAAC,CAAC,EAAED,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAGA,QAAQ,GAAG,aAAaN,MAAM,CAAC,SAAS,CAAC,CAACY,aAAa,CAAC,KAAK,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;AAClJ,CAAC;AAEDT,OAAO,CAACU,SAAS,GAAG;EAClBP,QAAQ,EAAEL,UAAU,CAAC,SAAS,CAAC,CAACa,IAAI;EACpCT,OAAO,EAAEJ,UAAU,CAAC,SAAS,CAAC,CAACc,KAAK,CAAC,CAAC,CAAC,CAAC;EACxCR,SAAS,EAAEN,UAAU,CAAC,SAAS,CAAC,CAACe,MAAM;EACvCR,SAAS,EAAEP,UAAU,CAAC,SAAS,CAAC,CAACgB,MAAM;EACvCR,OAAO,EAAER,UAAU,CAAC,SAAS,CAAC,CAACiB,SAAS,CAAC,CAACjB,UAAU,CAAC,SAAS,CAAC,CAACe,MAAM,EAAEf,UAAU,CAAC,SAAS,CAAC,CAACgB,MAAM,CAAC;AACvG,CAAC;AACDd,OAAO,CAACgB,YAAY,GAAG;EACrBb,QAAQ,EAAEc,SAAS;EACnBf,OAAO,EAAEe,SAAS;EAClBb,SAAS,EAAEa,SAAS;EACpBZ,SAAS,EAAEY,SAAS;EACpBX,OAAO,EAAEW;AACX,CAAC;AACD,IAAIC,QAAQ,GAAGlB,OAAO;AACtBN,OAAO,CAAC,SAAS,CAAC,GAAGwB,QAAQ"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}