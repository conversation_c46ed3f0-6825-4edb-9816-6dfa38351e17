{"name": "@types/caseless", "version": "0.12.2", "description": "TypeScript definitions for caseless", "license": "MIT", "contributors": [{"name": "downace", "url": "https://github.com/downace", "githubUsername": "downace"}, {"name": "<PERSON>", "url": "https://github.com/mastermatt", "githubUsername": "mastermatt"}, {"name": "<PERSON>", "url": "https://github.com/forivall", "githubUsername": "forivall"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/caseless"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "6c31853516bc415abdc2e298beda9673951015730429d225f6ff938b0549b9a6", "typeScriptVersion": "2.2"}