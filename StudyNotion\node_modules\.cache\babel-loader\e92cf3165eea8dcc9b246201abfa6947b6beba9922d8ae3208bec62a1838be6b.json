{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\");\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nvar _classCallCheck2 = _interopRequireDefault(require(\"@babel/runtime/helpers/classCallCheck\"));\nvar _createClass2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createClass\"));\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"@babel/runtime/helpers/possibleConstructorReturn\"));\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"@babel/runtime/helpers/getPrototypeOf\"));\nvar _assertThisInitialized2 = _interopRequireDefault(require(\"@babel/runtime/helpers/assertThisInitialized\"));\nvar _inherits2 = _interopRequireDefault(require(\"@babel/runtime/helpers/inherits\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _react = _interopRequireWildcard(require(\"react\"));\nvar _classnames = _interopRequireDefault(require(\"classnames\"));\nvar propTypes = {\n  actions: _propTypes[\"default\"].object,\n  player: _propTypes[\"default\"].object,\n  position: _propTypes[\"default\"].string,\n  className: _propTypes[\"default\"].string\n};\nvar defaultProps = {\n  position: 'left'\n};\nvar BigPlayButton = /*#__PURE__*/\nfunction (_Component) {\n  (0, _inherits2[\"default\"])(BigPlayButton, _Component);\n  function BigPlayButton(props, context) {\n    var _this;\n    (0, _classCallCheck2[\"default\"])(this, BigPlayButton);\n    _this = (0, _possibleConstructorReturn2[\"default\"])(this, (0, _getPrototypeOf2[\"default\"])(BigPlayButton).call(this, props, context));\n    _this.handleClick = _this.handleClick.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    return _this;\n  }\n  (0, _createClass2[\"default\"])(BigPlayButton, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {}\n  }, {\n    key: \"handleClick\",\n    value: function handleClick() {\n      var actions = this.props.actions;\n      actions.play();\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props = this.props,\n        player = _this$props.player,\n        position = _this$props.position;\n      return _react[\"default\"].createElement(\"button\", {\n        className: (0, _classnames[\"default\"])('video-react-button', 'video-react-big-play-button', \"video-react-big-play-button-\".concat(position), this.props.className, {\n          'big-play-button-hide': player.hasStarted || !player.currentSrc\n        }),\n        type: \"button\",\n        \"aria-live\": \"polite\",\n        tabIndex: \"0\",\n        onClick: this.handleClick\n      }, _react[\"default\"].createElement(\"span\", {\n        className: \"video-react-control-text\"\n      }, \"Play Video\"));\n    }\n  }]);\n  return BigPlayButton;\n}(_react.Component);\nexports[\"default\"] = BigPlayButton;\nBigPlayButton.propTypes = propTypes;\nBigPlayButton.defaultProps = defaultProps;\nBigPlayButton.displayName = 'BigPlayButton';", "map": {"version": 3, "names": ["_interopRequireWildcard", "require", "_interopRequireDefault", "Object", "defineProperty", "exports", "value", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_assertThisInitialized2", "_inherits2", "_propTypes", "_react", "_classnames", "propTypes", "actions", "object", "player", "position", "string", "className", "defaultProps", "BigPlayButton", "_Component", "props", "context", "_this", "call", "handleClick", "bind", "key", "componentDidMount", "play", "render", "_this$props", "createElement", "concat", "hasStarted", "currentSrc", "type", "tabIndex", "onClick", "Component", "displayName"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/video-react/lib/components/BigPlayButton.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\");\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _classCallCheck2 = _interopRequireDefault(require(\"@babel/runtime/helpers/classCallCheck\"));\n\nvar _createClass2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createClass\"));\n\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"@babel/runtime/helpers/possibleConstructorReturn\"));\n\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"@babel/runtime/helpers/getPrototypeOf\"));\n\nvar _assertThisInitialized2 = _interopRequireDefault(require(\"@babel/runtime/helpers/assertThisInitialized\"));\n\nvar _inherits2 = _interopRequireDefault(require(\"@babel/runtime/helpers/inherits\"));\n\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\n\nvar _react = _interopRequireWildcard(require(\"react\"));\n\nvar _classnames = _interopRequireDefault(require(\"classnames\"));\n\nvar propTypes = {\n  actions: _propTypes[\"default\"].object,\n  player: _propTypes[\"default\"].object,\n  position: _propTypes[\"default\"].string,\n  className: _propTypes[\"default\"].string\n};\nvar defaultProps = {\n  position: 'left'\n};\n\nvar BigPlayButton =\n/*#__PURE__*/\nfunction (_Component) {\n  (0, _inherits2[\"default\"])(BigPlayButton, _Component);\n\n  function BigPlayButton(props, context) {\n    var _this;\n\n    (0, _classCallCheck2[\"default\"])(this, BigPlayButton);\n    _this = (0, _possibleConstructorReturn2[\"default\"])(this, (0, _getPrototypeOf2[\"default\"])(BigPlayButton).call(this, props, context));\n    _this.handleClick = _this.handleClick.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    return _this;\n  }\n\n  (0, _createClass2[\"default\"])(BigPlayButton, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {}\n  }, {\n    key: \"handleClick\",\n    value: function handleClick() {\n      var actions = this.props.actions;\n      actions.play();\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props = this.props,\n          player = _this$props.player,\n          position = _this$props.position;\n      return _react[\"default\"].createElement(\"button\", {\n        className: (0, _classnames[\"default\"])('video-react-button', 'video-react-big-play-button', \"video-react-big-play-button-\".concat(position), this.props.className, {\n          'big-play-button-hide': player.hasStarted || !player.currentSrc\n        }),\n        type: \"button\",\n        \"aria-live\": \"polite\",\n        tabIndex: \"0\",\n        onClick: this.handleClick\n      }, _react[\"default\"].createElement(\"span\", {\n        className: \"video-react-control-text\"\n      }, \"Play Video\"));\n    }\n  }]);\n  return BigPlayButton;\n}(_react.Component);\n\nexports[\"default\"] = BigPlayButton;\nBigPlayButton.propTypes = propTypes;\nBigPlayButton.defaultProps = defaultProps;\nBigPlayButton.displayName = 'BigPlayButton';"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,uBAAuB,GAAGC,OAAO,CAAC,+CAA+C,CAAC;AAEtF,IAAIC,sBAAsB,GAAGD,OAAO,CAAC,8CAA8C,CAAC;AAEpFE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;AAE3B,IAAIE,gBAAgB,GAAGL,sBAAsB,CAACD,OAAO,CAAC,uCAAuC,CAAC,CAAC;AAE/F,IAAIO,aAAa,GAAGN,sBAAsB,CAACD,OAAO,CAAC,oCAAoC,CAAC,CAAC;AAEzF,IAAIQ,2BAA2B,GAAGP,sBAAsB,CAACD,OAAO,CAAC,kDAAkD,CAAC,CAAC;AAErH,IAAIS,gBAAgB,GAAGR,sBAAsB,CAACD,OAAO,CAAC,uCAAuC,CAAC,CAAC;AAE/F,IAAIU,uBAAuB,GAAGT,sBAAsB,CAACD,OAAO,CAAC,8CAA8C,CAAC,CAAC;AAE7G,IAAIW,UAAU,GAAGV,sBAAsB,CAACD,OAAO,CAAC,iCAAiC,CAAC,CAAC;AAEnF,IAAIY,UAAU,GAAGX,sBAAsB,CAACD,OAAO,CAAC,YAAY,CAAC,CAAC;AAE9D,IAAIa,MAAM,GAAGd,uBAAuB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AAEtD,IAAIc,WAAW,GAAGb,sBAAsB,CAACD,OAAO,CAAC,YAAY,CAAC,CAAC;AAE/D,IAAIe,SAAS,GAAG;EACdC,OAAO,EAAEJ,UAAU,CAAC,SAAS,CAAC,CAACK,MAAM;EACrCC,MAAM,EAAEN,UAAU,CAAC,SAAS,CAAC,CAACK,MAAM;EACpCE,QAAQ,EAAEP,UAAU,CAAC,SAAS,CAAC,CAACQ,MAAM;EACtCC,SAAS,EAAET,UAAU,CAAC,SAAS,CAAC,CAACQ;AACnC,CAAC;AACD,IAAIE,YAAY,GAAG;EACjBH,QAAQ,EAAE;AACZ,CAAC;AAED,IAAII,aAAa,GACjB;AACA,UAAUC,UAAU,EAAE;EACpB,CAAC,CAAC,EAAEb,UAAU,CAAC,SAAS,CAAC,EAAEY,aAAa,EAAEC,UAAU,CAAC;EAErD,SAASD,aAAaA,CAACE,KAAK,EAAEC,OAAO,EAAE;IACrC,IAAIC,KAAK;IAET,CAAC,CAAC,EAAErB,gBAAgB,CAAC,SAAS,CAAC,EAAE,IAAI,EAAEiB,aAAa,CAAC;IACrDI,KAAK,GAAG,CAAC,CAAC,EAAEnB,2BAA2B,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAEC,gBAAgB,CAAC,SAAS,CAAC,EAAEc,aAAa,CAAC,CAACK,IAAI,CAAC,IAAI,EAAEH,KAAK,EAAEC,OAAO,CAAC,CAAC;IACrIC,KAAK,CAACE,WAAW,GAAGF,KAAK,CAACE,WAAW,CAACC,IAAI,CAAC,CAAC,CAAC,EAAEpB,uBAAuB,CAAC,SAAS,CAAC,EAAEiB,KAAK,CAAC,CAAC;IAC1F,OAAOA,KAAK;EACd;EAEA,CAAC,CAAC,EAAEpB,aAAa,CAAC,SAAS,CAAC,EAAEgB,aAAa,EAAE,CAAC;IAC5CQ,GAAG,EAAE,mBAAmB;IACxB1B,KAAK,EAAE,SAAS2B,iBAAiBA,CAAA,EAAG,CAAC;EACvC,CAAC,EAAE;IACDD,GAAG,EAAE,aAAa;IAClB1B,KAAK,EAAE,SAASwB,WAAWA,CAAA,EAAG;MAC5B,IAAIb,OAAO,GAAG,IAAI,CAACS,KAAK,CAACT,OAAO;MAChCA,OAAO,CAACiB,IAAI,EAAE;IAChB;EACF,CAAC,EAAE;IACDF,GAAG,EAAE,QAAQ;IACb1B,KAAK,EAAE,SAAS6B,MAAMA,CAAA,EAAG;MACvB,IAAIC,WAAW,GAAG,IAAI,CAACV,KAAK;QACxBP,MAAM,GAAGiB,WAAW,CAACjB,MAAM;QAC3BC,QAAQ,GAAGgB,WAAW,CAAChB,QAAQ;MACnC,OAAON,MAAM,CAAC,SAAS,CAAC,CAACuB,aAAa,CAAC,QAAQ,EAAE;QAC/Cf,SAAS,EAAE,CAAC,CAAC,EAAEP,WAAW,CAAC,SAAS,CAAC,EAAE,oBAAoB,EAAE,6BAA6B,EAAE,8BAA8B,CAACuB,MAAM,CAAClB,QAAQ,CAAC,EAAE,IAAI,CAACM,KAAK,CAACJ,SAAS,EAAE;UACjK,sBAAsB,EAAEH,MAAM,CAACoB,UAAU,IAAI,CAACpB,MAAM,CAACqB;QACvD,CAAC,CAAC;QACFC,IAAI,EAAE,QAAQ;QACd,WAAW,EAAE,QAAQ;QACrBC,QAAQ,EAAE,GAAG;QACbC,OAAO,EAAE,IAAI,CAACb;MAChB,CAAC,EAAEhB,MAAM,CAAC,SAAS,CAAC,CAACuB,aAAa,CAAC,MAAM,EAAE;QACzCf,SAAS,EAAE;MACb,CAAC,EAAE,YAAY,CAAC,CAAC;IACnB;EACF,CAAC,CAAC,CAAC;EACH,OAAOE,aAAa;AACtB,CAAC,CAACV,MAAM,CAAC8B,SAAS,CAAC;AAEnBvC,OAAO,CAAC,SAAS,CAAC,GAAGmB,aAAa;AAClCA,aAAa,CAACR,SAAS,GAAGA,SAAS;AACnCQ,aAAa,CAACD,YAAY,GAAGA,YAAY;AACzCC,aAAa,CAACqB,WAAW,GAAG,eAAe"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}