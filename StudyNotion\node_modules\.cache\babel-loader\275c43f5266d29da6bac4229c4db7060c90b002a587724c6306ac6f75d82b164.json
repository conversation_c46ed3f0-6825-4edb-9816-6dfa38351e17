{"ast": null, "code": "/**\n * @typedef {import('micromark-util-types').Code} Code\n * @typedef {import('micromark-util-types').Construct} Construct\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Tokenizer} Tokenizer\n */\n\nimport { factorySpace } from 'micromark-factory-space';\nimport { markdownLineEnding, markdownSpace } from 'micromark-util-character';\nimport { codes } from 'micromark-util-symbol/codes.js';\nimport { constants } from 'micromark-util-symbol/constants.js';\nimport { types } from 'micromark-util-symbol/types.js';\nimport { ok as assert } from 'uvu/assert';\n\n/** @type {Construct} */\nexport const thematicBreak = {\n  name: 'thematicBreak',\n  tokenize: tokenizeThematicBreak\n};\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeThematicBreak(effects, ok, nok) {\n  let size = 0;\n  /** @type {NonNullable<Code>} */\n  let marker;\n  return start;\n\n  /**\n   * Start of thematic break.\n   *\n   * ```markdown\n   * > | ***\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    effects.enter(types.thematicBreak);\n    // To do: parse indent like `markdown-rs`.\n    return before(code);\n  }\n\n  /**\n   * After optional whitespace, at marker.\n   *\n   * ```markdown\n   * > | ***\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function before(code) {\n    assert(code === codes.asterisk || code === codes.dash || code === codes.underscore, 'expected `*`, `-`, or `_`');\n    marker = code;\n    return atBreak(code);\n  }\n\n  /**\n   * After something, before something else.\n   *\n   * ```markdown\n   * > | ***\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function atBreak(code) {\n    if (code === marker) {\n      effects.enter(types.thematicBreakSequence);\n      return sequence(code);\n    }\n    if (size >= constants.thematicBreakMarkerCountMin && (code === codes.eof || markdownLineEnding(code))) {\n      effects.exit(types.thematicBreak);\n      return ok(code);\n    }\n    return nok(code);\n  }\n\n  /**\n   * In sequence.\n   *\n   * ```markdown\n   * > | ***\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function sequence(code) {\n    if (code === marker) {\n      effects.consume(code);\n      size++;\n      return sequence;\n    }\n    effects.exit(types.thematicBreakSequence);\n    return markdownSpace(code) ? factorySpace(effects, atBreak, types.whitespace)(code) : atBreak(code);\n  }\n}", "map": {"version": 3, "names": ["factorySpace", "markdownLineEnding", "markdownSpace", "codes", "constants", "types", "ok", "assert", "thematicBreak", "name", "tokenize", "tokenizeThematicBreak", "effects", "nok", "size", "marker", "start", "code", "enter", "before", "asterisk", "dash", "underscore", "atBreak", "thematicBreakSequence", "sequence", "thematicBreakMarkerCountMin", "eof", "exit", "consume", "whitespace"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/micromark-core-commonmark/dev/lib/thematic-break.js"], "sourcesContent": ["/**\n * @typedef {import('micromark-util-types').Code} Code\n * @typedef {import('micromark-util-types').Construct} Construct\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Tokenizer} Tokenizer\n */\n\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownLineEnding, markdownSpace} from 'micromark-util-character'\nimport {codes} from 'micromark-util-symbol/codes.js'\nimport {constants} from 'micromark-util-symbol/constants.js'\nimport {types} from 'micromark-util-symbol/types.js'\nimport {ok as assert} from 'uvu/assert'\n\n/** @type {Construct} */\nexport const thematicBreak = {\n  name: 'thematicBreak',\n  tokenize: tokenizeThematicBreak\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeThematicBreak(effects, ok, nok) {\n  let size = 0\n  /** @type {NonNullable<Code>} */\n  let marker\n\n  return start\n\n  /**\n   * Start of thematic break.\n   *\n   * ```markdown\n   * > | ***\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    effects.enter(types.thematicBreak)\n    // To do: parse indent like `markdown-rs`.\n    return before(code)\n  }\n\n  /**\n   * After optional whitespace, at marker.\n   *\n   * ```markdown\n   * > | ***\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function before(code) {\n    assert(\n      code === codes.asterisk ||\n        code === codes.dash ||\n        code === codes.underscore,\n      'expected `*`, `-`, or `_`'\n    )\n    marker = code\n    return atBreak(code)\n  }\n\n  /**\n   * After something, before something else.\n   *\n   * ```markdown\n   * > | ***\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function atBreak(code) {\n    if (code === marker) {\n      effects.enter(types.thematicBreakSequence)\n      return sequence(code)\n    }\n\n    if (\n      size >= constants.thematicBreakMarkerCountMin &&\n      (code === codes.eof || markdownLineEnding(code))\n    ) {\n      effects.exit(types.thematicBreak)\n      return ok(code)\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In sequence.\n   *\n   * ```markdown\n   * > | ***\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function sequence(code) {\n    if (code === marker) {\n      effects.consume(code)\n      size++\n      return sequence\n    }\n\n    effects.exit(types.thematicBreakSequence)\n    return markdownSpace(code)\n      ? factorySpace(effects, atBreak, types.whitespace)(code)\n      : atBreak(code)\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,YAAY,QAAO,yBAAyB;AACpD,SAAQC,kBAAkB,EAAEC,aAAa,QAAO,0BAA0B;AAC1E,SAAQC,KAAK,QAAO,gCAAgC;AACpD,SAAQC,SAAS,QAAO,oCAAoC;AAC5D,SAAQC,KAAK,QAAO,gCAAgC;AACpD,SAAQC,EAAE,IAAIC,MAAM,QAAO,YAAY;;AAEvC;AACA,OAAO,MAAMC,aAAa,GAAG;EAC3BC,IAAI,EAAE,eAAe;EACrBC,QAAQ,EAAEC;AACZ,CAAC;;AAED;AACA;AACA;AACA;AACA,SAASA,qBAAqBA,CAACC,OAAO,EAAEN,EAAE,EAAEO,GAAG,EAAE;EAC/C,IAAIC,IAAI,GAAG,CAAC;EACZ;EACA,IAAIC,MAAM;EAEV,OAAOC,KAAK;;EAEZ;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,KAAKA,CAACC,IAAI,EAAE;IACnBL,OAAO,CAACM,KAAK,CAACb,KAAK,CAACG,aAAa,CAAC;IAClC;IACA,OAAOW,MAAM,CAACF,IAAI,CAAC;EACrB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASE,MAAMA,CAACF,IAAI,EAAE;IACpBV,MAAM,CACJU,IAAI,KAAKd,KAAK,CAACiB,QAAQ,IACrBH,IAAI,KAAKd,KAAK,CAACkB,IAAI,IACnBJ,IAAI,KAAKd,KAAK,CAACmB,UAAU,EAC3B,2BAA2B,CAC5B;IACDP,MAAM,GAAGE,IAAI;IACb,OAAOM,OAAO,CAACN,IAAI,CAAC;EACtB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASM,OAAOA,CAACN,IAAI,EAAE;IACrB,IAAIA,IAAI,KAAKF,MAAM,EAAE;MACnBH,OAAO,CAACM,KAAK,CAACb,KAAK,CAACmB,qBAAqB,CAAC;MAC1C,OAAOC,QAAQ,CAACR,IAAI,CAAC;IACvB;IAEA,IACEH,IAAI,IAAIV,SAAS,CAACsB,2BAA2B,KAC5CT,IAAI,KAAKd,KAAK,CAACwB,GAAG,IAAI1B,kBAAkB,CAACgB,IAAI,CAAC,CAAC,EAChD;MACAL,OAAO,CAACgB,IAAI,CAACvB,KAAK,CAACG,aAAa,CAAC;MACjC,OAAOF,EAAE,CAACW,IAAI,CAAC;IACjB;IAEA,OAAOJ,GAAG,CAACI,IAAI,CAAC;EAClB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASQ,QAAQA,CAACR,IAAI,EAAE;IACtB,IAAIA,IAAI,KAAKF,MAAM,EAAE;MACnBH,OAAO,CAACiB,OAAO,CAACZ,IAAI,CAAC;MACrBH,IAAI,EAAE;MACN,OAAOW,QAAQ;IACjB;IAEAb,OAAO,CAACgB,IAAI,CAACvB,KAAK,CAACmB,qBAAqB,CAAC;IACzC,OAAOtB,aAAa,CAACe,IAAI,CAAC,GACtBjB,YAAY,CAACY,OAAO,EAAEW,OAAO,EAAElB,KAAK,CAACyB,UAAU,CAAC,CAACb,IAAI,CAAC,GACtDM,OAAO,CAACN,IAAI,CAAC;EACnB;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}