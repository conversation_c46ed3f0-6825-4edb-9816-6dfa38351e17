{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = player;\nvar _objectSpread2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectSpread\"));\nvar _video = require(\"../actions/video\");\nvar _player = require(\"../actions/player\");\nvar initialState = {\n  currentSrc: null,\n  duration: 0,\n  currentTime: 0,\n  seekingTime: 0,\n  buffered: null,\n  waiting: false,\n  seeking: false,\n  paused: true,\n  autoPaused: false,\n  ended: false,\n  playbackRate: 1,\n  muted: false,\n  volume: 1,\n  readyState: 0,\n  networkState: 0,\n  videoWidth: 0,\n  videoHeight: 0,\n  hasStarted: false,\n  userActivity: true,\n  isActive: false,\n  isFullscreen: false,\n  activeTextTrack: null\n};\nfunction player() {\n  var state = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : initialState;\n  var action = arguments.length > 1 ? arguments[1] : undefined;\n  switch (action.type) {\n    case _player.USER_ACTIVATE:\n      return (0, _objectSpread2[\"default\"])({}, state, {\n        userActivity: action.activity\n      });\n    case _player.PLAYER_ACTIVATE:\n      return (0, _objectSpread2[\"default\"])({}, state, {\n        isActive: action.activity\n      });\n    case _player.FULLSCREEN_CHANGE:\n      return (0, _objectSpread2[\"default\"])({}, state, {\n        isFullscreen: !!action.isFullscreen\n      });\n    case _video.SEEKING_TIME:\n      return (0, _objectSpread2[\"default\"])({}, state, {\n        seekingTime: action.time\n      });\n    case _video.END_SEEKING:\n      return (0, _objectSpread2[\"default\"])({}, state, {\n        seekingTime: 0\n      });\n    case _video.LOAD_START:\n      return (0, _objectSpread2[\"default\"])({}, state, action.videoProps, {\n        hasStarted: false,\n        ended: false\n      });\n    case _video.CAN_PLAY:\n      return (0, _objectSpread2[\"default\"])({}, state, action.videoProps, {\n        waiting: false\n      });\n    case _video.WAITING:\n      return (0, _objectSpread2[\"default\"])({}, state, action.videoProps, {\n        waiting: true\n      });\n    case _video.CAN_PLAY_THROUGH:\n    case _video.PLAYING:\n      return (0, _objectSpread2[\"default\"])({}, state, action.videoProps, {\n        waiting: false\n      });\n    case _video.PLAY:\n      return (0, _objectSpread2[\"default\"])({}, state, action.videoProps, {\n        ended: false,\n        paused: false,\n        autoPaused: false,\n        waiting: false,\n        hasStarted: true\n      });\n    case _video.PAUSE:\n      return (0, _objectSpread2[\"default\"])({}, state, action.videoProps, {\n        paused: true\n      });\n    case _video.END:\n      return (0, _objectSpread2[\"default\"])({}, state, action.videoProps, {\n        ended: true\n      });\n    case _video.SEEKING:\n      return (0, _objectSpread2[\"default\"])({}, state, action.videoProps, {\n        seeking: true\n      });\n    case _video.SEEKED:\n      return (0, _objectSpread2[\"default\"])({}, state, action.videoProps, {\n        seeking: false\n      });\n    case _video.ERROR:\n      return (0, _objectSpread2[\"default\"])({}, state, action.videoProps, {\n        error: 'UNKNOWN ERROR',\n        ended: true\n      });\n    case _video.DURATION_CHANGE:\n    case _video.TIME_UPDATE:\n    case _video.VOLUME_CHANGE:\n    case _video.PROGRESS_CHANGE:\n    case _video.RATE_CHANGE:\n    case _video.SUSPEND:\n    case _video.ABORT:\n    case _video.EMPTIED:\n    case _video.STALLED:\n    case _video.LOADED_META_DATA:\n    case _video.LOADED_DATA:\n    case _video.RESIZE:\n      return (0, _objectSpread2[\"default\"])({}, state, action.videoProps);\n    case _video.ACTIVATE_TEXT_TRACK:\n      return (0, _objectSpread2[\"default\"])({}, state, {\n        activeTextTrack: action.textTrack\n      });\n    default:\n      return state;\n  }\n}", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "player", "_objectSpread2", "_video", "_player", "initialState", "currentSrc", "duration", "currentTime", "seekingTime", "buffered", "waiting", "seeking", "paused", "autoPaused", "ended", "playbackRate", "muted", "volume", "readyState", "networkState", "videoWidth", "videoHeight", "hasStarted", "userActivity", "isActive", "isFullscreen", "activeTextTrack", "state", "arguments", "length", "undefined", "action", "type", "USER_ACTIVATE", "activity", "PLAYER_ACTIVATE", "FULLSCREEN_CHANGE", "SEEKING_TIME", "time", "END_SEEKING", "LOAD_START", "videoProps", "CAN_PLAY", "WAITING", "CAN_PLAY_THROUGH", "PLAYING", "PLAY", "PAUSE", "END", "SEEKING", "SEEKED", "ERROR", "error", "DURATION_CHANGE", "TIME_UPDATE", "VOLUME_CHANGE", "PROGRESS_CHANGE", "RATE_CHANGE", "SUSPEND", "ABORT", "EMPTIED", "STALLED", "LOADED_META_DATA", "LOADED_DATA", "RESIZE", "ACTIVATE_TEXT_TRACK", "textTrack"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/video-react/lib/reducers/player.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = player;\n\nvar _objectSpread2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectSpread\"));\n\nvar _video = require(\"../actions/video\");\n\nvar _player = require(\"../actions/player\");\n\nvar initialState = {\n  currentSrc: null,\n  duration: 0,\n  currentTime: 0,\n  seekingTime: 0,\n  buffered: null,\n  waiting: false,\n  seeking: false,\n  paused: true,\n  autoPaused: false,\n  ended: false,\n  playbackRate: 1,\n  muted: false,\n  volume: 1,\n  readyState: 0,\n  networkState: 0,\n  videoWidth: 0,\n  videoHeight: 0,\n  hasStarted: false,\n  userActivity: true,\n  isActive: false,\n  isFullscreen: false,\n  activeTextTrack: null\n};\n\nfunction player() {\n  var state = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : initialState;\n  var action = arguments.length > 1 ? arguments[1] : undefined;\n\n  switch (action.type) {\n    case _player.USER_ACTIVATE:\n      return (0, _objectSpread2[\"default\"])({}, state, {\n        userActivity: action.activity\n      });\n\n    case _player.PLAYER_ACTIVATE:\n      return (0, _objectSpread2[\"default\"])({}, state, {\n        isActive: action.activity\n      });\n\n    case _player.FULLSCREEN_CHANGE:\n      return (0, _objectSpread2[\"default\"])({}, state, {\n        isFullscreen: !!action.isFullscreen\n      });\n\n    case _video.SEEKING_TIME:\n      return (0, _objectSpread2[\"default\"])({}, state, {\n        seekingTime: action.time\n      });\n\n    case _video.END_SEEKING:\n      return (0, _objectSpread2[\"default\"])({}, state, {\n        seekingTime: 0\n      });\n\n    case _video.LOAD_START:\n      return (0, _objectSpread2[\"default\"])({}, state, action.videoProps, {\n        hasStarted: false,\n        ended: false\n      });\n\n    case _video.CAN_PLAY:\n      return (0, _objectSpread2[\"default\"])({}, state, action.videoProps, {\n        waiting: false\n      });\n\n    case _video.WAITING:\n      return (0, _objectSpread2[\"default\"])({}, state, action.videoProps, {\n        waiting: true\n      });\n\n    case _video.CAN_PLAY_THROUGH:\n    case _video.PLAYING:\n      return (0, _objectSpread2[\"default\"])({}, state, action.videoProps, {\n        waiting: false\n      });\n\n    case _video.PLAY:\n      return (0, _objectSpread2[\"default\"])({}, state, action.videoProps, {\n        ended: false,\n        paused: false,\n        autoPaused: false,\n        waiting: false,\n        hasStarted: true\n      });\n\n    case _video.PAUSE:\n      return (0, _objectSpread2[\"default\"])({}, state, action.videoProps, {\n        paused: true\n      });\n\n    case _video.END:\n      return (0, _objectSpread2[\"default\"])({}, state, action.videoProps, {\n        ended: true\n      });\n\n    case _video.SEEKING:\n      return (0, _objectSpread2[\"default\"])({}, state, action.videoProps, {\n        seeking: true\n      });\n\n    case _video.SEEKED:\n      return (0, _objectSpread2[\"default\"])({}, state, action.videoProps, {\n        seeking: false\n      });\n\n    case _video.ERROR:\n      return (0, _objectSpread2[\"default\"])({}, state, action.videoProps, {\n        error: 'UNKNOWN ERROR',\n        ended: true\n      });\n\n    case _video.DURATION_CHANGE:\n    case _video.TIME_UPDATE:\n    case _video.VOLUME_CHANGE:\n    case _video.PROGRESS_CHANGE:\n    case _video.RATE_CHANGE:\n    case _video.SUSPEND:\n    case _video.ABORT:\n    case _video.EMPTIED:\n    case _video.STALLED:\n    case _video.LOADED_META_DATA:\n    case _video.LOADED_DATA:\n    case _video.RESIZE:\n      return (0, _objectSpread2[\"default\"])({}, state, action.videoProps);\n\n    case _video.ACTIVATE_TEXT_TRACK:\n      return (0, _objectSpread2[\"default\"])({}, state, {\n        activeTextTrack: action.textTrack\n      });\n\n    default:\n      return state;\n  }\n}"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC;AAEpFC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAAC,SAAS,CAAC,GAAGE,MAAM;AAE3B,IAAIC,cAAc,GAAGP,sBAAsB,CAACC,OAAO,CAAC,qCAAqC,CAAC,CAAC;AAE3F,IAAIO,MAAM,GAAGP,OAAO,CAAC,kBAAkB,CAAC;AAExC,IAAIQ,OAAO,GAAGR,OAAO,CAAC,mBAAmB,CAAC;AAE1C,IAAIS,YAAY,GAAG;EACjBC,UAAU,EAAE,IAAI;EAChBC,QAAQ,EAAE,CAAC;EACXC,WAAW,EAAE,CAAC;EACdC,WAAW,EAAE,CAAC;EACdC,QAAQ,EAAE,IAAI;EACdC,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE,KAAK;EACdC,MAAM,EAAE,IAAI;EACZC,UAAU,EAAE,KAAK;EACjBC,KAAK,EAAE,KAAK;EACZC,YAAY,EAAE,CAAC;EACfC,KAAK,EAAE,KAAK;EACZC,MAAM,EAAE,CAAC;EACTC,UAAU,EAAE,CAAC;EACbC,YAAY,EAAE,CAAC;EACfC,UAAU,EAAE,CAAC;EACbC,WAAW,EAAE,CAAC;EACdC,UAAU,EAAE,KAAK;EACjBC,YAAY,EAAE,IAAI;EAClBC,QAAQ,EAAE,KAAK;EACfC,YAAY,EAAE,KAAK;EACnBC,eAAe,EAAE;AACnB,CAAC;AAED,SAAS1B,MAAMA,CAAA,EAAG;EAChB,IAAI2B,KAAK,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAGxB,YAAY;EAC5F,IAAI2B,MAAM,GAAGH,SAAS,CAACC,MAAM,GAAG,CAAC,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAGE,SAAS;EAE5D,QAAQC,MAAM,CAACC,IAAI;IACjB,KAAK7B,OAAO,CAAC8B,aAAa;MACxB,OAAO,CAAC,CAAC,EAAEhC,cAAc,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE0B,KAAK,EAAE;QAC/CJ,YAAY,EAAEQ,MAAM,CAACG;MACvB,CAAC,CAAC;IAEJ,KAAK/B,OAAO,CAACgC,eAAe;MAC1B,OAAO,CAAC,CAAC,EAAElC,cAAc,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE0B,KAAK,EAAE;QAC/CH,QAAQ,EAAEO,MAAM,CAACG;MACnB,CAAC,CAAC;IAEJ,KAAK/B,OAAO,CAACiC,iBAAiB;MAC5B,OAAO,CAAC,CAAC,EAAEnC,cAAc,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE0B,KAAK,EAAE;QAC/CF,YAAY,EAAE,CAAC,CAACM,MAAM,CAACN;MACzB,CAAC,CAAC;IAEJ,KAAKvB,MAAM,CAACmC,YAAY;MACtB,OAAO,CAAC,CAAC,EAAEpC,cAAc,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE0B,KAAK,EAAE;QAC/CnB,WAAW,EAAEuB,MAAM,CAACO;MACtB,CAAC,CAAC;IAEJ,KAAKpC,MAAM,CAACqC,WAAW;MACrB,OAAO,CAAC,CAAC,EAAEtC,cAAc,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE0B,KAAK,EAAE;QAC/CnB,WAAW,EAAE;MACf,CAAC,CAAC;IAEJ,KAAKN,MAAM,CAACsC,UAAU;MACpB,OAAO,CAAC,CAAC,EAAEvC,cAAc,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE0B,KAAK,EAAEI,MAAM,CAACU,UAAU,EAAE;QAClEnB,UAAU,EAAE,KAAK;QACjBR,KAAK,EAAE;MACT,CAAC,CAAC;IAEJ,KAAKZ,MAAM,CAACwC,QAAQ;MAClB,OAAO,CAAC,CAAC,EAAEzC,cAAc,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE0B,KAAK,EAAEI,MAAM,CAACU,UAAU,EAAE;QAClE/B,OAAO,EAAE;MACX,CAAC,CAAC;IAEJ,KAAKR,MAAM,CAACyC,OAAO;MACjB,OAAO,CAAC,CAAC,EAAE1C,cAAc,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE0B,KAAK,EAAEI,MAAM,CAACU,UAAU,EAAE;QAClE/B,OAAO,EAAE;MACX,CAAC,CAAC;IAEJ,KAAKR,MAAM,CAAC0C,gBAAgB;IAC5B,KAAK1C,MAAM,CAAC2C,OAAO;MACjB,OAAO,CAAC,CAAC,EAAE5C,cAAc,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE0B,KAAK,EAAEI,MAAM,CAACU,UAAU,EAAE;QAClE/B,OAAO,EAAE;MACX,CAAC,CAAC;IAEJ,KAAKR,MAAM,CAAC4C,IAAI;MACd,OAAO,CAAC,CAAC,EAAE7C,cAAc,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE0B,KAAK,EAAEI,MAAM,CAACU,UAAU,EAAE;QAClE3B,KAAK,EAAE,KAAK;QACZF,MAAM,EAAE,KAAK;QACbC,UAAU,EAAE,KAAK;QACjBH,OAAO,EAAE,KAAK;QACdY,UAAU,EAAE;MACd,CAAC,CAAC;IAEJ,KAAKpB,MAAM,CAAC6C,KAAK;MACf,OAAO,CAAC,CAAC,EAAE9C,cAAc,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE0B,KAAK,EAAEI,MAAM,CAACU,UAAU,EAAE;QAClE7B,MAAM,EAAE;MACV,CAAC,CAAC;IAEJ,KAAKV,MAAM,CAAC8C,GAAG;MACb,OAAO,CAAC,CAAC,EAAE/C,cAAc,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE0B,KAAK,EAAEI,MAAM,CAACU,UAAU,EAAE;QAClE3B,KAAK,EAAE;MACT,CAAC,CAAC;IAEJ,KAAKZ,MAAM,CAAC+C,OAAO;MACjB,OAAO,CAAC,CAAC,EAAEhD,cAAc,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE0B,KAAK,EAAEI,MAAM,CAACU,UAAU,EAAE;QAClE9B,OAAO,EAAE;MACX,CAAC,CAAC;IAEJ,KAAKT,MAAM,CAACgD,MAAM;MAChB,OAAO,CAAC,CAAC,EAAEjD,cAAc,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE0B,KAAK,EAAEI,MAAM,CAACU,UAAU,EAAE;QAClE9B,OAAO,EAAE;MACX,CAAC,CAAC;IAEJ,KAAKT,MAAM,CAACiD,KAAK;MACf,OAAO,CAAC,CAAC,EAAElD,cAAc,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE0B,KAAK,EAAEI,MAAM,CAACU,UAAU,EAAE;QAClEW,KAAK,EAAE,eAAe;QACtBtC,KAAK,EAAE;MACT,CAAC,CAAC;IAEJ,KAAKZ,MAAM,CAACmD,eAAe;IAC3B,KAAKnD,MAAM,CAACoD,WAAW;IACvB,KAAKpD,MAAM,CAACqD,aAAa;IACzB,KAAKrD,MAAM,CAACsD,eAAe;IAC3B,KAAKtD,MAAM,CAACuD,WAAW;IACvB,KAAKvD,MAAM,CAACwD,OAAO;IACnB,KAAKxD,MAAM,CAACyD,KAAK;IACjB,KAAKzD,MAAM,CAAC0D,OAAO;IACnB,KAAK1D,MAAM,CAAC2D,OAAO;IACnB,KAAK3D,MAAM,CAAC4D,gBAAgB;IAC5B,KAAK5D,MAAM,CAAC6D,WAAW;IACvB,KAAK7D,MAAM,CAAC8D,MAAM;MAChB,OAAO,CAAC,CAAC,EAAE/D,cAAc,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE0B,KAAK,EAAEI,MAAM,CAACU,UAAU,CAAC;IAErE,KAAKvC,MAAM,CAAC+D,mBAAmB;MAC7B,OAAO,CAAC,CAAC,EAAEhE,cAAc,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE0B,KAAK,EAAE;QAC/CD,eAAe,EAAEK,MAAM,CAACmC;MAC1B,CAAC,CAAC;IAEJ;MACE,OAAOvC,KAAK;EAAC;AAEnB"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}