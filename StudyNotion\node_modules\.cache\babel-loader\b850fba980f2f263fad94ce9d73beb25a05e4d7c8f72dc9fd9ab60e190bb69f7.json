{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\temp\\\\StudyNotion\\\\src\\\\components\\\\core\\\\Auth\\\\LoginForm.jsx\",\n  _s = $RefreshSig$();\nimport { useState } from \"react\";\nimport { AiOutlineEye, AiOutlineEyeInvisible } from \"react-icons/ai\";\nimport { useDispatch } from \"react-redux\";\nimport { Link, useNavigate } from \"react-router-dom\";\nimport { login } from \"../../../services/operations/authAPI\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction LoginForm() {\n  _s();\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const [formData, setFormData] = useState({\n    email: \"\",\n    password: \"\"\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const {\n    email,\n    password\n  } = formData;\n  const handleOnChange = e => {\n    setFormData(prevData => ({\n      ...prevData,\n      [e.target.name]: e.target.value\n    }));\n  };\n  const handleOnSubmit = e => {\n    e.preventDefault();\n    dispatch(login(email, password, navigate));\n  };\n  return /*#__PURE__*/_jsxDEV(\"form\", {\n    onSubmit: handleOnSubmit,\n    className: \"mt-6 flex w-full flex-col gap-y-4\",\n    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n      className: \"w-full\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"mb-1 text-[0.875rem] leading-[1.375rem] text-richblack-5\",\n        children: [\"Email Address \", /*#__PURE__*/_jsxDEV(\"sup\", {\n          className: \"text-pink-200\",\n          children: \"*\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        required: true,\n        type: \"text\",\n        name: \"email\",\n        value: email,\n        onChange: handleOnChange,\n        placeholder: \"Enter email address\",\n        style: {\n          boxShadow: \"inset 0px -1px 0px rgba(255, 255, 255, 0.18)\"\n        },\n        className: \"w-full rounded-[0.5rem] bg-richblack-800 p-[12px] text-richblack-5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n      className: \"relative\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"mb-1 text-[0.875rem] leading-[1.375rem] text-richblack-5\",\n        children: [\"Password \", /*#__PURE__*/_jsxDEV(\"sup\", {\n          className: \"text-pink-200\",\n          children: \"*\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 20\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        required: true,\n        type: showPassword ? \"text\" : \"password\",\n        name: \"password\",\n        value: password,\n        onChange: handleOnChange,\n        placeholder: \"Enter Password\",\n        style: {\n          boxShadow: \"inset 0px -1px 0px rgba(255, 255, 255, 0.18)\"\n        },\n        className: \"w-full rounded-[0.5rem] bg-richblack-800 p-[12px] pr-12 text-richblack-5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        onClick: () => setShowPassword(prev => !prev),\n        className: \"absolute right-3 top-[38px] z-[10] cursor-pointer\",\n        children: showPassword ? /*#__PURE__*/_jsxDEV(AiOutlineEyeInvisible, {\n          fontSize: 24,\n          fill: \"#AFB2BF\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(AiOutlineEye, {\n          fontSize: 24,\n          fill: \"#AFB2BF\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/forgot-password\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-1 ml-auto max-w-max text-xs text-blue-100\",\n          children: \"Forgot Password\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      type: \"submit\",\n      className: \"mt-6 rounded-[8px] bg-yellow-50 py-[8px] px-[12px] font-medium text-richblack-900\",\n      children: \"Sign In\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 86,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 33,\n    columnNumber: 5\n  }, this);\n}\n_s(LoginForm, \"cl9caf8Ct8ufhZmTSavh/hvkhnQ=\", false, function () {\n  return [useNavigate, useDispatch];\n});\n_c = LoginForm;\nexport default LoginForm;\nvar _c;\n$RefreshReg$(_c, \"LoginForm\");", "map": {"version": 3, "names": ["useState", "AiOutlineEye", "AiOutlineEyeInvisible", "useDispatch", "Link", "useNavigate", "login", "jsxDEV", "_jsxDEV", "LoginForm", "_s", "navigate", "dispatch", "formData", "setFormData", "email", "password", "showPassword", "setShowPassword", "handleOnChange", "e", "prevData", "target", "name", "value", "handleOnSubmit", "preventDefault", "onSubmit", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "required", "type", "onChange", "placeholder", "style", "boxShadow", "onClick", "prev", "fontSize", "fill", "to", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/src/components/core/Auth/LoginForm.jsx"], "sourcesContent": ["import { useState } from \"react\"\r\nimport { Ai<PERSON>utlineEye, AiOutlineEyeInvisible } from \"react-icons/ai\"\r\nimport { useDispatch } from \"react-redux\"\r\nimport { Link, useNavigate } from \"react-router-dom\"\r\n\r\nimport { login } from \"../../../services/operations/authAPI\"\r\n\r\nfunction LoginForm() {\r\n  const navigate = useNavigate()\r\n  const dispatch = useDispatch()\r\n  const [formData, setFormData] = useState({\r\n    email: \"\",\r\n    password: \"\",\r\n  })\r\n\r\n  const [showPassword, setShowPassword] = useState(false)\r\n\r\n  const { email, password } = formData\r\n\r\n  const handleOnChange = (e) => {\r\n    setFormData((prevData) => ({\r\n      ...prevData,\r\n      [e.target.name]: e.target.value,\r\n    }))\r\n  }\r\n\r\n  const handleOnSubmit = (e) => {\r\n    e.preventDefault()\r\n    dispatch(login(email, password, navigate))\r\n  }\r\n\r\n  return (\r\n    <form\r\n      onSubmit={handleOnSubmit}\r\n      className=\"mt-6 flex w-full flex-col gap-y-4\"\r\n    >\r\n      <label className=\"w-full\">\r\n        <p className=\"mb-1 text-[0.875rem] leading-[1.375rem] text-richblack-5\">\r\n          Email Address <sup className=\"text-pink-200\">*</sup>\r\n        </p>\r\n        <input\r\n          required\r\n          type=\"text\"\r\n          name=\"email\"\r\n          value={email}\r\n          onChange={handleOnChange}\r\n          placeholder=\"Enter email address\"\r\n          style={{\r\n            boxShadow: \"inset 0px -1px 0px rgba(255, 255, 255, 0.18)\",\r\n          }}\r\n          className=\"w-full rounded-[0.5rem] bg-richblack-800 p-[12px] text-richblack-5\"\r\n        />\r\n      </label>\r\n      <label className=\"relative\">\r\n        <p className=\"mb-1 text-[0.875rem] leading-[1.375rem] text-richblack-5\">\r\n          Password <sup className=\"text-pink-200\">*</sup>\r\n        </p>\r\n        <input\r\n          required\r\n          type={showPassword ? \"text\" : \"password\"}\r\n          name=\"password\"\r\n          value={password}\r\n          onChange={handleOnChange}\r\n          placeholder=\"Enter Password\"\r\n          style={{\r\n            boxShadow: \"inset 0px -1px 0px rgba(255, 255, 255, 0.18)\",\r\n          }}\r\n          className=\"w-full rounded-[0.5rem] bg-richblack-800 p-[12px] pr-12 text-richblack-5\"\r\n        />\r\n        <span\r\n          onClick={() => setShowPassword((prev) => !prev)}\r\n          className=\"absolute right-3 top-[38px] z-[10] cursor-pointer\"\r\n        >\r\n          {showPassword ? (\r\n            <AiOutlineEyeInvisible fontSize={24} fill=\"#AFB2BF\" />\r\n          ) : (\r\n            <AiOutlineEye fontSize={24} fill=\"#AFB2BF\" />\r\n          )}\r\n        </span>\r\n        <Link to=\"/forgot-password\">\r\n          <p className=\"mt-1 ml-auto max-w-max text-xs text-blue-100\">\r\n            Forgot Password\r\n          </p>\r\n        </Link>\r\n      </label>\r\n      <button\r\n        type=\"submit\"\r\n        className=\"mt-6 rounded-[8px] bg-yellow-50 py-[8px] px-[12px] font-medium text-richblack-900\"\r\n      >\r\n        Sign In\r\n      </button>\r\n    </form>\r\n  )\r\n}\r\n\r\nexport default LoginForm"], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,YAAY,EAAEC,qBAAqB,QAAQ,gBAAgB;AACpE,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AAEpD,SAASC,KAAK,QAAQ,sCAAsC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAE5D,SAASC,SAASA,CAAA,EAAG;EAAAC,EAAA;EACnB,MAAMC,QAAQ,GAAGN,WAAW,EAAE;EAC9B,MAAMO,QAAQ,GAAGT,WAAW,EAAE;EAC9B,MAAM,CAACU,QAAQ,EAAEC,WAAW,CAAC,GAAGd,QAAQ,CAAC;IACvCe,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE;EACZ,CAAC,CAAC;EAEF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAEvD,MAAM;IAAEe,KAAK;IAAEC;EAAS,CAAC,GAAGH,QAAQ;EAEpC,MAAMM,cAAc,GAAIC,CAAC,IAAK;IAC5BN,WAAW,CAAEO,QAAQ,KAAM;MACzB,GAAGA,QAAQ;MACX,CAACD,CAAC,CAACE,MAAM,CAACC,IAAI,GAAGH,CAAC,CAACE,MAAM,CAACE;IAC5B,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMC,cAAc,GAAIL,CAAC,IAAK;IAC5BA,CAAC,CAACM,cAAc,EAAE;IAClBd,QAAQ,CAACN,KAAK,CAACS,KAAK,EAAEC,QAAQ,EAAEL,QAAQ,CAAC,CAAC;EAC5C,CAAC;EAED,oBACEH,OAAA;IACEmB,QAAQ,EAAEF,cAAe;IACzBG,SAAS,EAAC,mCAAmC;IAAAC,QAAA,gBAE7CrB,OAAA;MAAOoB,SAAS,EAAC,QAAQ;MAAAC,QAAA,gBACvBrB,OAAA;QAAGoB,SAAS,EAAC,0DAA0D;QAAAC,QAAA,GAAC,gBACxD,eAAArB,OAAA;UAAKoB,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAM;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAClD,eACJzB,OAAA;QACE0B,QAAQ;QACRC,IAAI,EAAC,MAAM;QACXZ,IAAI,EAAC,OAAO;QACZC,KAAK,EAAET,KAAM;QACbqB,QAAQ,EAAEjB,cAAe;QACzBkB,WAAW,EAAC,qBAAqB;QACjCC,KAAK,EAAE;UACLC,SAAS,EAAE;QACb,CAAE;QACFX,SAAS,EAAC;MAAoE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAC9E;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACI,eACRzB,OAAA;MAAOoB,SAAS,EAAC,UAAU;MAAAC,QAAA,gBACzBrB,OAAA;QAAGoB,SAAS,EAAC,0DAA0D;QAAAC,QAAA,GAAC,WAC7D,eAAArB,OAAA;UAAKoB,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAM;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAC7C,eACJzB,OAAA;QACE0B,QAAQ;QACRC,IAAI,EAAElB,YAAY,GAAG,MAAM,GAAG,UAAW;QACzCM,IAAI,EAAC,UAAU;QACfC,KAAK,EAAER,QAAS;QAChBoB,QAAQ,EAAEjB,cAAe;QACzBkB,WAAW,EAAC,gBAAgB;QAC5BC,KAAK,EAAE;UACLC,SAAS,EAAE;QACb,CAAE;QACFX,SAAS,EAAC;MAA0E;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACpF,eACFzB,OAAA;QACEgC,OAAO,EAAEA,CAAA,KAAMtB,eAAe,CAAEuB,IAAI,IAAK,CAACA,IAAI,CAAE;QAChDb,SAAS,EAAC,mDAAmD;QAAAC,QAAA,EAE5DZ,YAAY,gBACXT,OAAA,CAACN,qBAAqB;UAACwC,QAAQ,EAAE,EAAG;UAACC,IAAI,EAAC;QAAS;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAG,gBAEtDzB,OAAA,CAACP,YAAY;UAACyC,QAAQ,EAAE,EAAG;UAACC,IAAI,EAAC;QAAS;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAC3C;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACI,eACPzB,OAAA,CAACJ,IAAI;QAACwC,EAAE,EAAC,kBAAkB;QAAAf,QAAA,eACzBrB,OAAA;UAAGoB,SAAS,EAAC,8CAA8C;UAAAC,QAAA,EAAC;QAE5D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAI;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACD,eACRzB,OAAA;MACE2B,IAAI,EAAC,QAAQ;MACbP,SAAS,EAAC,mFAAmF;MAAAC,QAAA,EAC9F;IAED;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAS;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACJ;AAEX;AAACvB,EAAA,CAtFQD,SAAS;EAAA,QACCJ,WAAW,EACXF,WAAW;AAAA;AAAA0C,EAAA,GAFrBpC,SAAS;AAwFlB,eAAeA,SAAS;AAAA,IAAAoC,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}