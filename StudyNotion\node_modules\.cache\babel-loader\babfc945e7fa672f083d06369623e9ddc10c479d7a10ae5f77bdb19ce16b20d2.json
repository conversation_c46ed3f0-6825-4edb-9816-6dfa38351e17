{"ast": null, "code": "import { elementStyle } from '../../shared/utils.js';\nexport default function updateSize() {\n  const swiper = this;\n  let width;\n  let height;\n  const el = swiper.el;\n  if (typeof swiper.params.width !== 'undefined' && swiper.params.width !== null) {\n    width = swiper.params.width;\n  } else {\n    width = el.clientWidth;\n  }\n  if (typeof swiper.params.height !== 'undefined' && swiper.params.height !== null) {\n    height = swiper.params.height;\n  } else {\n    height = el.clientHeight;\n  }\n  if (width === 0 && swiper.isHorizontal() || height === 0 && swiper.isVertical()) {\n    return;\n  }\n\n  // Subtract paddings\n  width = width - parseInt(elementStyle(el, 'padding-left') || 0, 10) - parseInt(elementStyle(el, 'padding-right') || 0, 10);\n  height = height - parseInt(elementStyle(el, 'padding-top') || 0, 10) - parseInt(elementStyle(el, 'padding-bottom') || 0, 10);\n  if (Number.isNaN(width)) width = 0;\n  if (Number.isNaN(height)) height = 0;\n  Object.assign(swiper, {\n    width,\n    height,\n    size: swiper.isHorizontal() ? width : height\n  });\n}", "map": {"version": 3, "names": ["elementStyle", "updateSize", "swiper", "width", "height", "el", "params", "clientWidth", "clientHeight", "isHorizontal", "isVertical", "parseInt", "Number", "isNaN", "Object", "assign", "size"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/core/update/updateSize.js"], "sourcesContent": ["import { elementStyle } from '../../shared/utils.js';\nexport default function updateSize() {\n  const swiper = this;\n  let width;\n  let height;\n  const el = swiper.el;\n  if (typeof swiper.params.width !== 'undefined' && swiper.params.width !== null) {\n    width = swiper.params.width;\n  } else {\n    width = el.clientWidth;\n  }\n  if (typeof swiper.params.height !== 'undefined' && swiper.params.height !== null) {\n    height = swiper.params.height;\n  } else {\n    height = el.clientHeight;\n  }\n  if (width === 0 && swiper.isHorizontal() || height === 0 && swiper.isVertical()) {\n    return;\n  }\n\n  // Subtract paddings\n  width = width - parseInt(elementStyle(el, 'padding-left') || 0, 10) - parseInt(elementStyle(el, 'padding-right') || 0, 10);\n  height = height - parseInt(elementStyle(el, 'padding-top') || 0, 10) - parseInt(elementStyle(el, 'padding-bottom') || 0, 10);\n  if (Number.isNaN(width)) width = 0;\n  if (Number.isNaN(height)) height = 0;\n  Object.assign(swiper, {\n    width,\n    height,\n    size: swiper.isHorizontal() ? width : height\n  });\n}"], "mappings": "AAAA,SAASA,YAAY,QAAQ,uBAAuB;AACpD,eAAe,SAASC,UAAUA,CAAA,EAAG;EACnC,MAAMC,MAAM,GAAG,IAAI;EACnB,IAAIC,KAAK;EACT,IAAIC,MAAM;EACV,MAAMC,EAAE,GAAGH,MAAM,CAACG,EAAE;EACpB,IAAI,OAAOH,MAAM,CAACI,MAAM,CAACH,KAAK,KAAK,WAAW,IAAID,MAAM,CAACI,MAAM,CAACH,KAAK,KAAK,IAAI,EAAE;IAC9EA,KAAK,GAAGD,MAAM,CAACI,MAAM,CAACH,KAAK;EAC7B,CAAC,MAAM;IACLA,KAAK,GAAGE,EAAE,CAACE,WAAW;EACxB;EACA,IAAI,OAAOL,MAAM,CAACI,MAAM,CAACF,MAAM,KAAK,WAAW,IAAIF,MAAM,CAACI,MAAM,CAACF,MAAM,KAAK,IAAI,EAAE;IAChFA,MAAM,GAAGF,MAAM,CAACI,MAAM,CAACF,MAAM;EAC/B,CAAC,MAAM;IACLA,MAAM,GAAGC,EAAE,CAACG,YAAY;EAC1B;EACA,IAAIL,KAAK,KAAK,CAAC,IAAID,MAAM,CAACO,YAAY,EAAE,IAAIL,MAAM,KAAK,CAAC,IAAIF,MAAM,CAACQ,UAAU,EAAE,EAAE;IAC/E;EACF;;EAEA;EACAP,KAAK,GAAGA,KAAK,GAAGQ,QAAQ,CAACX,YAAY,CAACK,EAAE,EAAE,cAAc,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,GAAGM,QAAQ,CAACX,YAAY,CAACK,EAAE,EAAE,eAAe,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;EAC1HD,MAAM,GAAGA,MAAM,GAAGO,QAAQ,CAACX,YAAY,CAACK,EAAE,EAAE,aAAa,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,GAAGM,QAAQ,CAACX,YAAY,CAACK,EAAE,EAAE,gBAAgB,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;EAC5H,IAAIO,MAAM,CAACC,KAAK,CAACV,KAAK,CAAC,EAAEA,KAAK,GAAG,CAAC;EAClC,IAAIS,MAAM,CAACC,KAAK,CAACT,MAAM,CAAC,EAAEA,MAAM,GAAG,CAAC;EACpCU,MAAM,CAACC,MAAM,CAACb,MAAM,EAAE;IACpBC,KAAK;IACLC,MAAM;IACNY,IAAI,EAAEd,MAAM,CAACO,YAAY,EAAE,GAAGN,KAAK,GAAGC;EACxC,CAAC,CAAC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}