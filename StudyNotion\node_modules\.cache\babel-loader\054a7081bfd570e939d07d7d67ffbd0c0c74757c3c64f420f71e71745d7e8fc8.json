{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _react = _interopRequireDefault(require(\"react\"));\nvar _classnames = _interopRequireDefault(require(\"classnames\"));\nvar propTypes = {\n  percentage: _propTypes[\"default\"].string,\n  vertical: _propTypes[\"default\"].bool,\n  className: _propTypes[\"default\"].string\n};\nvar defaultProps = {\n  percentage: '100%',\n  vertical: false\n};\nfunction VolumeLevel(_ref) {\n  var percentage = _ref.percentage,\n    vertical = _ref.vertical,\n    className = _ref.className;\n  var style = {};\n  if (vertical) {\n    style.height = percentage;\n  } else {\n    style.width = percentage;\n  }\n  return _react[\"default\"].createElement(\"div\", {\n    className: (0, _classnames[\"default\"])(className, 'video-react-volume-level'),\n    style: style\n  }, _react[\"default\"].createElement(\"span\", {\n    className: \"video-react-control-text\"\n  }));\n}\nVolumeLevel.propTypes = propTypes;\nVolumeLevel.defaultProps = defaultProps;\nVolumeLevel.displayName = 'VolumeLevel';\nvar _default = VolumeLevel;\nexports[\"default\"] = _default;", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "_propTypes", "_react", "_classnames", "propTypes", "percentage", "string", "vertical", "bool", "className", "defaultProps", "VolumeLevel", "_ref", "style", "height", "width", "createElement", "displayName", "_default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/video-react/lib/components/volume-control/VolumeLevel.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\n\nvar _react = _interopRequireDefault(require(\"react\"));\n\nvar _classnames = _interopRequireDefault(require(\"classnames\"));\n\nvar propTypes = {\n  percentage: _propTypes[\"default\"].string,\n  vertical: _propTypes[\"default\"].bool,\n  className: _propTypes[\"default\"].string\n};\nvar defaultProps = {\n  percentage: '100%',\n  vertical: false\n};\n\nfunction VolumeLevel(_ref) {\n  var percentage = _ref.percentage,\n      vertical = _ref.vertical,\n      className = _ref.className;\n  var style = {};\n\n  if (vertical) {\n    style.height = percentage;\n  } else {\n    style.width = percentage;\n  }\n\n  return _react[\"default\"].createElement(\"div\", {\n    className: (0, _classnames[\"default\"])(className, 'video-react-volume-level'),\n    style: style\n  }, _react[\"default\"].createElement(\"span\", {\n    className: \"video-react-control-text\"\n  }));\n}\n\nVolumeLevel.propTypes = propTypes;\nVolumeLevel.defaultProps = defaultProps;\nVolumeLevel.displayName = 'VolumeLevel';\nvar _default = VolumeLevel;\nexports[\"default\"] = _default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC;AAEpFC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;AAE3B,IAAIE,UAAU,GAAGN,sBAAsB,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;AAE9D,IAAIM,MAAM,GAAGP,sBAAsB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AAErD,IAAIO,WAAW,GAAGR,sBAAsB,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;AAE/D,IAAIQ,SAAS,GAAG;EACdC,UAAU,EAAEJ,UAAU,CAAC,SAAS,CAAC,CAACK,MAAM;EACxCC,QAAQ,EAAEN,UAAU,CAAC,SAAS,CAAC,CAACO,IAAI;EACpCC,SAAS,EAAER,UAAU,CAAC,SAAS,CAAC,CAACK;AACnC,CAAC;AACD,IAAII,YAAY,GAAG;EACjBL,UAAU,EAAE,MAAM;EAClBE,QAAQ,EAAE;AACZ,CAAC;AAED,SAASI,WAAWA,CAACC,IAAI,EAAE;EACzB,IAAIP,UAAU,GAAGO,IAAI,CAACP,UAAU;IAC5BE,QAAQ,GAAGK,IAAI,CAACL,QAAQ;IACxBE,SAAS,GAAGG,IAAI,CAACH,SAAS;EAC9B,IAAII,KAAK,GAAG,CAAC,CAAC;EAEd,IAAIN,QAAQ,EAAE;IACZM,KAAK,CAACC,MAAM,GAAGT,UAAU;EAC3B,CAAC,MAAM;IACLQ,KAAK,CAACE,KAAK,GAAGV,UAAU;EAC1B;EAEA,OAAOH,MAAM,CAAC,SAAS,CAAC,CAACc,aAAa,CAAC,KAAK,EAAE;IAC5CP,SAAS,EAAE,CAAC,CAAC,EAAEN,WAAW,CAAC,SAAS,CAAC,EAAEM,SAAS,EAAE,0BAA0B,CAAC;IAC7EI,KAAK,EAAEA;EACT,CAAC,EAAEX,MAAM,CAAC,SAAS,CAAC,CAACc,aAAa,CAAC,MAAM,EAAE;IACzCP,SAAS,EAAE;EACb,CAAC,CAAC,CAAC;AACL;AAEAE,WAAW,CAACP,SAAS,GAAGA,SAAS;AACjCO,WAAW,CAACD,YAAY,GAAGA,YAAY;AACvCC,WAAW,CAACM,WAAW,GAAG,aAAa;AACvC,IAAIC,QAAQ,GAAGP,WAAW;AAC1BZ,OAAO,CAAC,SAAS,CAAC,GAAGmB,QAAQ"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}