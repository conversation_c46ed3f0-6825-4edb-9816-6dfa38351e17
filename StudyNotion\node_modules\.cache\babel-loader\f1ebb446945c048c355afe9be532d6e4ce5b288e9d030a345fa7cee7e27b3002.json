{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = LoadingSpinner;\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _react = _interopRequireDefault(require(\"react\"));\nvar _classnames = _interopRequireDefault(require(\"classnames\"));\nvar propTypes = {\n  player: _propTypes[\"default\"].object,\n  className: _propTypes[\"default\"].string\n};\nfunction LoadingSpinner(_ref) {\n  var player = _ref.player,\n    className = _ref.className;\n  if (player.error) {\n    return null;\n  }\n  return _react[\"default\"].createElement(\"div\", {\n    className: (0, _classnames[\"default\"])('video-react-loading-spinner', className)\n  });\n}\nLoadingSpinner.propTypes = propTypes;\nLoadingSpinner.displayName = 'LoadingSpinner';", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "LoadingSpinner", "_propTypes", "_react", "_classnames", "propTypes", "player", "object", "className", "string", "_ref", "error", "createElement", "displayName"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/video-react/lib/components/LoadingSpinner.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = LoadingSpinner;\n\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\n\nvar _react = _interopRequireDefault(require(\"react\"));\n\nvar _classnames = _interopRequireDefault(require(\"classnames\"));\n\nvar propTypes = {\n  player: _propTypes[\"default\"].object,\n  className: _propTypes[\"default\"].string\n};\n\nfunction LoadingSpinner(_ref) {\n  var player = _ref.player,\n      className = _ref.className;\n\n  if (player.error) {\n    return null;\n  }\n\n  return _react[\"default\"].createElement(\"div\", {\n    className: (0, _classnames[\"default\"])('video-react-loading-spinner', className)\n  });\n}\n\nLoadingSpinner.propTypes = propTypes;\nLoadingSpinner.displayName = 'LoadingSpinner';"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC;AAEpFC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAAC,SAAS,CAAC,GAAGE,cAAc;AAEnC,IAAIC,UAAU,GAAGP,sBAAsB,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;AAE9D,IAAIO,MAAM,GAAGR,sBAAsB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AAErD,IAAIQ,WAAW,GAAGT,sBAAsB,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;AAE/D,IAAIS,SAAS,GAAG;EACdC,MAAM,EAAEJ,UAAU,CAAC,SAAS,CAAC,CAACK,MAAM;EACpCC,SAAS,EAAEN,UAAU,CAAC,SAAS,CAAC,CAACO;AACnC,CAAC;AAED,SAASR,cAAcA,CAACS,IAAI,EAAE;EAC5B,IAAIJ,MAAM,GAAGI,IAAI,CAACJ,MAAM;IACpBE,SAAS,GAAGE,IAAI,CAACF,SAAS;EAE9B,IAAIF,MAAM,CAACK,KAAK,EAAE;IAChB,OAAO,IAAI;EACb;EAEA,OAAOR,MAAM,CAAC,SAAS,CAAC,CAACS,aAAa,CAAC,KAAK,EAAE;IAC5CJ,SAAS,EAAE,CAAC,CAAC,EAAEJ,WAAW,CAAC,SAAS,CAAC,EAAE,6BAA6B,EAAEI,SAAS;EACjF,CAAC,CAAC;AACJ;AAEAP,cAAc,CAACI,SAAS,GAAGA,SAAS;AACpCJ,cAAc,CAACY,WAAW,GAAG,gBAAgB"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}