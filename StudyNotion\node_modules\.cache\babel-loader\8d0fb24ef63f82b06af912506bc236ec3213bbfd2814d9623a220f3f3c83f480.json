{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _react = _interopRequireDefault(require(\"react\"));\nvar _classnames = _interopRequireDefault(require(\"classnames\"));\nvar _utils = require(\"../../utils\");\nvar propTypes = {\n  player: _propTypes[\"default\"].object,\n  className: _propTypes[\"default\"].string\n};\nfunction DurationDisplay(_ref) {\n  var duration = _ref.player.duration,\n    className = _ref.className;\n  var formattedTime = (0, _utils.formatTime)(duration);\n  return _react[\"default\"].createElement(\"div\", {\n    className: (0, _classnames[\"default\"])(className, 'video-react-duration video-react-time-control video-react-control')\n  }, _react[\"default\"].createElement(\"div\", {\n    className: \"video-react-duration-display\",\n    \"aria-live\": \"off\"\n  }, _react[\"default\"].createElement(\"span\", {\n    className: \"video-react-control-text\"\n  }, \"Duration Time \"), formattedTime));\n}\nDurationDisplay.propTypes = propTypes;\nDurationDisplay.displayName = 'DurationDisplay';\nvar _default = DurationDisplay;\nexports[\"default\"] = _default;", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "_propTypes", "_react", "_classnames", "_utils", "propTypes", "player", "object", "className", "string", "DurationDisplay", "_ref", "duration", "formattedTime", "formatTime", "createElement", "displayName", "_default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/video-react/lib/components/time-controls/DurationDisplay.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\n\nvar _react = _interopRequireDefault(require(\"react\"));\n\nvar _classnames = _interopRequireDefault(require(\"classnames\"));\n\nvar _utils = require(\"../../utils\");\n\nvar propTypes = {\n  player: _propTypes[\"default\"].object,\n  className: _propTypes[\"default\"].string\n};\n\nfunction DurationDisplay(_ref) {\n  var duration = _ref.player.duration,\n      className = _ref.className;\n  var formattedTime = (0, _utils.formatTime)(duration);\n  return _react[\"default\"].createElement(\"div\", {\n    className: (0, _classnames[\"default\"])(className, 'video-react-duration video-react-time-control video-react-control')\n  }, _react[\"default\"].createElement(\"div\", {\n    className: \"video-react-duration-display\",\n    \"aria-live\": \"off\"\n  }, _react[\"default\"].createElement(\"span\", {\n    className: \"video-react-control-text\"\n  }, \"Duration Time \"), formattedTime));\n}\n\nDurationDisplay.propTypes = propTypes;\nDurationDisplay.displayName = 'DurationDisplay';\nvar _default = DurationDisplay;\nexports[\"default\"] = _default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC;AAEpFC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;AAE3B,IAAIE,UAAU,GAAGN,sBAAsB,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;AAE9D,IAAIM,MAAM,GAAGP,sBAAsB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AAErD,IAAIO,WAAW,GAAGR,sBAAsB,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;AAE/D,IAAIQ,MAAM,GAAGR,OAAO,CAAC,aAAa,CAAC;AAEnC,IAAIS,SAAS,GAAG;EACdC,MAAM,EAAEL,UAAU,CAAC,SAAS,CAAC,CAACM,MAAM;EACpCC,SAAS,EAAEP,UAAU,CAAC,SAAS,CAAC,CAACQ;AACnC,CAAC;AAED,SAASC,eAAeA,CAACC,IAAI,EAAE;EAC7B,IAAIC,QAAQ,GAAGD,IAAI,CAACL,MAAM,CAACM,QAAQ;IAC/BJ,SAAS,GAAGG,IAAI,CAACH,SAAS;EAC9B,IAAIK,aAAa,GAAG,CAAC,CAAC,EAAET,MAAM,CAACU,UAAU,EAAEF,QAAQ,CAAC;EACpD,OAAOV,MAAM,CAAC,SAAS,CAAC,CAACa,aAAa,CAAC,KAAK,EAAE;IAC5CP,SAAS,EAAE,CAAC,CAAC,EAAEL,WAAW,CAAC,SAAS,CAAC,EAAEK,SAAS,EAAE,mEAAmE;EACvH,CAAC,EAAEN,MAAM,CAAC,SAAS,CAAC,CAACa,aAAa,CAAC,KAAK,EAAE;IACxCP,SAAS,EAAE,8BAA8B;IACzC,WAAW,EAAE;EACf,CAAC,EAAEN,MAAM,CAAC,SAAS,CAAC,CAACa,aAAa,CAAC,MAAM,EAAE;IACzCP,SAAS,EAAE;EACb,CAAC,EAAE,gBAAgB,CAAC,EAAEK,aAAa,CAAC,CAAC;AACvC;AAEAH,eAAe,CAACL,SAAS,GAAGA,SAAS;AACrCK,eAAe,CAACM,WAAW,GAAG,iBAAiB;AAC/C,IAAIC,QAAQ,GAAGP,eAAe;AAC9BX,OAAO,CAAC,SAAS,CAAC,GAAGkB,QAAQ"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}