{"name": "@types/bluebird", "version": "3.5.38", "description": "TypeScript definitions for bluebird", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/bluebird", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/lhecker", "githubUsername": "<PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/bluebird"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "9f467706c85be1a25de86a8b082cb50d6ae5b2cded8ae8bedb8405c63e7d5319", "typeScriptVersion": "4.1"}