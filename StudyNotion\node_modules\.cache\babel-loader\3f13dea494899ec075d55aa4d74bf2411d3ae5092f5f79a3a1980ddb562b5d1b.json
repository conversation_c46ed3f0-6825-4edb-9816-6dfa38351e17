{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = TimeDivider;\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _react = _interopRequireDefault(require(\"react\"));\nvar _classnames = _interopRequireDefault(require(\"classnames\"));\nvar propTypes = {\n  separator: _propTypes[\"default\"].string,\n  className: _propTypes[\"default\"].string\n};\nfunction TimeDivider(_ref) {\n  var separator = _ref.separator,\n    className = _ref.className;\n  var separatorText = separator || '/';\n  return _react[\"default\"].createElement(\"div\", {\n    className: (0, _classnames[\"default\"])('video-react-time-control video-react-time-divider', className),\n    dir: \"ltr\"\n  }, _react[\"default\"].createElement(\"div\", null, _react[\"default\"].createElement(\"span\", null, separatorText)));\n}\nTimeDivider.propTypes = propTypes;\nTimeDivider.displayName = 'TimeDivider';", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "TimeDivider", "_propTypes", "_react", "_classnames", "propTypes", "separator", "string", "className", "_ref", "separatorText", "createElement", "dir", "displayName"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/video-react/lib/components/time-controls/TimeDivider.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = TimeDivider;\n\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\n\nvar _react = _interopRequireDefault(require(\"react\"));\n\nvar _classnames = _interopRequireDefault(require(\"classnames\"));\n\nvar propTypes = {\n  separator: _propTypes[\"default\"].string,\n  className: _propTypes[\"default\"].string\n};\n\nfunction TimeDivider(_ref) {\n  var separator = _ref.separator,\n      className = _ref.className;\n  var separatorText = separator || '/';\n  return _react[\"default\"].createElement(\"div\", {\n    className: (0, _classnames[\"default\"])('video-react-time-control video-react-time-divider', className),\n    dir: \"ltr\"\n  }, _react[\"default\"].createElement(\"div\", null, _react[\"default\"].createElement(\"span\", null, separatorText)));\n}\n\nTimeDivider.propTypes = propTypes;\nTimeDivider.displayName = 'TimeDivider';"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC;AAEpFC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAAC,SAAS,CAAC,GAAGE,WAAW;AAEhC,IAAIC,UAAU,GAAGP,sBAAsB,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;AAE9D,IAAIO,MAAM,GAAGR,sBAAsB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AAErD,IAAIQ,WAAW,GAAGT,sBAAsB,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;AAE/D,IAAIS,SAAS,GAAG;EACdC,SAAS,EAAEJ,UAAU,CAAC,SAAS,CAAC,CAACK,MAAM;EACvCC,SAAS,EAAEN,UAAU,CAAC,SAAS,CAAC,CAACK;AACnC,CAAC;AAED,SAASN,WAAWA,CAACQ,IAAI,EAAE;EACzB,IAAIH,SAAS,GAAGG,IAAI,CAACH,SAAS;IAC1BE,SAAS,GAAGC,IAAI,CAACD,SAAS;EAC9B,IAAIE,aAAa,GAAGJ,SAAS,IAAI,GAAG;EACpC,OAAOH,MAAM,CAAC,SAAS,CAAC,CAACQ,aAAa,CAAC,KAAK,EAAE;IAC5CH,SAAS,EAAE,CAAC,CAAC,EAAEJ,WAAW,CAAC,SAAS,CAAC,EAAE,mDAAmD,EAAEI,SAAS,CAAC;IACtGI,GAAG,EAAE;EACP,CAAC,EAAET,MAAM,CAAC,SAAS,CAAC,CAACQ,aAAa,CAAC,KAAK,EAAE,IAAI,EAAER,MAAM,CAAC,SAAS,CAAC,CAACQ,aAAa,CAAC,MAAM,EAAE,IAAI,EAAED,aAAa,CAAC,CAAC,CAAC;AAChH;AAEAT,WAAW,CAACI,SAAS,GAAGA,SAAS;AACjCJ,WAAW,CAACY,WAAW,GAAG,aAAa"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}