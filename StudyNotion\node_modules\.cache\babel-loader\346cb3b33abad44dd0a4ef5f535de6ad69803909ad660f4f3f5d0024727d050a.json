{"ast": null, "code": "/**\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n * @typedef {import('mdast').Content} Content\n * @typedef {import('mdast').Parent} Parent\n * @typedef {import('mdast').Root} Root\n * @typedef {import('mdast').TableRow} TableRow\n * @typedef {import('../state.js').State} State\n */\n\n/**\n * @typedef {Root | Content} Nodes\n * @typedef {Extract<Nodes, Parent>} Parents\n */\n\n/**\n * Turn an mdast `tableRow` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {TableRow} node\n *   mdast node.\n * @param {Parents | null | undefined} parent\n *   Parent of `node`.\n * @returns {Element}\n *   hast node.\n */\nexport function tableRow(state, node, parent) {\n  const siblings = parent ? parent.children : undefined;\n  // Generate a body row when without parent.\n  const rowIndex = siblings ? siblings.indexOf(node) : 1;\n  const tagName = rowIndex === 0 ? 'th' : 'td';\n  const align = parent && parent.type === 'table' ? parent.align : undefined;\n  const length = align ? align.length : node.children.length;\n  let cellIndex = -1;\n  /** @type {Array<ElementContent>} */\n  const cells = [];\n  while (++cellIndex < length) {\n    // Note: can also be undefined.\n    const cell = node.children[cellIndex];\n    /** @type {Properties} */\n    const properties = {};\n    const alignValue = align ? align[cellIndex] : undefined;\n    if (alignValue) {\n      properties.align = alignValue;\n    }\n\n    /** @type {Element} */\n    let result = {\n      type: 'element',\n      tagName,\n      properties,\n      children: []\n    };\n    if (cell) {\n      result.children = state.all(cell);\n      state.patch(cell, result);\n      result = state.applyData(node, result);\n    }\n    cells.push(result);\n  }\n\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'tr',\n    properties: {},\n    children: state.wrap(cells, true)\n  };\n  state.patch(node, result);\n  return state.applyData(node, result);\n}", "map": {"version": 3, "names": ["tableRow", "state", "node", "parent", "siblings", "children", "undefined", "rowIndex", "indexOf", "tagName", "align", "type", "length", "cellIndex", "cells", "cell", "properties", "alignValue", "result", "all", "patch", "applyData", "push", "wrap"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/mdast-util-to-hast/lib/handlers/table-row.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n * @typedef {import('mdast').Content} Content\n * @typedef {import('mdast').Parent} Parent\n * @typedef {import('mdast').Root} Root\n * @typedef {import('mdast').TableRow} TableRow\n * @typedef {import('../state.js').State} State\n */\n\n/**\n * @typedef {Root | Content} Nodes\n * @typedef {Extract<Nodes, Parent>} Parents\n */\n\n/**\n * Turn an mdast `tableRow` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {TableRow} node\n *   mdast node.\n * @param {Parents | null | undefined} parent\n *   Parent of `node`.\n * @returns {Element}\n *   hast node.\n */\nexport function tableRow(state, node, parent) {\n  const siblings = parent ? parent.children : undefined\n  // Generate a body row when without parent.\n  const rowIndex = siblings ? siblings.indexOf(node) : 1\n  const tagName = rowIndex === 0 ? 'th' : 'td'\n  const align = parent && parent.type === 'table' ? parent.align : undefined\n  const length = align ? align.length : node.children.length\n  let cellIndex = -1\n  /** @type {Array<ElementContent>} */\n  const cells = []\n\n  while (++cellIndex < length) {\n    // Note: can also be undefined.\n    const cell = node.children[cellIndex]\n    /** @type {Properties} */\n    const properties = {}\n    const alignValue = align ? align[cellIndex] : undefined\n\n    if (alignValue) {\n      properties.align = alignValue\n    }\n\n    /** @type {Element} */\n    let result = {type: 'element', tagName, properties, children: []}\n\n    if (cell) {\n      result.children = state.all(cell)\n      state.patch(cell, result)\n      result = state.applyData(node, result)\n    }\n\n    cells.push(result)\n  }\n\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'tr',\n    properties: {},\n    children: state.wrap(cells, true)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,QAAQA,CAACC,KAAK,EAAEC,IAAI,EAAEC,MAAM,EAAE;EAC5C,MAAMC,QAAQ,GAAGD,MAAM,GAAGA,MAAM,CAACE,QAAQ,GAAGC,SAAS;EACrD;EACA,MAAMC,QAAQ,GAAGH,QAAQ,GAAGA,QAAQ,CAACI,OAAO,CAACN,IAAI,CAAC,GAAG,CAAC;EACtD,MAAMO,OAAO,GAAGF,QAAQ,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI;EAC5C,MAAMG,KAAK,GAAGP,MAAM,IAAIA,MAAM,CAACQ,IAAI,KAAK,OAAO,GAAGR,MAAM,CAACO,KAAK,GAAGJ,SAAS;EAC1E,MAAMM,MAAM,GAAGF,KAAK,GAAGA,KAAK,CAACE,MAAM,GAAGV,IAAI,CAACG,QAAQ,CAACO,MAAM;EAC1D,IAAIC,SAAS,GAAG,CAAC,CAAC;EAClB;EACA,MAAMC,KAAK,GAAG,EAAE;EAEhB,OAAO,EAAED,SAAS,GAAGD,MAAM,EAAE;IAC3B;IACA,MAAMG,IAAI,GAAGb,IAAI,CAACG,QAAQ,CAACQ,SAAS,CAAC;IACrC;IACA,MAAMG,UAAU,GAAG,CAAC,CAAC;IACrB,MAAMC,UAAU,GAAGP,KAAK,GAAGA,KAAK,CAACG,SAAS,CAAC,GAAGP,SAAS;IAEvD,IAAIW,UAAU,EAAE;MACdD,UAAU,CAACN,KAAK,GAAGO,UAAU;IAC/B;;IAEA;IACA,IAAIC,MAAM,GAAG;MAACP,IAAI,EAAE,SAAS;MAAEF,OAAO;MAAEO,UAAU;MAAEX,QAAQ,EAAE;IAAE,CAAC;IAEjE,IAAIU,IAAI,EAAE;MACRG,MAAM,CAACb,QAAQ,GAAGJ,KAAK,CAACkB,GAAG,CAACJ,IAAI,CAAC;MACjCd,KAAK,CAACmB,KAAK,CAACL,IAAI,EAAEG,MAAM,CAAC;MACzBA,MAAM,GAAGjB,KAAK,CAACoB,SAAS,CAACnB,IAAI,EAAEgB,MAAM,CAAC;IACxC;IAEAJ,KAAK,CAACQ,IAAI,CAACJ,MAAM,CAAC;EACpB;;EAEA;EACA,MAAMA,MAAM,GAAG;IACbP,IAAI,EAAE,SAAS;IACfF,OAAO,EAAE,IAAI;IACbO,UAAU,EAAE,CAAC,CAAC;IACdX,QAAQ,EAAEJ,KAAK,CAACsB,IAAI,CAACT,KAAK,EAAE,IAAI;EAClC,CAAC;EACDb,KAAK,CAACmB,KAAK,CAAClB,IAAI,EAAEgB,MAAM,CAAC;EACzB,OAAOjB,KAAK,CAACoB,SAAS,CAACnB,IAAI,EAAEgB,MAAM,CAAC;AACtC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}