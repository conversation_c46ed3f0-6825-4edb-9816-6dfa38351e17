{"ast": null, "code": "export default function prependSlide(slides) {\n  const swiper = this;\n  const {\n    params,\n    activeIndex,\n    slidesEl\n  } = swiper;\n  if (params.loop) {\n    swiper.loopDestroy();\n  }\n  let newActiveIndex = activeIndex + 1;\n  const prependElement = slideEl => {\n    if (typeof slideEl === 'string') {\n      const tempDOM = document.createElement('div');\n      tempDOM.innerHTML = slideEl;\n      slidesEl.prepend(tempDOM.children[0]);\n      tempDOM.innerHTML = '';\n    } else {\n      slidesEl.prepend(slideEl);\n    }\n  };\n  if (typeof slides === 'object' && 'length' in slides) {\n    for (let i = 0; i < slides.length; i += 1) {\n      if (slides[i]) prependElement(slides[i]);\n    }\n    newActiveIndex = activeIndex + slides.length;\n  } else {\n    prependElement(slides);\n  }\n  swiper.recalcSlides();\n  if (params.loop) {\n    swiper.loopCreate();\n  }\n  if (!params.observer || swiper.isElement) {\n    swiper.update();\n  }\n  swiper.slideTo(newActiveIndex, 0, false);\n}", "map": {"version": 3, "names": ["prependSlide", "slides", "swiper", "params", "activeIndex", "slidesEl", "loop", "loop<PERSON><PERSON><PERSON>", "newActiveIndex", "prependElement", "slideEl", "tempDOM", "document", "createElement", "innerHTML", "prepend", "children", "i", "length", "recalcSlides", "loopCreate", "observer", "isElement", "update", "slideTo"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/modules/manipulation/methods/prependSlide.js"], "sourcesContent": ["export default function prependSlide(slides) {\n  const swiper = this;\n  const {\n    params,\n    activeIndex,\n    slidesEl\n  } = swiper;\n  if (params.loop) {\n    swiper.loopDestroy();\n  }\n  let newActiveIndex = activeIndex + 1;\n  const prependElement = slideEl => {\n    if (typeof slideEl === 'string') {\n      const tempDOM = document.createElement('div');\n      tempDOM.innerHTML = slideEl;\n      slidesEl.prepend(tempDOM.children[0]);\n      tempDOM.innerHTML = '';\n    } else {\n      slidesEl.prepend(slideEl);\n    }\n  };\n  if (typeof slides === 'object' && 'length' in slides) {\n    for (let i = 0; i < slides.length; i += 1) {\n      if (slides[i]) prependElement(slides[i]);\n    }\n    newActiveIndex = activeIndex + slides.length;\n  } else {\n    prependElement(slides);\n  }\n  swiper.recalcSlides();\n  if (params.loop) {\n    swiper.loopCreate();\n  }\n  if (!params.observer || swiper.isElement) {\n    swiper.update();\n  }\n  swiper.slideTo(newActiveIndex, 0, false);\n}"], "mappings": "AAAA,eAAe,SAASA,YAAYA,CAACC,MAAM,EAAE;EAC3C,MAAMC,MAAM,GAAG,IAAI;EACnB,MAAM;IACJC,MAAM;IACNC,WAAW;IACXC;EACF,CAAC,GAAGH,MAAM;EACV,IAAIC,MAAM,CAACG,IAAI,EAAE;IACfJ,MAAM,CAACK,WAAW,EAAE;EACtB;EACA,IAAIC,cAAc,GAAGJ,WAAW,GAAG,CAAC;EACpC,MAAMK,cAAc,GAAGC,OAAO,IAAI;IAChC,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;MAC/B,MAAMC,OAAO,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAC7CF,OAAO,CAACG,SAAS,GAAGJ,OAAO;MAC3BL,QAAQ,CAACU,OAAO,CAACJ,OAAO,CAACK,QAAQ,CAAC,CAAC,CAAC,CAAC;MACrCL,OAAO,CAACG,SAAS,GAAG,EAAE;IACxB,CAAC,MAAM;MACLT,QAAQ,CAACU,OAAO,CAACL,OAAO,CAAC;IAC3B;EACF,CAAC;EACD,IAAI,OAAOT,MAAM,KAAK,QAAQ,IAAI,QAAQ,IAAIA,MAAM,EAAE;IACpD,KAAK,IAAIgB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGhB,MAAM,CAACiB,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;MACzC,IAAIhB,MAAM,CAACgB,CAAC,CAAC,EAAER,cAAc,CAACR,MAAM,CAACgB,CAAC,CAAC,CAAC;IAC1C;IACAT,cAAc,GAAGJ,WAAW,GAAGH,MAAM,CAACiB,MAAM;EAC9C,CAAC,MAAM;IACLT,cAAc,CAACR,MAAM,CAAC;EACxB;EACAC,MAAM,CAACiB,YAAY,EAAE;EACrB,IAAIhB,MAAM,CAACG,IAAI,EAAE;IACfJ,MAAM,CAACkB,UAAU,EAAE;EACrB;EACA,IAAI,CAACjB,MAAM,CAACkB,QAAQ,IAAInB,MAAM,CAACoB,SAAS,EAAE;IACxCpB,MAAM,CAACqB,MAAM,EAAE;EACjB;EACArB,MAAM,CAACsB,OAAO,CAAChB,cAAc,EAAE,CAAC,EAAE,KAAK,CAAC;AAC1C"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}