{"ast": null, "code": "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').ThematicBreak} ThematicBreak\n * @typedef {import('../state.js').State} State\n */\n\n/**\n * Turn an mdast `thematicBreak` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {ThematicBreak} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function thematicBreak(state, node) {\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'hr',\n    properties: {},\n    children: []\n  };\n  state.patch(node, result);\n  return state.applyData(node, result);\n}", "map": {"version": 3, "names": ["thematicBreak", "state", "node", "result", "type", "tagName", "properties", "children", "patch", "applyData"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/mdast-util-to-hast/lib/handlers/thematic-break.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').ThematicBreak} ThematicBreak\n * @typedef {import('../state.js').State} State\n */\n\n/**\n * Turn an mdast `thematicBreak` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {ThematicBreak} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function thematicBreak(state, node) {\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'hr',\n    properties: {},\n    children: []\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,aAAaA,CAACC,KAAK,EAAEC,IAAI,EAAE;EACzC;EACA,MAAMC,MAAM,GAAG;IACbC,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,IAAI;IACbC,UAAU,EAAE,CAAC,CAAC;IACdC,QAAQ,EAAE;EACZ,CAAC;EACDN,KAAK,CAACO,KAAK,CAACN,IAAI,EAAEC,MAAM,CAAC;EACzB,OAAOF,KAAK,CAACQ,SAAS,CAACP,IAAI,EAAEC,MAAM,CAAC;AACtC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}