{"ast": null, "code": "/* eslint no-unused-vars: \"off\" */\nexport default function slideToClosest() {\n  let speed = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : this.params.speed;\n  let runCallbacks = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n  let internal = arguments.length > 2 ? arguments[2] : undefined;\n  let threshold = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 0.5;\n  const swiper = this;\n  let index = swiper.activeIndex;\n  const skip = Math.min(swiper.params.slidesPerGroupSkip, index);\n  const snapIndex = skip + Math.floor((index - skip) / swiper.params.slidesPerGroup);\n  const translate = swiper.rtlTranslate ? swiper.translate : -swiper.translate;\n  if (translate >= swiper.snapGrid[snapIndex]) {\n    // The current translate is on or after the current snap index, so the choice\n    // is between the current index and the one after it.\n    const currentSnap = swiper.snapGrid[snapIndex];\n    const nextSnap = swiper.snapGrid[snapIndex + 1];\n    if (translate - currentSnap > (nextSnap - currentSnap) * threshold) {\n      index += swiper.params.slidesPerGroup;\n    }\n  } else {\n    // The current translate is before the current snap index, so the choice\n    // is between the current index and the one before it.\n    const prevSnap = swiper.snapGrid[snapIndex - 1];\n    const currentSnap = swiper.snapGrid[snapIndex];\n    if (translate - prevSnap <= (currentSnap - prevSnap) * threshold) {\n      index -= swiper.params.slidesPerGroup;\n    }\n  }\n  index = Math.max(index, 0);\n  index = Math.min(index, swiper.slidesGrid.length - 1);\n  return swiper.slideTo(index, speed, runCallbacks, internal);\n}", "map": {"version": 3, "names": ["slideToClosest", "speed", "arguments", "length", "undefined", "params", "runCallbacks", "internal", "threshold", "swiper", "index", "activeIndex", "skip", "Math", "min", "slidesPerGroupSkip", "snapIndex", "floor", "slidesPerGroup", "translate", "rtlTranslate", "snapGrid", "currentSnap", "nextSnap", "prevSnap", "max", "slidesGrid", "slideTo"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/core/slide/slideToClosest.js"], "sourcesContent": ["/* eslint no-unused-vars: \"off\" */\nexport default function slideToClosest(speed = this.params.speed, runCallbacks = true, internal, threshold = 0.5) {\n  const swiper = this;\n  let index = swiper.activeIndex;\n  const skip = Math.min(swiper.params.slidesPerGroupSkip, index);\n  const snapIndex = skip + Math.floor((index - skip) / swiper.params.slidesPerGroup);\n  const translate = swiper.rtlTranslate ? swiper.translate : -swiper.translate;\n  if (translate >= swiper.snapGrid[snapIndex]) {\n    // The current translate is on or after the current snap index, so the choice\n    // is between the current index and the one after it.\n    const currentSnap = swiper.snapGrid[snapIndex];\n    const nextSnap = swiper.snapGrid[snapIndex + 1];\n    if (translate - currentSnap > (nextSnap - currentSnap) * threshold) {\n      index += swiper.params.slidesPerGroup;\n    }\n  } else {\n    // The current translate is before the current snap index, so the choice\n    // is between the current index and the one before it.\n    const prevSnap = swiper.snapGrid[snapIndex - 1];\n    const currentSnap = swiper.snapGrid[snapIndex];\n    if (translate - prevSnap <= (currentSnap - prevSnap) * threshold) {\n      index -= swiper.params.slidesPerGroup;\n    }\n  }\n  index = Math.max(index, 0);\n  index = Math.min(index, swiper.slidesGrid.length - 1);\n  return swiper.slideTo(index, speed, runCallbacks, internal);\n}"], "mappings": "AAAA;AACA,eAAe,SAASA,cAAcA,CAAA,EAA4E;EAAA,IAA3EC,KAAK,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI,CAACG,MAAM,CAACJ,KAAK;EAAA,IAAEK,YAAY,GAAAJ,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;EAAA,IAAEK,QAAQ,GAAAL,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAAA,IAAEI,SAAS,GAAAN,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,GAAG;EAC9G,MAAMO,MAAM,GAAG,IAAI;EACnB,IAAIC,KAAK,GAAGD,MAAM,CAACE,WAAW;EAC9B,MAAMC,IAAI,GAAGC,IAAI,CAACC,GAAG,CAACL,MAAM,CAACJ,MAAM,CAACU,kBAAkB,EAAEL,KAAK,CAAC;EAC9D,MAAMM,SAAS,GAAGJ,IAAI,GAAGC,IAAI,CAACI,KAAK,CAAC,CAACP,KAAK,GAAGE,IAAI,IAAIH,MAAM,CAACJ,MAAM,CAACa,cAAc,CAAC;EAClF,MAAMC,SAAS,GAAGV,MAAM,CAACW,YAAY,GAAGX,MAAM,CAACU,SAAS,GAAG,CAACV,MAAM,CAACU,SAAS;EAC5E,IAAIA,SAAS,IAAIV,MAAM,CAACY,QAAQ,CAACL,SAAS,CAAC,EAAE;IAC3C;IACA;IACA,MAAMM,WAAW,GAAGb,MAAM,CAACY,QAAQ,CAACL,SAAS,CAAC;IAC9C,MAAMO,QAAQ,GAAGd,MAAM,CAACY,QAAQ,CAACL,SAAS,GAAG,CAAC,CAAC;IAC/C,IAAIG,SAAS,GAAGG,WAAW,GAAG,CAACC,QAAQ,GAAGD,WAAW,IAAId,SAAS,EAAE;MAClEE,KAAK,IAAID,MAAM,CAACJ,MAAM,CAACa,cAAc;IACvC;EACF,CAAC,MAAM;IACL;IACA;IACA,MAAMM,QAAQ,GAAGf,MAAM,CAACY,QAAQ,CAACL,SAAS,GAAG,CAAC,CAAC;IAC/C,MAAMM,WAAW,GAAGb,MAAM,CAACY,QAAQ,CAACL,SAAS,CAAC;IAC9C,IAAIG,SAAS,GAAGK,QAAQ,IAAI,CAACF,WAAW,GAAGE,QAAQ,IAAIhB,SAAS,EAAE;MAChEE,KAAK,IAAID,MAAM,CAACJ,MAAM,CAACa,cAAc;IACvC;EACF;EACAR,KAAK,GAAGG,IAAI,CAACY,GAAG,CAACf,KAAK,EAAE,CAAC,CAAC;EAC1BA,KAAK,GAAGG,IAAI,CAACC,GAAG,CAACJ,KAAK,EAAED,MAAM,CAACiB,UAAU,CAACvB,MAAM,GAAG,CAAC,CAAC;EACrD,OAAOM,MAAM,CAACkB,OAAO,CAACjB,KAAK,EAAET,KAAK,EAAEK,YAAY,EAAEC,QAAQ,CAAC;AAC7D"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}