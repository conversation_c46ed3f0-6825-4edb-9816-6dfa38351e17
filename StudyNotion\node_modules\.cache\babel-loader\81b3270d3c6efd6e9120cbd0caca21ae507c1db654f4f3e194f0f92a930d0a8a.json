{"ast": null, "code": "export default function setGrabCursor(moving) {\n  const swiper = this;\n  if (!swiper.params.simulateTouch || swiper.params.watchOverflow && swiper.isLocked || swiper.params.cssMode) return;\n  const el = swiper.params.touchEventsTarget === 'container' ? swiper.el : swiper.wrapperEl;\n  if (swiper.isElement) {\n    swiper.__preventObserver__ = true;\n  }\n  el.style.cursor = 'move';\n  el.style.cursor = moving ? 'grabbing' : 'grab';\n  if (swiper.isElement) {\n    requestAnimationFrame(() => {\n      swiper.__preventObserver__ = false;\n    });\n  }\n}", "map": {"version": 3, "names": ["setGrabCursor", "moving", "swiper", "params", "simulate<PERSON>ouch", "watchOverflow", "isLocked", "cssMode", "el", "touchEventsTarget", "wrapperEl", "isElement", "__preventObserver__", "style", "cursor", "requestAnimationFrame"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/core/grab-cursor/setGrabCursor.js"], "sourcesContent": ["export default function setGrabCursor(moving) {\n  const swiper = this;\n  if (!swiper.params.simulateTouch || swiper.params.watchOverflow && swiper.isLocked || swiper.params.cssMode) return;\n  const el = swiper.params.touchEventsTarget === 'container' ? swiper.el : swiper.wrapperEl;\n  if (swiper.isElement) {\n    swiper.__preventObserver__ = true;\n  }\n  el.style.cursor = 'move';\n  el.style.cursor = moving ? 'grabbing' : 'grab';\n  if (swiper.isElement) {\n    requestAnimationFrame(() => {\n      swiper.__preventObserver__ = false;\n    });\n  }\n}"], "mappings": "AAAA,eAAe,SAASA,aAAaA,CAACC,MAAM,EAAE;EAC5C,MAAMC,MAAM,GAAG,IAAI;EACnB,IAAI,CAACA,MAAM,CAACC,MAAM,CAACC,aAAa,IAAIF,MAAM,CAACC,MAAM,CAACE,aAAa,IAAIH,MAAM,CAACI,QAAQ,IAAIJ,MAAM,CAACC,MAAM,CAACI,OAAO,EAAE;EAC7G,MAAMC,EAAE,GAAGN,MAAM,CAACC,MAAM,CAACM,iBAAiB,KAAK,WAAW,GAAGP,MAAM,CAACM,EAAE,GAAGN,MAAM,CAACQ,SAAS;EACzF,IAAIR,MAAM,CAACS,SAAS,EAAE;IACpBT,MAAM,CAACU,mBAAmB,GAAG,IAAI;EACnC;EACAJ,EAAE,CAACK,KAAK,CAACC,MAAM,GAAG,MAAM;EACxBN,EAAE,CAACK,KAAK,CAACC,MAAM,GAAGb,MAAM,GAAG,UAAU,GAAG,MAAM;EAC9C,IAAIC,MAAM,CAACS,SAAS,EAAE;IACpBI,qBAAqB,CAAC,MAAM;MAC1Bb,MAAM,CAACU,mBAAmB,GAAG,KAAK;IACpC,CAAC,CAAC;EACJ;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}