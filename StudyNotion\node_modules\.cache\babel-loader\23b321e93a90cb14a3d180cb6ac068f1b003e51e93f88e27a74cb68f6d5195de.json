{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\");\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar _classCallCheck2 = _interopRequireDefault(require(\"@babel/runtime/helpers/classCallCheck\"));\nvar _createClass2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createClass\"));\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"@babel/runtime/helpers/possibleConstructorReturn\"));\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"@babel/runtime/helpers/getPrototypeOf\"));\nvar _assertThisInitialized2 = _interopRequireDefault(require(\"@babel/runtime/helpers/assertThisInitialized\"));\nvar _inherits2 = _interopRequireDefault(require(\"@babel/runtime/helpers/inherits\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _react = _interopRequireWildcard(require(\"react\"));\nvar _classnames = _interopRequireDefault(require(\"classnames\"));\nvar _Slider = _interopRequireDefault(require(\"../Slider\"));\nvar _VolumeLevel = _interopRequireDefault(require(\"./VolumeLevel\"));\nvar propTypes = {\n  actions: _propTypes[\"default\"].object,\n  player: _propTypes[\"default\"].object,\n  className: _propTypes[\"default\"].string,\n  onFocus: _propTypes[\"default\"].func,\n  onBlur: _propTypes[\"default\"].func\n};\nvar VolumeBar = /*#__PURE__*/\nfunction (_Component) {\n  (0, _inherits2[\"default\"])(VolumeBar, _Component);\n  function VolumeBar(props, context) {\n    var _this;\n    (0, _classCallCheck2[\"default\"])(this, VolumeBar);\n    _this = (0, _possibleConstructorReturn2[\"default\"])(this, (0, _getPrototypeOf2[\"default\"])(VolumeBar).call(this, props, context));\n    _this.state = {\n      percentage: '0%'\n    };\n    _this.handleMouseMove = _this.handleMouseMove.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handlePercentageChange = _this.handlePercentageChange.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.checkMuted = _this.checkMuted.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.getPercent = _this.getPercent.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.stepForward = _this.stepForward.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.stepBack = _this.stepBack.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleFocus = _this.handleFocus.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleBlur = _this.handleBlur.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleClick = _this.handleClick.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    return _this;\n  }\n  (0, _createClass2[\"default\"])(VolumeBar, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {}\n  }, {\n    key: \"getPercent\",\n    value: function getPercent() {\n      var player = this.props.player;\n      if (player.muted) {\n        return 0;\n      }\n      return player.volume;\n    }\n  }, {\n    key: \"checkMuted\",\n    value: function checkMuted() {\n      var _this$props = this.props,\n        player = _this$props.player,\n        actions = _this$props.actions;\n      if (player.muted) {\n        actions.mute(false);\n      }\n    }\n  }, {\n    key: \"handleMouseMove\",\n    value: function handleMouseMove(event) {\n      var actions = this.props.actions;\n      this.checkMuted();\n      var distance = this.slider.calculateDistance(event);\n      actions.changeVolume(distance);\n    }\n  }, {\n    key: \"stepForward\",\n    value: function stepForward() {\n      var _this$props2 = this.props,\n        player = _this$props2.player,\n        actions = _this$props2.actions;\n      this.checkMuted();\n      actions.changeVolume(player.volume + 0.1);\n    }\n  }, {\n    key: \"stepBack\",\n    value: function stepBack() {\n      var _this$props3 = this.props,\n        player = _this$props3.player,\n        actions = _this$props3.actions;\n      this.checkMuted();\n      actions.changeVolume(player.volume - 0.1);\n    }\n  }, {\n    key: \"handleFocus\",\n    value: function handleFocus(e) {\n      if (this.props.onFocus) {\n        this.props.onFocus(e);\n      }\n    }\n  }, {\n    key: \"handleBlur\",\n    value: function handleBlur(e) {\n      if (this.props.onBlur) {\n        this.props.onBlur(e);\n      }\n    }\n  }, {\n    key: \"handlePercentageChange\",\n    value: function handlePercentageChange(percentage) {\n      if (percentage !== this.state.percentage) {\n        this.setState({\n          percentage: percentage\n        });\n      }\n    }\n  }, {\n    key: \"handleClick\",\n    value: function handleClick(event) {\n      event.stopPropagation();\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this2 = this;\n      var _this$props4 = this.props,\n        player = _this$props4.player,\n        className = _this$props4.className;\n      var volume = (player.volume * 100).toFixed(2);\n      return _react[\"default\"].createElement(_Slider[\"default\"], (0, _extends2[\"default\"])({\n        ref: function ref(c) {\n          _this2.slider = c;\n        },\n        label: \"volume level\",\n        valuenow: volume,\n        valuetext: \"\".concat(volume, \"%\"),\n        onMouseMove: this.handleMouseMove,\n        onFocus: this.handleFocus,\n        onBlur: this.handleBlur,\n        onClick: this.handleClick,\n        sliderActive: this.handleFocus,\n        sliderInactive: this.handleBlur,\n        getPercent: this.getPercent,\n        onPercentageChange: this.handlePercentageChange,\n        stepForward: this.stepForward,\n        stepBack: this.stepBack\n      }, this.props, {\n        className: (0, _classnames[\"default\"])(className, 'video-react-volume-bar video-react-slider-bar')\n      }), _react[\"default\"].createElement(_VolumeLevel[\"default\"], this.props));\n    }\n  }]);\n  return VolumeBar;\n}(_react.Component);\nVolumeBar.propTypes = propTypes;\nVolumeBar.displayName = 'VolumeBar';\nvar _default = VolumeBar;\nexports[\"default\"] = _default;", "map": {"version": 3, "names": ["_interopRequireWildcard", "require", "_interopRequireDefault", "Object", "defineProperty", "exports", "value", "_extends2", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_assertThisInitialized2", "_inherits2", "_propTypes", "_react", "_classnames", "_<PERSON><PERSON><PERSON>", "_VolumeLevel", "propTypes", "actions", "object", "player", "className", "string", "onFocus", "func", "onBlur", "VolumeBar", "_Component", "props", "context", "_this", "call", "state", "percentage", "handleMouseMove", "bind", "handlePercentageChange", "checkMuted", "getPercent", "stepForward", "stepBack", "handleFocus", "handleBlur", "handleClick", "key", "componentDidMount", "muted", "volume", "_this$props", "mute", "event", "distance", "slider", "calculateDistance", "changeVolume", "_this$props2", "_this$props3", "e", "setState", "stopPropagation", "render", "_this2", "_this$props4", "toFixed", "createElement", "ref", "c", "label", "valuenow", "valuetext", "concat", "onMouseMove", "onClick", "sliderActive", "sliderInactive", "onPercentageChange", "Component", "displayName", "_default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/video-react/lib/components/volume-control/VolumeBar.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\");\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\n\nvar _classCallCheck2 = _interopRequireDefault(require(\"@babel/runtime/helpers/classCallCheck\"));\n\nvar _createClass2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createClass\"));\n\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"@babel/runtime/helpers/possibleConstructorReturn\"));\n\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"@babel/runtime/helpers/getPrototypeOf\"));\n\nvar _assertThisInitialized2 = _interopRequireDefault(require(\"@babel/runtime/helpers/assertThisInitialized\"));\n\nvar _inherits2 = _interopRequireDefault(require(\"@babel/runtime/helpers/inherits\"));\n\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\n\nvar _react = _interopRequireWildcard(require(\"react\"));\n\nvar _classnames = _interopRequireDefault(require(\"classnames\"));\n\nvar _Slider = _interopRequireDefault(require(\"../Slider\"));\n\nvar _VolumeLevel = _interopRequireDefault(require(\"./VolumeLevel\"));\n\nvar propTypes = {\n  actions: _propTypes[\"default\"].object,\n  player: _propTypes[\"default\"].object,\n  className: _propTypes[\"default\"].string,\n  onFocus: _propTypes[\"default\"].func,\n  onBlur: _propTypes[\"default\"].func\n};\n\nvar VolumeBar =\n/*#__PURE__*/\nfunction (_Component) {\n  (0, _inherits2[\"default\"])(VolumeBar, _Component);\n\n  function VolumeBar(props, context) {\n    var _this;\n\n    (0, _classCallCheck2[\"default\"])(this, VolumeBar);\n    _this = (0, _possibleConstructorReturn2[\"default\"])(this, (0, _getPrototypeOf2[\"default\"])(VolumeBar).call(this, props, context));\n    _this.state = {\n      percentage: '0%'\n    };\n    _this.handleMouseMove = _this.handleMouseMove.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handlePercentageChange = _this.handlePercentageChange.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.checkMuted = _this.checkMuted.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.getPercent = _this.getPercent.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.stepForward = _this.stepForward.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.stepBack = _this.stepBack.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleFocus = _this.handleFocus.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleBlur = _this.handleBlur.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleClick = _this.handleClick.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    return _this;\n  }\n\n  (0, _createClass2[\"default\"])(VolumeBar, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {}\n  }, {\n    key: \"getPercent\",\n    value: function getPercent() {\n      var player = this.props.player;\n\n      if (player.muted) {\n        return 0;\n      }\n\n      return player.volume;\n    }\n  }, {\n    key: \"checkMuted\",\n    value: function checkMuted() {\n      var _this$props = this.props,\n          player = _this$props.player,\n          actions = _this$props.actions;\n\n      if (player.muted) {\n        actions.mute(false);\n      }\n    }\n  }, {\n    key: \"handleMouseMove\",\n    value: function handleMouseMove(event) {\n      var actions = this.props.actions;\n      this.checkMuted();\n      var distance = this.slider.calculateDistance(event);\n      actions.changeVolume(distance);\n    }\n  }, {\n    key: \"stepForward\",\n    value: function stepForward() {\n      var _this$props2 = this.props,\n          player = _this$props2.player,\n          actions = _this$props2.actions;\n      this.checkMuted();\n      actions.changeVolume(player.volume + 0.1);\n    }\n  }, {\n    key: \"stepBack\",\n    value: function stepBack() {\n      var _this$props3 = this.props,\n          player = _this$props3.player,\n          actions = _this$props3.actions;\n      this.checkMuted();\n      actions.changeVolume(player.volume - 0.1);\n    }\n  }, {\n    key: \"handleFocus\",\n    value: function handleFocus(e) {\n      if (this.props.onFocus) {\n        this.props.onFocus(e);\n      }\n    }\n  }, {\n    key: \"handleBlur\",\n    value: function handleBlur(e) {\n      if (this.props.onBlur) {\n        this.props.onBlur(e);\n      }\n    }\n  }, {\n    key: \"handlePercentageChange\",\n    value: function handlePercentageChange(percentage) {\n      if (percentage !== this.state.percentage) {\n        this.setState({\n          percentage: percentage\n        });\n      }\n    }\n  }, {\n    key: \"handleClick\",\n    value: function handleClick(event) {\n      event.stopPropagation();\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this2 = this;\n\n      var _this$props4 = this.props,\n          player = _this$props4.player,\n          className = _this$props4.className;\n      var volume = (player.volume * 100).toFixed(2);\n      return _react[\"default\"].createElement(_Slider[\"default\"], (0, _extends2[\"default\"])({\n        ref: function ref(c) {\n          _this2.slider = c;\n        },\n        label: \"volume level\",\n        valuenow: volume,\n        valuetext: \"\".concat(volume, \"%\"),\n        onMouseMove: this.handleMouseMove,\n        onFocus: this.handleFocus,\n        onBlur: this.handleBlur,\n        onClick: this.handleClick,\n        sliderActive: this.handleFocus,\n        sliderInactive: this.handleBlur,\n        getPercent: this.getPercent,\n        onPercentageChange: this.handlePercentageChange,\n        stepForward: this.stepForward,\n        stepBack: this.stepBack\n      }, this.props, {\n        className: (0, _classnames[\"default\"])(className, 'video-react-volume-bar video-react-slider-bar')\n      }), _react[\"default\"].createElement(_VolumeLevel[\"default\"], this.props));\n    }\n  }]);\n  return VolumeBar;\n}(_react.Component);\n\nVolumeBar.propTypes = propTypes;\nVolumeBar.displayName = 'VolumeBar';\nvar _default = VolumeBar;\nexports[\"default\"] = _default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,uBAAuB,GAAGC,OAAO,CAAC,+CAA+C,CAAC;AAEtF,IAAIC,sBAAsB,GAAGD,OAAO,CAAC,8CAA8C,CAAC;AAEpFE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;AAE3B,IAAIE,SAAS,GAAGL,sBAAsB,CAACD,OAAO,CAAC,gCAAgC,CAAC,CAAC;AAEjF,IAAIO,gBAAgB,GAAGN,sBAAsB,CAACD,OAAO,CAAC,uCAAuC,CAAC,CAAC;AAE/F,IAAIQ,aAAa,GAAGP,sBAAsB,CAACD,OAAO,CAAC,oCAAoC,CAAC,CAAC;AAEzF,IAAIS,2BAA2B,GAAGR,sBAAsB,CAACD,OAAO,CAAC,kDAAkD,CAAC,CAAC;AAErH,IAAIU,gBAAgB,GAAGT,sBAAsB,CAACD,OAAO,CAAC,uCAAuC,CAAC,CAAC;AAE/F,IAAIW,uBAAuB,GAAGV,sBAAsB,CAACD,OAAO,CAAC,8CAA8C,CAAC,CAAC;AAE7G,IAAIY,UAAU,GAAGX,sBAAsB,CAACD,OAAO,CAAC,iCAAiC,CAAC,CAAC;AAEnF,IAAIa,UAAU,GAAGZ,sBAAsB,CAACD,OAAO,CAAC,YAAY,CAAC,CAAC;AAE9D,IAAIc,MAAM,GAAGf,uBAAuB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AAEtD,IAAIe,WAAW,GAAGd,sBAAsB,CAACD,OAAO,CAAC,YAAY,CAAC,CAAC;AAE/D,IAAIgB,OAAO,GAAGf,sBAAsB,CAACD,OAAO,CAAC,WAAW,CAAC,CAAC;AAE1D,IAAIiB,YAAY,GAAGhB,sBAAsB,CAACD,OAAO,CAAC,eAAe,CAAC,CAAC;AAEnE,IAAIkB,SAAS,GAAG;EACdC,OAAO,EAAEN,UAAU,CAAC,SAAS,CAAC,CAACO,MAAM;EACrCC,MAAM,EAAER,UAAU,CAAC,SAAS,CAAC,CAACO,MAAM;EACpCE,SAAS,EAAET,UAAU,CAAC,SAAS,CAAC,CAACU,MAAM;EACvCC,OAAO,EAAEX,UAAU,CAAC,SAAS,CAAC,CAACY,IAAI;EACnCC,MAAM,EAAEb,UAAU,CAAC,SAAS,CAAC,CAACY;AAChC,CAAC;AAED,IAAIE,SAAS,GACb;AACA,UAAUC,UAAU,EAAE;EACpB,CAAC,CAAC,EAAEhB,UAAU,CAAC,SAAS,CAAC,EAAEe,SAAS,EAAEC,UAAU,CAAC;EAEjD,SAASD,SAASA,CAACE,KAAK,EAAEC,OAAO,EAAE;IACjC,IAAIC,KAAK;IAET,CAAC,CAAC,EAAExB,gBAAgB,CAAC,SAAS,CAAC,EAAE,IAAI,EAAEoB,SAAS,CAAC;IACjDI,KAAK,GAAG,CAAC,CAAC,EAAEtB,2BAA2B,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAEC,gBAAgB,CAAC,SAAS,CAAC,EAAEiB,SAAS,CAAC,CAACK,IAAI,CAAC,IAAI,EAAEH,KAAK,EAAEC,OAAO,CAAC,CAAC;IACjIC,KAAK,CAACE,KAAK,GAAG;MACZC,UAAU,EAAE;IACd,CAAC;IACDH,KAAK,CAACI,eAAe,GAAGJ,KAAK,CAACI,eAAe,CAACC,IAAI,CAAC,CAAC,CAAC,EAAEzB,uBAAuB,CAAC,SAAS,CAAC,EAAEoB,KAAK,CAAC,CAAC;IAClGA,KAAK,CAACM,sBAAsB,GAAGN,KAAK,CAACM,sBAAsB,CAACD,IAAI,CAAC,CAAC,CAAC,EAAEzB,uBAAuB,CAAC,SAAS,CAAC,EAAEoB,KAAK,CAAC,CAAC;IAChHA,KAAK,CAACO,UAAU,GAAGP,KAAK,CAACO,UAAU,CAACF,IAAI,CAAC,CAAC,CAAC,EAAEzB,uBAAuB,CAAC,SAAS,CAAC,EAAEoB,KAAK,CAAC,CAAC;IACxFA,KAAK,CAACQ,UAAU,GAAGR,KAAK,CAACQ,UAAU,CAACH,IAAI,CAAC,CAAC,CAAC,EAAEzB,uBAAuB,CAAC,SAAS,CAAC,EAAEoB,KAAK,CAAC,CAAC;IACxFA,KAAK,CAACS,WAAW,GAAGT,KAAK,CAACS,WAAW,CAACJ,IAAI,CAAC,CAAC,CAAC,EAAEzB,uBAAuB,CAAC,SAAS,CAAC,EAAEoB,KAAK,CAAC,CAAC;IAC1FA,KAAK,CAACU,QAAQ,GAAGV,KAAK,CAACU,QAAQ,CAACL,IAAI,CAAC,CAAC,CAAC,EAAEzB,uBAAuB,CAAC,SAAS,CAAC,EAAEoB,KAAK,CAAC,CAAC;IACpFA,KAAK,CAACW,WAAW,GAAGX,KAAK,CAACW,WAAW,CAACN,IAAI,CAAC,CAAC,CAAC,EAAEzB,uBAAuB,CAAC,SAAS,CAAC,EAAEoB,KAAK,CAAC,CAAC;IAC1FA,KAAK,CAACY,UAAU,GAAGZ,KAAK,CAACY,UAAU,CAACP,IAAI,CAAC,CAAC,CAAC,EAAEzB,uBAAuB,CAAC,SAAS,CAAC,EAAEoB,KAAK,CAAC,CAAC;IACxFA,KAAK,CAACa,WAAW,GAAGb,KAAK,CAACa,WAAW,CAACR,IAAI,CAAC,CAAC,CAAC,EAAEzB,uBAAuB,CAAC,SAAS,CAAC,EAAEoB,KAAK,CAAC,CAAC;IAC1F,OAAOA,KAAK;EACd;EAEA,CAAC,CAAC,EAAEvB,aAAa,CAAC,SAAS,CAAC,EAAEmB,SAAS,EAAE,CAAC;IACxCkB,GAAG,EAAE,mBAAmB;IACxBxC,KAAK,EAAE,SAASyC,iBAAiBA,CAAA,EAAG,CAAC;EACvC,CAAC,EAAE;IACDD,GAAG,EAAE,YAAY;IACjBxC,KAAK,EAAE,SAASkC,UAAUA,CAAA,EAAG;MAC3B,IAAIlB,MAAM,GAAG,IAAI,CAACQ,KAAK,CAACR,MAAM;MAE9B,IAAIA,MAAM,CAAC0B,KAAK,EAAE;QAChB,OAAO,CAAC;MACV;MAEA,OAAO1B,MAAM,CAAC2B,MAAM;IACtB;EACF,CAAC,EAAE;IACDH,GAAG,EAAE,YAAY;IACjBxC,KAAK,EAAE,SAASiC,UAAUA,CAAA,EAAG;MAC3B,IAAIW,WAAW,GAAG,IAAI,CAACpB,KAAK;QACxBR,MAAM,GAAG4B,WAAW,CAAC5B,MAAM;QAC3BF,OAAO,GAAG8B,WAAW,CAAC9B,OAAO;MAEjC,IAAIE,MAAM,CAAC0B,KAAK,EAAE;QAChB5B,OAAO,CAAC+B,IAAI,CAAC,KAAK,CAAC;MACrB;IACF;EACF,CAAC,EAAE;IACDL,GAAG,EAAE,iBAAiB;IACtBxC,KAAK,EAAE,SAAS8B,eAAeA,CAACgB,KAAK,EAAE;MACrC,IAAIhC,OAAO,GAAG,IAAI,CAACU,KAAK,CAACV,OAAO;MAChC,IAAI,CAACmB,UAAU,EAAE;MACjB,IAAIc,QAAQ,GAAG,IAAI,CAACC,MAAM,CAACC,iBAAiB,CAACH,KAAK,CAAC;MACnDhC,OAAO,CAACoC,YAAY,CAACH,QAAQ,CAAC;IAChC;EACF,CAAC,EAAE;IACDP,GAAG,EAAE,aAAa;IAClBxC,KAAK,EAAE,SAASmC,WAAWA,CAAA,EAAG;MAC5B,IAAIgB,YAAY,GAAG,IAAI,CAAC3B,KAAK;QACzBR,MAAM,GAAGmC,YAAY,CAACnC,MAAM;QAC5BF,OAAO,GAAGqC,YAAY,CAACrC,OAAO;MAClC,IAAI,CAACmB,UAAU,EAAE;MACjBnB,OAAO,CAACoC,YAAY,CAAClC,MAAM,CAAC2B,MAAM,GAAG,GAAG,CAAC;IAC3C;EACF,CAAC,EAAE;IACDH,GAAG,EAAE,UAAU;IACfxC,KAAK,EAAE,SAASoC,QAAQA,CAAA,EAAG;MACzB,IAAIgB,YAAY,GAAG,IAAI,CAAC5B,KAAK;QACzBR,MAAM,GAAGoC,YAAY,CAACpC,MAAM;QAC5BF,OAAO,GAAGsC,YAAY,CAACtC,OAAO;MAClC,IAAI,CAACmB,UAAU,EAAE;MACjBnB,OAAO,CAACoC,YAAY,CAAClC,MAAM,CAAC2B,MAAM,GAAG,GAAG,CAAC;IAC3C;EACF,CAAC,EAAE;IACDH,GAAG,EAAE,aAAa;IAClBxC,KAAK,EAAE,SAASqC,WAAWA,CAACgB,CAAC,EAAE;MAC7B,IAAI,IAAI,CAAC7B,KAAK,CAACL,OAAO,EAAE;QACtB,IAAI,CAACK,KAAK,CAACL,OAAO,CAACkC,CAAC,CAAC;MACvB;IACF;EACF,CAAC,EAAE;IACDb,GAAG,EAAE,YAAY;IACjBxC,KAAK,EAAE,SAASsC,UAAUA,CAACe,CAAC,EAAE;MAC5B,IAAI,IAAI,CAAC7B,KAAK,CAACH,MAAM,EAAE;QACrB,IAAI,CAACG,KAAK,CAACH,MAAM,CAACgC,CAAC,CAAC;MACtB;IACF;EACF,CAAC,EAAE;IACDb,GAAG,EAAE,wBAAwB;IAC7BxC,KAAK,EAAE,SAASgC,sBAAsBA,CAACH,UAAU,EAAE;MACjD,IAAIA,UAAU,KAAK,IAAI,CAACD,KAAK,CAACC,UAAU,EAAE;QACxC,IAAI,CAACyB,QAAQ,CAAC;UACZzB,UAAU,EAAEA;QACd,CAAC,CAAC;MACJ;IACF;EACF,CAAC,EAAE;IACDW,GAAG,EAAE,aAAa;IAClBxC,KAAK,EAAE,SAASuC,WAAWA,CAACO,KAAK,EAAE;MACjCA,KAAK,CAACS,eAAe,EAAE;IACzB;EACF,CAAC,EAAE;IACDf,GAAG,EAAE,QAAQ;IACbxC,KAAK,EAAE,SAASwD,MAAMA,CAAA,EAAG;MACvB,IAAIC,MAAM,GAAG,IAAI;MAEjB,IAAIC,YAAY,GAAG,IAAI,CAAClC,KAAK;QACzBR,MAAM,GAAG0C,YAAY,CAAC1C,MAAM;QAC5BC,SAAS,GAAGyC,YAAY,CAACzC,SAAS;MACtC,IAAI0B,MAAM,GAAG,CAAC3B,MAAM,CAAC2B,MAAM,GAAG,GAAG,EAAEgB,OAAO,CAAC,CAAC,CAAC;MAC7C,OAAOlD,MAAM,CAAC,SAAS,CAAC,CAACmD,aAAa,CAACjD,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAEV,SAAS,CAAC,SAAS,CAAC,EAAE;QACnF4D,GAAG,EAAE,SAASA,GAAGA,CAACC,CAAC,EAAE;UACnBL,MAAM,CAACT,MAAM,GAAGc,CAAC;QACnB,CAAC;QACDC,KAAK,EAAE,cAAc;QACrBC,QAAQ,EAAErB,MAAM;QAChBsB,SAAS,EAAE,EAAE,CAACC,MAAM,CAACvB,MAAM,EAAE,GAAG,CAAC;QACjCwB,WAAW,EAAE,IAAI,CAACrC,eAAe;QACjCX,OAAO,EAAE,IAAI,CAACkB,WAAW;QACzBhB,MAAM,EAAE,IAAI,CAACiB,UAAU;QACvB8B,OAAO,EAAE,IAAI,CAAC7B,WAAW;QACzB8B,YAAY,EAAE,IAAI,CAAChC,WAAW;QAC9BiC,cAAc,EAAE,IAAI,CAAChC,UAAU;QAC/BJ,UAAU,EAAE,IAAI,CAACA,UAAU;QAC3BqC,kBAAkB,EAAE,IAAI,CAACvC,sBAAsB;QAC/CG,WAAW,EAAE,IAAI,CAACA,WAAW;QAC7BC,QAAQ,EAAE,IAAI,CAACA;MACjB,CAAC,EAAE,IAAI,CAACZ,KAAK,EAAE;QACbP,SAAS,EAAE,CAAC,CAAC,EAAEP,WAAW,CAAC,SAAS,CAAC,EAAEO,SAAS,EAAE,+CAA+C;MACnG,CAAC,CAAC,EAAER,MAAM,CAAC,SAAS,CAAC,CAACmD,aAAa,CAAChD,YAAY,CAAC,SAAS,CAAC,EAAE,IAAI,CAACY,KAAK,CAAC,CAAC;IAC3E;EACF,CAAC,CAAC,CAAC;EACH,OAAOF,SAAS;AAClB,CAAC,CAACb,MAAM,CAAC+D,SAAS,CAAC;AAEnBlD,SAAS,CAACT,SAAS,GAAGA,SAAS;AAC/BS,SAAS,CAACmD,WAAW,GAAG,WAAW;AACnC,IAAIC,QAAQ,GAAGpD,SAAS;AACxBvB,OAAO,CAAC,SAAS,CAAC,GAAG2E,QAAQ"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}