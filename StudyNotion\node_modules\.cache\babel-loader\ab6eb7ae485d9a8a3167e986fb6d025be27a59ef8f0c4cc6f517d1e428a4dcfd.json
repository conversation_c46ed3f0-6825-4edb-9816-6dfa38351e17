{"ast": null, "code": "import { getWindow, getDocument } from 'ssr-window';\nlet support;\nfunction calcSupport() {\n  const window = getWindow();\n  const document = getDocument();\n  return {\n    smoothScroll: document.documentElement && document.documentElement.style && 'scrollBehavior' in document.documentElement.style,\n    touch: !!('ontouchstart' in window || window.DocumentTouch && document instanceof window.DocumentTouch)\n  };\n}\nfunction getSupport() {\n  if (!support) {\n    support = calcSupport();\n  }\n  return support;\n}\nexport { getSupport };", "map": {"version": 3, "names": ["getWindow", "getDocument", "support", "calcSupport", "window", "document", "smoothScroll", "documentElement", "style", "touch", "DocumentTouch", "getSupport"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/shared/get-support.js"], "sourcesContent": ["import { getWindow, getDocument } from 'ssr-window';\nlet support;\nfunction calcSupport() {\n  const window = getWindow();\n  const document = getDocument();\n  return {\n    smoothScroll: document.documentElement && document.documentElement.style && 'scrollBehavior' in document.documentElement.style,\n    touch: !!('ontouchstart' in window || window.DocumentTouch && document instanceof window.DocumentTouch)\n  };\n}\nfunction getSupport() {\n  if (!support) {\n    support = calcSupport();\n  }\n  return support;\n}\nexport { getSupport };"], "mappings": "AAAA,SAASA,SAAS,EAAEC,WAAW,QAAQ,YAAY;AACnD,IAAIC,OAAO;AACX,SAASC,WAAWA,CAAA,EAAG;EACrB,MAAMC,MAAM,GAAGJ,SAAS,EAAE;EAC1B,MAAMK,QAAQ,GAAGJ,WAAW,EAAE;EAC9B,OAAO;IACLK,YAAY,EAAED,QAAQ,CAACE,eAAe,IAAIF,QAAQ,CAACE,eAAe,CAACC,KAAK,IAAI,gBAAgB,IAAIH,QAAQ,CAACE,eAAe,CAACC,KAAK;IAC9HC,KAAK,EAAE,CAAC,EAAE,cAAc,IAAIL,MAAM,IAAIA,MAAM,CAACM,aAAa,IAAIL,QAAQ,YAAYD,MAAM,CAACM,aAAa;EACxG,CAAC;AACH;AACA,SAASC,UAAUA,CAAA,EAAG;EACpB,IAAI,CAACT,OAAO,EAAE;IACZA,OAAO,GAAGC,WAAW,EAAE;EACzB;EACA,OAAOD,OAAO;AAChB;AACA,SAASS,UAAU"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}