{"ast": null, "code": "import { getDocument } from 'ssr-window';\nimport { createElement, elementChildren, setCSSProperty } from '../../shared/utils.js';\nexport default function Virtual(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on,\n    emit\n  } = _ref;\n  extendParams({\n    virtual: {\n      enabled: false,\n      slides: [],\n      cache: true,\n      renderSlide: null,\n      renderExternal: null,\n      renderExternalUpdate: true,\n      addSlidesBefore: 0,\n      addSlidesAfter: 0\n    }\n  });\n  let cssModeTimeout;\n  const document = getDocument();\n  swiper.virtual = {\n    cache: {},\n    from: undefined,\n    to: undefined,\n    slides: [],\n    offset: 0,\n    slidesGrid: []\n  };\n  const tempDOM = document.createElement('div');\n  function renderSlide(slide, index) {\n    const params = swiper.params.virtual;\n    if (params.cache && swiper.virtual.cache[index]) {\n      return swiper.virtual.cache[index];\n    }\n    // eslint-disable-next-line\n    let slideEl;\n    if (params.renderSlide) {\n      slideEl = params.renderSlide.call(swiper, slide, index);\n      if (typeof slideEl === 'string') {\n        tempDOM.innerHTML = slideEl;\n        slideEl = tempDOM.children[0];\n      }\n    } else if (swiper.isElement) {\n      slideEl = createElement('swiper-slide');\n    } else {\n      slideEl = createElement('div', swiper.params.slideClass);\n    }\n    slideEl.setAttribute('data-swiper-slide-index', index);\n    if (!params.renderSlide) {\n      slideEl.innerHTML = slide;\n    }\n    if (params.cache) swiper.virtual.cache[index] = slideEl;\n    return slideEl;\n  }\n  function update(force) {\n    const {\n      slidesPerView,\n      slidesPerGroup,\n      centeredSlides,\n      loop: isLoop\n    } = swiper.params;\n    const {\n      addSlidesBefore,\n      addSlidesAfter\n    } = swiper.params.virtual;\n    const {\n      from: previousFrom,\n      to: previousTo,\n      slides,\n      slidesGrid: previousSlidesGrid,\n      offset: previousOffset\n    } = swiper.virtual;\n    if (!swiper.params.cssMode) {\n      swiper.updateActiveIndex();\n    }\n    const activeIndex = swiper.activeIndex || 0;\n    let offsetProp;\n    if (swiper.rtlTranslate) offsetProp = 'right';else offsetProp = swiper.isHorizontal() ? 'left' : 'top';\n    let slidesAfter;\n    let slidesBefore;\n    if (centeredSlides) {\n      slidesAfter = Math.floor(slidesPerView / 2) + slidesPerGroup + addSlidesAfter;\n      slidesBefore = Math.floor(slidesPerView / 2) + slidesPerGroup + addSlidesBefore;\n    } else {\n      slidesAfter = slidesPerView + (slidesPerGroup - 1) + addSlidesAfter;\n      slidesBefore = (isLoop ? slidesPerView : slidesPerGroup) + addSlidesBefore;\n    }\n    let from = activeIndex - slidesBefore;\n    let to = activeIndex + slidesAfter;\n    if (!isLoop) {\n      from = Math.max(from, 0);\n      to = Math.min(to, slides.length - 1);\n    }\n    let offset = (swiper.slidesGrid[from] || 0) - (swiper.slidesGrid[0] || 0);\n    if (isLoop && activeIndex >= slidesBefore) {\n      from -= slidesBefore;\n      if (!centeredSlides) offset += swiper.slidesGrid[0];\n    } else if (isLoop && activeIndex < slidesBefore) {\n      from = -slidesBefore;\n      if (centeredSlides) offset += swiper.slidesGrid[0];\n    }\n    Object.assign(swiper.virtual, {\n      from,\n      to,\n      offset,\n      slidesGrid: swiper.slidesGrid,\n      slidesBefore,\n      slidesAfter\n    });\n    function onRendered() {\n      swiper.updateSlides();\n      swiper.updateProgress();\n      swiper.updateSlidesClasses();\n      emit('virtualUpdate');\n    }\n    if (previousFrom === from && previousTo === to && !force) {\n      if (swiper.slidesGrid !== previousSlidesGrid && offset !== previousOffset) {\n        swiper.slides.forEach(slideEl => {\n          slideEl.style[offsetProp] = `${offset - Math.abs(swiper.cssOverflowAdjustment())}px`;\n        });\n      }\n      swiper.updateProgress();\n      emit('virtualUpdate');\n      return;\n    }\n    if (swiper.params.virtual.renderExternal) {\n      swiper.params.virtual.renderExternal.call(swiper, {\n        offset,\n        from,\n        to,\n        slides: function getSlides() {\n          const slidesToRender = [];\n          for (let i = from; i <= to; i += 1) {\n            slidesToRender.push(slides[i]);\n          }\n          return slidesToRender;\n        }()\n      });\n      if (swiper.params.virtual.renderExternalUpdate) {\n        onRendered();\n      } else {\n        emit('virtualUpdate');\n      }\n      return;\n    }\n    const prependIndexes = [];\n    const appendIndexes = [];\n    const getSlideIndex = index => {\n      let slideIndex = index;\n      if (index < 0) {\n        slideIndex = slides.length + index;\n      } else if (slideIndex >= slides.length) {\n        // eslint-disable-next-line\n        slideIndex = slideIndex - slides.length;\n      }\n      return slideIndex;\n    };\n    if (force) {\n      swiper.slidesEl.querySelectorAll(`.${swiper.params.slideClass}, swiper-slide`).forEach(slideEl => {\n        slideEl.remove();\n      });\n    } else {\n      for (let i = previousFrom; i <= previousTo; i += 1) {\n        if (i < from || i > to) {\n          const slideIndex = getSlideIndex(i);\n          swiper.slidesEl.querySelectorAll(`.${swiper.params.slideClass}[data-swiper-slide-index=\"${slideIndex}\"], swiper-slide[data-swiper-slide-index=\"${slideIndex}\"]`).forEach(slideEl => {\n            slideEl.remove();\n          });\n        }\n      }\n    }\n    const loopFrom = isLoop ? -slides.length : 0;\n    const loopTo = isLoop ? slides.length * 2 : slides.length;\n    for (let i = loopFrom; i < loopTo; i += 1) {\n      if (i >= from && i <= to) {\n        const slideIndex = getSlideIndex(i);\n        if (typeof previousTo === 'undefined' || force) {\n          appendIndexes.push(slideIndex);\n        } else {\n          if (i > previousTo) appendIndexes.push(slideIndex);\n          if (i < previousFrom) prependIndexes.push(slideIndex);\n        }\n      }\n    }\n    appendIndexes.forEach(index => {\n      swiper.slidesEl.append(renderSlide(slides[index], index));\n    });\n    if (isLoop) {\n      for (let i = prependIndexes.length - 1; i >= 0; i -= 1) {\n        const index = prependIndexes[i];\n        swiper.slidesEl.prepend(renderSlide(slides[index], index));\n      }\n    } else {\n      prependIndexes.sort((a, b) => b - a);\n      prependIndexes.forEach(index => {\n        swiper.slidesEl.prepend(renderSlide(slides[index], index));\n      });\n    }\n    elementChildren(swiper.slidesEl, '.swiper-slide, swiper-slide').forEach(slideEl => {\n      slideEl.style[offsetProp] = `${offset - Math.abs(swiper.cssOverflowAdjustment())}px`;\n    });\n    onRendered();\n  }\n  function appendSlide(slides) {\n    if (typeof slides === 'object' && 'length' in slides) {\n      for (let i = 0; i < slides.length; i += 1) {\n        if (slides[i]) swiper.virtual.slides.push(slides[i]);\n      }\n    } else {\n      swiper.virtual.slides.push(slides);\n    }\n    update(true);\n  }\n  function prependSlide(slides) {\n    const activeIndex = swiper.activeIndex;\n    let newActiveIndex = activeIndex + 1;\n    let numberOfNewSlides = 1;\n    if (Array.isArray(slides)) {\n      for (let i = 0; i < slides.length; i += 1) {\n        if (slides[i]) swiper.virtual.slides.unshift(slides[i]);\n      }\n      newActiveIndex = activeIndex + slides.length;\n      numberOfNewSlides = slides.length;\n    } else {\n      swiper.virtual.slides.unshift(slides);\n    }\n    if (swiper.params.virtual.cache) {\n      const cache = swiper.virtual.cache;\n      const newCache = {};\n      Object.keys(cache).forEach(cachedIndex => {\n        const cachedEl = cache[cachedIndex];\n        const cachedElIndex = cachedEl.getAttribute('data-swiper-slide-index');\n        if (cachedElIndex) {\n          cachedEl.setAttribute('data-swiper-slide-index', parseInt(cachedElIndex, 10) + numberOfNewSlides);\n        }\n        newCache[parseInt(cachedIndex, 10) + numberOfNewSlides] = cachedEl;\n      });\n      swiper.virtual.cache = newCache;\n    }\n    update(true);\n    swiper.slideTo(newActiveIndex, 0);\n  }\n  function removeSlide(slidesIndexes) {\n    if (typeof slidesIndexes === 'undefined' || slidesIndexes === null) return;\n    let activeIndex = swiper.activeIndex;\n    if (Array.isArray(slidesIndexes)) {\n      for (let i = slidesIndexes.length - 1; i >= 0; i -= 1) {\n        swiper.virtual.slides.splice(slidesIndexes[i], 1);\n        if (swiper.params.virtual.cache) {\n          delete swiper.virtual.cache[slidesIndexes[i]];\n        }\n        if (slidesIndexes[i] < activeIndex) activeIndex -= 1;\n        activeIndex = Math.max(activeIndex, 0);\n      }\n    } else {\n      swiper.virtual.slides.splice(slidesIndexes, 1);\n      if (swiper.params.virtual.cache) {\n        delete swiper.virtual.cache[slidesIndexes];\n      }\n      if (slidesIndexes < activeIndex) activeIndex -= 1;\n      activeIndex = Math.max(activeIndex, 0);\n    }\n    update(true);\n    swiper.slideTo(activeIndex, 0);\n  }\n  function removeAllSlides() {\n    swiper.virtual.slides = [];\n    if (swiper.params.virtual.cache) {\n      swiper.virtual.cache = {};\n    }\n    update(true);\n    swiper.slideTo(0, 0);\n  }\n  on('beforeInit', () => {\n    if (!swiper.params.virtual.enabled) return;\n    let domSlidesAssigned;\n    if (typeof swiper.passedParams.virtual.slides === 'undefined') {\n      const slides = [...swiper.slidesEl.children].filter(el => el.matches(`.${swiper.params.slideClass}, swiper-slide`));\n      if (slides && slides.length) {\n        swiper.virtual.slides = [...slides];\n        domSlidesAssigned = true;\n        slides.forEach((slideEl, slideIndex) => {\n          slideEl.setAttribute('data-swiper-slide-index', slideIndex);\n          swiper.virtual.cache[slideIndex] = slideEl;\n          slideEl.remove();\n        });\n      }\n    }\n    if (!domSlidesAssigned) {\n      swiper.virtual.slides = swiper.params.virtual.slides;\n    }\n    swiper.classNames.push(`${swiper.params.containerModifierClass}virtual`);\n    swiper.params.watchSlidesProgress = true;\n    swiper.originalParams.watchSlidesProgress = true;\n    if (!swiper.params.initialSlide) {\n      update();\n    }\n  });\n  on('setTranslate', () => {\n    if (!swiper.params.virtual.enabled) return;\n    if (swiper.params.cssMode && !swiper._immediateVirtual) {\n      clearTimeout(cssModeTimeout);\n      cssModeTimeout = setTimeout(() => {\n        update();\n      }, 100);\n    } else {\n      update();\n    }\n  });\n  on('init update resize', () => {\n    if (!swiper.params.virtual.enabled) return;\n    if (swiper.params.cssMode) {\n      setCSSProperty(swiper.wrapperEl, '--swiper-virtual-size', `${swiper.virtualSize}px`);\n    }\n  });\n  Object.assign(swiper.virtual, {\n    appendSlide,\n    prependSlide,\n    removeSlide,\n    removeAllSlides,\n    update\n  });\n}", "map": {"version": 3, "names": ["getDocument", "createElement", "elementChildren", "setCSSProperty", "Virtual", "_ref", "swiper", "extendParams", "on", "emit", "virtual", "enabled", "slides", "cache", "renderSlide", "renderExternal", "renderExternalUpdate", "addSlidesBefore", "addSlidesAfter", "cssModeTimeout", "document", "from", "undefined", "to", "offset", "slidesGrid", "tempDOM", "slide", "index", "params", "slideEl", "call", "innerHTML", "children", "isElement", "slideClass", "setAttribute", "update", "force", "<PERSON><PERSON><PERSON><PERSON>iew", "slidesPerGroup", "centeredSlides", "loop", "isLoop", "previousFrom", "previousTo", "previousSlidesGrid", "previousOffset", "cssMode", "updateActiveIndex", "activeIndex", "offsetProp", "rtlTranslate", "isHorizontal", "slidesAfter", "slidesBefore", "Math", "floor", "max", "min", "length", "Object", "assign", "onRendered", "updateSlides", "updateProgress", "updateSlidesClasses", "for<PERSON>ach", "style", "abs", "cssOverflowAdjustment", "getSlides", "slidesToRender", "i", "push", "prependIndexes", "appendIndexes", "getSlideIndex", "slideIndex", "slidesEl", "querySelectorAll", "remove", "loopFrom", "loopTo", "append", "prepend", "sort", "a", "b", "appendSlide", "prependSlide", "newActiveIndex", "numberOfNewSlides", "Array", "isArray", "unshift", "newCache", "keys", "cachedIndex", "cachedEl", "cachedElIndex", "getAttribute", "parseInt", "slideTo", "removeSlide", "slidesIndexes", "splice", "removeAllSlides", "domSlidesAssigned", "passedParams", "filter", "el", "matches", "classNames", "containerModifierClass", "watchSlidesProgress", "originalParams", "initialSlide", "_immediateVirtual", "clearTimeout", "setTimeout", "wrapperEl", "virtualSize"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/modules/virtual/virtual.js"], "sourcesContent": ["import { getDocument } from 'ssr-window';\nimport { createElement, elementChildren, setCSSProperty } from '../../shared/utils.js';\nexport default function Virtual({\n  swiper,\n  extendParams,\n  on,\n  emit\n}) {\n  extendParams({\n    virtual: {\n      enabled: false,\n      slides: [],\n      cache: true,\n      renderSlide: null,\n      renderExternal: null,\n      renderExternalUpdate: true,\n      addSlidesBefore: 0,\n      addSlidesAfter: 0\n    }\n  });\n  let cssModeTimeout;\n  const document = getDocument();\n  swiper.virtual = {\n    cache: {},\n    from: undefined,\n    to: undefined,\n    slides: [],\n    offset: 0,\n    slidesGrid: []\n  };\n  const tempDOM = document.createElement('div');\n  function renderSlide(slide, index) {\n    const params = swiper.params.virtual;\n    if (params.cache && swiper.virtual.cache[index]) {\n      return swiper.virtual.cache[index];\n    }\n    // eslint-disable-next-line\n    let slideEl;\n    if (params.renderSlide) {\n      slideEl = params.renderSlide.call(swiper, slide, index);\n      if (typeof slideEl === 'string') {\n        tempDOM.innerHTML = slideEl;\n        slideEl = tempDOM.children[0];\n      }\n    } else if (swiper.isElement) {\n      slideEl = createElement('swiper-slide');\n    } else {\n      slideEl = createElement('div', swiper.params.slideClass);\n    }\n    slideEl.setAttribute('data-swiper-slide-index', index);\n    if (!params.renderSlide) {\n      slideEl.innerHTML = slide;\n    }\n    if (params.cache) swiper.virtual.cache[index] = slideEl;\n    return slideEl;\n  }\n  function update(force) {\n    const {\n      slidesPerView,\n      slidesPerGroup,\n      centeredSlides,\n      loop: isLoop\n    } = swiper.params;\n    const {\n      addSlidesBefore,\n      addSlidesAfter\n    } = swiper.params.virtual;\n    const {\n      from: previousFrom,\n      to: previousTo,\n      slides,\n      slidesGrid: previousSlidesGrid,\n      offset: previousOffset\n    } = swiper.virtual;\n    if (!swiper.params.cssMode) {\n      swiper.updateActiveIndex();\n    }\n    const activeIndex = swiper.activeIndex || 0;\n    let offsetProp;\n    if (swiper.rtlTranslate) offsetProp = 'right';else offsetProp = swiper.isHorizontal() ? 'left' : 'top';\n    let slidesAfter;\n    let slidesBefore;\n    if (centeredSlides) {\n      slidesAfter = Math.floor(slidesPerView / 2) + slidesPerGroup + addSlidesAfter;\n      slidesBefore = Math.floor(slidesPerView / 2) + slidesPerGroup + addSlidesBefore;\n    } else {\n      slidesAfter = slidesPerView + (slidesPerGroup - 1) + addSlidesAfter;\n      slidesBefore = (isLoop ? slidesPerView : slidesPerGroup) + addSlidesBefore;\n    }\n    let from = activeIndex - slidesBefore;\n    let to = activeIndex + slidesAfter;\n    if (!isLoop) {\n      from = Math.max(from, 0);\n      to = Math.min(to, slides.length - 1);\n    }\n    let offset = (swiper.slidesGrid[from] || 0) - (swiper.slidesGrid[0] || 0);\n    if (isLoop && activeIndex >= slidesBefore) {\n      from -= slidesBefore;\n      if (!centeredSlides) offset += swiper.slidesGrid[0];\n    } else if (isLoop && activeIndex < slidesBefore) {\n      from = -slidesBefore;\n      if (centeredSlides) offset += swiper.slidesGrid[0];\n    }\n    Object.assign(swiper.virtual, {\n      from,\n      to,\n      offset,\n      slidesGrid: swiper.slidesGrid,\n      slidesBefore,\n      slidesAfter\n    });\n    function onRendered() {\n      swiper.updateSlides();\n      swiper.updateProgress();\n      swiper.updateSlidesClasses();\n      emit('virtualUpdate');\n    }\n    if (previousFrom === from && previousTo === to && !force) {\n      if (swiper.slidesGrid !== previousSlidesGrid && offset !== previousOffset) {\n        swiper.slides.forEach(slideEl => {\n          slideEl.style[offsetProp] = `${offset - Math.abs(swiper.cssOverflowAdjustment())}px`;\n        });\n      }\n      swiper.updateProgress();\n      emit('virtualUpdate');\n      return;\n    }\n    if (swiper.params.virtual.renderExternal) {\n      swiper.params.virtual.renderExternal.call(swiper, {\n        offset,\n        from,\n        to,\n        slides: function getSlides() {\n          const slidesToRender = [];\n          for (let i = from; i <= to; i += 1) {\n            slidesToRender.push(slides[i]);\n          }\n          return slidesToRender;\n        }()\n      });\n      if (swiper.params.virtual.renderExternalUpdate) {\n        onRendered();\n      } else {\n        emit('virtualUpdate');\n      }\n      return;\n    }\n    const prependIndexes = [];\n    const appendIndexes = [];\n    const getSlideIndex = index => {\n      let slideIndex = index;\n      if (index < 0) {\n        slideIndex = slides.length + index;\n      } else if (slideIndex >= slides.length) {\n        // eslint-disable-next-line\n        slideIndex = slideIndex - slides.length;\n      }\n      return slideIndex;\n    };\n    if (force) {\n      swiper.slidesEl.querySelectorAll(`.${swiper.params.slideClass}, swiper-slide`).forEach(slideEl => {\n        slideEl.remove();\n      });\n    } else {\n      for (let i = previousFrom; i <= previousTo; i += 1) {\n        if (i < from || i > to) {\n          const slideIndex = getSlideIndex(i);\n          swiper.slidesEl.querySelectorAll(`.${swiper.params.slideClass}[data-swiper-slide-index=\"${slideIndex}\"], swiper-slide[data-swiper-slide-index=\"${slideIndex}\"]`).forEach(slideEl => {\n            slideEl.remove();\n          });\n        }\n      }\n    }\n    const loopFrom = isLoop ? -slides.length : 0;\n    const loopTo = isLoop ? slides.length * 2 : slides.length;\n    for (let i = loopFrom; i < loopTo; i += 1) {\n      if (i >= from && i <= to) {\n        const slideIndex = getSlideIndex(i);\n        if (typeof previousTo === 'undefined' || force) {\n          appendIndexes.push(slideIndex);\n        } else {\n          if (i > previousTo) appendIndexes.push(slideIndex);\n          if (i < previousFrom) prependIndexes.push(slideIndex);\n        }\n      }\n    }\n    appendIndexes.forEach(index => {\n      swiper.slidesEl.append(renderSlide(slides[index], index));\n    });\n    if (isLoop) {\n      for (let i = prependIndexes.length - 1; i >= 0; i -= 1) {\n        const index = prependIndexes[i];\n        swiper.slidesEl.prepend(renderSlide(slides[index], index));\n      }\n    } else {\n      prependIndexes.sort((a, b) => b - a);\n      prependIndexes.forEach(index => {\n        swiper.slidesEl.prepend(renderSlide(slides[index], index));\n      });\n    }\n    elementChildren(swiper.slidesEl, '.swiper-slide, swiper-slide').forEach(slideEl => {\n      slideEl.style[offsetProp] = `${offset - Math.abs(swiper.cssOverflowAdjustment())}px`;\n    });\n    onRendered();\n  }\n  function appendSlide(slides) {\n    if (typeof slides === 'object' && 'length' in slides) {\n      for (let i = 0; i < slides.length; i += 1) {\n        if (slides[i]) swiper.virtual.slides.push(slides[i]);\n      }\n    } else {\n      swiper.virtual.slides.push(slides);\n    }\n    update(true);\n  }\n  function prependSlide(slides) {\n    const activeIndex = swiper.activeIndex;\n    let newActiveIndex = activeIndex + 1;\n    let numberOfNewSlides = 1;\n    if (Array.isArray(slides)) {\n      for (let i = 0; i < slides.length; i += 1) {\n        if (slides[i]) swiper.virtual.slides.unshift(slides[i]);\n      }\n      newActiveIndex = activeIndex + slides.length;\n      numberOfNewSlides = slides.length;\n    } else {\n      swiper.virtual.slides.unshift(slides);\n    }\n    if (swiper.params.virtual.cache) {\n      const cache = swiper.virtual.cache;\n      const newCache = {};\n      Object.keys(cache).forEach(cachedIndex => {\n        const cachedEl = cache[cachedIndex];\n        const cachedElIndex = cachedEl.getAttribute('data-swiper-slide-index');\n        if (cachedElIndex) {\n          cachedEl.setAttribute('data-swiper-slide-index', parseInt(cachedElIndex, 10) + numberOfNewSlides);\n        }\n        newCache[parseInt(cachedIndex, 10) + numberOfNewSlides] = cachedEl;\n      });\n      swiper.virtual.cache = newCache;\n    }\n    update(true);\n    swiper.slideTo(newActiveIndex, 0);\n  }\n  function removeSlide(slidesIndexes) {\n    if (typeof slidesIndexes === 'undefined' || slidesIndexes === null) return;\n    let activeIndex = swiper.activeIndex;\n    if (Array.isArray(slidesIndexes)) {\n      for (let i = slidesIndexes.length - 1; i >= 0; i -= 1) {\n        swiper.virtual.slides.splice(slidesIndexes[i], 1);\n        if (swiper.params.virtual.cache) {\n          delete swiper.virtual.cache[slidesIndexes[i]];\n        }\n        if (slidesIndexes[i] < activeIndex) activeIndex -= 1;\n        activeIndex = Math.max(activeIndex, 0);\n      }\n    } else {\n      swiper.virtual.slides.splice(slidesIndexes, 1);\n      if (swiper.params.virtual.cache) {\n        delete swiper.virtual.cache[slidesIndexes];\n      }\n      if (slidesIndexes < activeIndex) activeIndex -= 1;\n      activeIndex = Math.max(activeIndex, 0);\n    }\n    update(true);\n    swiper.slideTo(activeIndex, 0);\n  }\n  function removeAllSlides() {\n    swiper.virtual.slides = [];\n    if (swiper.params.virtual.cache) {\n      swiper.virtual.cache = {};\n    }\n    update(true);\n    swiper.slideTo(0, 0);\n  }\n  on('beforeInit', () => {\n    if (!swiper.params.virtual.enabled) return;\n    let domSlidesAssigned;\n    if (typeof swiper.passedParams.virtual.slides === 'undefined') {\n      const slides = [...swiper.slidesEl.children].filter(el => el.matches(`.${swiper.params.slideClass}, swiper-slide`));\n      if (slides && slides.length) {\n        swiper.virtual.slides = [...slides];\n        domSlidesAssigned = true;\n        slides.forEach((slideEl, slideIndex) => {\n          slideEl.setAttribute('data-swiper-slide-index', slideIndex);\n          swiper.virtual.cache[slideIndex] = slideEl;\n          slideEl.remove();\n        });\n      }\n    }\n    if (!domSlidesAssigned) {\n      swiper.virtual.slides = swiper.params.virtual.slides;\n    }\n    swiper.classNames.push(`${swiper.params.containerModifierClass}virtual`);\n    swiper.params.watchSlidesProgress = true;\n    swiper.originalParams.watchSlidesProgress = true;\n    if (!swiper.params.initialSlide) {\n      update();\n    }\n  });\n  on('setTranslate', () => {\n    if (!swiper.params.virtual.enabled) return;\n    if (swiper.params.cssMode && !swiper._immediateVirtual) {\n      clearTimeout(cssModeTimeout);\n      cssModeTimeout = setTimeout(() => {\n        update();\n      }, 100);\n    } else {\n      update();\n    }\n  });\n  on('init update resize', () => {\n    if (!swiper.params.virtual.enabled) return;\n    if (swiper.params.cssMode) {\n      setCSSProperty(swiper.wrapperEl, '--swiper-virtual-size', `${swiper.virtualSize}px`);\n    }\n  });\n  Object.assign(swiper.virtual, {\n    appendSlide,\n    prependSlide,\n    removeSlide,\n    removeAllSlides,\n    update\n  });\n}"], "mappings": "AAAA,SAASA,WAAW,QAAQ,YAAY;AACxC,SAASC,aAAa,EAAEC,eAAe,EAAEC,cAAc,QAAQ,uBAAuB;AACtF,eAAe,SAASC,OAAOA,CAAAC,IAAA,EAK5B;EAAA,IAL6B;IAC9BC,MAAM;IACNC,YAAY;IACZC,EAAE;IACFC;EACF,CAAC,GAAAJ,IAAA;EACCE,YAAY,CAAC;IACXG,OAAO,EAAE;MACPC,OAAO,EAAE,KAAK;MACdC,MAAM,EAAE,EAAE;MACVC,KAAK,EAAE,IAAI;MACXC,WAAW,EAAE,IAAI;MACjBC,cAAc,EAAE,IAAI;MACpBC,oBAAoB,EAAE,IAAI;MAC1BC,eAAe,EAAE,CAAC;MAClBC,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;EACF,IAAIC,cAAc;EAClB,MAAMC,QAAQ,GAAGpB,WAAW,EAAE;EAC9BM,MAAM,CAACI,OAAO,GAAG;IACfG,KAAK,EAAE,CAAC,CAAC;IACTQ,IAAI,EAAEC,SAAS;IACfC,EAAE,EAAED,SAAS;IACbV,MAAM,EAAE,EAAE;IACVY,MAAM,EAAE,CAAC;IACTC,UAAU,EAAE;EACd,CAAC;EACD,MAAMC,OAAO,GAAGN,QAAQ,CAACnB,aAAa,CAAC,KAAK,CAAC;EAC7C,SAASa,WAAWA,CAACa,KAAK,EAAEC,KAAK,EAAE;IACjC,MAAMC,MAAM,GAAGvB,MAAM,CAACuB,MAAM,CAACnB,OAAO;IACpC,IAAImB,MAAM,CAAChB,KAAK,IAAIP,MAAM,CAACI,OAAO,CAACG,KAAK,CAACe,KAAK,CAAC,EAAE;MAC/C,OAAOtB,MAAM,CAACI,OAAO,CAACG,KAAK,CAACe,KAAK,CAAC;IACpC;IACA;IACA,IAAIE,OAAO;IACX,IAAID,MAAM,CAACf,WAAW,EAAE;MACtBgB,OAAO,GAAGD,MAAM,CAACf,WAAW,CAACiB,IAAI,CAACzB,MAAM,EAAEqB,KAAK,EAAEC,KAAK,CAAC;MACvD,IAAI,OAAOE,OAAO,KAAK,QAAQ,EAAE;QAC/BJ,OAAO,CAACM,SAAS,GAAGF,OAAO;QAC3BA,OAAO,GAAGJ,OAAO,CAACO,QAAQ,CAAC,CAAC,CAAC;MAC/B;IACF,CAAC,MAAM,IAAI3B,MAAM,CAAC4B,SAAS,EAAE;MAC3BJ,OAAO,GAAG7B,aAAa,CAAC,cAAc,CAAC;IACzC,CAAC,MAAM;MACL6B,OAAO,GAAG7B,aAAa,CAAC,KAAK,EAAEK,MAAM,CAACuB,MAAM,CAACM,UAAU,CAAC;IAC1D;IACAL,OAAO,CAACM,YAAY,CAAC,yBAAyB,EAAER,KAAK,CAAC;IACtD,IAAI,CAACC,MAAM,CAACf,WAAW,EAAE;MACvBgB,OAAO,CAACE,SAAS,GAAGL,KAAK;IAC3B;IACA,IAAIE,MAAM,CAAChB,KAAK,EAAEP,MAAM,CAACI,OAAO,CAACG,KAAK,CAACe,KAAK,CAAC,GAAGE,OAAO;IACvD,OAAOA,OAAO;EAChB;EACA,SAASO,MAAMA,CAACC,KAAK,EAAE;IACrB,MAAM;MACJC,aAAa;MACbC,cAAc;MACdC,cAAc;MACdC,IAAI,EAAEC;IACR,CAAC,GAAGrC,MAAM,CAACuB,MAAM;IACjB,MAAM;MACJZ,eAAe;MACfC;IACF,CAAC,GAAGZ,MAAM,CAACuB,MAAM,CAACnB,OAAO;IACzB,MAAM;MACJW,IAAI,EAAEuB,YAAY;MAClBrB,EAAE,EAAEsB,UAAU;MACdjC,MAAM;MACNa,UAAU,EAAEqB,kBAAkB;MAC9BtB,MAAM,EAAEuB;IACV,CAAC,GAAGzC,MAAM,CAACI,OAAO;IAClB,IAAI,CAACJ,MAAM,CAACuB,MAAM,CAACmB,OAAO,EAAE;MAC1B1C,MAAM,CAAC2C,iBAAiB,EAAE;IAC5B;IACA,MAAMC,WAAW,GAAG5C,MAAM,CAAC4C,WAAW,IAAI,CAAC;IAC3C,IAAIC,UAAU;IACd,IAAI7C,MAAM,CAAC8C,YAAY,EAAED,UAAU,GAAG,OAAO,CAAC,KAAKA,UAAU,GAAG7C,MAAM,CAAC+C,YAAY,EAAE,GAAG,MAAM,GAAG,KAAK;IACtG,IAAIC,WAAW;IACf,IAAIC,YAAY;IAChB,IAAId,cAAc,EAAE;MAClBa,WAAW,GAAGE,IAAI,CAACC,KAAK,CAAClB,aAAa,GAAG,CAAC,CAAC,GAAGC,cAAc,GAAGtB,cAAc;MAC7EqC,YAAY,GAAGC,IAAI,CAACC,KAAK,CAAClB,aAAa,GAAG,CAAC,CAAC,GAAGC,cAAc,GAAGvB,eAAe;IACjF,CAAC,MAAM;MACLqC,WAAW,GAAGf,aAAa,IAAIC,cAAc,GAAG,CAAC,CAAC,GAAGtB,cAAc;MACnEqC,YAAY,GAAG,CAACZ,MAAM,GAAGJ,aAAa,GAAGC,cAAc,IAAIvB,eAAe;IAC5E;IACA,IAAII,IAAI,GAAG6B,WAAW,GAAGK,YAAY;IACrC,IAAIhC,EAAE,GAAG2B,WAAW,GAAGI,WAAW;IAClC,IAAI,CAACX,MAAM,EAAE;MACXtB,IAAI,GAAGmC,IAAI,CAACE,GAAG,CAACrC,IAAI,EAAE,CAAC,CAAC;MACxBE,EAAE,GAAGiC,IAAI,CAACG,GAAG,CAACpC,EAAE,EAAEX,MAAM,CAACgD,MAAM,GAAG,CAAC,CAAC;IACtC;IACA,IAAIpC,MAAM,GAAG,CAAClB,MAAM,CAACmB,UAAU,CAACJ,IAAI,CAAC,IAAI,CAAC,KAAKf,MAAM,CAACmB,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IACzE,IAAIkB,MAAM,IAAIO,WAAW,IAAIK,YAAY,EAAE;MACzClC,IAAI,IAAIkC,YAAY;MACpB,IAAI,CAACd,cAAc,EAAEjB,MAAM,IAAIlB,MAAM,CAACmB,UAAU,CAAC,CAAC,CAAC;IACrD,CAAC,MAAM,IAAIkB,MAAM,IAAIO,WAAW,GAAGK,YAAY,EAAE;MAC/ClC,IAAI,GAAG,CAACkC,YAAY;MACpB,IAAId,cAAc,EAAEjB,MAAM,IAAIlB,MAAM,CAACmB,UAAU,CAAC,CAAC,CAAC;IACpD;IACAoC,MAAM,CAACC,MAAM,CAACxD,MAAM,CAACI,OAAO,EAAE;MAC5BW,IAAI;MACJE,EAAE;MACFC,MAAM;MACNC,UAAU,EAAEnB,MAAM,CAACmB,UAAU;MAC7B8B,YAAY;MACZD;IACF,CAAC,CAAC;IACF,SAASS,UAAUA,CAAA,EAAG;MACpBzD,MAAM,CAAC0D,YAAY,EAAE;MACrB1D,MAAM,CAAC2D,cAAc,EAAE;MACvB3D,MAAM,CAAC4D,mBAAmB,EAAE;MAC5BzD,IAAI,CAAC,eAAe,CAAC;IACvB;IACA,IAAImC,YAAY,KAAKvB,IAAI,IAAIwB,UAAU,KAAKtB,EAAE,IAAI,CAACe,KAAK,EAAE;MACxD,IAAIhC,MAAM,CAACmB,UAAU,KAAKqB,kBAAkB,IAAItB,MAAM,KAAKuB,cAAc,EAAE;QACzEzC,MAAM,CAACM,MAAM,CAACuD,OAAO,CAACrC,OAAO,IAAI;UAC/BA,OAAO,CAACsC,KAAK,CAACjB,UAAU,CAAC,GAAI,GAAE3B,MAAM,GAAGgC,IAAI,CAACa,GAAG,CAAC/D,MAAM,CAACgE,qBAAqB,EAAE,CAAE,IAAG;QACtF,CAAC,CAAC;MACJ;MACAhE,MAAM,CAAC2D,cAAc,EAAE;MACvBxD,IAAI,CAAC,eAAe,CAAC;MACrB;IACF;IACA,IAAIH,MAAM,CAACuB,MAAM,CAACnB,OAAO,CAACK,cAAc,EAAE;MACxCT,MAAM,CAACuB,MAAM,CAACnB,OAAO,CAACK,cAAc,CAACgB,IAAI,CAACzB,MAAM,EAAE;QAChDkB,MAAM;QACNH,IAAI;QACJE,EAAE;QACFX,MAAM,EAAE,SAAS2D,SAASA,CAAA,EAAG;UAC3B,MAAMC,cAAc,GAAG,EAAE;UACzB,KAAK,IAAIC,CAAC,GAAGpD,IAAI,EAAEoD,CAAC,IAAIlD,EAAE,EAAEkD,CAAC,IAAI,CAAC,EAAE;YAClCD,cAAc,CAACE,IAAI,CAAC9D,MAAM,CAAC6D,CAAC,CAAC,CAAC;UAChC;UACA,OAAOD,cAAc;QACvB,CAAC;MACH,CAAC,CAAC;MACF,IAAIlE,MAAM,CAACuB,MAAM,CAACnB,OAAO,CAACM,oBAAoB,EAAE;QAC9C+C,UAAU,EAAE;MACd,CAAC,MAAM;QACLtD,IAAI,CAAC,eAAe,CAAC;MACvB;MACA;IACF;IACA,MAAMkE,cAAc,GAAG,EAAE;IACzB,MAAMC,aAAa,GAAG,EAAE;IACxB,MAAMC,aAAa,GAAGjD,KAAK,IAAI;MAC7B,IAAIkD,UAAU,GAAGlD,KAAK;MACtB,IAAIA,KAAK,GAAG,CAAC,EAAE;QACbkD,UAAU,GAAGlE,MAAM,CAACgD,MAAM,GAAGhC,KAAK;MACpC,CAAC,MAAM,IAAIkD,UAAU,IAAIlE,MAAM,CAACgD,MAAM,EAAE;QACtC;QACAkB,UAAU,GAAGA,UAAU,GAAGlE,MAAM,CAACgD,MAAM;MACzC;MACA,OAAOkB,UAAU;IACnB,CAAC;IACD,IAAIxC,KAAK,EAAE;MACThC,MAAM,CAACyE,QAAQ,CAACC,gBAAgB,CAAE,IAAG1E,MAAM,CAACuB,MAAM,CAACM,UAAW,gBAAe,CAAC,CAACgC,OAAO,CAACrC,OAAO,IAAI;QAChGA,OAAO,CAACmD,MAAM,EAAE;MAClB,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,KAAK,IAAIR,CAAC,GAAG7B,YAAY,EAAE6B,CAAC,IAAI5B,UAAU,EAAE4B,CAAC,IAAI,CAAC,EAAE;QAClD,IAAIA,CAAC,GAAGpD,IAAI,IAAIoD,CAAC,GAAGlD,EAAE,EAAE;UACtB,MAAMuD,UAAU,GAAGD,aAAa,CAACJ,CAAC,CAAC;UACnCnE,MAAM,CAACyE,QAAQ,CAACC,gBAAgB,CAAE,IAAG1E,MAAM,CAACuB,MAAM,CAACM,UAAW,6BAA4B2C,UAAW,6CAA4CA,UAAW,IAAG,CAAC,CAACX,OAAO,CAACrC,OAAO,IAAI;YAClLA,OAAO,CAACmD,MAAM,EAAE;UAClB,CAAC,CAAC;QACJ;MACF;IACF;IACA,MAAMC,QAAQ,GAAGvC,MAAM,GAAG,CAAC/B,MAAM,CAACgD,MAAM,GAAG,CAAC;IAC5C,MAAMuB,MAAM,GAAGxC,MAAM,GAAG/B,MAAM,CAACgD,MAAM,GAAG,CAAC,GAAGhD,MAAM,CAACgD,MAAM;IACzD,KAAK,IAAIa,CAAC,GAAGS,QAAQ,EAAET,CAAC,GAAGU,MAAM,EAAEV,CAAC,IAAI,CAAC,EAAE;MACzC,IAAIA,CAAC,IAAIpD,IAAI,IAAIoD,CAAC,IAAIlD,EAAE,EAAE;QACxB,MAAMuD,UAAU,GAAGD,aAAa,CAACJ,CAAC,CAAC;QACnC,IAAI,OAAO5B,UAAU,KAAK,WAAW,IAAIP,KAAK,EAAE;UAC9CsC,aAAa,CAACF,IAAI,CAACI,UAAU,CAAC;QAChC,CAAC,MAAM;UACL,IAAIL,CAAC,GAAG5B,UAAU,EAAE+B,aAAa,CAACF,IAAI,CAACI,UAAU,CAAC;UAClD,IAAIL,CAAC,GAAG7B,YAAY,EAAE+B,cAAc,CAACD,IAAI,CAACI,UAAU,CAAC;QACvD;MACF;IACF;IACAF,aAAa,CAACT,OAAO,CAACvC,KAAK,IAAI;MAC7BtB,MAAM,CAACyE,QAAQ,CAACK,MAAM,CAACtE,WAAW,CAACF,MAAM,CAACgB,KAAK,CAAC,EAAEA,KAAK,CAAC,CAAC;IAC3D,CAAC,CAAC;IACF,IAAIe,MAAM,EAAE;MACV,KAAK,IAAI8B,CAAC,GAAGE,cAAc,CAACf,MAAM,GAAG,CAAC,EAAEa,CAAC,IAAI,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;QACtD,MAAM7C,KAAK,GAAG+C,cAAc,CAACF,CAAC,CAAC;QAC/BnE,MAAM,CAACyE,QAAQ,CAACM,OAAO,CAACvE,WAAW,CAACF,MAAM,CAACgB,KAAK,CAAC,EAAEA,KAAK,CAAC,CAAC;MAC5D;IACF,CAAC,MAAM;MACL+C,cAAc,CAACW,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,GAAGD,CAAC,CAAC;MACpCZ,cAAc,CAACR,OAAO,CAACvC,KAAK,IAAI;QAC9BtB,MAAM,CAACyE,QAAQ,CAACM,OAAO,CAACvE,WAAW,CAACF,MAAM,CAACgB,KAAK,CAAC,EAAEA,KAAK,CAAC,CAAC;MAC5D,CAAC,CAAC;IACJ;IACA1B,eAAe,CAACI,MAAM,CAACyE,QAAQ,EAAE,6BAA6B,CAAC,CAACZ,OAAO,CAACrC,OAAO,IAAI;MACjFA,OAAO,CAACsC,KAAK,CAACjB,UAAU,CAAC,GAAI,GAAE3B,MAAM,GAAGgC,IAAI,CAACa,GAAG,CAAC/D,MAAM,CAACgE,qBAAqB,EAAE,CAAE,IAAG;IACtF,CAAC,CAAC;IACFP,UAAU,EAAE;EACd;EACA,SAAS0B,WAAWA,CAAC7E,MAAM,EAAE;IAC3B,IAAI,OAAOA,MAAM,KAAK,QAAQ,IAAI,QAAQ,IAAIA,MAAM,EAAE;MACpD,KAAK,IAAI6D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG7D,MAAM,CAACgD,MAAM,EAAEa,CAAC,IAAI,CAAC,EAAE;QACzC,IAAI7D,MAAM,CAAC6D,CAAC,CAAC,EAAEnE,MAAM,CAACI,OAAO,CAACE,MAAM,CAAC8D,IAAI,CAAC9D,MAAM,CAAC6D,CAAC,CAAC,CAAC;MACtD;IACF,CAAC,MAAM;MACLnE,MAAM,CAACI,OAAO,CAACE,MAAM,CAAC8D,IAAI,CAAC9D,MAAM,CAAC;IACpC;IACAyB,MAAM,CAAC,IAAI,CAAC;EACd;EACA,SAASqD,YAAYA,CAAC9E,MAAM,EAAE;IAC5B,MAAMsC,WAAW,GAAG5C,MAAM,CAAC4C,WAAW;IACtC,IAAIyC,cAAc,GAAGzC,WAAW,GAAG,CAAC;IACpC,IAAI0C,iBAAiB,GAAG,CAAC;IACzB,IAAIC,KAAK,CAACC,OAAO,CAAClF,MAAM,CAAC,EAAE;MACzB,KAAK,IAAI6D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG7D,MAAM,CAACgD,MAAM,EAAEa,CAAC,IAAI,CAAC,EAAE;QACzC,IAAI7D,MAAM,CAAC6D,CAAC,CAAC,EAAEnE,MAAM,CAACI,OAAO,CAACE,MAAM,CAACmF,OAAO,CAACnF,MAAM,CAAC6D,CAAC,CAAC,CAAC;MACzD;MACAkB,cAAc,GAAGzC,WAAW,GAAGtC,MAAM,CAACgD,MAAM;MAC5CgC,iBAAiB,GAAGhF,MAAM,CAACgD,MAAM;IACnC,CAAC,MAAM;MACLtD,MAAM,CAACI,OAAO,CAACE,MAAM,CAACmF,OAAO,CAACnF,MAAM,CAAC;IACvC;IACA,IAAIN,MAAM,CAACuB,MAAM,CAACnB,OAAO,CAACG,KAAK,EAAE;MAC/B,MAAMA,KAAK,GAAGP,MAAM,CAACI,OAAO,CAACG,KAAK;MAClC,MAAMmF,QAAQ,GAAG,CAAC,CAAC;MACnBnC,MAAM,CAACoC,IAAI,CAACpF,KAAK,CAAC,CAACsD,OAAO,CAAC+B,WAAW,IAAI;QACxC,MAAMC,QAAQ,GAAGtF,KAAK,CAACqF,WAAW,CAAC;QACnC,MAAME,aAAa,GAAGD,QAAQ,CAACE,YAAY,CAAC,yBAAyB,CAAC;QACtE,IAAID,aAAa,EAAE;UACjBD,QAAQ,CAAC/D,YAAY,CAAC,yBAAyB,EAAEkE,QAAQ,CAACF,aAAa,EAAE,EAAE,CAAC,GAAGR,iBAAiB,CAAC;QACnG;QACAI,QAAQ,CAACM,QAAQ,CAACJ,WAAW,EAAE,EAAE,CAAC,GAAGN,iBAAiB,CAAC,GAAGO,QAAQ;MACpE,CAAC,CAAC;MACF7F,MAAM,CAACI,OAAO,CAACG,KAAK,GAAGmF,QAAQ;IACjC;IACA3D,MAAM,CAAC,IAAI,CAAC;IACZ/B,MAAM,CAACiG,OAAO,CAACZ,cAAc,EAAE,CAAC,CAAC;EACnC;EACA,SAASa,WAAWA,CAACC,aAAa,EAAE;IAClC,IAAI,OAAOA,aAAa,KAAK,WAAW,IAAIA,aAAa,KAAK,IAAI,EAAE;IACpE,IAAIvD,WAAW,GAAG5C,MAAM,CAAC4C,WAAW;IACpC,IAAI2C,KAAK,CAACC,OAAO,CAACW,aAAa,CAAC,EAAE;MAChC,KAAK,IAAIhC,CAAC,GAAGgC,aAAa,CAAC7C,MAAM,GAAG,CAAC,EAAEa,CAAC,IAAI,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;QACrDnE,MAAM,CAACI,OAAO,CAACE,MAAM,CAAC8F,MAAM,CAACD,aAAa,CAAChC,CAAC,CAAC,EAAE,CAAC,CAAC;QACjD,IAAInE,MAAM,CAACuB,MAAM,CAACnB,OAAO,CAACG,KAAK,EAAE;UAC/B,OAAOP,MAAM,CAACI,OAAO,CAACG,KAAK,CAAC4F,aAAa,CAAChC,CAAC,CAAC,CAAC;QAC/C;QACA,IAAIgC,aAAa,CAAChC,CAAC,CAAC,GAAGvB,WAAW,EAAEA,WAAW,IAAI,CAAC;QACpDA,WAAW,GAAGM,IAAI,CAACE,GAAG,CAACR,WAAW,EAAE,CAAC,CAAC;MACxC;IACF,CAAC,MAAM;MACL5C,MAAM,CAACI,OAAO,CAACE,MAAM,CAAC8F,MAAM,CAACD,aAAa,EAAE,CAAC,CAAC;MAC9C,IAAInG,MAAM,CAACuB,MAAM,CAACnB,OAAO,CAACG,KAAK,EAAE;QAC/B,OAAOP,MAAM,CAACI,OAAO,CAACG,KAAK,CAAC4F,aAAa,CAAC;MAC5C;MACA,IAAIA,aAAa,GAAGvD,WAAW,EAAEA,WAAW,IAAI,CAAC;MACjDA,WAAW,GAAGM,IAAI,CAACE,GAAG,CAACR,WAAW,EAAE,CAAC,CAAC;IACxC;IACAb,MAAM,CAAC,IAAI,CAAC;IACZ/B,MAAM,CAACiG,OAAO,CAACrD,WAAW,EAAE,CAAC,CAAC;EAChC;EACA,SAASyD,eAAeA,CAAA,EAAG;IACzBrG,MAAM,CAACI,OAAO,CAACE,MAAM,GAAG,EAAE;IAC1B,IAAIN,MAAM,CAACuB,MAAM,CAACnB,OAAO,CAACG,KAAK,EAAE;MAC/BP,MAAM,CAACI,OAAO,CAACG,KAAK,GAAG,CAAC,CAAC;IAC3B;IACAwB,MAAM,CAAC,IAAI,CAAC;IACZ/B,MAAM,CAACiG,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;EACtB;EACA/F,EAAE,CAAC,YAAY,EAAE,MAAM;IACrB,IAAI,CAACF,MAAM,CAACuB,MAAM,CAACnB,OAAO,CAACC,OAAO,EAAE;IACpC,IAAIiG,iBAAiB;IACrB,IAAI,OAAOtG,MAAM,CAACuG,YAAY,CAACnG,OAAO,CAACE,MAAM,KAAK,WAAW,EAAE;MAC7D,MAAMA,MAAM,GAAG,CAAC,GAAGN,MAAM,CAACyE,QAAQ,CAAC9C,QAAQ,CAAC,CAAC6E,MAAM,CAACC,EAAE,IAAIA,EAAE,CAACC,OAAO,CAAE,IAAG1G,MAAM,CAACuB,MAAM,CAACM,UAAW,gBAAe,CAAC,CAAC;MACnH,IAAIvB,MAAM,IAAIA,MAAM,CAACgD,MAAM,EAAE;QAC3BtD,MAAM,CAACI,OAAO,CAACE,MAAM,GAAG,CAAC,GAAGA,MAAM,CAAC;QACnCgG,iBAAiB,GAAG,IAAI;QACxBhG,MAAM,CAACuD,OAAO,CAAC,CAACrC,OAAO,EAAEgD,UAAU,KAAK;UACtChD,OAAO,CAACM,YAAY,CAAC,yBAAyB,EAAE0C,UAAU,CAAC;UAC3DxE,MAAM,CAACI,OAAO,CAACG,KAAK,CAACiE,UAAU,CAAC,GAAGhD,OAAO;UAC1CA,OAAO,CAACmD,MAAM,EAAE;QAClB,CAAC,CAAC;MACJ;IACF;IACA,IAAI,CAAC2B,iBAAiB,EAAE;MACtBtG,MAAM,CAACI,OAAO,CAACE,MAAM,GAAGN,MAAM,CAACuB,MAAM,CAACnB,OAAO,CAACE,MAAM;IACtD;IACAN,MAAM,CAAC2G,UAAU,CAACvC,IAAI,CAAE,GAAEpE,MAAM,CAACuB,MAAM,CAACqF,sBAAuB,SAAQ,CAAC;IACxE5G,MAAM,CAACuB,MAAM,CAACsF,mBAAmB,GAAG,IAAI;IACxC7G,MAAM,CAAC8G,cAAc,CAACD,mBAAmB,GAAG,IAAI;IAChD,IAAI,CAAC7G,MAAM,CAACuB,MAAM,CAACwF,YAAY,EAAE;MAC/BhF,MAAM,EAAE;IACV;EACF,CAAC,CAAC;EACF7B,EAAE,CAAC,cAAc,EAAE,MAAM;IACvB,IAAI,CAACF,MAAM,CAACuB,MAAM,CAACnB,OAAO,CAACC,OAAO,EAAE;IACpC,IAAIL,MAAM,CAACuB,MAAM,CAACmB,OAAO,IAAI,CAAC1C,MAAM,CAACgH,iBAAiB,EAAE;MACtDC,YAAY,CAACpG,cAAc,CAAC;MAC5BA,cAAc,GAAGqG,UAAU,CAAC,MAAM;QAChCnF,MAAM,EAAE;MACV,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,MAAM;MACLA,MAAM,EAAE;IACV;EACF,CAAC,CAAC;EACF7B,EAAE,CAAC,oBAAoB,EAAE,MAAM;IAC7B,IAAI,CAACF,MAAM,CAACuB,MAAM,CAACnB,OAAO,CAACC,OAAO,EAAE;IACpC,IAAIL,MAAM,CAACuB,MAAM,CAACmB,OAAO,EAAE;MACzB7C,cAAc,CAACG,MAAM,CAACmH,SAAS,EAAE,uBAAuB,EAAG,GAAEnH,MAAM,CAACoH,WAAY,IAAG,CAAC;IACtF;EACF,CAAC,CAAC;EACF7D,MAAM,CAACC,MAAM,CAACxD,MAAM,CAACI,OAAO,EAAE;IAC5B+E,WAAW;IACXC,YAAY;IACZc,WAAW;IACXG,eAAe;IACftE;EACF,CAAC,CAAC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}