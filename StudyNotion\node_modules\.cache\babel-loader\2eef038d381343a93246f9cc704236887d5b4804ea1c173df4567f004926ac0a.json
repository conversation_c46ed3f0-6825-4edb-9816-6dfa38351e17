{"ast": null, "code": "'use strict';\n\nlet FORCE_COLOR,\n  NODE_DISABLE_COLORS,\n  NO_COLOR,\n  TERM,\n  isTTY = true;\nif (typeof process !== 'undefined') {\n  ({\n    FORCE_COLOR,\n    NODE_DISABLE_COLORS,\n    NO_COLOR,\n    TERM\n  } = process.env || {});\n  isTTY = process.stdout && process.stdout.isTTY;\n}\nconst $ = {\n  enabled: !NODE_DISABLE_COLORS && NO_COLOR == null && TERM !== 'dumb' && (FORCE_COLOR != null && FORCE_COLOR !== '0' || isTTY),\n  // modifiers\n  reset: init(0, 0),\n  bold: init(1, 22),\n  dim: init(2, 22),\n  italic: init(3, 23),\n  underline: init(4, 24),\n  inverse: init(7, 27),\n  hidden: init(8, 28),\n  strikethrough: init(9, 29),\n  // colors\n  black: init(30, 39),\n  red: init(31, 39),\n  green: init(32, 39),\n  yellow: init(33, 39),\n  blue: init(34, 39),\n  magenta: init(35, 39),\n  cyan: init(36, 39),\n  white: init(37, 39),\n  gray: init(90, 39),\n  grey: init(90, 39),\n  // background colors\n  bgBlack: init(40, 49),\n  bgRed: init(41, 49),\n  bgGreen: init(42, 49),\n  bgYellow: init(43, 49),\n  bgBlue: init(44, 49),\n  bgMagenta: init(45, 49),\n  bgCyan: init(46, 49),\n  bgWhite: init(47, 49)\n};\nfunction run(arr, str) {\n  let i = 0,\n    tmp,\n    beg = '',\n    end = '';\n  for (; i < arr.length; i++) {\n    tmp = arr[i];\n    beg += tmp.open;\n    end += tmp.close;\n    if (!!~str.indexOf(tmp.close)) {\n      str = str.replace(tmp.rgx, tmp.close + tmp.open);\n    }\n  }\n  return beg + str + end;\n}\nfunction chain(has, keys) {\n  let ctx = {\n    has,\n    keys\n  };\n  ctx.reset = $.reset.bind(ctx);\n  ctx.bold = $.bold.bind(ctx);\n  ctx.dim = $.dim.bind(ctx);\n  ctx.italic = $.italic.bind(ctx);\n  ctx.underline = $.underline.bind(ctx);\n  ctx.inverse = $.inverse.bind(ctx);\n  ctx.hidden = $.hidden.bind(ctx);\n  ctx.strikethrough = $.strikethrough.bind(ctx);\n  ctx.black = $.black.bind(ctx);\n  ctx.red = $.red.bind(ctx);\n  ctx.green = $.green.bind(ctx);\n  ctx.yellow = $.yellow.bind(ctx);\n  ctx.blue = $.blue.bind(ctx);\n  ctx.magenta = $.magenta.bind(ctx);\n  ctx.cyan = $.cyan.bind(ctx);\n  ctx.white = $.white.bind(ctx);\n  ctx.gray = $.gray.bind(ctx);\n  ctx.grey = $.grey.bind(ctx);\n  ctx.bgBlack = $.bgBlack.bind(ctx);\n  ctx.bgRed = $.bgRed.bind(ctx);\n  ctx.bgGreen = $.bgGreen.bind(ctx);\n  ctx.bgYellow = $.bgYellow.bind(ctx);\n  ctx.bgBlue = $.bgBlue.bind(ctx);\n  ctx.bgMagenta = $.bgMagenta.bind(ctx);\n  ctx.bgCyan = $.bgCyan.bind(ctx);\n  ctx.bgWhite = $.bgWhite.bind(ctx);\n  return ctx;\n}\nfunction init(open, close) {\n  let blk = {\n    open: `\\x1b[${open}m`,\n    close: `\\x1b[${close}m`,\n    rgx: new RegExp(`\\\\x1b\\\\[${close}m`, 'g')\n  };\n  return function (txt) {\n    if (this !== void 0 && this.has !== void 0) {\n      !!~this.has.indexOf(open) || (this.has.push(open), this.keys.push(blk));\n      return txt === void 0 ? this : $.enabled ? run(this.keys, txt + '') : txt + '';\n    }\n    return txt === void 0 ? chain([open], [blk]) : $.enabled ? run([blk], txt + '') : txt + '';\n  };\n}\nexport default $;", "map": {"version": 3, "names": ["FORCE_COLOR", "NODE_DISABLE_COLORS", "NO_COLOR", "TERM", "isTTY", "process", "env", "stdout", "$", "enabled", "reset", "init", "bold", "dim", "italic", "underline", "inverse", "hidden", "strikethrough", "black", "red", "green", "yellow", "blue", "magenta", "cyan", "white", "gray", "grey", "bgBlack", "bgRed", "bgGreen", "bgYellow", "bgBlue", "bgMagenta", "bg<PERSON>yan", "bgWhite", "run", "arr", "str", "i", "tmp", "beg", "end", "length", "open", "close", "indexOf", "replace", "rgx", "chain", "has", "keys", "ctx", "bind", "blk", "RegExp", "txt", "push"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/uvu/node_modules/kleur/index.mjs"], "sourcesContent": ["'use strict';\n\nlet FORCE_COLOR, NODE_DISABLE_COLORS, NO_COLOR, TERM, isTTY=true;\nif (typeof process !== 'undefined') {\n\t({ FORCE_COLOR, NODE_DISABLE_COLORS, NO_COLOR, TERM } = process.env || {});\n\tisTTY = process.stdout && process.stdout.isTTY;\n}\n\nconst $ = {\n\tenabled: !NODE_DISABLE_COLORS && NO_COLOR == null && TERM !== 'dumb' && (\n\t\tFORCE_COLOR != null && FORCE_COLOR !== '0' || isTTY\n\t),\n\n\t// modifiers\n\treset: init(0, 0),\n\tbold: init(1, 22),\n\tdim: init(2, 22),\n\titalic: init(3, 23),\n\tunderline: init(4, 24),\n\tinverse: init(7, 27),\n\thidden: init(8, 28),\n\tstrikethrough: init(9, 29),\n\n\t// colors\n\tblack: init(30, 39),\n\tred: init(31, 39),\n\tgreen: init(32, 39),\n\tyellow: init(33, 39),\n\tblue: init(34, 39),\n\tmagenta: init(35, 39),\n\tcyan: init(36, 39),\n\twhite: init(37, 39),\n\tgray: init(90, 39),\n\tgrey: init(90, 39),\n\n\t// background colors\n\tbgBlack: init(40, 49),\n\tbgRed: init(41, 49),\n\tbgGreen: init(42, 49),\n\tbgYellow: init(43, 49),\n\tbgBlue: init(44, 49),\n\tbgMagenta: init(45, 49),\n\tbgCyan: init(46, 49),\n\tbgWhite: init(47, 49)\n};\n\nfunction run(arr, str) {\n\tlet i=0, tmp, beg='', end='';\n\tfor (; i < arr.length; i++) {\n\t\ttmp = arr[i];\n\t\tbeg += tmp.open;\n\t\tend += tmp.close;\n\t\tif (!!~str.indexOf(tmp.close)) {\n\t\t\tstr = str.replace(tmp.rgx, tmp.close + tmp.open);\n\t\t}\n\t}\n\treturn beg + str + end;\n}\n\nfunction chain(has, keys) {\n\tlet ctx = { has, keys };\n\n\tctx.reset = $.reset.bind(ctx);\n\tctx.bold = $.bold.bind(ctx);\n\tctx.dim = $.dim.bind(ctx);\n\tctx.italic = $.italic.bind(ctx);\n\tctx.underline = $.underline.bind(ctx);\n\tctx.inverse = $.inverse.bind(ctx);\n\tctx.hidden = $.hidden.bind(ctx);\n\tctx.strikethrough = $.strikethrough.bind(ctx);\n\n\tctx.black = $.black.bind(ctx);\n\tctx.red = $.red.bind(ctx);\n\tctx.green = $.green.bind(ctx);\n\tctx.yellow = $.yellow.bind(ctx);\n\tctx.blue = $.blue.bind(ctx);\n\tctx.magenta = $.magenta.bind(ctx);\n\tctx.cyan = $.cyan.bind(ctx);\n\tctx.white = $.white.bind(ctx);\n\tctx.gray = $.gray.bind(ctx);\n\tctx.grey = $.grey.bind(ctx);\n\n\tctx.bgBlack = $.bgBlack.bind(ctx);\n\tctx.bgRed = $.bgRed.bind(ctx);\n\tctx.bgGreen = $.bgGreen.bind(ctx);\n\tctx.bgYellow = $.bgYellow.bind(ctx);\n\tctx.bgBlue = $.bgBlue.bind(ctx);\n\tctx.bgMagenta = $.bgMagenta.bind(ctx);\n\tctx.bgCyan = $.bgCyan.bind(ctx);\n\tctx.bgWhite = $.bgWhite.bind(ctx);\n\n\treturn ctx;\n}\n\nfunction init(open, close) {\n\tlet blk = {\n\t\topen: `\\x1b[${open}m`,\n\t\tclose: `\\x1b[${close}m`,\n\t\trgx: new RegExp(`\\\\x1b\\\\[${close}m`, 'g')\n\t};\n\treturn function (txt) {\n\t\tif (this !== void 0 && this.has !== void 0) {\n\t\t\t!!~this.has.indexOf(open) || (this.has.push(open),this.keys.push(blk));\n\t\t\treturn txt === void 0 ? this : $.enabled ? run(this.keys, txt+'') : txt+'';\n\t\t}\n\t\treturn txt === void 0 ? chain([open], [blk]) : $.enabled ? run([blk], txt+'') : txt+'';\n\t};\n}\n\nexport default $;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,WAAW;EAAEC,mBAAmB;EAAEC,QAAQ;EAAEC,IAAI;EAAEC,KAAK,GAAC,IAAI;AAChE,IAAI,OAAOC,OAAO,KAAK,WAAW,EAAE;EACnC,CAAC;IAAEL,WAAW;IAAEC,mBAAmB;IAAEC,QAAQ;IAAEC;EAAK,CAAC,GAAGE,OAAO,CAACC,GAAG,IAAI,CAAC,CAAC;EACzEF,KAAK,GAAGC,OAAO,CAACE,MAAM,IAAIF,OAAO,CAACE,MAAM,CAACH,KAAK;AAC/C;AAEA,MAAMI,CAAC,GAAG;EACTC,OAAO,EAAE,CAACR,mBAAmB,IAAIC,QAAQ,IAAI,IAAI,IAAIC,IAAI,KAAK,MAAM,KACnEH,WAAW,IAAI,IAAI,IAAIA,WAAW,KAAK,GAAG,IAAII,KAAK,CACnD;EAED;EACAM,KAAK,EAAEC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;EACjBC,IAAI,EAAED,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC;EACjBE,GAAG,EAAEF,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC;EAChBG,MAAM,EAAEH,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC;EACnBI,SAAS,EAAEJ,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC;EACtBK,OAAO,EAAEL,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC;EACpBM,MAAM,EAAEN,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC;EACnBO,aAAa,EAAEP,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC;EAE1B;EACAQ,KAAK,EAAER,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC;EACnBS,GAAG,EAAET,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC;EACjBU,KAAK,EAAEV,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC;EACnBW,MAAM,EAAEX,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC;EACpBY,IAAI,EAAEZ,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC;EAClBa,OAAO,EAAEb,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC;EACrBc,IAAI,EAAEd,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC;EAClBe,KAAK,EAAEf,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC;EACnBgB,IAAI,EAAEhB,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC;EAClBiB,IAAI,EAAEjB,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC;EAElB;EACAkB,OAAO,EAAElB,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC;EACrBmB,KAAK,EAAEnB,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC;EACnBoB,OAAO,EAAEpB,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC;EACrBqB,QAAQ,EAAErB,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC;EACtBsB,MAAM,EAAEtB,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC;EACpBuB,SAAS,EAAEvB,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC;EACvBwB,MAAM,EAAExB,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC;EACpByB,OAAO,EAAEzB,IAAI,CAAC,EAAE,EAAE,EAAE;AACrB,CAAC;AAED,SAAS0B,GAAGA,CAACC,GAAG,EAAEC,GAAG,EAAE;EACtB,IAAIC,CAAC,GAAC,CAAC;IAAEC,GAAG;IAAEC,GAAG,GAAC,EAAE;IAAEC,GAAG,GAAC,EAAE;EAC5B,OAAOH,CAAC,GAAGF,GAAG,CAACM,MAAM,EAAEJ,CAAC,EAAE,EAAE;IAC3BC,GAAG,GAAGH,GAAG,CAACE,CAAC,CAAC;IACZE,GAAG,IAAID,GAAG,CAACI,IAAI;IACfF,GAAG,IAAIF,GAAG,CAACK,KAAK;IAChB,IAAI,CAAC,CAAC,CAACP,GAAG,CAACQ,OAAO,CAACN,GAAG,CAACK,KAAK,CAAC,EAAE;MAC9BP,GAAG,GAAGA,GAAG,CAACS,OAAO,CAACP,GAAG,CAACQ,GAAG,EAAER,GAAG,CAACK,KAAK,GAAGL,GAAG,CAACI,IAAI,CAAC;IACjD;EACD;EACA,OAAOH,GAAG,GAAGH,GAAG,GAAGI,GAAG;AACvB;AAEA,SAASO,KAAKA,CAACC,GAAG,EAAEC,IAAI,EAAE;EACzB,IAAIC,GAAG,GAAG;IAAEF,GAAG;IAAEC;EAAK,CAAC;EAEvBC,GAAG,CAAC3C,KAAK,GAAGF,CAAC,CAACE,KAAK,CAAC4C,IAAI,CAACD,GAAG,CAAC;EAC7BA,GAAG,CAACzC,IAAI,GAAGJ,CAAC,CAACI,IAAI,CAAC0C,IAAI,CAACD,GAAG,CAAC;EAC3BA,GAAG,CAACxC,GAAG,GAAGL,CAAC,CAACK,GAAG,CAACyC,IAAI,CAACD,GAAG,CAAC;EACzBA,GAAG,CAACvC,MAAM,GAAGN,CAAC,CAACM,MAAM,CAACwC,IAAI,CAACD,GAAG,CAAC;EAC/BA,GAAG,CAACtC,SAAS,GAAGP,CAAC,CAACO,SAAS,CAACuC,IAAI,CAACD,GAAG,CAAC;EACrCA,GAAG,CAACrC,OAAO,GAAGR,CAAC,CAACQ,OAAO,CAACsC,IAAI,CAACD,GAAG,CAAC;EACjCA,GAAG,CAACpC,MAAM,GAAGT,CAAC,CAACS,MAAM,CAACqC,IAAI,CAACD,GAAG,CAAC;EAC/BA,GAAG,CAACnC,aAAa,GAAGV,CAAC,CAACU,aAAa,CAACoC,IAAI,CAACD,GAAG,CAAC;EAE7CA,GAAG,CAAClC,KAAK,GAAGX,CAAC,CAACW,KAAK,CAACmC,IAAI,CAACD,GAAG,CAAC;EAC7BA,GAAG,CAACjC,GAAG,GAAGZ,CAAC,CAACY,GAAG,CAACkC,IAAI,CAACD,GAAG,CAAC;EACzBA,GAAG,CAAChC,KAAK,GAAGb,CAAC,CAACa,KAAK,CAACiC,IAAI,CAACD,GAAG,CAAC;EAC7BA,GAAG,CAAC/B,MAAM,GAAGd,CAAC,CAACc,MAAM,CAACgC,IAAI,CAACD,GAAG,CAAC;EAC/BA,GAAG,CAAC9B,IAAI,GAAGf,CAAC,CAACe,IAAI,CAAC+B,IAAI,CAACD,GAAG,CAAC;EAC3BA,GAAG,CAAC7B,OAAO,GAAGhB,CAAC,CAACgB,OAAO,CAAC8B,IAAI,CAACD,GAAG,CAAC;EACjCA,GAAG,CAAC5B,IAAI,GAAGjB,CAAC,CAACiB,IAAI,CAAC6B,IAAI,CAACD,GAAG,CAAC;EAC3BA,GAAG,CAAC3B,KAAK,GAAGlB,CAAC,CAACkB,KAAK,CAAC4B,IAAI,CAACD,GAAG,CAAC;EAC7BA,GAAG,CAAC1B,IAAI,GAAGnB,CAAC,CAACmB,IAAI,CAAC2B,IAAI,CAACD,GAAG,CAAC;EAC3BA,GAAG,CAACzB,IAAI,GAAGpB,CAAC,CAACoB,IAAI,CAAC0B,IAAI,CAACD,GAAG,CAAC;EAE3BA,GAAG,CAACxB,OAAO,GAAGrB,CAAC,CAACqB,OAAO,CAACyB,IAAI,CAACD,GAAG,CAAC;EACjCA,GAAG,CAACvB,KAAK,GAAGtB,CAAC,CAACsB,KAAK,CAACwB,IAAI,CAACD,GAAG,CAAC;EAC7BA,GAAG,CAACtB,OAAO,GAAGvB,CAAC,CAACuB,OAAO,CAACuB,IAAI,CAACD,GAAG,CAAC;EACjCA,GAAG,CAACrB,QAAQ,GAAGxB,CAAC,CAACwB,QAAQ,CAACsB,IAAI,CAACD,GAAG,CAAC;EACnCA,GAAG,CAACpB,MAAM,GAAGzB,CAAC,CAACyB,MAAM,CAACqB,IAAI,CAACD,GAAG,CAAC;EAC/BA,GAAG,CAACnB,SAAS,GAAG1B,CAAC,CAAC0B,SAAS,CAACoB,IAAI,CAACD,GAAG,CAAC;EACrCA,GAAG,CAAClB,MAAM,GAAG3B,CAAC,CAAC2B,MAAM,CAACmB,IAAI,CAACD,GAAG,CAAC;EAC/BA,GAAG,CAACjB,OAAO,GAAG5B,CAAC,CAAC4B,OAAO,CAACkB,IAAI,CAACD,GAAG,CAAC;EAEjC,OAAOA,GAAG;AACX;AAEA,SAAS1C,IAAIA,CAACkC,IAAI,EAAEC,KAAK,EAAE;EAC1B,IAAIS,GAAG,GAAG;IACTV,IAAI,EAAG,QAAOA,IAAK,GAAE;IACrBC,KAAK,EAAG,QAAOA,KAAM,GAAE;IACvBG,GAAG,EAAE,IAAIO,MAAM,CAAE,WAAUV,KAAM,GAAE,EAAE,GAAG;EACzC,CAAC;EACD,OAAO,UAAUW,GAAG,EAAE;IACrB,IAAI,IAAI,KAAK,KAAK,CAAC,IAAI,IAAI,CAACN,GAAG,KAAK,KAAK,CAAC,EAAE;MAC3C,CAAC,CAAC,CAAC,IAAI,CAACA,GAAG,CAACJ,OAAO,CAACF,IAAI,CAAC,KAAK,IAAI,CAACM,GAAG,CAACO,IAAI,CAACb,IAAI,CAAC,EAAC,IAAI,CAACO,IAAI,CAACM,IAAI,CAACH,GAAG,CAAC,CAAC;MACtE,OAAOE,GAAG,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGjD,CAAC,CAACC,OAAO,GAAG4B,GAAG,CAAC,IAAI,CAACe,IAAI,EAAEK,GAAG,GAAC,EAAE,CAAC,GAAGA,GAAG,GAAC,EAAE;IAC3E;IACA,OAAOA,GAAG,KAAK,KAAK,CAAC,GAAGP,KAAK,CAAC,CAACL,IAAI,CAAC,EAAE,CAACU,GAAG,CAAC,CAAC,GAAG/C,CAAC,CAACC,OAAO,GAAG4B,GAAG,CAAC,CAACkB,GAAG,CAAC,EAAEE,GAAG,GAAC,EAAE,CAAC,GAAGA,GAAG,GAAC,EAAE;EACvF,CAAC;AACF;AAEA,eAAejD,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}