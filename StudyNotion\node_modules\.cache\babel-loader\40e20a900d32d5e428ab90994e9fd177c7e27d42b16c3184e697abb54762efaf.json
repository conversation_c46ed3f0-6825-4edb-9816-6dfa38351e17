{"ast": null, "code": "/**\n * @typedef {import('micromark-util-types').Construct} Construct\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Tokenizer} Tokenizer\n */\n\nimport { factoryDestination } from 'micromark-factory-destination';\nimport { factoryLabel } from 'micromark-factory-label';\nimport { factorySpace } from 'micromark-factory-space';\nimport { factoryTitle } from 'micromark-factory-title';\nimport { factoryWhitespace } from 'micromark-factory-whitespace';\nimport { markdownLineEnding, markdownLineEndingOrSpace, markdownSpace } from 'micromark-util-character';\nimport { normalizeIdentifier } from 'micromark-util-normalize-identifier';\nimport { codes } from 'micromark-util-symbol/codes.js';\nimport { types } from 'micromark-util-symbol/types.js';\nimport { ok as assert } from 'uvu/assert';\n\n/** @type {Construct} */\nexport const definition = {\n  name: 'definition',\n  tokenize: tokenizeDefinition\n};\n\n/** @type {Construct} */\nconst titleBefore = {\n  tokenize: tokenizeTitleBefore,\n  partial: true\n};\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeDefinition(effects, ok, nok) {\n  const self = this;\n  /** @type {string} */\n  let identifier;\n  return start;\n\n  /**\n   * At start of a definition.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    // Do not interrupt paragraphs (but do follow definitions).\n    // To do: do `interrupt` the way `markdown-rs` does.\n    // To do: parse whitespace the way `markdown-rs` does.\n    effects.enter(types.definition);\n    return before(code);\n  }\n\n  /**\n   * After optional whitespace, at `[`.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function before(code) {\n    // To do: parse whitespace the way `markdown-rs` does.\n    assert(code === codes.leftSquareBracket, 'expected `[`');\n    return factoryLabel.call(self, effects, labelAfter,\n    // Note: we don’t need to reset the way `markdown-rs` does.\n    nok, types.definitionLabel, types.definitionLabelMarker, types.definitionLabelString)(code);\n  }\n\n  /**\n   * After label.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function labelAfter(code) {\n    identifier = normalizeIdentifier(self.sliceSerialize(self.events[self.events.length - 1][1]).slice(1, -1));\n    if (code === codes.colon) {\n      effects.enter(types.definitionMarker);\n      effects.consume(code);\n      effects.exit(types.definitionMarker);\n      return markerAfter;\n    }\n    return nok(code);\n  }\n\n  /**\n   * After marker.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function markerAfter(code) {\n    // Note: whitespace is optional.\n    return markdownLineEndingOrSpace(code) ? factoryWhitespace(effects, destinationBefore)(code) : destinationBefore(code);\n  }\n\n  /**\n   * Before destination.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function destinationBefore(code) {\n    return factoryDestination(effects, destinationAfter,\n    // Note: we don’t need to reset the way `markdown-rs` does.\n    nok, types.definitionDestination, types.definitionDestinationLiteral, types.definitionDestinationLiteralMarker, types.definitionDestinationRaw, types.definitionDestinationString)(code);\n  }\n\n  /**\n   * After destination.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function destinationAfter(code) {\n    return effects.attempt(titleBefore, after, after)(code);\n  }\n\n  /**\n   * After definition.\n   *\n   * ```markdown\n   * > | [a]: b\n   *           ^\n   * > | [a]: b \"c\"\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    return markdownSpace(code) ? factorySpace(effects, afterWhitespace, types.whitespace)(code) : afterWhitespace(code);\n  }\n\n  /**\n   * After definition, after optional whitespace.\n   *\n   * ```markdown\n   * > | [a]: b\n   *           ^\n   * > | [a]: b \"c\"\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function afterWhitespace(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.definition);\n\n      // Note: we don’t care about uniqueness.\n      // It’s likely that that doesn’t happen very frequently.\n      // It is more likely that it wastes precious time.\n      self.parser.defined.push(identifier);\n\n      // To do: `markdown-rs` interrupt.\n      // // You’d be interrupting.\n      // tokenizer.interrupt = true\n      return ok(code);\n    }\n    return nok(code);\n  }\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeTitleBefore(effects, ok, nok) {\n  return titleBefore;\n\n  /**\n   * After destination, at whitespace.\n   *\n   * ```markdown\n   * > | [a]: b\n   *           ^\n   * > | [a]: b \"c\"\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function titleBefore(code) {\n    return markdownLineEndingOrSpace(code) ? factoryWhitespace(effects, beforeMarker)(code) : nok(code);\n  }\n\n  /**\n   * At title.\n   *\n   * ```markdown\n   *   | [a]: b\n   * > | \"c\"\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function beforeMarker(code) {\n    return factoryTitle(effects, titleAfter, nok, types.definitionTitle, types.definitionTitleMarker, types.definitionTitleString)(code);\n  }\n\n  /**\n   * After title.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function titleAfter(code) {\n    return markdownSpace(code) ? factorySpace(effects, titleAfterOptionalWhitespace, types.whitespace)(code) : titleAfterOptionalWhitespace(code);\n  }\n\n  /**\n   * After title, after optional whitespace.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function titleAfterOptionalWhitespace(code) {\n    return code === codes.eof || markdownLineEnding(code) ? ok(code) : nok(code);\n  }\n}", "map": {"version": 3, "names": ["factoryDestination", "factoryLabel", "factorySpace", "factoryTitle", "factoryWhitespace", "markdownLineEnding", "markdownLineEndingOrSpace", "markdownSpace", "normalizeIdentifier", "codes", "types", "ok", "assert", "definition", "name", "tokenize", "tokenizeDefinition", "titleBefore", "tokenizeTitleBefore", "partial", "effects", "nok", "self", "identifier", "start", "code", "enter", "before", "leftSquareBracket", "call", "labelAfter", "definitionLabel", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "definitionLabelString", "sliceSerialize", "events", "length", "slice", "colon", "definitionMarker", "consume", "exit", "markerAfter", "destinationBefore", "destinationAfter", "definitionDestination", "definitionDestinationLiteral", "definitionDestinationLiteralMarker", "definitionDestinationRaw", "definitionDestinationString", "attempt", "after", "afterWhitespace", "whitespace", "eof", "parser", "defined", "push", "<PERSON><PERSON><PERSON><PERSON>", "titleAfter", "definitionTitle", "definitionTitleMarker", "definitionTitleString", "titleAfterOptionalWhitespace"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/micromark-core-commonmark/dev/lib/definition.js"], "sourcesContent": ["/**\n * @typedef {import('micromark-util-types').Construct} Construct\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Tokenizer} Tokenizer\n */\n\nimport {factoryDestination} from 'micromark-factory-destination'\nimport {factoryLabel} from 'micromark-factory-label'\nimport {factorySpace} from 'micromark-factory-space'\nimport {factoryTitle} from 'micromark-factory-title'\nimport {factoryWhitespace} from 'micromark-factory-whitespace'\nimport {\n  markdownLineEnding,\n  markdownLineEndingOrSpace,\n  markdownSpace\n} from 'micromark-util-character'\nimport {normalizeIdentifier} from 'micromark-util-normalize-identifier'\nimport {codes} from 'micromark-util-symbol/codes.js'\nimport {types} from 'micromark-util-symbol/types.js'\nimport {ok as assert} from 'uvu/assert'\n\n/** @type {Construct} */\nexport const definition = {name: 'definition', tokenize: tokenizeDefinition}\n\n/** @type {Construct} */\nconst titleBefore = {tokenize: tokenizeTitleBefore, partial: true}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeDefinition(effects, ok, nok) {\n  const self = this\n  /** @type {string} */\n  let identifier\n\n  return start\n\n  /**\n   * At start of a definition.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    // Do not interrupt paragraphs (but do follow definitions).\n    // To do: do `interrupt` the way `markdown-rs` does.\n    // To do: parse whitespace the way `markdown-rs` does.\n    effects.enter(types.definition)\n    return before(code)\n  }\n\n  /**\n   * After optional whitespace, at `[`.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function before(code) {\n    // To do: parse whitespace the way `markdown-rs` does.\n    assert(code === codes.leftSquareBracket, 'expected `[`')\n    return factoryLabel.call(\n      self,\n      effects,\n      labelAfter,\n      // Note: we don’t need to reset the way `markdown-rs` does.\n      nok,\n      types.definitionLabel,\n      types.definitionLabelMarker,\n      types.definitionLabelString\n    )(code)\n  }\n\n  /**\n   * After label.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function labelAfter(code) {\n    identifier = normalizeIdentifier(\n      self.sliceSerialize(self.events[self.events.length - 1][1]).slice(1, -1)\n    )\n\n    if (code === codes.colon) {\n      effects.enter(types.definitionMarker)\n      effects.consume(code)\n      effects.exit(types.definitionMarker)\n      return markerAfter\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After marker.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function markerAfter(code) {\n    // Note: whitespace is optional.\n    return markdownLineEndingOrSpace(code)\n      ? factoryWhitespace(effects, destinationBefore)(code)\n      : destinationBefore(code)\n  }\n\n  /**\n   * Before destination.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function destinationBefore(code) {\n    return factoryDestination(\n      effects,\n      destinationAfter,\n      // Note: we don’t need to reset the way `markdown-rs` does.\n      nok,\n      types.definitionDestination,\n      types.definitionDestinationLiteral,\n      types.definitionDestinationLiteralMarker,\n      types.definitionDestinationRaw,\n      types.definitionDestinationString\n    )(code)\n  }\n\n  /**\n   * After destination.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function destinationAfter(code) {\n    return effects.attempt(titleBefore, after, after)(code)\n  }\n\n  /**\n   * After definition.\n   *\n   * ```markdown\n   * > | [a]: b\n   *           ^\n   * > | [a]: b \"c\"\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    return markdownSpace(code)\n      ? factorySpace(effects, afterWhitespace, types.whitespace)(code)\n      : afterWhitespace(code)\n  }\n\n  /**\n   * After definition, after optional whitespace.\n   *\n   * ```markdown\n   * > | [a]: b\n   *           ^\n   * > | [a]: b \"c\"\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function afterWhitespace(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.definition)\n\n      // Note: we don’t care about uniqueness.\n      // It’s likely that that doesn’t happen very frequently.\n      // It is more likely that it wastes precious time.\n      self.parser.defined.push(identifier)\n\n      // To do: `markdown-rs` interrupt.\n      // // You’d be interrupting.\n      // tokenizer.interrupt = true\n      return ok(code)\n    }\n\n    return nok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeTitleBefore(effects, ok, nok) {\n  return titleBefore\n\n  /**\n   * After destination, at whitespace.\n   *\n   * ```markdown\n   * > | [a]: b\n   *           ^\n   * > | [a]: b \"c\"\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function titleBefore(code) {\n    return markdownLineEndingOrSpace(code)\n      ? factoryWhitespace(effects, beforeMarker)(code)\n      : nok(code)\n  }\n\n  /**\n   * At title.\n   *\n   * ```markdown\n   *   | [a]: b\n   * > | \"c\"\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function beforeMarker(code) {\n    return factoryTitle(\n      effects,\n      titleAfter,\n      nok,\n      types.definitionTitle,\n      types.definitionTitleMarker,\n      types.definitionTitleString\n    )(code)\n  }\n\n  /**\n   * After title.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function titleAfter(code) {\n    return markdownSpace(code)\n      ? factorySpace(\n          effects,\n          titleAfterOptionalWhitespace,\n          types.whitespace\n        )(code)\n      : titleAfterOptionalWhitespace(code)\n  }\n\n  /**\n   * After title, after optional whitespace.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function titleAfterOptionalWhitespace(code) {\n    return code === codes.eof || markdownLineEnding(code) ? ok(code) : nok(code)\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,kBAAkB,QAAO,+BAA+B;AAChE,SAAQC,YAAY,QAAO,yBAAyB;AACpD,SAAQC,YAAY,QAAO,yBAAyB;AACpD,SAAQC,YAAY,QAAO,yBAAyB;AACpD,SAAQC,iBAAiB,QAAO,8BAA8B;AAC9D,SACEC,kBAAkB,EAClBC,yBAAyB,EACzBC,aAAa,QACR,0BAA0B;AACjC,SAAQC,mBAAmB,QAAO,qCAAqC;AACvE,SAAQC,KAAK,QAAO,gCAAgC;AACpD,SAAQC,KAAK,QAAO,gCAAgC;AACpD,SAAQC,EAAE,IAAIC,MAAM,QAAO,YAAY;;AAEvC;AACA,OAAO,MAAMC,UAAU,GAAG;EAACC,IAAI,EAAE,YAAY;EAAEC,QAAQ,EAAEC;AAAkB,CAAC;;AAE5E;AACA,MAAMC,WAAW,GAAG;EAACF,QAAQ,EAAEG,mBAAmB;EAAEC,OAAO,EAAE;AAAI,CAAC;;AAElE;AACA;AACA;AACA;AACA,SAASH,kBAAkBA,CAACI,OAAO,EAAET,EAAE,EAAEU,GAAG,EAAE;EAC5C,MAAMC,IAAI,GAAG,IAAI;EACjB;EACA,IAAIC,UAAU;EAEd,OAAOC,KAAK;;EAEZ;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,KAAKA,CAACC,IAAI,EAAE;IACnB;IACA;IACA;IACAL,OAAO,CAACM,KAAK,CAAChB,KAAK,CAACG,UAAU,CAAC;IAC/B,OAAOc,MAAM,CAACF,IAAI,CAAC;EACrB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASE,MAAMA,CAACF,IAAI,EAAE;IACpB;IACAb,MAAM,CAACa,IAAI,KAAKhB,KAAK,CAACmB,iBAAiB,EAAE,cAAc,CAAC;IACxD,OAAO3B,YAAY,CAAC4B,IAAI,CACtBP,IAAI,EACJF,OAAO,EACPU,UAAU;IACV;IACAT,GAAG,EACHX,KAAK,CAACqB,eAAe,EACrBrB,KAAK,CAACsB,qBAAqB,EAC3BtB,KAAK,CAACuB,qBAAqB,CAC5B,CAACR,IAAI,CAAC;EACT;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASK,UAAUA,CAACL,IAAI,EAAE;IACxBF,UAAU,GAAGf,mBAAmB,CAC9Bc,IAAI,CAACY,cAAc,CAACZ,IAAI,CAACa,MAAM,CAACb,IAAI,CAACa,MAAM,CAACC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CACzE;IAED,IAAIZ,IAAI,KAAKhB,KAAK,CAAC6B,KAAK,EAAE;MACxBlB,OAAO,CAACM,KAAK,CAAChB,KAAK,CAAC6B,gBAAgB,CAAC;MACrCnB,OAAO,CAACoB,OAAO,CAACf,IAAI,CAAC;MACrBL,OAAO,CAACqB,IAAI,CAAC/B,KAAK,CAAC6B,gBAAgB,CAAC;MACpC,OAAOG,WAAW;IACpB;IAEA,OAAOrB,GAAG,CAACI,IAAI,CAAC;EAClB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASiB,WAAWA,CAACjB,IAAI,EAAE;IACzB;IACA,OAAOnB,yBAAyB,CAACmB,IAAI,CAAC,GAClCrB,iBAAiB,CAACgB,OAAO,EAAEuB,iBAAiB,CAAC,CAAClB,IAAI,CAAC,GACnDkB,iBAAiB,CAAClB,IAAI,CAAC;EAC7B;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASkB,iBAAiBA,CAAClB,IAAI,EAAE;IAC/B,OAAOzB,kBAAkB,CACvBoB,OAAO,EACPwB,gBAAgB;IAChB;IACAvB,GAAG,EACHX,KAAK,CAACmC,qBAAqB,EAC3BnC,KAAK,CAACoC,4BAA4B,EAClCpC,KAAK,CAACqC,kCAAkC,EACxCrC,KAAK,CAACsC,wBAAwB,EAC9BtC,KAAK,CAACuC,2BAA2B,CAClC,CAACxB,IAAI,CAAC;EACT;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASmB,gBAAgBA,CAACnB,IAAI,EAAE;IAC9B,OAAOL,OAAO,CAAC8B,OAAO,CAACjC,WAAW,EAAEkC,KAAK,EAAEA,KAAK,CAAC,CAAC1B,IAAI,CAAC;EACzD;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAAS0B,KAAKA,CAAC1B,IAAI,EAAE;IACnB,OAAOlB,aAAa,CAACkB,IAAI,CAAC,GACtBvB,YAAY,CAACkB,OAAO,EAAEgC,eAAe,EAAE1C,KAAK,CAAC2C,UAAU,CAAC,CAAC5B,IAAI,CAAC,GAC9D2B,eAAe,CAAC3B,IAAI,CAAC;EAC3B;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAAS2B,eAAeA,CAAC3B,IAAI,EAAE;IAC7B,IAAIA,IAAI,KAAKhB,KAAK,CAAC6C,GAAG,IAAIjD,kBAAkB,CAACoB,IAAI,CAAC,EAAE;MAClDL,OAAO,CAACqB,IAAI,CAAC/B,KAAK,CAACG,UAAU,CAAC;;MAE9B;MACA;MACA;MACAS,IAAI,CAACiC,MAAM,CAACC,OAAO,CAACC,IAAI,CAAClC,UAAU,CAAC;;MAEpC;MACA;MACA;MACA,OAAOZ,EAAE,CAACc,IAAI,CAAC;IACjB;IAEA,OAAOJ,GAAG,CAACI,IAAI,CAAC;EAClB;AACF;;AAEA;AACA;AACA;AACA;AACA,SAASP,mBAAmBA,CAACE,OAAO,EAAET,EAAE,EAAEU,GAAG,EAAE;EAC7C,OAAOJ,WAAW;;EAElB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,WAAWA,CAACQ,IAAI,EAAE;IACzB,OAAOnB,yBAAyB,CAACmB,IAAI,CAAC,GAClCrB,iBAAiB,CAACgB,OAAO,EAAEsC,YAAY,CAAC,CAACjC,IAAI,CAAC,GAC9CJ,GAAG,CAACI,IAAI,CAAC;EACf;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASiC,YAAYA,CAACjC,IAAI,EAAE;IAC1B,OAAOtB,YAAY,CACjBiB,OAAO,EACPuC,UAAU,EACVtC,GAAG,EACHX,KAAK,CAACkD,eAAe,EACrBlD,KAAK,CAACmD,qBAAqB,EAC3BnD,KAAK,CAACoD,qBAAqB,CAC5B,CAACrC,IAAI,CAAC;EACT;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASkC,UAAUA,CAAClC,IAAI,EAAE;IACxB,OAAOlB,aAAa,CAACkB,IAAI,CAAC,GACtBvB,YAAY,CACVkB,OAAO,EACP2C,4BAA4B,EAC5BrD,KAAK,CAAC2C,UAAU,CACjB,CAAC5B,IAAI,CAAC,GACPsC,4BAA4B,CAACtC,IAAI,CAAC;EACxC;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASsC,4BAA4BA,CAACtC,IAAI,EAAE;IAC1C,OAAOA,IAAI,KAAKhB,KAAK,CAAC6C,GAAG,IAAIjD,kBAAkB,CAACoB,IAAI,CAAC,GAAGd,EAAE,CAACc,IAAI,CAAC,GAAGJ,GAAG,CAACI,IAAI,CAAC;EAC9E;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}