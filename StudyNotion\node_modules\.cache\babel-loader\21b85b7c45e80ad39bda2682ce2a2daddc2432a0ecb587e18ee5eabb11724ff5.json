{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar _react = _interopRequireDefault(require(\"react\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _allowed = _interopRequireDefault(require(\"../utils/allowed\"));\nvar Thead = function Thead(props) {\n  var children = props.children;\n  return /*#__PURE__*/_react[\"default\"].createElement(\"thead\", (0, _extends2[\"default\"])({\n    \"data-testid\": \"thead\"\n  }, (0, _allowed[\"default\"])(props)), /*#__PURE__*/_react[\"default\"].cloneElement(children, {\n    inHeader: true\n  }));\n};\nThead.propTypes = {\n  children: _propTypes[\"default\"].node\n};\nThead.defaultProps = {\n  children: undefined\n};\nvar _default = Thead;\nexports[\"default\"] = _default;", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "_extends2", "_react", "_propTypes", "_allowed", "<PERSON><PERSON>", "props", "children", "createElement", "cloneElement", "inHeader", "propTypes", "node", "defaultProps", "undefined", "_default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/react-super-responsive-table/dist/components/Thead.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\n\nvar _react = _interopRequireDefault(require(\"react\"));\n\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\n\nvar _allowed = _interopRequireDefault(require(\"../utils/allowed\"));\n\nvar Thead = function Thead(props) {\n  var children = props.children;\n  return /*#__PURE__*/_react[\"default\"].createElement(\"thead\", (0, _extends2[\"default\"])({\n    \"data-testid\": \"thead\"\n  }, (0, _allowed[\"default\"])(props)), /*#__PURE__*/_react[\"default\"].cloneElement(children, {\n    inHeader: true\n  }));\n};\n\nThead.propTypes = {\n  children: _propTypes[\"default\"].node\n};\nThead.defaultProps = {\n  children: undefined\n};\nvar _default = Thead;\nexports[\"default\"] = _default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC;AAEpFC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;AAE3B,IAAIE,SAAS,GAAGN,sBAAsB,CAACC,OAAO,CAAC,gCAAgC,CAAC,CAAC;AAEjF,IAAIM,MAAM,GAAGP,sBAAsB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AAErD,IAAIO,UAAU,GAAGR,sBAAsB,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;AAE9D,IAAIQ,QAAQ,GAAGT,sBAAsB,CAACC,OAAO,CAAC,kBAAkB,CAAC,CAAC;AAElE,IAAIS,KAAK,GAAG,SAASA,KAAKA,CAACC,KAAK,EAAE;EAChC,IAAIC,QAAQ,GAAGD,KAAK,CAACC,QAAQ;EAC7B,OAAO,aAAaL,MAAM,CAAC,SAAS,CAAC,CAACM,aAAa,CAAC,OAAO,EAAE,CAAC,CAAC,EAAEP,SAAS,CAAC,SAAS,CAAC,EAAE;IACrF,aAAa,EAAE;EACjB,CAAC,EAAE,CAAC,CAAC,EAAEG,QAAQ,CAAC,SAAS,CAAC,EAAEE,KAAK,CAAC,CAAC,EAAE,aAAaJ,MAAM,CAAC,SAAS,CAAC,CAACO,YAAY,CAACF,QAAQ,EAAE;IACzFG,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC;AACL,CAAC;AAEDL,KAAK,CAACM,SAAS,GAAG;EAChBJ,QAAQ,EAAEJ,UAAU,CAAC,SAAS,CAAC,CAACS;AAClC,CAAC;AACDP,KAAK,CAACQ,YAAY,GAAG;EACnBN,QAAQ,EAAEO;AACZ,CAAC;AACD,IAAIC,QAAQ,GAAGV,KAAK;AACpBN,OAAO,CAAC,SAAS,CAAC,GAAGgB,QAAQ"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}