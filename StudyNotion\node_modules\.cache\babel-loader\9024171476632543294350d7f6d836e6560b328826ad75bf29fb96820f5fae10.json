{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _react = _interopRequireDefault(require(\"react\"));\nvar _classnames = _interopRequireDefault(require(\"classnames\"));\nvar _utils = require(\"../../utils\");\nvar propTypes = {\n  player: _propTypes[\"default\"].object,\n  className: _propTypes[\"default\"].string\n};\nfunction CurrentTimeDisplay(_ref) {\n  var _ref$player = _ref.player,\n    currentTime = _ref$player.currentTime,\n    duration = _ref$player.duration,\n    className = _ref.className;\n  var formattedTime = (0, _utils.formatTime)(currentTime, duration);\n  return _react[\"default\"].createElement(\"div\", {\n    className: (0, _classnames[\"default\"])('video-react-current-time video-react-time-control video-react-control', className)\n  }, _react[\"default\"].createElement(\"div\", {\n    className: \"video-react-current-time-display\",\n    \"aria-live\": \"off\"\n  }, _react[\"default\"].createElement(\"span\", {\n    className: \"video-react-control-text\"\n  }, \"Current Time \"), formattedTime));\n}\nCurrentTimeDisplay.propTypes = propTypes;\nCurrentTimeDisplay.displayName = 'CurrentTimeDisplay';\nvar _default = CurrentTimeDisplay;\nexports[\"default\"] = _default;", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "_propTypes", "_react", "_classnames", "_utils", "propTypes", "player", "object", "className", "string", "CurrentTimeDisplay", "_ref", "_ref$player", "currentTime", "duration", "formattedTime", "formatTime", "createElement", "displayName", "_default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/video-react/lib/components/time-controls/CurrentTimeDisplay.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\n\nvar _react = _interopRequireDefault(require(\"react\"));\n\nvar _classnames = _interopRequireDefault(require(\"classnames\"));\n\nvar _utils = require(\"../../utils\");\n\nvar propTypes = {\n  player: _propTypes[\"default\"].object,\n  className: _propTypes[\"default\"].string\n};\n\nfunction CurrentTimeDisplay(_ref) {\n  var _ref$player = _ref.player,\n      currentTime = _ref$player.currentTime,\n      duration = _ref$player.duration,\n      className = _ref.className;\n  var formattedTime = (0, _utils.formatTime)(currentTime, duration);\n  return _react[\"default\"].createElement(\"div\", {\n    className: (0, _classnames[\"default\"])('video-react-current-time video-react-time-control video-react-control', className)\n  }, _react[\"default\"].createElement(\"div\", {\n    className: \"video-react-current-time-display\",\n    \"aria-live\": \"off\"\n  }, _react[\"default\"].createElement(\"span\", {\n    className: \"video-react-control-text\"\n  }, \"Current Time \"), formattedTime));\n}\n\nCurrentTimeDisplay.propTypes = propTypes;\nCurrentTimeDisplay.displayName = 'CurrentTimeDisplay';\nvar _default = CurrentTimeDisplay;\nexports[\"default\"] = _default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC;AAEpFC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;AAE3B,IAAIE,UAAU,GAAGN,sBAAsB,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;AAE9D,IAAIM,MAAM,GAAGP,sBAAsB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AAErD,IAAIO,WAAW,GAAGR,sBAAsB,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;AAE/D,IAAIQ,MAAM,GAAGR,OAAO,CAAC,aAAa,CAAC;AAEnC,IAAIS,SAAS,GAAG;EACdC,MAAM,EAAEL,UAAU,CAAC,SAAS,CAAC,CAACM,MAAM;EACpCC,SAAS,EAAEP,UAAU,CAAC,SAAS,CAAC,CAACQ;AACnC,CAAC;AAED,SAASC,kBAAkBA,CAACC,IAAI,EAAE;EAChC,IAAIC,WAAW,GAAGD,IAAI,CAACL,MAAM;IACzBO,WAAW,GAAGD,WAAW,CAACC,WAAW;IACrCC,QAAQ,GAAGF,WAAW,CAACE,QAAQ;IAC/BN,SAAS,GAAGG,IAAI,CAACH,SAAS;EAC9B,IAAIO,aAAa,GAAG,CAAC,CAAC,EAAEX,MAAM,CAACY,UAAU,EAAEH,WAAW,EAAEC,QAAQ,CAAC;EACjE,OAAOZ,MAAM,CAAC,SAAS,CAAC,CAACe,aAAa,CAAC,KAAK,EAAE;IAC5CT,SAAS,EAAE,CAAC,CAAC,EAAEL,WAAW,CAAC,SAAS,CAAC,EAAE,uEAAuE,EAAEK,SAAS;EAC3H,CAAC,EAAEN,MAAM,CAAC,SAAS,CAAC,CAACe,aAAa,CAAC,KAAK,EAAE;IACxCT,SAAS,EAAE,kCAAkC;IAC7C,WAAW,EAAE;EACf,CAAC,EAAEN,MAAM,CAAC,SAAS,CAAC,CAACe,aAAa,CAAC,MAAM,EAAE;IACzCT,SAAS,EAAE;EACb,CAAC,EAAE,eAAe,CAAC,EAAEO,aAAa,CAAC,CAAC;AACtC;AAEAL,kBAAkB,CAACL,SAAS,GAAGA,SAAS;AACxCK,kBAAkB,CAACQ,WAAW,GAAG,oBAAoB;AACrD,IAAIC,QAAQ,GAAGT,kBAAkB;AACjCX,OAAO,CAAC,SAAS,CAAC,GAAGoB,QAAQ"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}