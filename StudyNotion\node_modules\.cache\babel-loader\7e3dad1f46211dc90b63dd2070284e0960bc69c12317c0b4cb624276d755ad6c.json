{"ast": null, "code": "import loopCreate from './loopCreate.js';\nimport loopFix from './loopFix.js';\nimport loopDestroy from './loopDestroy.js';\nexport default {\n  loopCreate,\n  loopFix,\n  loopDestroy\n};", "map": {"version": 3, "names": ["loopCreate", "loopFix", "loop<PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/core/loop/index.js"], "sourcesContent": ["import loopCreate from './loopCreate.js';\nimport loopFix from './loopFix.js';\nimport loopDestroy from './loopDestroy.js';\nexport default {\n  loopCreate,\n  loopFix,\n  loopDestroy\n};"], "mappings": "AAAA,OAAOA,UAAU,MAAM,iBAAiB;AACxC,OAAOC,OAAO,MAAM,cAAc;AAClC,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,eAAe;EACbF,UAAU;EACVC,OAAO;EACPC;AACF,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}