{"ast": null, "code": "import { animateCSSModeScroll } from '../../shared/utils.js';\nexport default function slideTo() {\n  let index = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n  let speed = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : this.params.speed;\n  let runCallbacks = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;\n  let internal = arguments.length > 3 ? arguments[3] : undefined;\n  let initial = arguments.length > 4 ? arguments[4] : undefined;\n  if (typeof index === 'string') {\n    index = parseInt(index, 10);\n  }\n  const swiper = this;\n  let slideIndex = index;\n  if (slideIndex < 0) slideIndex = 0;\n  const {\n    params,\n    snapGrid,\n    slidesGrid,\n    previousIndex,\n    activeIndex,\n    rtlTranslate: rtl,\n    wrapperEl,\n    enabled\n  } = swiper;\n  if (swiper.animating && params.preventInteractionOnTransition || !enabled && !internal && !initial) {\n    return false;\n  }\n  const skip = Math.min(swiper.params.slidesPerGroupSkip, slideIndex);\n  let snapIndex = skip + Math.floor((slideIndex - skip) / swiper.params.slidesPerGroup);\n  if (snapIndex >= snapGrid.length) snapIndex = snapGrid.length - 1;\n  const translate = -snapGrid[snapIndex];\n  // Normalize slideIndex\n  if (params.normalizeSlideIndex) {\n    for (let i = 0; i < slidesGrid.length; i += 1) {\n      const normalizedTranslate = -Math.floor(translate * 100);\n      const normalizedGrid = Math.floor(slidesGrid[i] * 100);\n      const normalizedGridNext = Math.floor(slidesGrid[i + 1] * 100);\n      if (typeof slidesGrid[i + 1] !== 'undefined') {\n        if (normalizedTranslate >= normalizedGrid && normalizedTranslate < normalizedGridNext - (normalizedGridNext - normalizedGrid) / 2) {\n          slideIndex = i;\n        } else if (normalizedTranslate >= normalizedGrid && normalizedTranslate < normalizedGridNext) {\n          slideIndex = i + 1;\n        }\n      } else if (normalizedTranslate >= normalizedGrid) {\n        slideIndex = i;\n      }\n    }\n  }\n  // Directions locks\n  if (swiper.initialized && slideIndex !== activeIndex) {\n    if (!swiper.allowSlideNext && (rtl ? translate > swiper.translate && translate > swiper.minTranslate() : translate < swiper.translate && translate < swiper.minTranslate())) {\n      return false;\n    }\n    if (!swiper.allowSlidePrev && translate > swiper.translate && translate > swiper.maxTranslate()) {\n      if ((activeIndex || 0) !== slideIndex) {\n        return false;\n      }\n    }\n  }\n  if (slideIndex !== (previousIndex || 0) && runCallbacks) {\n    swiper.emit('beforeSlideChangeStart');\n  }\n\n  // Update progress\n  swiper.updateProgress(translate);\n  let direction;\n  if (slideIndex > activeIndex) direction = 'next';else if (slideIndex < activeIndex) direction = 'prev';else direction = 'reset';\n\n  // Update Index\n  if (rtl && -translate === swiper.translate || !rtl && translate === swiper.translate) {\n    swiper.updateActiveIndex(slideIndex);\n    // Update Height\n    if (params.autoHeight) {\n      swiper.updateAutoHeight();\n    }\n    swiper.updateSlidesClasses();\n    if (params.effect !== 'slide') {\n      swiper.setTranslate(translate);\n    }\n    if (direction !== 'reset') {\n      swiper.transitionStart(runCallbacks, direction);\n      swiper.transitionEnd(runCallbacks, direction);\n    }\n    return false;\n  }\n  if (params.cssMode) {\n    const isH = swiper.isHorizontal();\n    const t = rtl ? translate : -translate;\n    if (speed === 0) {\n      const isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n      if (isVirtual) {\n        swiper.wrapperEl.style.scrollSnapType = 'none';\n        swiper._immediateVirtual = true;\n      }\n      if (isVirtual && !swiper._cssModeVirtualInitialSet && swiper.params.initialSlide > 0) {\n        swiper._cssModeVirtualInitialSet = true;\n        requestAnimationFrame(() => {\n          wrapperEl[isH ? 'scrollLeft' : 'scrollTop'] = t;\n        });\n      } else {\n        wrapperEl[isH ? 'scrollLeft' : 'scrollTop'] = t;\n      }\n      if (isVirtual) {\n        requestAnimationFrame(() => {\n          swiper.wrapperEl.style.scrollSnapType = '';\n          swiper._immediateVirtual = false;\n        });\n      }\n    } else {\n      if (!swiper.support.smoothScroll) {\n        animateCSSModeScroll({\n          swiper,\n          targetPosition: t,\n          side: isH ? 'left' : 'top'\n        });\n        return true;\n      }\n      wrapperEl.scrollTo({\n        [isH ? 'left' : 'top']: t,\n        behavior: 'smooth'\n      });\n    }\n    return true;\n  }\n  swiper.setTransition(speed);\n  swiper.setTranslate(translate);\n  swiper.updateActiveIndex(slideIndex);\n  swiper.updateSlidesClasses();\n  swiper.emit('beforeTransitionStart', speed, internal);\n  swiper.transitionStart(runCallbacks, direction);\n  if (speed === 0) {\n    swiper.transitionEnd(runCallbacks, direction);\n  } else if (!swiper.animating) {\n    swiper.animating = true;\n    if (!swiper.onSlideToWrapperTransitionEnd) {\n      swiper.onSlideToWrapperTransitionEnd = function transitionEnd(e) {\n        if (!swiper || swiper.destroyed) return;\n        if (e.target !== this) return;\n        swiper.wrapperEl.removeEventListener('transitionend', swiper.onSlideToWrapperTransitionEnd);\n        swiper.onSlideToWrapperTransitionEnd = null;\n        delete swiper.onSlideToWrapperTransitionEnd;\n        swiper.transitionEnd(runCallbacks, direction);\n      };\n    }\n    swiper.wrapperEl.addEventListener('transitionend', swiper.onSlideToWrapperTransitionEnd);\n  }\n  return true;\n}", "map": {"version": 3, "names": ["animateCSSModeScroll", "slideTo", "index", "arguments", "length", "undefined", "speed", "params", "runCallbacks", "internal", "initial", "parseInt", "swiper", "slideIndex", "snapGrid", "slidesGrid", "previousIndex", "activeIndex", "rtlTranslate", "rtl", "wrapperEl", "enabled", "animating", "preventInteractionOnTransition", "skip", "Math", "min", "slidesPerGroupSkip", "snapIndex", "floor", "slidesPerGroup", "translate", "normalizeSlideIndex", "i", "normalizedTranslate", "normalizedGrid", "normalizedGridNext", "initialized", "allowSlideNext", "minTranslate", "allowSlidePrev", "maxTranslate", "emit", "updateProgress", "direction", "updateActiveIndex", "autoHeight", "updateAutoHeight", "updateSlidesClasses", "effect", "setTranslate", "transitionStart", "transitionEnd", "cssMode", "isH", "isHorizontal", "t", "isVirtual", "virtual", "style", "scrollSnapType", "_immediateVirtual", "_cssModeVirtualInitialSet", "initialSlide", "requestAnimationFrame", "support", "smoothScroll", "targetPosition", "side", "scrollTo", "behavior", "setTransition", "onSlideToWrapperTransitionEnd", "e", "destroyed", "target", "removeEventListener", "addEventListener"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/core/slide/slideTo.js"], "sourcesContent": ["import { animateCSSModeScroll } from '../../shared/utils.js';\nexport default function slideTo(index = 0, speed = this.params.speed, runCallbacks = true, internal, initial) {\n  if (typeof index === 'string') {\n    index = parseInt(index, 10);\n  }\n  const swiper = this;\n  let slideIndex = index;\n  if (slideIndex < 0) slideIndex = 0;\n  const {\n    params,\n    snapGrid,\n    slidesGrid,\n    previousIndex,\n    activeIndex,\n    rtlTranslate: rtl,\n    wrapperEl,\n    enabled\n  } = swiper;\n  if (swiper.animating && params.preventInteractionOnTransition || !enabled && !internal && !initial) {\n    return false;\n  }\n  const skip = Math.min(swiper.params.slidesPerGroupSkip, slideIndex);\n  let snapIndex = skip + Math.floor((slideIndex - skip) / swiper.params.slidesPerGroup);\n  if (snapIndex >= snapGrid.length) snapIndex = snapGrid.length - 1;\n  const translate = -snapGrid[snapIndex];\n  // Normalize slideIndex\n  if (params.normalizeSlideIndex) {\n    for (let i = 0; i < slidesGrid.length; i += 1) {\n      const normalizedTranslate = -Math.floor(translate * 100);\n      const normalizedGrid = Math.floor(slidesGrid[i] * 100);\n      const normalizedGridNext = Math.floor(slidesGrid[i + 1] * 100);\n      if (typeof slidesGrid[i + 1] !== 'undefined') {\n        if (normalizedTranslate >= normalizedGrid && normalizedTranslate < normalizedGridNext - (normalizedGridNext - normalizedGrid) / 2) {\n          slideIndex = i;\n        } else if (normalizedTranslate >= normalizedGrid && normalizedTranslate < normalizedGridNext) {\n          slideIndex = i + 1;\n        }\n      } else if (normalizedTranslate >= normalizedGrid) {\n        slideIndex = i;\n      }\n    }\n  }\n  // Directions locks\n  if (swiper.initialized && slideIndex !== activeIndex) {\n    if (!swiper.allowSlideNext && (rtl ? translate > swiper.translate && translate > swiper.minTranslate() : translate < swiper.translate && translate < swiper.minTranslate())) {\n      return false;\n    }\n    if (!swiper.allowSlidePrev && translate > swiper.translate && translate > swiper.maxTranslate()) {\n      if ((activeIndex || 0) !== slideIndex) {\n        return false;\n      }\n    }\n  }\n  if (slideIndex !== (previousIndex || 0) && runCallbacks) {\n    swiper.emit('beforeSlideChangeStart');\n  }\n\n  // Update progress\n  swiper.updateProgress(translate);\n  let direction;\n  if (slideIndex > activeIndex) direction = 'next';else if (slideIndex < activeIndex) direction = 'prev';else direction = 'reset';\n\n  // Update Index\n  if (rtl && -translate === swiper.translate || !rtl && translate === swiper.translate) {\n    swiper.updateActiveIndex(slideIndex);\n    // Update Height\n    if (params.autoHeight) {\n      swiper.updateAutoHeight();\n    }\n    swiper.updateSlidesClasses();\n    if (params.effect !== 'slide') {\n      swiper.setTranslate(translate);\n    }\n    if (direction !== 'reset') {\n      swiper.transitionStart(runCallbacks, direction);\n      swiper.transitionEnd(runCallbacks, direction);\n    }\n    return false;\n  }\n  if (params.cssMode) {\n    const isH = swiper.isHorizontal();\n    const t = rtl ? translate : -translate;\n    if (speed === 0) {\n      const isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n      if (isVirtual) {\n        swiper.wrapperEl.style.scrollSnapType = 'none';\n        swiper._immediateVirtual = true;\n      }\n      if (isVirtual && !swiper._cssModeVirtualInitialSet && swiper.params.initialSlide > 0) {\n        swiper._cssModeVirtualInitialSet = true;\n        requestAnimationFrame(() => {\n          wrapperEl[isH ? 'scrollLeft' : 'scrollTop'] = t;\n        });\n      } else {\n        wrapperEl[isH ? 'scrollLeft' : 'scrollTop'] = t;\n      }\n      if (isVirtual) {\n        requestAnimationFrame(() => {\n          swiper.wrapperEl.style.scrollSnapType = '';\n          swiper._immediateVirtual = false;\n        });\n      }\n    } else {\n      if (!swiper.support.smoothScroll) {\n        animateCSSModeScroll({\n          swiper,\n          targetPosition: t,\n          side: isH ? 'left' : 'top'\n        });\n        return true;\n      }\n      wrapperEl.scrollTo({\n        [isH ? 'left' : 'top']: t,\n        behavior: 'smooth'\n      });\n    }\n    return true;\n  }\n  swiper.setTransition(speed);\n  swiper.setTranslate(translate);\n  swiper.updateActiveIndex(slideIndex);\n  swiper.updateSlidesClasses();\n  swiper.emit('beforeTransitionStart', speed, internal);\n  swiper.transitionStart(runCallbacks, direction);\n  if (speed === 0) {\n    swiper.transitionEnd(runCallbacks, direction);\n  } else if (!swiper.animating) {\n    swiper.animating = true;\n    if (!swiper.onSlideToWrapperTransitionEnd) {\n      swiper.onSlideToWrapperTransitionEnd = function transitionEnd(e) {\n        if (!swiper || swiper.destroyed) return;\n        if (e.target !== this) return;\n        swiper.wrapperEl.removeEventListener('transitionend', swiper.onSlideToWrapperTransitionEnd);\n        swiper.onSlideToWrapperTransitionEnd = null;\n        delete swiper.onSlideToWrapperTransitionEnd;\n        swiper.transitionEnd(runCallbacks, direction);\n      };\n    }\n    swiper.wrapperEl.addEventListener('transitionend', swiper.onSlideToWrapperTransitionEnd);\n  }\n  return true;\n}"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,uBAAuB;AAC5D,eAAe,SAASC,OAAOA,CAAA,EAA+E;EAAA,IAA9EC,KAAK,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAAA,IAAEG,KAAK,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI,CAACI,MAAM,CAACD,KAAK;EAAA,IAAEE,YAAY,GAAAL,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;EAAA,IAAEM,QAAQ,GAAAN,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAAA,IAAEK,OAAO,GAAAP,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAC1G,IAAI,OAAOH,KAAK,KAAK,QAAQ,EAAE;IAC7BA,KAAK,GAAGS,QAAQ,CAACT,KAAK,EAAE,EAAE,CAAC;EAC7B;EACA,MAAMU,MAAM,GAAG,IAAI;EACnB,IAAIC,UAAU,GAAGX,KAAK;EACtB,IAAIW,UAAU,GAAG,CAAC,EAAEA,UAAU,GAAG,CAAC;EAClC,MAAM;IACJN,MAAM;IACNO,QAAQ;IACRC,UAAU;IACVC,aAAa;IACbC,WAAW;IACXC,YAAY,EAAEC,GAAG;IACjBC,SAAS;IACTC;EACF,CAAC,GAAGT,MAAM;EACV,IAAIA,MAAM,CAACU,SAAS,IAAIf,MAAM,CAACgB,8BAA8B,IAAI,CAACF,OAAO,IAAI,CAACZ,QAAQ,IAAI,CAACC,OAAO,EAAE;IAClG,OAAO,KAAK;EACd;EACA,MAAMc,IAAI,GAAGC,IAAI,CAACC,GAAG,CAACd,MAAM,CAACL,MAAM,CAACoB,kBAAkB,EAAEd,UAAU,CAAC;EACnE,IAAIe,SAAS,GAAGJ,IAAI,GAAGC,IAAI,CAACI,KAAK,CAAC,CAAChB,UAAU,GAAGW,IAAI,IAAIZ,MAAM,CAACL,MAAM,CAACuB,cAAc,CAAC;EACrF,IAAIF,SAAS,IAAId,QAAQ,CAACV,MAAM,EAAEwB,SAAS,GAAGd,QAAQ,CAACV,MAAM,GAAG,CAAC;EACjE,MAAM2B,SAAS,GAAG,CAACjB,QAAQ,CAACc,SAAS,CAAC;EACtC;EACA,IAAIrB,MAAM,CAACyB,mBAAmB,EAAE;IAC9B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGlB,UAAU,CAACX,MAAM,EAAE6B,CAAC,IAAI,CAAC,EAAE;MAC7C,MAAMC,mBAAmB,GAAG,CAACT,IAAI,CAACI,KAAK,CAACE,SAAS,GAAG,GAAG,CAAC;MACxD,MAAMI,cAAc,GAAGV,IAAI,CAACI,KAAK,CAACd,UAAU,CAACkB,CAAC,CAAC,GAAG,GAAG,CAAC;MACtD,MAAMG,kBAAkB,GAAGX,IAAI,CAACI,KAAK,CAACd,UAAU,CAACkB,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;MAC9D,IAAI,OAAOlB,UAAU,CAACkB,CAAC,GAAG,CAAC,CAAC,KAAK,WAAW,EAAE;QAC5C,IAAIC,mBAAmB,IAAIC,cAAc,IAAID,mBAAmB,GAAGE,kBAAkB,GAAG,CAACA,kBAAkB,GAAGD,cAAc,IAAI,CAAC,EAAE;UACjItB,UAAU,GAAGoB,CAAC;QAChB,CAAC,MAAM,IAAIC,mBAAmB,IAAIC,cAAc,IAAID,mBAAmB,GAAGE,kBAAkB,EAAE;UAC5FvB,UAAU,GAAGoB,CAAC,GAAG,CAAC;QACpB;MACF,CAAC,MAAM,IAAIC,mBAAmB,IAAIC,cAAc,EAAE;QAChDtB,UAAU,GAAGoB,CAAC;MAChB;IACF;EACF;EACA;EACA,IAAIrB,MAAM,CAACyB,WAAW,IAAIxB,UAAU,KAAKI,WAAW,EAAE;IACpD,IAAI,CAACL,MAAM,CAAC0B,cAAc,KAAKnB,GAAG,GAAGY,SAAS,GAAGnB,MAAM,CAACmB,SAAS,IAAIA,SAAS,GAAGnB,MAAM,CAAC2B,YAAY,EAAE,GAAGR,SAAS,GAAGnB,MAAM,CAACmB,SAAS,IAAIA,SAAS,GAAGnB,MAAM,CAAC2B,YAAY,EAAE,CAAC,EAAE;MAC3K,OAAO,KAAK;IACd;IACA,IAAI,CAAC3B,MAAM,CAAC4B,cAAc,IAAIT,SAAS,GAAGnB,MAAM,CAACmB,SAAS,IAAIA,SAAS,GAAGnB,MAAM,CAAC6B,YAAY,EAAE,EAAE;MAC/F,IAAI,CAACxB,WAAW,IAAI,CAAC,MAAMJ,UAAU,EAAE;QACrC,OAAO,KAAK;MACd;IACF;EACF;EACA,IAAIA,UAAU,MAAMG,aAAa,IAAI,CAAC,CAAC,IAAIR,YAAY,EAAE;IACvDI,MAAM,CAAC8B,IAAI,CAAC,wBAAwB,CAAC;EACvC;;EAEA;EACA9B,MAAM,CAAC+B,cAAc,CAACZ,SAAS,CAAC;EAChC,IAAIa,SAAS;EACb,IAAI/B,UAAU,GAAGI,WAAW,EAAE2B,SAAS,GAAG,MAAM,CAAC,KAAK,IAAI/B,UAAU,GAAGI,WAAW,EAAE2B,SAAS,GAAG,MAAM,CAAC,KAAKA,SAAS,GAAG,OAAO;;EAE/H;EACA,IAAIzB,GAAG,IAAI,CAACY,SAAS,KAAKnB,MAAM,CAACmB,SAAS,IAAI,CAACZ,GAAG,IAAIY,SAAS,KAAKnB,MAAM,CAACmB,SAAS,EAAE;IACpFnB,MAAM,CAACiC,iBAAiB,CAAChC,UAAU,CAAC;IACpC;IACA,IAAIN,MAAM,CAACuC,UAAU,EAAE;MACrBlC,MAAM,CAACmC,gBAAgB,EAAE;IAC3B;IACAnC,MAAM,CAACoC,mBAAmB,EAAE;IAC5B,IAAIzC,MAAM,CAAC0C,MAAM,KAAK,OAAO,EAAE;MAC7BrC,MAAM,CAACsC,YAAY,CAACnB,SAAS,CAAC;IAChC;IACA,IAAIa,SAAS,KAAK,OAAO,EAAE;MACzBhC,MAAM,CAACuC,eAAe,CAAC3C,YAAY,EAAEoC,SAAS,CAAC;MAC/ChC,MAAM,CAACwC,aAAa,CAAC5C,YAAY,EAAEoC,SAAS,CAAC;IAC/C;IACA,OAAO,KAAK;EACd;EACA,IAAIrC,MAAM,CAAC8C,OAAO,EAAE;IAClB,MAAMC,GAAG,GAAG1C,MAAM,CAAC2C,YAAY,EAAE;IACjC,MAAMC,CAAC,GAAGrC,GAAG,GAAGY,SAAS,GAAG,CAACA,SAAS;IACtC,IAAIzB,KAAK,KAAK,CAAC,EAAE;MACf,MAAMmD,SAAS,GAAG7C,MAAM,CAAC8C,OAAO,IAAI9C,MAAM,CAACL,MAAM,CAACmD,OAAO,CAACrC,OAAO;MACjE,IAAIoC,SAAS,EAAE;QACb7C,MAAM,CAACQ,SAAS,CAACuC,KAAK,CAACC,cAAc,GAAG,MAAM;QAC9ChD,MAAM,CAACiD,iBAAiB,GAAG,IAAI;MACjC;MACA,IAAIJ,SAAS,IAAI,CAAC7C,MAAM,CAACkD,yBAAyB,IAAIlD,MAAM,CAACL,MAAM,CAACwD,YAAY,GAAG,CAAC,EAAE;QACpFnD,MAAM,CAACkD,yBAAyB,GAAG,IAAI;QACvCE,qBAAqB,CAAC,MAAM;UAC1B5C,SAAS,CAACkC,GAAG,GAAG,YAAY,GAAG,WAAW,CAAC,GAAGE,CAAC;QACjD,CAAC,CAAC;MACJ,CAAC,MAAM;QACLpC,SAAS,CAACkC,GAAG,GAAG,YAAY,GAAG,WAAW,CAAC,GAAGE,CAAC;MACjD;MACA,IAAIC,SAAS,EAAE;QACbO,qBAAqB,CAAC,MAAM;UAC1BpD,MAAM,CAACQ,SAAS,CAACuC,KAAK,CAACC,cAAc,GAAG,EAAE;UAC1ChD,MAAM,CAACiD,iBAAiB,GAAG,KAAK;QAClC,CAAC,CAAC;MACJ;IACF,CAAC,MAAM;MACL,IAAI,CAACjD,MAAM,CAACqD,OAAO,CAACC,YAAY,EAAE;QAChClE,oBAAoB,CAAC;UACnBY,MAAM;UACNuD,cAAc,EAAEX,CAAC;UACjBY,IAAI,EAAEd,GAAG,GAAG,MAAM,GAAG;QACvB,CAAC,CAAC;QACF,OAAO,IAAI;MACb;MACAlC,SAAS,CAACiD,QAAQ,CAAC;QACjB,CAACf,GAAG,GAAG,MAAM,GAAG,KAAK,GAAGE,CAAC;QACzBc,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;IACA,OAAO,IAAI;EACb;EACA1D,MAAM,CAAC2D,aAAa,CAACjE,KAAK,CAAC;EAC3BM,MAAM,CAACsC,YAAY,CAACnB,SAAS,CAAC;EAC9BnB,MAAM,CAACiC,iBAAiB,CAAChC,UAAU,CAAC;EACpCD,MAAM,CAACoC,mBAAmB,EAAE;EAC5BpC,MAAM,CAAC8B,IAAI,CAAC,uBAAuB,EAAEpC,KAAK,EAAEG,QAAQ,CAAC;EACrDG,MAAM,CAACuC,eAAe,CAAC3C,YAAY,EAAEoC,SAAS,CAAC;EAC/C,IAAItC,KAAK,KAAK,CAAC,EAAE;IACfM,MAAM,CAACwC,aAAa,CAAC5C,YAAY,EAAEoC,SAAS,CAAC;EAC/C,CAAC,MAAM,IAAI,CAAChC,MAAM,CAACU,SAAS,EAAE;IAC5BV,MAAM,CAACU,SAAS,GAAG,IAAI;IACvB,IAAI,CAACV,MAAM,CAAC4D,6BAA6B,EAAE;MACzC5D,MAAM,CAAC4D,6BAA6B,GAAG,SAASpB,aAAaA,CAACqB,CAAC,EAAE;QAC/D,IAAI,CAAC7D,MAAM,IAAIA,MAAM,CAAC8D,SAAS,EAAE;QACjC,IAAID,CAAC,CAACE,MAAM,KAAK,IAAI,EAAE;QACvB/D,MAAM,CAACQ,SAAS,CAACwD,mBAAmB,CAAC,eAAe,EAAEhE,MAAM,CAAC4D,6BAA6B,CAAC;QAC3F5D,MAAM,CAAC4D,6BAA6B,GAAG,IAAI;QAC3C,OAAO5D,MAAM,CAAC4D,6BAA6B;QAC3C5D,MAAM,CAACwC,aAAa,CAAC5C,YAAY,EAAEoC,SAAS,CAAC;MAC/C,CAAC;IACH;IACAhC,MAAM,CAACQ,SAAS,CAACyD,gBAAgB,CAAC,eAAe,EAAEjE,MAAM,CAAC4D,6BAA6B,CAAC;EAC1F;EACA,OAAO,IAAI;AACb"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}