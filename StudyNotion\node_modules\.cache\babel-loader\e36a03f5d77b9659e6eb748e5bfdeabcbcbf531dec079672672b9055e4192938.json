{"ast": null, "code": "/**\n * @typedef {import('micromark-util-types').Event} Event\n */\n\nimport { subtokenize } from 'micromark-util-subtokenize';\n\n/**\n * @param {Array<Event>} events\n * @returns {Array<Event>}\n */\nexport function postprocess(events) {\n  while (!subtokenize(events)) {\n    // Empty\n  }\n  return events;\n}", "map": {"version": 3, "names": ["subtokenize", "postprocess", "events"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/micromark/dev/lib/postprocess.js"], "sourcesContent": ["/**\n * @typedef {import('micromark-util-types').Event} Event\n */\n\nimport {subtokenize} from 'micromark-util-subtokenize'\n\n/**\n * @param {Array<Event>} events\n * @returns {Array<Event>}\n */\nexport function postprocess(events) {\n  while (!subtokenize(events)) {\n    // Empty\n  }\n\n  return events\n}\n"], "mappings": "AAAA;AACA;AACA;;AAEA,SAAQA,WAAW,QAAO,4BAA4B;;AAEtD;AACA;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAACC,MAAM,EAAE;EAClC,OAAO,CAACF,WAAW,CAACE,MAAM,CAAC,EAAE;IAC3B;EAAA;EAGF,OAAOA,MAAM;AACf"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}