{"ast": null, "code": "import { ACCOUNT_TYPE } from \"../utils/constants\";\nexport const sidebarLinks = [{\n  id: 1,\n  name: \"My Profile\",\n  path: \"/dashboard/my-profile\",\n  icon: \"VscAccount\"\n}, {\n  id: 2,\n  name: \"Dashboard\",\n  path: \"/dashboard/instructor\",\n  type: ACCOUNT_TYPE.INSTRUCTOR,\n  icon: \"VscDashboard\"\n}, {\n  id: 3,\n  name: \"My Courses\",\n  path: \"/dashboard/my-courses\",\n  type: ACCOUNT_TYPE.INSTRUCTOR,\n  icon: \"VscVm\"\n}, {\n  id: 4,\n  name: \"Add Course\",\n  path: \"/dashboard/add-course\",\n  type: ACCOUNT_TYPE.INSTRUCTOR,\n  icon: \"VscAdd\"\n}, {\n  id: 5,\n  name: \"Enrolled Courses\",\n  path: \"/dashboard/enrolled-courses\",\n  type: ACCOUNT_TYPE.STUDENT,\n  icon: \"VscMortarBoard\"\n}, {\n  id: 6,\n  name: \"<PERSON> Cart\",\n  path: \"/dashboard/cart\",\n  type: ACCOUNT_TYPE.STUDENT,\n  icon: \"VscHistory\"\n}];", "map": {"version": 3, "names": ["ACCOUNT_TYPE", "sidebarLinks", "id", "name", "path", "icon", "type", "INSTRUCTOR", "STUDENT"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/src/data/dashboard-links.js"], "sourcesContent": ["import { ACCOUNT_TYPE } from \"../utils/constants\";\r\nexport const sidebarLinks = [\r\n  {\r\n    id: 1,\r\n    name: \"My Profile\",\r\n    path: \"/dashboard/my-profile\",\r\n    icon: \"VscAccount\",\r\n  },\r\n  {\r\n    id: 2,\r\n    name: \"Dashboard\",\r\n    path: \"/dashboard/instructor\",\r\n    type: ACCOUNT_TYPE.INSTRUCTOR,\r\n    icon: \"VscDashboard\",\r\n  },\r\n  {\r\n    id: 3,\r\n    name: \"My Courses\",\r\n    path: \"/dashboard/my-courses\",\r\n    type: ACCOUNT_TYPE.INSTRUCTOR,\r\n    icon: \"VscVm\",\r\n  },\r\n  {\r\n    id: 4,\r\n    name: \"Add Course\",\r\n    path: \"/dashboard/add-course\",\r\n    type: ACCOUNT_TYPE.INSTRUCTOR,\r\n    icon: \"VscAdd\",\r\n  },\r\n  {\r\n    id: 5,\r\n    name: \"Enrolled Courses\",\r\n    path: \"/dashboard/enrolled-courses\",\r\n    type: ACCOUNT_TYPE.STUDENT,\r\n    icon: \"VscMortarBoard\",\r\n  },\r\n  {\r\n    id: 6,\r\n    name: \"<PERSON> <PERSON><PERSON>\",\r\n    path: \"/dashboard/cart\",\r\n    type: ACCOUNT_TYPE.STUDENT,\r\n    icon: \"VscHistory\",\r\n  },\r\n];\r\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,oBAAoB;AACjD,OAAO,MAAMC,YAAY,GAAG,CAC1B;EACEC,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,YAAY;EAClBC,IAAI,EAAE,uBAAuB;EAC7BC,IAAI,EAAE;AACR,CAAC,EACD;EACEH,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,uBAAuB;EAC7BE,IAAI,EAAEN,YAAY,CAACO,UAAU;EAC7BF,IAAI,EAAE;AACR,CAAC,EACD;EACEH,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,YAAY;EAClBC,IAAI,EAAE,uBAAuB;EAC7BE,IAAI,EAAEN,YAAY,CAACO,UAAU;EAC7BF,IAAI,EAAE;AACR,CAAC,EACD;EACEH,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,YAAY;EAClBC,IAAI,EAAE,uBAAuB;EAC7BE,IAAI,EAAEN,YAAY,CAACO,UAAU;EAC7BF,IAAI,EAAE;AACR,CAAC,EACD;EACEH,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,kBAAkB;EACxBC,IAAI,EAAE,6BAA6B;EACnCE,IAAI,EAAEN,YAAY,CAACQ,OAAO;EAC1BH,IAAI,EAAE;AACR,CAAC,EACD;EACEH,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,iBAAiB;EACvBE,IAAI,EAAEN,YAAY,CAACQ,OAAO;EAC1BH,IAAI,EAAE;AACR,CAAC,CACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}