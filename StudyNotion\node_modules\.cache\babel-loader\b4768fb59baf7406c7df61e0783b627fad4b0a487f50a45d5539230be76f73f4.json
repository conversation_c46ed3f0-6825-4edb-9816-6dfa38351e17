{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\");\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nvar _classCallCheck2 = _interopRequireDefault(require(\"@babel/runtime/helpers/classCallCheck\"));\nvar _createClass2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createClass\"));\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"@babel/runtime/helpers/possibleConstructorReturn\"));\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"@babel/runtime/helpers/getPrototypeOf\"));\nvar _assertThisInitialized2 = _interopRequireDefault(require(\"@babel/runtime/helpers/assertThisInitialized\"));\nvar _inherits2 = _interopRequireDefault(require(\"@babel/runtime/helpers/inherits\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _react = _interopRequireWildcard(require(\"react\"));\nvar propTypes = {\n  actions: _propTypes[\"default\"].object,\n  className: _propTypes[\"default\"].string,\n  seconds: _propTypes[\"default\"].oneOf([5, 10, 30])\n};\nvar defaultProps = {\n  seconds: 10\n};\nvar _default = function _default(mode) {\n  var ForwardReplayControl = /*#__PURE__*/\n  function (_Component) {\n    (0, _inherits2[\"default\"])(ForwardReplayControl, _Component);\n    function ForwardReplayControl(props, context) {\n      var _this;\n      (0, _classCallCheck2[\"default\"])(this, ForwardReplayControl);\n      _this = (0, _possibleConstructorReturn2[\"default\"])(this, (0, _getPrototypeOf2[\"default\"])(ForwardReplayControl).call(this, props, context));\n      _this.handleClick = _this.handleClick.bind((0, _assertThisInitialized2[\"default\"])(_this));\n      return _this;\n    }\n    (0, _createClass2[\"default\"])(ForwardReplayControl, [{\n      key: \"handleClick\",\n      value: function handleClick() {\n        var _this$props = this.props,\n          actions = _this$props.actions,\n          seconds = _this$props.seconds; // Depends mode to implement different actions\n\n        if (mode === 'forward') {\n          actions.forward(seconds);\n        } else {\n          actions.replay(seconds);\n        }\n      }\n    }, {\n      key: \"render\",\n      value: function render() {\n        var _this2 = this;\n        var _this$props2 = this.props,\n          seconds = _this$props2.seconds,\n          className = _this$props2.className;\n        var classNames = ['video-react-control', 'video-react-button', 'video-react-icon'];\n        classNames.push(\"video-react-icon-\".concat(mode, \"-\").concat(seconds), \"video-react-\".concat(mode, \"-control\"));\n        if (className) {\n          classNames.push(className);\n        }\n        return _react[\"default\"].createElement(\"button\", {\n          ref: function ref(c) {\n            _this2.button = c;\n          },\n          className: classNames.join(' '),\n          type: \"button\",\n          onClick: this.handleClick\n        }, _react[\"default\"].createElement(\"span\", {\n          className: \"video-react-control-text\"\n        }, \"\".concat(mode, \" \").concat(seconds, \" seconds\")));\n      }\n    }]);\n    return ForwardReplayControl;\n  }(_react.Component);\n  ForwardReplayControl.propTypes = propTypes;\n  ForwardReplayControl.defaultProps = defaultProps;\n  return ForwardReplayControl;\n};\nexports[\"default\"] = _default;", "map": {"version": 3, "names": ["_interopRequireWildcard", "require", "_interopRequireDefault", "Object", "defineProperty", "exports", "value", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_assertThisInitialized2", "_inherits2", "_propTypes", "_react", "propTypes", "actions", "object", "className", "string", "seconds", "oneOf", "defaultProps", "_default", "mode", "ForwardReplayControl", "_Component", "props", "context", "_this", "call", "handleClick", "bind", "key", "_this$props", "forward", "replay", "render", "_this2", "_this$props2", "classNames", "push", "concat", "createElement", "ref", "c", "button", "join", "type", "onClick", "Component"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/video-react/lib/components/control-bar/ForwardReplayControl.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\");\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _classCallCheck2 = _interopRequireDefault(require(\"@babel/runtime/helpers/classCallCheck\"));\n\nvar _createClass2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createClass\"));\n\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"@babel/runtime/helpers/possibleConstructorReturn\"));\n\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"@babel/runtime/helpers/getPrototypeOf\"));\n\nvar _assertThisInitialized2 = _interopRequireDefault(require(\"@babel/runtime/helpers/assertThisInitialized\"));\n\nvar _inherits2 = _interopRequireDefault(require(\"@babel/runtime/helpers/inherits\"));\n\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\n\nvar _react = _interopRequireWildcard(require(\"react\"));\n\nvar propTypes = {\n  actions: _propTypes[\"default\"].object,\n  className: _propTypes[\"default\"].string,\n  seconds: _propTypes[\"default\"].oneOf([5, 10, 30])\n};\nvar defaultProps = {\n  seconds: 10\n};\n\nvar _default = function _default(mode) {\n  var ForwardReplayControl =\n  /*#__PURE__*/\n  function (_Component) {\n    (0, _inherits2[\"default\"])(ForwardReplayControl, _Component);\n\n    function ForwardReplayControl(props, context) {\n      var _this;\n\n      (0, _classCallCheck2[\"default\"])(this, ForwardReplayControl);\n      _this = (0, _possibleConstructorReturn2[\"default\"])(this, (0, _getPrototypeOf2[\"default\"])(ForwardReplayControl).call(this, props, context));\n      _this.handleClick = _this.handleClick.bind((0, _assertThisInitialized2[\"default\"])(_this));\n      return _this;\n    }\n\n    (0, _createClass2[\"default\"])(ForwardReplayControl, [{\n      key: \"handleClick\",\n      value: function handleClick() {\n        var _this$props = this.props,\n            actions = _this$props.actions,\n            seconds = _this$props.seconds; // Depends mode to implement different actions\n\n        if (mode === 'forward') {\n          actions.forward(seconds);\n        } else {\n          actions.replay(seconds);\n        }\n      }\n    }, {\n      key: \"render\",\n      value: function render() {\n        var _this2 = this;\n\n        var _this$props2 = this.props,\n            seconds = _this$props2.seconds,\n            className = _this$props2.className;\n        var classNames = ['video-react-control', 'video-react-button', 'video-react-icon'];\n        classNames.push(\"video-react-icon-\".concat(mode, \"-\").concat(seconds), \"video-react-\".concat(mode, \"-control\"));\n\n        if (className) {\n          classNames.push(className);\n        }\n\n        return _react[\"default\"].createElement(\"button\", {\n          ref: function ref(c) {\n            _this2.button = c;\n          },\n          className: classNames.join(' '),\n          type: \"button\",\n          onClick: this.handleClick\n        }, _react[\"default\"].createElement(\"span\", {\n          className: \"video-react-control-text\"\n        }, \"\".concat(mode, \" \").concat(seconds, \" seconds\")));\n      }\n    }]);\n    return ForwardReplayControl;\n  }(_react.Component);\n\n  ForwardReplayControl.propTypes = propTypes;\n  ForwardReplayControl.defaultProps = defaultProps;\n  return ForwardReplayControl;\n};\n\nexports[\"default\"] = _default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,uBAAuB,GAAGC,OAAO,CAAC,+CAA+C,CAAC;AAEtF,IAAIC,sBAAsB,GAAGD,OAAO,CAAC,8CAA8C,CAAC;AAEpFE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;AAE3B,IAAIE,gBAAgB,GAAGL,sBAAsB,CAACD,OAAO,CAAC,uCAAuC,CAAC,CAAC;AAE/F,IAAIO,aAAa,GAAGN,sBAAsB,CAACD,OAAO,CAAC,oCAAoC,CAAC,CAAC;AAEzF,IAAIQ,2BAA2B,GAAGP,sBAAsB,CAACD,OAAO,CAAC,kDAAkD,CAAC,CAAC;AAErH,IAAIS,gBAAgB,GAAGR,sBAAsB,CAACD,OAAO,CAAC,uCAAuC,CAAC,CAAC;AAE/F,IAAIU,uBAAuB,GAAGT,sBAAsB,CAACD,OAAO,CAAC,8CAA8C,CAAC,CAAC;AAE7G,IAAIW,UAAU,GAAGV,sBAAsB,CAACD,OAAO,CAAC,iCAAiC,CAAC,CAAC;AAEnF,IAAIY,UAAU,GAAGX,sBAAsB,CAACD,OAAO,CAAC,YAAY,CAAC,CAAC;AAE9D,IAAIa,MAAM,GAAGd,uBAAuB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AAEtD,IAAIc,SAAS,GAAG;EACdC,OAAO,EAAEH,UAAU,CAAC,SAAS,CAAC,CAACI,MAAM;EACrCC,SAAS,EAAEL,UAAU,CAAC,SAAS,CAAC,CAACM,MAAM;EACvCC,OAAO,EAAEP,UAAU,CAAC,SAAS,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;AAClD,CAAC;AACD,IAAIC,YAAY,GAAG;EACjBF,OAAO,EAAE;AACX,CAAC;AAED,IAAIG,QAAQ,GAAG,SAASA,QAAQA,CAACC,IAAI,EAAE;EACrC,IAAIC,oBAAoB,GACxB;EACA,UAAUC,UAAU,EAAE;IACpB,CAAC,CAAC,EAAEd,UAAU,CAAC,SAAS,CAAC,EAAEa,oBAAoB,EAAEC,UAAU,CAAC;IAE5D,SAASD,oBAAoBA,CAACE,KAAK,EAAEC,OAAO,EAAE;MAC5C,IAAIC,KAAK;MAET,CAAC,CAAC,EAAEtB,gBAAgB,CAAC,SAAS,CAAC,EAAE,IAAI,EAAEkB,oBAAoB,CAAC;MAC5DI,KAAK,GAAG,CAAC,CAAC,EAAEpB,2BAA2B,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAEC,gBAAgB,CAAC,SAAS,CAAC,EAAEe,oBAAoB,CAAC,CAACK,IAAI,CAAC,IAAI,EAAEH,KAAK,EAAEC,OAAO,CAAC,CAAC;MAC5IC,KAAK,CAACE,WAAW,GAAGF,KAAK,CAACE,WAAW,CAACC,IAAI,CAAC,CAAC,CAAC,EAAErB,uBAAuB,CAAC,SAAS,CAAC,EAAEkB,KAAK,CAAC,CAAC;MAC1F,OAAOA,KAAK;IACd;IAEA,CAAC,CAAC,EAAErB,aAAa,CAAC,SAAS,CAAC,EAAEiB,oBAAoB,EAAE,CAAC;MACnDQ,GAAG,EAAE,aAAa;MAClB3B,KAAK,EAAE,SAASyB,WAAWA,CAAA,EAAG;QAC5B,IAAIG,WAAW,GAAG,IAAI,CAACP,KAAK;UACxBX,OAAO,GAAGkB,WAAW,CAAClB,OAAO;UAC7BI,OAAO,GAAGc,WAAW,CAACd,OAAO,CAAC,CAAC;;QAEnC,IAAII,IAAI,KAAK,SAAS,EAAE;UACtBR,OAAO,CAACmB,OAAO,CAACf,OAAO,CAAC;QAC1B,CAAC,MAAM;UACLJ,OAAO,CAACoB,MAAM,CAAChB,OAAO,CAAC;QACzB;MACF;IACF,CAAC,EAAE;MACDa,GAAG,EAAE,QAAQ;MACb3B,KAAK,EAAE,SAAS+B,MAAMA,CAAA,EAAG;QACvB,IAAIC,MAAM,GAAG,IAAI;QAEjB,IAAIC,YAAY,GAAG,IAAI,CAACZ,KAAK;UACzBP,OAAO,GAAGmB,YAAY,CAACnB,OAAO;UAC9BF,SAAS,GAAGqB,YAAY,CAACrB,SAAS;QACtC,IAAIsB,UAAU,GAAG,CAAC,qBAAqB,EAAE,oBAAoB,EAAE,kBAAkB,CAAC;QAClFA,UAAU,CAACC,IAAI,CAAC,mBAAmB,CAACC,MAAM,CAAClB,IAAI,EAAE,GAAG,CAAC,CAACkB,MAAM,CAACtB,OAAO,CAAC,EAAE,cAAc,CAACsB,MAAM,CAAClB,IAAI,EAAE,UAAU,CAAC,CAAC;QAE/G,IAAIN,SAAS,EAAE;UACbsB,UAAU,CAACC,IAAI,CAACvB,SAAS,CAAC;QAC5B;QAEA,OAAOJ,MAAM,CAAC,SAAS,CAAC,CAAC6B,aAAa,CAAC,QAAQ,EAAE;UAC/CC,GAAG,EAAE,SAASA,GAAGA,CAACC,CAAC,EAAE;YACnBP,MAAM,CAACQ,MAAM,GAAGD,CAAC;UACnB,CAAC;UACD3B,SAAS,EAAEsB,UAAU,CAACO,IAAI,CAAC,GAAG,CAAC;UAC/BC,IAAI,EAAE,QAAQ;UACdC,OAAO,EAAE,IAAI,CAAClB;QAChB,CAAC,EAAEjB,MAAM,CAAC,SAAS,CAAC,CAAC6B,aAAa,CAAC,MAAM,EAAE;UACzCzB,SAAS,EAAE;QACb,CAAC,EAAE,EAAE,CAACwB,MAAM,CAAClB,IAAI,EAAE,GAAG,CAAC,CAACkB,MAAM,CAACtB,OAAO,EAAE,UAAU,CAAC,CAAC,CAAC;MACvD;IACF,CAAC,CAAC,CAAC;IACH,OAAOK,oBAAoB;EAC7B,CAAC,CAACX,MAAM,CAACoC,SAAS,CAAC;EAEnBzB,oBAAoB,CAACV,SAAS,GAAGA,SAAS;EAC1CU,oBAAoB,CAACH,YAAY,GAAGA,YAAY;EAChD,OAAOG,oBAAoB;AAC7B,CAAC;AAEDpB,OAAO,CAAC,SAAS,CAAC,GAAGkB,QAAQ"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}