{"ast": null, "code": "/* eslint no-unused-vars: \"off\" */\nexport default function slideReset() {\n  let speed = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : this.params.speed;\n  let runCallbacks = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n  let internal = arguments.length > 2 ? arguments[2] : undefined;\n  const swiper = this;\n  return swiper.slideTo(swiper.activeIndex, speed, runCallbacks, internal);\n}", "map": {"version": 3, "names": ["slideReset", "speed", "arguments", "length", "undefined", "params", "runCallbacks", "internal", "swiper", "slideTo", "activeIndex"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/core/slide/slideReset.js"], "sourcesContent": ["/* eslint no-unused-vars: \"off\" */\nexport default function slideReset(speed = this.params.speed, runCallbacks = true, internal) {\n  const swiper = this;\n  return swiper.slideTo(swiper.activeIndex, speed, runCallbacks, internal);\n}"], "mappings": "AAAA;AACA,eAAe,SAASA,UAAUA,CAAA,EAA2D;EAAA,IAA1DC,KAAK,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI,CAACG,MAAM,CAACJ,KAAK;EAAA,IAAEK,YAAY,GAAAJ,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;EAAA,IAAEK,QAAQ,GAAAL,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EACzF,MAAMI,MAAM,GAAG,IAAI;EACnB,OAAOA,MAAM,CAACC,OAAO,CAACD,MAAM,CAACE,WAAW,EAAET,KAAK,EAAEK,YAAY,EAAEC,QAAQ,CAAC;AAC1E"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}