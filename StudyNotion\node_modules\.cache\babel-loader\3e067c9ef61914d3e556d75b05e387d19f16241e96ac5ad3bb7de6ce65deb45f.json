{"ast": null, "code": "function _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport React, { useRef, useState, useEffect, forwardRef } from 'react';\nimport SwiperCore from 'swiper';\nimport { getParams } from '../components-shared/get-params.js';\nimport { mountSwiper } from '../components-shared/mount-swiper.js';\nimport { needsScrollbar, needsNavigation, needsPagination, uniqueClasses, extend, wrapperClass } from '../components-shared/utils.js';\nimport { getChangedParams } from '../components-shared/get-changed-params.js';\nimport { getChildren } from './get-children.js';\nimport { updateSwiper } from '../components-shared/update-swiper.js';\nimport { renderVirtual } from './virtual.js';\nimport { updateOnVirtualData } from '../components-shared/update-on-virtual-data.js';\nimport { useIsomorphicLayoutEffect } from './use-isomorphic-layout-effect.js';\nimport { SwiperContext } from './context.js';\nconst Swiper = /*#__PURE__*/forwardRef(function (_temp, externalElRef) {\n  let {\n    className,\n    tag: Tag = 'div',\n    wrapperTag: WrapperTag = 'div',\n    children,\n    onSwiper,\n    ...rest\n  } = _temp === void 0 ? {} : _temp;\n  let eventsAssigned = false;\n  const [containerClasses, setContainerClasses] = useState('swiper');\n  const [virtualData, setVirtualData] = useState(null);\n  const [breakpointChanged, setBreakpointChanged] = useState(false);\n  const initializedRef = useRef(false);\n  const swiperElRef = useRef(null);\n  const swiperRef = useRef(null);\n  const oldPassedParamsRef = useRef(null);\n  const oldSlides = useRef(null);\n  const nextElRef = useRef(null);\n  const prevElRef = useRef(null);\n  const paginationElRef = useRef(null);\n  const scrollbarElRef = useRef(null);\n  const {\n    params: swiperParams,\n    passedParams,\n    rest: restProps,\n    events\n  } = getParams(rest);\n  const {\n    slides,\n    slots\n  } = getChildren(children);\n  const onBeforeBreakpoint = () => {\n    setBreakpointChanged(!breakpointChanged);\n  };\n  Object.assign(swiperParams.on, {\n    _containerClasses(swiper, classes) {\n      setContainerClasses(classes);\n    }\n  });\n  const initSwiper = () => {\n    // init swiper\n    Object.assign(swiperParams.on, events);\n    eventsAssigned = true;\n    const passParams = {\n      ...swiperParams\n    };\n    delete passParams.wrapperClass;\n    swiperRef.current = new SwiperCore(passParams);\n    if (swiperRef.current.virtual && swiperRef.current.params.virtual.enabled) {\n      swiperRef.current.virtual.slides = slides;\n      const extendWith = {\n        cache: false,\n        slides,\n        renderExternal: setVirtualData,\n        renderExternalUpdate: false\n      };\n      extend(swiperRef.current.params.virtual, extendWith);\n      extend(swiperRef.current.originalParams.virtual, extendWith);\n    }\n  };\n  if (!swiperElRef.current) {\n    initSwiper();\n  }\n\n  // Listen for breakpoints change\n  if (swiperRef.current) {\n    swiperRef.current.on('_beforeBreakpoint', onBeforeBreakpoint);\n  }\n  const attachEvents = () => {\n    if (eventsAssigned || !events || !swiperRef.current) return;\n    Object.keys(events).forEach(eventName => {\n      swiperRef.current.on(eventName, events[eventName]);\n    });\n  };\n  const detachEvents = () => {\n    if (!events || !swiperRef.current) return;\n    Object.keys(events).forEach(eventName => {\n      swiperRef.current.off(eventName, events[eventName]);\n    });\n  };\n  useEffect(() => {\n    return () => {\n      if (swiperRef.current) swiperRef.current.off('_beforeBreakpoint', onBeforeBreakpoint);\n    };\n  });\n\n  // set initialized flag\n  useEffect(() => {\n    if (!initializedRef.current && swiperRef.current) {\n      swiperRef.current.emitSlidesClasses();\n      initializedRef.current = true;\n    }\n  });\n\n  // mount swiper\n  useIsomorphicLayoutEffect(() => {\n    if (externalElRef) {\n      externalElRef.current = swiperElRef.current;\n    }\n    if (!swiperElRef.current) return;\n    if (swiperRef.current.destroyed) {\n      initSwiper();\n    }\n    mountSwiper({\n      el: swiperElRef.current,\n      nextEl: nextElRef.current,\n      prevEl: prevElRef.current,\n      paginationEl: paginationElRef.current,\n      scrollbarEl: scrollbarElRef.current,\n      swiper: swiperRef.current\n    }, swiperParams);\n    if (onSwiper) onSwiper(swiperRef.current);\n    // eslint-disable-next-line\n    return () => {\n      if (swiperRef.current && !swiperRef.current.destroyed) {\n        swiperRef.current.destroy(true, false);\n      }\n    };\n  }, []);\n\n  // watch for params change\n  useIsomorphicLayoutEffect(() => {\n    attachEvents();\n    const changedParams = getChangedParams(passedParams, oldPassedParamsRef.current, slides, oldSlides.current, c => c.key);\n    oldPassedParamsRef.current = passedParams;\n    oldSlides.current = slides;\n    if (changedParams.length && swiperRef.current && !swiperRef.current.destroyed) {\n      updateSwiper({\n        swiper: swiperRef.current,\n        slides,\n        passedParams,\n        changedParams,\n        nextEl: nextElRef.current,\n        prevEl: prevElRef.current,\n        scrollbarEl: scrollbarElRef.current,\n        paginationEl: paginationElRef.current\n      });\n    }\n    return () => {\n      detachEvents();\n    };\n  });\n\n  // update on virtual update\n  useIsomorphicLayoutEffect(() => {\n    updateOnVirtualData(swiperRef.current);\n  }, [virtualData]);\n\n  // bypass swiper instance to slides\n  function renderSlides() {\n    if (swiperParams.virtual) {\n      return renderVirtual(swiperRef.current, slides, virtualData);\n    }\n    return slides.map((child, index) => {\n      return /*#__PURE__*/React.cloneElement(child, {\n        swiper: swiperRef.current,\n        swiperSlideIndex: index\n      });\n    });\n  }\n  return /*#__PURE__*/React.createElement(Tag, _extends({\n    ref: swiperElRef,\n    className: uniqueClasses(`${containerClasses}${className ? ` ${className}` : ''}`)\n  }, restProps), /*#__PURE__*/React.createElement(SwiperContext.Provider, {\n    value: swiperRef.current\n  }, slots['container-start'], /*#__PURE__*/React.createElement(WrapperTag, {\n    className: wrapperClass(swiperParams.wrapperClass)\n  }, slots['wrapper-start'], renderSlides(), slots['wrapper-end']), needsNavigation(swiperParams) && /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"div\", {\n    ref: prevElRef,\n    className: \"swiper-button-prev\"\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    ref: nextElRef,\n    className: \"swiper-button-next\"\n  })), needsScrollbar(swiperParams) && /*#__PURE__*/React.createElement(\"div\", {\n    ref: scrollbarElRef,\n    className: \"swiper-scrollbar\"\n  }), needsPagination(swiperParams) && /*#__PURE__*/React.createElement(\"div\", {\n    ref: paginationElRef,\n    className: \"swiper-pagination\"\n  }), slots['container-end']));\n});\nSwiper.displayName = 'Swiper';\nexport { Swiper };", "map": {"version": 3, "names": ["_extends", "Object", "assign", "bind", "target", "i", "arguments", "length", "source", "key", "prototype", "hasOwnProperty", "call", "apply", "React", "useRef", "useState", "useEffect", "forwardRef", "SwiperCore", "getParams", "mountSwiper", "needsScrollbar", "needsNavigation", "needsPagination", "uniqueClasses", "extend", "wrapperClass", "getChangedParams", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "updateSwiper", "renderVirtual", "updateOnVirtualData", "useIsomorphicLayoutEffect", "SwiperContext", "Swiper", "_temp", "externalElRef", "className", "tag", "Tag", "wrapperTag", "WrapperTag", "children", "onSwiper", "rest", "eventsAssigned", "containerClasses", "setContainerClasses", "virtualData", "setVirtualData", "breakpointChanged", "setBreakpointChanged", "initializedRef", "swiperElRef", "swiperRef", "oldPassedParamsRef", "oldSlides", "nextElRef", "prevElRef", "paginationElRef", "scrollbarElRef", "params", "swiperParams", "passedParams", "restProps", "events", "slides", "slots", "onBeforeBreakpoint", "on", "_containerClasses", "swiper", "classes", "initSwiper", "passParams", "current", "virtual", "enabled", "extendWith", "cache", "renderExternal", "renderExternalUpdate", "originalParams", "attachEvents", "keys", "for<PERSON>ach", "eventName", "detachEvents", "off", "emitSlidesClasses", "destroyed", "el", "nextEl", "prevEl", "paginationEl", "scrollbarEl", "destroy", "changedParams", "c", "renderSlides", "map", "child", "index", "cloneElement", "swiperSlideIndex", "createElement", "ref", "Provider", "value", "Fragment", "displayName"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/react/swiper.js"], "sourcesContent": ["function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\nimport React, { useRef, useState, useEffect, forwardRef } from 'react';\nimport SwiperCore from 'swiper';\nimport { getParams } from '../components-shared/get-params.js';\nimport { mountSwiper } from '../components-shared/mount-swiper.js';\nimport { needsScrollbar, needsNavigation, needsPagination, uniqueClasses, extend, wrapperClass } from '../components-shared/utils.js';\nimport { getChangedParams } from '../components-shared/get-changed-params.js';\nimport { getChildren } from './get-children.js';\nimport { updateSwiper } from '../components-shared/update-swiper.js';\nimport { renderVirtual } from './virtual.js';\nimport { updateOnVirtualData } from '../components-shared/update-on-virtual-data.js';\nimport { useIsomorphicLayoutEffect } from './use-isomorphic-layout-effect.js';\nimport { SwiperContext } from './context.js';\nconst Swiper = /*#__PURE__*/forwardRef(function (_temp, externalElRef) {\n  let {\n    className,\n    tag: Tag = 'div',\n    wrapperTag: WrapperTag = 'div',\n    children,\n    onSwiper,\n    ...rest\n  } = _temp === void 0 ? {} : _temp;\n  let eventsAssigned = false;\n  const [containerClasses, setContainerClasses] = useState('swiper');\n  const [virtualData, setVirtualData] = useState(null);\n  const [breakpointChanged, setBreakpointChanged] = useState(false);\n  const initializedRef = useRef(false);\n  const swiperElRef = useRef(null);\n  const swiperRef = useRef(null);\n  const oldPassedParamsRef = useRef(null);\n  const oldSlides = useRef(null);\n  const nextElRef = useRef(null);\n  const prevElRef = useRef(null);\n  const paginationElRef = useRef(null);\n  const scrollbarElRef = useRef(null);\n  const {\n    params: swiperParams,\n    passedParams,\n    rest: restProps,\n    events\n  } = getParams(rest);\n  const {\n    slides,\n    slots\n  } = getChildren(children);\n  const onBeforeBreakpoint = () => {\n    setBreakpointChanged(!breakpointChanged);\n  };\n  Object.assign(swiperParams.on, {\n    _containerClasses(swiper, classes) {\n      setContainerClasses(classes);\n    }\n  });\n  const initSwiper = () => {\n    // init swiper\n    Object.assign(swiperParams.on, events);\n    eventsAssigned = true;\n    const passParams = {\n      ...swiperParams\n    };\n    delete passParams.wrapperClass;\n    swiperRef.current = new SwiperCore(passParams);\n    if (swiperRef.current.virtual && swiperRef.current.params.virtual.enabled) {\n      swiperRef.current.virtual.slides = slides;\n      const extendWith = {\n        cache: false,\n        slides,\n        renderExternal: setVirtualData,\n        renderExternalUpdate: false\n      };\n      extend(swiperRef.current.params.virtual, extendWith);\n      extend(swiperRef.current.originalParams.virtual, extendWith);\n    }\n  };\n  if (!swiperElRef.current) {\n    initSwiper();\n  }\n\n  // Listen for breakpoints change\n  if (swiperRef.current) {\n    swiperRef.current.on('_beforeBreakpoint', onBeforeBreakpoint);\n  }\n  const attachEvents = () => {\n    if (eventsAssigned || !events || !swiperRef.current) return;\n    Object.keys(events).forEach(eventName => {\n      swiperRef.current.on(eventName, events[eventName]);\n    });\n  };\n  const detachEvents = () => {\n    if (!events || !swiperRef.current) return;\n    Object.keys(events).forEach(eventName => {\n      swiperRef.current.off(eventName, events[eventName]);\n    });\n  };\n  useEffect(() => {\n    return () => {\n      if (swiperRef.current) swiperRef.current.off('_beforeBreakpoint', onBeforeBreakpoint);\n    };\n  });\n\n  // set initialized flag\n  useEffect(() => {\n    if (!initializedRef.current && swiperRef.current) {\n      swiperRef.current.emitSlidesClasses();\n      initializedRef.current = true;\n    }\n  });\n\n  // mount swiper\n  useIsomorphicLayoutEffect(() => {\n    if (externalElRef) {\n      externalElRef.current = swiperElRef.current;\n    }\n    if (!swiperElRef.current) return;\n    if (swiperRef.current.destroyed) {\n      initSwiper();\n    }\n    mountSwiper({\n      el: swiperElRef.current,\n      nextEl: nextElRef.current,\n      prevEl: prevElRef.current,\n      paginationEl: paginationElRef.current,\n      scrollbarEl: scrollbarElRef.current,\n      swiper: swiperRef.current\n    }, swiperParams);\n    if (onSwiper) onSwiper(swiperRef.current);\n    // eslint-disable-next-line\n    return () => {\n      if (swiperRef.current && !swiperRef.current.destroyed) {\n        swiperRef.current.destroy(true, false);\n      }\n    };\n  }, []);\n\n  // watch for params change\n  useIsomorphicLayoutEffect(() => {\n    attachEvents();\n    const changedParams = getChangedParams(passedParams, oldPassedParamsRef.current, slides, oldSlides.current, c => c.key);\n    oldPassedParamsRef.current = passedParams;\n    oldSlides.current = slides;\n    if (changedParams.length && swiperRef.current && !swiperRef.current.destroyed) {\n      updateSwiper({\n        swiper: swiperRef.current,\n        slides,\n        passedParams,\n        changedParams,\n        nextEl: nextElRef.current,\n        prevEl: prevElRef.current,\n        scrollbarEl: scrollbarElRef.current,\n        paginationEl: paginationElRef.current\n      });\n    }\n    return () => {\n      detachEvents();\n    };\n  });\n\n  // update on virtual update\n  useIsomorphicLayoutEffect(() => {\n    updateOnVirtualData(swiperRef.current);\n  }, [virtualData]);\n\n  // bypass swiper instance to slides\n  function renderSlides() {\n    if (swiperParams.virtual) {\n      return renderVirtual(swiperRef.current, slides, virtualData);\n    }\n    return slides.map((child, index) => {\n      return /*#__PURE__*/React.cloneElement(child, {\n        swiper: swiperRef.current,\n        swiperSlideIndex: index\n      });\n    });\n  }\n  return /*#__PURE__*/React.createElement(Tag, _extends({\n    ref: swiperElRef,\n    className: uniqueClasses(`${containerClasses}${className ? ` ${className}` : ''}`)\n  }, restProps), /*#__PURE__*/React.createElement(SwiperContext.Provider, {\n    value: swiperRef.current\n  }, slots['container-start'], /*#__PURE__*/React.createElement(WrapperTag, {\n    className: wrapperClass(swiperParams.wrapperClass)\n  }, slots['wrapper-start'], renderSlides(), slots['wrapper-end']), needsNavigation(swiperParams) && /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"div\", {\n    ref: prevElRef,\n    className: \"swiper-button-prev\"\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    ref: nextElRef,\n    className: \"swiper-button-next\"\n  })), needsScrollbar(swiperParams) && /*#__PURE__*/React.createElement(\"div\", {\n    ref: scrollbarElRef,\n    className: \"swiper-scrollbar\"\n  }), needsPagination(swiperParams) && /*#__PURE__*/React.createElement(\"div\", {\n    ref: paginationElRef,\n    className: \"swiper-pagination\"\n  }), slots['container-end']));\n});\nSwiper.displayName = 'Swiper';\nexport { Swiper };"], "mappings": "AAAA,SAASA,QAAQA,CAAA,EAAG;EAAEA,QAAQ,GAAGC,MAAM,CAACC,MAAM,GAAGD,MAAM,CAACC,MAAM,CAACC,IAAI,EAAE,GAAG,UAAUC,MAAM,EAAE;IAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MAAE,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC;MAAE,KAAK,IAAII,GAAG,IAAID,MAAM,EAAE;QAAE,IAAIP,MAAM,CAACS,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,MAAM,EAAEC,GAAG,CAAC,EAAE;UAAEL,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;QAAE;MAAE;IAAE;IAAE,OAAOL,MAAM;EAAE,CAAC;EAAE,OAAOJ,QAAQ,CAACa,KAAK,CAAC,IAAI,EAAEP,SAAS,CAAC;AAAE;AAClV,OAAOQ,KAAK,IAAIC,MAAM,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,QAAQ,OAAO;AACtE,OAAOC,UAAU,MAAM,QAAQ;AAC/B,SAASC,SAAS,QAAQ,oCAAoC;AAC9D,SAASC,WAAW,QAAQ,sCAAsC;AAClE,SAASC,cAAc,EAAEC,eAAe,EAAEC,eAAe,EAAEC,aAAa,EAAEC,MAAM,EAAEC,YAAY,QAAQ,+BAA+B;AACrI,SAASC,gBAAgB,QAAQ,4CAA4C;AAC7E,SAASC,WAAW,QAAQ,mBAAmB;AAC/C,SAASC,YAAY,QAAQ,uCAAuC;AACpE,SAASC,aAAa,QAAQ,cAAc;AAC5C,SAASC,mBAAmB,QAAQ,gDAAgD;AACpF,SAASC,yBAAyB,QAAQ,mCAAmC;AAC7E,SAASC,aAAa,QAAQ,cAAc;AAC5C,MAAMC,MAAM,GAAG,aAAajB,UAAU,CAAC,UAAUkB,KAAK,EAAEC,aAAa,EAAE;EACrE,IAAI;IACFC,SAAS;IACTC,GAAG,EAAEC,GAAG,GAAG,KAAK;IAChBC,UAAU,EAAEC,UAAU,GAAG,KAAK;IAC9BC,QAAQ;IACRC,QAAQ;IACR,GAAGC;EACL,CAAC,GAAGT,KAAK,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,KAAK;EACjC,IAAIU,cAAc,GAAG,KAAK;EAC1B,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhC,QAAQ,CAAC,QAAQ,CAAC;EAClE,MAAM,CAACiC,WAAW,EAAEC,cAAc,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACmC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAMqC,cAAc,GAAGtC,MAAM,CAAC,KAAK,CAAC;EACpC,MAAMuC,WAAW,GAAGvC,MAAM,CAAC,IAAI,CAAC;EAChC,MAAMwC,SAAS,GAAGxC,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMyC,kBAAkB,GAAGzC,MAAM,CAAC,IAAI,CAAC;EACvC,MAAM0C,SAAS,GAAG1C,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAM2C,SAAS,GAAG3C,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAM4C,SAAS,GAAG5C,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAM6C,eAAe,GAAG7C,MAAM,CAAC,IAAI,CAAC;EACpC,MAAM8C,cAAc,GAAG9C,MAAM,CAAC,IAAI,CAAC;EACnC,MAAM;IACJ+C,MAAM,EAAEC,YAAY;IACpBC,YAAY;IACZnB,IAAI,EAAEoB,SAAS;IACfC;EACF,CAAC,GAAG9C,SAAS,CAACyB,IAAI,CAAC;EACnB,MAAM;IACJsB,MAAM;IACNC;EACF,CAAC,GAAGvC,WAAW,CAACc,QAAQ,CAAC;EACzB,MAAM0B,kBAAkB,GAAGA,CAAA,KAAM;IAC/BjB,oBAAoB,CAAC,CAACD,iBAAiB,CAAC;EAC1C,CAAC;EACDlD,MAAM,CAACC,MAAM,CAAC6D,YAAY,CAACO,EAAE,EAAE;IAC7BC,iBAAiBA,CAACC,MAAM,EAAEC,OAAO,EAAE;MACjCzB,mBAAmB,CAACyB,OAAO,CAAC;IAC9B;EACF,CAAC,CAAC;EACF,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB;IACAzE,MAAM,CAACC,MAAM,CAAC6D,YAAY,CAACO,EAAE,EAAEJ,MAAM,CAAC;IACtCpB,cAAc,GAAG,IAAI;IACrB,MAAM6B,UAAU,GAAG;MACjB,GAAGZ;IACL,CAAC;IACD,OAAOY,UAAU,CAAChD,YAAY;IAC9B4B,SAAS,CAACqB,OAAO,GAAG,IAAIzD,UAAU,CAACwD,UAAU,CAAC;IAC9C,IAAIpB,SAAS,CAACqB,OAAO,CAACC,OAAO,IAAItB,SAAS,CAACqB,OAAO,CAACd,MAAM,CAACe,OAAO,CAACC,OAAO,EAAE;MACzEvB,SAAS,CAACqB,OAAO,CAACC,OAAO,CAACV,MAAM,GAAGA,MAAM;MACzC,MAAMY,UAAU,GAAG;QACjBC,KAAK,EAAE,KAAK;QACZb,MAAM;QACNc,cAAc,EAAE/B,cAAc;QAC9BgC,oBAAoB,EAAE;MACxB,CAAC;MACDxD,MAAM,CAAC6B,SAAS,CAACqB,OAAO,CAACd,MAAM,CAACe,OAAO,EAAEE,UAAU,CAAC;MACpDrD,MAAM,CAAC6B,SAAS,CAACqB,OAAO,CAACO,cAAc,CAACN,OAAO,EAAEE,UAAU,CAAC;IAC9D;EACF,CAAC;EACD,IAAI,CAACzB,WAAW,CAACsB,OAAO,EAAE;IACxBF,UAAU,EAAE;EACd;;EAEA;EACA,IAAInB,SAAS,CAACqB,OAAO,EAAE;IACrBrB,SAAS,CAACqB,OAAO,CAACN,EAAE,CAAC,mBAAmB,EAAED,kBAAkB,CAAC;EAC/D;EACA,MAAMe,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAItC,cAAc,IAAI,CAACoB,MAAM,IAAI,CAACX,SAAS,CAACqB,OAAO,EAAE;IACrD3E,MAAM,CAACoF,IAAI,CAACnB,MAAM,CAAC,CAACoB,OAAO,CAACC,SAAS,IAAI;MACvChC,SAAS,CAACqB,OAAO,CAACN,EAAE,CAACiB,SAAS,EAAErB,MAAM,CAACqB,SAAS,CAAC,CAAC;IACpD,CAAC,CAAC;EACJ,CAAC;EACD,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI,CAACtB,MAAM,IAAI,CAACX,SAAS,CAACqB,OAAO,EAAE;IACnC3E,MAAM,CAACoF,IAAI,CAACnB,MAAM,CAAC,CAACoB,OAAO,CAACC,SAAS,IAAI;MACvChC,SAAS,CAACqB,OAAO,CAACa,GAAG,CAACF,SAAS,EAAErB,MAAM,CAACqB,SAAS,CAAC,CAAC;IACrD,CAAC,CAAC;EACJ,CAAC;EACDtE,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX,IAAIsC,SAAS,CAACqB,OAAO,EAAErB,SAAS,CAACqB,OAAO,CAACa,GAAG,CAAC,mBAAmB,EAAEpB,kBAAkB,CAAC;IACvF,CAAC;EACH,CAAC,CAAC;;EAEF;EACApD,SAAS,CAAC,MAAM;IACd,IAAI,CAACoC,cAAc,CAACuB,OAAO,IAAIrB,SAAS,CAACqB,OAAO,EAAE;MAChDrB,SAAS,CAACqB,OAAO,CAACc,iBAAiB,EAAE;MACrCrC,cAAc,CAACuB,OAAO,GAAG,IAAI;IAC/B;EACF,CAAC,CAAC;;EAEF;EACA3C,yBAAyB,CAAC,MAAM;IAC9B,IAAII,aAAa,EAAE;MACjBA,aAAa,CAACuC,OAAO,GAAGtB,WAAW,CAACsB,OAAO;IAC7C;IACA,IAAI,CAACtB,WAAW,CAACsB,OAAO,EAAE;IAC1B,IAAIrB,SAAS,CAACqB,OAAO,CAACe,SAAS,EAAE;MAC/BjB,UAAU,EAAE;IACd;IACArD,WAAW,CAAC;MACVuE,EAAE,EAAEtC,WAAW,CAACsB,OAAO;MACvBiB,MAAM,EAAEnC,SAAS,CAACkB,OAAO;MACzBkB,MAAM,EAAEnC,SAAS,CAACiB,OAAO;MACzBmB,YAAY,EAAEnC,eAAe,CAACgB,OAAO;MACrCoB,WAAW,EAAEnC,cAAc,CAACe,OAAO;MACnCJ,MAAM,EAAEjB,SAAS,CAACqB;IACpB,CAAC,EAAEb,YAAY,CAAC;IAChB,IAAInB,QAAQ,EAAEA,QAAQ,CAACW,SAAS,CAACqB,OAAO,CAAC;IACzC;IACA,OAAO,MAAM;MACX,IAAIrB,SAAS,CAACqB,OAAO,IAAI,CAACrB,SAAS,CAACqB,OAAO,CAACe,SAAS,EAAE;QACrDpC,SAAS,CAACqB,OAAO,CAACqB,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC;MACxC;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAhE,yBAAyB,CAAC,MAAM;IAC9BmD,YAAY,EAAE;IACd,MAAMc,aAAa,GAAGtE,gBAAgB,CAACoC,YAAY,EAAER,kBAAkB,CAACoB,OAAO,EAAET,MAAM,EAAEV,SAAS,CAACmB,OAAO,EAAEuB,CAAC,IAAIA,CAAC,CAAC1F,GAAG,CAAC;IACvH+C,kBAAkB,CAACoB,OAAO,GAAGZ,YAAY;IACzCP,SAAS,CAACmB,OAAO,GAAGT,MAAM;IAC1B,IAAI+B,aAAa,CAAC3F,MAAM,IAAIgD,SAAS,CAACqB,OAAO,IAAI,CAACrB,SAAS,CAACqB,OAAO,CAACe,SAAS,EAAE;MAC7E7D,YAAY,CAAC;QACX0C,MAAM,EAAEjB,SAAS,CAACqB,OAAO;QACzBT,MAAM;QACNH,YAAY;QACZkC,aAAa;QACbL,MAAM,EAAEnC,SAAS,CAACkB,OAAO;QACzBkB,MAAM,EAAEnC,SAAS,CAACiB,OAAO;QACzBoB,WAAW,EAAEnC,cAAc,CAACe,OAAO;QACnCmB,YAAY,EAAEnC,eAAe,CAACgB;MAChC,CAAC,CAAC;IACJ;IACA,OAAO,MAAM;MACXY,YAAY,EAAE;IAChB,CAAC;EACH,CAAC,CAAC;;EAEF;EACAvD,yBAAyB,CAAC,MAAM;IAC9BD,mBAAmB,CAACuB,SAAS,CAACqB,OAAO,CAAC;EACxC,CAAC,EAAE,CAAC3B,WAAW,CAAC,CAAC;;EAEjB;EACA,SAASmD,YAAYA,CAAA,EAAG;IACtB,IAAIrC,YAAY,CAACc,OAAO,EAAE;MACxB,OAAO9C,aAAa,CAACwB,SAAS,CAACqB,OAAO,EAAET,MAAM,EAAElB,WAAW,CAAC;IAC9D;IACA,OAAOkB,MAAM,CAACkC,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,KAAK;MAClC,OAAO,aAAazF,KAAK,CAAC0F,YAAY,CAACF,KAAK,EAAE;QAC5C9B,MAAM,EAAEjB,SAAS,CAACqB,OAAO;QACzB6B,gBAAgB,EAAEF;MACpB,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EACA,OAAO,aAAazF,KAAK,CAAC4F,aAAa,CAAClE,GAAG,EAAExC,QAAQ,CAAC;IACpD2G,GAAG,EAAErD,WAAW;IAChBhB,SAAS,EAAEb,aAAa,CAAE,GAAEsB,gBAAiB,GAAET,SAAS,GAAI,IAAGA,SAAU,EAAC,GAAG,EAAG,EAAC;EACnF,CAAC,EAAE2B,SAAS,CAAC,EAAE,aAAanD,KAAK,CAAC4F,aAAa,CAACxE,aAAa,CAAC0E,QAAQ,EAAE;IACtEC,KAAK,EAAEtD,SAAS,CAACqB;EACnB,CAAC,EAAER,KAAK,CAAC,iBAAiB,CAAC,EAAE,aAAatD,KAAK,CAAC4F,aAAa,CAAChE,UAAU,EAAE;IACxEJ,SAAS,EAAEX,YAAY,CAACoC,YAAY,CAACpC,YAAY;EACnD,CAAC,EAAEyC,KAAK,CAAC,eAAe,CAAC,EAAEgC,YAAY,EAAE,EAAEhC,KAAK,CAAC,aAAa,CAAC,CAAC,EAAE7C,eAAe,CAACwC,YAAY,CAAC,IAAI,aAAajD,KAAK,CAAC4F,aAAa,CAAC5F,KAAK,CAACgG,QAAQ,EAAE,IAAI,EAAE,aAAahG,KAAK,CAAC4F,aAAa,CAAC,KAAK,EAAE;IAChMC,GAAG,EAAEhD,SAAS;IACdrB,SAAS,EAAE;EACb,CAAC,CAAC,EAAE,aAAaxB,KAAK,CAAC4F,aAAa,CAAC,KAAK,EAAE;IAC1CC,GAAG,EAAEjD,SAAS;IACdpB,SAAS,EAAE;EACb,CAAC,CAAC,CAAC,EAAEhB,cAAc,CAACyC,YAAY,CAAC,IAAI,aAAajD,KAAK,CAAC4F,aAAa,CAAC,KAAK,EAAE;IAC3EC,GAAG,EAAE9C,cAAc;IACnBvB,SAAS,EAAE;EACb,CAAC,CAAC,EAAEd,eAAe,CAACuC,YAAY,CAAC,IAAI,aAAajD,KAAK,CAAC4F,aAAa,CAAC,KAAK,EAAE;IAC3EC,GAAG,EAAE/C,eAAe;IACpBtB,SAAS,EAAE;EACb,CAAC,CAAC,EAAE8B,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC;AAC9B,CAAC,CAAC;AACFjC,MAAM,CAAC4E,WAAW,GAAG,QAAQ;AAC7B,SAAS5E,MAAM"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}