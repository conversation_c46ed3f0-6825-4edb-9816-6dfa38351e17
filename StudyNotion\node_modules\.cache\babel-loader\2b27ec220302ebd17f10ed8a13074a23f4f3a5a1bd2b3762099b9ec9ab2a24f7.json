{"ast": null, "code": "export default function unsetGrabCursor() {\n  const swiper = this;\n  if (swiper.params.watchOverflow && swiper.isLocked || swiper.params.cssMode) {\n    return;\n  }\n  if (swiper.isElement) {\n    swiper.__preventObserver__ = true;\n  }\n  swiper[swiper.params.touchEventsTarget === 'container' ? 'el' : 'wrapperEl'].style.cursor = '';\n  if (swiper.isElement) {\n    requestAnimationFrame(() => {\n      swiper.__preventObserver__ = false;\n    });\n  }\n}", "map": {"version": 3, "names": ["unsetGrabCursor", "swiper", "params", "watchOverflow", "isLocked", "cssMode", "isElement", "__preventObserver__", "touchEventsTarget", "style", "cursor", "requestAnimationFrame"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/core/grab-cursor/unsetGrabCursor.js"], "sourcesContent": ["export default function unsetGrabCursor() {\n  const swiper = this;\n  if (swiper.params.watchOverflow && swiper.isLocked || swiper.params.cssMode) {\n    return;\n  }\n  if (swiper.isElement) {\n    swiper.__preventObserver__ = true;\n  }\n  swiper[swiper.params.touchEventsTarget === 'container' ? 'el' : 'wrapperEl'].style.cursor = '';\n  if (swiper.isElement) {\n    requestAnimationFrame(() => {\n      swiper.__preventObserver__ = false;\n    });\n  }\n}"], "mappings": "AAAA,eAAe,SAASA,eAAeA,CAAA,EAAG;EACxC,MAAMC,MAAM,GAAG,IAAI;EACnB,IAAIA,MAAM,CAACC,MAAM,CAACC,aAAa,IAAIF,MAAM,CAACG,QAAQ,IAAIH,MAAM,CAACC,MAAM,CAACG,OAAO,EAAE;IAC3E;EACF;EACA,IAAIJ,MAAM,CAACK,SAAS,EAAE;IACpBL,MAAM,CAACM,mBAAmB,GAAG,IAAI;EACnC;EACAN,MAAM,CAACA,MAAM,CAACC,MAAM,CAACM,iBAAiB,KAAK,WAAW,GAAG,IAAI,GAAG,WAAW,CAAC,CAACC,KAAK,CAACC,MAAM,GAAG,EAAE;EAC9F,IAAIT,MAAM,CAACK,SAAS,EAAE;IACpBK,qBAAqB,CAAC,MAAM;MAC1BV,MAAM,CAACM,mBAAmB,GAAG,KAAK;IACpC,CAAC,CAAC;EACJ;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}