{"ast": null, "code": "/**\n * @typedef {import('hast').Element} HastElement\n * @typedef {import('hast').Text} HastText\n * @typedef {import('mdast').Text} MdastText\n * @typedef {import('../state.js').State} State\n */\n\nimport { trimLines } from 'trim-lines';\n\n/**\n * Turn an mdast `text` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdastText} node\n *   mdast node.\n * @returns {HastText | HastElement}\n *   hast node.\n */\nexport function text(state, node) {\n  /** @type {HastText} */\n  const result = {\n    type: 'text',\n    value: trimLines(String(node.value))\n  };\n  state.patch(node, result);\n  return state.applyData(node, result);\n}", "map": {"version": 3, "names": ["trimLines", "text", "state", "node", "result", "type", "value", "String", "patch", "applyData"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/mdast-util-to-hast/lib/handlers/text.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Element} HastElement\n * @typedef {import('hast').Text} HastText\n * @typedef {import('mdast').Text} MdastText\n * @typedef {import('../state.js').State} State\n */\n\nimport {trimLines} from 'trim-lines'\n\n/**\n * Turn an mdast `text` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdastText} node\n *   mdast node.\n * @returns {HastText | HastElement}\n *   hast node.\n */\nexport function text(state, node) {\n  /** @type {HastText} */\n  const result = {type: 'text', value: trimLines(String(node.value))}\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,SAAS,QAAO,YAAY;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,IAAIA,CAACC,KAAK,EAAEC,IAAI,EAAE;EAChC;EACA,MAAMC,MAAM,GAAG;IAACC,IAAI,EAAE,MAAM;IAAEC,KAAK,EAAEN,SAAS,CAACO,MAAM,CAACJ,IAAI,CAACG,KAAK,CAAC;EAAC,CAAC;EACnEJ,KAAK,CAACM,KAAK,CAACL,IAAI,EAAEC,MAAM,CAAC;EACzB,OAAOF,KAAK,CAACO,SAAS,CAACN,IAAI,EAAEC,MAAM,CAAC;AACtC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}