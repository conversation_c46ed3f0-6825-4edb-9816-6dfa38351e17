{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nvar _defineProperty2 = _interopRequireDefault(require(\"@babel/runtime/helpers/defineProperty\"));\nvar _toConsumableArray2 = _interopRequireDefault(require(\"@babel/runtime/helpers/toConsumableArray\"));\nvar _classCallCheck2 = _interopRequireDefault(require(\"@babel/runtime/helpers/classCallCheck\"));\nvar _createClass2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createClass\"));\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"@babel/runtime/helpers/possibleConstructorReturn\"));\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"@babel/runtime/helpers/getPrototypeOf\"));\nvar _assertThisInitialized2 = _interopRequireDefault(require(\"@babel/runtime/helpers/assertThisInitialized\"));\nvar _inherits2 = _interopRequireDefault(require(\"@babel/runtime/helpers/inherits\"));\nvar _react = require(\"react\");\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _dom = require(\"../utils/dom\");\nvar propTypes = {\n  clickable: _propTypes[\"default\"].bool,\n  dblclickable: _propTypes[\"default\"].bool,\n  manager: _propTypes[\"default\"].object,\n  actions: _propTypes[\"default\"].object,\n  player: _propTypes[\"default\"].object,\n  shortcuts: _propTypes[\"default\"].array\n};\nvar defaultProps = {\n  clickable: true,\n  dblclickable: true\n};\nvar Shortcut = /*#__PURE__*/\nfunction (_Component) {\n  (0, _inherits2[\"default\"])(Shortcut, _Component);\n  function Shortcut(props, context) {\n    var _this;\n    (0, _classCallCheck2[\"default\"])(this, Shortcut);\n    _this = (0, _possibleConstructorReturn2[\"default\"])(this, (0, _getPrototypeOf2[\"default\"])(Shortcut).call(this, props, context));\n    _this.defaultShortcuts = [{\n      keyCode: 32,\n      // spacebar\n      handle: _this.togglePlay\n    }, {\n      keyCode: 75,\n      // k\n      handle: _this.togglePlay\n    }, {\n      keyCode: 70,\n      // f\n      handle: _this.toggleFullscreen\n    }, {\n      keyCode: 37,\n      // Left arrow\n      handle: function handle(player, actions) {\n        if (!player.hasStarted) {\n          return;\n        }\n        actions.replay(5, {\n          action: 'replay-5',\n          source: 'shortcut'\n        }); // Go back 5 seconds\n      }\n    }, {\n      keyCode: 74,\n      // j\n      handle: function handle(player, actions) {\n        if (!player.hasStarted) {\n          return;\n        }\n        actions.replay(10, {\n          action: 'replay-10',\n          source: 'shortcut'\n        }); // Go back 10 seconds\n      }\n    }, {\n      keyCode: 39,\n      // Right arrow\n      handle: function handle(player, actions) {\n        if (!player.hasStarted) {\n          return;\n        }\n        actions.forward(5, {\n          action: 'forward-5',\n          source: 'shortcut'\n        }); // Go forward 5 seconds\n      }\n    }, {\n      keyCode: 76,\n      // l\n      handle: function handle(player, actions) {\n        if (!player.hasStarted) {\n          return;\n        }\n        actions.forward(10, {\n          action: 'forward-10',\n          source: 'shortcut'\n        }); // Go forward 10 seconds\n      }\n    }, {\n      keyCode: 36,\n      // Home\n      handle: function handle(player, actions) {\n        if (!player.hasStarted) {\n          return;\n        }\n        actions.seek(0); // Go to beginning of video\n      }\n    }, {\n      keyCode: 35,\n      // End\n      handle: function handle(player, actions) {\n        if (!player.hasStarted) {\n          return;\n        } // Go to end of video\n\n        actions.seek(player.duration);\n      }\n    }, {\n      keyCode: 38,\n      // Up arrow\n      handle: function handle(player, actions) {\n        // Increase volume 5%\n        var v = player.volume + 0.05;\n        if (v > 1) {\n          v = 1;\n        }\n        actions.changeVolume(v, {\n          action: 'volume-up',\n          source: 'shortcut'\n        });\n      }\n    }, {\n      keyCode: 40,\n      // Down arrow\n      handle: function handle(player, actions) {\n        // Decrease volume 5%\n        var v = player.volume - 0.05;\n        if (v < 0) {\n          v = 0;\n        }\n        var action = v > 0 ? 'volume-down' : 'volume-off';\n        actions.changeVolume(v, {\n          action: action,\n          source: 'shortcut'\n        });\n      }\n    }, {\n      keyCode: 190,\n      // Shift + >\n      shift: true,\n      handle: function handle(player, actions) {\n        // Increase speed\n        var playbackRate = player.playbackRate;\n        if (playbackRate >= 1.5) {\n          playbackRate = 2;\n        } else if (playbackRate >= 1.25) {\n          playbackRate = 1.5;\n        } else if (playbackRate >= 1.0) {\n          playbackRate = 1.25;\n        } else if (playbackRate >= 0.5) {\n          playbackRate = 1.0;\n        } else if (playbackRate >= 0.25) {\n          playbackRate = 0.5;\n        } else if (playbackRate >= 0) {\n          playbackRate = 0.25;\n        }\n        actions.changeRate(playbackRate, {\n          action: 'fast-forward',\n          source: 'shortcut'\n        });\n      }\n    }, {\n      keyCode: 188,\n      // Shift + <\n      shift: true,\n      handle: function handle(player, actions) {\n        // Decrease speed\n        var playbackRate = player.playbackRate;\n        if (playbackRate <= 0.5) {\n          playbackRate = 0.25;\n        } else if (playbackRate <= 1.0) {\n          playbackRate = 0.5;\n        } else if (playbackRate <= 1.25) {\n          playbackRate = 1.0;\n        } else if (playbackRate <= 1.5) {\n          playbackRate = 1.25;\n        } else if (playbackRate <= 2) {\n          playbackRate = 1.5;\n        }\n        actions.changeRate(playbackRate, {\n          action: 'fast-rewind',\n          source: 'shortcut'\n        });\n      }\n    }];\n    _this.shortcuts = (0, _toConsumableArray2[\"default\"])(_this.defaultShortcuts);\n    _this.mergeShortcuts = _this.mergeShortcuts.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleKeyPress = _this.handleKeyPress.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleClick = _this.handleClick.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleDoubleClick = _this.handleDoubleClick.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    return _this;\n  }\n  (0, _createClass2[\"default\"])(Shortcut, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      this.mergeShortcuts();\n      document.addEventListener('keydown', this.handleKeyPress);\n      document.addEventListener('click', this.handleClick);\n      document.addEventListener('dblclick', this.handleDoubleClick);\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(prevProps) {\n      if (prevProps.shortcuts !== this.props.shortcuts) {\n        this.mergeShortcuts();\n      }\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      document.removeEventListener('keydown', this.handleKeyPress);\n      document.removeEventListener('click', this.handleClick);\n      document.removeEventListener('dblclick', this.handleDoubleClick);\n    } // merge the shortcuts from props\n  }, {\n    key: \"mergeShortcuts\",\n    value: function mergeShortcuts() {\n      var getShortcutKey = function getShortcutKey(_ref) {\n        var _ref$keyCode = _ref.keyCode,\n          keyCode = _ref$keyCode === void 0 ? 0 : _ref$keyCode,\n          _ref$ctrl = _ref.ctrl,\n          ctrl = _ref$ctrl === void 0 ? false : _ref$ctrl,\n          _ref$shift = _ref.shift,\n          shift = _ref$shift === void 0 ? false : _ref$shift,\n          _ref$alt = _ref.alt,\n          alt = _ref$alt === void 0 ? false : _ref$alt;\n        return \"\".concat(keyCode, \":\").concat(ctrl, \":\").concat(shift, \":\").concat(alt);\n      };\n      var defaultShortcuts = this.defaultShortcuts.reduce(function (shortcuts, shortcut) {\n        return Object.assign(shortcuts, (0, _defineProperty2[\"default\"])({}, getShortcutKey(shortcut), shortcut));\n      }, {});\n      var mergedShortcuts = (this.props.shortcuts || []).reduce(function (shortcuts, shortcut) {\n        var keyCode = shortcut.keyCode,\n          handle = shortcut.handle;\n        if (keyCode && typeof handle === 'function') {\n          return Object.assign(shortcuts, (0, _defineProperty2[\"default\"])({}, getShortcutKey(shortcut), shortcut));\n        }\n        return shortcuts;\n      }, defaultShortcuts);\n      var gradeShortcut = function gradeShortcut(s) {\n        var score = 0;\n        var ps = ['ctrl', 'shift', 'alt'];\n        ps.forEach(function (key) {\n          if (s[key]) {\n            score++;\n          }\n        });\n        return score;\n      };\n      this.shortcuts = Object.keys(mergedShortcuts).map(function (key) {\n        return mergedShortcuts[key];\n      }).sort(function (a, b) {\n        return gradeShortcut(b) - gradeShortcut(a);\n      });\n    }\n  }, {\n    key: \"togglePlay\",\n    value: function togglePlay(player, actions) {\n      if (player.paused) {\n        actions.play({\n          action: 'play',\n          source: 'shortcut'\n        });\n      } else {\n        actions.pause({\n          action: 'pause',\n          source: 'shortcut'\n        });\n      }\n    }\n  }, {\n    key: \"toggleFullscreen\",\n    value: function toggleFullscreen(player, actions) {\n      actions.toggleFullscreen(player);\n    }\n  }, {\n    key: \"handleKeyPress\",\n    value: function handleKeyPress(e) {\n      var _this$props = this.props,\n        player = _this$props.player,\n        actions = _this$props.actions;\n      if (!player.isActive) {\n        return;\n      }\n      if (document.activeElement && ((0, _dom.hasClass)(document.activeElement, 'video-react-control') || (0, _dom.hasClass)(document.activeElement, 'video-react-menu-button-active') // || hasClass(document.activeElement, 'video-react-slider')\n      || (0, _dom.hasClass)(document.activeElement, 'video-react-big-play-button'))) {\n        return;\n      }\n      var keyCode = e.keyCode || e.which;\n      var ctrl = e.ctrlKey || e.metaKey;\n      var shift = e.shiftKey;\n      var alt = e.altKey;\n      var shortcut = this.shortcuts.filter(function (s) {\n        if (!s.keyCode || s.keyCode - keyCode !== 0) {\n          return false;\n        }\n        if (s.ctrl !== undefined && s.ctrl !== ctrl || s.shift !== undefined && s.shift !== shift || s.alt !== undefined && s.alt !== alt) {\n          return false;\n        }\n        return true;\n      })[0];\n      if (shortcut) {\n        shortcut.handle(player, actions);\n        e.preventDefault();\n      }\n    } // only if player is active and player is ready\n  }, {\n    key: \"canBeClicked\",\n    value: function canBeClicked(player, e) {\n      if (!player.isActive || e.target.nodeName !== 'VIDEO' || player.readyState !== 4) {\n        return false;\n      }\n      return true;\n    }\n  }, {\n    key: \"handleClick\",\n    value: function handleClick(e) {\n      var _this$props2 = this.props,\n        player = _this$props2.player,\n        actions = _this$props2.actions,\n        clickable = _this$props2.clickable;\n      if (!this.canBeClicked(player, e) || !clickable) {\n        return;\n      }\n      this.togglePlay(player, actions); // e.preventDefault();\n    }\n  }, {\n    key: \"handleDoubleClick\",\n    value: function handleDoubleClick(e) {\n      var _this$props3 = this.props,\n        player = _this$props3.player,\n        actions = _this$props3.actions,\n        dblclickable = _this$props3.dblclickable;\n      if (!this.canBeClicked(player, e) || !dblclickable) {\n        return;\n      }\n      this.toggleFullscreen(player, actions); // e.preventDefault();\n    } // this component dose not render anything\n    // it's just for the key down event\n  }, {\n    key: \"render\",\n    value: function render() {\n      return null;\n    }\n  }]);\n  return Shortcut;\n}(_react.Component);\nexports[\"default\"] = Shortcut;\nShortcut.propTypes = propTypes;\nShortcut.defaultProps = defaultProps;\nShortcut.displayName = 'Shortcut';", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "_defineProperty2", "_toConsumableArray2", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_assertThisInitialized2", "_inherits2", "_react", "_propTypes", "_dom", "propTypes", "clickable", "bool", "dblclickable", "manager", "object", "actions", "player", "shortcuts", "array", "defaultProps", "Shortcut", "_Component", "props", "context", "_this", "call", "defaultShortcuts", "keyCode", "handle", "togglePlay", "toggleFullscreen", "hasStarted", "replay", "action", "source", "forward", "seek", "duration", "v", "volume", "changeVolume", "shift", "playbackRate", "changeRate", "mergeShortcuts", "bind", "handleKeyPress", "handleClick", "handleDoubleClick", "key", "componentDidMount", "document", "addEventListener", "componentDidUpdate", "prevProps", "componentWillUnmount", "removeEventListener", "getShortcutKey", "_ref", "_ref$keyCode", "_ref$ctrl", "ctrl", "_ref$shift", "_ref$alt", "alt", "concat", "reduce", "shortcut", "assign", "mergedShortcuts", "gradeShortcut", "s", "score", "ps", "for<PERSON>ach", "keys", "map", "sort", "a", "b", "paused", "play", "pause", "e", "_this$props", "isActive", "activeElement", "hasClass", "which", "ctrl<PERSON>ey", "metaKey", "shift<PERSON>ey", "altKey", "filter", "undefined", "preventDefault", "canBeClicked", "target", "nodeName", "readyState", "_this$props2", "_this$props3", "render", "Component", "displayName"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/video-react/lib/components/Shortcut.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _defineProperty2 = _interopRequireDefault(require(\"@babel/runtime/helpers/defineProperty\"));\n\nvar _toConsumableArray2 = _interopRequireDefault(require(\"@babel/runtime/helpers/toConsumableArray\"));\n\nvar _classCallCheck2 = _interopRequireDefault(require(\"@babel/runtime/helpers/classCallCheck\"));\n\nvar _createClass2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createClass\"));\n\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"@babel/runtime/helpers/possibleConstructorReturn\"));\n\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"@babel/runtime/helpers/getPrototypeOf\"));\n\nvar _assertThisInitialized2 = _interopRequireDefault(require(\"@babel/runtime/helpers/assertThisInitialized\"));\n\nvar _inherits2 = _interopRequireDefault(require(\"@babel/runtime/helpers/inherits\"));\n\nvar _react = require(\"react\");\n\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\n\nvar _dom = require(\"../utils/dom\");\n\nvar propTypes = {\n  clickable: _propTypes[\"default\"].bool,\n  dblclickable: _propTypes[\"default\"].bool,\n  manager: _propTypes[\"default\"].object,\n  actions: _propTypes[\"default\"].object,\n  player: _propTypes[\"default\"].object,\n  shortcuts: _propTypes[\"default\"].array\n};\nvar defaultProps = {\n  clickable: true,\n  dblclickable: true\n};\n\nvar Shortcut =\n/*#__PURE__*/\nfunction (_Component) {\n  (0, _inherits2[\"default\"])(Shortcut, _Component);\n\n  function Shortcut(props, context) {\n    var _this;\n\n    (0, _classCallCheck2[\"default\"])(this, Shortcut);\n    _this = (0, _possibleConstructorReturn2[\"default\"])(this, (0, _getPrototypeOf2[\"default\"])(Shortcut).call(this, props, context));\n    _this.defaultShortcuts = [{\n      keyCode: 32,\n      // spacebar\n      handle: _this.togglePlay\n    }, {\n      keyCode: 75,\n      // k\n      handle: _this.togglePlay\n    }, {\n      keyCode: 70,\n      // f\n      handle: _this.toggleFullscreen\n    }, {\n      keyCode: 37,\n      // Left arrow\n      handle: function handle(player, actions) {\n        if (!player.hasStarted) {\n          return;\n        }\n\n        actions.replay(5, {\n          action: 'replay-5',\n          source: 'shortcut'\n        }); // Go back 5 seconds\n      }\n    }, {\n      keyCode: 74,\n      // j\n      handle: function handle(player, actions) {\n        if (!player.hasStarted) {\n          return;\n        }\n\n        actions.replay(10, {\n          action: 'replay-10',\n          source: 'shortcut'\n        }); // Go back 10 seconds\n      }\n    }, {\n      keyCode: 39,\n      // Right arrow\n      handle: function handle(player, actions) {\n        if (!player.hasStarted) {\n          return;\n        }\n\n        actions.forward(5, {\n          action: 'forward-5',\n          source: 'shortcut'\n        }); // Go forward 5 seconds\n      }\n    }, {\n      keyCode: 76,\n      // l\n      handle: function handle(player, actions) {\n        if (!player.hasStarted) {\n          return;\n        }\n\n        actions.forward(10, {\n          action: 'forward-10',\n          source: 'shortcut'\n        }); // Go forward 10 seconds\n      }\n    }, {\n      keyCode: 36,\n      // Home\n      handle: function handle(player, actions) {\n        if (!player.hasStarted) {\n          return;\n        }\n\n        actions.seek(0); // Go to beginning of video\n      }\n    }, {\n      keyCode: 35,\n      // End\n      handle: function handle(player, actions) {\n        if (!player.hasStarted) {\n          return;\n        } // Go to end of video\n\n\n        actions.seek(player.duration);\n      }\n    }, {\n      keyCode: 38,\n      // Up arrow\n      handle: function handle(player, actions) {\n        // Increase volume 5%\n        var v = player.volume + 0.05;\n\n        if (v > 1) {\n          v = 1;\n        }\n\n        actions.changeVolume(v, {\n          action: 'volume-up',\n          source: 'shortcut'\n        });\n      }\n    }, {\n      keyCode: 40,\n      // Down arrow\n      handle: function handle(player, actions) {\n        // Decrease volume 5%\n        var v = player.volume - 0.05;\n\n        if (v < 0) {\n          v = 0;\n        }\n\n        var action = v > 0 ? 'volume-down' : 'volume-off';\n        actions.changeVolume(v, {\n          action: action,\n          source: 'shortcut'\n        });\n      }\n    }, {\n      keyCode: 190,\n      // Shift + >\n      shift: true,\n      handle: function handle(player, actions) {\n        // Increase speed\n        var playbackRate = player.playbackRate;\n\n        if (playbackRate >= 1.5) {\n          playbackRate = 2;\n        } else if (playbackRate >= 1.25) {\n          playbackRate = 1.5;\n        } else if (playbackRate >= 1.0) {\n          playbackRate = 1.25;\n        } else if (playbackRate >= 0.5) {\n          playbackRate = 1.0;\n        } else if (playbackRate >= 0.25) {\n          playbackRate = 0.5;\n        } else if (playbackRate >= 0) {\n          playbackRate = 0.25;\n        }\n\n        actions.changeRate(playbackRate, {\n          action: 'fast-forward',\n          source: 'shortcut'\n        });\n      }\n    }, {\n      keyCode: 188,\n      // Shift + <\n      shift: true,\n      handle: function handle(player, actions) {\n        // Decrease speed\n        var playbackRate = player.playbackRate;\n\n        if (playbackRate <= 0.5) {\n          playbackRate = 0.25;\n        } else if (playbackRate <= 1.0) {\n          playbackRate = 0.5;\n        } else if (playbackRate <= 1.25) {\n          playbackRate = 1.0;\n        } else if (playbackRate <= 1.5) {\n          playbackRate = 1.25;\n        } else if (playbackRate <= 2) {\n          playbackRate = 1.5;\n        }\n\n        actions.changeRate(playbackRate, {\n          action: 'fast-rewind',\n          source: 'shortcut'\n        });\n      }\n    }];\n    _this.shortcuts = (0, _toConsumableArray2[\"default\"])(_this.defaultShortcuts);\n    _this.mergeShortcuts = _this.mergeShortcuts.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleKeyPress = _this.handleKeyPress.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleClick = _this.handleClick.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleDoubleClick = _this.handleDoubleClick.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    return _this;\n  }\n\n  (0, _createClass2[\"default\"])(Shortcut, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      this.mergeShortcuts();\n      document.addEventListener('keydown', this.handleKeyPress);\n      document.addEventListener('click', this.handleClick);\n      document.addEventListener('dblclick', this.handleDoubleClick);\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(prevProps) {\n      if (prevProps.shortcuts !== this.props.shortcuts) {\n        this.mergeShortcuts();\n      }\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      document.removeEventListener('keydown', this.handleKeyPress);\n      document.removeEventListener('click', this.handleClick);\n      document.removeEventListener('dblclick', this.handleDoubleClick);\n    } // merge the shortcuts from props\n\n  }, {\n    key: \"mergeShortcuts\",\n    value: function mergeShortcuts() {\n      var getShortcutKey = function getShortcutKey(_ref) {\n        var _ref$keyCode = _ref.keyCode,\n            keyCode = _ref$keyCode === void 0 ? 0 : _ref$keyCode,\n            _ref$ctrl = _ref.ctrl,\n            ctrl = _ref$ctrl === void 0 ? false : _ref$ctrl,\n            _ref$shift = _ref.shift,\n            shift = _ref$shift === void 0 ? false : _ref$shift,\n            _ref$alt = _ref.alt,\n            alt = _ref$alt === void 0 ? false : _ref$alt;\n        return \"\".concat(keyCode, \":\").concat(ctrl, \":\").concat(shift, \":\").concat(alt);\n      };\n\n      var defaultShortcuts = this.defaultShortcuts.reduce(function (shortcuts, shortcut) {\n        return Object.assign(shortcuts, (0, _defineProperty2[\"default\"])({}, getShortcutKey(shortcut), shortcut));\n      }, {});\n      var mergedShortcuts = (this.props.shortcuts || []).reduce(function (shortcuts, shortcut) {\n        var keyCode = shortcut.keyCode,\n            handle = shortcut.handle;\n\n        if (keyCode && typeof handle === 'function') {\n          return Object.assign(shortcuts, (0, _defineProperty2[\"default\"])({}, getShortcutKey(shortcut), shortcut));\n        }\n\n        return shortcuts;\n      }, defaultShortcuts);\n\n      var gradeShortcut = function gradeShortcut(s) {\n        var score = 0;\n        var ps = ['ctrl', 'shift', 'alt'];\n        ps.forEach(function (key) {\n          if (s[key]) {\n            score++;\n          }\n        });\n        return score;\n      };\n\n      this.shortcuts = Object.keys(mergedShortcuts).map(function (key) {\n        return mergedShortcuts[key];\n      }).sort(function (a, b) {\n        return gradeShortcut(b) - gradeShortcut(a);\n      });\n    }\n  }, {\n    key: \"togglePlay\",\n    value: function togglePlay(player, actions) {\n      if (player.paused) {\n        actions.play({\n          action: 'play',\n          source: 'shortcut'\n        });\n      } else {\n        actions.pause({\n          action: 'pause',\n          source: 'shortcut'\n        });\n      }\n    }\n  }, {\n    key: \"toggleFullscreen\",\n    value: function toggleFullscreen(player, actions) {\n      actions.toggleFullscreen(player);\n    }\n  }, {\n    key: \"handleKeyPress\",\n    value: function handleKeyPress(e) {\n      var _this$props = this.props,\n          player = _this$props.player,\n          actions = _this$props.actions;\n\n      if (!player.isActive) {\n        return;\n      }\n\n      if (document.activeElement && ((0, _dom.hasClass)(document.activeElement, 'video-react-control') || (0, _dom.hasClass)(document.activeElement, 'video-react-menu-button-active') // || hasClass(document.activeElement, 'video-react-slider')\n      || (0, _dom.hasClass)(document.activeElement, 'video-react-big-play-button'))) {\n        return;\n      }\n\n      var keyCode = e.keyCode || e.which;\n      var ctrl = e.ctrlKey || e.metaKey;\n      var shift = e.shiftKey;\n      var alt = e.altKey;\n      var shortcut = this.shortcuts.filter(function (s) {\n        if (!s.keyCode || s.keyCode - keyCode !== 0) {\n          return false;\n        }\n\n        if (s.ctrl !== undefined && s.ctrl !== ctrl || s.shift !== undefined && s.shift !== shift || s.alt !== undefined && s.alt !== alt) {\n          return false;\n        }\n\n        return true;\n      })[0];\n\n      if (shortcut) {\n        shortcut.handle(player, actions);\n        e.preventDefault();\n      }\n    } // only if player is active and player is ready\n\n  }, {\n    key: \"canBeClicked\",\n    value: function canBeClicked(player, e) {\n      if (!player.isActive || e.target.nodeName !== 'VIDEO' || player.readyState !== 4) {\n        return false;\n      }\n\n      return true;\n    }\n  }, {\n    key: \"handleClick\",\n    value: function handleClick(e) {\n      var _this$props2 = this.props,\n          player = _this$props2.player,\n          actions = _this$props2.actions,\n          clickable = _this$props2.clickable;\n\n      if (!this.canBeClicked(player, e) || !clickable) {\n        return;\n      }\n\n      this.togglePlay(player, actions); // e.preventDefault();\n    }\n  }, {\n    key: \"handleDoubleClick\",\n    value: function handleDoubleClick(e) {\n      var _this$props3 = this.props,\n          player = _this$props3.player,\n          actions = _this$props3.actions,\n          dblclickable = _this$props3.dblclickable;\n\n      if (!this.canBeClicked(player, e) || !dblclickable) {\n        return;\n      }\n\n      this.toggleFullscreen(player, actions); // e.preventDefault();\n    } // this component dose not render anything\n    // it's just for the key down event\n\n  }, {\n    key: \"render\",\n    value: function render() {\n      return null;\n    }\n  }]);\n  return Shortcut;\n}(_react.Component);\n\nexports[\"default\"] = Shortcut;\nShortcut.propTypes = propTypes;\nShortcut.defaultProps = defaultProps;\nShortcut.displayName = 'Shortcut';"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC;AAEpFC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;AAE3B,IAAIE,gBAAgB,GAAGN,sBAAsB,CAACC,OAAO,CAAC,uCAAuC,CAAC,CAAC;AAE/F,IAAIM,mBAAmB,GAAGP,sBAAsB,CAACC,OAAO,CAAC,0CAA0C,CAAC,CAAC;AAErG,IAAIO,gBAAgB,GAAGR,sBAAsB,CAACC,OAAO,CAAC,uCAAuC,CAAC,CAAC;AAE/F,IAAIQ,aAAa,GAAGT,sBAAsB,CAACC,OAAO,CAAC,oCAAoC,CAAC,CAAC;AAEzF,IAAIS,2BAA2B,GAAGV,sBAAsB,CAACC,OAAO,CAAC,kDAAkD,CAAC,CAAC;AAErH,IAAIU,gBAAgB,GAAGX,sBAAsB,CAACC,OAAO,CAAC,uCAAuC,CAAC,CAAC;AAE/F,IAAIW,uBAAuB,GAAGZ,sBAAsB,CAACC,OAAO,CAAC,8CAA8C,CAAC,CAAC;AAE7G,IAAIY,UAAU,GAAGb,sBAAsB,CAACC,OAAO,CAAC,iCAAiC,CAAC,CAAC;AAEnF,IAAIa,MAAM,GAAGb,OAAO,CAAC,OAAO,CAAC;AAE7B,IAAIc,UAAU,GAAGf,sBAAsB,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;AAE9D,IAAIe,IAAI,GAAGf,OAAO,CAAC,cAAc,CAAC;AAElC,IAAIgB,SAAS,GAAG;EACdC,SAAS,EAAEH,UAAU,CAAC,SAAS,CAAC,CAACI,IAAI;EACrCC,YAAY,EAAEL,UAAU,CAAC,SAAS,CAAC,CAACI,IAAI;EACxCE,OAAO,EAAEN,UAAU,CAAC,SAAS,CAAC,CAACO,MAAM;EACrCC,OAAO,EAAER,UAAU,CAAC,SAAS,CAAC,CAACO,MAAM;EACrCE,MAAM,EAAET,UAAU,CAAC,SAAS,CAAC,CAACO,MAAM;EACpCG,SAAS,EAAEV,UAAU,CAAC,SAAS,CAAC,CAACW;AACnC,CAAC;AACD,IAAIC,YAAY,GAAG;EACjBT,SAAS,EAAE,IAAI;EACfE,YAAY,EAAE;AAChB,CAAC;AAED,IAAIQ,QAAQ,GACZ;AACA,UAAUC,UAAU,EAAE;EACpB,CAAC,CAAC,EAAEhB,UAAU,CAAC,SAAS,CAAC,EAAEe,QAAQ,EAAEC,UAAU,CAAC;EAEhD,SAASD,QAAQA,CAACE,KAAK,EAAEC,OAAO,EAAE;IAChC,IAAIC,KAAK;IAET,CAAC,CAAC,EAAExB,gBAAgB,CAAC,SAAS,CAAC,EAAE,IAAI,EAAEoB,QAAQ,CAAC;IAChDI,KAAK,GAAG,CAAC,CAAC,EAAEtB,2BAA2B,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAEC,gBAAgB,CAAC,SAAS,CAAC,EAAEiB,QAAQ,CAAC,CAACK,IAAI,CAAC,IAAI,EAAEH,KAAK,EAAEC,OAAO,CAAC,CAAC;IAChIC,KAAK,CAACE,gBAAgB,GAAG,CAAC;MACxBC,OAAO,EAAE,EAAE;MACX;MACAC,MAAM,EAAEJ,KAAK,CAACK;IAChB,CAAC,EAAE;MACDF,OAAO,EAAE,EAAE;MACX;MACAC,MAAM,EAAEJ,KAAK,CAACK;IAChB,CAAC,EAAE;MACDF,OAAO,EAAE,EAAE;MACX;MACAC,MAAM,EAAEJ,KAAK,CAACM;IAChB,CAAC,EAAE;MACDH,OAAO,EAAE,EAAE;MACX;MACAC,MAAM,EAAE,SAASA,MAAMA,CAACZ,MAAM,EAAED,OAAO,EAAE;QACvC,IAAI,CAACC,MAAM,CAACe,UAAU,EAAE;UACtB;QACF;QAEAhB,OAAO,CAACiB,MAAM,CAAC,CAAC,EAAE;UAChBC,MAAM,EAAE,UAAU;UAClBC,MAAM,EAAE;QACV,CAAC,CAAC,CAAC,CAAC;MACN;IACF,CAAC,EAAE;MACDP,OAAO,EAAE,EAAE;MACX;MACAC,MAAM,EAAE,SAASA,MAAMA,CAACZ,MAAM,EAAED,OAAO,EAAE;QACvC,IAAI,CAACC,MAAM,CAACe,UAAU,EAAE;UACtB;QACF;QAEAhB,OAAO,CAACiB,MAAM,CAAC,EAAE,EAAE;UACjBC,MAAM,EAAE,WAAW;UACnBC,MAAM,EAAE;QACV,CAAC,CAAC,CAAC,CAAC;MACN;IACF,CAAC,EAAE;MACDP,OAAO,EAAE,EAAE;MACX;MACAC,MAAM,EAAE,SAASA,MAAMA,CAACZ,MAAM,EAAED,OAAO,EAAE;QACvC,IAAI,CAACC,MAAM,CAACe,UAAU,EAAE;UACtB;QACF;QAEAhB,OAAO,CAACoB,OAAO,CAAC,CAAC,EAAE;UACjBF,MAAM,EAAE,WAAW;UACnBC,MAAM,EAAE;QACV,CAAC,CAAC,CAAC,CAAC;MACN;IACF,CAAC,EAAE;MACDP,OAAO,EAAE,EAAE;MACX;MACAC,MAAM,EAAE,SAASA,MAAMA,CAACZ,MAAM,EAAED,OAAO,EAAE;QACvC,IAAI,CAACC,MAAM,CAACe,UAAU,EAAE;UACtB;QACF;QAEAhB,OAAO,CAACoB,OAAO,CAAC,EAAE,EAAE;UAClBF,MAAM,EAAE,YAAY;UACpBC,MAAM,EAAE;QACV,CAAC,CAAC,CAAC,CAAC;MACN;IACF,CAAC,EAAE;MACDP,OAAO,EAAE,EAAE;MACX;MACAC,MAAM,EAAE,SAASA,MAAMA,CAACZ,MAAM,EAAED,OAAO,EAAE;QACvC,IAAI,CAACC,MAAM,CAACe,UAAU,EAAE;UACtB;QACF;QAEAhB,OAAO,CAACqB,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB;IACF,CAAC,EAAE;MACDT,OAAO,EAAE,EAAE;MACX;MACAC,MAAM,EAAE,SAASA,MAAMA,CAACZ,MAAM,EAAED,OAAO,EAAE;QACvC,IAAI,CAACC,MAAM,CAACe,UAAU,EAAE;UACtB;QACF,CAAC,CAAC;;QAGFhB,OAAO,CAACqB,IAAI,CAACpB,MAAM,CAACqB,QAAQ,CAAC;MAC/B;IACF,CAAC,EAAE;MACDV,OAAO,EAAE,EAAE;MACX;MACAC,MAAM,EAAE,SAASA,MAAMA,CAACZ,MAAM,EAAED,OAAO,EAAE;QACvC;QACA,IAAIuB,CAAC,GAAGtB,MAAM,CAACuB,MAAM,GAAG,IAAI;QAE5B,IAAID,CAAC,GAAG,CAAC,EAAE;UACTA,CAAC,GAAG,CAAC;QACP;QAEAvB,OAAO,CAACyB,YAAY,CAACF,CAAC,EAAE;UACtBL,MAAM,EAAE,WAAW;UACnBC,MAAM,EAAE;QACV,CAAC,CAAC;MACJ;IACF,CAAC,EAAE;MACDP,OAAO,EAAE,EAAE;MACX;MACAC,MAAM,EAAE,SAASA,MAAMA,CAACZ,MAAM,EAAED,OAAO,EAAE;QACvC;QACA,IAAIuB,CAAC,GAAGtB,MAAM,CAACuB,MAAM,GAAG,IAAI;QAE5B,IAAID,CAAC,GAAG,CAAC,EAAE;UACTA,CAAC,GAAG,CAAC;QACP;QAEA,IAAIL,MAAM,GAAGK,CAAC,GAAG,CAAC,GAAG,aAAa,GAAG,YAAY;QACjDvB,OAAO,CAACyB,YAAY,CAACF,CAAC,EAAE;UACtBL,MAAM,EAAEA,MAAM;UACdC,MAAM,EAAE;QACV,CAAC,CAAC;MACJ;IACF,CAAC,EAAE;MACDP,OAAO,EAAE,GAAG;MACZ;MACAc,KAAK,EAAE,IAAI;MACXb,MAAM,EAAE,SAASA,MAAMA,CAACZ,MAAM,EAAED,OAAO,EAAE;QACvC;QACA,IAAI2B,YAAY,GAAG1B,MAAM,CAAC0B,YAAY;QAEtC,IAAIA,YAAY,IAAI,GAAG,EAAE;UACvBA,YAAY,GAAG,CAAC;QAClB,CAAC,MAAM,IAAIA,YAAY,IAAI,IAAI,EAAE;UAC/BA,YAAY,GAAG,GAAG;QACpB,CAAC,MAAM,IAAIA,YAAY,IAAI,GAAG,EAAE;UAC9BA,YAAY,GAAG,IAAI;QACrB,CAAC,MAAM,IAAIA,YAAY,IAAI,GAAG,EAAE;UAC9BA,YAAY,GAAG,GAAG;QACpB,CAAC,MAAM,IAAIA,YAAY,IAAI,IAAI,EAAE;UAC/BA,YAAY,GAAG,GAAG;QACpB,CAAC,MAAM,IAAIA,YAAY,IAAI,CAAC,EAAE;UAC5BA,YAAY,GAAG,IAAI;QACrB;QAEA3B,OAAO,CAAC4B,UAAU,CAACD,YAAY,EAAE;UAC/BT,MAAM,EAAE,cAAc;UACtBC,MAAM,EAAE;QACV,CAAC,CAAC;MACJ;IACF,CAAC,EAAE;MACDP,OAAO,EAAE,GAAG;MACZ;MACAc,KAAK,EAAE,IAAI;MACXb,MAAM,EAAE,SAASA,MAAMA,CAACZ,MAAM,EAAED,OAAO,EAAE;QACvC;QACA,IAAI2B,YAAY,GAAG1B,MAAM,CAAC0B,YAAY;QAEtC,IAAIA,YAAY,IAAI,GAAG,EAAE;UACvBA,YAAY,GAAG,IAAI;QACrB,CAAC,MAAM,IAAIA,YAAY,IAAI,GAAG,EAAE;UAC9BA,YAAY,GAAG,GAAG;QACpB,CAAC,MAAM,IAAIA,YAAY,IAAI,IAAI,EAAE;UAC/BA,YAAY,GAAG,GAAG;QACpB,CAAC,MAAM,IAAIA,YAAY,IAAI,GAAG,EAAE;UAC9BA,YAAY,GAAG,IAAI;QACrB,CAAC,MAAM,IAAIA,YAAY,IAAI,CAAC,EAAE;UAC5BA,YAAY,GAAG,GAAG;QACpB;QAEA3B,OAAO,CAAC4B,UAAU,CAACD,YAAY,EAAE;UAC/BT,MAAM,EAAE,aAAa;UACrBC,MAAM,EAAE;QACV,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IACFV,KAAK,CAACP,SAAS,GAAG,CAAC,CAAC,EAAElB,mBAAmB,CAAC,SAAS,CAAC,EAAEyB,KAAK,CAACE,gBAAgB,CAAC;IAC7EF,KAAK,CAACoB,cAAc,GAAGpB,KAAK,CAACoB,cAAc,CAACC,IAAI,CAAC,CAAC,CAAC,EAAEzC,uBAAuB,CAAC,SAAS,CAAC,EAAEoB,KAAK,CAAC,CAAC;IAChGA,KAAK,CAACsB,cAAc,GAAGtB,KAAK,CAACsB,cAAc,CAACD,IAAI,CAAC,CAAC,CAAC,EAAEzC,uBAAuB,CAAC,SAAS,CAAC,EAAEoB,KAAK,CAAC,CAAC;IAChGA,KAAK,CAACuB,WAAW,GAAGvB,KAAK,CAACuB,WAAW,CAACF,IAAI,CAAC,CAAC,CAAC,EAAEzC,uBAAuB,CAAC,SAAS,CAAC,EAAEoB,KAAK,CAAC,CAAC;IAC1FA,KAAK,CAACwB,iBAAiB,GAAGxB,KAAK,CAACwB,iBAAiB,CAACH,IAAI,CAAC,CAAC,CAAC,EAAEzC,uBAAuB,CAAC,SAAS,CAAC,EAAEoB,KAAK,CAAC,CAAC;IACtG,OAAOA,KAAK;EACd;EAEA,CAAC,CAAC,EAAEvB,aAAa,CAAC,SAAS,CAAC,EAAEmB,QAAQ,EAAE,CAAC;IACvC6B,GAAG,EAAE,mBAAmB;IACxBpD,KAAK,EAAE,SAASqD,iBAAiBA,CAAA,EAAG;MAClC,IAAI,CAACN,cAAc,EAAE;MACrBO,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAACN,cAAc,CAAC;MACzDK,QAAQ,CAACC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACL,WAAW,CAAC;MACpDI,QAAQ,CAACC,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAACJ,iBAAiB,CAAC;IAC/D;EACF,CAAC,EAAE;IACDC,GAAG,EAAE,oBAAoB;IACzBpD,KAAK,EAAE,SAASwD,kBAAkBA,CAACC,SAAS,EAAE;MAC5C,IAAIA,SAAS,CAACrC,SAAS,KAAK,IAAI,CAACK,KAAK,CAACL,SAAS,EAAE;QAChD,IAAI,CAAC2B,cAAc,EAAE;MACvB;IACF;EACF,CAAC,EAAE;IACDK,GAAG,EAAE,sBAAsB;IAC3BpD,KAAK,EAAE,SAAS0D,oBAAoBA,CAAA,EAAG;MACrCJ,QAAQ,CAACK,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAACV,cAAc,CAAC;MAC5DK,QAAQ,CAACK,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACT,WAAW,CAAC;MACvDI,QAAQ,CAACK,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAACR,iBAAiB,CAAC;IAClE,CAAC,CAAC;EAEJ,CAAC,EAAE;IACDC,GAAG,EAAE,gBAAgB;IACrBpD,KAAK,EAAE,SAAS+C,cAAcA,CAAA,EAAG;MAC/B,IAAIa,cAAc,GAAG,SAASA,cAAcA,CAACC,IAAI,EAAE;QACjD,IAAIC,YAAY,GAAGD,IAAI,CAAC/B,OAAO;UAC3BA,OAAO,GAAGgC,YAAY,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,YAAY;UACpDC,SAAS,GAAGF,IAAI,CAACG,IAAI;UACrBA,IAAI,GAAGD,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,SAAS;UAC/CE,UAAU,GAAGJ,IAAI,CAACjB,KAAK;UACvBA,KAAK,GAAGqB,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,UAAU;UAClDC,QAAQ,GAAGL,IAAI,CAACM,GAAG;UACnBA,GAAG,GAAGD,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,QAAQ;QAChD,OAAO,EAAE,CAACE,MAAM,CAACtC,OAAO,EAAE,GAAG,CAAC,CAACsC,MAAM,CAACJ,IAAI,EAAE,GAAG,CAAC,CAACI,MAAM,CAACxB,KAAK,EAAE,GAAG,CAAC,CAACwB,MAAM,CAACD,GAAG,CAAC;MACjF,CAAC;MAED,IAAItC,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAACwC,MAAM,CAAC,UAAUjD,SAAS,EAAEkD,QAAQ,EAAE;QACjF,OAAOzE,MAAM,CAAC0E,MAAM,CAACnD,SAAS,EAAE,CAAC,CAAC,EAAEnB,gBAAgB,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE2D,cAAc,CAACU,QAAQ,CAAC,EAAEA,QAAQ,CAAC,CAAC;MAC3G,CAAC,EAAE,CAAC,CAAC,CAAC;MACN,IAAIE,eAAe,GAAG,CAAC,IAAI,CAAC/C,KAAK,CAACL,SAAS,IAAI,EAAE,EAAEiD,MAAM,CAAC,UAAUjD,SAAS,EAAEkD,QAAQ,EAAE;QACvF,IAAIxC,OAAO,GAAGwC,QAAQ,CAACxC,OAAO;UAC1BC,MAAM,GAAGuC,QAAQ,CAACvC,MAAM;QAE5B,IAAID,OAAO,IAAI,OAAOC,MAAM,KAAK,UAAU,EAAE;UAC3C,OAAOlC,MAAM,CAAC0E,MAAM,CAACnD,SAAS,EAAE,CAAC,CAAC,EAAEnB,gBAAgB,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE2D,cAAc,CAACU,QAAQ,CAAC,EAAEA,QAAQ,CAAC,CAAC;QAC3G;QAEA,OAAOlD,SAAS;MAClB,CAAC,EAAES,gBAAgB,CAAC;MAEpB,IAAI4C,aAAa,GAAG,SAASA,aAAaA,CAACC,CAAC,EAAE;QAC5C,IAAIC,KAAK,GAAG,CAAC;QACb,IAAIC,EAAE,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC;QACjCA,EAAE,CAACC,OAAO,CAAC,UAAUzB,GAAG,EAAE;UACxB,IAAIsB,CAAC,CAACtB,GAAG,CAAC,EAAE;YACVuB,KAAK,EAAE;UACT;QACF,CAAC,CAAC;QACF,OAAOA,KAAK;MACd,CAAC;MAED,IAAI,CAACvD,SAAS,GAAGvB,MAAM,CAACiF,IAAI,CAACN,eAAe,CAAC,CAACO,GAAG,CAAC,UAAU3B,GAAG,EAAE;QAC/D,OAAOoB,eAAe,CAACpB,GAAG,CAAC;MAC7B,CAAC,CAAC,CAAC4B,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;QACtB,OAAOT,aAAa,CAACS,CAAC,CAAC,GAAGT,aAAa,CAACQ,CAAC,CAAC;MAC5C,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACD7B,GAAG,EAAE,YAAY;IACjBpD,KAAK,EAAE,SAASgC,UAAUA,CAACb,MAAM,EAAED,OAAO,EAAE;MAC1C,IAAIC,MAAM,CAACgE,MAAM,EAAE;QACjBjE,OAAO,CAACkE,IAAI,CAAC;UACXhD,MAAM,EAAE,MAAM;UACdC,MAAM,EAAE;QACV,CAAC,CAAC;MACJ,CAAC,MAAM;QACLnB,OAAO,CAACmE,KAAK,CAAC;UACZjD,MAAM,EAAE,OAAO;UACfC,MAAM,EAAE;QACV,CAAC,CAAC;MACJ;IACF;EACF,CAAC,EAAE;IACDe,GAAG,EAAE,kBAAkB;IACvBpD,KAAK,EAAE,SAASiC,gBAAgBA,CAACd,MAAM,EAAED,OAAO,EAAE;MAChDA,OAAO,CAACe,gBAAgB,CAACd,MAAM,CAAC;IAClC;EACF,CAAC,EAAE;IACDiC,GAAG,EAAE,gBAAgB;IACrBpD,KAAK,EAAE,SAASiD,cAAcA,CAACqC,CAAC,EAAE;MAChC,IAAIC,WAAW,GAAG,IAAI,CAAC9D,KAAK;QACxBN,MAAM,GAAGoE,WAAW,CAACpE,MAAM;QAC3BD,OAAO,GAAGqE,WAAW,CAACrE,OAAO;MAEjC,IAAI,CAACC,MAAM,CAACqE,QAAQ,EAAE;QACpB;MACF;MAEA,IAAIlC,QAAQ,CAACmC,aAAa,KAAK,CAAC,CAAC,EAAE9E,IAAI,CAAC+E,QAAQ,EAAEpC,QAAQ,CAACmC,aAAa,EAAE,qBAAqB,CAAC,IAAI,CAAC,CAAC,EAAE9E,IAAI,CAAC+E,QAAQ,EAAEpC,QAAQ,CAACmC,aAAa,EAAE,gCAAgC,CAAC,CAAC;MAAA,GAC9K,CAAC,CAAC,EAAE9E,IAAI,CAAC+E,QAAQ,EAAEpC,QAAQ,CAACmC,aAAa,EAAE,6BAA6B,CAAC,CAAC,EAAE;QAC7E;MACF;MAEA,IAAI3D,OAAO,GAAGwD,CAAC,CAACxD,OAAO,IAAIwD,CAAC,CAACK,KAAK;MAClC,IAAI3B,IAAI,GAAGsB,CAAC,CAACM,OAAO,IAAIN,CAAC,CAACO,OAAO;MACjC,IAAIjD,KAAK,GAAG0C,CAAC,CAACQ,QAAQ;MACtB,IAAI3B,GAAG,GAAGmB,CAAC,CAACS,MAAM;MAClB,IAAIzB,QAAQ,GAAG,IAAI,CAAClD,SAAS,CAAC4E,MAAM,CAAC,UAAUtB,CAAC,EAAE;QAChD,IAAI,CAACA,CAAC,CAAC5C,OAAO,IAAI4C,CAAC,CAAC5C,OAAO,GAAGA,OAAO,KAAK,CAAC,EAAE;UAC3C,OAAO,KAAK;QACd;QAEA,IAAI4C,CAAC,CAACV,IAAI,KAAKiC,SAAS,IAAIvB,CAAC,CAACV,IAAI,KAAKA,IAAI,IAAIU,CAAC,CAAC9B,KAAK,KAAKqD,SAAS,IAAIvB,CAAC,CAAC9B,KAAK,KAAKA,KAAK,IAAI8B,CAAC,CAACP,GAAG,KAAK8B,SAAS,IAAIvB,CAAC,CAACP,GAAG,KAAKA,GAAG,EAAE;UACjI,OAAO,KAAK;QACd;QAEA,OAAO,IAAI;MACb,CAAC,CAAC,CAAC,CAAC,CAAC;MAEL,IAAIG,QAAQ,EAAE;QACZA,QAAQ,CAACvC,MAAM,CAACZ,MAAM,EAAED,OAAO,CAAC;QAChCoE,CAAC,CAACY,cAAc,EAAE;MACpB;IACF,CAAC,CAAC;EAEJ,CAAC,EAAE;IACD9C,GAAG,EAAE,cAAc;IACnBpD,KAAK,EAAE,SAASmG,YAAYA,CAAChF,MAAM,EAAEmE,CAAC,EAAE;MACtC,IAAI,CAACnE,MAAM,CAACqE,QAAQ,IAAIF,CAAC,CAACc,MAAM,CAACC,QAAQ,KAAK,OAAO,IAAIlF,MAAM,CAACmF,UAAU,KAAK,CAAC,EAAE;QAChF,OAAO,KAAK;MACd;MAEA,OAAO,IAAI;IACb;EACF,CAAC,EAAE;IACDlD,GAAG,EAAE,aAAa;IAClBpD,KAAK,EAAE,SAASkD,WAAWA,CAACoC,CAAC,EAAE;MAC7B,IAAIiB,YAAY,GAAG,IAAI,CAAC9E,KAAK;QACzBN,MAAM,GAAGoF,YAAY,CAACpF,MAAM;QAC5BD,OAAO,GAAGqF,YAAY,CAACrF,OAAO;QAC9BL,SAAS,GAAG0F,YAAY,CAAC1F,SAAS;MAEtC,IAAI,CAAC,IAAI,CAACsF,YAAY,CAAChF,MAAM,EAAEmE,CAAC,CAAC,IAAI,CAACzE,SAAS,EAAE;QAC/C;MACF;MAEA,IAAI,CAACmB,UAAU,CAACb,MAAM,EAAED,OAAO,CAAC,CAAC,CAAC;IACpC;EACF,CAAC,EAAE;IACDkC,GAAG,EAAE,mBAAmB;IACxBpD,KAAK,EAAE,SAASmD,iBAAiBA,CAACmC,CAAC,EAAE;MACnC,IAAIkB,YAAY,GAAG,IAAI,CAAC/E,KAAK;QACzBN,MAAM,GAAGqF,YAAY,CAACrF,MAAM;QAC5BD,OAAO,GAAGsF,YAAY,CAACtF,OAAO;QAC9BH,YAAY,GAAGyF,YAAY,CAACzF,YAAY;MAE5C,IAAI,CAAC,IAAI,CAACoF,YAAY,CAAChF,MAAM,EAAEmE,CAAC,CAAC,IAAI,CAACvE,YAAY,EAAE;QAClD;MACF;MAEA,IAAI,CAACkB,gBAAgB,CAACd,MAAM,EAAED,OAAO,CAAC,CAAC,CAAC;IAC1C,CAAC,CAAC;IACF;EAEF,CAAC,EAAE;IACDkC,GAAG,EAAE,QAAQ;IACbpD,KAAK,EAAE,SAASyG,MAAMA,CAAA,EAAG;MACvB,OAAO,IAAI;IACb;EACF,CAAC,CAAC,CAAC;EACH,OAAOlF,QAAQ;AACjB,CAAC,CAACd,MAAM,CAACiG,SAAS,CAAC;AAEnB3G,OAAO,CAAC,SAAS,CAAC,GAAGwB,QAAQ;AAC7BA,QAAQ,CAACX,SAAS,GAAGA,SAAS;AAC9BW,QAAQ,CAACD,YAAY,GAAGA,YAAY;AACpCC,QAAQ,CAACoF,WAAW,GAAG,UAAU"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}