{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\");\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nvar _classCallCheck2 = _interopRequireDefault(require(\"@babel/runtime/helpers/classCallCheck\"));\nvar _createClass2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createClass\"));\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"@babel/runtime/helpers/possibleConstructorReturn\"));\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"@babel/runtime/helpers/getPrototypeOf\"));\nvar _assertThisInitialized2 = _interopRequireDefault(require(\"@babel/runtime/helpers/assertThisInitialized\"));\nvar _inherits2 = _interopRequireDefault(require(\"@babel/runtime/helpers/inherits\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _react = _interopRequireWildcard(require(\"react\"));\nvar _classnames = _interopRequireDefault(require(\"classnames\"));\nvar _MenuButton = _interopRequireDefault(require(\"../menu/MenuButton\"));\nvar propTypes = {\n  player: _propTypes[\"default\"].object,\n  actions: _propTypes[\"default\"].object,\n  rates: _propTypes[\"default\"].array,\n  className: _propTypes[\"default\"].string\n};\nvar defaultProps = {\n  rates: [2, 1.5, 1.25, 1, 0.5, 0.25]\n};\nvar PlaybackRateMenuButton = /*#__PURE__*/\nfunction (_Component) {\n  (0, _inherits2[\"default\"])(PlaybackRateMenuButton, _Component);\n  function PlaybackRateMenuButton(props, context) {\n    var _this;\n    (0, _classCallCheck2[\"default\"])(this, PlaybackRateMenuButton);\n    _this = (0, _possibleConstructorReturn2[\"default\"])(this, (0, _getPrototypeOf2[\"default\"])(PlaybackRateMenuButton).call(this, props, context));\n    _this.handleSelectItem = _this.handleSelectItem.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    return _this;\n  }\n  (0, _createClass2[\"default\"])(PlaybackRateMenuButton, [{\n    key: \"handleSelectItem\",\n    value: function handleSelectItem(index) {\n      var _this$props = this.props,\n        rates = _this$props.rates,\n        actions = _this$props.actions;\n      if (index >= 0 && index < rates.length) {\n        actions.changeRate(rates[index]);\n      }\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props2 = this.props,\n        rates = _this$props2.rates,\n        player = _this$props2.player;\n      var items = rates.map(function (rate) {\n        return {\n          label: \"\".concat(rate, \"x\"),\n          value: rate\n        };\n      });\n      var selectedIndex = rates.indexOf(player.playbackRate) || 0;\n      return _react[\"default\"].createElement(_MenuButton[\"default\"], {\n        className: (0, _classnames[\"default\"])('video-react-playback-rate', this.props.className),\n        onSelectItem: this.handleSelectItem,\n        items: items,\n        selectedIndex: selectedIndex\n      }, _react[\"default\"].createElement(\"span\", {\n        className: \"video-react-control-text\"\n      }, \"Playback Rate\"), _react[\"default\"].createElement(\"div\", {\n        className: \"video-react-playback-rate-value\"\n      }, \"\".concat(player.playbackRate.toFixed(2), \"x\")));\n    }\n  }]);\n  return PlaybackRateMenuButton;\n}(_react.Component);\nPlaybackRateMenuButton.propTypes = propTypes;\nPlaybackRateMenuButton.defaultProps = defaultProps;\nPlaybackRateMenuButton.displayName = 'PlaybackRateMenuButton';\nvar _default = PlaybackRateMenuButton;\nexports[\"default\"] = _default;", "map": {"version": 3, "names": ["_interopRequireWildcard", "require", "_interopRequireDefault", "Object", "defineProperty", "exports", "value", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_assertThisInitialized2", "_inherits2", "_propTypes", "_react", "_classnames", "_MenuButton", "propTypes", "player", "object", "actions", "rates", "array", "className", "string", "defaultProps", "PlaybackRateMenuButton", "_Component", "props", "context", "_this", "call", "handleSelectItem", "bind", "key", "index", "_this$props", "length", "changeRate", "render", "_this$props2", "items", "map", "rate", "label", "concat", "selectedIndex", "indexOf", "playbackRate", "createElement", "onSelectItem", "toFixed", "Component", "displayName", "_default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/video-react/lib/components/control-bar/PlaybackRateMenuButton.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\");\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _classCallCheck2 = _interopRequireDefault(require(\"@babel/runtime/helpers/classCallCheck\"));\n\nvar _createClass2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createClass\"));\n\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"@babel/runtime/helpers/possibleConstructorReturn\"));\n\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"@babel/runtime/helpers/getPrototypeOf\"));\n\nvar _assertThisInitialized2 = _interopRequireDefault(require(\"@babel/runtime/helpers/assertThisInitialized\"));\n\nvar _inherits2 = _interopRequireDefault(require(\"@babel/runtime/helpers/inherits\"));\n\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\n\nvar _react = _interopRequireWildcard(require(\"react\"));\n\nvar _classnames = _interopRequireDefault(require(\"classnames\"));\n\nvar _MenuButton = _interopRequireDefault(require(\"../menu/MenuButton\"));\n\nvar propTypes = {\n  player: _propTypes[\"default\"].object,\n  actions: _propTypes[\"default\"].object,\n  rates: _propTypes[\"default\"].array,\n  className: _propTypes[\"default\"].string\n};\nvar defaultProps = {\n  rates: [2, 1.5, 1.25, 1, 0.5, 0.25]\n};\n\nvar PlaybackRateMenuButton =\n/*#__PURE__*/\nfunction (_Component) {\n  (0, _inherits2[\"default\"])(PlaybackRateMenuButton, _Component);\n\n  function PlaybackRateMenuButton(props, context) {\n    var _this;\n\n    (0, _classCallCheck2[\"default\"])(this, PlaybackRateMenuButton);\n    _this = (0, _possibleConstructorReturn2[\"default\"])(this, (0, _getPrototypeOf2[\"default\"])(PlaybackRateMenuButton).call(this, props, context));\n    _this.handleSelectItem = _this.handleSelectItem.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    return _this;\n  }\n\n  (0, _createClass2[\"default\"])(PlaybackRateMenuButton, [{\n    key: \"handleSelectItem\",\n    value: function handleSelectItem(index) {\n      var _this$props = this.props,\n          rates = _this$props.rates,\n          actions = _this$props.actions;\n\n      if (index >= 0 && index < rates.length) {\n        actions.changeRate(rates[index]);\n      }\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props2 = this.props,\n          rates = _this$props2.rates,\n          player = _this$props2.player;\n      var items = rates.map(function (rate) {\n        return {\n          label: \"\".concat(rate, \"x\"),\n          value: rate\n        };\n      });\n      var selectedIndex = rates.indexOf(player.playbackRate) || 0;\n      return _react[\"default\"].createElement(_MenuButton[\"default\"], {\n        className: (0, _classnames[\"default\"])('video-react-playback-rate', this.props.className),\n        onSelectItem: this.handleSelectItem,\n        items: items,\n        selectedIndex: selectedIndex\n      }, _react[\"default\"].createElement(\"span\", {\n        className: \"video-react-control-text\"\n      }, \"Playback Rate\"), _react[\"default\"].createElement(\"div\", {\n        className: \"video-react-playback-rate-value\"\n      }, \"\".concat(player.playbackRate.toFixed(2), \"x\")));\n    }\n  }]);\n  return PlaybackRateMenuButton;\n}(_react.Component);\n\nPlaybackRateMenuButton.propTypes = propTypes;\nPlaybackRateMenuButton.defaultProps = defaultProps;\nPlaybackRateMenuButton.displayName = 'PlaybackRateMenuButton';\nvar _default = PlaybackRateMenuButton;\nexports[\"default\"] = _default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,uBAAuB,GAAGC,OAAO,CAAC,+CAA+C,CAAC;AAEtF,IAAIC,sBAAsB,GAAGD,OAAO,CAAC,8CAA8C,CAAC;AAEpFE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;AAE3B,IAAIE,gBAAgB,GAAGL,sBAAsB,CAACD,OAAO,CAAC,uCAAuC,CAAC,CAAC;AAE/F,IAAIO,aAAa,GAAGN,sBAAsB,CAACD,OAAO,CAAC,oCAAoC,CAAC,CAAC;AAEzF,IAAIQ,2BAA2B,GAAGP,sBAAsB,CAACD,OAAO,CAAC,kDAAkD,CAAC,CAAC;AAErH,IAAIS,gBAAgB,GAAGR,sBAAsB,CAACD,OAAO,CAAC,uCAAuC,CAAC,CAAC;AAE/F,IAAIU,uBAAuB,GAAGT,sBAAsB,CAACD,OAAO,CAAC,8CAA8C,CAAC,CAAC;AAE7G,IAAIW,UAAU,GAAGV,sBAAsB,CAACD,OAAO,CAAC,iCAAiC,CAAC,CAAC;AAEnF,IAAIY,UAAU,GAAGX,sBAAsB,CAACD,OAAO,CAAC,YAAY,CAAC,CAAC;AAE9D,IAAIa,MAAM,GAAGd,uBAAuB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AAEtD,IAAIc,WAAW,GAAGb,sBAAsB,CAACD,OAAO,CAAC,YAAY,CAAC,CAAC;AAE/D,IAAIe,WAAW,GAAGd,sBAAsB,CAACD,OAAO,CAAC,oBAAoB,CAAC,CAAC;AAEvE,IAAIgB,SAAS,GAAG;EACdC,MAAM,EAAEL,UAAU,CAAC,SAAS,CAAC,CAACM,MAAM;EACpCC,OAAO,EAAEP,UAAU,CAAC,SAAS,CAAC,CAACM,MAAM;EACrCE,KAAK,EAAER,UAAU,CAAC,SAAS,CAAC,CAACS,KAAK;EAClCC,SAAS,EAAEV,UAAU,CAAC,SAAS,CAAC,CAACW;AACnC,CAAC;AACD,IAAIC,YAAY,GAAG;EACjBJ,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,IAAI;AACpC,CAAC;AAED,IAAIK,sBAAsB,GAC1B;AACA,UAAUC,UAAU,EAAE;EACpB,CAAC,CAAC,EAAEf,UAAU,CAAC,SAAS,CAAC,EAAEc,sBAAsB,EAAEC,UAAU,CAAC;EAE9D,SAASD,sBAAsBA,CAACE,KAAK,EAAEC,OAAO,EAAE;IAC9C,IAAIC,KAAK;IAET,CAAC,CAAC,EAAEvB,gBAAgB,CAAC,SAAS,CAAC,EAAE,IAAI,EAAEmB,sBAAsB,CAAC;IAC9DI,KAAK,GAAG,CAAC,CAAC,EAAErB,2BAA2B,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAEC,gBAAgB,CAAC,SAAS,CAAC,EAAEgB,sBAAsB,CAAC,CAACK,IAAI,CAAC,IAAI,EAAEH,KAAK,EAAEC,OAAO,CAAC,CAAC;IAC9IC,KAAK,CAACE,gBAAgB,GAAGF,KAAK,CAACE,gBAAgB,CAACC,IAAI,CAAC,CAAC,CAAC,EAAEtB,uBAAuB,CAAC,SAAS,CAAC,EAAEmB,KAAK,CAAC,CAAC;IACpG,OAAOA,KAAK;EACd;EAEA,CAAC,CAAC,EAAEtB,aAAa,CAAC,SAAS,CAAC,EAAEkB,sBAAsB,EAAE,CAAC;IACrDQ,GAAG,EAAE,kBAAkB;IACvB5B,KAAK,EAAE,SAAS0B,gBAAgBA,CAACG,KAAK,EAAE;MACtC,IAAIC,WAAW,GAAG,IAAI,CAACR,KAAK;QACxBP,KAAK,GAAGe,WAAW,CAACf,KAAK;QACzBD,OAAO,GAAGgB,WAAW,CAAChB,OAAO;MAEjC,IAAIe,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAGd,KAAK,CAACgB,MAAM,EAAE;QACtCjB,OAAO,CAACkB,UAAU,CAACjB,KAAK,CAACc,KAAK,CAAC,CAAC;MAClC;IACF;EACF,CAAC,EAAE;IACDD,GAAG,EAAE,QAAQ;IACb5B,KAAK,EAAE,SAASiC,MAAMA,CAAA,EAAG;MACvB,IAAIC,YAAY,GAAG,IAAI,CAACZ,KAAK;QACzBP,KAAK,GAAGmB,YAAY,CAACnB,KAAK;QAC1BH,MAAM,GAAGsB,YAAY,CAACtB,MAAM;MAChC,IAAIuB,KAAK,GAAGpB,KAAK,CAACqB,GAAG,CAAC,UAAUC,IAAI,EAAE;QACpC,OAAO;UACLC,KAAK,EAAE,EAAE,CAACC,MAAM,CAACF,IAAI,EAAE,GAAG,CAAC;UAC3BrC,KAAK,EAAEqC;QACT,CAAC;MACH,CAAC,CAAC;MACF,IAAIG,aAAa,GAAGzB,KAAK,CAAC0B,OAAO,CAAC7B,MAAM,CAAC8B,YAAY,CAAC,IAAI,CAAC;MAC3D,OAAOlC,MAAM,CAAC,SAAS,CAAC,CAACmC,aAAa,CAACjC,WAAW,CAAC,SAAS,CAAC,EAAE;QAC7DO,SAAS,EAAE,CAAC,CAAC,EAAER,WAAW,CAAC,SAAS,CAAC,EAAE,2BAA2B,EAAE,IAAI,CAACa,KAAK,CAACL,SAAS,CAAC;QACzF2B,YAAY,EAAE,IAAI,CAAClB,gBAAgB;QACnCS,KAAK,EAAEA,KAAK;QACZK,aAAa,EAAEA;MACjB,CAAC,EAAEhC,MAAM,CAAC,SAAS,CAAC,CAACmC,aAAa,CAAC,MAAM,EAAE;QACzC1B,SAAS,EAAE;MACb,CAAC,EAAE,eAAe,CAAC,EAAET,MAAM,CAAC,SAAS,CAAC,CAACmC,aAAa,CAAC,KAAK,EAAE;QAC1D1B,SAAS,EAAE;MACb,CAAC,EAAE,EAAE,CAACsB,MAAM,CAAC3B,MAAM,CAAC8B,YAAY,CAACG,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IACrD;EACF,CAAC,CAAC,CAAC;EACH,OAAOzB,sBAAsB;AAC/B,CAAC,CAACZ,MAAM,CAACsC,SAAS,CAAC;AAEnB1B,sBAAsB,CAACT,SAAS,GAAGA,SAAS;AAC5CS,sBAAsB,CAACD,YAAY,GAAGA,YAAY;AAClDC,sBAAsB,CAAC2B,WAAW,GAAG,wBAAwB;AAC7D,IAAIC,QAAQ,GAAG5B,sBAAsB;AACrCrB,OAAO,CAAC,SAAS,CAAC,GAAGiD,QAAQ"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}