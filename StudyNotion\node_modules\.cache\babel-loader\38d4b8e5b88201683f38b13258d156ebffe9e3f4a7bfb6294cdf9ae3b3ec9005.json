{"ast": null, "code": "import { create } from './util/create.js';\nexport const xlink = create({\n  space: 'xlink',\n  transform(_, prop) {\n    return 'xlink:' + prop.slice(5).toLowerCase();\n  },\n  properties: {\n    xLinkActuate: null,\n    xLinkArcRole: null,\n    xLinkHref: null,\n    xLinkRole: null,\n    xLinkShow: null,\n    xLinkTitle: null,\n    xLinkType: null\n  }\n});", "map": {"version": 3, "names": ["create", "xlink", "space", "transform", "_", "prop", "slice", "toLowerCase", "properties", "xLinkActuate", "xLinkArcRole", "xLinkHref", "xLinkRole", "xLinkShow", "xLinkTitle", "xLinkType"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/property-information/lib/xlink.js"], "sourcesContent": ["import {create} from './util/create.js'\n\nexport const xlink = create({\n  space: 'xlink',\n  transform(_, prop) {\n    return 'xlink:' + prop.slice(5).toLowerCase()\n  },\n  properties: {\n    xLinkActuate: null,\n    xLinkArcRole: null,\n    xLinkHref: null,\n    xLinkRole: null,\n    xLinkShow: null,\n    xLinkTitle: null,\n    xLinkType: null\n  }\n})\n"], "mappings": "AAAA,SAAQA,MAAM,QAAO,kBAAkB;AAEvC,OAAO,MAAMC,KAAK,GAAGD,MAAM,CAAC;EAC1BE,KAAK,EAAE,OAAO;EACdC,SAASA,CAACC,CAAC,EAAEC,IAAI,EAAE;IACjB,OAAO,QAAQ,GAAGA,IAAI,CAACC,KAAK,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE;EAC/C,CAAC;EACDC,UAAU,EAAE;IACVC,YAAY,EAAE,IAAI;IAClBC,YAAY,EAAE,IAAI;IAClBC,SAAS,EAAE,IAAI;IACfC,SAAS,EAAE,IAAI;IACfC,SAAS,EAAE,IAAI;IACfC,UAAU,EAAE,IAAI;IAChBC,SAAS,EAAE;EACb;AACF,CAAC,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}