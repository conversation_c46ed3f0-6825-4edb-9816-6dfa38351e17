{"ast": null, "code": "import { useEffect, useLayoutEffect } from 'react';\nfunction useIsomorphicLayoutEffect(callback, deps) {\n  // eslint-disable-next-line\n  if (typeof window === 'undefined') return useEffect(callback, deps);\n  return useLayoutEffect(callback, deps);\n}\nexport { useIsomorphicLayoutEffect };", "map": {"version": 3, "names": ["useEffect", "useLayoutEffect", "useIsomorphicLayoutEffect", "callback", "deps", "window"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/react/use-isomorphic-layout-effect.js"], "sourcesContent": ["import { useEffect, useLayoutEffect } from 'react';\nfunction useIsomorphicLayoutEffect(callback, deps) {\n  // eslint-disable-next-line\n  if (typeof window === 'undefined') return useEffect(callback, deps);\n  return useLayoutEffect(callback, deps);\n}\nexport { useIsomorphicLayoutEffect };"], "mappings": "AAAA,SAASA,SAAS,EAAEC,eAAe,QAAQ,OAAO;AAClD,SAASC,yBAAyBA,CAACC,QAAQ,EAAEC,IAAI,EAAE;EACjD;EACA,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE,OAAOL,SAAS,CAACG,QAAQ,EAAEC,IAAI,CAAC;EACnE,OAAOH,eAAe,CAACE,QAAQ,EAAEC,IAAI,CAAC;AACxC;AACA,SAASF,yBAAyB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}