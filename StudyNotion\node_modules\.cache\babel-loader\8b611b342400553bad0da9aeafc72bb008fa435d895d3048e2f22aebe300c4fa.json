{"ast": null, "code": "function checkOverflow() {\n  const swiper = this;\n  const {\n    isLocked: wasLocked,\n    params\n  } = swiper;\n  const {\n    slidesOffsetBefore\n  } = params;\n  if (slidesOffsetBefore) {\n    const lastSlideIndex = swiper.slides.length - 1;\n    const lastSlideRightEdge = swiper.slidesGrid[lastSlideIndex] + swiper.slidesSizesGrid[lastSlideIndex] + slidesOffsetBefore * 2;\n    swiper.isLocked = swiper.size > lastSlideRightEdge;\n  } else {\n    swiper.isLocked = swiper.snapGrid.length === 1;\n  }\n  if (params.allowSlideNext === true) {\n    swiper.allowSlideNext = !swiper.isLocked;\n  }\n  if (params.allowSlidePrev === true) {\n    swiper.allowSlidePrev = !swiper.isLocked;\n  }\n  if (wasLocked && wasLocked !== swiper.isLocked) {\n    swiper.isEnd = false;\n  }\n  if (wasLocked !== swiper.isLocked) {\n    swiper.emit(swiper.isLocked ? 'lock' : 'unlock');\n  }\n}\nexport default {\n  checkOverflow\n};", "map": {"version": 3, "names": ["checkOverflow", "swiper", "isLocked", "wasLocked", "params", "slidesOffsetBefore", "lastSlideIndex", "slides", "length", "lastSlideRightEdge", "slidesGrid", "slidesSizesGrid", "size", "snapGrid", "allowSlideNext", "allowSlidePrev", "isEnd", "emit"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/core/check-overflow/index.js"], "sourcesContent": ["function checkOverflow() {\n  const swiper = this;\n  const {\n    isLocked: wasLocked,\n    params\n  } = swiper;\n  const {\n    slidesOffsetBefore\n  } = params;\n  if (slidesOffsetBefore) {\n    const lastSlideIndex = swiper.slides.length - 1;\n    const lastSlideRightEdge = swiper.slidesGrid[lastSlideIndex] + swiper.slidesSizesGrid[lastSlideIndex] + slidesOffsetBefore * 2;\n    swiper.isLocked = swiper.size > lastSlideRightEdge;\n  } else {\n    swiper.isLocked = swiper.snapGrid.length === 1;\n  }\n  if (params.allowSlideNext === true) {\n    swiper.allowSlideNext = !swiper.isLocked;\n  }\n  if (params.allowSlidePrev === true) {\n    swiper.allowSlidePrev = !swiper.isLocked;\n  }\n  if (wasLocked && wasLocked !== swiper.isLocked) {\n    swiper.isEnd = false;\n  }\n  if (wasLocked !== swiper.isLocked) {\n    swiper.emit(swiper.isLocked ? 'lock' : 'unlock');\n  }\n}\nexport default {\n  checkOverflow\n};"], "mappings": "AAAA,SAASA,aAAaA,CAAA,EAAG;EACvB,MAAMC,MAAM,GAAG,IAAI;EACnB,MAAM;IACJC,QAAQ,EAAEC,SAAS;IACnBC;EACF,CAAC,GAAGH,MAAM;EACV,MAAM;IACJI;EACF,CAAC,GAAGD,MAAM;EACV,IAAIC,kBAAkB,EAAE;IACtB,MAAMC,cAAc,GAAGL,MAAM,CAACM,MAAM,CAACC,MAAM,GAAG,CAAC;IAC/C,MAAMC,kBAAkB,GAAGR,MAAM,CAACS,UAAU,CAACJ,cAAc,CAAC,GAAGL,MAAM,CAACU,eAAe,CAACL,cAAc,CAAC,GAAGD,kBAAkB,GAAG,CAAC;IAC9HJ,MAAM,CAACC,QAAQ,GAAGD,MAAM,CAACW,IAAI,GAAGH,kBAAkB;EACpD,CAAC,MAAM;IACLR,MAAM,CAACC,QAAQ,GAAGD,MAAM,CAACY,QAAQ,CAACL,MAAM,KAAK,CAAC;EAChD;EACA,IAAIJ,MAAM,CAACU,cAAc,KAAK,IAAI,EAAE;IAClCb,MAAM,CAACa,cAAc,GAAG,CAACb,MAAM,CAACC,QAAQ;EAC1C;EACA,IAAIE,MAAM,CAACW,cAAc,KAAK,IAAI,EAAE;IAClCd,MAAM,CAACc,cAAc,GAAG,CAACd,MAAM,CAACC,QAAQ;EAC1C;EACA,IAAIC,SAAS,IAAIA,SAAS,KAAKF,MAAM,CAACC,QAAQ,EAAE;IAC9CD,MAAM,CAACe,KAAK,GAAG,KAAK;EACtB;EACA,IAAIb,SAAS,KAAKF,MAAM,CAACC,QAAQ,EAAE;IACjCD,MAAM,CAACgB,IAAI,CAAChB,MAAM,CAACC,QAAQ,GAAG,MAAM,GAAG,QAAQ,CAAC;EAClD;AACF;AACA,eAAe;EACbF;AACF,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}