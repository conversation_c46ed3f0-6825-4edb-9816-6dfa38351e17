{"ast": null, "code": "import React from 'react';\nfunction renderVirtual(swiper, slides, virtualData) {\n  if (!virtualData) return null;\n  const getSlideIndex = index => {\n    let slideIndex = index;\n    if (index < 0) {\n      slideIndex = slides.length + index;\n    } else if (slideIndex >= slides.length) {\n      // eslint-disable-next-line\n      slideIndex = slideIndex - slides.length;\n    }\n    return slideIndex;\n  };\n  const style = swiper.isHorizontal() ? {\n    [swiper.rtlTranslate ? 'right' : 'left']: `${virtualData.offset}px`\n  } : {\n    top: `${virtualData.offset}px`\n  };\n  const {\n    from,\n    to\n  } = virtualData;\n  const loopFrom = swiper.params.loop ? -slides.length : 0;\n  const loopTo = swiper.params.loop ? slides.length * 2 : slides.length;\n  const slidesToRender = [];\n  for (let i = loopFrom; i < loopTo; i += 1) {\n    if (i >= from && i <= to) {\n      slidesToRender.push(slides[getSlideIndex(i)]);\n    }\n  }\n  return slidesToRender.map((child, index) => {\n    return /*#__PURE__*/React.cloneElement(child, {\n      swiper,\n      style,\n      key: `slide-${index}`\n    });\n  });\n}\nexport { renderVirtual };", "map": {"version": 3, "names": ["React", "renderVirtual", "swiper", "slides", "virtualData", "getSlideIndex", "index", "slideIndex", "length", "style", "isHorizontal", "rtlTranslate", "offset", "top", "from", "to", "loopFrom", "params", "loop", "loopTo", "slidesToRender", "i", "push", "map", "child", "cloneElement", "key"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/react/virtual.js"], "sourcesContent": ["import React from 'react';\nfunction renderVirtual(swiper, slides, virtualData) {\n  if (!virtualData) return null;\n  const getSlideIndex = index => {\n    let slideIndex = index;\n    if (index < 0) {\n      slideIndex = slides.length + index;\n    } else if (slideIndex >= slides.length) {\n      // eslint-disable-next-line\n      slideIndex = slideIndex - slides.length;\n    }\n    return slideIndex;\n  };\n  const style = swiper.isHorizontal() ? {\n    [swiper.rtlTranslate ? 'right' : 'left']: `${virtualData.offset}px`\n  } : {\n    top: `${virtualData.offset}px`\n  };\n  const {\n    from,\n    to\n  } = virtualData;\n  const loopFrom = swiper.params.loop ? -slides.length : 0;\n  const loopTo = swiper.params.loop ? slides.length * 2 : slides.length;\n  const slidesToRender = [];\n  for (let i = loopFrom; i < loopTo; i += 1) {\n    if (i >= from && i <= to) {\n      slidesToRender.push(slides[getSlideIndex(i)]);\n    }\n  }\n  return slidesToRender.map((child, index) => {\n    return /*#__PURE__*/React.cloneElement(child, {\n      swiper,\n      style,\n      key: `slide-${index}`\n    });\n  });\n}\nexport { renderVirtual };"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAaA,CAACC,MAAM,EAAEC,MAAM,EAAEC,WAAW,EAAE;EAClD,IAAI,CAACA,WAAW,EAAE,OAAO,IAAI;EAC7B,MAAMC,aAAa,GAAGC,KAAK,IAAI;IAC7B,IAAIC,UAAU,GAAGD,KAAK;IACtB,IAAIA,KAAK,GAAG,CAAC,EAAE;MACbC,UAAU,GAAGJ,MAAM,CAACK,MAAM,GAAGF,KAAK;IACpC,CAAC,MAAM,IAAIC,UAAU,IAAIJ,MAAM,CAACK,MAAM,EAAE;MACtC;MACAD,UAAU,GAAGA,UAAU,GAAGJ,MAAM,CAACK,MAAM;IACzC;IACA,OAAOD,UAAU;EACnB,CAAC;EACD,MAAME,KAAK,GAAGP,MAAM,CAACQ,YAAY,EAAE,GAAG;IACpC,CAACR,MAAM,CAACS,YAAY,GAAG,OAAO,GAAG,MAAM,GAAI,GAAEP,WAAW,CAACQ,MAAO;EAClE,CAAC,GAAG;IACFC,GAAG,EAAG,GAAET,WAAW,CAACQ,MAAO;EAC7B,CAAC;EACD,MAAM;IACJE,IAAI;IACJC;EACF,CAAC,GAAGX,WAAW;EACf,MAAMY,QAAQ,GAAGd,MAAM,CAACe,MAAM,CAACC,IAAI,GAAG,CAACf,MAAM,CAACK,MAAM,GAAG,CAAC;EACxD,MAAMW,MAAM,GAAGjB,MAAM,CAACe,MAAM,CAACC,IAAI,GAAGf,MAAM,CAACK,MAAM,GAAG,CAAC,GAAGL,MAAM,CAACK,MAAM;EACrE,MAAMY,cAAc,GAAG,EAAE;EACzB,KAAK,IAAIC,CAAC,GAAGL,QAAQ,EAAEK,CAAC,GAAGF,MAAM,EAAEE,CAAC,IAAI,CAAC,EAAE;IACzC,IAAIA,CAAC,IAAIP,IAAI,IAAIO,CAAC,IAAIN,EAAE,EAAE;MACxBK,cAAc,CAACE,IAAI,CAACnB,MAAM,CAACE,aAAa,CAACgB,CAAC,CAAC,CAAC,CAAC;IAC/C;EACF;EACA,OAAOD,cAAc,CAACG,GAAG,CAAC,CAACC,KAAK,EAAElB,KAAK,KAAK;IAC1C,OAAO,aAAaN,KAAK,CAACyB,YAAY,CAACD,KAAK,EAAE;MAC5CtB,MAAM;MACNO,KAAK;MACLiB,GAAG,EAAG,SAAQpB,KAAM;IACtB,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AACA,SAASL,aAAa"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}