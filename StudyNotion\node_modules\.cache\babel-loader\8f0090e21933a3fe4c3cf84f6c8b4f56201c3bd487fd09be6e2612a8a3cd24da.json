{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nvar _classCallCheck2 = _interopRequireDefault(require(\"@babel/runtime/helpers/classCallCheck\"));\nvar _createClass2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createClass\"));\nvar Fullscreen = /*#__PURE__*/\nfunction () {\n  function Fullscreen() {\n    (0, _classCallCheck2[\"default\"])(this, Fullscreen);\n  }\n  (0, _createClass2[\"default\"])(Fullscreen, [{\n    key: \"request\",\n    value: function request(elm) {\n      if (elm.requestFullscreen) {\n        elm.requestFullscreen();\n      } else if (elm.webkitRequestFullscreen) {\n        elm.webkitRequestFullscreen();\n      } else if (elm.mozRequestFullScreen) {\n        elm.mozRequestFullScreen();\n      } else if (elm.msRequestFullscreen) {\n        elm.msRequestFullscreen();\n      }\n    }\n  }, {\n    key: \"exit\",\n    value: function exit() {\n      if (document.exitFullscreen) {\n        document.exitFullscreen();\n      } else if (document.webkitExitFullscreen) {\n        document.webkitExitFullscreen();\n      } else if (document.mozCancelFullScreen) {\n        document.mozCancelFullScreen();\n      } else if (document.msExitFullscreen) {\n        document.msExitFullscreen();\n      }\n    }\n  }, {\n    key: \"addEventListener\",\n    value: function addEventListener(handler) {\n      document.addEventListener('fullscreenchange', handler);\n      document.addEventListener('webkitfullscreenchange', handler);\n      document.addEventListener('mozfullscreenchange', handler);\n      document.addEventListener('MSFullscreenChange', handler);\n    }\n  }, {\n    key: \"removeEventListener\",\n    value: function removeEventListener(handler) {\n      document.removeEventListener('fullscreenchange', handler);\n      document.removeEventListener('webkitfullscreenchange', handler);\n      document.removeEventListener('mozfullscreenchange', handler);\n      document.removeEventListener('MSFullscreenChange', handler);\n    }\n  }, {\n    key: \"isFullscreen\",\n    get: function get() {\n      return document.fullscreenElement || document.webkitFullscreenElement || document.mozFullScreenElement || document.msFullscreenElement;\n    }\n  }, {\n    key: \"enabled\",\n    get: function get() {\n      return document.fullscreenEnabled || document.webkitFullscreenEnabled || document.mozFullScreenEnabled || document.msFullscreenEnabled;\n    }\n  }]);\n  return Fullscreen;\n}();\nvar _default = new Fullscreen();\nexports[\"default\"] = _default;", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "_classCallCheck2", "_createClass2", "Fullscreen", "key", "request", "elm", "requestFullscreen", "webkitRequestFullscreen", "mozRequestFullScreen", "msRequestFullscreen", "exit", "document", "exitFullscreen", "webkitExitFullscreen", "mozCancelFullScreen", "msExitFullscreen", "addEventListener", "handler", "removeEventListener", "get", "fullscreenElement", "webkitFullscreenElement", "mozFullScreenElement", "msFullscreenElement", "fullscreenEnabled", "webkitFullscreenEnabled", "mozFullScreenEnabled", "msFullscreenEnabled", "_default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/video-react/lib/utils/fullscreen.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _classCallCheck2 = _interopRequireDefault(require(\"@babel/runtime/helpers/classCallCheck\"));\n\nvar _createClass2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createClass\"));\n\nvar Fullscreen =\n/*#__PURE__*/\nfunction () {\n  function Fullscreen() {\n    (0, _classCallCheck2[\"default\"])(this, Fullscreen);\n  }\n\n  (0, _createClass2[\"default\"])(Fullscreen, [{\n    key: \"request\",\n    value: function request(elm) {\n      if (elm.requestFullscreen) {\n        elm.requestFullscreen();\n      } else if (elm.webkitRequestFullscreen) {\n        elm.webkitRequestFullscreen();\n      } else if (elm.mozRequestFullScreen) {\n        elm.mozRequestFullScreen();\n      } else if (elm.msRequestFullscreen) {\n        elm.msRequestFullscreen();\n      }\n    }\n  }, {\n    key: \"exit\",\n    value: function exit() {\n      if (document.exitFullscreen) {\n        document.exitFullscreen();\n      } else if (document.webkitExitFullscreen) {\n        document.webkitExitFullscreen();\n      } else if (document.mozCancelFullScreen) {\n        document.mozCancelFullScreen();\n      } else if (document.msExitFullscreen) {\n        document.msExitFullscreen();\n      }\n    }\n  }, {\n    key: \"addEventListener\",\n    value: function addEventListener(handler) {\n      document.addEventListener('fullscreenchange', handler);\n      document.addEventListener('webkitfullscreenchange', handler);\n      document.addEventListener('mozfullscreenchange', handler);\n      document.addEventListener('MSFullscreenChange', handler);\n    }\n  }, {\n    key: \"removeEventListener\",\n    value: function removeEventListener(handler) {\n      document.removeEventListener('fullscreenchange', handler);\n      document.removeEventListener('webkitfullscreenchange', handler);\n      document.removeEventListener('mozfullscreenchange', handler);\n      document.removeEventListener('MSFullscreenChange', handler);\n    }\n  }, {\n    key: \"isFullscreen\",\n    get: function get() {\n      return document.fullscreenElement || document.webkitFullscreenElement || document.mozFullScreenElement || document.msFullscreenElement;\n    }\n  }, {\n    key: \"enabled\",\n    get: function get() {\n      return document.fullscreenEnabled || document.webkitFullscreenEnabled || document.mozFullScreenEnabled || document.msFullscreenEnabled;\n    }\n  }]);\n  return Fullscreen;\n}();\n\nvar _default = new Fullscreen();\n\nexports[\"default\"] = _default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC;AAEpFC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;AAE3B,IAAIE,gBAAgB,GAAGN,sBAAsB,CAACC,OAAO,CAAC,uCAAuC,CAAC,CAAC;AAE/F,IAAIM,aAAa,GAAGP,sBAAsB,CAACC,OAAO,CAAC,oCAAoC,CAAC,CAAC;AAEzF,IAAIO,UAAU,GACd;AACA,YAAY;EACV,SAASA,UAAUA,CAAA,EAAG;IACpB,CAAC,CAAC,EAAEF,gBAAgB,CAAC,SAAS,CAAC,EAAE,IAAI,EAAEE,UAAU,CAAC;EACpD;EAEA,CAAC,CAAC,EAAED,aAAa,CAAC,SAAS,CAAC,EAAEC,UAAU,EAAE,CAAC;IACzCC,GAAG,EAAE,SAAS;IACdJ,KAAK,EAAE,SAASK,OAAOA,CAACC,GAAG,EAAE;MAC3B,IAAIA,GAAG,CAACC,iBAAiB,EAAE;QACzBD,GAAG,CAACC,iBAAiB,EAAE;MACzB,CAAC,MAAM,IAAID,GAAG,CAACE,uBAAuB,EAAE;QACtCF,GAAG,CAACE,uBAAuB,EAAE;MAC/B,CAAC,MAAM,IAAIF,GAAG,CAACG,oBAAoB,EAAE;QACnCH,GAAG,CAACG,oBAAoB,EAAE;MAC5B,CAAC,MAAM,IAAIH,GAAG,CAACI,mBAAmB,EAAE;QAClCJ,GAAG,CAACI,mBAAmB,EAAE;MAC3B;IACF;EACF,CAAC,EAAE;IACDN,GAAG,EAAE,MAAM;IACXJ,KAAK,EAAE,SAASW,IAAIA,CAAA,EAAG;MACrB,IAAIC,QAAQ,CAACC,cAAc,EAAE;QAC3BD,QAAQ,CAACC,cAAc,EAAE;MAC3B,CAAC,MAAM,IAAID,QAAQ,CAACE,oBAAoB,EAAE;QACxCF,QAAQ,CAACE,oBAAoB,EAAE;MACjC,CAAC,MAAM,IAAIF,QAAQ,CAACG,mBAAmB,EAAE;QACvCH,QAAQ,CAACG,mBAAmB,EAAE;MAChC,CAAC,MAAM,IAAIH,QAAQ,CAACI,gBAAgB,EAAE;QACpCJ,QAAQ,CAACI,gBAAgB,EAAE;MAC7B;IACF;EACF,CAAC,EAAE;IACDZ,GAAG,EAAE,kBAAkB;IACvBJ,KAAK,EAAE,SAASiB,gBAAgBA,CAACC,OAAO,EAAE;MACxCN,QAAQ,CAACK,gBAAgB,CAAC,kBAAkB,EAAEC,OAAO,CAAC;MACtDN,QAAQ,CAACK,gBAAgB,CAAC,wBAAwB,EAAEC,OAAO,CAAC;MAC5DN,QAAQ,CAACK,gBAAgB,CAAC,qBAAqB,EAAEC,OAAO,CAAC;MACzDN,QAAQ,CAACK,gBAAgB,CAAC,oBAAoB,EAAEC,OAAO,CAAC;IAC1D;EACF,CAAC,EAAE;IACDd,GAAG,EAAE,qBAAqB;IAC1BJ,KAAK,EAAE,SAASmB,mBAAmBA,CAACD,OAAO,EAAE;MAC3CN,QAAQ,CAACO,mBAAmB,CAAC,kBAAkB,EAAED,OAAO,CAAC;MACzDN,QAAQ,CAACO,mBAAmB,CAAC,wBAAwB,EAAED,OAAO,CAAC;MAC/DN,QAAQ,CAACO,mBAAmB,CAAC,qBAAqB,EAAED,OAAO,CAAC;MAC5DN,QAAQ,CAACO,mBAAmB,CAAC,oBAAoB,EAAED,OAAO,CAAC;IAC7D;EACF,CAAC,EAAE;IACDd,GAAG,EAAE,cAAc;IACnBgB,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;MAClB,OAAOR,QAAQ,CAACS,iBAAiB,IAAIT,QAAQ,CAACU,uBAAuB,IAAIV,QAAQ,CAACW,oBAAoB,IAAIX,QAAQ,CAACY,mBAAmB;IACxI;EACF,CAAC,EAAE;IACDpB,GAAG,EAAE,SAAS;IACdgB,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;MAClB,OAAOR,QAAQ,CAACa,iBAAiB,IAAIb,QAAQ,CAACc,uBAAuB,IAAId,QAAQ,CAACe,oBAAoB,IAAIf,QAAQ,CAACgB,mBAAmB;IACxI;EACF,CAAC,CAAC,CAAC;EACH,OAAOzB,UAAU;AACnB,CAAC,EAAE;AAEH,IAAI0B,QAAQ,GAAG,IAAI1B,UAAU,EAAE;AAE/BJ,OAAO,CAAC,SAAS,CAAC,GAAG8B,QAAQ"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}