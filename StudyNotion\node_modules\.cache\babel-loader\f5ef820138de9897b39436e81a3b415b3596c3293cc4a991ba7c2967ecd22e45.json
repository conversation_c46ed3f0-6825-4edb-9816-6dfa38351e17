{"ast": null, "code": "/**\n * @typedef PointLike\n * @property {number | null | undefined} [line]\n * @property {number | null | undefined} [column]\n * @property {number | null | undefined} [offset]\n *\n * @typedef PositionLike\n * @property {PointLike | null | undefined} [start]\n * @property {PointLike | null | undefined} [end]\n *\n * @typedef NodeLike\n * @property {PositionLike | null | undefined} [position]\n */\n\n/**\n * Check if `node` is generated.\n *\n * @param {NodeLike | null | undefined} [node]\n *   Node to check.\n * @returns {boolean}\n *   Whether `node` is generated (does not have positional info).\n */\nexport function generated(node) {\n  return !node || !node.position || !node.position.start || !node.position.start.line || !node.position.start.column || !node.position.end || !node.position.end.line || !node.position.end.column;\n}", "map": {"version": 3, "names": ["generated", "node", "position", "start", "line", "column", "end"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/unist-util-generated/lib/index.js"], "sourcesContent": ["/**\n * @typedef PointLike\n * @property {number | null | undefined} [line]\n * @property {number | null | undefined} [column]\n * @property {number | null | undefined} [offset]\n *\n * @typedef PositionLike\n * @property {PointLike | null | undefined} [start]\n * @property {PointLike | null | undefined} [end]\n *\n * @typedef NodeLike\n * @property {PositionLike | null | undefined} [position]\n */\n\n/**\n * Check if `node` is generated.\n *\n * @param {NodeLike | null | undefined} [node]\n *   Node to check.\n * @returns {boolean}\n *   Whether `node` is generated (does not have positional info).\n */\nexport function generated(node) {\n  return (\n    !node ||\n    !node.position ||\n    !node.position.start ||\n    !node.position.start.line ||\n    !node.position.start.column ||\n    !node.position.end ||\n    !node.position.end.line ||\n    !node.position.end.column\n  )\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,SAASA,CAACC,IAAI,EAAE;EAC9B,OACE,CAACA,IAAI,IACL,CAACA,IAAI,CAACC,QAAQ,IACd,CAACD,IAAI,CAACC,QAAQ,CAACC,KAAK,IACpB,CAACF,IAAI,CAACC,QAAQ,CAACC,KAAK,CAACC,IAAI,IACzB,CAACH,IAAI,CAACC,QAAQ,CAACC,KAAK,CAACE,MAAM,IAC3B,CAACJ,IAAI,CAACC,QAAQ,CAACI,GAAG,IAClB,CAACL,IAAI,CAACC,QAAQ,CAACI,GAAG,CAACF,IAAI,IACvB,CAACH,IAAI,CAACC,QAAQ,CAACI,GAAG,CAACD,MAAM;AAE7B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}