{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nvar _defineProperty2 = _interopRequireDefault(require(\"@babel/runtime/helpers/defineProperty\"));\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    enumerableOnly && (symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    })), keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = null != arguments[i] ? arguments[i] : {};\n    i % 2 ? ownKeys(Object(source), !0).forEach(function (key) {\n      (0, _defineProperty2[\"default\"])(target, key, source[key]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) {\n      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n    });\n  }\n  return target;\n}\nvar omit = function omit(obj, omitProps) {\n  return Object.keys(obj).filter(function (key) {\n    return omitProps.indexOf(key) === -1;\n  }).reduce(function (returnObj, key) {\n    return _objectSpread(_objectSpread({}, returnObj), {}, (0, _defineProperty2[\"default\"])({}, key, obj[key]));\n  }, {});\n};\nvar allowed = function allowed(props) {\n  return omit(props, ['inHeader', 'columnKey', 'headers', 'forwardedRef']);\n};\nvar _default = allowed;\nexports[\"default\"] = _default;", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "_defineProperty2", "ownKeys", "object", "enumerableOnly", "keys", "getOwnPropertySymbols", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "target", "i", "arguments", "length", "source", "for<PERSON>ach", "key", "getOwnPropertyDescriptors", "defineProperties", "omit", "obj", "omitProps", "indexOf", "reduce", "returnObj", "allowed", "props", "_default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/react-super-responsive-table/dist/utils/allowed.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _defineProperty2 = _interopRequireDefault(require(\"@babel/runtime/helpers/defineProperty\"));\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2[\"default\"])(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n\nvar omit = function omit(obj, omitProps) {\n  return Object.keys(obj).filter(function (key) {\n    return omitProps.indexOf(key) === -1;\n  }).reduce(function (returnObj, key) {\n    return _objectSpread(_objectSpread({}, returnObj), {}, (0, _defineProperty2[\"default\"])({}, key, obj[key]));\n  }, {});\n};\n\nvar allowed = function allowed(props) {\n  return omit(props, ['inHeader', 'columnKey', 'headers', 'forwardedRef']);\n};\n\nvar _default = allowed;\nexports[\"default\"] = _default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC;AAEpFC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;AAE3B,IAAIE,gBAAgB,GAAGN,sBAAsB,CAACC,OAAO,CAAC,uCAAuC,CAAC,CAAC;AAE/F,SAASM,OAAOA,CAACC,MAAM,EAAEC,cAAc,EAAE;EAAE,IAAIC,IAAI,GAAGR,MAAM,CAACQ,IAAI,CAACF,MAAM,CAAC;EAAE,IAAIN,MAAM,CAACS,qBAAqB,EAAE;IAAE,IAAIC,OAAO,GAAGV,MAAM,CAACS,qBAAqB,CAACH,MAAM,CAAC;IAAEC,cAAc,KAAKG,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAE;MAAE,OAAOZ,MAAM,CAACa,wBAAwB,CAACP,MAAM,EAAEM,GAAG,CAAC,CAACE,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEN,IAAI,CAACO,IAAI,CAACC,KAAK,CAACR,IAAI,EAAEE,OAAO,CAAC;EAAE;EAAE,OAAOF,IAAI;AAAE;AAEpV,SAASS,aAAaA,CAACC,MAAM,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAIG,MAAM,GAAG,IAAI,IAAIF,SAAS,CAACD,CAAC,CAAC,GAAGC,SAAS,CAACD,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGd,OAAO,CAACL,MAAM,CAACsB,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;MAAE,CAAC,CAAC,EAAEpB,gBAAgB,CAAC,SAAS,CAAC,EAAEc,MAAM,EAAEM,GAAG,EAAEF,MAAM,CAACE,GAAG,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGxB,MAAM,CAACyB,yBAAyB,GAAGzB,MAAM,CAAC0B,gBAAgB,CAACR,MAAM,EAAElB,MAAM,CAACyB,yBAAyB,CAACH,MAAM,CAAC,CAAC,GAAGjB,OAAO,CAACL,MAAM,CAACsB,MAAM,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;MAAExB,MAAM,CAACC,cAAc,CAACiB,MAAM,EAAEM,GAAG,EAAExB,MAAM,CAACa,wBAAwB,CAACS,MAAM,EAAEE,GAAG,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAON,MAAM;AAAE;AAE1gB,IAAIS,IAAI,GAAG,SAASA,IAAIA,CAACC,GAAG,EAAEC,SAAS,EAAE;EACvC,OAAO7B,MAAM,CAACQ,IAAI,CAACoB,GAAG,CAAC,CAACjB,MAAM,CAAC,UAAUa,GAAG,EAAE;IAC5C,OAAOK,SAAS,CAACC,OAAO,CAACN,GAAG,CAAC,KAAK,CAAC,CAAC;EACtC,CAAC,CAAC,CAACO,MAAM,CAAC,UAAUC,SAAS,EAAER,GAAG,EAAE;IAClC,OAAOP,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEe,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE5B,gBAAgB,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAEoB,GAAG,EAAEI,GAAG,CAACJ,GAAG,CAAC,CAAC,CAAC;EAC7G,CAAC,EAAE,CAAC,CAAC,CAAC;AACR,CAAC;AAED,IAAIS,OAAO,GAAG,SAASA,OAAOA,CAACC,KAAK,EAAE;EACpC,OAAOP,IAAI,CAACO,KAAK,EAAE,CAAC,UAAU,EAAE,WAAW,EAAE,SAAS,EAAE,cAAc,CAAC,CAAC;AAC1E,CAAC;AAED,IAAIC,QAAQ,GAAGF,OAAO;AACtB/B,OAAO,CAAC,SAAS,CAAC,GAAGiC,QAAQ"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}