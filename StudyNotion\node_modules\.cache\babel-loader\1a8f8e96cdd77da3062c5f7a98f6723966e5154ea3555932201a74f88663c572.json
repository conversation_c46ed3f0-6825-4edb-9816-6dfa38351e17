{"ast": null, "code": "import getTranslate from './getTranslate.js';\nimport setTranslate from './setTranslate.js';\nimport minTranslate from './minTranslate.js';\nimport maxTranslate from './maxTranslate.js';\nimport translateTo from './translateTo.js';\nexport default {\n  getTranslate,\n  setTranslate,\n  minTranslate,\n  maxTranslate,\n  translateTo\n};", "map": {"version": 3, "names": ["getTranslate", "setTranslate", "minTranslate", "maxTranslate", "translateTo"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/core/translate/index.js"], "sourcesContent": ["import getTranslate from './getTranslate.js';\nimport setTranslate from './setTranslate.js';\nimport minTranslate from './minTranslate.js';\nimport maxTranslate from './maxTranslate.js';\nimport translateTo from './translateTo.js';\nexport default {\n  getTranslate,\n  setTranslate,\n  minTranslate,\n  maxTranslate,\n  translateTo\n};"], "mappings": "AAAA,OAAOA,YAAY,MAAM,mBAAmB;AAC5C,OAAOC,YAAY,MAAM,mBAAmB;AAC5C,OAAOC,YAAY,MAAM,mBAAmB;AAC5C,OAAOC,YAAY,MAAM,mBAAmB;AAC5C,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,eAAe;EACbJ,YAAY;EACZC,YAAY;EACZC,YAAY;EACZC,YAAY;EACZC;AACF,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}