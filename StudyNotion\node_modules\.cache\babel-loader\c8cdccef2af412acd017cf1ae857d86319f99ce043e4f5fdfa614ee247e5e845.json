{"ast": null, "code": "import classesToSelector from '../../shared/classes-to-selector.js';\nimport createElementIfNotDefined from '../../shared/create-element-if-not-defined.js';\nimport { elementIndex, elementOuterSize, elementParents } from '../../shared/utils.js';\nexport default function Pagination(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on,\n    emit\n  } = _ref;\n  const pfx = 'swiper-pagination';\n  extendParams({\n    pagination: {\n      el: null,\n      bulletElement: 'span',\n      clickable: false,\n      hideOnClick: false,\n      renderBullet: null,\n      renderProgressbar: null,\n      renderFraction: null,\n      renderCustom: null,\n      progressbarOpposite: false,\n      type: 'bullets',\n      // 'bullets' or 'progressbar' or 'fraction' or 'custom'\n      dynamicBullets: false,\n      dynamicMainBullets: 1,\n      formatFractionCurrent: number => number,\n      formatFractionTotal: number => number,\n      bulletClass: `${pfx}-bullet`,\n      bulletActiveClass: `${pfx}-bullet-active`,\n      modifierClass: `${pfx}-`,\n      currentClass: `${pfx}-current`,\n      totalClass: `${pfx}-total`,\n      hiddenClass: `${pfx}-hidden`,\n      progressbarFillClass: `${pfx}-progressbar-fill`,\n      progressbarOppositeClass: `${pfx}-progressbar-opposite`,\n      clickableClass: `${pfx}-clickable`,\n      lockClass: `${pfx}-lock`,\n      horizontalClass: `${pfx}-horizontal`,\n      verticalClass: `${pfx}-vertical`,\n      paginationDisabledClass: `${pfx}-disabled`\n    }\n  });\n  swiper.pagination = {\n    el: null,\n    bullets: []\n  };\n  let bulletSize;\n  let dynamicBulletIndex = 0;\n  const makeElementsArray = el => {\n    if (!Array.isArray(el)) el = [el].filter(e => !!e);\n    return el;\n  };\n  function isPaginationDisabled() {\n    return !swiper.params.pagination.el || !swiper.pagination.el || Array.isArray(swiper.pagination.el) && swiper.pagination.el.length === 0;\n  }\n  function setSideBullets(bulletEl, position) {\n    const {\n      bulletActiveClass\n    } = swiper.params.pagination;\n    if (!bulletEl) return;\n    bulletEl = bulletEl[`${position === 'prev' ? 'previous' : 'next'}ElementSibling`];\n    if (bulletEl) {\n      bulletEl.classList.add(`${bulletActiveClass}-${position}`);\n      bulletEl = bulletEl[`${position === 'prev' ? 'previous' : 'next'}ElementSibling`];\n      if (bulletEl) {\n        bulletEl.classList.add(`${bulletActiveClass}-${position}-${position}`);\n      }\n    }\n  }\n  function onBulletClick(e) {\n    const bulletEl = e.target.closest(classesToSelector(swiper.params.pagination.bulletClass));\n    if (!bulletEl) {\n      return;\n    }\n    e.preventDefault();\n    const index = elementIndex(bulletEl) * swiper.params.slidesPerGroup;\n    if (swiper.params.loop) {\n      if (swiper.realIndex === index) return;\n      const newSlideIndex = swiper.getSlideIndexByData(index);\n      const currentSlideIndex = swiper.getSlideIndexByData(swiper.realIndex);\n      if (newSlideIndex > swiper.slides.length - swiper.loopedSlides) {\n        swiper.loopFix({\n          direction: newSlideIndex > currentSlideIndex ? 'next' : 'prev',\n          activeSlideIndex: newSlideIndex,\n          slideTo: false\n        });\n      }\n      swiper.slideToLoop(index);\n    } else {\n      swiper.slideTo(index);\n    }\n  }\n  function update() {\n    // Render || Update Pagination bullets/items\n    const rtl = swiper.rtl;\n    const params = swiper.params.pagination;\n    if (isPaginationDisabled()) return;\n    let el = swiper.pagination.el;\n    el = makeElementsArray(el);\n    // Current/Total\n    let current;\n    let previousIndex;\n    const slidesLength = swiper.virtual && swiper.params.virtual.enabled ? swiper.virtual.slides.length : swiper.slides.length;\n    const total = swiper.params.loop ? Math.ceil(slidesLength / swiper.params.slidesPerGroup) : swiper.snapGrid.length;\n    if (swiper.params.loop) {\n      previousIndex = swiper.previousRealIndex || 0;\n      current = swiper.params.slidesPerGroup > 1 ? Math.floor(swiper.realIndex / swiper.params.slidesPerGroup) : swiper.realIndex;\n    } else if (typeof swiper.snapIndex !== 'undefined') {\n      current = swiper.snapIndex;\n      previousIndex = swiper.previousSnapIndex;\n    } else {\n      previousIndex = swiper.previousIndex || 0;\n      current = swiper.activeIndex || 0;\n    }\n    // Types\n    if (params.type === 'bullets' && swiper.pagination.bullets && swiper.pagination.bullets.length > 0) {\n      const bullets = swiper.pagination.bullets;\n      let firstIndex;\n      let lastIndex;\n      let midIndex;\n      if (params.dynamicBullets) {\n        bulletSize = elementOuterSize(bullets[0], swiper.isHorizontal() ? 'width' : 'height', true);\n        el.forEach(subEl => {\n          subEl.style[swiper.isHorizontal() ? 'width' : 'height'] = `${bulletSize * (params.dynamicMainBullets + 4)}px`;\n        });\n        if (params.dynamicMainBullets > 1 && previousIndex !== undefined) {\n          dynamicBulletIndex += current - (previousIndex || 0);\n          if (dynamicBulletIndex > params.dynamicMainBullets - 1) {\n            dynamicBulletIndex = params.dynamicMainBullets - 1;\n          } else if (dynamicBulletIndex < 0) {\n            dynamicBulletIndex = 0;\n          }\n        }\n        firstIndex = Math.max(current - dynamicBulletIndex, 0);\n        lastIndex = firstIndex + (Math.min(bullets.length, params.dynamicMainBullets) - 1);\n        midIndex = (lastIndex + firstIndex) / 2;\n      }\n      bullets.forEach(bulletEl => {\n        const classesToRemove = [...['', '-next', '-next-next', '-prev', '-prev-prev', '-main'].map(suffix => `${params.bulletActiveClass}${suffix}`)].map(s => typeof s === 'string' && s.includes(' ') ? s.split(' ') : s).flat();\n        bulletEl.classList.remove(...classesToRemove);\n      });\n      if (el.length > 1) {\n        bullets.forEach(bullet => {\n          const bulletIndex = elementIndex(bullet);\n          if (bulletIndex === current) {\n            bullet.classList.add(...params.bulletActiveClass.split(' '));\n          } else if (swiper.isElement) {\n            bullet.setAttribute('part', 'bullet');\n          }\n          if (params.dynamicBullets) {\n            if (bulletIndex >= firstIndex && bulletIndex <= lastIndex) {\n              bullet.classList.add(...`${params.bulletActiveClass}-main`.split(' '));\n            }\n            if (bulletIndex === firstIndex) {\n              setSideBullets(bullet, 'prev');\n            }\n            if (bulletIndex === lastIndex) {\n              setSideBullets(bullet, 'next');\n            }\n          }\n        });\n      } else {\n        const bullet = bullets[current];\n        if (bullet) {\n          bullet.classList.add(...params.bulletActiveClass.split(' '));\n        }\n        if (swiper.isElement) {\n          bullets.forEach((bulletEl, bulletIndex) => {\n            bulletEl.setAttribute('part', bulletIndex === current ? 'bullet-active' : 'bullet');\n          });\n        }\n        if (params.dynamicBullets) {\n          const firstDisplayedBullet = bullets[firstIndex];\n          const lastDisplayedBullet = bullets[lastIndex];\n          for (let i = firstIndex; i <= lastIndex; i += 1) {\n            if (bullets[i]) {\n              bullets[i].classList.add(...`${params.bulletActiveClass}-main`.split(' '));\n            }\n          }\n          setSideBullets(firstDisplayedBullet, 'prev');\n          setSideBullets(lastDisplayedBullet, 'next');\n        }\n      }\n      if (params.dynamicBullets) {\n        const dynamicBulletsLength = Math.min(bullets.length, params.dynamicMainBullets + 4);\n        const bulletsOffset = (bulletSize * dynamicBulletsLength - bulletSize) / 2 - midIndex * bulletSize;\n        const offsetProp = rtl ? 'right' : 'left';\n        bullets.forEach(bullet => {\n          bullet.style[swiper.isHorizontal() ? offsetProp : 'top'] = `${bulletsOffset}px`;\n        });\n      }\n    }\n    el.forEach((subEl, subElIndex) => {\n      if (params.type === 'fraction') {\n        subEl.querySelectorAll(classesToSelector(params.currentClass)).forEach(fractionEl => {\n          fractionEl.textContent = params.formatFractionCurrent(current + 1);\n        });\n        subEl.querySelectorAll(classesToSelector(params.totalClass)).forEach(totalEl => {\n          totalEl.textContent = params.formatFractionTotal(total);\n        });\n      }\n      if (params.type === 'progressbar') {\n        let progressbarDirection;\n        if (params.progressbarOpposite) {\n          progressbarDirection = swiper.isHorizontal() ? 'vertical' : 'horizontal';\n        } else {\n          progressbarDirection = swiper.isHorizontal() ? 'horizontal' : 'vertical';\n        }\n        const scale = (current + 1) / total;\n        let scaleX = 1;\n        let scaleY = 1;\n        if (progressbarDirection === 'horizontal') {\n          scaleX = scale;\n        } else {\n          scaleY = scale;\n        }\n        subEl.querySelectorAll(classesToSelector(params.progressbarFillClass)).forEach(progressEl => {\n          progressEl.style.transform = `translate3d(0,0,0) scaleX(${scaleX}) scaleY(${scaleY})`;\n          progressEl.style.transitionDuration = `${swiper.params.speed}ms`;\n        });\n      }\n      if (params.type === 'custom' && params.renderCustom) {\n        subEl.innerHTML = params.renderCustom(swiper, current + 1, total);\n        if (subElIndex === 0) emit('paginationRender', subEl);\n      } else {\n        if (subElIndex === 0) emit('paginationRender', subEl);\n        emit('paginationUpdate', subEl);\n      }\n      if (swiper.params.watchOverflow && swiper.enabled) {\n        subEl.classList[swiper.isLocked ? 'add' : 'remove'](params.lockClass);\n      }\n    });\n  }\n  function render() {\n    // Render Container\n    const params = swiper.params.pagination;\n    if (isPaginationDisabled()) return;\n    const slidesLength = swiper.virtual && swiper.params.virtual.enabled ? swiper.virtual.slides.length : swiper.slides.length;\n    let el = swiper.pagination.el;\n    el = makeElementsArray(el);\n    let paginationHTML = '';\n    if (params.type === 'bullets') {\n      let numberOfBullets = swiper.params.loop ? Math.ceil(slidesLength / swiper.params.slidesPerGroup) : swiper.snapGrid.length;\n      if (swiper.params.freeMode && swiper.params.freeMode.enabled && numberOfBullets > slidesLength) {\n        numberOfBullets = slidesLength;\n      }\n      for (let i = 0; i < numberOfBullets; i += 1) {\n        if (params.renderBullet) {\n          paginationHTML += params.renderBullet.call(swiper, i, params.bulletClass);\n        } else {\n          // prettier-ignore\n          paginationHTML += `<${params.bulletElement} ${swiper.isElement ? 'part=\"bullet\"' : ''} class=\"${params.bulletClass}\"></${params.bulletElement}>`;\n        }\n      }\n    }\n    if (params.type === 'fraction') {\n      if (params.renderFraction) {\n        paginationHTML = params.renderFraction.call(swiper, params.currentClass, params.totalClass);\n      } else {\n        paginationHTML = `<span class=\"${params.currentClass}\"></span>` + ' / ' + `<span class=\"${params.totalClass}\"></span>`;\n      }\n    }\n    if (params.type === 'progressbar') {\n      if (params.renderProgressbar) {\n        paginationHTML = params.renderProgressbar.call(swiper, params.progressbarFillClass);\n      } else {\n        paginationHTML = `<span class=\"${params.progressbarFillClass}\"></span>`;\n      }\n    }\n    swiper.pagination.bullets = [];\n    el.forEach(subEl => {\n      if (params.type !== 'custom') {\n        subEl.innerHTML = paginationHTML || '';\n      }\n      if (params.type === 'bullets') {\n        swiper.pagination.bullets.push(...subEl.querySelectorAll(classesToSelector(params.bulletClass)));\n      }\n    });\n    if (params.type !== 'custom') {\n      emit('paginationRender', el[0]);\n    }\n  }\n  function init() {\n    swiper.params.pagination = createElementIfNotDefined(swiper, swiper.originalParams.pagination, swiper.params.pagination, {\n      el: 'swiper-pagination'\n    });\n    const params = swiper.params.pagination;\n    if (!params.el) return;\n    let el;\n    if (typeof params.el === 'string' && swiper.isElement) {\n      el = swiper.el.shadowRoot.querySelector(params.el);\n    }\n    if (!el && typeof params.el === 'string') {\n      el = [...document.querySelectorAll(params.el)];\n    }\n    if (!el) {\n      el = params.el;\n    }\n    if (!el || el.length === 0) return;\n    if (swiper.params.uniqueNavElements && typeof params.el === 'string' && Array.isArray(el) && el.length > 1) {\n      el = [...swiper.el.querySelectorAll(params.el)];\n      // check if it belongs to another nested Swiper\n      if (el.length > 1) {\n        el = el.filter(subEl => {\n          if (elementParents(subEl, '.swiper')[0] !== swiper.el) return false;\n          return true;\n        })[0];\n      }\n    }\n    if (Array.isArray(el) && el.length === 1) el = el[0];\n    Object.assign(swiper.pagination, {\n      el\n    });\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      if (params.type === 'bullets' && params.clickable) {\n        subEl.classList.add(params.clickableClass);\n      }\n      subEl.classList.add(params.modifierClass + params.type);\n      subEl.classList.add(swiper.isHorizontal() ? params.horizontalClass : params.verticalClass);\n      if (params.type === 'bullets' && params.dynamicBullets) {\n        subEl.classList.add(`${params.modifierClass}${params.type}-dynamic`);\n        dynamicBulletIndex = 0;\n        if (params.dynamicMainBullets < 1) {\n          params.dynamicMainBullets = 1;\n        }\n      }\n      if (params.type === 'progressbar' && params.progressbarOpposite) {\n        subEl.classList.add(params.progressbarOppositeClass);\n      }\n      if (params.clickable) {\n        subEl.addEventListener('click', onBulletClick);\n      }\n      if (!swiper.enabled) {\n        subEl.classList.add(params.lockClass);\n      }\n    });\n  }\n  function destroy() {\n    const params = swiper.params.pagination;\n    if (isPaginationDisabled()) return;\n    let el = swiper.pagination.el;\n    if (el) {\n      el = makeElementsArray(el);\n      el.forEach(subEl => {\n        subEl.classList.remove(params.hiddenClass);\n        subEl.classList.remove(params.modifierClass + params.type);\n        subEl.classList.remove(swiper.isHorizontal() ? params.horizontalClass : params.verticalClass);\n        if (params.clickable) {\n          subEl.removeEventListener('click', onBulletClick);\n        }\n      });\n    }\n    if (swiper.pagination.bullets) swiper.pagination.bullets.forEach(subEl => subEl.classList.remove(...params.bulletActiveClass.split(' ')));\n  }\n  on('changeDirection', () => {\n    if (!swiper.pagination || !swiper.pagination.el) return;\n    const params = swiper.params.pagination;\n    let {\n      el\n    } = swiper.pagination;\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.classList.remove(params.horizontalClass, params.verticalClass);\n      subEl.classList.add(swiper.isHorizontal() ? params.horizontalClass : params.verticalClass);\n    });\n  });\n  on('init', () => {\n    if (swiper.params.pagination.enabled === false) {\n      // eslint-disable-next-line\n      disable();\n    } else {\n      init();\n      render();\n      update();\n    }\n  });\n  on('activeIndexChange', () => {\n    if (typeof swiper.snapIndex === 'undefined') {\n      update();\n    }\n  });\n  on('snapIndexChange', () => {\n    update();\n  });\n  on('snapGridLengthChange', () => {\n    render();\n    update();\n  });\n  on('destroy', () => {\n    destroy();\n  });\n  on('enable disable', () => {\n    let {\n      el\n    } = swiper.pagination;\n    if (el) {\n      el = makeElementsArray(el);\n      el.forEach(subEl => subEl.classList[swiper.enabled ? 'remove' : 'add'](swiper.params.pagination.lockClass));\n    }\n  });\n  on('lock unlock', () => {\n    update();\n  });\n  on('click', (_s, e) => {\n    const targetEl = e.target;\n    let {\n      el\n    } = swiper.pagination;\n    if (!Array.isArray(el)) el = [el].filter(element => !!element);\n    if (swiper.params.pagination.el && swiper.params.pagination.hideOnClick && el && el.length > 0 && !targetEl.classList.contains(swiper.params.pagination.bulletClass)) {\n      if (swiper.navigation && (swiper.navigation.nextEl && targetEl === swiper.navigation.nextEl || swiper.navigation.prevEl && targetEl === swiper.navigation.prevEl)) return;\n      const isHidden = el[0].classList.contains(swiper.params.pagination.hiddenClass);\n      if (isHidden === true) {\n        emit('paginationShow');\n      } else {\n        emit('paginationHide');\n      }\n      el.forEach(subEl => subEl.classList.toggle(swiper.params.pagination.hiddenClass));\n    }\n  });\n  const enable = () => {\n    swiper.el.classList.remove(swiper.params.pagination.paginationDisabledClass);\n    let {\n      el\n    } = swiper.pagination;\n    if (el) {\n      el = makeElementsArray(el);\n      el.forEach(subEl => subEl.classList.remove(swiper.params.pagination.paginationDisabledClass));\n    }\n    init();\n    render();\n    update();\n  };\n  const disable = () => {\n    swiper.el.classList.add(swiper.params.pagination.paginationDisabledClass);\n    let {\n      el\n    } = swiper.pagination;\n    if (el) {\n      el = makeElementsArray(el);\n      el.forEach(subEl => subEl.classList.add(swiper.params.pagination.paginationDisabledClass));\n    }\n    destroy();\n  };\n  Object.assign(swiper.pagination, {\n    enable,\n    disable,\n    render,\n    update,\n    init,\n    destroy\n  });\n}", "map": {"version": 3, "names": ["classesToSelector", "createElementIfNotDefined", "elementIndex", "elementOuterSize", "elementParents", "Pagination", "_ref", "swiper", "extendParams", "on", "emit", "pfx", "pagination", "el", "bulletElement", "clickable", "hideOnClick", "renderBullet", "renderProgressbar", "renderFraction", "renderCustom", "progressbarOpposite", "type", "dynamicBullets", "dynamicMainBullets", "formatFractionCurrent", "number", "formatFractionTotal", "bulletClass", "bulletActiveClass", "modifierClass", "currentClass", "totalClass", "hiddenClass", "progressbarFillClass", "progressbarOppositeClass", "clickableClass", "lockClass", "horizontalClass", "verticalClass", "paginationDisabledClass", "bullets", "bulletSize", "dynamicBulletIndex", "makeElementsArray", "Array", "isArray", "filter", "e", "isPaginationDisabled", "params", "length", "setSideBullets", "bulletEl", "position", "classList", "add", "onBulletClick", "target", "closest", "preventDefault", "index", "slidesPerGroup", "loop", "realIndex", "newSlideIndex", "getSlideIndexByData", "currentSlideIndex", "slides", "loopedSlides", "loopFix", "direction", "activeSlideIndex", "slideTo", "slideToLoop", "update", "rtl", "current", "previousIndex", "<PERSON><PERSON><PERSON><PERSON>", "virtual", "enabled", "total", "Math", "ceil", "snapGrid", "previousRealIndex", "floor", "snapIndex", "previousSnapIndex", "activeIndex", "firstIndex", "lastIndex", "midIndex", "isHorizontal", "for<PERSON>ach", "subEl", "style", "undefined", "max", "min", "classesToRemove", "map", "suffix", "s", "includes", "split", "flat", "remove", "bullet", "bulletIndex", "isElement", "setAttribute", "first<PERSON><PERSON>played<PERSON><PERSON>et", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "i", "dynamicBulletsLength", "bulletsOffset", "offsetProp", "subElIndex", "querySelectorAll", "fractionEl", "textContent", "totalEl", "progressbarDirection", "scale", "scaleX", "scaleY", "progressEl", "transform", "transitionDuration", "speed", "innerHTML", "watchOverflow", "isLocked", "render", "paginationHTML", "numberOfBullets", "freeMode", "call", "push", "init", "originalParams", "shadowRoot", "querySelector", "document", "uniqueNavElements", "Object", "assign", "addEventListener", "destroy", "removeEventListener", "disable", "_s", "targetEl", "element", "contains", "navigation", "nextEl", "prevEl", "isHidden", "toggle", "enable"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/modules/pagination/pagination.js"], "sourcesContent": ["import classesToSelector from '../../shared/classes-to-selector.js';\nimport createElementIfNotDefined from '../../shared/create-element-if-not-defined.js';\nimport { elementIndex, elementOuterSize, elementParents } from '../../shared/utils.js';\nexport default function Pagination({\n  swiper,\n  extendParams,\n  on,\n  emit\n}) {\n  const pfx = 'swiper-pagination';\n  extendParams({\n    pagination: {\n      el: null,\n      bulletElement: 'span',\n      clickable: false,\n      hideOnClick: false,\n      renderBullet: null,\n      renderProgressbar: null,\n      renderFraction: null,\n      renderCustom: null,\n      progressbarOpposite: false,\n      type: 'bullets',\n      // 'bullets' or 'progressbar' or 'fraction' or 'custom'\n      dynamicBullets: false,\n      dynamicMainBullets: 1,\n      formatFractionCurrent: number => number,\n      formatFractionTotal: number => number,\n      bulletClass: `${pfx}-bullet`,\n      bulletActiveClass: `${pfx}-bullet-active`,\n      modifierClass: `${pfx}-`,\n      currentClass: `${pfx}-current`,\n      totalClass: `${pfx}-total`,\n      hiddenClass: `${pfx}-hidden`,\n      progressbarFillClass: `${pfx}-progressbar-fill`,\n      progressbarOppositeClass: `${pfx}-progressbar-opposite`,\n      clickableClass: `${pfx}-clickable`,\n      lockClass: `${pfx}-lock`,\n      horizontalClass: `${pfx}-horizontal`,\n      verticalClass: `${pfx}-vertical`,\n      paginationDisabledClass: `${pfx}-disabled`\n    }\n  });\n  swiper.pagination = {\n    el: null,\n    bullets: []\n  };\n  let bulletSize;\n  let dynamicBulletIndex = 0;\n  const makeElementsArray = el => {\n    if (!Array.isArray(el)) el = [el].filter(e => !!e);\n    return el;\n  };\n  function isPaginationDisabled() {\n    return !swiper.params.pagination.el || !swiper.pagination.el || Array.isArray(swiper.pagination.el) && swiper.pagination.el.length === 0;\n  }\n  function setSideBullets(bulletEl, position) {\n    const {\n      bulletActiveClass\n    } = swiper.params.pagination;\n    if (!bulletEl) return;\n    bulletEl = bulletEl[`${position === 'prev' ? 'previous' : 'next'}ElementSibling`];\n    if (bulletEl) {\n      bulletEl.classList.add(`${bulletActiveClass}-${position}`);\n      bulletEl = bulletEl[`${position === 'prev' ? 'previous' : 'next'}ElementSibling`];\n      if (bulletEl) {\n        bulletEl.classList.add(`${bulletActiveClass}-${position}-${position}`);\n      }\n    }\n  }\n  function onBulletClick(e) {\n    const bulletEl = e.target.closest(classesToSelector(swiper.params.pagination.bulletClass));\n    if (!bulletEl) {\n      return;\n    }\n    e.preventDefault();\n    const index = elementIndex(bulletEl) * swiper.params.slidesPerGroup;\n    if (swiper.params.loop) {\n      if (swiper.realIndex === index) return;\n      const newSlideIndex = swiper.getSlideIndexByData(index);\n      const currentSlideIndex = swiper.getSlideIndexByData(swiper.realIndex);\n      if (newSlideIndex > swiper.slides.length - swiper.loopedSlides) {\n        swiper.loopFix({\n          direction: newSlideIndex > currentSlideIndex ? 'next' : 'prev',\n          activeSlideIndex: newSlideIndex,\n          slideTo: false\n        });\n      }\n      swiper.slideToLoop(index);\n    } else {\n      swiper.slideTo(index);\n    }\n  }\n  function update() {\n    // Render || Update Pagination bullets/items\n    const rtl = swiper.rtl;\n    const params = swiper.params.pagination;\n    if (isPaginationDisabled()) return;\n    let el = swiper.pagination.el;\n    el = makeElementsArray(el);\n    // Current/Total\n    let current;\n    let previousIndex;\n    const slidesLength = swiper.virtual && swiper.params.virtual.enabled ? swiper.virtual.slides.length : swiper.slides.length;\n    const total = swiper.params.loop ? Math.ceil(slidesLength / swiper.params.slidesPerGroup) : swiper.snapGrid.length;\n    if (swiper.params.loop) {\n      previousIndex = swiper.previousRealIndex || 0;\n      current = swiper.params.slidesPerGroup > 1 ? Math.floor(swiper.realIndex / swiper.params.slidesPerGroup) : swiper.realIndex;\n    } else if (typeof swiper.snapIndex !== 'undefined') {\n      current = swiper.snapIndex;\n      previousIndex = swiper.previousSnapIndex;\n    } else {\n      previousIndex = swiper.previousIndex || 0;\n      current = swiper.activeIndex || 0;\n    }\n    // Types\n    if (params.type === 'bullets' && swiper.pagination.bullets && swiper.pagination.bullets.length > 0) {\n      const bullets = swiper.pagination.bullets;\n      let firstIndex;\n      let lastIndex;\n      let midIndex;\n      if (params.dynamicBullets) {\n        bulletSize = elementOuterSize(bullets[0], swiper.isHorizontal() ? 'width' : 'height', true);\n        el.forEach(subEl => {\n          subEl.style[swiper.isHorizontal() ? 'width' : 'height'] = `${bulletSize * (params.dynamicMainBullets + 4)}px`;\n        });\n        if (params.dynamicMainBullets > 1 && previousIndex !== undefined) {\n          dynamicBulletIndex += current - (previousIndex || 0);\n          if (dynamicBulletIndex > params.dynamicMainBullets - 1) {\n            dynamicBulletIndex = params.dynamicMainBullets - 1;\n          } else if (dynamicBulletIndex < 0) {\n            dynamicBulletIndex = 0;\n          }\n        }\n        firstIndex = Math.max(current - dynamicBulletIndex, 0);\n        lastIndex = firstIndex + (Math.min(bullets.length, params.dynamicMainBullets) - 1);\n        midIndex = (lastIndex + firstIndex) / 2;\n      }\n      bullets.forEach(bulletEl => {\n        const classesToRemove = [...['', '-next', '-next-next', '-prev', '-prev-prev', '-main'].map(suffix => `${params.bulletActiveClass}${suffix}`)].map(s => typeof s === 'string' && s.includes(' ') ? s.split(' ') : s).flat();\n        bulletEl.classList.remove(...classesToRemove);\n      });\n      if (el.length > 1) {\n        bullets.forEach(bullet => {\n          const bulletIndex = elementIndex(bullet);\n          if (bulletIndex === current) {\n            bullet.classList.add(...params.bulletActiveClass.split(' '));\n          } else if (swiper.isElement) {\n            bullet.setAttribute('part', 'bullet');\n          }\n          if (params.dynamicBullets) {\n            if (bulletIndex >= firstIndex && bulletIndex <= lastIndex) {\n              bullet.classList.add(...`${params.bulletActiveClass}-main`.split(' '));\n            }\n            if (bulletIndex === firstIndex) {\n              setSideBullets(bullet, 'prev');\n            }\n            if (bulletIndex === lastIndex) {\n              setSideBullets(bullet, 'next');\n            }\n          }\n        });\n      } else {\n        const bullet = bullets[current];\n        if (bullet) {\n          bullet.classList.add(...params.bulletActiveClass.split(' '));\n        }\n        if (swiper.isElement) {\n          bullets.forEach((bulletEl, bulletIndex) => {\n            bulletEl.setAttribute('part', bulletIndex === current ? 'bullet-active' : 'bullet');\n          });\n        }\n        if (params.dynamicBullets) {\n          const firstDisplayedBullet = bullets[firstIndex];\n          const lastDisplayedBullet = bullets[lastIndex];\n          for (let i = firstIndex; i <= lastIndex; i += 1) {\n            if (bullets[i]) {\n              bullets[i].classList.add(...`${params.bulletActiveClass}-main`.split(' '));\n            }\n          }\n          setSideBullets(firstDisplayedBullet, 'prev');\n          setSideBullets(lastDisplayedBullet, 'next');\n        }\n      }\n      if (params.dynamicBullets) {\n        const dynamicBulletsLength = Math.min(bullets.length, params.dynamicMainBullets + 4);\n        const bulletsOffset = (bulletSize * dynamicBulletsLength - bulletSize) / 2 - midIndex * bulletSize;\n        const offsetProp = rtl ? 'right' : 'left';\n        bullets.forEach(bullet => {\n          bullet.style[swiper.isHorizontal() ? offsetProp : 'top'] = `${bulletsOffset}px`;\n        });\n      }\n    }\n    el.forEach((subEl, subElIndex) => {\n      if (params.type === 'fraction') {\n        subEl.querySelectorAll(classesToSelector(params.currentClass)).forEach(fractionEl => {\n          fractionEl.textContent = params.formatFractionCurrent(current + 1);\n        });\n        subEl.querySelectorAll(classesToSelector(params.totalClass)).forEach(totalEl => {\n          totalEl.textContent = params.formatFractionTotal(total);\n        });\n      }\n      if (params.type === 'progressbar') {\n        let progressbarDirection;\n        if (params.progressbarOpposite) {\n          progressbarDirection = swiper.isHorizontal() ? 'vertical' : 'horizontal';\n        } else {\n          progressbarDirection = swiper.isHorizontal() ? 'horizontal' : 'vertical';\n        }\n        const scale = (current + 1) / total;\n        let scaleX = 1;\n        let scaleY = 1;\n        if (progressbarDirection === 'horizontal') {\n          scaleX = scale;\n        } else {\n          scaleY = scale;\n        }\n        subEl.querySelectorAll(classesToSelector(params.progressbarFillClass)).forEach(progressEl => {\n          progressEl.style.transform = `translate3d(0,0,0) scaleX(${scaleX}) scaleY(${scaleY})`;\n          progressEl.style.transitionDuration = `${swiper.params.speed}ms`;\n        });\n      }\n      if (params.type === 'custom' && params.renderCustom) {\n        subEl.innerHTML = params.renderCustom(swiper, current + 1, total);\n        if (subElIndex === 0) emit('paginationRender', subEl);\n      } else {\n        if (subElIndex === 0) emit('paginationRender', subEl);\n        emit('paginationUpdate', subEl);\n      }\n      if (swiper.params.watchOverflow && swiper.enabled) {\n        subEl.classList[swiper.isLocked ? 'add' : 'remove'](params.lockClass);\n      }\n    });\n  }\n  function render() {\n    // Render Container\n    const params = swiper.params.pagination;\n    if (isPaginationDisabled()) return;\n    const slidesLength = swiper.virtual && swiper.params.virtual.enabled ? swiper.virtual.slides.length : swiper.slides.length;\n    let el = swiper.pagination.el;\n    el = makeElementsArray(el);\n    let paginationHTML = '';\n    if (params.type === 'bullets') {\n      let numberOfBullets = swiper.params.loop ? Math.ceil(slidesLength / swiper.params.slidesPerGroup) : swiper.snapGrid.length;\n      if (swiper.params.freeMode && swiper.params.freeMode.enabled && numberOfBullets > slidesLength) {\n        numberOfBullets = slidesLength;\n      }\n      for (let i = 0; i < numberOfBullets; i += 1) {\n        if (params.renderBullet) {\n          paginationHTML += params.renderBullet.call(swiper, i, params.bulletClass);\n        } else {\n          // prettier-ignore\n          paginationHTML += `<${params.bulletElement} ${swiper.isElement ? 'part=\"bullet\"' : ''} class=\"${params.bulletClass}\"></${params.bulletElement}>`;\n        }\n      }\n    }\n    if (params.type === 'fraction') {\n      if (params.renderFraction) {\n        paginationHTML = params.renderFraction.call(swiper, params.currentClass, params.totalClass);\n      } else {\n        paginationHTML = `<span class=\"${params.currentClass}\"></span>` + ' / ' + `<span class=\"${params.totalClass}\"></span>`;\n      }\n    }\n    if (params.type === 'progressbar') {\n      if (params.renderProgressbar) {\n        paginationHTML = params.renderProgressbar.call(swiper, params.progressbarFillClass);\n      } else {\n        paginationHTML = `<span class=\"${params.progressbarFillClass}\"></span>`;\n      }\n    }\n    swiper.pagination.bullets = [];\n    el.forEach(subEl => {\n      if (params.type !== 'custom') {\n        subEl.innerHTML = paginationHTML || '';\n      }\n      if (params.type === 'bullets') {\n        swiper.pagination.bullets.push(...subEl.querySelectorAll(classesToSelector(params.bulletClass)));\n      }\n    });\n    if (params.type !== 'custom') {\n      emit('paginationRender', el[0]);\n    }\n  }\n  function init() {\n    swiper.params.pagination = createElementIfNotDefined(swiper, swiper.originalParams.pagination, swiper.params.pagination, {\n      el: 'swiper-pagination'\n    });\n    const params = swiper.params.pagination;\n    if (!params.el) return;\n    let el;\n    if (typeof params.el === 'string' && swiper.isElement) {\n      el = swiper.el.shadowRoot.querySelector(params.el);\n    }\n    if (!el && typeof params.el === 'string') {\n      el = [...document.querySelectorAll(params.el)];\n    }\n    if (!el) {\n      el = params.el;\n    }\n    if (!el || el.length === 0) return;\n    if (swiper.params.uniqueNavElements && typeof params.el === 'string' && Array.isArray(el) && el.length > 1) {\n      el = [...swiper.el.querySelectorAll(params.el)];\n      // check if it belongs to another nested Swiper\n      if (el.length > 1) {\n        el = el.filter(subEl => {\n          if (elementParents(subEl, '.swiper')[0] !== swiper.el) return false;\n          return true;\n        })[0];\n      }\n    }\n    if (Array.isArray(el) && el.length === 1) el = el[0];\n    Object.assign(swiper.pagination, {\n      el\n    });\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      if (params.type === 'bullets' && params.clickable) {\n        subEl.classList.add(params.clickableClass);\n      }\n      subEl.classList.add(params.modifierClass + params.type);\n      subEl.classList.add(swiper.isHorizontal() ? params.horizontalClass : params.verticalClass);\n      if (params.type === 'bullets' && params.dynamicBullets) {\n        subEl.classList.add(`${params.modifierClass}${params.type}-dynamic`);\n        dynamicBulletIndex = 0;\n        if (params.dynamicMainBullets < 1) {\n          params.dynamicMainBullets = 1;\n        }\n      }\n      if (params.type === 'progressbar' && params.progressbarOpposite) {\n        subEl.classList.add(params.progressbarOppositeClass);\n      }\n      if (params.clickable) {\n        subEl.addEventListener('click', onBulletClick);\n      }\n      if (!swiper.enabled) {\n        subEl.classList.add(params.lockClass);\n      }\n    });\n  }\n  function destroy() {\n    const params = swiper.params.pagination;\n    if (isPaginationDisabled()) return;\n    let el = swiper.pagination.el;\n    if (el) {\n      el = makeElementsArray(el);\n      el.forEach(subEl => {\n        subEl.classList.remove(params.hiddenClass);\n        subEl.classList.remove(params.modifierClass + params.type);\n        subEl.classList.remove(swiper.isHorizontal() ? params.horizontalClass : params.verticalClass);\n        if (params.clickable) {\n          subEl.removeEventListener('click', onBulletClick);\n        }\n      });\n    }\n    if (swiper.pagination.bullets) swiper.pagination.bullets.forEach(subEl => subEl.classList.remove(...params.bulletActiveClass.split(' ')));\n  }\n  on('changeDirection', () => {\n    if (!swiper.pagination || !swiper.pagination.el) return;\n    const params = swiper.params.pagination;\n    let {\n      el\n    } = swiper.pagination;\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.classList.remove(params.horizontalClass, params.verticalClass);\n      subEl.classList.add(swiper.isHorizontal() ? params.horizontalClass : params.verticalClass);\n    });\n  });\n  on('init', () => {\n    if (swiper.params.pagination.enabled === false) {\n      // eslint-disable-next-line\n      disable();\n    } else {\n      init();\n      render();\n      update();\n    }\n  });\n  on('activeIndexChange', () => {\n    if (typeof swiper.snapIndex === 'undefined') {\n      update();\n    }\n  });\n  on('snapIndexChange', () => {\n    update();\n  });\n  on('snapGridLengthChange', () => {\n    render();\n    update();\n  });\n  on('destroy', () => {\n    destroy();\n  });\n  on('enable disable', () => {\n    let {\n      el\n    } = swiper.pagination;\n    if (el) {\n      el = makeElementsArray(el);\n      el.forEach(subEl => subEl.classList[swiper.enabled ? 'remove' : 'add'](swiper.params.pagination.lockClass));\n    }\n  });\n  on('lock unlock', () => {\n    update();\n  });\n  on('click', (_s, e) => {\n    const targetEl = e.target;\n    let {\n      el\n    } = swiper.pagination;\n    if (!Array.isArray(el)) el = [el].filter(element => !!element);\n    if (swiper.params.pagination.el && swiper.params.pagination.hideOnClick && el && el.length > 0 && !targetEl.classList.contains(swiper.params.pagination.bulletClass)) {\n      if (swiper.navigation && (swiper.navigation.nextEl && targetEl === swiper.navigation.nextEl || swiper.navigation.prevEl && targetEl === swiper.navigation.prevEl)) return;\n      const isHidden = el[0].classList.contains(swiper.params.pagination.hiddenClass);\n      if (isHidden === true) {\n        emit('paginationShow');\n      } else {\n        emit('paginationHide');\n      }\n      el.forEach(subEl => subEl.classList.toggle(swiper.params.pagination.hiddenClass));\n    }\n  });\n  const enable = () => {\n    swiper.el.classList.remove(swiper.params.pagination.paginationDisabledClass);\n    let {\n      el\n    } = swiper.pagination;\n    if (el) {\n      el = makeElementsArray(el);\n      el.forEach(subEl => subEl.classList.remove(swiper.params.pagination.paginationDisabledClass));\n    }\n    init();\n    render();\n    update();\n  };\n  const disable = () => {\n    swiper.el.classList.add(swiper.params.pagination.paginationDisabledClass);\n    let {\n      el\n    } = swiper.pagination;\n    if (el) {\n      el = makeElementsArray(el);\n      el.forEach(subEl => subEl.classList.add(swiper.params.pagination.paginationDisabledClass));\n    }\n    destroy();\n  };\n  Object.assign(swiper.pagination, {\n    enable,\n    disable,\n    render,\n    update,\n    init,\n    destroy\n  });\n}"], "mappings": "AAAA,OAAOA,iBAAiB,MAAM,qCAAqC;AACnE,OAAOC,yBAAyB,MAAM,+CAA+C;AACrF,SAASC,YAAY,EAAEC,gBAAgB,EAAEC,cAAc,QAAQ,uBAAuB;AACtF,eAAe,SAASC,UAAUA,CAAAC,IAAA,EAK/B;EAAA,IALgC;IACjCC,MAAM;IACNC,YAAY;IACZC,EAAE;IACFC;EACF,CAAC,GAAAJ,IAAA;EACC,MAAMK,GAAG,GAAG,mBAAmB;EAC/BH,YAAY,CAAC;IACXI,UAAU,EAAE;MACVC,EAAE,EAAE,IAAI;MACRC,aAAa,EAAE,MAAM;MACrBC,SAAS,EAAE,KAAK;MAChBC,WAAW,EAAE,KAAK;MAClBC,YAAY,EAAE,IAAI;MAClBC,iBAAiB,EAAE,IAAI;MACvBC,cAAc,EAAE,IAAI;MACpBC,YAAY,EAAE,IAAI;MAClBC,mBAAmB,EAAE,KAAK;MAC1BC,IAAI,EAAE,SAAS;MACf;MACAC,cAAc,EAAE,KAAK;MACrBC,kBAAkB,EAAE,CAAC;MACrBC,qBAAqB,EAAEC,MAAM,IAAIA,MAAM;MACvCC,mBAAmB,EAAED,MAAM,IAAIA,MAAM;MACrCE,WAAW,EAAG,GAAEjB,GAAI,SAAQ;MAC5BkB,iBAAiB,EAAG,GAAElB,GAAI,gBAAe;MACzCmB,aAAa,EAAG,GAAEnB,GAAI,GAAE;MACxBoB,YAAY,EAAG,GAAEpB,GAAI,UAAS;MAC9BqB,UAAU,EAAG,GAAErB,GAAI,QAAO;MAC1BsB,WAAW,EAAG,GAAEtB,GAAI,SAAQ;MAC5BuB,oBAAoB,EAAG,GAAEvB,GAAI,mBAAkB;MAC/CwB,wBAAwB,EAAG,GAAExB,GAAI,uBAAsB;MACvDyB,cAAc,EAAG,GAAEzB,GAAI,YAAW;MAClC0B,SAAS,EAAG,GAAE1B,GAAI,OAAM;MACxB2B,eAAe,EAAG,GAAE3B,GAAI,aAAY;MACpC4B,aAAa,EAAG,GAAE5B,GAAI,WAAU;MAChC6B,uBAAuB,EAAG,GAAE7B,GAAI;IAClC;EACF,CAAC,CAAC;EACFJ,MAAM,CAACK,UAAU,GAAG;IAClBC,EAAE,EAAE,IAAI;IACR4B,OAAO,EAAE;EACX,CAAC;EACD,IAAIC,UAAU;EACd,IAAIC,kBAAkB,GAAG,CAAC;EAC1B,MAAMC,iBAAiB,GAAG/B,EAAE,IAAI;IAC9B,IAAI,CAACgC,KAAK,CAACC,OAAO,CAACjC,EAAE,CAAC,EAAEA,EAAE,GAAG,CAACA,EAAE,CAAC,CAACkC,MAAM,CAACC,CAAC,IAAI,CAAC,CAACA,CAAC,CAAC;IAClD,OAAOnC,EAAE;EACX,CAAC;EACD,SAASoC,oBAAoBA,CAAA,EAAG;IAC9B,OAAO,CAAC1C,MAAM,CAAC2C,MAAM,CAACtC,UAAU,CAACC,EAAE,IAAI,CAACN,MAAM,CAACK,UAAU,CAACC,EAAE,IAAIgC,KAAK,CAACC,OAAO,CAACvC,MAAM,CAACK,UAAU,CAACC,EAAE,CAAC,IAAIN,MAAM,CAACK,UAAU,CAACC,EAAE,CAACsC,MAAM,KAAK,CAAC;EAC1I;EACA,SAASC,cAAcA,CAACC,QAAQ,EAAEC,QAAQ,EAAE;IAC1C,MAAM;MACJzB;IACF,CAAC,GAAGtB,MAAM,CAAC2C,MAAM,CAACtC,UAAU;IAC5B,IAAI,CAACyC,QAAQ,EAAE;IACfA,QAAQ,GAAGA,QAAQ,CAAE,GAAEC,QAAQ,KAAK,MAAM,GAAG,UAAU,GAAG,MAAO,gBAAe,CAAC;IACjF,IAAID,QAAQ,EAAE;MACZA,QAAQ,CAACE,SAAS,CAACC,GAAG,CAAE,GAAE3B,iBAAkB,IAAGyB,QAAS,EAAC,CAAC;MAC1DD,QAAQ,GAAGA,QAAQ,CAAE,GAAEC,QAAQ,KAAK,MAAM,GAAG,UAAU,GAAG,MAAO,gBAAe,CAAC;MACjF,IAAID,QAAQ,EAAE;QACZA,QAAQ,CAACE,SAAS,CAACC,GAAG,CAAE,GAAE3B,iBAAkB,IAAGyB,QAAS,IAAGA,QAAS,EAAC,CAAC;MACxE;IACF;EACF;EACA,SAASG,aAAaA,CAACT,CAAC,EAAE;IACxB,MAAMK,QAAQ,GAAGL,CAAC,CAACU,MAAM,CAACC,OAAO,CAAC3D,iBAAiB,CAACO,MAAM,CAAC2C,MAAM,CAACtC,UAAU,CAACgB,WAAW,CAAC,CAAC;IAC1F,IAAI,CAACyB,QAAQ,EAAE;MACb;IACF;IACAL,CAAC,CAACY,cAAc,EAAE;IAClB,MAAMC,KAAK,GAAG3D,YAAY,CAACmD,QAAQ,CAAC,GAAG9C,MAAM,CAAC2C,MAAM,CAACY,cAAc;IACnE,IAAIvD,MAAM,CAAC2C,MAAM,CAACa,IAAI,EAAE;MACtB,IAAIxD,MAAM,CAACyD,SAAS,KAAKH,KAAK,EAAE;MAChC,MAAMI,aAAa,GAAG1D,MAAM,CAAC2D,mBAAmB,CAACL,KAAK,CAAC;MACvD,MAAMM,iBAAiB,GAAG5D,MAAM,CAAC2D,mBAAmB,CAAC3D,MAAM,CAACyD,SAAS,CAAC;MACtE,IAAIC,aAAa,GAAG1D,MAAM,CAAC6D,MAAM,CAACjB,MAAM,GAAG5C,MAAM,CAAC8D,YAAY,EAAE;QAC9D9D,MAAM,CAAC+D,OAAO,CAAC;UACbC,SAAS,EAAEN,aAAa,GAAGE,iBAAiB,GAAG,MAAM,GAAG,MAAM;UAC9DK,gBAAgB,EAAEP,aAAa;UAC/BQ,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;MACAlE,MAAM,CAACmE,WAAW,CAACb,KAAK,CAAC;IAC3B,CAAC,MAAM;MACLtD,MAAM,CAACkE,OAAO,CAACZ,KAAK,CAAC;IACvB;EACF;EACA,SAASc,MAAMA,CAAA,EAAG;IAChB;IACA,MAAMC,GAAG,GAAGrE,MAAM,CAACqE,GAAG;IACtB,MAAM1B,MAAM,GAAG3C,MAAM,CAAC2C,MAAM,CAACtC,UAAU;IACvC,IAAIqC,oBAAoB,EAAE,EAAE;IAC5B,IAAIpC,EAAE,GAAGN,MAAM,CAACK,UAAU,CAACC,EAAE;IAC7BA,EAAE,GAAG+B,iBAAiB,CAAC/B,EAAE,CAAC;IAC1B;IACA,IAAIgE,OAAO;IACX,IAAIC,aAAa;IACjB,MAAMC,YAAY,GAAGxE,MAAM,CAACyE,OAAO,IAAIzE,MAAM,CAAC2C,MAAM,CAAC8B,OAAO,CAACC,OAAO,GAAG1E,MAAM,CAACyE,OAAO,CAACZ,MAAM,CAACjB,MAAM,GAAG5C,MAAM,CAAC6D,MAAM,CAACjB,MAAM;IAC1H,MAAM+B,KAAK,GAAG3E,MAAM,CAAC2C,MAAM,CAACa,IAAI,GAAGoB,IAAI,CAACC,IAAI,CAACL,YAAY,GAAGxE,MAAM,CAAC2C,MAAM,CAACY,cAAc,CAAC,GAAGvD,MAAM,CAAC8E,QAAQ,CAAClC,MAAM;IAClH,IAAI5C,MAAM,CAAC2C,MAAM,CAACa,IAAI,EAAE;MACtBe,aAAa,GAAGvE,MAAM,CAAC+E,iBAAiB,IAAI,CAAC;MAC7CT,OAAO,GAAGtE,MAAM,CAAC2C,MAAM,CAACY,cAAc,GAAG,CAAC,GAAGqB,IAAI,CAACI,KAAK,CAAChF,MAAM,CAACyD,SAAS,GAAGzD,MAAM,CAAC2C,MAAM,CAACY,cAAc,CAAC,GAAGvD,MAAM,CAACyD,SAAS;IAC7H,CAAC,MAAM,IAAI,OAAOzD,MAAM,CAACiF,SAAS,KAAK,WAAW,EAAE;MAClDX,OAAO,GAAGtE,MAAM,CAACiF,SAAS;MAC1BV,aAAa,GAAGvE,MAAM,CAACkF,iBAAiB;IAC1C,CAAC,MAAM;MACLX,aAAa,GAAGvE,MAAM,CAACuE,aAAa,IAAI,CAAC;MACzCD,OAAO,GAAGtE,MAAM,CAACmF,WAAW,IAAI,CAAC;IACnC;IACA;IACA,IAAIxC,MAAM,CAAC5B,IAAI,KAAK,SAAS,IAAIf,MAAM,CAACK,UAAU,CAAC6B,OAAO,IAAIlC,MAAM,CAACK,UAAU,CAAC6B,OAAO,CAACU,MAAM,GAAG,CAAC,EAAE;MAClG,MAAMV,OAAO,GAAGlC,MAAM,CAACK,UAAU,CAAC6B,OAAO;MACzC,IAAIkD,UAAU;MACd,IAAIC,SAAS;MACb,IAAIC,QAAQ;MACZ,IAAI3C,MAAM,CAAC3B,cAAc,EAAE;QACzBmB,UAAU,GAAGvC,gBAAgB,CAACsC,OAAO,CAAC,CAAC,CAAC,EAAElC,MAAM,CAACuF,YAAY,EAAE,GAAG,OAAO,GAAG,QAAQ,EAAE,IAAI,CAAC;QAC3FjF,EAAE,CAACkF,OAAO,CAACC,KAAK,IAAI;UAClBA,KAAK,CAACC,KAAK,CAAC1F,MAAM,CAACuF,YAAY,EAAE,GAAG,OAAO,GAAG,QAAQ,CAAC,GAAI,GAAEpD,UAAU,IAAIQ,MAAM,CAAC1B,kBAAkB,GAAG,CAAC,CAAE,IAAG;QAC/G,CAAC,CAAC;QACF,IAAI0B,MAAM,CAAC1B,kBAAkB,GAAG,CAAC,IAAIsD,aAAa,KAAKoB,SAAS,EAAE;UAChEvD,kBAAkB,IAAIkC,OAAO,IAAIC,aAAa,IAAI,CAAC,CAAC;UACpD,IAAInC,kBAAkB,GAAGO,MAAM,CAAC1B,kBAAkB,GAAG,CAAC,EAAE;YACtDmB,kBAAkB,GAAGO,MAAM,CAAC1B,kBAAkB,GAAG,CAAC;UACpD,CAAC,MAAM,IAAImB,kBAAkB,GAAG,CAAC,EAAE;YACjCA,kBAAkB,GAAG,CAAC;UACxB;QACF;QACAgD,UAAU,GAAGR,IAAI,CAACgB,GAAG,CAACtB,OAAO,GAAGlC,kBAAkB,EAAE,CAAC,CAAC;QACtDiD,SAAS,GAAGD,UAAU,IAAIR,IAAI,CAACiB,GAAG,CAAC3D,OAAO,CAACU,MAAM,EAAED,MAAM,CAAC1B,kBAAkB,CAAC,GAAG,CAAC,CAAC;QAClFqE,QAAQ,GAAG,CAACD,SAAS,GAAGD,UAAU,IAAI,CAAC;MACzC;MACAlD,OAAO,CAACsD,OAAO,CAAC1C,QAAQ,IAAI;QAC1B,MAAMgD,eAAe,GAAG,CAAC,GAAG,CAAC,EAAE,EAAE,OAAO,EAAE,YAAY,EAAE,OAAO,EAAE,YAAY,EAAE,OAAO,CAAC,CAACC,GAAG,CAACC,MAAM,IAAK,GAAErD,MAAM,CAACrB,iBAAkB,GAAE0E,MAAO,EAAC,CAAC,CAAC,CAACD,GAAG,CAACE,CAAC,IAAI,OAAOA,CAAC,KAAK,QAAQ,IAAIA,CAAC,CAACC,QAAQ,CAAC,GAAG,CAAC,GAAGD,CAAC,CAACE,KAAK,CAAC,GAAG,CAAC,GAAGF,CAAC,CAAC,CAACG,IAAI,EAAE;QAC3NtD,QAAQ,CAACE,SAAS,CAACqD,MAAM,CAAC,GAAGP,eAAe,CAAC;MAC/C,CAAC,CAAC;MACF,IAAIxF,EAAE,CAACsC,MAAM,GAAG,CAAC,EAAE;QACjBV,OAAO,CAACsD,OAAO,CAACc,MAAM,IAAI;UACxB,MAAMC,WAAW,GAAG5G,YAAY,CAAC2G,MAAM,CAAC;UACxC,IAAIC,WAAW,KAAKjC,OAAO,EAAE;YAC3BgC,MAAM,CAACtD,SAAS,CAACC,GAAG,CAAC,GAAGN,MAAM,CAACrB,iBAAiB,CAAC6E,KAAK,CAAC,GAAG,CAAC,CAAC;UAC9D,CAAC,MAAM,IAAInG,MAAM,CAACwG,SAAS,EAAE;YAC3BF,MAAM,CAACG,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC;UACvC;UACA,IAAI9D,MAAM,CAAC3B,cAAc,EAAE;YACzB,IAAIuF,WAAW,IAAInB,UAAU,IAAImB,WAAW,IAAIlB,SAAS,EAAE;cACzDiB,MAAM,CAACtD,SAAS,CAACC,GAAG,CAAC,GAAI,GAAEN,MAAM,CAACrB,iBAAkB,OAAM,CAAC6E,KAAK,CAAC,GAAG,CAAC,CAAC;YACxE;YACA,IAAII,WAAW,KAAKnB,UAAU,EAAE;cAC9BvC,cAAc,CAACyD,MAAM,EAAE,MAAM,CAAC;YAChC;YACA,IAAIC,WAAW,KAAKlB,SAAS,EAAE;cAC7BxC,cAAc,CAACyD,MAAM,EAAE,MAAM,CAAC;YAChC;UACF;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,MAAMA,MAAM,GAAGpE,OAAO,CAACoC,OAAO,CAAC;QAC/B,IAAIgC,MAAM,EAAE;UACVA,MAAM,CAACtD,SAAS,CAACC,GAAG,CAAC,GAAGN,MAAM,CAACrB,iBAAiB,CAAC6E,KAAK,CAAC,GAAG,CAAC,CAAC;QAC9D;QACA,IAAInG,MAAM,CAACwG,SAAS,EAAE;UACpBtE,OAAO,CAACsD,OAAO,CAAC,CAAC1C,QAAQ,EAAEyD,WAAW,KAAK;YACzCzD,QAAQ,CAAC2D,YAAY,CAAC,MAAM,EAAEF,WAAW,KAAKjC,OAAO,GAAG,eAAe,GAAG,QAAQ,CAAC;UACrF,CAAC,CAAC;QACJ;QACA,IAAI3B,MAAM,CAAC3B,cAAc,EAAE;UACzB,MAAM0F,oBAAoB,GAAGxE,OAAO,CAACkD,UAAU,CAAC;UAChD,MAAMuB,mBAAmB,GAAGzE,OAAO,CAACmD,SAAS,CAAC;UAC9C,KAAK,IAAIuB,CAAC,GAAGxB,UAAU,EAAEwB,CAAC,IAAIvB,SAAS,EAAEuB,CAAC,IAAI,CAAC,EAAE;YAC/C,IAAI1E,OAAO,CAAC0E,CAAC,CAAC,EAAE;cACd1E,OAAO,CAAC0E,CAAC,CAAC,CAAC5D,SAAS,CAACC,GAAG,CAAC,GAAI,GAAEN,MAAM,CAACrB,iBAAkB,OAAM,CAAC6E,KAAK,CAAC,GAAG,CAAC,CAAC;YAC5E;UACF;UACAtD,cAAc,CAAC6D,oBAAoB,EAAE,MAAM,CAAC;UAC5C7D,cAAc,CAAC8D,mBAAmB,EAAE,MAAM,CAAC;QAC7C;MACF;MACA,IAAIhE,MAAM,CAAC3B,cAAc,EAAE;QACzB,MAAM6F,oBAAoB,GAAGjC,IAAI,CAACiB,GAAG,CAAC3D,OAAO,CAACU,MAAM,EAAED,MAAM,CAAC1B,kBAAkB,GAAG,CAAC,CAAC;QACpF,MAAM6F,aAAa,GAAG,CAAC3E,UAAU,GAAG0E,oBAAoB,GAAG1E,UAAU,IAAI,CAAC,GAAGmD,QAAQ,GAAGnD,UAAU;QAClG,MAAM4E,UAAU,GAAG1C,GAAG,GAAG,OAAO,GAAG,MAAM;QACzCnC,OAAO,CAACsD,OAAO,CAACc,MAAM,IAAI;UACxBA,MAAM,CAACZ,KAAK,CAAC1F,MAAM,CAACuF,YAAY,EAAE,GAAGwB,UAAU,GAAG,KAAK,CAAC,GAAI,GAAED,aAAc,IAAG;QACjF,CAAC,CAAC;MACJ;IACF;IACAxG,EAAE,CAACkF,OAAO,CAAC,CAACC,KAAK,EAAEuB,UAAU,KAAK;MAChC,IAAIrE,MAAM,CAAC5B,IAAI,KAAK,UAAU,EAAE;QAC9B0E,KAAK,CAACwB,gBAAgB,CAACxH,iBAAiB,CAACkD,MAAM,CAACnB,YAAY,CAAC,CAAC,CAACgE,OAAO,CAAC0B,UAAU,IAAI;UACnFA,UAAU,CAACC,WAAW,GAAGxE,MAAM,CAACzB,qBAAqB,CAACoD,OAAO,GAAG,CAAC,CAAC;QACpE,CAAC,CAAC;QACFmB,KAAK,CAACwB,gBAAgB,CAACxH,iBAAiB,CAACkD,MAAM,CAAClB,UAAU,CAAC,CAAC,CAAC+D,OAAO,CAAC4B,OAAO,IAAI;UAC9EA,OAAO,CAACD,WAAW,GAAGxE,MAAM,CAACvB,mBAAmB,CAACuD,KAAK,CAAC;QACzD,CAAC,CAAC;MACJ;MACA,IAAIhC,MAAM,CAAC5B,IAAI,KAAK,aAAa,EAAE;QACjC,IAAIsG,oBAAoB;QACxB,IAAI1E,MAAM,CAAC7B,mBAAmB,EAAE;UAC9BuG,oBAAoB,GAAGrH,MAAM,CAACuF,YAAY,EAAE,GAAG,UAAU,GAAG,YAAY;QAC1E,CAAC,MAAM;UACL8B,oBAAoB,GAAGrH,MAAM,CAACuF,YAAY,EAAE,GAAG,YAAY,GAAG,UAAU;QAC1E;QACA,MAAM+B,KAAK,GAAG,CAAChD,OAAO,GAAG,CAAC,IAAIK,KAAK;QACnC,IAAI4C,MAAM,GAAG,CAAC;QACd,IAAIC,MAAM,GAAG,CAAC;QACd,IAAIH,oBAAoB,KAAK,YAAY,EAAE;UACzCE,MAAM,GAAGD,KAAK;QAChB,CAAC,MAAM;UACLE,MAAM,GAAGF,KAAK;QAChB;QACA7B,KAAK,CAACwB,gBAAgB,CAACxH,iBAAiB,CAACkD,MAAM,CAAChB,oBAAoB,CAAC,CAAC,CAAC6D,OAAO,CAACiC,UAAU,IAAI;UAC3FA,UAAU,CAAC/B,KAAK,CAACgC,SAAS,GAAI,6BAA4BH,MAAO,YAAWC,MAAO,GAAE;UACrFC,UAAU,CAAC/B,KAAK,CAACiC,kBAAkB,GAAI,GAAE3H,MAAM,CAAC2C,MAAM,CAACiF,KAAM,IAAG;QAClE,CAAC,CAAC;MACJ;MACA,IAAIjF,MAAM,CAAC5B,IAAI,KAAK,QAAQ,IAAI4B,MAAM,CAAC9B,YAAY,EAAE;QACnD4E,KAAK,CAACoC,SAAS,GAAGlF,MAAM,CAAC9B,YAAY,CAACb,MAAM,EAAEsE,OAAO,GAAG,CAAC,EAAEK,KAAK,CAAC;QACjE,IAAIqC,UAAU,KAAK,CAAC,EAAE7G,IAAI,CAAC,kBAAkB,EAAEsF,KAAK,CAAC;MACvD,CAAC,MAAM;QACL,IAAIuB,UAAU,KAAK,CAAC,EAAE7G,IAAI,CAAC,kBAAkB,EAAEsF,KAAK,CAAC;QACrDtF,IAAI,CAAC,kBAAkB,EAAEsF,KAAK,CAAC;MACjC;MACA,IAAIzF,MAAM,CAAC2C,MAAM,CAACmF,aAAa,IAAI9H,MAAM,CAAC0E,OAAO,EAAE;QACjDe,KAAK,CAACzC,SAAS,CAAChD,MAAM,CAAC+H,QAAQ,GAAG,KAAK,GAAG,QAAQ,CAAC,CAACpF,MAAM,CAACb,SAAS,CAAC;MACvE;IACF,CAAC,CAAC;EACJ;EACA,SAASkG,MAAMA,CAAA,EAAG;IAChB;IACA,MAAMrF,MAAM,GAAG3C,MAAM,CAAC2C,MAAM,CAACtC,UAAU;IACvC,IAAIqC,oBAAoB,EAAE,EAAE;IAC5B,MAAM8B,YAAY,GAAGxE,MAAM,CAACyE,OAAO,IAAIzE,MAAM,CAAC2C,MAAM,CAAC8B,OAAO,CAACC,OAAO,GAAG1E,MAAM,CAACyE,OAAO,CAACZ,MAAM,CAACjB,MAAM,GAAG5C,MAAM,CAAC6D,MAAM,CAACjB,MAAM;IAC1H,IAAItC,EAAE,GAAGN,MAAM,CAACK,UAAU,CAACC,EAAE;IAC7BA,EAAE,GAAG+B,iBAAiB,CAAC/B,EAAE,CAAC;IAC1B,IAAI2H,cAAc,GAAG,EAAE;IACvB,IAAItF,MAAM,CAAC5B,IAAI,KAAK,SAAS,EAAE;MAC7B,IAAImH,eAAe,GAAGlI,MAAM,CAAC2C,MAAM,CAACa,IAAI,GAAGoB,IAAI,CAACC,IAAI,CAACL,YAAY,GAAGxE,MAAM,CAAC2C,MAAM,CAACY,cAAc,CAAC,GAAGvD,MAAM,CAAC8E,QAAQ,CAAClC,MAAM;MAC1H,IAAI5C,MAAM,CAAC2C,MAAM,CAACwF,QAAQ,IAAInI,MAAM,CAAC2C,MAAM,CAACwF,QAAQ,CAACzD,OAAO,IAAIwD,eAAe,GAAG1D,YAAY,EAAE;QAC9F0D,eAAe,GAAG1D,YAAY;MAChC;MACA,KAAK,IAAIoC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsB,eAAe,EAAEtB,CAAC,IAAI,CAAC,EAAE;QAC3C,IAAIjE,MAAM,CAACjC,YAAY,EAAE;UACvBuH,cAAc,IAAItF,MAAM,CAACjC,YAAY,CAAC0H,IAAI,CAACpI,MAAM,EAAE4G,CAAC,EAAEjE,MAAM,CAACtB,WAAW,CAAC;QAC3E,CAAC,MAAM;UACL;UACA4G,cAAc,IAAK,IAAGtF,MAAM,CAACpC,aAAc,IAAGP,MAAM,CAACwG,SAAS,GAAG,eAAe,GAAG,EAAG,WAAU7D,MAAM,CAACtB,WAAY,OAAMsB,MAAM,CAACpC,aAAc,GAAE;QAClJ;MACF;IACF;IACA,IAAIoC,MAAM,CAAC5B,IAAI,KAAK,UAAU,EAAE;MAC9B,IAAI4B,MAAM,CAAC/B,cAAc,EAAE;QACzBqH,cAAc,GAAGtF,MAAM,CAAC/B,cAAc,CAACwH,IAAI,CAACpI,MAAM,EAAE2C,MAAM,CAACnB,YAAY,EAAEmB,MAAM,CAAClB,UAAU,CAAC;MAC7F,CAAC,MAAM;QACLwG,cAAc,GAAI,gBAAetF,MAAM,CAACnB,YAAa,WAAU,GAAG,KAAK,GAAI,gBAAemB,MAAM,CAAClB,UAAW,WAAU;MACxH;IACF;IACA,IAAIkB,MAAM,CAAC5B,IAAI,KAAK,aAAa,EAAE;MACjC,IAAI4B,MAAM,CAAChC,iBAAiB,EAAE;QAC5BsH,cAAc,GAAGtF,MAAM,CAAChC,iBAAiB,CAACyH,IAAI,CAACpI,MAAM,EAAE2C,MAAM,CAAChB,oBAAoB,CAAC;MACrF,CAAC,MAAM;QACLsG,cAAc,GAAI,gBAAetF,MAAM,CAAChB,oBAAqB,WAAU;MACzE;IACF;IACA3B,MAAM,CAACK,UAAU,CAAC6B,OAAO,GAAG,EAAE;IAC9B5B,EAAE,CAACkF,OAAO,CAACC,KAAK,IAAI;MAClB,IAAI9C,MAAM,CAAC5B,IAAI,KAAK,QAAQ,EAAE;QAC5B0E,KAAK,CAACoC,SAAS,GAAGI,cAAc,IAAI,EAAE;MACxC;MACA,IAAItF,MAAM,CAAC5B,IAAI,KAAK,SAAS,EAAE;QAC7Bf,MAAM,CAACK,UAAU,CAAC6B,OAAO,CAACmG,IAAI,CAAC,GAAG5C,KAAK,CAACwB,gBAAgB,CAACxH,iBAAiB,CAACkD,MAAM,CAACtB,WAAW,CAAC,CAAC,CAAC;MAClG;IACF,CAAC,CAAC;IACF,IAAIsB,MAAM,CAAC5B,IAAI,KAAK,QAAQ,EAAE;MAC5BZ,IAAI,CAAC,kBAAkB,EAAEG,EAAE,CAAC,CAAC,CAAC,CAAC;IACjC;EACF;EACA,SAASgI,IAAIA,CAAA,EAAG;IACdtI,MAAM,CAAC2C,MAAM,CAACtC,UAAU,GAAGX,yBAAyB,CAACM,MAAM,EAAEA,MAAM,CAACuI,cAAc,CAAClI,UAAU,EAAEL,MAAM,CAAC2C,MAAM,CAACtC,UAAU,EAAE;MACvHC,EAAE,EAAE;IACN,CAAC,CAAC;IACF,MAAMqC,MAAM,GAAG3C,MAAM,CAAC2C,MAAM,CAACtC,UAAU;IACvC,IAAI,CAACsC,MAAM,CAACrC,EAAE,EAAE;IAChB,IAAIA,EAAE;IACN,IAAI,OAAOqC,MAAM,CAACrC,EAAE,KAAK,QAAQ,IAAIN,MAAM,CAACwG,SAAS,EAAE;MACrDlG,EAAE,GAAGN,MAAM,CAACM,EAAE,CAACkI,UAAU,CAACC,aAAa,CAAC9F,MAAM,CAACrC,EAAE,CAAC;IACpD;IACA,IAAI,CAACA,EAAE,IAAI,OAAOqC,MAAM,CAACrC,EAAE,KAAK,QAAQ,EAAE;MACxCA,EAAE,GAAG,CAAC,GAAGoI,QAAQ,CAACzB,gBAAgB,CAACtE,MAAM,CAACrC,EAAE,CAAC,CAAC;IAChD;IACA,IAAI,CAACA,EAAE,EAAE;MACPA,EAAE,GAAGqC,MAAM,CAACrC,EAAE;IAChB;IACA,IAAI,CAACA,EAAE,IAAIA,EAAE,CAACsC,MAAM,KAAK,CAAC,EAAE;IAC5B,IAAI5C,MAAM,CAAC2C,MAAM,CAACgG,iBAAiB,IAAI,OAAOhG,MAAM,CAACrC,EAAE,KAAK,QAAQ,IAAIgC,KAAK,CAACC,OAAO,CAACjC,EAAE,CAAC,IAAIA,EAAE,CAACsC,MAAM,GAAG,CAAC,EAAE;MAC1GtC,EAAE,GAAG,CAAC,GAAGN,MAAM,CAACM,EAAE,CAAC2G,gBAAgB,CAACtE,MAAM,CAACrC,EAAE,CAAC,CAAC;MAC/C;MACA,IAAIA,EAAE,CAACsC,MAAM,GAAG,CAAC,EAAE;QACjBtC,EAAE,GAAGA,EAAE,CAACkC,MAAM,CAACiD,KAAK,IAAI;UACtB,IAAI5F,cAAc,CAAC4F,KAAK,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,KAAKzF,MAAM,CAACM,EAAE,EAAE,OAAO,KAAK;UACnE,OAAO,IAAI;QACb,CAAC,CAAC,CAAC,CAAC,CAAC;MACP;IACF;IACA,IAAIgC,KAAK,CAACC,OAAO,CAACjC,EAAE,CAAC,IAAIA,EAAE,CAACsC,MAAM,KAAK,CAAC,EAAEtC,EAAE,GAAGA,EAAE,CAAC,CAAC,CAAC;IACpDsI,MAAM,CAACC,MAAM,CAAC7I,MAAM,CAACK,UAAU,EAAE;MAC/BC;IACF,CAAC,CAAC;IACFA,EAAE,GAAG+B,iBAAiB,CAAC/B,EAAE,CAAC;IAC1BA,EAAE,CAACkF,OAAO,CAACC,KAAK,IAAI;MAClB,IAAI9C,MAAM,CAAC5B,IAAI,KAAK,SAAS,IAAI4B,MAAM,CAACnC,SAAS,EAAE;QACjDiF,KAAK,CAACzC,SAAS,CAACC,GAAG,CAACN,MAAM,CAACd,cAAc,CAAC;MAC5C;MACA4D,KAAK,CAACzC,SAAS,CAACC,GAAG,CAACN,MAAM,CAACpB,aAAa,GAAGoB,MAAM,CAAC5B,IAAI,CAAC;MACvD0E,KAAK,CAACzC,SAAS,CAACC,GAAG,CAACjD,MAAM,CAACuF,YAAY,EAAE,GAAG5C,MAAM,CAACZ,eAAe,GAAGY,MAAM,CAACX,aAAa,CAAC;MAC1F,IAAIW,MAAM,CAAC5B,IAAI,KAAK,SAAS,IAAI4B,MAAM,CAAC3B,cAAc,EAAE;QACtDyE,KAAK,CAACzC,SAAS,CAACC,GAAG,CAAE,GAAEN,MAAM,CAACpB,aAAc,GAAEoB,MAAM,CAAC5B,IAAK,UAAS,CAAC;QACpEqB,kBAAkB,GAAG,CAAC;QACtB,IAAIO,MAAM,CAAC1B,kBAAkB,GAAG,CAAC,EAAE;UACjC0B,MAAM,CAAC1B,kBAAkB,GAAG,CAAC;QAC/B;MACF;MACA,IAAI0B,MAAM,CAAC5B,IAAI,KAAK,aAAa,IAAI4B,MAAM,CAAC7B,mBAAmB,EAAE;QAC/D2E,KAAK,CAACzC,SAAS,CAACC,GAAG,CAACN,MAAM,CAACf,wBAAwB,CAAC;MACtD;MACA,IAAIe,MAAM,CAACnC,SAAS,EAAE;QACpBiF,KAAK,CAACqD,gBAAgB,CAAC,OAAO,EAAE5F,aAAa,CAAC;MAChD;MACA,IAAI,CAAClD,MAAM,CAAC0E,OAAO,EAAE;QACnBe,KAAK,CAACzC,SAAS,CAACC,GAAG,CAACN,MAAM,CAACb,SAAS,CAAC;MACvC;IACF,CAAC,CAAC;EACJ;EACA,SAASiH,OAAOA,CAAA,EAAG;IACjB,MAAMpG,MAAM,GAAG3C,MAAM,CAAC2C,MAAM,CAACtC,UAAU;IACvC,IAAIqC,oBAAoB,EAAE,EAAE;IAC5B,IAAIpC,EAAE,GAAGN,MAAM,CAACK,UAAU,CAACC,EAAE;IAC7B,IAAIA,EAAE,EAAE;MACNA,EAAE,GAAG+B,iBAAiB,CAAC/B,EAAE,CAAC;MAC1BA,EAAE,CAACkF,OAAO,CAACC,KAAK,IAAI;QAClBA,KAAK,CAACzC,SAAS,CAACqD,MAAM,CAAC1D,MAAM,CAACjB,WAAW,CAAC;QAC1C+D,KAAK,CAACzC,SAAS,CAACqD,MAAM,CAAC1D,MAAM,CAACpB,aAAa,GAAGoB,MAAM,CAAC5B,IAAI,CAAC;QAC1D0E,KAAK,CAACzC,SAAS,CAACqD,MAAM,CAACrG,MAAM,CAACuF,YAAY,EAAE,GAAG5C,MAAM,CAACZ,eAAe,GAAGY,MAAM,CAACX,aAAa,CAAC;QAC7F,IAAIW,MAAM,CAACnC,SAAS,EAAE;UACpBiF,KAAK,CAACuD,mBAAmB,CAAC,OAAO,EAAE9F,aAAa,CAAC;QACnD;MACF,CAAC,CAAC;IACJ;IACA,IAAIlD,MAAM,CAACK,UAAU,CAAC6B,OAAO,EAAElC,MAAM,CAACK,UAAU,CAAC6B,OAAO,CAACsD,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACzC,SAAS,CAACqD,MAAM,CAAC,GAAG1D,MAAM,CAACrB,iBAAiB,CAAC6E,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;EAC3I;EACAjG,EAAE,CAAC,iBAAiB,EAAE,MAAM;IAC1B,IAAI,CAACF,MAAM,CAACK,UAAU,IAAI,CAACL,MAAM,CAACK,UAAU,CAACC,EAAE,EAAE;IACjD,MAAMqC,MAAM,GAAG3C,MAAM,CAAC2C,MAAM,CAACtC,UAAU;IACvC,IAAI;MACFC;IACF,CAAC,GAAGN,MAAM,CAACK,UAAU;IACrBC,EAAE,GAAG+B,iBAAiB,CAAC/B,EAAE,CAAC;IAC1BA,EAAE,CAACkF,OAAO,CAACC,KAAK,IAAI;MAClBA,KAAK,CAACzC,SAAS,CAACqD,MAAM,CAAC1D,MAAM,CAACZ,eAAe,EAAEY,MAAM,CAACX,aAAa,CAAC;MACpEyD,KAAK,CAACzC,SAAS,CAACC,GAAG,CAACjD,MAAM,CAACuF,YAAY,EAAE,GAAG5C,MAAM,CAACZ,eAAe,GAAGY,MAAM,CAACX,aAAa,CAAC;IAC5F,CAAC,CAAC;EACJ,CAAC,CAAC;EACF9B,EAAE,CAAC,MAAM,EAAE,MAAM;IACf,IAAIF,MAAM,CAAC2C,MAAM,CAACtC,UAAU,CAACqE,OAAO,KAAK,KAAK,EAAE;MAC9C;MACAuE,OAAO,EAAE;IACX,CAAC,MAAM;MACLX,IAAI,EAAE;MACNN,MAAM,EAAE;MACR5D,MAAM,EAAE;IACV;EACF,CAAC,CAAC;EACFlE,EAAE,CAAC,mBAAmB,EAAE,MAAM;IAC5B,IAAI,OAAOF,MAAM,CAACiF,SAAS,KAAK,WAAW,EAAE;MAC3Cb,MAAM,EAAE;IACV;EACF,CAAC,CAAC;EACFlE,EAAE,CAAC,iBAAiB,EAAE,MAAM;IAC1BkE,MAAM,EAAE;EACV,CAAC,CAAC;EACFlE,EAAE,CAAC,sBAAsB,EAAE,MAAM;IAC/B8H,MAAM,EAAE;IACR5D,MAAM,EAAE;EACV,CAAC,CAAC;EACFlE,EAAE,CAAC,SAAS,EAAE,MAAM;IAClB6I,OAAO,EAAE;EACX,CAAC,CAAC;EACF7I,EAAE,CAAC,gBAAgB,EAAE,MAAM;IACzB,IAAI;MACFI;IACF,CAAC,GAAGN,MAAM,CAACK,UAAU;IACrB,IAAIC,EAAE,EAAE;MACNA,EAAE,GAAG+B,iBAAiB,CAAC/B,EAAE,CAAC;MAC1BA,EAAE,CAACkF,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACzC,SAAS,CAAChD,MAAM,CAAC0E,OAAO,GAAG,QAAQ,GAAG,KAAK,CAAC,CAAC1E,MAAM,CAAC2C,MAAM,CAACtC,UAAU,CAACyB,SAAS,CAAC,CAAC;IAC7G;EACF,CAAC,CAAC;EACF5B,EAAE,CAAC,aAAa,EAAE,MAAM;IACtBkE,MAAM,EAAE;EACV,CAAC,CAAC;EACFlE,EAAE,CAAC,OAAO,EAAE,CAACgJ,EAAE,EAAEzG,CAAC,KAAK;IACrB,MAAM0G,QAAQ,GAAG1G,CAAC,CAACU,MAAM;IACzB,IAAI;MACF7C;IACF,CAAC,GAAGN,MAAM,CAACK,UAAU;IACrB,IAAI,CAACiC,KAAK,CAACC,OAAO,CAACjC,EAAE,CAAC,EAAEA,EAAE,GAAG,CAACA,EAAE,CAAC,CAACkC,MAAM,CAAC4G,OAAO,IAAI,CAAC,CAACA,OAAO,CAAC;IAC9D,IAAIpJ,MAAM,CAAC2C,MAAM,CAACtC,UAAU,CAACC,EAAE,IAAIN,MAAM,CAAC2C,MAAM,CAACtC,UAAU,CAACI,WAAW,IAAIH,EAAE,IAAIA,EAAE,CAACsC,MAAM,GAAG,CAAC,IAAI,CAACuG,QAAQ,CAACnG,SAAS,CAACqG,QAAQ,CAACrJ,MAAM,CAAC2C,MAAM,CAACtC,UAAU,CAACgB,WAAW,CAAC,EAAE;MACpK,IAAIrB,MAAM,CAACsJ,UAAU,KAAKtJ,MAAM,CAACsJ,UAAU,CAACC,MAAM,IAAIJ,QAAQ,KAAKnJ,MAAM,CAACsJ,UAAU,CAACC,MAAM,IAAIvJ,MAAM,CAACsJ,UAAU,CAACE,MAAM,IAAIL,QAAQ,KAAKnJ,MAAM,CAACsJ,UAAU,CAACE,MAAM,CAAC,EAAE;MACnK,MAAMC,QAAQ,GAAGnJ,EAAE,CAAC,CAAC,CAAC,CAAC0C,SAAS,CAACqG,QAAQ,CAACrJ,MAAM,CAAC2C,MAAM,CAACtC,UAAU,CAACqB,WAAW,CAAC;MAC/E,IAAI+H,QAAQ,KAAK,IAAI,EAAE;QACrBtJ,IAAI,CAAC,gBAAgB,CAAC;MACxB,CAAC,MAAM;QACLA,IAAI,CAAC,gBAAgB,CAAC;MACxB;MACAG,EAAE,CAACkF,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACzC,SAAS,CAAC0G,MAAM,CAAC1J,MAAM,CAAC2C,MAAM,CAACtC,UAAU,CAACqB,WAAW,CAAC,CAAC;IACnF;EACF,CAAC,CAAC;EACF,MAAMiI,MAAM,GAAGA,CAAA,KAAM;IACnB3J,MAAM,CAACM,EAAE,CAAC0C,SAAS,CAACqD,MAAM,CAACrG,MAAM,CAAC2C,MAAM,CAACtC,UAAU,CAAC4B,uBAAuB,CAAC;IAC5E,IAAI;MACF3B;IACF,CAAC,GAAGN,MAAM,CAACK,UAAU;IACrB,IAAIC,EAAE,EAAE;MACNA,EAAE,GAAG+B,iBAAiB,CAAC/B,EAAE,CAAC;MAC1BA,EAAE,CAACkF,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACzC,SAAS,CAACqD,MAAM,CAACrG,MAAM,CAAC2C,MAAM,CAACtC,UAAU,CAAC4B,uBAAuB,CAAC,CAAC;IAC/F;IACAqG,IAAI,EAAE;IACNN,MAAM,EAAE;IACR5D,MAAM,EAAE;EACV,CAAC;EACD,MAAM6E,OAAO,GAAGA,CAAA,KAAM;IACpBjJ,MAAM,CAACM,EAAE,CAAC0C,SAAS,CAACC,GAAG,CAACjD,MAAM,CAAC2C,MAAM,CAACtC,UAAU,CAAC4B,uBAAuB,CAAC;IACzE,IAAI;MACF3B;IACF,CAAC,GAAGN,MAAM,CAACK,UAAU;IACrB,IAAIC,EAAE,EAAE;MACNA,EAAE,GAAG+B,iBAAiB,CAAC/B,EAAE,CAAC;MAC1BA,EAAE,CAACkF,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACzC,SAAS,CAACC,GAAG,CAACjD,MAAM,CAAC2C,MAAM,CAACtC,UAAU,CAAC4B,uBAAuB,CAAC,CAAC;IAC5F;IACA8G,OAAO,EAAE;EACX,CAAC;EACDH,MAAM,CAACC,MAAM,CAAC7I,MAAM,CAACK,UAAU,EAAE;IAC/BsJ,MAAM;IACNV,OAAO;IACPjB,MAAM;IACN5D,MAAM;IACNkE,IAAI;IACJS;EACF,CAAC,CAAC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}