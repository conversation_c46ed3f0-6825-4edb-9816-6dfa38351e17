{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\");\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nvar _classCallCheck2 = _interopRequireDefault(require(\"@babel/runtime/helpers/classCallCheck\"));\nvar _createClass2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createClass\"));\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"@babel/runtime/helpers/possibleConstructorReturn\"));\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"@babel/runtime/helpers/getPrototypeOf\"));\nvar _inherits2 = _interopRequireDefault(require(\"@babel/runtime/helpers/inherits\"));\nvar _react = _interopRequireWildcard(require(\"react\"));\nvar _PlaybackRateMenuButton = _interopRequireDefault(require(\"./PlaybackRateMenuButton\"));\nvar _utils = require(\"../../utils\");\nvar PlaybackRate = /*#__PURE__*/\nfunction (_Component) {\n  (0, _inherits2[\"default\"])(PlaybackRate, _Component);\n  function PlaybackRate(props, context) {\n    var _this;\n    (0, _classCallCheck2[\"default\"])(this, PlaybackRate);\n    _this = (0, _possibleConstructorReturn2[\"default\"])(this, (0, _getPrototypeOf2[\"default\"])(PlaybackRate).call(this, props, context));\n    (0, _utils.deprecatedWarning)('PlaybackRate', 'PlaybackRateMenuButton');\n    return _this;\n  }\n  (0, _createClass2[\"default\"])(PlaybackRate, [{\n    key: \"render\",\n    value: function render() {\n      return _react[\"default\"].createElement(_PlaybackRateMenuButton[\"default\"], this.props);\n    }\n  }]);\n  return PlaybackRate;\n}(_react.Component);\nexports[\"default\"] = PlaybackRate;\nPlaybackRate.displayName = 'PlaybackRate';", "map": {"version": 3, "names": ["_interopRequireWildcard", "require", "_interopRequireDefault", "Object", "defineProperty", "exports", "value", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_inherits2", "_react", "_PlaybackRateMenuButton", "_utils", "PlaybackRate", "_Component", "props", "context", "_this", "call", "deprecatedWarning", "key", "render", "createElement", "Component", "displayName"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/video-react/lib/components/control-bar/PlaybackRate.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\");\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _classCallCheck2 = _interopRequireDefault(require(\"@babel/runtime/helpers/classCallCheck\"));\n\nvar _createClass2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createClass\"));\n\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"@babel/runtime/helpers/possibleConstructorReturn\"));\n\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"@babel/runtime/helpers/getPrototypeOf\"));\n\nvar _inherits2 = _interopRequireDefault(require(\"@babel/runtime/helpers/inherits\"));\n\nvar _react = _interopRequireWildcard(require(\"react\"));\n\nvar _PlaybackRateMenuButton = _interopRequireDefault(require(\"./PlaybackRateMenuButton\"));\n\nvar _utils = require(\"../../utils\");\n\nvar PlaybackRate =\n/*#__PURE__*/\nfunction (_Component) {\n  (0, _inherits2[\"default\"])(PlaybackRate, _Component);\n\n  function PlaybackRate(props, context) {\n    var _this;\n\n    (0, _classCallCheck2[\"default\"])(this, PlaybackRate);\n    _this = (0, _possibleConstructorReturn2[\"default\"])(this, (0, _getPrototypeOf2[\"default\"])(PlaybackRate).call(this, props, context));\n    (0, _utils.deprecatedWarning)('PlaybackRate', 'PlaybackRateMenuButton');\n    return _this;\n  }\n\n  (0, _createClass2[\"default\"])(PlaybackRate, [{\n    key: \"render\",\n    value: function render() {\n      return _react[\"default\"].createElement(_PlaybackRateMenuButton[\"default\"], this.props);\n    }\n  }]);\n  return PlaybackRate;\n}(_react.Component);\n\nexports[\"default\"] = PlaybackRate;\nPlaybackRate.displayName = 'PlaybackRate';"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,uBAAuB,GAAGC,OAAO,CAAC,+CAA+C,CAAC;AAEtF,IAAIC,sBAAsB,GAAGD,OAAO,CAAC,8CAA8C,CAAC;AAEpFE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;AAE3B,IAAIE,gBAAgB,GAAGL,sBAAsB,CAACD,OAAO,CAAC,uCAAuC,CAAC,CAAC;AAE/F,IAAIO,aAAa,GAAGN,sBAAsB,CAACD,OAAO,CAAC,oCAAoC,CAAC,CAAC;AAEzF,IAAIQ,2BAA2B,GAAGP,sBAAsB,CAACD,OAAO,CAAC,kDAAkD,CAAC,CAAC;AAErH,IAAIS,gBAAgB,GAAGR,sBAAsB,CAACD,OAAO,CAAC,uCAAuC,CAAC,CAAC;AAE/F,IAAIU,UAAU,GAAGT,sBAAsB,CAACD,OAAO,CAAC,iCAAiC,CAAC,CAAC;AAEnF,IAAIW,MAAM,GAAGZ,uBAAuB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AAEtD,IAAIY,uBAAuB,GAAGX,sBAAsB,CAACD,OAAO,CAAC,0BAA0B,CAAC,CAAC;AAEzF,IAAIa,MAAM,GAAGb,OAAO,CAAC,aAAa,CAAC;AAEnC,IAAIc,YAAY,GAChB;AACA,UAAUC,UAAU,EAAE;EACpB,CAAC,CAAC,EAAEL,UAAU,CAAC,SAAS,CAAC,EAAEI,YAAY,EAAEC,UAAU,CAAC;EAEpD,SAASD,YAAYA,CAACE,KAAK,EAAEC,OAAO,EAAE;IACpC,IAAIC,KAAK;IAET,CAAC,CAAC,EAAEZ,gBAAgB,CAAC,SAAS,CAAC,EAAE,IAAI,EAAEQ,YAAY,CAAC;IACpDI,KAAK,GAAG,CAAC,CAAC,EAAEV,2BAA2B,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAEC,gBAAgB,CAAC,SAAS,CAAC,EAAEK,YAAY,CAAC,CAACK,IAAI,CAAC,IAAI,EAAEH,KAAK,EAAEC,OAAO,CAAC,CAAC;IACpI,CAAC,CAAC,EAAEJ,MAAM,CAACO,iBAAiB,EAAE,cAAc,EAAE,wBAAwB,CAAC;IACvE,OAAOF,KAAK;EACd;EAEA,CAAC,CAAC,EAAEX,aAAa,CAAC,SAAS,CAAC,EAAEO,YAAY,EAAE,CAAC;IAC3CO,GAAG,EAAE,QAAQ;IACbhB,KAAK,EAAE,SAASiB,MAAMA,CAAA,EAAG;MACvB,OAAOX,MAAM,CAAC,SAAS,CAAC,CAACY,aAAa,CAACX,uBAAuB,CAAC,SAAS,CAAC,EAAE,IAAI,CAACI,KAAK,CAAC;IACxF;EACF,CAAC,CAAC,CAAC;EACH,OAAOF,YAAY;AACrB,CAAC,CAACH,MAAM,CAACa,SAAS,CAAC;AAEnBpB,OAAO,CAAC,SAAS,CAAC,GAAGU,YAAY;AACjCA,YAAY,CAACW,WAAW,GAAG,cAAc"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}