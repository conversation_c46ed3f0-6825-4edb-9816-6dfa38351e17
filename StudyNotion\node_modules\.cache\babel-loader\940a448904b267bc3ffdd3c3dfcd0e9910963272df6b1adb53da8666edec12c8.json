{"ast": null, "code": "export const processLazyPreloader = (swiper, imageEl) => {\n  if (!swiper || swiper.destroyed || !swiper.params) return;\n  const slideSelector = () => swiper.isElement ? `swiper-slide` : `.${swiper.params.slideClass}`;\n  const slideEl = imageEl.closest(slideSelector());\n  if (slideEl) {\n    const lazyEl = slideEl.querySelector(`.${swiper.params.lazyPreloaderClass}`);\n    if (lazyEl) lazyEl.remove();\n  }\n};\nconst unlazy = (swiper, index) => {\n  if (!swiper.slides[index]) return;\n  const imageEl = swiper.slides[index].querySelector('[loading=\"lazy\"]');\n  if (imageEl) imageEl.removeAttribute('loading');\n};\nexport const preload = swiper => {\n  if (!swiper || swiper.destroyed || !swiper.params) return;\n  let amount = swiper.params.lazyPreloadPrevNext;\n  const len = swiper.slides.length;\n  if (!len || !amount || amount < 0) return;\n  amount = Math.min(amount, len);\n  const slidesPerView = swiper.params.slidesPerView === 'auto' ? swiper.slidesPerViewDynamic() : Math.ceil(swiper.params.slidesPerView);\n  const activeIndex = swiper.activeIndex;\n  if (swiper.params.grid && swiper.params.grid.rows > 1) {\n    const activeColumn = activeIndex;\n    const preloadColumns = [activeColumn - amount];\n    preloadColumns.push(...Array.from({\n      length: amount\n    }).map((_, i) => {\n      return activeColumn + slidesPerView + i;\n    }));\n    swiper.slides.forEach((slideEl, i) => {\n      if (preloadColumns.includes(slideEl.column)) unlazy(swiper, i);\n    });\n    return;\n  }\n  const slideIndexLastInView = activeIndex + slidesPerView - 1;\n  if (swiper.params.rewind || swiper.params.loop) {\n    for (let i = activeIndex - amount; i <= slideIndexLastInView + amount; i += 1) {\n      const realIndex = (i % len + len) % len;\n      if (realIndex < activeIndex || realIndex > slideIndexLastInView) unlazy(swiper, realIndex);\n    }\n  } else {\n    for (let i = Math.max(activeIndex - amount, 0); i <= Math.min(slideIndexLastInView + amount, len - 1); i += 1) {\n      if (i !== activeIndex && (i > slideIndexLastInView || i < activeIndex)) {\n        unlazy(swiper, i);\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["processLazyPreloader", "swiper", "imageEl", "destroyed", "params", "slideSelector", "isElement", "slideClass", "slideEl", "closest", "lazyEl", "querySelector", "lazyPreloaderClass", "remove", "unlazy", "index", "slides", "removeAttribute", "preload", "amount", "lazyPreloadPrevNext", "len", "length", "Math", "min", "<PERSON><PERSON><PERSON><PERSON>iew", "slidesPerViewDynamic", "ceil", "activeIndex", "grid", "rows", "activeColumn", "preloadColumns", "push", "Array", "from", "map", "_", "i", "for<PERSON>ach", "includes", "column", "slideIndexLastInView", "rewind", "loop", "realIndex", "max"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/shared/process-lazy-preloader.js"], "sourcesContent": ["export const processLazyPreloader = (swiper, imageEl) => {\n  if (!swiper || swiper.destroyed || !swiper.params) return;\n  const slideSelector = () => swiper.isElement ? `swiper-slide` : `.${swiper.params.slideClass}`;\n  const slideEl = imageEl.closest(slideSelector());\n  if (slideEl) {\n    const lazyEl = slideEl.querySelector(`.${swiper.params.lazyPreloaderClass}`);\n    if (lazyEl) lazyEl.remove();\n  }\n};\nconst unlazy = (swiper, index) => {\n  if (!swiper.slides[index]) return;\n  const imageEl = swiper.slides[index].querySelector('[loading=\"lazy\"]');\n  if (imageEl) imageEl.removeAttribute('loading');\n};\nexport const preload = swiper => {\n  if (!swiper || swiper.destroyed || !swiper.params) return;\n  let amount = swiper.params.lazyPreloadPrevNext;\n  const len = swiper.slides.length;\n  if (!len || !amount || amount < 0) return;\n  amount = Math.min(amount, len);\n  const slidesPerView = swiper.params.slidesPerView === 'auto' ? swiper.slidesPerViewDynamic() : Math.ceil(swiper.params.slidesPerView);\n  const activeIndex = swiper.activeIndex;\n  if (swiper.params.grid && swiper.params.grid.rows > 1) {\n    const activeColumn = activeIndex;\n    const preloadColumns = [activeColumn - amount];\n    preloadColumns.push(...Array.from({\n      length: amount\n    }).map((_, i) => {\n      return activeColumn + slidesPerView + i;\n    }));\n    swiper.slides.forEach((slideEl, i) => {\n      if (preloadColumns.includes(slideEl.column)) unlazy(swiper, i);\n    });\n    return;\n  }\n  const slideIndexLastInView = activeIndex + slidesPerView - 1;\n  if (swiper.params.rewind || swiper.params.loop) {\n    for (let i = activeIndex - amount; i <= slideIndexLastInView + amount; i += 1) {\n      const realIndex = (i % len + len) % len;\n      if (realIndex < activeIndex || realIndex > slideIndexLastInView) unlazy(swiper, realIndex);\n    }\n  } else {\n    for (let i = Math.max(activeIndex - amount, 0); i <= Math.min(slideIndexLastInView + amount, len - 1); i += 1) {\n      if (i !== activeIndex && (i > slideIndexLastInView || i < activeIndex)) {\n        unlazy(swiper, i);\n      }\n    }\n  }\n};"], "mappings": "AAAA,OAAO,MAAMA,oBAAoB,GAAGA,CAACC,MAAM,EAAEC,OAAO,KAAK;EACvD,IAAI,CAACD,MAAM,IAAIA,MAAM,CAACE,SAAS,IAAI,CAACF,MAAM,CAACG,MAAM,EAAE;EACnD,MAAMC,aAAa,GAAGA,CAAA,KAAMJ,MAAM,CAACK,SAAS,GAAI,cAAa,GAAI,IAAGL,MAAM,CAACG,MAAM,CAACG,UAAW,EAAC;EAC9F,MAAMC,OAAO,GAAGN,OAAO,CAACO,OAAO,CAACJ,aAAa,EAAE,CAAC;EAChD,IAAIG,OAAO,EAAE;IACX,MAAME,MAAM,GAAGF,OAAO,CAACG,aAAa,CAAE,IAAGV,MAAM,CAACG,MAAM,CAACQ,kBAAmB,EAAC,CAAC;IAC5E,IAAIF,MAAM,EAAEA,MAAM,CAACG,MAAM,EAAE;EAC7B;AACF,CAAC;AACD,MAAMC,MAAM,GAAGA,CAACb,MAAM,EAAEc,KAAK,KAAK;EAChC,IAAI,CAACd,MAAM,CAACe,MAAM,CAACD,KAAK,CAAC,EAAE;EAC3B,MAAMb,OAAO,GAAGD,MAAM,CAACe,MAAM,CAACD,KAAK,CAAC,CAACJ,aAAa,CAAC,kBAAkB,CAAC;EACtE,IAAIT,OAAO,EAAEA,OAAO,CAACe,eAAe,CAAC,SAAS,CAAC;AACjD,CAAC;AACD,OAAO,MAAMC,OAAO,GAAGjB,MAAM,IAAI;EAC/B,IAAI,CAACA,MAAM,IAAIA,MAAM,CAACE,SAAS,IAAI,CAACF,MAAM,CAACG,MAAM,EAAE;EACnD,IAAIe,MAAM,GAAGlB,MAAM,CAACG,MAAM,CAACgB,mBAAmB;EAC9C,MAAMC,GAAG,GAAGpB,MAAM,CAACe,MAAM,CAACM,MAAM;EAChC,IAAI,CAACD,GAAG,IAAI,CAACF,MAAM,IAAIA,MAAM,GAAG,CAAC,EAAE;EACnCA,MAAM,GAAGI,IAAI,CAACC,GAAG,CAACL,MAAM,EAAEE,GAAG,CAAC;EAC9B,MAAMI,aAAa,GAAGxB,MAAM,CAACG,MAAM,CAACqB,aAAa,KAAK,MAAM,GAAGxB,MAAM,CAACyB,oBAAoB,EAAE,GAAGH,IAAI,CAACI,IAAI,CAAC1B,MAAM,CAACG,MAAM,CAACqB,aAAa,CAAC;EACrI,MAAMG,WAAW,GAAG3B,MAAM,CAAC2B,WAAW;EACtC,IAAI3B,MAAM,CAACG,MAAM,CAACyB,IAAI,IAAI5B,MAAM,CAACG,MAAM,CAACyB,IAAI,CAACC,IAAI,GAAG,CAAC,EAAE;IACrD,MAAMC,YAAY,GAAGH,WAAW;IAChC,MAAMI,cAAc,GAAG,CAACD,YAAY,GAAGZ,MAAM,CAAC;IAC9Ca,cAAc,CAACC,IAAI,CAAC,GAAGC,KAAK,CAACC,IAAI,CAAC;MAChCb,MAAM,EAAEH;IACV,CAAC,CAAC,CAACiB,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MACf,OAAOP,YAAY,GAAGN,aAAa,GAAGa,CAAC;IACzC,CAAC,CAAC,CAAC;IACHrC,MAAM,CAACe,MAAM,CAACuB,OAAO,CAAC,CAAC/B,OAAO,EAAE8B,CAAC,KAAK;MACpC,IAAIN,cAAc,CAACQ,QAAQ,CAAChC,OAAO,CAACiC,MAAM,CAAC,EAAE3B,MAAM,CAACb,MAAM,EAAEqC,CAAC,CAAC;IAChE,CAAC,CAAC;IACF;EACF;EACA,MAAMI,oBAAoB,GAAGd,WAAW,GAAGH,aAAa,GAAG,CAAC;EAC5D,IAAIxB,MAAM,CAACG,MAAM,CAACuC,MAAM,IAAI1C,MAAM,CAACG,MAAM,CAACwC,IAAI,EAAE;IAC9C,KAAK,IAAIN,CAAC,GAAGV,WAAW,GAAGT,MAAM,EAAEmB,CAAC,IAAII,oBAAoB,GAAGvB,MAAM,EAAEmB,CAAC,IAAI,CAAC,EAAE;MAC7E,MAAMO,SAAS,GAAG,CAACP,CAAC,GAAGjB,GAAG,GAAGA,GAAG,IAAIA,GAAG;MACvC,IAAIwB,SAAS,GAAGjB,WAAW,IAAIiB,SAAS,GAAGH,oBAAoB,EAAE5B,MAAM,CAACb,MAAM,EAAE4C,SAAS,CAAC;IAC5F;EACF,CAAC,MAAM;IACL,KAAK,IAAIP,CAAC,GAAGf,IAAI,CAACuB,GAAG,CAAClB,WAAW,GAAGT,MAAM,EAAE,CAAC,CAAC,EAAEmB,CAAC,IAAIf,IAAI,CAACC,GAAG,CAACkB,oBAAoB,GAAGvB,MAAM,EAAEE,GAAG,GAAG,CAAC,CAAC,EAAEiB,CAAC,IAAI,CAAC,EAAE;MAC7G,IAAIA,CAAC,KAAKV,WAAW,KAAKU,CAAC,GAAGI,oBAAoB,IAAIJ,CAAC,GAAGV,WAAW,CAAC,EAAE;QACtEd,MAAM,CAACb,MAAM,EAAEqC,CAAC,CAAC;MACnB;IACF;EACF;AACF,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}