{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\temp\\\\StudyNotion\\\\src\\\\components\\\\core\\\\HomePage\\\\CourseCard.jsx\";\nimport React from \"react\";\n\n// Importing React Icons\nimport { HiUsers } from \"react-icons/hi\";\nimport { ImTree } from \"react-icons/im\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CourseCard = _ref => {\n  let {\n    cardData,\n    currentCard,\n    setCurrentCard\n  } = _ref;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `w-[360px] lg:w-[30%] ${currentCard === (cardData === null || cardData === void 0 ? void 0 : cardData.heading) ? \"bg-white shadow-[12px_12px_0_0] shadow-yellow-50\" : \"bg-richblack-800\"}  text-richblack-25 h-[300px] box-border cursor-pointer`,\n    onClick: () => setCurrentCard(cardData === null || cardData === void 0 ? void 0 : cardData.heading),\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"border-b-[2px] border-richblack-400 border-dashed h-[80%] p-6 flex flex-col gap-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: ` ${currentCard === (cardData === null || cardData === void 0 ? void 0 : cardData.heading) && \"text-richblack-800\"} font-semibold text-[20px]`,\n        children: cardData === null || cardData === void 0 ? void 0 : cardData.heading\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-richblack-400\",\n        children: cardData === null || cardData === void 0 ? void 0 : cardData.description\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `flex justify-between ${currentCard === (cardData === null || cardData === void 0 ? void 0 : cardData.heading) ? \"text-blue-300\" : \"text-richblack-300\"} px-6 py-3 font-medium`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-2 text-[16px]\",\n        children: [/*#__PURE__*/_jsxDEV(HiUsers, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: cardData === null || cardData === void 0 ? void 0 : cardData.level\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-2 text-[16px]\",\n        children: [/*#__PURE__*/_jsxDEV(ImTree, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [cardData === null || cardData === void 0 ? void 0 : cardData.lessionNumber, \" Lession\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 5\n  }, this);\n};\n_c = CourseCard;\nexport default CourseCard;\nvar _c;\n$RefreshReg$(_c, \"CourseCard\");", "map": {"version": 3, "names": ["React", "HiUsers", "ImTree", "jsxDEV", "_jsxDEV", "CourseCard", "_ref", "cardData", "currentCard", "setCurrentCard", "className", "heading", "onClick", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "description", "level", "lessionNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/src/components/core/HomePage/CourseCard.jsx"], "sourcesContent": ["import React from \"react\";\r\n\r\n// Importing React Icons\r\nimport { HiUsers } from \"react-icons/hi\";\r\nimport { ImTree } from \"react-icons/im\";\r\n\r\nconst CourseCard = ({cardData, currentCard, setCurrentCard}) => {\r\n  return (\r\n    <div\r\n      className={`w-[360px] lg:w-[30%] ${\r\n        currentCard === cardData?.heading\r\n          ? \"bg-white shadow-[12px_12px_0_0] shadow-yellow-50\"\r\n          : \"bg-richblack-800\"\r\n      }  text-richblack-25 h-[300px] box-border cursor-pointer`}\r\n      onClick={() => setCurrentCard(cardData?.heading)}\r\n    >\r\n      <div className=\"border-b-[2px] border-richblack-400 border-dashed h-[80%] p-6 flex flex-col gap-3\">\r\n        <div\r\n          className={` ${\r\n            currentCard === cardData?.heading && \"text-richblack-800\"\r\n          } font-semibold text-[20px]`}\r\n        >\r\n          {cardData?.heading}\r\n        </div>\r\n\r\n        <div className=\"text-richblack-400\">{cardData?.description}</div>\r\n      </div>\r\n\r\n      <div\r\n        className={`flex justify-between ${\r\n          currentCard === cardData?.heading ? \"text-blue-300\" : \"text-richblack-300\"\r\n        } px-6 py-3 font-medium`}\r\n      >\r\n        {/* Level */}\r\n        <div className=\"flex items-center gap-2 text-[16px]\">\r\n          <HiUsers />\r\n          <p>{cardData?.level}</p>\r\n        </div>\r\n\r\n        {/* Flow Chart */}\r\n        <div className=\"flex items-center gap-2 text-[16px]\">\r\n          <ImTree />\r\n          <p>{cardData?.lessionNumber} Lession</p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CourseCard;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA,SAASC,OAAO,QAAQ,gBAAgB;AACxC,SAASC,MAAM,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,UAAU,GAAGC,IAAA,IAA6C;EAAA,IAA5C;IAACC,QAAQ;IAAEC,WAAW;IAAEC;EAAc,CAAC,GAAAH,IAAA;EACzD,oBACEF,OAAA;IACEM,SAAS,EAAG,wBACVF,WAAW,MAAKD,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEI,OAAO,IAC7B,kDAAkD,GAClD,kBACL,yDAAyD;IAC1DC,OAAO,EAAEA,CAAA,KAAMH,cAAc,CAACF,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEI,OAAO,CAAE;IAAAE,QAAA,gBAEjDT,OAAA;MAAKM,SAAS,EAAC,mFAAmF;MAAAG,QAAA,gBAChGT,OAAA;QACEM,SAAS,EAAG,IACVF,WAAW,MAAKD,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEI,OAAO,KAAI,oBACtC,4BAA4B;QAAAE,QAAA,EAE5BN,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEI;MAAO;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACd,eAENb,OAAA;QAAKM,SAAS,EAAC,oBAAoB;QAAAG,QAAA,EAAEN,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEW;MAAW;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAAO;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAC7D,eAENb,OAAA;MACEM,SAAS,EAAG,wBACVF,WAAW,MAAKD,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEI,OAAO,IAAG,eAAe,GAAG,oBACvD,wBAAwB;MAAAE,QAAA,gBAGzBT,OAAA;QAAKM,SAAS,EAAC,qCAAqC;QAAAG,QAAA,gBAClDT,OAAA,CAACH,OAAO;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAG,eACXb,OAAA;UAAAS,QAAA,EAAIN,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEY;QAAK;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAK;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACpB,eAGNb,OAAA;QAAKM,SAAS,EAAC,qCAAqC;QAAAG,QAAA,gBAClDT,OAAA,CAACF,MAAM;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAG,eACVb,OAAA;UAAAS,QAAA,GAAIN,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEa,aAAa,EAAC,UAAQ;QAAA;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAI;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACpC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACF;AAEV,CAAC;AAACI,EAAA,GAzCIhB,UAAU;AA2ChB,eAAeA,UAAU;AAAC,IAAAgB,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}