{"ast": null, "code": "/**\n * @typedef {import('micromark-util-types').Encoding} Encoding\n * @typedef {import('micromark-util-types').Event} Event\n * @typedef {import('micromark-util-types').ParseOptions} ParseOptions\n * @typedef {import('micromark-util-types').Token} Token\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Value} Value\n *\n * @typedef {import('unist').Parent} UnistParent\n * @typedef {import('unist').Point} Point\n *\n * @typedef {import('mdast').PhrasingContent} PhrasingContent\n * @typedef {import('mdast').StaticPhrasingContent} StaticPhrasingContent\n * @typedef {import('mdast').Content} Content\n * @typedef {import('mdast').Break} Break\n * @typedef {import('mdast').Blockquote} Blockquote\n * @typedef {import('mdast').Code} Code\n * @typedef {import('mdast').Definition} Definition\n * @typedef {import('mdast').Emphasis} Emphasis\n * @typedef {import('mdast').Heading} Heading\n * @typedef {import('mdast').HTML} HTML\n * @typedef {import('mdast').Image} Image\n * @typedef {import('mdast').ImageReference} ImageReference\n * @typedef {import('mdast').InlineCode} InlineCode\n * @typedef {import('mdast').Link} Link\n * @typedef {import('mdast').LinkReference} LinkReference\n * @typedef {import('mdast').List} List\n * @typedef {import('mdast').ListItem} ListItem\n * @typedef {import('mdast').Paragraph} Paragraph\n * @typedef {import('mdast').Root} Root\n * @typedef {import('mdast').Strong} Strong\n * @typedef {import('mdast').Text} Text\n * @typedef {import('mdast').ThematicBreak} ThematicBreak\n * @typedef {import('mdast').ReferenceType} ReferenceType\n * @typedef {import('../index.js').CompileData} CompileData\n */\n\n/**\n * @typedef {Root | Content} Node\n * @typedef {Extract<Node, UnistParent>} Parent\n *\n * @typedef {Omit<UnistParent, 'type' | 'children'> & {type: 'fragment', children: Array<PhrasingContent>}} Fragment\n */\n\n/**\n * @callback Transform\n *   Extra transform, to change the AST afterwards.\n * @param {Root} tree\n *   Tree to transform.\n * @returns {Root | undefined | null | void}\n *   New tree or nothing (in which case the current tree is used).\n *\n * @callback Handle\n *   Handle a token.\n * @param {CompileContext} this\n *   Context.\n * @param {Token} token\n *   Current token.\n * @returns {void}\n *   Nothing.\n *\n * @typedef {Record<string, Handle>} Handles\n *   Token types mapping to handles\n *\n * @callback OnEnterError\n *   Handle the case where the `right` token is open, but it is closed (by the\n *   `left` token) or because we reached the end of the document.\n * @param {Omit<CompileContext, 'sliceSerialize'>} this\n *   Context.\n * @param {Token | undefined} left\n *   Left token.\n * @param {Token} right\n *   Right token.\n * @returns {void}\n *   Nothing.\n *\n * @callback OnExitError\n *   Handle the case where the `right` token is open but it is closed by\n *   exiting the `left` token.\n * @param {Omit<CompileContext, 'sliceSerialize'>} this\n *   Context.\n * @param {Token} left\n *   Left token.\n * @param {Token} right\n *   Right token.\n * @returns {void}\n *   Nothing.\n *\n * @typedef {[Token, OnEnterError | undefined]} TokenTuple\n *   Open token on the stack, with an optional error handler for when\n *   that token isn’t closed properly.\n */\n\n/**\n * @typedef Config\n *   Configuration.\n *\n *   We have our defaults, but extensions will add more.\n * @property {Array<string>} canContainEols\n *   Token types where line endings are used.\n * @property {Handles} enter\n *   Opening handles.\n * @property {Handles} exit\n *   Closing handles.\n * @property {Array<Transform>} transforms\n *   Tree transforms.\n *\n * @typedef {Partial<Config>} Extension\n *   Change how markdown tokens from micromark are turned into mdast.\n *\n * @typedef CompileContext\n *   mdast compiler context.\n * @property {Array<Node | Fragment>} stack\n *   Stack of nodes.\n * @property {Array<TokenTuple>} tokenStack\n *   Stack of tokens.\n * @property {<Key extends keyof CompileData>(key: Key) => CompileData[Key]} getData\n *   Get data from the key/value store.\n * @property {<Key extends keyof CompileData>(key: Key, value?: CompileData[Key]) => void} setData\n *   Set data into the key/value store.\n * @property {(this: CompileContext) => void} buffer\n *   Capture some of the output data.\n * @property {(this: CompileContext) => string} resume\n *   Stop capturing and access the output data.\n * @property {<Kind extends Node>(this: CompileContext, node: Kind, token: Token, onError?: OnEnterError) => Kind} enter\n *   Enter a token.\n * @property {(this: CompileContext, token: Token, onError?: OnExitError) => Node} exit\n *   Exit a token.\n * @property {TokenizeContext['sliceSerialize']} sliceSerialize\n *   Get the string value of a token.\n * @property {Config} config\n *   Configuration.\n *\n * @typedef FromMarkdownOptions\n *   Configuration for how to build mdast.\n * @property {Array<Extension | Array<Extension>> | null | undefined} [mdastExtensions]\n *   Extensions for this utility to change how tokens are turned into a tree.\n *\n * @typedef {ParseOptions & FromMarkdownOptions} Options\n *   Configuration.\n */\n\n// To do: micromark: create a registry of tokens?\n// To do: next major: don’t return given `Node` from `enter`.\n// To do: next major: remove setter/getter.\n\nimport { ok as assert } from 'uvu/assert';\nimport { toString } from 'mdast-util-to-string';\nimport { parse } from 'micromark/lib/parse.js';\nimport { preprocess } from 'micromark/lib/preprocess.js';\nimport { postprocess } from 'micromark/lib/postprocess.js';\nimport { decodeNumericCharacterReference } from 'micromark-util-decode-numeric-character-reference';\nimport { decodeString } from 'micromark-util-decode-string';\nimport { normalizeIdentifier } from 'micromark-util-normalize-identifier';\nimport { codes } from 'micromark-util-symbol/codes.js';\nimport { constants } from 'micromark-util-symbol/constants.js';\nimport { types } from 'micromark-util-symbol/types.js';\nimport { decodeNamedCharacterReference } from 'decode-named-character-reference';\nimport { stringifyPosition } from 'unist-util-stringify-position';\nconst own = {}.hasOwnProperty;\n\n/**\n * @param value\n *   Markdown to parse.\n * @param encoding\n *   Character encoding for when `value` is `Buffer`.\n * @param options\n *   Configuration.\n * @returns\n *   mdast tree.\n */\nexport const fromMarkdown =\n/**\n * @type {(\n *   ((value: Value, encoding: Encoding, options?: Options | null | undefined) => Root) &\n *   ((value: Value, options?: Options | null | undefined) => Root)\n * )}\n */\n\n/**\n * @param {Value} value\n * @param {Encoding | Options | null | undefined} [encoding]\n * @param {Options | null | undefined} [options]\n * @returns {Root}\n */\nfunction (value, encoding, options) {\n  if (typeof encoding !== 'string') {\n    options = encoding;\n    encoding = undefined;\n  }\n  return compiler(options)(postprocess(parse(options).document().write(preprocess()(value, encoding, true))));\n};\n\n/**\n * Note this compiler only understand complete buffering, not streaming.\n *\n * @param {Options | null | undefined} [options]\n */\nfunction compiler(options) {\n  /** @type {Config} */\n  const config = {\n    transforms: [],\n    canContainEols: ['emphasis', 'fragment', 'heading', 'paragraph', 'strong'],\n    enter: {\n      autolink: opener(link),\n      autolinkProtocol: onenterdata,\n      autolinkEmail: onenterdata,\n      atxHeading: opener(heading),\n      blockQuote: opener(blockQuote),\n      characterEscape: onenterdata,\n      characterReference: onenterdata,\n      codeFenced: opener(codeFlow),\n      codeFencedFenceInfo: buffer,\n      codeFencedFenceMeta: buffer,\n      codeIndented: opener(codeFlow, buffer),\n      codeText: opener(codeText, buffer),\n      codeTextData: onenterdata,\n      data: onenterdata,\n      codeFlowValue: onenterdata,\n      definition: opener(definition),\n      definitionDestinationString: buffer,\n      definitionLabelString: buffer,\n      definitionTitleString: buffer,\n      emphasis: opener(emphasis),\n      hardBreakEscape: opener(hardBreak),\n      hardBreakTrailing: opener(hardBreak),\n      htmlFlow: opener(html, buffer),\n      htmlFlowData: onenterdata,\n      htmlText: opener(html, buffer),\n      htmlTextData: onenterdata,\n      image: opener(image),\n      label: buffer,\n      link: opener(link),\n      listItem: opener(listItem),\n      listItemValue: onenterlistitemvalue,\n      listOrdered: opener(list, onenterlistordered),\n      listUnordered: opener(list),\n      paragraph: opener(paragraph),\n      reference: onenterreference,\n      referenceString: buffer,\n      resourceDestinationString: buffer,\n      resourceTitleString: buffer,\n      setextHeading: opener(heading),\n      strong: opener(strong),\n      thematicBreak: opener(thematicBreak)\n    },\n    exit: {\n      atxHeading: closer(),\n      atxHeadingSequence: onexitatxheadingsequence,\n      autolink: closer(),\n      autolinkEmail: onexitautolinkemail,\n      autolinkProtocol: onexitautolinkprotocol,\n      blockQuote: closer(),\n      characterEscapeValue: onexitdata,\n      characterReferenceMarkerHexadecimal: onexitcharacterreferencemarker,\n      characterReferenceMarkerNumeric: onexitcharacterreferencemarker,\n      characterReferenceValue: onexitcharacterreferencevalue,\n      codeFenced: closer(onexitcodefenced),\n      codeFencedFence: onexitcodefencedfence,\n      codeFencedFenceInfo: onexitcodefencedfenceinfo,\n      codeFencedFenceMeta: onexitcodefencedfencemeta,\n      codeFlowValue: onexitdata,\n      codeIndented: closer(onexitcodeindented),\n      codeText: closer(onexitcodetext),\n      codeTextData: onexitdata,\n      data: onexitdata,\n      definition: closer(),\n      definitionDestinationString: onexitdefinitiondestinationstring,\n      definitionLabelString: onexitdefinitionlabelstring,\n      definitionTitleString: onexitdefinitiontitlestring,\n      emphasis: closer(),\n      hardBreakEscape: closer(onexithardbreak),\n      hardBreakTrailing: closer(onexithardbreak),\n      htmlFlow: closer(onexithtmlflow),\n      htmlFlowData: onexitdata,\n      htmlText: closer(onexithtmltext),\n      htmlTextData: onexitdata,\n      image: closer(onexitimage),\n      label: onexitlabel,\n      labelText: onexitlabeltext,\n      lineEnding: onexitlineending,\n      link: closer(onexitlink),\n      listItem: closer(),\n      listOrdered: closer(),\n      listUnordered: closer(),\n      paragraph: closer(),\n      referenceString: onexitreferencestring,\n      resourceDestinationString: onexitresourcedestinationstring,\n      resourceTitleString: onexitresourcetitlestring,\n      resource: onexitresource,\n      setextHeading: closer(onexitsetextheading),\n      setextHeadingLineSequence: onexitsetextheadinglinesequence,\n      setextHeadingText: onexitsetextheadingtext,\n      strong: closer(),\n      thematicBreak: closer()\n    }\n  };\n  configure(config, (options || {}).mdastExtensions || []);\n\n  /** @type {CompileData} */\n  const data = {};\n  return compile;\n\n  /**\n   * Turn micromark events into an mdast tree.\n   *\n   * @param {Array<Event>} events\n   *   Events.\n   * @returns {Root}\n   *   mdast tree.\n   */\n  function compile(events) {\n    /** @type {Root} */\n    let tree = {\n      type: 'root',\n      children: []\n    };\n    /** @type {Omit<CompileContext, 'sliceSerialize'>} */\n    const context = {\n      stack: [tree],\n      tokenStack: [],\n      config,\n      enter,\n      exit,\n      buffer,\n      resume,\n      setData,\n      getData\n    };\n    /** @type {Array<number>} */\n    const listStack = [];\n    let index = -1;\n    while (++index < events.length) {\n      // We preprocess lists to add `listItem` tokens, and to infer whether\n      // items the list itself are spread out.\n      if (events[index][1].type === types.listOrdered || events[index][1].type === types.listUnordered) {\n        if (events[index][0] === 'enter') {\n          listStack.push(index);\n        } else {\n          const tail = listStack.pop();\n          assert(typeof tail === 'number', 'expected list ot be open');\n          index = prepareList(events, tail, index);\n        }\n      }\n    }\n    index = -1;\n    while (++index < events.length) {\n      const handler = config[events[index][0]];\n      if (own.call(handler, events[index][1].type)) {\n        handler[events[index][1].type].call(Object.assign({\n          sliceSerialize: events[index][2].sliceSerialize\n        }, context), events[index][1]);\n      }\n    }\n\n    // Handle tokens still being open.\n    if (context.tokenStack.length > 0) {\n      const tail = context.tokenStack[context.tokenStack.length - 1];\n      const handler = tail[1] || defaultOnError;\n      handler.call(context, undefined, tail[0]);\n    }\n\n    // Figure out `root` position.\n    tree.position = {\n      start: point(events.length > 0 ? events[0][1].start : {\n        line: 1,\n        column: 1,\n        offset: 0\n      }),\n      end: point(events.length > 0 ? events[events.length - 2][1].end : {\n        line: 1,\n        column: 1,\n        offset: 0\n      })\n    };\n\n    // Call transforms.\n    index = -1;\n    while (++index < config.transforms.length) {\n      tree = config.transforms[index](tree) || tree;\n    }\n    return tree;\n  }\n\n  /**\n   * @param {Array<Event>} events\n   * @param {number} start\n   * @param {number} length\n   * @returns {number}\n   */\n  function prepareList(events, start, length) {\n    let index = start - 1;\n    let containerBalance = -1;\n    let listSpread = false;\n    /** @type {Token | undefined} */\n    let listItem;\n    /** @type {number | undefined} */\n    let lineIndex;\n    /** @type {number | undefined} */\n    let firstBlankLineIndex;\n    /** @type {boolean | undefined} */\n    let atMarker;\n    while (++index <= length) {\n      const event = events[index];\n      if (event[1].type === types.listUnordered || event[1].type === types.listOrdered || event[1].type === types.blockQuote) {\n        if (event[0] === 'enter') {\n          containerBalance++;\n        } else {\n          containerBalance--;\n        }\n        atMarker = undefined;\n      } else if (event[1].type === types.lineEndingBlank) {\n        if (event[0] === 'enter') {\n          if (listItem && !atMarker && !containerBalance && !firstBlankLineIndex) {\n            firstBlankLineIndex = index;\n          }\n          atMarker = undefined;\n        }\n      } else if (event[1].type === types.linePrefix || event[1].type === types.listItemValue || event[1].type === types.listItemMarker || event[1].type === types.listItemPrefix || event[1].type === types.listItemPrefixWhitespace) {\n        // Empty.\n      } else {\n        atMarker = undefined;\n      }\n      if (!containerBalance && event[0] === 'enter' && event[1].type === types.listItemPrefix || containerBalance === -1 && event[0] === 'exit' && (event[1].type === types.listUnordered || event[1].type === types.listOrdered)) {\n        if (listItem) {\n          let tailIndex = index;\n          lineIndex = undefined;\n          while (tailIndex--) {\n            const tailEvent = events[tailIndex];\n            if (tailEvent[1].type === types.lineEnding || tailEvent[1].type === types.lineEndingBlank) {\n              if (tailEvent[0] === 'exit') continue;\n              if (lineIndex) {\n                events[lineIndex][1].type = types.lineEndingBlank;\n                listSpread = true;\n              }\n              tailEvent[1].type = types.lineEnding;\n              lineIndex = tailIndex;\n            } else if (tailEvent[1].type === types.linePrefix || tailEvent[1].type === types.blockQuotePrefix || tailEvent[1].type === types.blockQuotePrefixWhitespace || tailEvent[1].type === types.blockQuoteMarker || tailEvent[1].type === types.listItemIndent) {\n              // Empty\n            } else {\n              break;\n            }\n          }\n          if (firstBlankLineIndex && (!lineIndex || firstBlankLineIndex < lineIndex)) {\n            listItem._spread = true;\n          }\n\n          // Fix position.\n          listItem.end = Object.assign({}, lineIndex ? events[lineIndex][1].start : event[1].end);\n          events.splice(lineIndex || index, 0, ['exit', listItem, event[2]]);\n          index++;\n          length++;\n        }\n\n        // Create a new list item.\n        if (event[1].type === types.listItemPrefix) {\n          listItem = {\n            type: 'listItem',\n            _spread: false,\n            start: Object.assign({}, event[1].start),\n            // @ts-expect-error: we’ll add `end` in a second.\n            end: undefined\n          };\n          // @ts-expect-error: `listItem` is most definitely defined, TS...\n          events.splice(index, 0, ['enter', listItem, event[2]]);\n          index++;\n          length++;\n          firstBlankLineIndex = undefined;\n          atMarker = true;\n        }\n      }\n    }\n    events[start][1]._spread = listSpread;\n    return length;\n  }\n\n  /**\n   * Set data.\n   *\n   * @template {keyof CompileData} Key\n   *   Field type.\n   * @param {Key} key\n   *   Key of field.\n   * @param {CompileData[Key]} [value]\n   *   New value.\n   * @returns {void}\n   *   Nothing.\n   */\n  function setData(key, value) {\n    data[key] = value;\n  }\n\n  /**\n   * Get data.\n   *\n   * @template {keyof CompileData} Key\n   *   Field type.\n   * @param {Key} key\n   *   Key of field.\n   * @returns {CompileData[Key]}\n   *   Value.\n   */\n  function getData(key) {\n    return data[key];\n  }\n\n  /**\n   * Create an opener handle.\n   *\n   * @param {(token: Token) => Node} create\n   *   Create a node.\n   * @param {Handle} [and]\n   *   Optional function to also run.\n   * @returns {Handle}\n   *   Handle.\n   */\n  function opener(create, and) {\n    return open;\n\n    /**\n     * @this {CompileContext}\n     * @param {Token} token\n     * @returns {void}\n     */\n    function open(token) {\n      enter.call(this, create(token), token);\n      if (and) and.call(this, token);\n    }\n  }\n\n  /**\n   * @this {CompileContext}\n   * @returns {void}\n   */\n  function buffer() {\n    this.stack.push({\n      type: 'fragment',\n      children: []\n    });\n  }\n\n  /**\n   * @template {Node} Kind\n   *   Node type.\n   * @this {CompileContext}\n   *   Context.\n   * @param {Kind} node\n   *   Node to enter.\n   * @param {Token} token\n   *   Corresponding token.\n   * @param {OnEnterError | undefined} [errorHandler]\n   *   Handle the case where this token is open, but it is closed by something else.\n   * @returns {Kind}\n   *   The given node.\n   */\n  function enter(node, token, errorHandler) {\n    const parent = this.stack[this.stack.length - 1];\n    assert(parent, 'expected `parent`');\n    assert('children' in parent, 'expected `parent`');\n    // @ts-expect-error: Assume `Node` can exist as a child of `parent`.\n    parent.children.push(node);\n    this.stack.push(node);\n    this.tokenStack.push([token, errorHandler]);\n    // @ts-expect-error: `end` will be patched later.\n    node.position = {\n      start: point(token.start)\n    };\n    return node;\n  }\n\n  /**\n   * Create a closer handle.\n   *\n   * @param {Handle} [and]\n   *   Optional function to also run.\n   * @returns {Handle}\n   *   Handle.\n   */\n  function closer(and) {\n    return close;\n\n    /**\n     * @this {CompileContext}\n     * @param {Token} token\n     * @returns {void}\n     */\n    function close(token) {\n      if (and) and.call(this, token);\n      exit.call(this, token);\n    }\n  }\n\n  /**\n   * @this {CompileContext}\n   *   Context.\n   * @param {Token} token\n   *   Corresponding token.\n   * @param {OnExitError | undefined} [onExitError]\n   *   Handle the case where another token is open.\n   * @returns {Node}\n   *   The closed node.\n   */\n  function exit(token, onExitError) {\n    const node = this.stack.pop();\n    assert(node, 'expected `node`');\n    const open = this.tokenStack.pop();\n    if (!open) {\n      throw new Error('Cannot close `' + token.type + '` (' + stringifyPosition({\n        start: token.start,\n        end: token.end\n      }) + '): it’s not open');\n    } else if (open[0].type !== token.type) {\n      if (onExitError) {\n        onExitError.call(this, token, open[0]);\n      } else {\n        const handler = open[1] || defaultOnError;\n        handler.call(this, token, open[0]);\n      }\n    }\n    assert(node.type !== 'fragment', 'unexpected fragment `exit`ed');\n    assert(node.position, 'expected `position` to be defined');\n    node.position.end = point(token.end);\n    return node;\n  }\n\n  /**\n   * @this {CompileContext}\n   * @returns {string}\n   */\n  function resume() {\n    return toString(this.stack.pop());\n  }\n\n  //\n  // Handlers.\n  //\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterlistordered() {\n    setData('expectingFirstListItemValue', true);\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterlistitemvalue(token) {\n    if (getData('expectingFirstListItemValue')) {\n      const ancestor = this.stack[this.stack.length - 2];\n      assert(ancestor, 'expected nodes on stack');\n      assert(ancestor.type === 'list', 'expected list on stack');\n      ancestor.start = Number.parseInt(this.sliceSerialize(token), constants.numericBaseDecimal);\n      setData('expectingFirstListItemValue');\n    }\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodefencedfenceinfo() {\n    const data = this.resume();\n    const node = this.stack[this.stack.length - 1];\n    assert(node, 'expected node on stack');\n    assert(node.type === 'code', 'expected code on stack');\n    node.lang = data;\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodefencedfencemeta() {\n    const data = this.resume();\n    const node = this.stack[this.stack.length - 1];\n    assert(node, 'expected node on stack');\n    assert(node.type === 'code', 'expected code on stack');\n    node.meta = data;\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodefencedfence() {\n    // Exit if this is the closing fence.\n    if (getData('flowCodeInside')) return;\n    this.buffer();\n    setData('flowCodeInside', true);\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodefenced() {\n    const data = this.resume();\n    const node = this.stack[this.stack.length - 1];\n    assert(node, 'expected node on stack');\n    assert(node.type === 'code', 'expected code on stack');\n    node.value = data.replace(/^(\\r?\\n|\\r)|(\\r?\\n|\\r)$/g, '');\n    setData('flowCodeInside');\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodeindented() {\n    const data = this.resume();\n    const node = this.stack[this.stack.length - 1];\n    assert(node, 'expected node on stack');\n    assert(node.type === 'code', 'expected code on stack');\n    node.value = data.replace(/(\\r?\\n|\\r)$/g, '');\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitdefinitionlabelstring(token) {\n    const label = this.resume();\n    const node = this.stack[this.stack.length - 1];\n    assert(node, 'expected node on stack');\n    assert(node.type === 'definition', 'expected definition on stack');\n    node.label = label;\n    node.identifier = normalizeIdentifier(this.sliceSerialize(token)).toLowerCase();\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitdefinitiontitlestring() {\n    const data = this.resume();\n    const node = this.stack[this.stack.length - 1];\n    assert(node, 'expected node on stack');\n    assert(node.type === 'definition', 'expected definition on stack');\n    node.title = data;\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitdefinitiondestinationstring() {\n    const data = this.resume();\n    const node = this.stack[this.stack.length - 1];\n    assert(node, 'expected node on stack');\n    assert(node.type === 'definition', 'expected definition on stack');\n    node.url = data;\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitatxheadingsequence(token) {\n    const node = this.stack[this.stack.length - 1];\n    assert(node, 'expected node on stack');\n    assert(node.type === 'heading', 'expected heading on stack');\n    if (!node.depth) {\n      const depth = this.sliceSerialize(token).length;\n      assert(depth === 1 || depth === 2 || depth === 3 || depth === 4 || depth === 5 || depth === 6, 'expected `depth` between `1` and `6`');\n      node.depth = depth;\n    }\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitsetextheadingtext() {\n    setData('setextHeadingSlurpLineEnding', true);\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitsetextheadinglinesequence(token) {\n    const node = this.stack[this.stack.length - 1];\n    assert(node, 'expected node on stack');\n    assert(node.type === 'heading', 'expected heading on stack');\n    node.depth = this.sliceSerialize(token).charCodeAt(0) === codes.equalsTo ? 1 : 2;\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitsetextheading() {\n    setData('setextHeadingSlurpLineEnding');\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onenterdata(token) {\n    const node = this.stack[this.stack.length - 1];\n    assert(node, 'expected node on stack');\n    assert('children' in node, 'expected parent on stack');\n    let tail = node.children[node.children.length - 1];\n    if (!tail || tail.type !== 'text') {\n      // Add a new text node.\n      tail = text();\n      // @ts-expect-error: we’ll add `end` later.\n      tail.position = {\n        start: point(token.start)\n      };\n      // @ts-expect-error: Assume `parent` accepts `text`.\n      node.children.push(tail);\n    }\n    this.stack.push(tail);\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitdata(token) {\n    const tail = this.stack.pop();\n    assert(tail, 'expected a `node` to be on the stack');\n    assert('value' in tail, 'expected a `literal` to be on the stack');\n    assert(tail.position, 'expected `node` to have an open position');\n    tail.value += this.sliceSerialize(token);\n    tail.position.end = point(token.end);\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitlineending(token) {\n    const context = this.stack[this.stack.length - 1];\n    assert(context, 'expected `node`');\n\n    // If we’re at a hard break, include the line ending in there.\n    if (getData('atHardBreak')) {\n      assert('children' in context, 'expected `parent`');\n      const tail = context.children[context.children.length - 1];\n      assert(tail.position, 'expected tail to have a starting position');\n      tail.position.end = point(token.end);\n      setData('atHardBreak');\n      return;\n    }\n    if (!getData('setextHeadingSlurpLineEnding') && config.canContainEols.includes(context.type)) {\n      onenterdata.call(this, token);\n      onexitdata.call(this, token);\n    }\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexithardbreak() {\n    setData('atHardBreak', true);\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexithtmlflow() {\n    const data = this.resume();\n    const node = this.stack[this.stack.length - 1];\n    assert(node, 'expected node on stack');\n    assert(node.type === 'html', 'expected html on stack');\n    node.value = data;\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexithtmltext() {\n    const data = this.resume();\n    const node = this.stack[this.stack.length - 1];\n    assert(node, 'expected node on stack');\n    assert(node.type === 'html', 'expected html on stack');\n    node.value = data;\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitcodetext() {\n    const data = this.resume();\n    const node = this.stack[this.stack.length - 1];\n    assert(node, 'expected node on stack');\n    assert(node.type === 'inlineCode', 'expected inline code on stack');\n    node.value = data;\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitlink() {\n    const node = this.stack[this.stack.length - 1];\n    assert(node, 'expected node on stack');\n    assert(node.type === 'link', 'expected link on stack');\n\n    // Note: there are also `identifier` and `label` fields on this link node!\n    // These are used / cleaned here.\n\n    // To do: clean.\n    if (getData('inReference')) {\n      /** @type {ReferenceType} */\n      const referenceType = getData('referenceType') || 'shortcut';\n      node.type += 'Reference';\n      // @ts-expect-error: mutate.\n      node.referenceType = referenceType;\n      // @ts-expect-error: mutate.\n      delete node.url;\n      delete node.title;\n    } else {\n      // @ts-expect-error: mutate.\n      delete node.identifier;\n      // @ts-expect-error: mutate.\n      delete node.label;\n    }\n    setData('referenceType');\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitimage() {\n    const node = this.stack[this.stack.length - 1];\n    assert(node, 'expected node on stack');\n    assert(node.type === 'image', 'expected image on stack');\n\n    // Note: there are also `identifier` and `label` fields on this link node!\n    // These are used / cleaned here.\n\n    // To do: clean.\n    if (getData('inReference')) {\n      /** @type {ReferenceType} */\n      const referenceType = getData('referenceType') || 'shortcut';\n      node.type += 'Reference';\n      // @ts-expect-error: mutate.\n      node.referenceType = referenceType;\n      // @ts-expect-error: mutate.\n      delete node.url;\n      delete node.title;\n    } else {\n      // @ts-expect-error: mutate.\n      delete node.identifier;\n      // @ts-expect-error: mutate.\n      delete node.label;\n    }\n    setData('referenceType');\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitlabeltext(token) {\n    const string = this.sliceSerialize(token);\n    const ancestor = this.stack[this.stack.length - 2];\n    assert(ancestor, 'expected ancestor on stack');\n    assert(ancestor.type === 'image' || ancestor.type === 'link', 'expected image or link on stack');\n\n    // @ts-expect-error: stash this on the node, as it might become a reference\n    // later.\n    ancestor.label = decodeString(string);\n    // @ts-expect-error: same as above.\n    ancestor.identifier = normalizeIdentifier(string).toLowerCase();\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitlabel() {\n    const fragment = this.stack[this.stack.length - 1];\n    assert(fragment, 'expected node on stack');\n    assert(fragment.type === 'fragment', 'expected fragment on stack');\n    const value = this.resume();\n    const node = this.stack[this.stack.length - 1];\n    assert(node, 'expected node on stack');\n    assert(node.type === 'image' || node.type === 'link', 'expected image or link on stack');\n\n    // Assume a reference.\n    setData('inReference', true);\n    if (node.type === 'link') {\n      /** @type {Array<StaticPhrasingContent>} */\n      // @ts-expect-error: Assume static phrasing content.\n      const children = fragment.children;\n      node.children = children;\n    } else {\n      node.alt = value;\n    }\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitresourcedestinationstring() {\n    const data = this.resume();\n    const node = this.stack[this.stack.length - 1];\n    assert(node, 'expected node on stack');\n    assert(node.type === 'image' || node.type === 'link', 'expected image or link on stack');\n    node.url = data;\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitresourcetitlestring() {\n    const data = this.resume();\n    const node = this.stack[this.stack.length - 1];\n    assert(node, 'expected node on stack');\n    assert(node.type === 'image' || node.type === 'link', 'expected image or link on stack');\n    node.title = data;\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitresource() {\n    setData('inReference');\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onenterreference() {\n    setData('referenceType', 'collapsed');\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitreferencestring(token) {\n    const label = this.resume();\n    const node = this.stack[this.stack.length - 1];\n    assert(node, 'expected node on stack');\n    assert(node.type === 'image' || node.type === 'link', 'expected image reference or link reference on stack');\n\n    // @ts-expect-error: stash this on the node, as it might become a reference\n    // later.\n    node.label = label;\n    // @ts-expect-error: same as above.\n    node.identifier = normalizeIdentifier(this.sliceSerialize(token)).toLowerCase();\n    setData('referenceType', 'full');\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitcharacterreferencemarker(token) {\n    assert(token.type === 'characterReferenceMarkerNumeric' || token.type === 'characterReferenceMarkerHexadecimal');\n    setData('characterReferenceType', token.type);\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcharacterreferencevalue(token) {\n    const data = this.sliceSerialize(token);\n    const type = getData('characterReferenceType');\n    /** @type {string} */\n    let value;\n    if (type) {\n      value = decodeNumericCharacterReference(data, type === types.characterReferenceMarkerNumeric ? constants.numericBaseDecimal : constants.numericBaseHexadecimal);\n      setData('characterReferenceType');\n    } else {\n      const result = decodeNamedCharacterReference(data);\n      assert(result !== false, 'expected reference to decode');\n      value = result;\n    }\n    const tail = this.stack.pop();\n    assert(tail, 'expected `node`');\n    assert(tail.position, 'expected `node.position`');\n    assert('value' in tail, 'expected `node.value`');\n    tail.value += value;\n    tail.position.end = point(token.end);\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitautolinkprotocol(token) {\n    onexitdata.call(this, token);\n    const node = this.stack[this.stack.length - 1];\n    assert(node, 'expected node on stack');\n    assert(node.type === 'link', 'expected link on stack');\n    node.url = this.sliceSerialize(token);\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitautolinkemail(token) {\n    onexitdata.call(this, token);\n    const node = this.stack[this.stack.length - 1];\n    assert(node, 'expected node on stack');\n    assert(node.type === 'link', 'expected link on stack');\n    node.url = 'mailto:' + this.sliceSerialize(token);\n  }\n\n  //\n  // Creaters.\n  //\n\n  /** @returns {Blockquote} */\n  function blockQuote() {\n    return {\n      type: 'blockquote',\n      children: []\n    };\n  }\n\n  /** @returns {Code} */\n  function codeFlow() {\n    return {\n      type: 'code',\n      lang: null,\n      meta: null,\n      value: ''\n    };\n  }\n\n  /** @returns {InlineCode} */\n  function codeText() {\n    return {\n      type: 'inlineCode',\n      value: ''\n    };\n  }\n\n  /** @returns {Definition} */\n  function definition() {\n    return {\n      type: 'definition',\n      identifier: '',\n      label: null,\n      title: null,\n      url: ''\n    };\n  }\n\n  /** @returns {Emphasis} */\n  function emphasis() {\n    return {\n      type: 'emphasis',\n      children: []\n    };\n  }\n\n  /** @returns {Heading} */\n  function heading() {\n    // @ts-expect-error `depth` will be set later.\n    return {\n      type: 'heading',\n      depth: undefined,\n      children: []\n    };\n  }\n\n  /** @returns {Break} */\n  function hardBreak() {\n    return {\n      type: 'break'\n    };\n  }\n\n  /** @returns {HTML} */\n  function html() {\n    return {\n      type: 'html',\n      value: ''\n    };\n  }\n\n  /** @returns {Image} */\n  function image() {\n    return {\n      type: 'image',\n      title: null,\n      url: '',\n      alt: null\n    };\n  }\n\n  /** @returns {Link} */\n  function link() {\n    return {\n      type: 'link',\n      title: null,\n      url: '',\n      children: []\n    };\n  }\n\n  /**\n   * @param {Token} token\n   * @returns {List}\n   */\n  function list(token) {\n    return {\n      type: 'list',\n      ordered: token.type === 'listOrdered',\n      start: null,\n      spread: token._spread,\n      children: []\n    };\n  }\n\n  /**\n   * @param {Token} token\n   * @returns {ListItem}\n   */\n  function listItem(token) {\n    return {\n      type: 'listItem',\n      spread: token._spread,\n      checked: null,\n      children: []\n    };\n  }\n\n  /** @returns {Paragraph} */\n  function paragraph() {\n    return {\n      type: 'paragraph',\n      children: []\n    };\n  }\n\n  /** @returns {Strong} */\n  function strong() {\n    return {\n      type: 'strong',\n      children: []\n    };\n  }\n\n  /** @returns {Text} */\n  function text() {\n    return {\n      type: 'text',\n      value: ''\n    };\n  }\n\n  /** @returns {ThematicBreak} */\n  function thematicBreak() {\n    return {\n      type: 'thematicBreak'\n    };\n  }\n}\n\n/**\n * Copy a point-like value.\n *\n * @param {Point} d\n *   Point-like value.\n * @returns {Point}\n *   unist point.\n */\nfunction point(d) {\n  return {\n    line: d.line,\n    column: d.column,\n    offset: d.offset\n  };\n}\n\n/**\n * @param {Config} combined\n * @param {Array<Extension | Array<Extension>>} extensions\n * @returns {void}\n */\nfunction configure(combined, extensions) {\n  let index = -1;\n  while (++index < extensions.length) {\n    const value = extensions[index];\n    if (Array.isArray(value)) {\n      configure(combined, value);\n    } else {\n      extension(combined, value);\n    }\n  }\n}\n\n/**\n * @param {Config} combined\n * @param {Extension} extension\n * @returns {void}\n */\nfunction extension(combined, extension) {\n  /** @type {keyof Extension} */\n  let key;\n  for (key in extension) {\n    if (own.call(extension, key)) {\n      if (key === 'canContainEols') {\n        const right = extension[key];\n        if (right) {\n          combined[key].push(...right);\n        }\n      } else if (key === 'transforms') {\n        const right = extension[key];\n        if (right) {\n          combined[key].push(...right);\n        }\n      } else if (key === 'enter' || key === 'exit') {\n        const right = extension[key];\n        if (right) {\n          Object.assign(combined[key], right);\n        }\n      }\n    }\n  }\n}\n\n/** @type {OnEnterError} */\nfunction defaultOnError(left, right) {\n  if (left) {\n    throw new Error('Cannot close `' + left.type + '` (' + stringifyPosition({\n      start: left.start,\n      end: left.end\n    }) + '): a different token (`' + right.type + '`, ' + stringifyPosition({\n      start: right.start,\n      end: right.end\n    }) + ') is open');\n  } else {\n    throw new Error('Cannot close document, a token (`' + right.type + '`, ' + stringifyPosition({\n      start: right.start,\n      end: right.end\n    }) + ') is still open');\n  }\n}", "map": {"version": 3, "names": ["ok", "assert", "toString", "parse", "preprocess", "postprocess", "decodeNumericCharacterReference", "decodeString", "normalizeIdentifier", "codes", "constants", "types", "decodeNamedCharacterReference", "stringifyPosition", "own", "hasOwnProperty", "fromMarkdown", "value", "encoding", "options", "undefined", "compiler", "document", "write", "config", "transforms", "canContainEols", "enter", "autolink", "opener", "link", "autolinkProtocol", "onenterdata", "autolinkEmail", "atxHeading", "heading", "blockQuote", "characterEscape", "characterReference", "codeFenced", "codeFlow", "codeFencedFenceInfo", "buffer", "codeFencedFenceMeta", "codeIndented", "codeText", "codeTextData", "data", "codeFlowValue", "definition", "definitionDestinationString", "definitionLabelString", "definitionTitleString", "emphasis", "hardBreakEscape", "hardBreak", "hardBreakTrailing", "htmlFlow", "html", "htmlFlowData", "htmlText", "htmlTextData", "image", "label", "listItem", "listItemValue", "onenterlistitemvalue", "listOrdered", "list", "onenterlistordered", "listUnordered", "paragraph", "reference", "onenterreference", "referenceString", "resourceDestinationString", "resourceTitleString", "setextHeading", "strong", "thematicBreak", "exit", "closer", "atxHeadingSequence", "onexitatxheadingsequence", "onexitautolinkemail", "onexitautolinkprotocol", "characterEscapeValue", "onexitdata", "characterReferenceMarkerHexadecimal", "onexitcharacterreferencemarker", "characterReferenceMarkerNumeric", "characterReferenceValue", "onexitcharacterreferencevalue", "onexitcodefenced", "codeFencedFence", "onexitcodefencedfence", "onexitcodefencedfenceinfo", "onexitcodefencedfencemeta", "onexitcodeindented", "onexitcodetext", "onexitdefinitiondestinationstring", "onexitdefinitionlabelstring", "onexitdefinitiontitlestring", "onexithardbreak", "onexithtmlflow", "onexithtmltext", "onexitimage", "onexitlabel", "labelText", "onexitlabeltext", "lineEnding", "onexitlineending", "onexitlink", "onexitreferencestring", "onexitresourcedestinationstring", "onexitresourcetitlestring", "resource", "onexitresource", "onexitsetextheading", "setextHeadingLineSequence", "onexitsetextheadinglinesequence", "setextHeadingText", "onexitsetextheadingtext", "configure", "mdastExtensions", "compile", "events", "tree", "type", "children", "context", "stack", "tokenStack", "resume", "setData", "getData", "listStack", "index", "length", "push", "tail", "pop", "prepareList", "handler", "call", "Object", "assign", "sliceSerialize", "defaultOnError", "position", "start", "point", "line", "column", "offset", "end", "containerBalance", "listSpread", "lineIndex", "firstBlankLineIndex", "atMarker", "event", "lineEndingBlank", "linePrefix", "listItemMarker", "listItemPrefix", "listItemPrefixWhitespace", "tailIndex", "tailEvent", "blockQuotePrefix", "blockQuotePrefixWhitespace", "blockQuoteMarker", "listItemIndent", "_spread", "splice", "key", "create", "and", "open", "token", "node", "<PERSON><PERSON><PERSON><PERSON>", "parent", "close", "onExitError", "Error", "ancestor", "Number", "parseInt", "numericBaseDecimal", "lang", "meta", "replace", "identifier", "toLowerCase", "title", "url", "depth", "charCodeAt", "equalsTo", "text", "includes", "referenceType", "string", "fragment", "alt", "numericBaseHexadecimal", "result", "ordered", "spread", "checked", "d", "combined", "extensions", "Array", "isArray", "extension", "right", "left"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/mdast-util-from-markdown/dev/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('micromark-util-types').Encoding} Encoding\n * @typedef {import('micromark-util-types').Event} Event\n * @typedef {import('micromark-util-types').ParseOptions} ParseOptions\n * @typedef {import('micromark-util-types').Token} Token\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Value} Value\n *\n * @typedef {import('unist').Parent} UnistParent\n * @typedef {import('unist').Point} Point\n *\n * @typedef {import('mdast').PhrasingContent} PhrasingContent\n * @typedef {import('mdast').StaticPhrasingContent} StaticPhrasingContent\n * @typedef {import('mdast').Content} Content\n * @typedef {import('mdast').Break} Break\n * @typedef {import('mdast').Blockquote} Blockquote\n * @typedef {import('mdast').Code} Code\n * @typedef {import('mdast').Definition} Definition\n * @typedef {import('mdast').Emphasis} Emphasis\n * @typedef {import('mdast').Heading} Heading\n * @typedef {import('mdast').HTML} HTML\n * @typedef {import('mdast').Image} Image\n * @typedef {import('mdast').ImageReference} ImageReference\n * @typedef {import('mdast').InlineCode} InlineCode\n * @typedef {import('mdast').Link} Link\n * @typedef {import('mdast').LinkReference} LinkReference\n * @typedef {import('mdast').List} List\n * @typedef {import('mdast').ListItem} ListItem\n * @typedef {import('mdast').Paragraph} Paragraph\n * @typedef {import('mdast').Root} Root\n * @typedef {import('mdast').Strong} Strong\n * @typedef {import('mdast').Text} Text\n * @typedef {import('mdast').ThematicBreak} ThematicBreak\n * @typedef {import('mdast').ReferenceType} ReferenceType\n * @typedef {import('../index.js').CompileData} CompileData\n */\n\n/**\n * @typedef {Root | Content} Node\n * @typedef {Extract<Node, UnistParent>} Parent\n *\n * @typedef {Omit<UnistParent, 'type' | 'children'> & {type: 'fragment', children: Array<PhrasingContent>}} Fragment\n */\n\n/**\n * @callback Transform\n *   Extra transform, to change the AST afterwards.\n * @param {Root} tree\n *   Tree to transform.\n * @returns {Root | undefined | null | void}\n *   New tree or nothing (in which case the current tree is used).\n *\n * @callback Handle\n *   Handle a token.\n * @param {CompileContext} this\n *   Context.\n * @param {Token} token\n *   Current token.\n * @returns {void}\n *   Nothing.\n *\n * @typedef {Record<string, Handle>} Handles\n *   Token types mapping to handles\n *\n * @callback OnEnterError\n *   Handle the case where the `right` token is open, but it is closed (by the\n *   `left` token) or because we reached the end of the document.\n * @param {Omit<CompileContext, 'sliceSerialize'>} this\n *   Context.\n * @param {Token | undefined} left\n *   Left token.\n * @param {Token} right\n *   Right token.\n * @returns {void}\n *   Nothing.\n *\n * @callback OnExitError\n *   Handle the case where the `right` token is open but it is closed by\n *   exiting the `left` token.\n * @param {Omit<CompileContext, 'sliceSerialize'>} this\n *   Context.\n * @param {Token} left\n *   Left token.\n * @param {Token} right\n *   Right token.\n * @returns {void}\n *   Nothing.\n *\n * @typedef {[Token, OnEnterError | undefined]} TokenTuple\n *   Open token on the stack, with an optional error handler for when\n *   that token isn’t closed properly.\n */\n\n/**\n * @typedef Config\n *   Configuration.\n *\n *   We have our defaults, but extensions will add more.\n * @property {Array<string>} canContainEols\n *   Token types where line endings are used.\n * @property {Handles} enter\n *   Opening handles.\n * @property {Handles} exit\n *   Closing handles.\n * @property {Array<Transform>} transforms\n *   Tree transforms.\n *\n * @typedef {Partial<Config>} Extension\n *   Change how markdown tokens from micromark are turned into mdast.\n *\n * @typedef CompileContext\n *   mdast compiler context.\n * @property {Array<Node | Fragment>} stack\n *   Stack of nodes.\n * @property {Array<TokenTuple>} tokenStack\n *   Stack of tokens.\n * @property {<Key extends keyof CompileData>(key: Key) => CompileData[Key]} getData\n *   Get data from the key/value store.\n * @property {<Key extends keyof CompileData>(key: Key, value?: CompileData[Key]) => void} setData\n *   Set data into the key/value store.\n * @property {(this: CompileContext) => void} buffer\n *   Capture some of the output data.\n * @property {(this: CompileContext) => string} resume\n *   Stop capturing and access the output data.\n * @property {<Kind extends Node>(this: CompileContext, node: Kind, token: Token, onError?: OnEnterError) => Kind} enter\n *   Enter a token.\n * @property {(this: CompileContext, token: Token, onError?: OnExitError) => Node} exit\n *   Exit a token.\n * @property {TokenizeContext['sliceSerialize']} sliceSerialize\n *   Get the string value of a token.\n * @property {Config} config\n *   Configuration.\n *\n * @typedef FromMarkdownOptions\n *   Configuration for how to build mdast.\n * @property {Array<Extension | Array<Extension>> | null | undefined} [mdastExtensions]\n *   Extensions for this utility to change how tokens are turned into a tree.\n *\n * @typedef {ParseOptions & FromMarkdownOptions} Options\n *   Configuration.\n */\n\n// To do: micromark: create a registry of tokens?\n// To do: next major: don’t return given `Node` from `enter`.\n// To do: next major: remove setter/getter.\n\nimport {ok as assert} from 'uvu/assert'\nimport {toString} from 'mdast-util-to-string'\nimport {parse} from 'micromark/lib/parse.js'\nimport {preprocess} from 'micromark/lib/preprocess.js'\nimport {postprocess} from 'micromark/lib/postprocess.js'\nimport {decodeNumericCharacterReference} from 'micromark-util-decode-numeric-character-reference'\nimport {decodeString} from 'micromark-util-decode-string'\nimport {normalizeIdentifier} from 'micromark-util-normalize-identifier'\nimport {codes} from 'micromark-util-symbol/codes.js'\nimport {constants} from 'micromark-util-symbol/constants.js'\nimport {types} from 'micromark-util-symbol/types.js'\nimport {decodeNamedCharacterReference} from 'decode-named-character-reference'\nimport {stringifyPosition} from 'unist-util-stringify-position'\n\nconst own = {}.hasOwnProperty\n\n/**\n * @param value\n *   Markdown to parse.\n * @param encoding\n *   Character encoding for when `value` is `Buffer`.\n * @param options\n *   Configuration.\n * @returns\n *   mdast tree.\n */\nexport const fromMarkdown =\n  /**\n   * @type {(\n   *   ((value: Value, encoding: Encoding, options?: Options | null | undefined) => Root) &\n   *   ((value: Value, options?: Options | null | undefined) => Root)\n   * )}\n   */\n  (\n    /**\n     * @param {Value} value\n     * @param {Encoding | Options | null | undefined} [encoding]\n     * @param {Options | null | undefined} [options]\n     * @returns {Root}\n     */\n    function (value, encoding, options) {\n      if (typeof encoding !== 'string') {\n        options = encoding\n        encoding = undefined\n      }\n\n      return compiler(options)(\n        postprocess(\n          parse(options).document().write(preprocess()(value, encoding, true))\n        )\n      )\n    }\n  )\n\n/**\n * Note this compiler only understand complete buffering, not streaming.\n *\n * @param {Options | null | undefined} [options]\n */\nfunction compiler(options) {\n  /** @type {Config} */\n  const config = {\n    transforms: [],\n    canContainEols: ['emphasis', 'fragment', 'heading', 'paragraph', 'strong'],\n    enter: {\n      autolink: opener(link),\n      autolinkProtocol: onenterdata,\n      autolinkEmail: onenterdata,\n      atxHeading: opener(heading),\n      blockQuote: opener(blockQuote),\n      characterEscape: onenterdata,\n      characterReference: onenterdata,\n      codeFenced: opener(codeFlow),\n      codeFencedFenceInfo: buffer,\n      codeFencedFenceMeta: buffer,\n      codeIndented: opener(codeFlow, buffer),\n      codeText: opener(codeText, buffer),\n      codeTextData: onenterdata,\n      data: onenterdata,\n      codeFlowValue: onenterdata,\n      definition: opener(definition),\n      definitionDestinationString: buffer,\n      definitionLabelString: buffer,\n      definitionTitleString: buffer,\n      emphasis: opener(emphasis),\n      hardBreakEscape: opener(hardBreak),\n      hardBreakTrailing: opener(hardBreak),\n      htmlFlow: opener(html, buffer),\n      htmlFlowData: onenterdata,\n      htmlText: opener(html, buffer),\n      htmlTextData: onenterdata,\n      image: opener(image),\n      label: buffer,\n      link: opener(link),\n      listItem: opener(listItem),\n      listItemValue: onenterlistitemvalue,\n      listOrdered: opener(list, onenterlistordered),\n      listUnordered: opener(list),\n      paragraph: opener(paragraph),\n      reference: onenterreference,\n      referenceString: buffer,\n      resourceDestinationString: buffer,\n      resourceTitleString: buffer,\n      setextHeading: opener(heading),\n      strong: opener(strong),\n      thematicBreak: opener(thematicBreak)\n    },\n    exit: {\n      atxHeading: closer(),\n      atxHeadingSequence: onexitatxheadingsequence,\n      autolink: closer(),\n      autolinkEmail: onexitautolinkemail,\n      autolinkProtocol: onexitautolinkprotocol,\n      blockQuote: closer(),\n      characterEscapeValue: onexitdata,\n      characterReferenceMarkerHexadecimal: onexitcharacterreferencemarker,\n      characterReferenceMarkerNumeric: onexitcharacterreferencemarker,\n      characterReferenceValue: onexitcharacterreferencevalue,\n      codeFenced: closer(onexitcodefenced),\n      codeFencedFence: onexitcodefencedfence,\n      codeFencedFenceInfo: onexitcodefencedfenceinfo,\n      codeFencedFenceMeta: onexitcodefencedfencemeta,\n      codeFlowValue: onexitdata,\n      codeIndented: closer(onexitcodeindented),\n      codeText: closer(onexitcodetext),\n      codeTextData: onexitdata,\n      data: onexitdata,\n      definition: closer(),\n      definitionDestinationString: onexitdefinitiondestinationstring,\n      definitionLabelString: onexitdefinitionlabelstring,\n      definitionTitleString: onexitdefinitiontitlestring,\n      emphasis: closer(),\n      hardBreakEscape: closer(onexithardbreak),\n      hardBreakTrailing: closer(onexithardbreak),\n      htmlFlow: closer(onexithtmlflow),\n      htmlFlowData: onexitdata,\n      htmlText: closer(onexithtmltext),\n      htmlTextData: onexitdata,\n      image: closer(onexitimage),\n      label: onexitlabel,\n      labelText: onexitlabeltext,\n      lineEnding: onexitlineending,\n      link: closer(onexitlink),\n      listItem: closer(),\n      listOrdered: closer(),\n      listUnordered: closer(),\n      paragraph: closer(),\n      referenceString: onexitreferencestring,\n      resourceDestinationString: onexitresourcedestinationstring,\n      resourceTitleString: onexitresourcetitlestring,\n      resource: onexitresource,\n      setextHeading: closer(onexitsetextheading),\n      setextHeadingLineSequence: onexitsetextheadinglinesequence,\n      setextHeadingText: onexitsetextheadingtext,\n      strong: closer(),\n      thematicBreak: closer()\n    }\n  }\n\n  configure(config, (options || {}).mdastExtensions || [])\n\n  /** @type {CompileData} */\n  const data = {}\n\n  return compile\n\n  /**\n   * Turn micromark events into an mdast tree.\n   *\n   * @param {Array<Event>} events\n   *   Events.\n   * @returns {Root}\n   *   mdast tree.\n   */\n  function compile(events) {\n    /** @type {Root} */\n    let tree = {type: 'root', children: []}\n    /** @type {Omit<CompileContext, 'sliceSerialize'>} */\n    const context = {\n      stack: [tree],\n      tokenStack: [],\n      config,\n      enter,\n      exit,\n      buffer,\n      resume,\n      setData,\n      getData\n    }\n    /** @type {Array<number>} */\n    const listStack = []\n    let index = -1\n\n    while (++index < events.length) {\n      // We preprocess lists to add `listItem` tokens, and to infer whether\n      // items the list itself are spread out.\n      if (\n        events[index][1].type === types.listOrdered ||\n        events[index][1].type === types.listUnordered\n      ) {\n        if (events[index][0] === 'enter') {\n          listStack.push(index)\n        } else {\n          const tail = listStack.pop()\n          assert(typeof tail === 'number', 'expected list ot be open')\n          index = prepareList(events, tail, index)\n        }\n      }\n    }\n\n    index = -1\n\n    while (++index < events.length) {\n      const handler = config[events[index][0]]\n\n      if (own.call(handler, events[index][1].type)) {\n        handler[events[index][1].type].call(\n          Object.assign(\n            {sliceSerialize: events[index][2].sliceSerialize},\n            context\n          ),\n          events[index][1]\n        )\n      }\n    }\n\n    // Handle tokens still being open.\n    if (context.tokenStack.length > 0) {\n      const tail = context.tokenStack[context.tokenStack.length - 1]\n      const handler = tail[1] || defaultOnError\n      handler.call(context, undefined, tail[0])\n    }\n\n    // Figure out `root` position.\n    tree.position = {\n      start: point(\n        events.length > 0 ? events[0][1].start : {line: 1, column: 1, offset: 0}\n      ),\n      end: point(\n        events.length > 0\n          ? events[events.length - 2][1].end\n          : {line: 1, column: 1, offset: 0}\n      )\n    }\n\n    // Call transforms.\n    index = -1\n    while (++index < config.transforms.length) {\n      tree = config.transforms[index](tree) || tree\n    }\n\n    return tree\n  }\n\n  /**\n   * @param {Array<Event>} events\n   * @param {number} start\n   * @param {number} length\n   * @returns {number}\n   */\n  function prepareList(events, start, length) {\n    let index = start - 1\n    let containerBalance = -1\n    let listSpread = false\n    /** @type {Token | undefined} */\n    let listItem\n    /** @type {number | undefined} */\n    let lineIndex\n    /** @type {number | undefined} */\n    let firstBlankLineIndex\n    /** @type {boolean | undefined} */\n    let atMarker\n\n    while (++index <= length) {\n      const event = events[index]\n\n      if (\n        event[1].type === types.listUnordered ||\n        event[1].type === types.listOrdered ||\n        event[1].type === types.blockQuote\n      ) {\n        if (event[0] === 'enter') {\n          containerBalance++\n        } else {\n          containerBalance--\n        }\n\n        atMarker = undefined\n      } else if (event[1].type === types.lineEndingBlank) {\n        if (event[0] === 'enter') {\n          if (\n            listItem &&\n            !atMarker &&\n            !containerBalance &&\n            !firstBlankLineIndex\n          ) {\n            firstBlankLineIndex = index\n          }\n\n          atMarker = undefined\n        }\n      } else if (\n        event[1].type === types.linePrefix ||\n        event[1].type === types.listItemValue ||\n        event[1].type === types.listItemMarker ||\n        event[1].type === types.listItemPrefix ||\n        event[1].type === types.listItemPrefixWhitespace\n      ) {\n        // Empty.\n      } else {\n        atMarker = undefined\n      }\n\n      if (\n        (!containerBalance &&\n          event[0] === 'enter' &&\n          event[1].type === types.listItemPrefix) ||\n        (containerBalance === -1 &&\n          event[0] === 'exit' &&\n          (event[1].type === types.listUnordered ||\n            event[1].type === types.listOrdered))\n      ) {\n        if (listItem) {\n          let tailIndex = index\n          lineIndex = undefined\n\n          while (tailIndex--) {\n            const tailEvent = events[tailIndex]\n\n            if (\n              tailEvent[1].type === types.lineEnding ||\n              tailEvent[1].type === types.lineEndingBlank\n            ) {\n              if (tailEvent[0] === 'exit') continue\n\n              if (lineIndex) {\n                events[lineIndex][1].type = types.lineEndingBlank\n                listSpread = true\n              }\n\n              tailEvent[1].type = types.lineEnding\n              lineIndex = tailIndex\n            } else if (\n              tailEvent[1].type === types.linePrefix ||\n              tailEvent[1].type === types.blockQuotePrefix ||\n              tailEvent[1].type === types.blockQuotePrefixWhitespace ||\n              tailEvent[1].type === types.blockQuoteMarker ||\n              tailEvent[1].type === types.listItemIndent\n            ) {\n              // Empty\n            } else {\n              break\n            }\n          }\n\n          if (\n            firstBlankLineIndex &&\n            (!lineIndex || firstBlankLineIndex < lineIndex)\n          ) {\n            listItem._spread = true\n          }\n\n          // Fix position.\n          listItem.end = Object.assign(\n            {},\n            lineIndex ? events[lineIndex][1].start : event[1].end\n          )\n\n          events.splice(lineIndex || index, 0, ['exit', listItem, event[2]])\n          index++\n          length++\n        }\n\n        // Create a new list item.\n        if (event[1].type === types.listItemPrefix) {\n          listItem = {\n            type: 'listItem',\n            _spread: false,\n            start: Object.assign({}, event[1].start),\n            // @ts-expect-error: we’ll add `end` in a second.\n            end: undefined\n          }\n          // @ts-expect-error: `listItem` is most definitely defined, TS...\n          events.splice(index, 0, ['enter', listItem, event[2]])\n          index++\n          length++\n          firstBlankLineIndex = undefined\n          atMarker = true\n        }\n      }\n    }\n\n    events[start][1]._spread = listSpread\n    return length\n  }\n\n  /**\n   * Set data.\n   *\n   * @template {keyof CompileData} Key\n   *   Field type.\n   * @param {Key} key\n   *   Key of field.\n   * @param {CompileData[Key]} [value]\n   *   New value.\n   * @returns {void}\n   *   Nothing.\n   */\n  function setData(key, value) {\n    data[key] = value\n  }\n\n  /**\n   * Get data.\n   *\n   * @template {keyof CompileData} Key\n   *   Field type.\n   * @param {Key} key\n   *   Key of field.\n   * @returns {CompileData[Key]}\n   *   Value.\n   */\n  function getData(key) {\n    return data[key]\n  }\n\n  /**\n   * Create an opener handle.\n   *\n   * @param {(token: Token) => Node} create\n   *   Create a node.\n   * @param {Handle} [and]\n   *   Optional function to also run.\n   * @returns {Handle}\n   *   Handle.\n   */\n  function opener(create, and) {\n    return open\n\n    /**\n     * @this {CompileContext}\n     * @param {Token} token\n     * @returns {void}\n     */\n    function open(token) {\n      enter.call(this, create(token), token)\n      if (and) and.call(this, token)\n    }\n  }\n\n  /**\n   * @this {CompileContext}\n   * @returns {void}\n   */\n  function buffer() {\n    this.stack.push({type: 'fragment', children: []})\n  }\n\n  /**\n   * @template {Node} Kind\n   *   Node type.\n   * @this {CompileContext}\n   *   Context.\n   * @param {Kind} node\n   *   Node to enter.\n   * @param {Token} token\n   *   Corresponding token.\n   * @param {OnEnterError | undefined} [errorHandler]\n   *   Handle the case where this token is open, but it is closed by something else.\n   * @returns {Kind}\n   *   The given node.\n   */\n  function enter(node, token, errorHandler) {\n    const parent = this.stack[this.stack.length - 1]\n    assert(parent, 'expected `parent`')\n    assert('children' in parent, 'expected `parent`')\n    // @ts-expect-error: Assume `Node` can exist as a child of `parent`.\n    parent.children.push(node)\n    this.stack.push(node)\n    this.tokenStack.push([token, errorHandler])\n    // @ts-expect-error: `end` will be patched later.\n    node.position = {start: point(token.start)}\n    return node\n  }\n\n  /**\n   * Create a closer handle.\n   *\n   * @param {Handle} [and]\n   *   Optional function to also run.\n   * @returns {Handle}\n   *   Handle.\n   */\n  function closer(and) {\n    return close\n\n    /**\n     * @this {CompileContext}\n     * @param {Token} token\n     * @returns {void}\n     */\n    function close(token) {\n      if (and) and.call(this, token)\n      exit.call(this, token)\n    }\n  }\n\n  /**\n   * @this {CompileContext}\n   *   Context.\n   * @param {Token} token\n   *   Corresponding token.\n   * @param {OnExitError | undefined} [onExitError]\n   *   Handle the case where another token is open.\n   * @returns {Node}\n   *   The closed node.\n   */\n  function exit(token, onExitError) {\n    const node = this.stack.pop()\n    assert(node, 'expected `node`')\n    const open = this.tokenStack.pop()\n\n    if (!open) {\n      throw new Error(\n        'Cannot close `' +\n          token.type +\n          '` (' +\n          stringifyPosition({start: token.start, end: token.end}) +\n          '): it’s not open'\n      )\n    } else if (open[0].type !== token.type) {\n      if (onExitError) {\n        onExitError.call(this, token, open[0])\n      } else {\n        const handler = open[1] || defaultOnError\n        handler.call(this, token, open[0])\n      }\n    }\n\n    assert(node.type !== 'fragment', 'unexpected fragment `exit`ed')\n    assert(node.position, 'expected `position` to be defined')\n    node.position.end = point(token.end)\n    return node\n  }\n\n  /**\n   * @this {CompileContext}\n   * @returns {string}\n   */\n  function resume() {\n    return toString(this.stack.pop())\n  }\n\n  //\n  // Handlers.\n  //\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterlistordered() {\n    setData('expectingFirstListItemValue', true)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterlistitemvalue(token) {\n    if (getData('expectingFirstListItemValue')) {\n      const ancestor = this.stack[this.stack.length - 2]\n      assert(ancestor, 'expected nodes on stack')\n      assert(ancestor.type === 'list', 'expected list on stack')\n      ancestor.start = Number.parseInt(\n        this.sliceSerialize(token),\n        constants.numericBaseDecimal\n      )\n      setData('expectingFirstListItemValue')\n    }\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodefencedfenceinfo() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'code', 'expected code on stack')\n    node.lang = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodefencedfencemeta() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'code', 'expected code on stack')\n    node.meta = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodefencedfence() {\n    // Exit if this is the closing fence.\n    if (getData('flowCodeInside')) return\n    this.buffer()\n    setData('flowCodeInside', true)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodefenced() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'code', 'expected code on stack')\n\n    node.value = data.replace(/^(\\r?\\n|\\r)|(\\r?\\n|\\r)$/g, '')\n    setData('flowCodeInside')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodeindented() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'code', 'expected code on stack')\n\n    node.value = data.replace(/(\\r?\\n|\\r)$/g, '')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitdefinitionlabelstring(token) {\n    const label = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'definition', 'expected definition on stack')\n\n    node.label = label\n    node.identifier = normalizeIdentifier(\n      this.sliceSerialize(token)\n    ).toLowerCase()\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitdefinitiontitlestring() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'definition', 'expected definition on stack')\n\n    node.title = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitdefinitiondestinationstring() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'definition', 'expected definition on stack')\n\n    node.url = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitatxheadingsequence(token) {\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'heading', 'expected heading on stack')\n\n    if (!node.depth) {\n      const depth = this.sliceSerialize(token).length\n\n      assert(\n        depth === 1 ||\n          depth === 2 ||\n          depth === 3 ||\n          depth === 4 ||\n          depth === 5 ||\n          depth === 6,\n        'expected `depth` between `1` and `6`'\n      )\n\n      node.depth = depth\n    }\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitsetextheadingtext() {\n    setData('setextHeadingSlurpLineEnding', true)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitsetextheadinglinesequence(token) {\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'heading', 'expected heading on stack')\n\n    node.depth =\n      this.sliceSerialize(token).charCodeAt(0) === codes.equalsTo ? 1 : 2\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitsetextheading() {\n    setData('setextHeadingSlurpLineEnding')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onenterdata(token) {\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert('children' in node, 'expected parent on stack')\n\n    let tail = node.children[node.children.length - 1]\n\n    if (!tail || tail.type !== 'text') {\n      // Add a new text node.\n      tail = text()\n      // @ts-expect-error: we’ll add `end` later.\n      tail.position = {start: point(token.start)}\n      // @ts-expect-error: Assume `parent` accepts `text`.\n      node.children.push(tail)\n    }\n\n    this.stack.push(tail)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitdata(token) {\n    const tail = this.stack.pop()\n    assert(tail, 'expected a `node` to be on the stack')\n    assert('value' in tail, 'expected a `literal` to be on the stack')\n    assert(tail.position, 'expected `node` to have an open position')\n    tail.value += this.sliceSerialize(token)\n    tail.position.end = point(token.end)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitlineending(token) {\n    const context = this.stack[this.stack.length - 1]\n    assert(context, 'expected `node`')\n\n    // If we’re at a hard break, include the line ending in there.\n    if (getData('atHardBreak')) {\n      assert('children' in context, 'expected `parent`')\n      const tail = context.children[context.children.length - 1]\n      assert(tail.position, 'expected tail to have a starting position')\n      tail.position.end = point(token.end)\n      setData('atHardBreak')\n      return\n    }\n\n    if (\n      !getData('setextHeadingSlurpLineEnding') &&\n      config.canContainEols.includes(context.type)\n    ) {\n      onenterdata.call(this, token)\n      onexitdata.call(this, token)\n    }\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexithardbreak() {\n    setData('atHardBreak', true)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexithtmlflow() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'html', 'expected html on stack')\n\n    node.value = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexithtmltext() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'html', 'expected html on stack')\n\n    node.value = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitcodetext() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'inlineCode', 'expected inline code on stack')\n\n    node.value = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitlink() {\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'link', 'expected link on stack')\n\n    // Note: there are also `identifier` and `label` fields on this link node!\n    // These are used / cleaned here.\n\n    // To do: clean.\n    if (getData('inReference')) {\n      /** @type {ReferenceType} */\n      const referenceType = getData('referenceType') || 'shortcut'\n\n      node.type += 'Reference'\n      // @ts-expect-error: mutate.\n      node.referenceType = referenceType\n      // @ts-expect-error: mutate.\n      delete node.url\n      delete node.title\n    } else {\n      // @ts-expect-error: mutate.\n      delete node.identifier\n      // @ts-expect-error: mutate.\n      delete node.label\n    }\n\n    setData('referenceType')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitimage() {\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'image', 'expected image on stack')\n\n    // Note: there are also `identifier` and `label` fields on this link node!\n    // These are used / cleaned here.\n\n    // To do: clean.\n    if (getData('inReference')) {\n      /** @type {ReferenceType} */\n      const referenceType = getData('referenceType') || 'shortcut'\n\n      node.type += 'Reference'\n      // @ts-expect-error: mutate.\n      node.referenceType = referenceType\n      // @ts-expect-error: mutate.\n      delete node.url\n      delete node.title\n    } else {\n      // @ts-expect-error: mutate.\n      delete node.identifier\n      // @ts-expect-error: mutate.\n      delete node.label\n    }\n\n    setData('referenceType')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitlabeltext(token) {\n    const string = this.sliceSerialize(token)\n    const ancestor = this.stack[this.stack.length - 2]\n    assert(ancestor, 'expected ancestor on stack')\n    assert(\n      ancestor.type === 'image' || ancestor.type === 'link',\n      'expected image or link on stack'\n    )\n\n    // @ts-expect-error: stash this on the node, as it might become a reference\n    // later.\n    ancestor.label = decodeString(string)\n    // @ts-expect-error: same as above.\n    ancestor.identifier = normalizeIdentifier(string).toLowerCase()\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitlabel() {\n    const fragment = this.stack[this.stack.length - 1]\n    assert(fragment, 'expected node on stack')\n    assert(fragment.type === 'fragment', 'expected fragment on stack')\n    const value = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(\n      node.type === 'image' || node.type === 'link',\n      'expected image or link on stack'\n    )\n\n    // Assume a reference.\n    setData('inReference', true)\n\n    if (node.type === 'link') {\n      /** @type {Array<StaticPhrasingContent>} */\n      // @ts-expect-error: Assume static phrasing content.\n      const children = fragment.children\n\n      node.children = children\n    } else {\n      node.alt = value\n    }\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitresourcedestinationstring() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(\n      node.type === 'image' || node.type === 'link',\n      'expected image or link on stack'\n    )\n    node.url = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitresourcetitlestring() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(\n      node.type === 'image' || node.type === 'link',\n      'expected image or link on stack'\n    )\n    node.title = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitresource() {\n    setData('inReference')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onenterreference() {\n    setData('referenceType', 'collapsed')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitreferencestring(token) {\n    const label = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(\n      node.type === 'image' || node.type === 'link',\n      'expected image reference or link reference on stack'\n    )\n\n    // @ts-expect-error: stash this on the node, as it might become a reference\n    // later.\n    node.label = label\n    // @ts-expect-error: same as above.\n    node.identifier = normalizeIdentifier(\n      this.sliceSerialize(token)\n    ).toLowerCase()\n    setData('referenceType', 'full')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitcharacterreferencemarker(token) {\n    assert(\n      token.type === 'characterReferenceMarkerNumeric' ||\n        token.type === 'characterReferenceMarkerHexadecimal'\n    )\n    setData('characterReferenceType', token.type)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcharacterreferencevalue(token) {\n    const data = this.sliceSerialize(token)\n    const type = getData('characterReferenceType')\n    /** @type {string} */\n    let value\n\n    if (type) {\n      value = decodeNumericCharacterReference(\n        data,\n        type === types.characterReferenceMarkerNumeric\n          ? constants.numericBaseDecimal\n          : constants.numericBaseHexadecimal\n      )\n      setData('characterReferenceType')\n    } else {\n      const result = decodeNamedCharacterReference(data)\n      assert(result !== false, 'expected reference to decode')\n      value = result\n    }\n\n    const tail = this.stack.pop()\n    assert(tail, 'expected `node`')\n    assert(tail.position, 'expected `node.position`')\n    assert('value' in tail, 'expected `node.value`')\n    tail.value += value\n    tail.position.end = point(token.end)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitautolinkprotocol(token) {\n    onexitdata.call(this, token)\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'link', 'expected link on stack')\n\n    node.url = this.sliceSerialize(token)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitautolinkemail(token) {\n    onexitdata.call(this, token)\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'link', 'expected link on stack')\n\n    node.url = 'mailto:' + this.sliceSerialize(token)\n  }\n\n  //\n  // Creaters.\n  //\n\n  /** @returns {Blockquote} */\n  function blockQuote() {\n    return {type: 'blockquote', children: []}\n  }\n\n  /** @returns {Code} */\n  function codeFlow() {\n    return {type: 'code', lang: null, meta: null, value: ''}\n  }\n\n  /** @returns {InlineCode} */\n  function codeText() {\n    return {type: 'inlineCode', value: ''}\n  }\n\n  /** @returns {Definition} */\n  function definition() {\n    return {\n      type: 'definition',\n      identifier: '',\n      label: null,\n      title: null,\n      url: ''\n    }\n  }\n\n  /** @returns {Emphasis} */\n  function emphasis() {\n    return {type: 'emphasis', children: []}\n  }\n\n  /** @returns {Heading} */\n  function heading() {\n    // @ts-expect-error `depth` will be set later.\n    return {type: 'heading', depth: undefined, children: []}\n  }\n\n  /** @returns {Break} */\n  function hardBreak() {\n    return {type: 'break'}\n  }\n\n  /** @returns {HTML} */\n  function html() {\n    return {type: 'html', value: ''}\n  }\n\n  /** @returns {Image} */\n  function image() {\n    return {type: 'image', title: null, url: '', alt: null}\n  }\n\n  /** @returns {Link} */\n  function link() {\n    return {type: 'link', title: null, url: '', children: []}\n  }\n\n  /**\n   * @param {Token} token\n   * @returns {List}\n   */\n  function list(token) {\n    return {\n      type: 'list',\n      ordered: token.type === 'listOrdered',\n      start: null,\n      spread: token._spread,\n      children: []\n    }\n  }\n\n  /**\n   * @param {Token} token\n   * @returns {ListItem}\n   */\n  function listItem(token) {\n    return {\n      type: 'listItem',\n      spread: token._spread,\n      checked: null,\n      children: []\n    }\n  }\n\n  /** @returns {Paragraph} */\n  function paragraph() {\n    return {type: 'paragraph', children: []}\n  }\n\n  /** @returns {Strong} */\n  function strong() {\n    return {type: 'strong', children: []}\n  }\n\n  /** @returns {Text} */\n  function text() {\n    return {type: 'text', value: ''}\n  }\n\n  /** @returns {ThematicBreak} */\n  function thematicBreak() {\n    return {type: 'thematicBreak'}\n  }\n}\n\n/**\n * Copy a point-like value.\n *\n * @param {Point} d\n *   Point-like value.\n * @returns {Point}\n *   unist point.\n */\nfunction point(d) {\n  return {line: d.line, column: d.column, offset: d.offset}\n}\n\n/**\n * @param {Config} combined\n * @param {Array<Extension | Array<Extension>>} extensions\n * @returns {void}\n */\nfunction configure(combined, extensions) {\n  let index = -1\n\n  while (++index < extensions.length) {\n    const value = extensions[index]\n\n    if (Array.isArray(value)) {\n      configure(combined, value)\n    } else {\n      extension(combined, value)\n    }\n  }\n}\n\n/**\n * @param {Config} combined\n * @param {Extension} extension\n * @returns {void}\n */\nfunction extension(combined, extension) {\n  /** @type {keyof Extension} */\n  let key\n\n  for (key in extension) {\n    if (own.call(extension, key)) {\n      if (key === 'canContainEols') {\n        const right = extension[key]\n        if (right) {\n          combined[key].push(...right)\n        }\n      } else if (key === 'transforms') {\n        const right = extension[key]\n        if (right) {\n          combined[key].push(...right)\n        }\n      } else if (key === 'enter' || key === 'exit') {\n        const right = extension[key]\n        if (right) {\n          Object.assign(combined[key], right)\n        }\n      }\n    }\n  }\n}\n\n/** @type {OnEnterError} */\nfunction defaultOnError(left, right) {\n  if (left) {\n    throw new Error(\n      'Cannot close `' +\n        left.type +\n        '` (' +\n        stringifyPosition({start: left.start, end: left.end}) +\n        '): a different token (`' +\n        right.type +\n        '`, ' +\n        stringifyPosition({start: right.start, end: right.end}) +\n        ') is open'\n    )\n  } else {\n    throw new Error(\n      'Cannot close document, a token (`' +\n        right.type +\n        '`, ' +\n        stringifyPosition({start: right.start, end: right.end}) +\n        ') is still open'\n    )\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAAQA,EAAE,IAAIC,MAAM,QAAO,YAAY;AACvC,SAAQC,QAAQ,QAAO,sBAAsB;AAC7C,SAAQC,KAAK,QAAO,wBAAwB;AAC5C,SAAQC,UAAU,QAAO,6BAA6B;AACtD,SAAQC,WAAW,QAAO,8BAA8B;AACxD,SAAQC,+BAA+B,QAAO,mDAAmD;AACjG,SAAQC,YAAY,QAAO,8BAA8B;AACzD,SAAQC,mBAAmB,QAAO,qCAAqC;AACvE,SAAQC,KAAK,QAAO,gCAAgC;AACpD,SAAQC,SAAS,QAAO,oCAAoC;AAC5D,SAAQC,KAAK,QAAO,gCAAgC;AACpD,SAAQC,6BAA6B,QAAO,kCAAkC;AAC9E,SAAQC,iBAAiB,QAAO,+BAA+B;AAE/D,MAAMC,GAAG,GAAG,CAAC,CAAC,CAACC,cAAc;;AAE7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,YAAY;AACvB;AACF;AACA;AACA;AACA;AACA;;AAEI;AACJ;AACA;AACA;AACA;AACA;AACI,SAAAA,CAAUC,KAAK,EAAEC,QAAQ,EAAEC,OAAO,EAAE;EAClC,IAAI,OAAOD,QAAQ,KAAK,QAAQ,EAAE;IAChCC,OAAO,GAAGD,QAAQ;IAClBA,QAAQ,GAAGE,SAAS;EACtB;EAEA,OAAOC,QAAQ,CAACF,OAAO,CAAC,CACtBd,WAAW,CACTF,KAAK,CAACgB,OAAO,CAAC,CAACG,QAAQ,EAAE,CAACC,KAAK,CAACnB,UAAU,EAAE,CAACa,KAAK,EAAEC,QAAQ,EAAE,IAAI,CAAC,CAAC,CACrE,CACF;AACH,CACD;;AAEH;AACA;AACA;AACA;AACA;AACA,SAASG,QAAQA,CAACF,OAAO,EAAE;EACzB;EACA,MAAMK,MAAM,GAAG;IACbC,UAAU,EAAE,EAAE;IACdC,cAAc,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,WAAW,EAAE,QAAQ,CAAC;IAC1EC,KAAK,EAAE;MACLC,QAAQ,EAAEC,MAAM,CAACC,IAAI,CAAC;MACtBC,gBAAgB,EAAEC,WAAW;MAC7BC,aAAa,EAAED,WAAW;MAC1BE,UAAU,EAAEL,MAAM,CAACM,OAAO,CAAC;MAC3BC,UAAU,EAAEP,MAAM,CAACO,UAAU,CAAC;MAC9BC,eAAe,EAAEL,WAAW;MAC5BM,kBAAkB,EAAEN,WAAW;MAC/BO,UAAU,EAAEV,MAAM,CAACW,QAAQ,CAAC;MAC5BC,mBAAmB,EAAEC,MAAM;MAC3BC,mBAAmB,EAAED,MAAM;MAC3BE,YAAY,EAAEf,MAAM,CAACW,QAAQ,EAAEE,MAAM,CAAC;MACtCG,QAAQ,EAAEhB,MAAM,CAACgB,QAAQ,EAAEH,MAAM,CAAC;MAClCI,YAAY,EAAEd,WAAW;MACzBe,IAAI,EAAEf,WAAW;MACjBgB,aAAa,EAAEhB,WAAW;MAC1BiB,UAAU,EAAEpB,MAAM,CAACoB,UAAU,CAAC;MAC9BC,2BAA2B,EAAER,MAAM;MACnCS,qBAAqB,EAAET,MAAM;MAC7BU,qBAAqB,EAAEV,MAAM;MAC7BW,QAAQ,EAAExB,MAAM,CAACwB,QAAQ,CAAC;MAC1BC,eAAe,EAAEzB,MAAM,CAAC0B,SAAS,CAAC;MAClCC,iBAAiB,EAAE3B,MAAM,CAAC0B,SAAS,CAAC;MACpCE,QAAQ,EAAE5B,MAAM,CAAC6B,IAAI,EAAEhB,MAAM,CAAC;MAC9BiB,YAAY,EAAE3B,WAAW;MACzB4B,QAAQ,EAAE/B,MAAM,CAAC6B,IAAI,EAAEhB,MAAM,CAAC;MAC9BmB,YAAY,EAAE7B,WAAW;MACzB8B,KAAK,EAAEjC,MAAM,CAACiC,KAAK,CAAC;MACpBC,KAAK,EAAErB,MAAM;MACbZ,IAAI,EAAED,MAAM,CAACC,IAAI,CAAC;MAClBkC,QAAQ,EAAEnC,MAAM,CAACmC,QAAQ,CAAC;MAC1BC,aAAa,EAAEC,oBAAoB;MACnCC,WAAW,EAAEtC,MAAM,CAACuC,IAAI,EAAEC,kBAAkB,CAAC;MAC7CC,aAAa,EAAEzC,MAAM,CAACuC,IAAI,CAAC;MAC3BG,SAAS,EAAE1C,MAAM,CAAC0C,SAAS,CAAC;MAC5BC,SAAS,EAAEC,gBAAgB;MAC3BC,eAAe,EAAEhC,MAAM;MACvBiC,yBAAyB,EAAEjC,MAAM;MACjCkC,mBAAmB,EAAElC,MAAM;MAC3BmC,aAAa,EAAEhD,MAAM,CAACM,OAAO,CAAC;MAC9B2C,MAAM,EAAEjD,MAAM,CAACiD,MAAM,CAAC;MACtBC,aAAa,EAAElD,MAAM,CAACkD,aAAa;IACrC,CAAC;IACDC,IAAI,EAAE;MACJ9C,UAAU,EAAE+C,MAAM,EAAE;MACpBC,kBAAkB,EAAEC,wBAAwB;MAC5CvD,QAAQ,EAAEqD,MAAM,EAAE;MAClBhD,aAAa,EAAEmD,mBAAmB;MAClCrD,gBAAgB,EAAEsD,sBAAsB;MACxCjD,UAAU,EAAE6C,MAAM,EAAE;MACpBK,oBAAoB,EAAEC,UAAU;MAChCC,mCAAmC,EAAEC,8BAA8B;MACnEC,+BAA+B,EAAED,8BAA8B;MAC/DE,uBAAuB,EAAEC,6BAA6B;MACtDrD,UAAU,EAAE0C,MAAM,CAACY,gBAAgB,CAAC;MACpCC,eAAe,EAAEC,qBAAqB;MACtCtD,mBAAmB,EAAEuD,yBAAyB;MAC9CrD,mBAAmB,EAAEsD,yBAAyB;MAC9CjD,aAAa,EAAEuC,UAAU;MACzB3C,YAAY,EAAEqC,MAAM,CAACiB,kBAAkB,CAAC;MACxCrD,QAAQ,EAAEoC,MAAM,CAACkB,cAAc,CAAC;MAChCrD,YAAY,EAAEyC,UAAU;MACxBxC,IAAI,EAAEwC,UAAU;MAChBtC,UAAU,EAAEgC,MAAM,EAAE;MACpB/B,2BAA2B,EAAEkD,iCAAiC;MAC9DjD,qBAAqB,EAAEkD,2BAA2B;MAClDjD,qBAAqB,EAAEkD,2BAA2B;MAClDjD,QAAQ,EAAE4B,MAAM,EAAE;MAClB3B,eAAe,EAAE2B,MAAM,CAACsB,eAAe,CAAC;MACxC/C,iBAAiB,EAAEyB,MAAM,CAACsB,eAAe,CAAC;MAC1C9C,QAAQ,EAAEwB,MAAM,CAACuB,cAAc,CAAC;MAChC7C,YAAY,EAAE4B,UAAU;MACxB3B,QAAQ,EAAEqB,MAAM,CAACwB,cAAc,CAAC;MAChC5C,YAAY,EAAE0B,UAAU;MACxBzB,KAAK,EAAEmB,MAAM,CAACyB,WAAW,CAAC;MAC1B3C,KAAK,EAAE4C,WAAW;MAClBC,SAAS,EAAEC,eAAe;MAC1BC,UAAU,EAAEC,gBAAgB;MAC5BjF,IAAI,EAAEmD,MAAM,CAAC+B,UAAU,CAAC;MACxBhD,QAAQ,EAAEiB,MAAM,EAAE;MAClBd,WAAW,EAAEc,MAAM,EAAE;MACrBX,aAAa,EAAEW,MAAM,EAAE;MACvBV,SAAS,EAAEU,MAAM,EAAE;MACnBP,eAAe,EAAEuC,qBAAqB;MACtCtC,yBAAyB,EAAEuC,+BAA+B;MAC1DtC,mBAAmB,EAAEuC,yBAAyB;MAC9CC,QAAQ,EAAEC,cAAc;MACxBxC,aAAa,EAAEI,MAAM,CAACqC,mBAAmB,CAAC;MAC1CC,yBAAyB,EAAEC,+BAA+B;MAC1DC,iBAAiB,EAAEC,uBAAuB;MAC1C5C,MAAM,EAAEG,MAAM,EAAE;MAChBF,aAAa,EAAEE,MAAM;IACvB;EACF,CAAC;EAED0C,SAAS,CAACnG,MAAM,EAAE,CAACL,OAAO,IAAI,CAAC,CAAC,EAAEyG,eAAe,IAAI,EAAE,CAAC;;EAExD;EACA,MAAM7E,IAAI,GAAG,CAAC,CAAC;EAEf,OAAO8E,OAAO;;EAEd;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,OAAOA,CAACC,MAAM,EAAE;IACvB;IACA,IAAIC,IAAI,GAAG;MAACC,IAAI,EAAE,MAAM;MAAEC,QAAQ,EAAE;IAAE,CAAC;IACvC;IACA,MAAMC,OAAO,GAAG;MACdC,KAAK,EAAE,CAACJ,IAAI,CAAC;MACbK,UAAU,EAAE,EAAE;MACd5G,MAAM;MACNG,KAAK;MACLqD,IAAI;MACJtC,MAAM;MACN2F,MAAM;MACNC,OAAO;MACPC;IACF,CAAC;IACD;IACA,MAAMC,SAAS,GAAG,EAAE;IACpB,IAAIC,KAAK,GAAG,CAAC,CAAC;IAEd,OAAO,EAAEA,KAAK,GAAGX,MAAM,CAACY,MAAM,EAAE;MAC9B;MACA;MACA,IACEZ,MAAM,CAACW,KAAK,CAAC,CAAC,CAAC,CAAC,CAACT,IAAI,KAAKrH,KAAK,CAACwD,WAAW,IAC3C2D,MAAM,CAACW,KAAK,CAAC,CAAC,CAAC,CAAC,CAACT,IAAI,KAAKrH,KAAK,CAAC2D,aAAa,EAC7C;QACA,IAAIwD,MAAM,CAACW,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,OAAO,EAAE;UAChCD,SAAS,CAACG,IAAI,CAACF,KAAK,CAAC;QACvB,CAAC,MAAM;UACL,MAAMG,IAAI,GAAGJ,SAAS,CAACK,GAAG,EAAE;UAC5B5I,MAAM,CAAC,OAAO2I,IAAI,KAAK,QAAQ,EAAE,0BAA0B,CAAC;UAC5DH,KAAK,GAAGK,WAAW,CAAChB,MAAM,EAAEc,IAAI,EAAEH,KAAK,CAAC;QAC1C;MACF;IACF;IAEAA,KAAK,GAAG,CAAC,CAAC;IAEV,OAAO,EAAEA,KAAK,GAAGX,MAAM,CAACY,MAAM,EAAE;MAC9B,MAAMK,OAAO,GAAGvH,MAAM,CAACsG,MAAM,CAACW,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;MAExC,IAAI3H,GAAG,CAACkI,IAAI,CAACD,OAAO,EAAEjB,MAAM,CAACW,KAAK,CAAC,CAAC,CAAC,CAAC,CAACT,IAAI,CAAC,EAAE;QAC5Ce,OAAO,CAACjB,MAAM,CAACW,KAAK,CAAC,CAAC,CAAC,CAAC,CAACT,IAAI,CAAC,CAACgB,IAAI,CACjCC,MAAM,CAACC,MAAM,CACX;UAACC,cAAc,EAAErB,MAAM,CAACW,KAAK,CAAC,CAAC,CAAC,CAAC,CAACU;QAAc,CAAC,EACjDjB,OAAO,CACR,EACDJ,MAAM,CAACW,KAAK,CAAC,CAAC,CAAC,CAAC,CACjB;MACH;IACF;;IAEA;IACA,IAAIP,OAAO,CAACE,UAAU,CAACM,MAAM,GAAG,CAAC,EAAE;MACjC,MAAME,IAAI,GAAGV,OAAO,CAACE,UAAU,CAACF,OAAO,CAACE,UAAU,CAACM,MAAM,GAAG,CAAC,CAAC;MAC9D,MAAMK,OAAO,GAAGH,IAAI,CAAC,CAAC,CAAC,IAAIQ,cAAc;MACzCL,OAAO,CAACC,IAAI,CAACd,OAAO,EAAE9G,SAAS,EAAEwH,IAAI,CAAC,CAAC,CAAC,CAAC;IAC3C;;IAEA;IACAb,IAAI,CAACsB,QAAQ,GAAG;MACdC,KAAK,EAAEC,KAAK,CACVzB,MAAM,CAACY,MAAM,GAAG,CAAC,GAAGZ,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAACwB,KAAK,GAAG;QAACE,IAAI,EAAE,CAAC;QAAEC,MAAM,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAC,CAAC,CACzE;MACDC,GAAG,EAAEJ,KAAK,CACRzB,MAAM,CAACY,MAAM,GAAG,CAAC,GACbZ,MAAM,CAACA,MAAM,CAACY,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAACiB,GAAG,GAChC;QAACH,IAAI,EAAE,CAAC;QAAEC,MAAM,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAC,CAAC;IAEvC,CAAC;;IAED;IACAjB,KAAK,GAAG,CAAC,CAAC;IACV,OAAO,EAAEA,KAAK,GAAGjH,MAAM,CAACC,UAAU,CAACiH,MAAM,EAAE;MACzCX,IAAI,GAAGvG,MAAM,CAACC,UAAU,CAACgH,KAAK,CAAC,CAACV,IAAI,CAAC,IAAIA,IAAI;IAC/C;IAEA,OAAOA,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,SAASe,WAAWA,CAAChB,MAAM,EAAEwB,KAAK,EAAEZ,MAAM,EAAE;IAC1C,IAAID,KAAK,GAAGa,KAAK,GAAG,CAAC;IACrB,IAAIM,gBAAgB,GAAG,CAAC,CAAC;IACzB,IAAIC,UAAU,GAAG,KAAK;IACtB;IACA,IAAI7F,QAAQ;IACZ;IACA,IAAI8F,SAAS;IACb;IACA,IAAIC,mBAAmB;IACvB;IACA,IAAIC,QAAQ;IAEZ,OAAO,EAAEvB,KAAK,IAAIC,MAAM,EAAE;MACxB,MAAMuB,KAAK,GAAGnC,MAAM,CAACW,KAAK,CAAC;MAE3B,IACEwB,KAAK,CAAC,CAAC,CAAC,CAACjC,IAAI,KAAKrH,KAAK,CAAC2D,aAAa,IACrC2F,KAAK,CAAC,CAAC,CAAC,CAACjC,IAAI,KAAKrH,KAAK,CAACwD,WAAW,IACnC8F,KAAK,CAAC,CAAC,CAAC,CAACjC,IAAI,KAAKrH,KAAK,CAACyB,UAAU,EAClC;QACA,IAAI6H,KAAK,CAAC,CAAC,CAAC,KAAK,OAAO,EAAE;UACxBL,gBAAgB,EAAE;QACpB,CAAC,MAAM;UACLA,gBAAgB,EAAE;QACpB;QAEAI,QAAQ,GAAG5I,SAAS;MACtB,CAAC,MAAM,IAAI6I,KAAK,CAAC,CAAC,CAAC,CAACjC,IAAI,KAAKrH,KAAK,CAACuJ,eAAe,EAAE;QAClD,IAAID,KAAK,CAAC,CAAC,CAAC,KAAK,OAAO,EAAE;UACxB,IACEjG,QAAQ,IACR,CAACgG,QAAQ,IACT,CAACJ,gBAAgB,IACjB,CAACG,mBAAmB,EACpB;YACAA,mBAAmB,GAAGtB,KAAK;UAC7B;UAEAuB,QAAQ,GAAG5I,SAAS;QACtB;MACF,CAAC,MAAM,IACL6I,KAAK,CAAC,CAAC,CAAC,CAACjC,IAAI,KAAKrH,KAAK,CAACwJ,UAAU,IAClCF,KAAK,CAAC,CAAC,CAAC,CAACjC,IAAI,KAAKrH,KAAK,CAACsD,aAAa,IACrCgG,KAAK,CAAC,CAAC,CAAC,CAACjC,IAAI,KAAKrH,KAAK,CAACyJ,cAAc,IACtCH,KAAK,CAAC,CAAC,CAAC,CAACjC,IAAI,KAAKrH,KAAK,CAAC0J,cAAc,IACtCJ,KAAK,CAAC,CAAC,CAAC,CAACjC,IAAI,KAAKrH,KAAK,CAAC2J,wBAAwB,EAChD;QACA;MAAA,CACD,MAAM;QACLN,QAAQ,GAAG5I,SAAS;MACtB;MAEA,IACG,CAACwI,gBAAgB,IAChBK,KAAK,CAAC,CAAC,CAAC,KAAK,OAAO,IACpBA,KAAK,CAAC,CAAC,CAAC,CAACjC,IAAI,KAAKrH,KAAK,CAAC0J,cAAc,IACvCT,gBAAgB,KAAK,CAAC,CAAC,IACtBK,KAAK,CAAC,CAAC,CAAC,KAAK,MAAM,KAClBA,KAAK,CAAC,CAAC,CAAC,CAACjC,IAAI,KAAKrH,KAAK,CAAC2D,aAAa,IACpC2F,KAAK,CAAC,CAAC,CAAC,CAACjC,IAAI,KAAKrH,KAAK,CAACwD,WAAW,CAAE,EACzC;QACA,IAAIH,QAAQ,EAAE;UACZ,IAAIuG,SAAS,GAAG9B,KAAK;UACrBqB,SAAS,GAAG1I,SAAS;UAErB,OAAOmJ,SAAS,EAAE,EAAE;YAClB,MAAMC,SAAS,GAAG1C,MAAM,CAACyC,SAAS,CAAC;YAEnC,IACEC,SAAS,CAAC,CAAC,CAAC,CAACxC,IAAI,KAAKrH,KAAK,CAACmG,UAAU,IACtC0D,SAAS,CAAC,CAAC,CAAC,CAACxC,IAAI,KAAKrH,KAAK,CAACuJ,eAAe,EAC3C;cACA,IAAIM,SAAS,CAAC,CAAC,CAAC,KAAK,MAAM,EAAE;cAE7B,IAAIV,SAAS,EAAE;gBACbhC,MAAM,CAACgC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC9B,IAAI,GAAGrH,KAAK,CAACuJ,eAAe;gBACjDL,UAAU,GAAG,IAAI;cACnB;cAEAW,SAAS,CAAC,CAAC,CAAC,CAACxC,IAAI,GAAGrH,KAAK,CAACmG,UAAU;cACpCgD,SAAS,GAAGS,SAAS;YACvB,CAAC,MAAM,IACLC,SAAS,CAAC,CAAC,CAAC,CAACxC,IAAI,KAAKrH,KAAK,CAACwJ,UAAU,IACtCK,SAAS,CAAC,CAAC,CAAC,CAACxC,IAAI,KAAKrH,KAAK,CAAC8J,gBAAgB,IAC5CD,SAAS,CAAC,CAAC,CAAC,CAACxC,IAAI,KAAKrH,KAAK,CAAC+J,0BAA0B,IACtDF,SAAS,CAAC,CAAC,CAAC,CAACxC,IAAI,KAAKrH,KAAK,CAACgK,gBAAgB,IAC5CH,SAAS,CAAC,CAAC,CAAC,CAACxC,IAAI,KAAKrH,KAAK,CAACiK,cAAc,EAC1C;cACA;YAAA,CACD,MAAM;cACL;YACF;UACF;UAEA,IACEb,mBAAmB,KAClB,CAACD,SAAS,IAAIC,mBAAmB,GAAGD,SAAS,CAAC,EAC/C;YACA9F,QAAQ,CAAC6G,OAAO,GAAG,IAAI;UACzB;;UAEA;UACA7G,QAAQ,CAAC2F,GAAG,GAAGV,MAAM,CAACC,MAAM,CAC1B,CAAC,CAAC,EACFY,SAAS,GAAGhC,MAAM,CAACgC,SAAS,CAAC,CAAC,CAAC,CAAC,CAACR,KAAK,GAAGW,KAAK,CAAC,CAAC,CAAC,CAACN,GAAG,CACtD;UAED7B,MAAM,CAACgD,MAAM,CAAChB,SAAS,IAAIrB,KAAK,EAAE,CAAC,EAAE,CAAC,MAAM,EAAEzE,QAAQ,EAAEiG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;UAClExB,KAAK,EAAE;UACPC,MAAM,EAAE;QACV;;QAEA;QACA,IAAIuB,KAAK,CAAC,CAAC,CAAC,CAACjC,IAAI,KAAKrH,KAAK,CAAC0J,cAAc,EAAE;UAC1CrG,QAAQ,GAAG;YACTgE,IAAI,EAAE,UAAU;YAChB6C,OAAO,EAAE,KAAK;YACdvB,KAAK,EAAEL,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEe,KAAK,CAAC,CAAC,CAAC,CAACX,KAAK,CAAC;YACxC;YACAK,GAAG,EAAEvI;UACP,CAAC;UACD;UACA0G,MAAM,CAACgD,MAAM,CAACrC,KAAK,EAAE,CAAC,EAAE,CAAC,OAAO,EAAEzE,QAAQ,EAAEiG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;UACtDxB,KAAK,EAAE;UACPC,MAAM,EAAE;UACRqB,mBAAmB,GAAG3I,SAAS;UAC/B4I,QAAQ,GAAG,IAAI;QACjB;MACF;IACF;IAEAlC,MAAM,CAACwB,KAAK,CAAC,CAAC,CAAC,CAAC,CAACuB,OAAO,GAAGhB,UAAU;IACrC,OAAOnB,MAAM;EACf;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASJ,OAAOA,CAACyC,GAAG,EAAE9J,KAAK,EAAE;IAC3B8B,IAAI,CAACgI,GAAG,CAAC,GAAG9J,KAAK;EACnB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASsH,OAAOA,CAACwC,GAAG,EAAE;IACpB,OAAOhI,IAAI,CAACgI,GAAG,CAAC;EAClB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASlJ,MAAMA,CAACmJ,MAAM,EAAEC,GAAG,EAAE;IAC3B,OAAOC,IAAI;;IAEX;AACJ;AACA;AACA;AACA;IACI,SAASA,IAAIA,CAACC,KAAK,EAAE;MACnBxJ,KAAK,CAACqH,IAAI,CAAC,IAAI,EAAEgC,MAAM,CAACG,KAAK,CAAC,EAAEA,KAAK,CAAC;MACtC,IAAIF,GAAG,EAAEA,GAAG,CAACjC,IAAI,CAAC,IAAI,EAAEmC,KAAK,CAAC;IAChC;EACF;;EAEA;AACF;AACA;AACA;EACE,SAASzI,MAAMA,CAAA,EAAG;IAChB,IAAI,CAACyF,KAAK,CAACQ,IAAI,CAAC;MAACX,IAAI,EAAE,UAAU;MAAEC,QAAQ,EAAE;IAAE,CAAC,CAAC;EACnD;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAAStG,KAAKA,CAACyJ,IAAI,EAAED,KAAK,EAAEE,YAAY,EAAE;IACxC,MAAMC,MAAM,GAAG,IAAI,CAACnD,KAAK,CAAC,IAAI,CAACA,KAAK,CAACO,MAAM,GAAG,CAAC,CAAC;IAChDzI,MAAM,CAACqL,MAAM,EAAE,mBAAmB,CAAC;IACnCrL,MAAM,CAAC,UAAU,IAAIqL,MAAM,EAAE,mBAAmB,CAAC;IACjD;IACAA,MAAM,CAACrD,QAAQ,CAACU,IAAI,CAACyC,IAAI,CAAC;IAC1B,IAAI,CAACjD,KAAK,CAACQ,IAAI,CAACyC,IAAI,CAAC;IACrB,IAAI,CAAChD,UAAU,CAACO,IAAI,CAAC,CAACwC,KAAK,EAAEE,YAAY,CAAC,CAAC;IAC3C;IACAD,IAAI,CAAC/B,QAAQ,GAAG;MAACC,KAAK,EAAEC,KAAK,CAAC4B,KAAK,CAAC7B,KAAK;IAAC,CAAC;IAC3C,OAAO8B,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASnG,MAAMA,CAACgG,GAAG,EAAE;IACnB,OAAOM,KAAK;;IAEZ;AACJ;AACA;AACA;AACA;IACI,SAASA,KAAKA,CAACJ,KAAK,EAAE;MACpB,IAAIF,GAAG,EAAEA,GAAG,CAACjC,IAAI,CAAC,IAAI,EAAEmC,KAAK,CAAC;MAC9BnG,IAAI,CAACgE,IAAI,CAAC,IAAI,EAAEmC,KAAK,CAAC;IACxB;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASnG,IAAIA,CAACmG,KAAK,EAAEK,WAAW,EAAE;IAChC,MAAMJ,IAAI,GAAG,IAAI,CAACjD,KAAK,CAACU,GAAG,EAAE;IAC7B5I,MAAM,CAACmL,IAAI,EAAE,iBAAiB,CAAC;IAC/B,MAAMF,IAAI,GAAG,IAAI,CAAC9C,UAAU,CAACS,GAAG,EAAE;IAElC,IAAI,CAACqC,IAAI,EAAE;MACT,MAAM,IAAIO,KAAK,CACb,gBAAgB,GACdN,KAAK,CAACnD,IAAI,GACV,KAAK,GACLnH,iBAAiB,CAAC;QAACyI,KAAK,EAAE6B,KAAK,CAAC7B,KAAK;QAAEK,GAAG,EAAEwB,KAAK,CAACxB;MAAG,CAAC,CAAC,GACvD,kBAAkB,CACrB;IACH,CAAC,MAAM,IAAIuB,IAAI,CAAC,CAAC,CAAC,CAAClD,IAAI,KAAKmD,KAAK,CAACnD,IAAI,EAAE;MACtC,IAAIwD,WAAW,EAAE;QACfA,WAAW,CAACxC,IAAI,CAAC,IAAI,EAAEmC,KAAK,EAAED,IAAI,CAAC,CAAC,CAAC,CAAC;MACxC,CAAC,MAAM;QACL,MAAMnC,OAAO,GAAGmC,IAAI,CAAC,CAAC,CAAC,IAAI9B,cAAc;QACzCL,OAAO,CAACC,IAAI,CAAC,IAAI,EAAEmC,KAAK,EAAED,IAAI,CAAC,CAAC,CAAC,CAAC;MACpC;IACF;IAEAjL,MAAM,CAACmL,IAAI,CAACpD,IAAI,KAAK,UAAU,EAAE,8BAA8B,CAAC;IAChE/H,MAAM,CAACmL,IAAI,CAAC/B,QAAQ,EAAE,mCAAmC,CAAC;IAC1D+B,IAAI,CAAC/B,QAAQ,CAACM,GAAG,GAAGJ,KAAK,CAAC4B,KAAK,CAACxB,GAAG,CAAC;IACpC,OAAOyB,IAAI;EACb;;EAEA;AACF;AACA;AACA;EACE,SAAS/C,MAAMA,CAAA,EAAG;IAChB,OAAOnI,QAAQ,CAAC,IAAI,CAACiI,KAAK,CAACU,GAAG,EAAE,CAAC;EACnC;;EAEA;EACA;EACA;;EAEA;AACF;AACA;AACA;EACE,SAASxE,kBAAkBA,CAAA,EAAG;IAC5BiE,OAAO,CAAC,6BAA6B,EAAE,IAAI,CAAC;EAC9C;;EAEA;AACF;AACA;AACA;EACE,SAASpE,oBAAoBA,CAACiH,KAAK,EAAE;IACnC,IAAI5C,OAAO,CAAC,6BAA6B,CAAC,EAAE;MAC1C,MAAMmD,QAAQ,GAAG,IAAI,CAACvD,KAAK,CAAC,IAAI,CAACA,KAAK,CAACO,MAAM,GAAG,CAAC,CAAC;MAClDzI,MAAM,CAACyL,QAAQ,EAAE,yBAAyB,CAAC;MAC3CzL,MAAM,CAACyL,QAAQ,CAAC1D,IAAI,KAAK,MAAM,EAAE,wBAAwB,CAAC;MAC1D0D,QAAQ,CAACpC,KAAK,GAAGqC,MAAM,CAACC,QAAQ,CAC9B,IAAI,CAACzC,cAAc,CAACgC,KAAK,CAAC,EAC1BzK,SAAS,CAACmL,kBAAkB,CAC7B;MACDvD,OAAO,CAAC,6BAA6B,CAAC;IACxC;EACF;;EAEA;AACF;AACA;AACA;EACE,SAAStC,yBAAyBA,CAAA,EAAG;IACnC,MAAMjD,IAAI,GAAG,IAAI,CAACsF,MAAM,EAAE;IAC1B,MAAM+C,IAAI,GAAG,IAAI,CAACjD,KAAK,CAAC,IAAI,CAACA,KAAK,CAACO,MAAM,GAAG,CAAC,CAAC;IAC9CzI,MAAM,CAACmL,IAAI,EAAE,wBAAwB,CAAC;IACtCnL,MAAM,CAACmL,IAAI,CAACpD,IAAI,KAAK,MAAM,EAAE,wBAAwB,CAAC;IACtDoD,IAAI,CAACU,IAAI,GAAG/I,IAAI;EAClB;;EAEA;AACF;AACA;AACA;EACE,SAASkD,yBAAyBA,CAAA,EAAG;IACnC,MAAMlD,IAAI,GAAG,IAAI,CAACsF,MAAM,EAAE;IAC1B,MAAM+C,IAAI,GAAG,IAAI,CAACjD,KAAK,CAAC,IAAI,CAACA,KAAK,CAACO,MAAM,GAAG,CAAC,CAAC;IAC9CzI,MAAM,CAACmL,IAAI,EAAE,wBAAwB,CAAC;IACtCnL,MAAM,CAACmL,IAAI,CAACpD,IAAI,KAAK,MAAM,EAAE,wBAAwB,CAAC;IACtDoD,IAAI,CAACW,IAAI,GAAGhJ,IAAI;EAClB;;EAEA;AACF;AACA;AACA;EACE,SAASgD,qBAAqBA,CAAA,EAAG;IAC/B;IACA,IAAIwC,OAAO,CAAC,gBAAgB,CAAC,EAAE;IAC/B,IAAI,CAAC7F,MAAM,EAAE;IACb4F,OAAO,CAAC,gBAAgB,EAAE,IAAI,CAAC;EACjC;;EAEA;AACF;AACA;AACA;EACE,SAASzC,gBAAgBA,CAAA,EAAG;IAC1B,MAAM9C,IAAI,GAAG,IAAI,CAACsF,MAAM,EAAE;IAC1B,MAAM+C,IAAI,GAAG,IAAI,CAACjD,KAAK,CAAC,IAAI,CAACA,KAAK,CAACO,MAAM,GAAG,CAAC,CAAC;IAC9CzI,MAAM,CAACmL,IAAI,EAAE,wBAAwB,CAAC;IACtCnL,MAAM,CAACmL,IAAI,CAACpD,IAAI,KAAK,MAAM,EAAE,wBAAwB,CAAC;IAEtDoD,IAAI,CAACnK,KAAK,GAAG8B,IAAI,CAACiJ,OAAO,CAAC,0BAA0B,EAAE,EAAE,CAAC;IACzD1D,OAAO,CAAC,gBAAgB,CAAC;EAC3B;;EAEA;AACF;AACA;AACA;EACE,SAASpC,kBAAkBA,CAAA,EAAG;IAC5B,MAAMnD,IAAI,GAAG,IAAI,CAACsF,MAAM,EAAE;IAC1B,MAAM+C,IAAI,GAAG,IAAI,CAACjD,KAAK,CAAC,IAAI,CAACA,KAAK,CAACO,MAAM,GAAG,CAAC,CAAC;IAC9CzI,MAAM,CAACmL,IAAI,EAAE,wBAAwB,CAAC;IACtCnL,MAAM,CAACmL,IAAI,CAACpD,IAAI,KAAK,MAAM,EAAE,wBAAwB,CAAC;IAEtDoD,IAAI,CAACnK,KAAK,GAAG8B,IAAI,CAACiJ,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC;EAC/C;;EAEA;AACF;AACA;AACA;EACE,SAAS3F,2BAA2BA,CAAC8E,KAAK,EAAE;IAC1C,MAAMpH,KAAK,GAAG,IAAI,CAACsE,MAAM,EAAE;IAC3B,MAAM+C,IAAI,GAAG,IAAI,CAACjD,KAAK,CAAC,IAAI,CAACA,KAAK,CAACO,MAAM,GAAG,CAAC,CAAC;IAC9CzI,MAAM,CAACmL,IAAI,EAAE,wBAAwB,CAAC;IACtCnL,MAAM,CAACmL,IAAI,CAACpD,IAAI,KAAK,YAAY,EAAE,8BAA8B,CAAC;IAElEoD,IAAI,CAACrH,KAAK,GAAGA,KAAK;IAClBqH,IAAI,CAACa,UAAU,GAAGzL,mBAAmB,CACnC,IAAI,CAAC2I,cAAc,CAACgC,KAAK,CAAC,CAC3B,CAACe,WAAW,EAAE;EACjB;;EAEA;AACF;AACA;AACA;EACE,SAAS5F,2BAA2BA,CAAA,EAAG;IACrC,MAAMvD,IAAI,GAAG,IAAI,CAACsF,MAAM,EAAE;IAC1B,MAAM+C,IAAI,GAAG,IAAI,CAACjD,KAAK,CAAC,IAAI,CAACA,KAAK,CAACO,MAAM,GAAG,CAAC,CAAC;IAC9CzI,MAAM,CAACmL,IAAI,EAAE,wBAAwB,CAAC;IACtCnL,MAAM,CAACmL,IAAI,CAACpD,IAAI,KAAK,YAAY,EAAE,8BAA8B,CAAC;IAElEoD,IAAI,CAACe,KAAK,GAAGpJ,IAAI;EACnB;;EAEA;AACF;AACA;AACA;EACE,SAASqD,iCAAiCA,CAAA,EAAG;IAC3C,MAAMrD,IAAI,GAAG,IAAI,CAACsF,MAAM,EAAE;IAC1B,MAAM+C,IAAI,GAAG,IAAI,CAACjD,KAAK,CAAC,IAAI,CAACA,KAAK,CAACO,MAAM,GAAG,CAAC,CAAC;IAC9CzI,MAAM,CAACmL,IAAI,EAAE,wBAAwB,CAAC;IACtCnL,MAAM,CAACmL,IAAI,CAACpD,IAAI,KAAK,YAAY,EAAE,8BAA8B,CAAC;IAElEoD,IAAI,CAACgB,GAAG,GAAGrJ,IAAI;EACjB;;EAEA;AACF;AACA;AACA;EACE,SAASoC,wBAAwBA,CAACgG,KAAK,EAAE;IACvC,MAAMC,IAAI,GAAG,IAAI,CAACjD,KAAK,CAAC,IAAI,CAACA,KAAK,CAACO,MAAM,GAAG,CAAC,CAAC;IAC9CzI,MAAM,CAACmL,IAAI,EAAE,wBAAwB,CAAC;IACtCnL,MAAM,CAACmL,IAAI,CAACpD,IAAI,KAAK,SAAS,EAAE,2BAA2B,CAAC;IAE5D,IAAI,CAACoD,IAAI,CAACiB,KAAK,EAAE;MACf,MAAMA,KAAK,GAAG,IAAI,CAAClD,cAAc,CAACgC,KAAK,CAAC,CAACzC,MAAM;MAE/CzI,MAAM,CACJoM,KAAK,KAAK,CAAC,IACTA,KAAK,KAAK,CAAC,IACXA,KAAK,KAAK,CAAC,IACXA,KAAK,KAAK,CAAC,IACXA,KAAK,KAAK,CAAC,IACXA,KAAK,KAAK,CAAC,EACb,sCAAsC,CACvC;MAEDjB,IAAI,CAACiB,KAAK,GAAGA,KAAK;IACpB;EACF;;EAEA;AACF;AACA;AACA;EACE,SAAS3E,uBAAuBA,CAAA,EAAG;IACjCY,OAAO,CAAC,8BAA8B,EAAE,IAAI,CAAC;EAC/C;;EAEA;AACF;AACA;AACA;EACE,SAASd,+BAA+BA,CAAC2D,KAAK,EAAE;IAC9C,MAAMC,IAAI,GAAG,IAAI,CAACjD,KAAK,CAAC,IAAI,CAACA,KAAK,CAACO,MAAM,GAAG,CAAC,CAAC;IAC9CzI,MAAM,CAACmL,IAAI,EAAE,wBAAwB,CAAC;IACtCnL,MAAM,CAACmL,IAAI,CAACpD,IAAI,KAAK,SAAS,EAAE,2BAA2B,CAAC;IAE5DoD,IAAI,CAACiB,KAAK,GACR,IAAI,CAAClD,cAAc,CAACgC,KAAK,CAAC,CAACmB,UAAU,CAAC,CAAC,CAAC,KAAK7L,KAAK,CAAC8L,QAAQ,GAAG,CAAC,GAAG,CAAC;EACvE;;EAEA;AACF;AACA;AACA;EACE,SAASjF,mBAAmBA,CAAA,EAAG;IAC7BgB,OAAO,CAAC,8BAA8B,CAAC;EACzC;;EAEA;AACF;AACA;AACA;;EAEE,SAAStG,WAAWA,CAACmJ,KAAK,EAAE;IAC1B,MAAMC,IAAI,GAAG,IAAI,CAACjD,KAAK,CAAC,IAAI,CAACA,KAAK,CAACO,MAAM,GAAG,CAAC,CAAC;IAC9CzI,MAAM,CAACmL,IAAI,EAAE,wBAAwB,CAAC;IACtCnL,MAAM,CAAC,UAAU,IAAImL,IAAI,EAAE,0BAA0B,CAAC;IAEtD,IAAIxC,IAAI,GAAGwC,IAAI,CAACnD,QAAQ,CAACmD,IAAI,CAACnD,QAAQ,CAACS,MAAM,GAAG,CAAC,CAAC;IAElD,IAAI,CAACE,IAAI,IAAIA,IAAI,CAACZ,IAAI,KAAK,MAAM,EAAE;MACjC;MACAY,IAAI,GAAG4D,IAAI,EAAE;MACb;MACA5D,IAAI,CAACS,QAAQ,GAAG;QAACC,KAAK,EAAEC,KAAK,CAAC4B,KAAK,CAAC7B,KAAK;MAAC,CAAC;MAC3C;MACA8B,IAAI,CAACnD,QAAQ,CAACU,IAAI,CAACC,IAAI,CAAC;IAC1B;IAEA,IAAI,CAACT,KAAK,CAACQ,IAAI,CAACC,IAAI,CAAC;EACvB;;EAEA;AACF;AACA;AACA;;EAEE,SAASrD,UAAUA,CAAC4F,KAAK,EAAE;IACzB,MAAMvC,IAAI,GAAG,IAAI,CAACT,KAAK,CAACU,GAAG,EAAE;IAC7B5I,MAAM,CAAC2I,IAAI,EAAE,sCAAsC,CAAC;IACpD3I,MAAM,CAAC,OAAO,IAAI2I,IAAI,EAAE,yCAAyC,CAAC;IAClE3I,MAAM,CAAC2I,IAAI,CAACS,QAAQ,EAAE,0CAA0C,CAAC;IACjET,IAAI,CAAC3H,KAAK,IAAI,IAAI,CAACkI,cAAc,CAACgC,KAAK,CAAC;IACxCvC,IAAI,CAACS,QAAQ,CAACM,GAAG,GAAGJ,KAAK,CAAC4B,KAAK,CAACxB,GAAG,CAAC;EACtC;;EAEA;AACF;AACA;AACA;;EAEE,SAAS5C,gBAAgBA,CAACoE,KAAK,EAAE;IAC/B,MAAMjD,OAAO,GAAG,IAAI,CAACC,KAAK,CAAC,IAAI,CAACA,KAAK,CAACO,MAAM,GAAG,CAAC,CAAC;IACjDzI,MAAM,CAACiI,OAAO,EAAE,iBAAiB,CAAC;;IAElC;IACA,IAAIK,OAAO,CAAC,aAAa,CAAC,EAAE;MAC1BtI,MAAM,CAAC,UAAU,IAAIiI,OAAO,EAAE,mBAAmB,CAAC;MAClD,MAAMU,IAAI,GAAGV,OAAO,CAACD,QAAQ,CAACC,OAAO,CAACD,QAAQ,CAACS,MAAM,GAAG,CAAC,CAAC;MAC1DzI,MAAM,CAAC2I,IAAI,CAACS,QAAQ,EAAE,2CAA2C,CAAC;MAClET,IAAI,CAACS,QAAQ,CAACM,GAAG,GAAGJ,KAAK,CAAC4B,KAAK,CAACxB,GAAG,CAAC;MACpCrB,OAAO,CAAC,aAAa,CAAC;MACtB;IACF;IAEA,IACE,CAACC,OAAO,CAAC,8BAA8B,CAAC,IACxC/G,MAAM,CAACE,cAAc,CAAC+K,QAAQ,CAACvE,OAAO,CAACF,IAAI,CAAC,EAC5C;MACAhG,WAAW,CAACgH,IAAI,CAAC,IAAI,EAAEmC,KAAK,CAAC;MAC7B5F,UAAU,CAACyD,IAAI,CAAC,IAAI,EAAEmC,KAAK,CAAC;IAC9B;EACF;;EAEA;AACF;AACA;AACA;;EAEE,SAAS5E,eAAeA,CAAA,EAAG;IACzB+B,OAAO,CAAC,aAAa,EAAE,IAAI,CAAC;EAC9B;;EAEA;AACF;AACA;AACA;;EAEE,SAAS9B,cAAcA,CAAA,EAAG;IACxB,MAAMzD,IAAI,GAAG,IAAI,CAACsF,MAAM,EAAE;IAC1B,MAAM+C,IAAI,GAAG,IAAI,CAACjD,KAAK,CAAC,IAAI,CAACA,KAAK,CAACO,MAAM,GAAG,CAAC,CAAC;IAC9CzI,MAAM,CAACmL,IAAI,EAAE,wBAAwB,CAAC;IACtCnL,MAAM,CAACmL,IAAI,CAACpD,IAAI,KAAK,MAAM,EAAE,wBAAwB,CAAC;IAEtDoD,IAAI,CAACnK,KAAK,GAAG8B,IAAI;EACnB;;EAEA;AACF;AACA;AACA;;EAEE,SAAS0D,cAAcA,CAAA,EAAG;IACxB,MAAM1D,IAAI,GAAG,IAAI,CAACsF,MAAM,EAAE;IAC1B,MAAM+C,IAAI,GAAG,IAAI,CAACjD,KAAK,CAAC,IAAI,CAACA,KAAK,CAACO,MAAM,GAAG,CAAC,CAAC;IAC9CzI,MAAM,CAACmL,IAAI,EAAE,wBAAwB,CAAC;IACtCnL,MAAM,CAACmL,IAAI,CAACpD,IAAI,KAAK,MAAM,EAAE,wBAAwB,CAAC;IAEtDoD,IAAI,CAACnK,KAAK,GAAG8B,IAAI;EACnB;;EAEA;AACF;AACA;AACA;;EAEE,SAASoD,cAAcA,CAAA,EAAG;IACxB,MAAMpD,IAAI,GAAG,IAAI,CAACsF,MAAM,EAAE;IAC1B,MAAM+C,IAAI,GAAG,IAAI,CAACjD,KAAK,CAAC,IAAI,CAACA,KAAK,CAACO,MAAM,GAAG,CAAC,CAAC;IAC9CzI,MAAM,CAACmL,IAAI,EAAE,wBAAwB,CAAC;IACtCnL,MAAM,CAACmL,IAAI,CAACpD,IAAI,KAAK,YAAY,EAAE,+BAA+B,CAAC;IAEnEoD,IAAI,CAACnK,KAAK,GAAG8B,IAAI;EACnB;;EAEA;AACF;AACA;AACA;;EAEE,SAASiE,UAAUA,CAAA,EAAG;IACpB,MAAMoE,IAAI,GAAG,IAAI,CAACjD,KAAK,CAAC,IAAI,CAACA,KAAK,CAACO,MAAM,GAAG,CAAC,CAAC;IAC9CzI,MAAM,CAACmL,IAAI,EAAE,wBAAwB,CAAC;IACtCnL,MAAM,CAACmL,IAAI,CAACpD,IAAI,KAAK,MAAM,EAAE,wBAAwB,CAAC;;IAEtD;IACA;;IAEA;IACA,IAAIO,OAAO,CAAC,aAAa,CAAC,EAAE;MAC1B;MACA,MAAMmE,aAAa,GAAGnE,OAAO,CAAC,eAAe,CAAC,IAAI,UAAU;MAE5D6C,IAAI,CAACpD,IAAI,IAAI,WAAW;MACxB;MACAoD,IAAI,CAACsB,aAAa,GAAGA,aAAa;MAClC;MACA,OAAOtB,IAAI,CAACgB,GAAG;MACf,OAAOhB,IAAI,CAACe,KAAK;IACnB,CAAC,MAAM;MACL;MACA,OAAOf,IAAI,CAACa,UAAU;MACtB;MACA,OAAOb,IAAI,CAACrH,KAAK;IACnB;IAEAuE,OAAO,CAAC,eAAe,CAAC;EAC1B;;EAEA;AACF;AACA;AACA;;EAEE,SAAS5B,WAAWA,CAAA,EAAG;IACrB,MAAM0E,IAAI,GAAG,IAAI,CAACjD,KAAK,CAAC,IAAI,CAACA,KAAK,CAACO,MAAM,GAAG,CAAC,CAAC;IAC9CzI,MAAM,CAACmL,IAAI,EAAE,wBAAwB,CAAC;IACtCnL,MAAM,CAACmL,IAAI,CAACpD,IAAI,KAAK,OAAO,EAAE,yBAAyB,CAAC;;IAExD;IACA;;IAEA;IACA,IAAIO,OAAO,CAAC,aAAa,CAAC,EAAE;MAC1B;MACA,MAAMmE,aAAa,GAAGnE,OAAO,CAAC,eAAe,CAAC,IAAI,UAAU;MAE5D6C,IAAI,CAACpD,IAAI,IAAI,WAAW;MACxB;MACAoD,IAAI,CAACsB,aAAa,GAAGA,aAAa;MAClC;MACA,OAAOtB,IAAI,CAACgB,GAAG;MACf,OAAOhB,IAAI,CAACe,KAAK;IACnB,CAAC,MAAM;MACL;MACA,OAAOf,IAAI,CAACa,UAAU;MACtB;MACA,OAAOb,IAAI,CAACrH,KAAK;IACnB;IAEAuE,OAAO,CAAC,eAAe,CAAC;EAC1B;;EAEA;AACF;AACA;AACA;;EAEE,SAASzB,eAAeA,CAACsE,KAAK,EAAE;IAC9B,MAAMwB,MAAM,GAAG,IAAI,CAACxD,cAAc,CAACgC,KAAK,CAAC;IACzC,MAAMO,QAAQ,GAAG,IAAI,CAACvD,KAAK,CAAC,IAAI,CAACA,KAAK,CAACO,MAAM,GAAG,CAAC,CAAC;IAClDzI,MAAM,CAACyL,QAAQ,EAAE,4BAA4B,CAAC;IAC9CzL,MAAM,CACJyL,QAAQ,CAAC1D,IAAI,KAAK,OAAO,IAAI0D,QAAQ,CAAC1D,IAAI,KAAK,MAAM,EACrD,iCAAiC,CAClC;;IAED;IACA;IACA0D,QAAQ,CAAC3H,KAAK,GAAGxD,YAAY,CAACoM,MAAM,CAAC;IACrC;IACAjB,QAAQ,CAACO,UAAU,GAAGzL,mBAAmB,CAACmM,MAAM,CAAC,CAACT,WAAW,EAAE;EACjE;;EAEA;AACF;AACA;AACA;;EAEE,SAASvF,WAAWA,CAAA,EAAG;IACrB,MAAMiG,QAAQ,GAAG,IAAI,CAACzE,KAAK,CAAC,IAAI,CAACA,KAAK,CAACO,MAAM,GAAG,CAAC,CAAC;IAClDzI,MAAM,CAAC2M,QAAQ,EAAE,wBAAwB,CAAC;IAC1C3M,MAAM,CAAC2M,QAAQ,CAAC5E,IAAI,KAAK,UAAU,EAAE,4BAA4B,CAAC;IAClE,MAAM/G,KAAK,GAAG,IAAI,CAACoH,MAAM,EAAE;IAC3B,MAAM+C,IAAI,GAAG,IAAI,CAACjD,KAAK,CAAC,IAAI,CAACA,KAAK,CAACO,MAAM,GAAG,CAAC,CAAC;IAC9CzI,MAAM,CAACmL,IAAI,EAAE,wBAAwB,CAAC;IACtCnL,MAAM,CACJmL,IAAI,CAACpD,IAAI,KAAK,OAAO,IAAIoD,IAAI,CAACpD,IAAI,KAAK,MAAM,EAC7C,iCAAiC,CAClC;;IAED;IACAM,OAAO,CAAC,aAAa,EAAE,IAAI,CAAC;IAE5B,IAAI8C,IAAI,CAACpD,IAAI,KAAK,MAAM,EAAE;MACxB;MACA;MACA,MAAMC,QAAQ,GAAG2E,QAAQ,CAAC3E,QAAQ;MAElCmD,IAAI,CAACnD,QAAQ,GAAGA,QAAQ;IAC1B,CAAC,MAAM;MACLmD,IAAI,CAACyB,GAAG,GAAG5L,KAAK;IAClB;EACF;;EAEA;AACF;AACA;AACA;;EAEE,SAASiG,+BAA+BA,CAAA,EAAG;IACzC,MAAMnE,IAAI,GAAG,IAAI,CAACsF,MAAM,EAAE;IAC1B,MAAM+C,IAAI,GAAG,IAAI,CAACjD,KAAK,CAAC,IAAI,CAACA,KAAK,CAACO,MAAM,GAAG,CAAC,CAAC;IAC9CzI,MAAM,CAACmL,IAAI,EAAE,wBAAwB,CAAC;IACtCnL,MAAM,CACJmL,IAAI,CAACpD,IAAI,KAAK,OAAO,IAAIoD,IAAI,CAACpD,IAAI,KAAK,MAAM,EAC7C,iCAAiC,CAClC;IACDoD,IAAI,CAACgB,GAAG,GAAGrJ,IAAI;EACjB;;EAEA;AACF;AACA;AACA;;EAEE,SAASoE,yBAAyBA,CAAA,EAAG;IACnC,MAAMpE,IAAI,GAAG,IAAI,CAACsF,MAAM,EAAE;IAC1B,MAAM+C,IAAI,GAAG,IAAI,CAACjD,KAAK,CAAC,IAAI,CAACA,KAAK,CAACO,MAAM,GAAG,CAAC,CAAC;IAC9CzI,MAAM,CAACmL,IAAI,EAAE,wBAAwB,CAAC;IACtCnL,MAAM,CACJmL,IAAI,CAACpD,IAAI,KAAK,OAAO,IAAIoD,IAAI,CAACpD,IAAI,KAAK,MAAM,EAC7C,iCAAiC,CAClC;IACDoD,IAAI,CAACe,KAAK,GAAGpJ,IAAI;EACnB;;EAEA;AACF;AACA;AACA;;EAEE,SAASsE,cAAcA,CAAA,EAAG;IACxBiB,OAAO,CAAC,aAAa,CAAC;EACxB;;EAEA;AACF;AACA;AACA;;EAEE,SAAS7D,gBAAgBA,CAAA,EAAG;IAC1B6D,OAAO,CAAC,eAAe,EAAE,WAAW,CAAC;EACvC;;EAEA;AACF;AACA;AACA;;EAEE,SAASrB,qBAAqBA,CAACkE,KAAK,EAAE;IACpC,MAAMpH,KAAK,GAAG,IAAI,CAACsE,MAAM,EAAE;IAC3B,MAAM+C,IAAI,GAAG,IAAI,CAACjD,KAAK,CAAC,IAAI,CAACA,KAAK,CAACO,MAAM,GAAG,CAAC,CAAC;IAC9CzI,MAAM,CAACmL,IAAI,EAAE,wBAAwB,CAAC;IACtCnL,MAAM,CACJmL,IAAI,CAACpD,IAAI,KAAK,OAAO,IAAIoD,IAAI,CAACpD,IAAI,KAAK,MAAM,EAC7C,qDAAqD,CACtD;;IAED;IACA;IACAoD,IAAI,CAACrH,KAAK,GAAGA,KAAK;IAClB;IACAqH,IAAI,CAACa,UAAU,GAAGzL,mBAAmB,CACnC,IAAI,CAAC2I,cAAc,CAACgC,KAAK,CAAC,CAC3B,CAACe,WAAW,EAAE;IACf5D,OAAO,CAAC,eAAe,EAAE,MAAM,CAAC;EAClC;;EAEA;AACF;AACA;AACA;;EAEE,SAAS7C,8BAA8BA,CAAC0F,KAAK,EAAE;IAC7ClL,MAAM,CACJkL,KAAK,CAACnD,IAAI,KAAK,iCAAiC,IAC9CmD,KAAK,CAACnD,IAAI,KAAK,qCAAqC,CACvD;IACDM,OAAO,CAAC,wBAAwB,EAAE6C,KAAK,CAACnD,IAAI,CAAC;EAC/C;;EAEA;AACF;AACA;AACA;EACE,SAASpC,6BAA6BA,CAACuF,KAAK,EAAE;IAC5C,MAAMpI,IAAI,GAAG,IAAI,CAACoG,cAAc,CAACgC,KAAK,CAAC;IACvC,MAAMnD,IAAI,GAAGO,OAAO,CAAC,wBAAwB,CAAC;IAC9C;IACA,IAAItH,KAAK;IAET,IAAI+G,IAAI,EAAE;MACR/G,KAAK,GAAGX,+BAA+B,CACrCyC,IAAI,EACJiF,IAAI,KAAKrH,KAAK,CAAC+E,+BAA+B,GAC1ChF,SAAS,CAACmL,kBAAkB,GAC5BnL,SAAS,CAACoM,sBAAsB,CACrC;MACDxE,OAAO,CAAC,wBAAwB,CAAC;IACnC,CAAC,MAAM;MACL,MAAMyE,MAAM,GAAGnM,6BAA6B,CAACmC,IAAI,CAAC;MAClD9C,MAAM,CAAC8M,MAAM,KAAK,KAAK,EAAE,8BAA8B,CAAC;MACxD9L,KAAK,GAAG8L,MAAM;IAChB;IAEA,MAAMnE,IAAI,GAAG,IAAI,CAACT,KAAK,CAACU,GAAG,EAAE;IAC7B5I,MAAM,CAAC2I,IAAI,EAAE,iBAAiB,CAAC;IAC/B3I,MAAM,CAAC2I,IAAI,CAACS,QAAQ,EAAE,0BAA0B,CAAC;IACjDpJ,MAAM,CAAC,OAAO,IAAI2I,IAAI,EAAE,uBAAuB,CAAC;IAChDA,IAAI,CAAC3H,KAAK,IAAIA,KAAK;IACnB2H,IAAI,CAACS,QAAQ,CAACM,GAAG,GAAGJ,KAAK,CAAC4B,KAAK,CAACxB,GAAG,CAAC;EACtC;;EAEA;AACF;AACA;AACA;EACE,SAAStE,sBAAsBA,CAAC8F,KAAK,EAAE;IACrC5F,UAAU,CAACyD,IAAI,CAAC,IAAI,EAAEmC,KAAK,CAAC;IAC5B,MAAMC,IAAI,GAAG,IAAI,CAACjD,KAAK,CAAC,IAAI,CAACA,KAAK,CAACO,MAAM,GAAG,CAAC,CAAC;IAC9CzI,MAAM,CAACmL,IAAI,EAAE,wBAAwB,CAAC;IACtCnL,MAAM,CAACmL,IAAI,CAACpD,IAAI,KAAK,MAAM,EAAE,wBAAwB,CAAC;IAEtDoD,IAAI,CAACgB,GAAG,GAAG,IAAI,CAACjD,cAAc,CAACgC,KAAK,CAAC;EACvC;;EAEA;AACF;AACA;AACA;EACE,SAAS/F,mBAAmBA,CAAC+F,KAAK,EAAE;IAClC5F,UAAU,CAACyD,IAAI,CAAC,IAAI,EAAEmC,KAAK,CAAC;IAC5B,MAAMC,IAAI,GAAG,IAAI,CAACjD,KAAK,CAAC,IAAI,CAACA,KAAK,CAACO,MAAM,GAAG,CAAC,CAAC;IAC9CzI,MAAM,CAACmL,IAAI,EAAE,wBAAwB,CAAC;IACtCnL,MAAM,CAACmL,IAAI,CAACpD,IAAI,KAAK,MAAM,EAAE,wBAAwB,CAAC;IAEtDoD,IAAI,CAACgB,GAAG,GAAG,SAAS,GAAG,IAAI,CAACjD,cAAc,CAACgC,KAAK,CAAC;EACnD;;EAEA;EACA;EACA;;EAEA;EACA,SAAS/I,UAAUA,CAAA,EAAG;IACpB,OAAO;MAAC4F,IAAI,EAAE,YAAY;MAAEC,QAAQ,EAAE;IAAE,CAAC;EAC3C;;EAEA;EACA,SAASzF,QAAQA,CAAA,EAAG;IAClB,OAAO;MAACwF,IAAI,EAAE,MAAM;MAAE8D,IAAI,EAAE,IAAI;MAAEC,IAAI,EAAE,IAAI;MAAE9K,KAAK,EAAE;IAAE,CAAC;EAC1D;;EAEA;EACA,SAAS4B,QAAQA,CAAA,EAAG;IAClB,OAAO;MAACmF,IAAI,EAAE,YAAY;MAAE/G,KAAK,EAAE;IAAE,CAAC;EACxC;;EAEA;EACA,SAASgC,UAAUA,CAAA,EAAG;IACpB,OAAO;MACL+E,IAAI,EAAE,YAAY;MAClBiE,UAAU,EAAE,EAAE;MACdlI,KAAK,EAAE,IAAI;MACXoI,KAAK,EAAE,IAAI;MACXC,GAAG,EAAE;IACP,CAAC;EACH;;EAEA;EACA,SAAS/I,QAAQA,CAAA,EAAG;IAClB,OAAO;MAAC2E,IAAI,EAAE,UAAU;MAAEC,QAAQ,EAAE;IAAE,CAAC;EACzC;;EAEA;EACA,SAAS9F,OAAOA,CAAA,EAAG;IACjB;IACA,OAAO;MAAC6F,IAAI,EAAE,SAAS;MAAEqE,KAAK,EAAEjL,SAAS;MAAE6G,QAAQ,EAAE;IAAE,CAAC;EAC1D;;EAEA;EACA,SAAS1E,SAASA,CAAA,EAAG;IACnB,OAAO;MAACyE,IAAI,EAAE;IAAO,CAAC;EACxB;;EAEA;EACA,SAAStE,IAAIA,CAAA,EAAG;IACd,OAAO;MAACsE,IAAI,EAAE,MAAM;MAAE/G,KAAK,EAAE;IAAE,CAAC;EAClC;;EAEA;EACA,SAAS6C,KAAKA,CAAA,EAAG;IACf,OAAO;MAACkE,IAAI,EAAE,OAAO;MAAEmE,KAAK,EAAE,IAAI;MAAEC,GAAG,EAAE,EAAE;MAAES,GAAG,EAAE;IAAI,CAAC;EACzD;;EAEA;EACA,SAAS/K,IAAIA,CAAA,EAAG;IACd,OAAO;MAACkG,IAAI,EAAE,MAAM;MAAEmE,KAAK,EAAE,IAAI;MAAEC,GAAG,EAAE,EAAE;MAAEnE,QAAQ,EAAE;IAAE,CAAC;EAC3D;;EAEA;AACF;AACA;AACA;EACE,SAAS7D,IAAIA,CAAC+G,KAAK,EAAE;IACnB,OAAO;MACLnD,IAAI,EAAE,MAAM;MACZgF,OAAO,EAAE7B,KAAK,CAACnD,IAAI,KAAK,aAAa;MACrCsB,KAAK,EAAE,IAAI;MACX2D,MAAM,EAAE9B,KAAK,CAACN,OAAO;MACrB5C,QAAQ,EAAE;IACZ,CAAC;EACH;;EAEA;AACF;AACA;AACA;EACE,SAASjE,QAAQA,CAACmH,KAAK,EAAE;IACvB,OAAO;MACLnD,IAAI,EAAE,UAAU;MAChBiF,MAAM,EAAE9B,KAAK,CAACN,OAAO;MACrBqC,OAAO,EAAE,IAAI;MACbjF,QAAQ,EAAE;IACZ,CAAC;EACH;;EAEA;EACA,SAAS1D,SAASA,CAAA,EAAG;IACnB,OAAO;MAACyD,IAAI,EAAE,WAAW;MAAEC,QAAQ,EAAE;IAAE,CAAC;EAC1C;;EAEA;EACA,SAASnD,MAAMA,CAAA,EAAG;IAChB,OAAO;MAACkD,IAAI,EAAE,QAAQ;MAAEC,QAAQ,EAAE;IAAE,CAAC;EACvC;;EAEA;EACA,SAASuE,IAAIA,CAAA,EAAG;IACd,OAAO;MAACxE,IAAI,EAAE,MAAM;MAAE/G,KAAK,EAAE;IAAE,CAAC;EAClC;;EAEA;EACA,SAAS8D,aAAaA,CAAA,EAAG;IACvB,OAAO;MAACiD,IAAI,EAAE;IAAe,CAAC;EAChC;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASuB,KAAKA,CAAC4D,CAAC,EAAE;EAChB,OAAO;IAAC3D,IAAI,EAAE2D,CAAC,CAAC3D,IAAI;IAAEC,MAAM,EAAE0D,CAAC,CAAC1D,MAAM;IAAEC,MAAM,EAAEyD,CAAC,CAACzD;EAAM,CAAC;AAC3D;;AAEA;AACA;AACA;AACA;AACA;AACA,SAAS/B,SAASA,CAACyF,QAAQ,EAAEC,UAAU,EAAE;EACvC,IAAI5E,KAAK,GAAG,CAAC,CAAC;EAEd,OAAO,EAAEA,KAAK,GAAG4E,UAAU,CAAC3E,MAAM,EAAE;IAClC,MAAMzH,KAAK,GAAGoM,UAAU,CAAC5E,KAAK,CAAC;IAE/B,IAAI6E,KAAK,CAACC,OAAO,CAACtM,KAAK,CAAC,EAAE;MACxB0G,SAAS,CAACyF,QAAQ,EAAEnM,KAAK,CAAC;IAC5B,CAAC,MAAM;MACLuM,SAAS,CAACJ,QAAQ,EAAEnM,KAAK,CAAC;IAC5B;EACF;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASuM,SAASA,CAACJ,QAAQ,EAAEI,SAAS,EAAE;EACtC;EACA,IAAIzC,GAAG;EAEP,KAAKA,GAAG,IAAIyC,SAAS,EAAE;IACrB,IAAI1M,GAAG,CAACkI,IAAI,CAACwE,SAAS,EAAEzC,GAAG,CAAC,EAAE;MAC5B,IAAIA,GAAG,KAAK,gBAAgB,EAAE;QAC5B,MAAM0C,KAAK,GAAGD,SAAS,CAACzC,GAAG,CAAC;QAC5B,IAAI0C,KAAK,EAAE;UACTL,QAAQ,CAACrC,GAAG,CAAC,CAACpC,IAAI,CAAC,GAAG8E,KAAK,CAAC;QAC9B;MACF,CAAC,MAAM,IAAI1C,GAAG,KAAK,YAAY,EAAE;QAC/B,MAAM0C,KAAK,GAAGD,SAAS,CAACzC,GAAG,CAAC;QAC5B,IAAI0C,KAAK,EAAE;UACTL,QAAQ,CAACrC,GAAG,CAAC,CAACpC,IAAI,CAAC,GAAG8E,KAAK,CAAC;QAC9B;MACF,CAAC,MAAM,IAAI1C,GAAG,KAAK,OAAO,IAAIA,GAAG,KAAK,MAAM,EAAE;QAC5C,MAAM0C,KAAK,GAAGD,SAAS,CAACzC,GAAG,CAAC;QAC5B,IAAI0C,KAAK,EAAE;UACTxE,MAAM,CAACC,MAAM,CAACkE,QAAQ,CAACrC,GAAG,CAAC,EAAE0C,KAAK,CAAC;QACrC;MACF;IACF;EACF;AACF;;AAEA;AACA,SAASrE,cAAcA,CAACsE,IAAI,EAAED,KAAK,EAAE;EACnC,IAAIC,IAAI,EAAE;IACR,MAAM,IAAIjC,KAAK,CACb,gBAAgB,GACdiC,IAAI,CAAC1F,IAAI,GACT,KAAK,GACLnH,iBAAiB,CAAC;MAACyI,KAAK,EAAEoE,IAAI,CAACpE,KAAK;MAAEK,GAAG,EAAE+D,IAAI,CAAC/D;IAAG,CAAC,CAAC,GACrD,yBAAyB,GACzB8D,KAAK,CAACzF,IAAI,GACV,KAAK,GACLnH,iBAAiB,CAAC;MAACyI,KAAK,EAAEmE,KAAK,CAACnE,KAAK;MAAEK,GAAG,EAAE8D,KAAK,CAAC9D;IAAG,CAAC,CAAC,GACvD,WAAW,CACd;EACH,CAAC,MAAM;IACL,MAAM,IAAI8B,KAAK,CACb,mCAAmC,GACjCgC,KAAK,CAACzF,IAAI,GACV,KAAK,GACLnH,iBAAiB,CAAC;MAACyI,KAAK,EAAEmE,KAAK,CAACnE,KAAK;MAAEK,GAAG,EAAE8D,KAAK,CAAC9D;IAAG,CAAC,CAAC,GACvD,iBAAiB,CACpB;EACH;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}