{"ast": null, "code": "var global = require('../internals/global');\nvar isObject = require('../internals/is-object');\nvar document = global.document;\n// typeof document.createElement is 'object' in old IE\nvar EXISTS = isObject(document) && isObject(document.createElement);\nmodule.exports = function (it) {\n  return EXISTS ? document.createElement(it) : {};\n};", "map": {"version": 3, "names": ["global", "require", "isObject", "document", "EXISTS", "createElement", "module", "exports", "it"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/core-js-pure/internals/document-create-element.js"], "sourcesContent": ["var global = require('../internals/global');\nvar isObject = require('../internals/is-object');\n\nvar document = global.document;\n// typeof document.createElement is 'object' in old IE\nvar EXISTS = isObject(document) && isObject(document.createElement);\n\nmodule.exports = function (it) {\n  return EXISTS ? document.createElement(it) : {};\n};\n"], "mappings": "AAAA,IAAIA,MAAM,GAAGC,OAAO,CAAC,qBAAqB,CAAC;AAC3C,IAAIC,QAAQ,GAAGD,OAAO,CAAC,wBAAwB,CAAC;AAEhD,IAAIE,QAAQ,GAAGH,MAAM,CAACG,QAAQ;AAC9B;AACA,IAAIC,MAAM,GAAGF,QAAQ,CAACC,QAAQ,CAAC,IAAID,QAAQ,CAACC,QAAQ,CAACE,aAAa,CAAC;AAEnEC,MAAM,CAACC,OAAO,GAAG,UAAUC,EAAE,EAAE;EAC7B,OAAOJ,MAAM,GAAGD,QAAQ,CAACE,aAAa,CAACG,EAAE,CAAC,GAAG,CAAC,CAAC;AACjD,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}