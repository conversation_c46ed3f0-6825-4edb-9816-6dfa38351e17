{"ast": null, "code": "export default {\n  init: true,\n  direction: 'horizontal',\n  oneWayMovement: false,\n  touchEventsTarget: 'wrapper',\n  initialSlide: 0,\n  speed: 300,\n  cssMode: false,\n  updateOnWindowResize: true,\n  resizeObserver: true,\n  nested: false,\n  createElements: false,\n  enabled: true,\n  focusableElements: 'input, select, option, textarea, button, video, label',\n  // Overrides\n  width: null,\n  height: null,\n  //\n  preventInteractionOnTransition: false,\n  // ssr\n  userAgent: null,\n  url: null,\n  // To support iOS's swipe-to-go-back gesture (when being used in-app).\n  edgeSwipeDetection: false,\n  edgeSwipeThreshold: 20,\n  // Autoheight\n  autoHeight: false,\n  // Set wrapper width\n  setWrapperSize: false,\n  // Virtual Translate\n  virtualTranslate: false,\n  // Effects\n  effect: 'slide',\n  // 'slide' or 'fade' or 'cube' or 'coverflow' or 'flip'\n\n  // Breakpoints\n  breakpoints: undefined,\n  breakpointsBase: 'window',\n  // Slides grid\n  spaceBetween: 0,\n  slidesPerView: 1,\n  slidesPerGroup: 1,\n  slidesPerGroupSkip: 0,\n  slidesPerGroupAuto: false,\n  centeredSlides: false,\n  centeredSlidesBounds: false,\n  slidesOffsetBefore: 0,\n  // in px\n  slidesOffsetAfter: 0,\n  // in px\n  normalizeSlideIndex: true,\n  centerInsufficientSlides: false,\n  // Disable swiper and hide navigation when container not overflow\n  watchOverflow: true,\n  // Round length\n  roundLengths: false,\n  // Touches\n  touchRatio: 1,\n  touchAngle: 45,\n  simulateTouch: true,\n  shortSwipes: true,\n  longSwipes: true,\n  longSwipesRatio: 0.5,\n  longSwipesMs: 300,\n  followFinger: true,\n  allowTouchMove: true,\n  threshold: 5,\n  touchMoveStopPropagation: false,\n  touchStartPreventDefault: true,\n  touchStartForcePreventDefault: false,\n  touchReleaseOnEdges: false,\n  // Unique Navigation Elements\n  uniqueNavElements: true,\n  // Resistance\n  resistance: true,\n  resistanceRatio: 0.85,\n  // Progress\n  watchSlidesProgress: false,\n  // Cursor\n  grabCursor: false,\n  // Clicks\n  preventClicks: true,\n  preventClicksPropagation: true,\n  slideToClickedSlide: false,\n  // loop\n  loop: false,\n  loopedSlides: null,\n  loopPreventsSliding: true,\n  // rewind\n  rewind: false,\n  // Swiping/no swiping\n  allowSlidePrev: true,\n  allowSlideNext: true,\n  swipeHandler: null,\n  // '.swipe-handler',\n  noSwiping: true,\n  noSwipingClass: 'swiper-no-swiping',\n  noSwipingSelector: null,\n  // Passive Listeners\n  passiveListeners: true,\n  maxBackfaceHiddenSlides: 10,\n  // NS\n  containerModifierClass: 'swiper-',\n  // NEW\n  slideClass: 'swiper-slide',\n  slideActiveClass: 'swiper-slide-active',\n  slideVisibleClass: 'swiper-slide-visible',\n  slideNextClass: 'swiper-slide-next',\n  slidePrevClass: 'swiper-slide-prev',\n  wrapperClass: 'swiper-wrapper',\n  lazyPreloaderClass: 'swiper-lazy-preloader',\n  lazyPreloadPrevNext: 0,\n  // Callbacks\n  runCallbacksOnInit: true,\n  // Internals\n  _emitClasses: false\n};", "map": {"version": 3, "names": ["init", "direction", "oneWayMovement", "touchEventsTarget", "initialSlide", "speed", "cssMode", "updateOnWindowResize", "resizeObserver", "nested", "createElements", "enabled", "focusableElements", "width", "height", "preventInteractionOnTransition", "userAgent", "url", "edgeSwipeDetection", "edgeSwipeThreshold", "autoHeight", "setWrapperSize", "virtualTranslate", "effect", "breakpoints", "undefined", "breakpointsBase", "spaceBetween", "<PERSON><PERSON><PERSON><PERSON>iew", "slidesPerGroup", "slidesPerGroupSkip", "slidesPerGroupAuto", "centeredSlides", "centeredSlidesBounds", "slidesOffsetBefore", "slidesOffsetAfter", "normalizeSlideIndex", "centerInsufficientSlides", "watchOverflow", "roundLengths", "touchRatio", "touchAngle", "simulate<PERSON>ouch", "shortSwipes", "longSwipes", "longSwipesRatio", "longSwipesMs", "follow<PERSON><PERSON>", "allowTouchMove", "threshold", "touchMoveStopPropagation", "touchStartPreventDefault", "touchStartForcePreventDefault", "touchReleaseOnEdges", "uniqueNavElements", "resistance", "resistanceRatio", "watchSlidesProgress", "grabCursor", "preventClicks", "preventClicksPropagation", "slideToClickedSlide", "loop", "loopedSlides", "loopPreventsSliding", "rewind", "allowSlidePrev", "allowSlideNext", "swi<PERSON><PERSON><PERSON><PERSON>", "noSwiping", "noSwipingClass", "noSwipingSelector", "passiveListeners", "maxBackfaceHiddenSlides", "containerModifierClass", "slideClass", "slideActiveClass", "slideVisibleClass", "slideNextClass", "slidePrevClass", "wrapperClass", "lazyPreloaderClass", "lazyPreloadPrevNext", "runCallbacksOnInit", "_emitClasses"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/core/defaults.js"], "sourcesContent": ["export default {\n  init: true,\n  direction: 'horizontal',\n  oneWayMovement: false,\n  touchEventsTarget: 'wrapper',\n  initialSlide: 0,\n  speed: 300,\n  cssMode: false,\n  updateOnWindowResize: true,\n  resizeObserver: true,\n  nested: false,\n  createElements: false,\n  enabled: true,\n  focusableElements: 'input, select, option, textarea, button, video, label',\n  // Overrides\n  width: null,\n  height: null,\n  //\n  preventInteractionOnTransition: false,\n  // ssr\n  userAgent: null,\n  url: null,\n  // To support iOS's swipe-to-go-back gesture (when being used in-app).\n  edgeSwipeDetection: false,\n  edgeSwipeThreshold: 20,\n  // Autoheight\n  autoHeight: false,\n  // Set wrapper width\n  setWrapperSize: false,\n  // Virtual Translate\n  virtualTranslate: false,\n  // Effects\n  effect: 'slide',\n  // 'slide' or 'fade' or 'cube' or 'coverflow' or 'flip'\n\n  // Breakpoints\n  breakpoints: undefined,\n  breakpointsBase: 'window',\n  // Slides grid\n  spaceBetween: 0,\n  slidesPerView: 1,\n  slidesPerGroup: 1,\n  slidesPerGroupSkip: 0,\n  slidesPerGroupAuto: false,\n  centeredSlides: false,\n  centeredSlidesBounds: false,\n  slidesOffsetBefore: 0,\n  // in px\n  slidesOffsetAfter: 0,\n  // in px\n  normalizeSlideIndex: true,\n  centerInsufficientSlides: false,\n  // Disable swiper and hide navigation when container not overflow\n  watchOverflow: true,\n  // Round length\n  roundLengths: false,\n  // Touches\n  touchRatio: 1,\n  touchAngle: 45,\n  simulateTouch: true,\n  shortSwipes: true,\n  longSwipes: true,\n  longSwipesRatio: 0.5,\n  longSwipesMs: 300,\n  followFinger: true,\n  allowTouchMove: true,\n  threshold: 5,\n  touchMoveStopPropagation: false,\n  touchStartPreventDefault: true,\n  touchStartForcePreventDefault: false,\n  touchReleaseOnEdges: false,\n  // Unique Navigation Elements\n  uniqueNavElements: true,\n  // Resistance\n  resistance: true,\n  resistanceRatio: 0.85,\n  // Progress\n  watchSlidesProgress: false,\n  // Cursor\n  grabCursor: false,\n  // Clicks\n  preventClicks: true,\n  preventClicksPropagation: true,\n  slideToClickedSlide: false,\n  // loop\n  loop: false,\n  loopedSlides: null,\n  loopPreventsSliding: true,\n  // rewind\n  rewind: false,\n  // Swiping/no swiping\n  allowSlidePrev: true,\n  allowSlideNext: true,\n  swipeHandler: null,\n  // '.swipe-handler',\n  noSwiping: true,\n  noSwipingClass: 'swiper-no-swiping',\n  noSwipingSelector: null,\n  // Passive Listeners\n  passiveListeners: true,\n  maxBackfaceHiddenSlides: 10,\n  // NS\n  containerModifierClass: 'swiper-',\n  // NEW\n  slideClass: 'swiper-slide',\n  slideActiveClass: 'swiper-slide-active',\n  slideVisibleClass: 'swiper-slide-visible',\n  slideNextClass: 'swiper-slide-next',\n  slidePrevClass: 'swiper-slide-prev',\n  wrapperClass: 'swiper-wrapper',\n  lazyPreloaderClass: 'swiper-lazy-preloader',\n  lazyPreloadPrevNext: 0,\n  // Callbacks\n  runCallbacksOnInit: true,\n  // Internals\n  _emitClasses: false\n};"], "mappings": "AAAA,eAAe;EACbA,IAAI,EAAE,IAAI;EACVC,SAAS,EAAE,YAAY;EACvBC,cAAc,EAAE,KAAK;EACrBC,iBAAiB,EAAE,SAAS;EAC5BC,YAAY,EAAE,CAAC;EACfC,KAAK,EAAE,GAAG;EACVC,OAAO,EAAE,KAAK;EACdC,oBAAoB,EAAE,IAAI;EAC1BC,cAAc,EAAE,IAAI;EACpBC,MAAM,EAAE,KAAK;EACbC,cAAc,EAAE,KAAK;EACrBC,OAAO,EAAE,IAAI;EACbC,iBAAiB,EAAE,uDAAuD;EAC1E;EACAC,KAAK,EAAE,IAAI;EACXC,MAAM,EAAE,IAAI;EACZ;EACAC,8BAA8B,EAAE,KAAK;EACrC;EACAC,SAAS,EAAE,IAAI;EACfC,GAAG,EAAE,IAAI;EACT;EACAC,kBAAkB,EAAE,KAAK;EACzBC,kBAAkB,EAAE,EAAE;EACtB;EACAC,UAAU,EAAE,KAAK;EACjB;EACAC,cAAc,EAAE,KAAK;EACrB;EACAC,gBAAgB,EAAE,KAAK;EACvB;EACAC,MAAM,EAAE,OAAO;EACf;;EAEA;EACAC,WAAW,EAAEC,SAAS;EACtBC,eAAe,EAAE,QAAQ;EACzB;EACAC,YAAY,EAAE,CAAC;EACfC,aAAa,EAAE,CAAC;EAChBC,cAAc,EAAE,CAAC;EACjBC,kBAAkB,EAAE,CAAC;EACrBC,kBAAkB,EAAE,KAAK;EACzBC,cAAc,EAAE,KAAK;EACrBC,oBAAoB,EAAE,KAAK;EAC3BC,kBAAkB,EAAE,CAAC;EACrB;EACAC,iBAAiB,EAAE,CAAC;EACpB;EACAC,mBAAmB,EAAE,IAAI;EACzBC,wBAAwB,EAAE,KAAK;EAC/B;EACAC,aAAa,EAAE,IAAI;EACnB;EACAC,YAAY,EAAE,KAAK;EACnB;EACAC,UAAU,EAAE,CAAC;EACbC,UAAU,EAAE,EAAE;EACdC,aAAa,EAAE,IAAI;EACnBC,WAAW,EAAE,IAAI;EACjBC,UAAU,EAAE,IAAI;EAChBC,eAAe,EAAE,GAAG;EACpBC,YAAY,EAAE,GAAG;EACjBC,YAAY,EAAE,IAAI;EAClBC,cAAc,EAAE,IAAI;EACpBC,SAAS,EAAE,CAAC;EACZC,wBAAwB,EAAE,KAAK;EAC/BC,wBAAwB,EAAE,IAAI;EAC9BC,6BAA6B,EAAE,KAAK;EACpCC,mBAAmB,EAAE,KAAK;EAC1B;EACAC,iBAAiB,EAAE,IAAI;EACvB;EACAC,UAAU,EAAE,IAAI;EAChBC,eAAe,EAAE,IAAI;EACrB;EACAC,mBAAmB,EAAE,KAAK;EAC1B;EACAC,UAAU,EAAE,KAAK;EACjB;EACAC,aAAa,EAAE,IAAI;EACnBC,wBAAwB,EAAE,IAAI;EAC9BC,mBAAmB,EAAE,KAAK;EAC1B;EACAC,IAAI,EAAE,KAAK;EACXC,YAAY,EAAE,IAAI;EAClBC,mBAAmB,EAAE,IAAI;EACzB;EACAC,MAAM,EAAE,KAAK;EACb;EACAC,cAAc,EAAE,IAAI;EACpBC,cAAc,EAAE,IAAI;EACpBC,YAAY,EAAE,IAAI;EAClB;EACAC,SAAS,EAAE,IAAI;EACfC,cAAc,EAAE,mBAAmB;EACnCC,iBAAiB,EAAE,IAAI;EACvB;EACAC,gBAAgB,EAAE,IAAI;EACtBC,uBAAuB,EAAE,EAAE;EAC3B;EACAC,sBAAsB,EAAE,SAAS;EACjC;EACAC,UAAU,EAAE,cAAc;EAC1BC,gBAAgB,EAAE,qBAAqB;EACvCC,iBAAiB,EAAE,sBAAsB;EACzCC,cAAc,EAAE,mBAAmB;EACnCC,cAAc,EAAE,mBAAmB;EACnCC,YAAY,EAAE,gBAAgB;EAC9BC,kBAAkB,EAAE,uBAAuB;EAC3CC,mBAAmB,EAAE,CAAC;EACtB;EACAC,kBAAkB,EAAE,IAAI;EACxB;EACAC,YAAY,EAAE;AAChB,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}