{"name": "@types/request", "version": "2.48.8", "description": "TypeScript definitions for request", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/request", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/soywiz", "githubUsername": "soywiz"}, {"name": "bonnici", "url": "https://github.com/bonnici", "githubUsername": "bonnici"}, {"name": "<PERSON>", "url": "https://github.com/Bartvds", "githubUsername": "Bartvds"}, {"name": "<PERSON>", "url": "https://github.com/joeskeen", "githubUsername": "j<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/ccurrens", "githubUsername": "ccurrens"}, {"name": "<PERSON>", "url": "https://github.com/lookfirst", "githubUsername": "lookfirst"}, {"name": "<PERSON>", "url": "https://github.com/mastermatt", "githubUsername": "mastermatt"}, {"name": "<PERSON>", "url": "https://github.com/josecolella", "githubUsername": "j<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/murbanowicz", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/request"}, "scripts": {}, "dependencies": {"@types/caseless": "*", "@types/node": "*", "@types/tough-cookie": "*", "form-data": "^2.5.0"}, "typesPublisherContentHash": "1d1a7d3a65e28ea707b27eb4b0425fb4bdc214cf0c834f393b6b4faa6ee714d1", "typeScriptVersion": "3.8"}