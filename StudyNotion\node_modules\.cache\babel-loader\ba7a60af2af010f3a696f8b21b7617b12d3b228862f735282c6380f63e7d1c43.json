{"ast": null, "code": "export default function updateClickedSlide(e) {\n  const swiper = this;\n  const params = swiper.params;\n  const slide = e.closest(`.${params.slideClass}, swiper-slide`);\n  let slideFound = false;\n  let slideIndex;\n  if (slide) {\n    for (let i = 0; i < swiper.slides.length; i += 1) {\n      if (swiper.slides[i] === slide) {\n        slideFound = true;\n        slideIndex = i;\n        break;\n      }\n    }\n  }\n  if (slide && slideFound) {\n    swiper.clickedSlide = slide;\n    if (swiper.virtual && swiper.params.virtual.enabled) {\n      swiper.clickedIndex = parseInt(slide.getAttribute('data-swiper-slide-index'), 10);\n    } else {\n      swiper.clickedIndex = slideIndex;\n    }\n  } else {\n    swiper.clickedSlide = undefined;\n    swiper.clickedIndex = undefined;\n    return;\n  }\n  if (params.slideToClickedSlide && swiper.clickedIndex !== undefined && swiper.clickedIndex !== swiper.activeIndex) {\n    swiper.slideToClickedSlide();\n  }\n}", "map": {"version": 3, "names": ["updateClickedSlide", "e", "swiper", "params", "slide", "closest", "slideClass", "slideFound", "slideIndex", "i", "slides", "length", "clickedSlide", "virtual", "enabled", "clickedIndex", "parseInt", "getAttribute", "undefined", "slideToClickedSlide", "activeIndex"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/core/update/updateClickedSlide.js"], "sourcesContent": ["export default function updateClickedSlide(e) {\n  const swiper = this;\n  const params = swiper.params;\n  const slide = e.closest(`.${params.slideClass}, swiper-slide`);\n  let slideFound = false;\n  let slideIndex;\n  if (slide) {\n    for (let i = 0; i < swiper.slides.length; i += 1) {\n      if (swiper.slides[i] === slide) {\n        slideFound = true;\n        slideIndex = i;\n        break;\n      }\n    }\n  }\n  if (slide && slideFound) {\n    swiper.clickedSlide = slide;\n    if (swiper.virtual && swiper.params.virtual.enabled) {\n      swiper.clickedIndex = parseInt(slide.getAttribute('data-swiper-slide-index'), 10);\n    } else {\n      swiper.clickedIndex = slideIndex;\n    }\n  } else {\n    swiper.clickedSlide = undefined;\n    swiper.clickedIndex = undefined;\n    return;\n  }\n  if (params.slideToClickedSlide && swiper.clickedIndex !== undefined && swiper.clickedIndex !== swiper.activeIndex) {\n    swiper.slideToClickedSlide();\n  }\n}"], "mappings": "AAAA,eAAe,SAASA,kBAAkBA,CAACC,CAAC,EAAE;EAC5C,MAAMC,MAAM,GAAG,IAAI;EACnB,MAAMC,MAAM,GAAGD,MAAM,CAACC,MAAM;EAC5B,MAAMC,KAAK,GAAGH,CAAC,CAACI,OAAO,CAAE,IAAGF,MAAM,CAACG,UAAW,gBAAe,CAAC;EAC9D,IAAIC,UAAU,GAAG,KAAK;EACtB,IAAIC,UAAU;EACd,IAAIJ,KAAK,EAAE;IACT,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,MAAM,CAACQ,MAAM,CAACC,MAAM,EAAEF,CAAC,IAAI,CAAC,EAAE;MAChD,IAAIP,MAAM,CAACQ,MAAM,CAACD,CAAC,CAAC,KAAKL,KAAK,EAAE;QAC9BG,UAAU,GAAG,IAAI;QACjBC,UAAU,GAAGC,CAAC;QACd;MACF;IACF;EACF;EACA,IAAIL,KAAK,IAAIG,UAAU,EAAE;IACvBL,MAAM,CAACU,YAAY,GAAGR,KAAK;IAC3B,IAAIF,MAAM,CAACW,OAAO,IAAIX,MAAM,CAACC,MAAM,CAACU,OAAO,CAACC,OAAO,EAAE;MACnDZ,MAAM,CAACa,YAAY,GAAGC,QAAQ,CAACZ,KAAK,CAACa,YAAY,CAAC,yBAAyB,CAAC,EAAE,EAAE,CAAC;IACnF,CAAC,MAAM;MACLf,MAAM,CAACa,YAAY,GAAGP,UAAU;IAClC;EACF,CAAC,MAAM;IACLN,MAAM,CAACU,YAAY,GAAGM,SAAS;IAC/BhB,MAAM,CAACa,YAAY,GAAGG,SAAS;IAC/B;EACF;EACA,IAAIf,MAAM,CAACgB,mBAAmB,IAAIjB,MAAM,CAACa,YAAY,KAAKG,SAAS,IAAIhB,MAAM,CAACa,YAAY,KAAKb,MAAM,CAACkB,WAAW,EAAE;IACjHlB,MAAM,CAACiB,mBAAmB,EAAE;EAC9B;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}