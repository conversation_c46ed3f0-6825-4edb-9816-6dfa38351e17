{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar _react = _interopRequireDefault(require(\"react\"));\nvar _tableContext = require(\"../utils/tableContext\");\nvar _TdInner = _interopRequireDefault(require(\"./TdInner\"));\nvar Td = function Td(props) {\n  return /*#__PURE__*/_react[\"default\"].createElement(_tableContext.Consumer, null, function (headers) {\n    return /*#__PURE__*/_react[\"default\"].createElement(_TdInner[\"default\"], (0, _extends2[\"default\"])({}, props, {\n      headers: headers\n    }));\n  });\n};\nvar _default = Td;\nexports[\"default\"] = _default;", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "_extends2", "_react", "_tableContext", "_TdInner", "Td", "props", "createElement", "Consumer", "headers", "_default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/react-super-responsive-table/dist/components/Td.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\n\nvar _react = _interopRequireDefault(require(\"react\"));\n\nvar _tableContext = require(\"../utils/tableContext\");\n\nvar _TdInner = _interopRequireDefault(require(\"./TdInner\"));\n\nvar Td = function Td(props) {\n  return /*#__PURE__*/_react[\"default\"].createElement(_tableContext.Consumer, null, function (headers) {\n    return /*#__PURE__*/_react[\"default\"].createElement(_TdInner[\"default\"], (0, _extends2[\"default\"])({}, props, {\n      headers: headers\n    }));\n  });\n};\n\nvar _default = Td;\nexports[\"default\"] = _default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC;AAEpFC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;AAE3B,IAAIE,SAAS,GAAGN,sBAAsB,CAACC,OAAO,CAAC,gCAAgC,CAAC,CAAC;AAEjF,IAAIM,MAAM,GAAGP,sBAAsB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AAErD,IAAIO,aAAa,GAAGP,OAAO,CAAC,uBAAuB,CAAC;AAEpD,IAAIQ,QAAQ,GAAGT,sBAAsB,CAACC,OAAO,CAAC,WAAW,CAAC,CAAC;AAE3D,IAAIS,EAAE,GAAG,SAASA,EAAEA,CAACC,KAAK,EAAE;EAC1B,OAAO,aAAaJ,MAAM,CAAC,SAAS,CAAC,CAACK,aAAa,CAACJ,aAAa,CAACK,QAAQ,EAAE,IAAI,EAAE,UAAUC,OAAO,EAAE;IACnG,OAAO,aAAaP,MAAM,CAAC,SAAS,CAAC,CAACK,aAAa,CAACH,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAEH,SAAS,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAEK,KAAK,EAAE;MAC5GG,OAAO,EAAEA;IACX,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;AACJ,CAAC;AAED,IAAIC,QAAQ,GAAGL,EAAE;AACjBN,OAAO,CAAC,SAAS,CAAC,GAAGW,QAAQ"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}