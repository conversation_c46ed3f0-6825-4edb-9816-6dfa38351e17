{"ast": null, "code": "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').LinkReference} LinkReference\n * @typedef {import('../state.js').State} State\n */\n\nimport { normalizeUri } from 'micromark-util-sanitize-uri';\nimport { revert } from '../revert.js';\n\n/**\n * Turn an mdast `linkReference` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {LinkReference} node\n *   mdast node.\n * @returns {ElementContent | Array<ElementContent>}\n *   hast node.\n */\nexport function linkReference(state, node) {\n  const def = state.definition(node.identifier);\n  if (!def) {\n    return revert(state, node);\n  }\n\n  /** @type {Properties} */\n  const properties = {\n    href: normalizeUri(def.url || '')\n  };\n  if (def.title !== null && def.title !== undefined) {\n    properties.title = def.title;\n  }\n\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'a',\n    properties,\n    children: state.all(node)\n  };\n  state.patch(node, result);\n  return state.applyData(node, result);\n}", "map": {"version": 3, "names": ["normalizeUri", "revert", "linkReference", "state", "node", "def", "definition", "identifier", "properties", "href", "url", "title", "undefined", "result", "type", "tagName", "children", "all", "patch", "applyData"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/mdast-util-to-hast/lib/handlers/link-reference.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').LinkReference} LinkReference\n * @typedef {import('../state.js').State} State\n */\n\nimport {normalizeUri} from 'micromark-util-sanitize-uri'\nimport {revert} from '../revert.js'\n\n/**\n * Turn an mdast `linkReference` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {LinkReference} node\n *   mdast node.\n * @returns {ElementContent | Array<ElementContent>}\n *   hast node.\n */\nexport function linkReference(state, node) {\n  const def = state.definition(node.identifier)\n\n  if (!def) {\n    return revert(state, node)\n  }\n\n  /** @type {Properties} */\n  const properties = {href: normalizeUri(def.url || '')}\n\n  if (def.title !== null && def.title !== undefined) {\n    properties.title = def.title\n  }\n\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'a',\n    properties,\n    children: state.all(node)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,YAAY,QAAO,6BAA6B;AACxD,SAAQC,MAAM,QAAO,cAAc;;AAEnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,aAAaA,CAACC,KAAK,EAAEC,IAAI,EAAE;EACzC,MAAMC,GAAG,GAAGF,KAAK,CAACG,UAAU,CAACF,IAAI,CAACG,UAAU,CAAC;EAE7C,IAAI,CAACF,GAAG,EAAE;IACR,OAAOJ,MAAM,CAACE,KAAK,EAAEC,IAAI,CAAC;EAC5B;;EAEA;EACA,MAAMI,UAAU,GAAG;IAACC,IAAI,EAAET,YAAY,CAACK,GAAG,CAACK,GAAG,IAAI,EAAE;EAAC,CAAC;EAEtD,IAAIL,GAAG,CAACM,KAAK,KAAK,IAAI,IAAIN,GAAG,CAACM,KAAK,KAAKC,SAAS,EAAE;IACjDJ,UAAU,CAACG,KAAK,GAAGN,GAAG,CAACM,KAAK;EAC9B;;EAEA;EACA,MAAME,MAAM,GAAG;IACbC,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,GAAG;IACZP,UAAU;IACVQ,QAAQ,EAAEb,KAAK,CAACc,GAAG,CAACb,IAAI;EAC1B,CAAC;EACDD,KAAK,CAACe,KAAK,CAACd,IAAI,EAAES,MAAM,CAAC;EACzB,OAAOV,KAAK,CAACgB,SAAS,CAACf,IAAI,EAAES,MAAM,CAAC;AACtC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}