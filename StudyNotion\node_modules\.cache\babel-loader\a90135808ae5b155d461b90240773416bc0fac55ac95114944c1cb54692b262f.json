{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = PopupButton;\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar _objectSpread2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectSpread\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _react = _interopRequireDefault(require(\"react\"));\nvar _classnames = _interopRequireDefault(require(\"classnames\"));\nvar _ClickableComponent = _interopRequireDefault(require(\"../ClickableComponent\"));\nvar _Popup = _interopRequireDefault(require(\"./Popup\"));\nvar propTypes = {\n  inline: _propTypes[\"default\"].bool,\n  onClick: _propTypes[\"default\"].func.isRequired,\n  onFocus: _propTypes[\"default\"].func,\n  onBlur: _propTypes[\"default\"].func,\n  className: _propTypes[\"default\"].string\n};\nvar defaultProps = {\n  inline: true\n};\nfunction PopupButton(props) {\n  var inline = props.inline,\n    className = props.className;\n  var ps = (0, _objectSpread2[\"default\"])({}, props);\n  delete ps.children;\n  delete ps.inline;\n  delete ps.className;\n  return _react[\"default\"].createElement(_ClickableComponent[\"default\"], (0, _extends2[\"default\"])({\n    className: (0, _classnames[\"default\"])(className, {\n      'video-react-menu-button-inline': !!inline,\n      'video-react-menu-button-popup': !inline\n    }, 'video-react-control video-react-button video-react-menu-button')\n  }, ps), _react[\"default\"].createElement(_Popup[\"default\"], props));\n}\nPopupButton.propTypes = propTypes;\nPopupButton.defaultProps = defaultProps;\nPopupButton.displayName = 'PopupButton';", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "Popup<PERSON><PERSON>on", "_extends2", "_objectSpread2", "_propTypes", "_react", "_classnames", "_ClickableComponent", "_Popup", "propTypes", "inline", "bool", "onClick", "func", "isRequired", "onFocus", "onBlur", "className", "string", "defaultProps", "props", "ps", "children", "createElement", "displayName"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/video-react/lib/components/popup/PopupButton.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = PopupButton;\n\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\n\nvar _objectSpread2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectSpread\"));\n\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\n\nvar _react = _interopRequireDefault(require(\"react\"));\n\nvar _classnames = _interopRequireDefault(require(\"classnames\"));\n\nvar _ClickableComponent = _interopRequireDefault(require(\"../ClickableComponent\"));\n\nvar _Popup = _interopRequireDefault(require(\"./Popup\"));\n\nvar propTypes = {\n  inline: _propTypes[\"default\"].bool,\n  onClick: _propTypes[\"default\"].func.isRequired,\n  onFocus: _propTypes[\"default\"].func,\n  onBlur: _propTypes[\"default\"].func,\n  className: _propTypes[\"default\"].string\n};\nvar defaultProps = {\n  inline: true\n};\n\nfunction PopupButton(props) {\n  var inline = props.inline,\n      className = props.className;\n  var ps = (0, _objectSpread2[\"default\"])({}, props);\n  delete ps.children;\n  delete ps.inline;\n  delete ps.className;\n  return _react[\"default\"].createElement(_ClickableComponent[\"default\"], (0, _extends2[\"default\"])({\n    className: (0, _classnames[\"default\"])(className, {\n      'video-react-menu-button-inline': !!inline,\n      'video-react-menu-button-popup': !inline\n    }, 'video-react-control video-react-button video-react-menu-button')\n  }, ps), _react[\"default\"].createElement(_Popup[\"default\"], props));\n}\n\nPopupButton.propTypes = propTypes;\nPopupButton.defaultProps = defaultProps;\nPopupButton.displayName = 'PopupButton';"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC;AAEpFC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAAC,SAAS,CAAC,GAAGE,WAAW;AAEhC,IAAIC,SAAS,GAAGP,sBAAsB,CAACC,OAAO,CAAC,gCAAgC,CAAC,CAAC;AAEjF,IAAIO,cAAc,GAAGR,sBAAsB,CAACC,OAAO,CAAC,qCAAqC,CAAC,CAAC;AAE3F,IAAIQ,UAAU,GAAGT,sBAAsB,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;AAE9D,IAAIS,MAAM,GAAGV,sBAAsB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AAErD,IAAIU,WAAW,GAAGX,sBAAsB,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;AAE/D,IAAIW,mBAAmB,GAAGZ,sBAAsB,CAACC,OAAO,CAAC,uBAAuB,CAAC,CAAC;AAElF,IAAIY,MAAM,GAAGb,sBAAsB,CAACC,OAAO,CAAC,SAAS,CAAC,CAAC;AAEvD,IAAIa,SAAS,GAAG;EACdC,MAAM,EAAEN,UAAU,CAAC,SAAS,CAAC,CAACO,IAAI;EAClCC,OAAO,EAAER,UAAU,CAAC,SAAS,CAAC,CAACS,IAAI,CAACC,UAAU;EAC9CC,OAAO,EAAEX,UAAU,CAAC,SAAS,CAAC,CAACS,IAAI;EACnCG,MAAM,EAAEZ,UAAU,CAAC,SAAS,CAAC,CAACS,IAAI;EAClCI,SAAS,EAAEb,UAAU,CAAC,SAAS,CAAC,CAACc;AACnC,CAAC;AACD,IAAIC,YAAY,GAAG;EACjBT,MAAM,EAAE;AACV,CAAC;AAED,SAAST,WAAWA,CAACmB,KAAK,EAAE;EAC1B,IAAIV,MAAM,GAAGU,KAAK,CAACV,MAAM;IACrBO,SAAS,GAAGG,KAAK,CAACH,SAAS;EAC/B,IAAII,EAAE,GAAG,CAAC,CAAC,EAAElB,cAAc,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAEiB,KAAK,CAAC;EAClD,OAAOC,EAAE,CAACC,QAAQ;EAClB,OAAOD,EAAE,CAACX,MAAM;EAChB,OAAOW,EAAE,CAACJ,SAAS;EACnB,OAAOZ,MAAM,CAAC,SAAS,CAAC,CAACkB,aAAa,CAAChB,mBAAmB,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAEL,SAAS,CAAC,SAAS,CAAC,EAAE;IAC/Fe,SAAS,EAAE,CAAC,CAAC,EAAEX,WAAW,CAAC,SAAS,CAAC,EAAEW,SAAS,EAAE;MAChD,gCAAgC,EAAE,CAAC,CAACP,MAAM;MAC1C,+BAA+B,EAAE,CAACA;IACpC,CAAC,EAAE,gEAAgE;EACrE,CAAC,EAAEW,EAAE,CAAC,EAAEhB,MAAM,CAAC,SAAS,CAAC,CAACkB,aAAa,CAACf,MAAM,CAAC,SAAS,CAAC,EAAEY,KAAK,CAAC,CAAC;AACpE;AAEAnB,WAAW,CAACQ,SAAS,GAAGA,SAAS;AACjCR,WAAW,CAACkB,YAAY,GAAGA,YAAY;AACvClB,WAAW,CAACuB,WAAW,GAAG,aAAa"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}