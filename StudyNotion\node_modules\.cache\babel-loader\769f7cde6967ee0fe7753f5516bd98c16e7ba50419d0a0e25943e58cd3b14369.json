{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\temp\\\\StudyNotion\\\\src\\\\components\\\\core\\\\Dashboard\\\\Cart\\\\RenderCartCourses.jsx\",\n  _s = $RefreshSig$();\nimport { FaStar } from \"react-icons/fa\";\nimport { RiDeleteBin6Line } from \"react-icons/ri\";\nimport ReactStars from \"react-rating-stars-component\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { removeFromCart } from \"../../../../slices/cartSlice\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function RenderCartCourses() {\n  _s();\n  const {\n    cart\n  } = useSelector(state => state.cart);\n  const dispatch = useDispatch();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-1 flex-col\",\n    children: cart.map((course, indx) => {\n      var _course$category, _course$ratingAndRevi, _course$ratingAndRevi2;\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `flex w-full flex-wrap items-start justify-between gap-6 ${indx !== cart.length - 1 && \"border-b border-b-richblack-400 pb-6\"} ${indx !== 0 && \"mt-6\"} `,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-1 flex-col gap-4 xl:flex-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: course === null || course === void 0 ? void 0 : course.thumbnail,\n            alt: course === null || course === void 0 ? void 0 : course.courseName,\n            className: \"h-[148px] w-[220px] rounded-lg object-cover\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col space-y-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-lg font-medium text-richblack-5\",\n              children: course === null || course === void 0 ? void 0 : course.courseName\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 27,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-richblack-300\",\n              children: course === null || course === void 0 ? void 0 : (_course$category = course.category) === null || _course$category === void 0 ? void 0 : _course$category.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 30,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-yellow-5\",\n                children: \"4.5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 34,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(ReactStars, {\n                count: 5,\n                value: course === null || course === void 0 ? void 0 : (_course$ratingAndRevi = course.ratingAndReviews) === null || _course$ratingAndRevi === void 0 ? void 0 : _course$ratingAndRevi.length,\n                size: 20,\n                edit: false,\n                activeColor: \"#ffd700\",\n                emptyIcon: /*#__PURE__*/_jsxDEV(FaStar, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 41,\n                  columnNumber: 30\n                }, this),\n                fullIcon: /*#__PURE__*/_jsxDEV(FaStar, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 42,\n                  columnNumber: 29\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 35,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-richblack-400\",\n                children: [course === null || course === void 0 ? void 0 : (_course$ratingAndRevi2 = course.ratingAndReviews) === null || _course$ratingAndRevi2 === void 0 ? void 0 : _course$ratingAndRevi2.length, \" Ratings\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 44,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 33,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col items-end space-y-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => dispatch(removeFromCart(course._id)),\n            className: \"flex items-center gap-x-1 rounded-md border border-richblack-600 bg-richblack-700 py-3 px-[12px] text-pink-200\",\n            children: [/*#__PURE__*/_jsxDEV(RiDeleteBin6Line, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 55,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Remove\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 56,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mb-6 text-3xl font-medium text-yellow-100\",\n            children: [\"\\u20B9 \", course === null || course === void 0 ? void 0 : course.price]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 11\n        }, this)]\n      }, course._id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 14,\n        columnNumber: 9\n      }, this);\n    })\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 5\n  }, this);\n}\n_s(RenderCartCourses, \"2Uk6fFZ91Mc7Y8ypsgiP11a+U3g=\", false, function () {\n  return [useSelector, useDispatch];\n});\n_c = RenderCartCourses;\nvar _c;\n$RefreshReg$(_c, \"RenderCartCourses\");", "map": {"version": 3, "names": ["FaStar", "RiDeleteBin6Line", "ReactStars", "useDispatch", "useSelector", "removeFromCart", "jsxDEV", "_jsxDEV", "RenderCartCourses", "_s", "cart", "state", "dispatch", "className", "children", "map", "course", "indx", "_course$category", "_course$ratingAndRevi", "_course$ratingAndRevi2", "length", "src", "thumbnail", "alt", "courseName", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "category", "name", "count", "value", "ratingAndReviews", "size", "edit", "activeColor", "emptyIcon", "fullIcon", "onClick", "_id", "price", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/src/components/core/Dashboard/Cart/RenderCartCourses.jsx"], "sourcesContent": ["import { FaStar } from \"react-icons/fa\"\r\nimport { RiDeleteBin6Line } from \"react-icons/ri\"\r\nimport ReactStars from \"react-rating-stars-component\"\r\nimport { useDispatch, useSelector } from \"react-redux\"\r\n\r\nimport { removeFromCart } from \"../../../../slices/cartSlice\"\r\n\r\nexport default function RenderCartCourses() {\r\n  const { cart } = useSelector((state) => state.cart)\r\n  const dispatch = useDispatch()\r\n  return (\r\n    <div className=\"flex flex-1 flex-col\">\r\n      {cart.map((course, indx) => (\r\n        <div\r\n          key={course._id}\r\n          className={`flex w-full flex-wrap items-start justify-between gap-6 ${\r\n            indx !== cart.length - 1 && \"border-b border-b-richblack-400 pb-6\"\r\n          } ${indx !== 0 && \"mt-6\"} `}\r\n        >\r\n          <div className=\"flex flex-1 flex-col gap-4 xl:flex-row\">\r\n            <img\r\n              src={course?.thumbnail}\r\n              alt={course?.courseName}\r\n              className=\"h-[148px] w-[220px] rounded-lg object-cover\"\r\n            />\r\n            <div className=\"flex flex-col space-y-1\">\r\n              <p className=\"text-lg font-medium text-richblack-5\">\r\n                {course?.courseName}\r\n              </p>\r\n              <p className=\"text-sm text-richblack-300\">\r\n                {course?.category?.name}\r\n              </p>\r\n              <div className=\"flex items-center gap-2\">\r\n                <span className=\"text-yellow-5\">4.5</span>\r\n                <ReactStars\r\n                  count={5}\r\n                  value={course?.ratingAndReviews?.length}\r\n                  size={20}\r\n                  edit={false}\r\n                  activeColor=\"#ffd700\"\r\n                  emptyIcon={<FaStar />}\r\n                  fullIcon={<FaStar />}\r\n                />\r\n                <span className=\"text-richblack-400\">\r\n                  {course?.ratingAndReviews?.length} Ratings\r\n                </span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div className=\"flex flex-col items-end space-y-2\">\r\n            <button\r\n              onClick={() => dispatch(removeFromCart(course._id))}\r\n              className=\"flex items-center gap-x-1 rounded-md border border-richblack-600 bg-richblack-700 py-3 px-[12px] text-pink-200\"\r\n            >\r\n              <RiDeleteBin6Line />\r\n              <span>Remove</span>\r\n            </button>\r\n            <p className=\"mb-6 text-3xl font-medium text-yellow-100\">\r\n              ₹ {course?.price}\r\n            </p>\r\n          </div>\r\n        </div>\r\n      ))}\r\n    </div>\r\n  )\r\n}"], "mappings": ";;AAAA,SAASA,MAAM,QAAQ,gBAAgB;AACvC,SAASC,gBAAgB,QAAQ,gBAAgB;AACjD,OAAOC,UAAU,MAAM,8BAA8B;AACrD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AAEtD,SAASC,cAAc,QAAQ,8BAA8B;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAE7D,eAAe,SAASC,iBAAiBA,CAAA,EAAG;EAAAC,EAAA;EAC1C,MAAM;IAAEC;EAAK,CAAC,GAAGN,WAAW,CAAEO,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAME,QAAQ,GAAGT,WAAW,EAAE;EAC9B,oBACEI,OAAA;IAAKM,SAAS,EAAC,sBAAsB;IAAAC,QAAA,EAClCJ,IAAI,CAACK,GAAG,CAAC,CAACC,MAAM,EAAEC,IAAI;MAAA,IAAAC,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA;MAAA,oBACrBb,OAAA;QAEEM,SAAS,EAAG,2DACVI,IAAI,KAAKP,IAAI,CAACW,MAAM,GAAG,CAAC,IAAI,sCAC7B,IAAGJ,IAAI,KAAK,CAAC,IAAI,MAAO,GAAG;QAAAH,QAAA,gBAE5BP,OAAA;UAAKM,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrDP,OAAA;YACEe,GAAG,EAAEN,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEO,SAAU;YACvBC,GAAG,EAAER,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAES,UAAW;YACxBZ,SAAS,EAAC;UAA6C;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACvD,eACFtB,OAAA;YAAKM,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtCP,OAAA;cAAGM,SAAS,EAAC,sCAAsC;cAAAC,QAAA,EAChDE,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAES;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QACjB,eACJtB,OAAA;cAAGM,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EACtCE,MAAM,aAANA,MAAM,wBAAAE,gBAAA,GAANF,MAAM,CAAEc,QAAQ,cAAAZ,gBAAA,uBAAhBA,gBAAA,CAAkBa;YAAI;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QACrB,eACJtB,OAAA;cAAKM,SAAS,EAAC,yBAAyB;cAAAC,QAAA,gBACtCP,OAAA;gBAAMM,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAG;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAO,eAC1CtB,OAAA,CAACL,UAAU;gBACT8B,KAAK,EAAE,CAAE;gBACTC,KAAK,EAAEjB,MAAM,aAANA,MAAM,wBAAAG,qBAAA,GAANH,MAAM,CAAEkB,gBAAgB,cAAAf,qBAAA,uBAAxBA,qBAAA,CAA0BE,MAAO;gBACxCc,IAAI,EAAE,EAAG;gBACTC,IAAI,EAAE,KAAM;gBACZC,WAAW,EAAC,SAAS;gBACrBC,SAAS,eAAE/B,OAAA,CAACP,MAAM;kBAAA0B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QAAI;gBACtBU,QAAQ,eAAEhC,OAAA,CAACP,MAAM;kBAAA0B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA;cAAI;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QACrB,eACFtB,OAAA;gBAAMM,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,GACjCE,MAAM,aAANA,MAAM,wBAAAI,sBAAA,GAANJ,MAAM,CAAEkB,gBAAgB,cAAAd,sBAAA,uBAAxBA,sBAAA,CAA0BC,MAAM,EAAC,UACpC;cAAA;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAO;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QACH;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACF;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACF,eACNtB,OAAA;UAAKM,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDP,OAAA;YACEiC,OAAO,EAAEA,CAAA,KAAM5B,QAAQ,CAACP,cAAc,CAACW,MAAM,CAACyB,GAAG,CAAC,CAAE;YACpD5B,SAAS,EAAC,gHAAgH;YAAAC,QAAA,gBAE1HP,OAAA,CAACN,gBAAgB;cAAAyB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QAAG,eACpBtB,OAAA;cAAAO,QAAA,EAAM;YAAM;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QAAO;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACZ,eACTtB,OAAA;YAAGM,SAAS,EAAC,2CAA2C;YAAAC,QAAA,GAAC,SACrD,EAACE,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE0B,KAAK;UAAA;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACd;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACA;MAAA,GA9CDb,MAAM,CAACyB,GAAG;QAAAf,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QA+CX;IAAA,CACP;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACE;AAEV;AAACpB,EAAA,CA1DuBD,iBAAiB;EAAA,QACtBJ,WAAW,EACXD,WAAW;AAAA;AAAAwC,EAAA,GAFNnC,iBAAiB;AAAA,IAAAmC,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}