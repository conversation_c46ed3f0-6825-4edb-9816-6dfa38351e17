{"ast": null, "code": "import { createContext, useContext } from 'react';\nexport const SwiperSlideContext = /*#__PURE__*/createContext(null);\nexport const useSwiperSlide = () => {\n  return useContext(SwiperSlideContext);\n};\nexport const SwiperContext = /*#__PURE__*/createContext(null);\nexport const useSwiper = () => {\n  return useContext(SwiperContext);\n};", "map": {"version": 3, "names": ["createContext", "useContext", "SwiperSlideContext", "useSwiperSlide", "SwiperContext", "useSwiper"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/react/context.js"], "sourcesContent": ["import { createContext, useContext } from 'react';\nexport const SwiperSlideContext = /*#__PURE__*/createContext(null);\nexport const useSwiperSlide = () => {\n  return useContext(SwiperSlideContext);\n};\nexport const SwiperContext = /*#__PURE__*/createContext(null);\nexport const useSwiper = () => {\n  return useContext(SwiperContext);\n};"], "mappings": "AAAA,SAASA,aAAa,EAAEC,UAAU,QAAQ,OAAO;AACjD,OAAO,MAAMC,kBAAkB,GAAG,aAAaF,aAAa,CAAC,IAAI,CAAC;AAClE,OAAO,MAAMG,cAAc,GAAGA,CAAA,KAAM;EAClC,OAAOF,UAAU,CAACC,kBAAkB,CAAC;AACvC,CAAC;AACD,OAAO,MAAME,aAAa,GAAG,aAAaJ,aAAa,CAAC,IAAI,CAAC;AAC7D,OAAO,MAAMK,SAAS,GAAGA,CAAA,KAAM;EAC7B,OAAOJ,UAAU,CAACG,aAAa,CAAC;AAClC,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}