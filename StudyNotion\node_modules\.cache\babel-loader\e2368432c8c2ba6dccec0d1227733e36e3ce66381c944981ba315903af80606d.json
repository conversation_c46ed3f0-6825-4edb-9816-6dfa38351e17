{"ast": null, "code": "export default function removeSlide(slidesIndexes) {\n  const swiper = this;\n  const {\n    params,\n    activeIndex\n  } = swiper;\n  let activeIndexBuffer = activeIndex;\n  if (params.loop) {\n    activeIndexBuffer -= swiper.loopedSlides;\n    swiper.loopDestroy();\n  }\n  let newActiveIndex = activeIndexBuffer;\n  let indexToRemove;\n  if (typeof slidesIndexes === 'object' && 'length' in slidesIndexes) {\n    for (let i = 0; i < slidesIndexes.length; i += 1) {\n      indexToRemove = slidesIndexes[i];\n      if (swiper.slides[indexToRemove]) swiper.slides[indexToRemove].remove();\n      if (indexToRemove < newActiveIndex) newActiveIndex -= 1;\n    }\n    newActiveIndex = Math.max(newActiveIndex, 0);\n  } else {\n    indexToRemove = slidesIndexes;\n    if (swiper.slides[indexToRemove]) swiper.slides[indexToRemove].remove();\n    if (indexToRemove < newActiveIndex) newActiveIndex -= 1;\n    newActiveIndex = Math.max(newActiveIndex, 0);\n  }\n  swiper.recalcSlides();\n  if (params.loop) {\n    swiper.loopCreate();\n  }\n  if (!params.observer || swiper.isElement) {\n    swiper.update();\n  }\n  if (params.loop) {\n    swiper.slideTo(newActiveIndex + swiper.loopedSlides, 0, false);\n  } else {\n    swiper.slideTo(newActiveIndex, 0, false);\n  }\n}", "map": {"version": 3, "names": ["removeSlide", "slidesIndexes", "swiper", "params", "activeIndex", "activeIndexBuffer", "loop", "loopedSlides", "loop<PERSON><PERSON><PERSON>", "newActiveIndex", "indexToRemove", "i", "length", "slides", "remove", "Math", "max", "recalcSlides", "loopCreate", "observer", "isElement", "update", "slideTo"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/modules/manipulation/methods/removeSlide.js"], "sourcesContent": ["export default function removeSlide(slidesIndexes) {\n  const swiper = this;\n  const {\n    params,\n    activeIndex\n  } = swiper;\n  let activeIndexBuffer = activeIndex;\n  if (params.loop) {\n    activeIndexBuffer -= swiper.loopedSlides;\n    swiper.loopDestroy();\n  }\n  let newActiveIndex = activeIndexBuffer;\n  let indexToRemove;\n  if (typeof slidesIndexes === 'object' && 'length' in slidesIndexes) {\n    for (let i = 0; i < slidesIndexes.length; i += 1) {\n      indexToRemove = slidesIndexes[i];\n      if (swiper.slides[indexToRemove]) swiper.slides[indexToRemove].remove();\n      if (indexToRemove < newActiveIndex) newActiveIndex -= 1;\n    }\n    newActiveIndex = Math.max(newActiveIndex, 0);\n  } else {\n    indexToRemove = slidesIndexes;\n    if (swiper.slides[indexToRemove]) swiper.slides[indexToRemove].remove();\n    if (indexToRemove < newActiveIndex) newActiveIndex -= 1;\n    newActiveIndex = Math.max(newActiveIndex, 0);\n  }\n  swiper.recalcSlides();\n  if (params.loop) {\n    swiper.loopCreate();\n  }\n  if (!params.observer || swiper.isElement) {\n    swiper.update();\n  }\n  if (params.loop) {\n    swiper.slideTo(newActiveIndex + swiper.loopedSlides, 0, false);\n  } else {\n    swiper.slideTo(newActiveIndex, 0, false);\n  }\n}"], "mappings": "AAAA,eAAe,SAASA,WAAWA,CAACC,aAAa,EAAE;EACjD,MAAMC,MAAM,GAAG,IAAI;EACnB,MAAM;IACJC,MAAM;IACNC;EACF,CAAC,GAAGF,MAAM;EACV,IAAIG,iBAAiB,GAAGD,WAAW;EACnC,IAAID,MAAM,CAACG,IAAI,EAAE;IACfD,iBAAiB,IAAIH,MAAM,CAACK,YAAY;IACxCL,MAAM,CAACM,WAAW,EAAE;EACtB;EACA,IAAIC,cAAc,GAAGJ,iBAAiB;EACtC,IAAIK,aAAa;EACjB,IAAI,OAAOT,aAAa,KAAK,QAAQ,IAAI,QAAQ,IAAIA,aAAa,EAAE;IAClE,KAAK,IAAIU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGV,aAAa,CAACW,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;MAChDD,aAAa,GAAGT,aAAa,CAACU,CAAC,CAAC;MAChC,IAAIT,MAAM,CAACW,MAAM,CAACH,aAAa,CAAC,EAAER,MAAM,CAACW,MAAM,CAACH,aAAa,CAAC,CAACI,MAAM,EAAE;MACvE,IAAIJ,aAAa,GAAGD,cAAc,EAAEA,cAAc,IAAI,CAAC;IACzD;IACAA,cAAc,GAAGM,IAAI,CAACC,GAAG,CAACP,cAAc,EAAE,CAAC,CAAC;EAC9C,CAAC,MAAM;IACLC,aAAa,GAAGT,aAAa;IAC7B,IAAIC,MAAM,CAACW,MAAM,CAACH,aAAa,CAAC,EAAER,MAAM,CAACW,MAAM,CAACH,aAAa,CAAC,CAACI,MAAM,EAAE;IACvE,IAAIJ,aAAa,GAAGD,cAAc,EAAEA,cAAc,IAAI,CAAC;IACvDA,cAAc,GAAGM,IAAI,CAACC,GAAG,CAACP,cAAc,EAAE,CAAC,CAAC;EAC9C;EACAP,MAAM,CAACe,YAAY,EAAE;EACrB,IAAId,MAAM,CAACG,IAAI,EAAE;IACfJ,MAAM,CAACgB,UAAU,EAAE;EACrB;EACA,IAAI,CAACf,MAAM,CAACgB,QAAQ,IAAIjB,MAAM,CAACkB,SAAS,EAAE;IACxClB,MAAM,CAACmB,MAAM,EAAE;EACjB;EACA,IAAIlB,MAAM,CAACG,IAAI,EAAE;IACfJ,MAAM,CAACoB,OAAO,CAACb,cAAc,GAAGP,MAAM,CAACK,YAAY,EAAE,CAAC,EAAE,KAAK,CAAC;EAChE,CAAC,MAAM;IACLL,MAAM,CAACoB,OAAO,CAACb,cAAc,EAAE,CAAC,EAAE,KAAK,CAAC;EAC1C;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}