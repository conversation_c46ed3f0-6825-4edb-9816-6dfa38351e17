{"ast": null, "code": "export default function slideToLoop() {\n  let index = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n  let speed = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : this.params.speed;\n  let runCallbacks = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;\n  let internal = arguments.length > 3 ? arguments[3] : undefined;\n  if (typeof index === 'string') {\n    const indexAsNumber = parseInt(index, 10);\n    index = indexAsNumber;\n  }\n  const swiper = this;\n  let newIndex = index;\n  if (swiper.params.loop) {\n    if (swiper.virtual && swiper.params.virtual.enabled) {\n      // eslint-disable-next-line\n      newIndex = newIndex + swiper.virtual.slidesBefore;\n    } else {\n      newIndex = swiper.getSlideIndexByData(newIndex);\n    }\n  }\n  return swiper.slideTo(newIndex, speed, runCallbacks, internal);\n}", "map": {"version": 3, "names": ["slideToLoop", "index", "arguments", "length", "undefined", "speed", "params", "runCallbacks", "internal", "indexAsNumber", "parseInt", "swiper", "newIndex", "loop", "virtual", "enabled", "slidesBefore", "getSlideIndexByData", "slideTo"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/core/slide/slideToLoop.js"], "sourcesContent": ["export default function slideToLoop(index = 0, speed = this.params.speed, runCallbacks = true, internal) {\n  if (typeof index === 'string') {\n    const indexAsNumber = parseInt(index, 10);\n    index = indexAsNumber;\n  }\n  const swiper = this;\n  let newIndex = index;\n  if (swiper.params.loop) {\n    if (swiper.virtual && swiper.params.virtual.enabled) {\n      // eslint-disable-next-line\n      newIndex = newIndex + swiper.virtual.slidesBefore;\n    } else {\n      newIndex = swiper.getSlideIndexByData(newIndex);\n    }\n  }\n  return swiper.slideTo(newIndex, speed, runCallbacks, internal);\n}"], "mappings": "AAAA,eAAe,SAASA,WAAWA,CAAA,EAAsE;EAAA,IAArEC,KAAK,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAAA,IAAEG,KAAK,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI,CAACI,MAAM,CAACD,KAAK;EAAA,IAAEE,YAAY,GAAAL,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;EAAA,IAAEM,QAAQ,GAAAN,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EACrG,IAAI,OAAOH,KAAK,KAAK,QAAQ,EAAE;IAC7B,MAAMQ,aAAa,GAAGC,QAAQ,CAACT,KAAK,EAAE,EAAE,CAAC;IACzCA,KAAK,GAAGQ,aAAa;EACvB;EACA,MAAME,MAAM,GAAG,IAAI;EACnB,IAAIC,QAAQ,GAAGX,KAAK;EACpB,IAAIU,MAAM,CAACL,MAAM,CAACO,IAAI,EAAE;IACtB,IAAIF,MAAM,CAACG,OAAO,IAAIH,MAAM,CAACL,MAAM,CAACQ,OAAO,CAACC,OAAO,EAAE;MACnD;MACAH,QAAQ,GAAGA,QAAQ,GAAGD,MAAM,CAACG,OAAO,CAACE,YAAY;IACnD,CAAC,MAAM;MACLJ,QAAQ,GAAGD,MAAM,CAACM,mBAAmB,CAACL,QAAQ,CAAC;IACjD;EACF;EACA,OAAOD,MAAM,CAACO,OAAO,CAACN,QAAQ,EAAEP,KAAK,EAAEE,YAAY,EAAEC,QAAQ,CAAC;AAChE"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}