{"ast": null, "code": "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').Code} Code\n * @typedef {import('../state.js').State} State\n\n */\n\n/**\n * Turn an mdast `code` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Code} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function code(state, node) {\n  const value = node.value ? node.value + '\\n' : '';\n  // To do: next major, use `node.lang` w/o regex, the splitting’s been going\n  // on for years in remark now.\n  const lang = node.lang ? node.lang.match(/^[^ \\t]+(?=[ \\t]|$)/) : null;\n  /** @type {Properties} */\n  const properties = {};\n  if (lang) {\n    properties.className = ['language-' + lang];\n  }\n\n  // Create `<code>`.\n  /** @type {Element} */\n  let result = {\n    type: 'element',\n    tagName: 'code',\n    properties,\n    children: [{\n      type: 'text',\n      value\n    }]\n  };\n  if (node.meta) {\n    result.data = {\n      meta: node.meta\n    };\n  }\n  state.patch(node, result);\n  result = state.applyData(node, result);\n\n  // Create `<pre>`.\n  result = {\n    type: 'element',\n    tagName: 'pre',\n    properties: {},\n    children: [result]\n  };\n  state.patch(node, result);\n  return result;\n}", "map": {"version": 3, "names": ["code", "state", "node", "value", "lang", "match", "properties", "className", "result", "type", "tagName", "children", "meta", "data", "patch", "applyData"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/mdast-util-to-hast/lib/handlers/code.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').Code} Code\n * @typedef {import('../state.js').State} State\n\n */\n\n/**\n * Turn an mdast `code` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Code} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function code(state, node) {\n  const value = node.value ? node.value + '\\n' : ''\n  // To do: next major, use `node.lang` w/o regex, the splitting’s been going\n  // on for years in remark now.\n  const lang = node.lang ? node.lang.match(/^[^ \\t]+(?=[ \\t]|$)/) : null\n  /** @type {Properties} */\n  const properties = {}\n\n  if (lang) {\n    properties.className = ['language-' + lang]\n  }\n\n  // Create `<code>`.\n  /** @type {Element} */\n  let result = {\n    type: 'element',\n    tagName: 'code',\n    properties,\n    children: [{type: 'text', value}]\n  }\n\n  if (node.meta) {\n    result.data = {meta: node.meta}\n  }\n\n  state.patch(node, result)\n  result = state.applyData(node, result)\n\n  // Create `<pre>`.\n  result = {type: 'element', tagName: 'pre', properties: {}, children: [result]}\n  state.patch(node, result)\n  return result\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,IAAIA,CAACC,KAAK,EAAEC,IAAI,EAAE;EAChC,MAAMC,KAAK,GAAGD,IAAI,CAACC,KAAK,GAAGD,IAAI,CAACC,KAAK,GAAG,IAAI,GAAG,EAAE;EACjD;EACA;EACA,MAAMC,IAAI,GAAGF,IAAI,CAACE,IAAI,GAAGF,IAAI,CAACE,IAAI,CAACC,KAAK,CAAC,qBAAqB,CAAC,GAAG,IAAI;EACtE;EACA,MAAMC,UAAU,GAAG,CAAC,CAAC;EAErB,IAAIF,IAAI,EAAE;IACRE,UAAU,CAACC,SAAS,GAAG,CAAC,WAAW,GAAGH,IAAI,CAAC;EAC7C;;EAEA;EACA;EACA,IAAII,MAAM,GAAG;IACXC,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,MAAM;IACfJ,UAAU;IACVK,QAAQ,EAAE,CAAC;MAACF,IAAI,EAAE,MAAM;MAAEN;IAAK,CAAC;EAClC,CAAC;EAED,IAAID,IAAI,CAACU,IAAI,EAAE;IACbJ,MAAM,CAACK,IAAI,GAAG;MAACD,IAAI,EAAEV,IAAI,CAACU;IAAI,CAAC;EACjC;EAEAX,KAAK,CAACa,KAAK,CAACZ,IAAI,EAAEM,MAAM,CAAC;EACzBA,MAAM,GAAGP,KAAK,CAACc,SAAS,CAACb,IAAI,EAAEM,MAAM,CAAC;;EAEtC;EACAA,MAAM,GAAG;IAACC,IAAI,EAAE,SAAS;IAAEC,OAAO,EAAE,KAAK;IAAEJ,UAAU,EAAE,CAAC,CAAC;IAAEK,QAAQ,EAAE,CAACH,MAAM;EAAC,CAAC;EAC9EP,KAAK,CAACa,KAAK,CAACZ,IAAI,EAAEM,MAAM,CAAC;EACzB,OAAOA,MAAM;AACf"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}