{"ast": null, "code": "export default function onScroll() {\n  const swiper = this;\n  const {\n    wrapperEl,\n    rtlTranslate,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n  swiper.previousTranslate = swiper.translate;\n  if (swiper.isHorizontal()) {\n    swiper.translate = -wrapperEl.scrollLeft;\n  } else {\n    swiper.translate = -wrapperEl.scrollTop;\n  }\n  // eslint-disable-next-line\n  if (swiper.translate === 0) swiper.translate = 0;\n  swiper.updateActiveIndex();\n  swiper.updateSlidesClasses();\n  let newProgress;\n  const translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n  if (translatesDiff === 0) {\n    newProgress = 0;\n  } else {\n    newProgress = (swiper.translate - swiper.minTranslate()) / translatesDiff;\n  }\n  if (newProgress !== swiper.progress) {\n    swiper.updateProgress(rtlTranslate ? -swiper.translate : swiper.translate);\n  }\n  swiper.emit('setTranslate', swiper.translate, false);\n}", "map": {"version": 3, "names": ["onScroll", "swiper", "wrapperEl", "rtlTranslate", "enabled", "previousTranslate", "translate", "isHorizontal", "scrollLeft", "scrollTop", "updateActiveIndex", "updateSlidesClasses", "newProgress", "translatesDiff", "maxTranslate", "minTranslate", "progress", "updateProgress", "emit"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/core/events/onScroll.js"], "sourcesContent": ["export default function onScroll() {\n  const swiper = this;\n  const {\n    wrapperEl,\n    rtlTranslate,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n  swiper.previousTranslate = swiper.translate;\n  if (swiper.isHorizontal()) {\n    swiper.translate = -wrapperEl.scrollLeft;\n  } else {\n    swiper.translate = -wrapperEl.scrollTop;\n  }\n  // eslint-disable-next-line\n  if (swiper.translate === 0) swiper.translate = 0;\n  swiper.updateActiveIndex();\n  swiper.updateSlidesClasses();\n  let newProgress;\n  const translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n  if (translatesDiff === 0) {\n    newProgress = 0;\n  } else {\n    newProgress = (swiper.translate - swiper.minTranslate()) / translatesDiff;\n  }\n  if (newProgress !== swiper.progress) {\n    swiper.updateProgress(rtlTranslate ? -swiper.translate : swiper.translate);\n  }\n  swiper.emit('setTranslate', swiper.translate, false);\n}"], "mappings": "AAAA,eAAe,SAASA,QAAQA,CAAA,EAAG;EACjC,MAAMC,MAAM,GAAG,IAAI;EACnB,MAAM;IACJC,SAAS;IACTC,YAAY;IACZC;EACF,CAAC,GAAGH,MAAM;EACV,IAAI,CAACG,OAAO,EAAE;EACdH,MAAM,CAACI,iBAAiB,GAAGJ,MAAM,CAACK,SAAS;EAC3C,IAAIL,MAAM,CAACM,YAAY,EAAE,EAAE;IACzBN,MAAM,CAACK,SAAS,GAAG,CAACJ,SAAS,CAACM,UAAU;EAC1C,CAAC,MAAM;IACLP,MAAM,CAACK,SAAS,GAAG,CAACJ,SAAS,CAACO,SAAS;EACzC;EACA;EACA,IAAIR,MAAM,CAACK,SAAS,KAAK,CAAC,EAAEL,MAAM,CAACK,SAAS,GAAG,CAAC;EAChDL,MAAM,CAACS,iBAAiB,EAAE;EAC1BT,MAAM,CAACU,mBAAmB,EAAE;EAC5B,IAAIC,WAAW;EACf,MAAMC,cAAc,GAAGZ,MAAM,CAACa,YAAY,EAAE,GAAGb,MAAM,CAACc,YAAY,EAAE;EACpE,IAAIF,cAAc,KAAK,CAAC,EAAE;IACxBD,WAAW,GAAG,CAAC;EACjB,CAAC,MAAM;IACLA,WAAW,GAAG,CAACX,MAAM,CAACK,SAAS,GAAGL,MAAM,CAACc,YAAY,EAAE,IAAIF,cAAc;EAC3E;EACA,IAAID,WAAW,KAAKX,MAAM,CAACe,QAAQ,EAAE;IACnCf,MAAM,CAACgB,cAAc,CAACd,YAAY,GAAG,CAACF,MAAM,CAACK,SAAS,GAAGL,MAAM,CAACK,SAAS,CAAC;EAC5E;EACAL,MAAM,CAACiB,IAAI,CAAC,cAAc,EAAEjB,MAAM,CAACK,SAAS,EAAE,KAAK,CAAC;AACtD"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}