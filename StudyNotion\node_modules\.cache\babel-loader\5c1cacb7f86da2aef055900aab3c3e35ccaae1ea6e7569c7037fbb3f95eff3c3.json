{"ast": null, "code": "export default function updateSlidesOffset() {\n  const swiper = this;\n  const slides = swiper.slides;\n  // eslint-disable-next-line\n  const minusOffset = swiper.isElement ? swiper.isHorizontal() ? swiper.wrapperEl.offsetLeft : swiper.wrapperEl.offsetTop : 0;\n  for (let i = 0; i < slides.length; i += 1) {\n    slides[i].swiperSlideOffset = (swiper.isHorizontal() ? slides[i].offsetLeft : slides[i].offsetTop) - minusOffset - swiper.cssOverflowAdjustment();\n  }\n}", "map": {"version": 3, "names": ["updateSlidesOffset", "swiper", "slides", "minusOffset", "isElement", "isHorizontal", "wrapperEl", "offsetLeft", "offsetTop", "i", "length", "swiperSlideOffset", "cssOverflowAdjustment"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/core/update/updateSlidesOffset.js"], "sourcesContent": ["export default function updateSlidesOffset() {\n  const swiper = this;\n  const slides = swiper.slides;\n  // eslint-disable-next-line\n  const minusOffset = swiper.isElement ? swiper.isHorizontal() ? swiper.wrapperEl.offsetLeft : swiper.wrapperEl.offsetTop : 0;\n  for (let i = 0; i < slides.length; i += 1) {\n    slides[i].swiperSlideOffset = (swiper.isHorizontal() ? slides[i].offsetLeft : slides[i].offsetTop) - minusOffset - swiper.cssOverflowAdjustment();\n  }\n}"], "mappings": "AAAA,eAAe,SAASA,kBAAkBA,CAAA,EAAG;EAC3C,MAAMC,MAAM,GAAG,IAAI;EACnB,MAAMC,MAAM,GAAGD,MAAM,CAACC,MAAM;EAC5B;EACA,MAAMC,WAAW,GAAGF,MAAM,CAACG,SAAS,GAAGH,MAAM,CAACI,YAAY,EAAE,GAAGJ,MAAM,CAACK,SAAS,CAACC,UAAU,GAAGN,MAAM,CAACK,SAAS,CAACE,SAAS,GAAG,CAAC;EAC3H,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,MAAM,CAACQ,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;IACzCP,MAAM,CAACO,CAAC,CAAC,CAACE,iBAAiB,GAAG,CAACV,MAAM,CAACI,YAAY,EAAE,GAAGH,MAAM,CAACO,CAAC,CAAC,CAACF,UAAU,GAAGL,MAAM,CAACO,CAAC,CAAC,CAACD,SAAS,IAAIL,WAAW,GAAGF,MAAM,CAACW,qBAAqB,EAAE;EACnJ;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}