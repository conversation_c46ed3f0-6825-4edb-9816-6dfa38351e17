{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\");\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nvar _objectSpread2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectSpread\"));\nvar _defineProperty2 = _interopRequireDefault(require(\"@babel/runtime/helpers/defineProperty\"));\nvar _objectWithoutProperties2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectWithoutProperties\"));\nvar _classCallCheck2 = _interopRequireDefault(require(\"@babel/runtime/helpers/classCallCheck\"));\nvar _createClass2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createClass\"));\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"@babel/runtime/helpers/possibleConstructorReturn\"));\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"@babel/runtime/helpers/getPrototypeOf\"));\nvar _assertThisInitialized2 = _interopRequireDefault(require(\"@babel/runtime/helpers/assertThisInitialized\"));\nvar _inherits2 = _interopRequireDefault(require(\"@babel/runtime/helpers/inherits\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _react = _interopRequireWildcard(require(\"react\"));\nvar _classnames = _interopRequireDefault(require(\"classnames\"));\nvar _Manager = _interopRequireDefault(require(\"../Manager\"));\nvar _BigPlayButton = _interopRequireDefault(require(\"./BigPlayButton\"));\nvar _LoadingSpinner = _interopRequireDefault(require(\"./LoadingSpinner\"));\nvar _PosterImage = _interopRequireDefault(require(\"./PosterImage\"));\nvar _Video = _interopRequireDefault(require(\"./Video\"));\nvar _Bezel = _interopRequireDefault(require(\"./Bezel\"));\nvar _Shortcut = _interopRequireDefault(require(\"./Shortcut\"));\nvar _ControlBar = _interopRequireDefault(require(\"./control-bar/ControlBar\"));\nvar browser = _interopRequireWildcard(require(\"../utils/browser\"));\nvar _dom = require(\"../utils/dom\");\nvar _utils = require(\"../utils\");\nvar _fullscreen = _interopRequireDefault(require(\"../utils/fullscreen\"));\nvar propTypes = {\n  children: _propTypes[\"default\"].any,\n  width: _propTypes[\"default\"].oneOfType([_propTypes[\"default\"].string, _propTypes[\"default\"].number]),\n  height: _propTypes[\"default\"].oneOfType([_propTypes[\"default\"].string, _propTypes[\"default\"].number]),\n  fluid: _propTypes[\"default\"].bool,\n  muted: _propTypes[\"default\"].bool,\n  playsInline: _propTypes[\"default\"].bool,\n  aspectRatio: _propTypes[\"default\"].string,\n  className: _propTypes[\"default\"].string,\n  videoId: _propTypes[\"default\"].string,\n  startTime: _propTypes[\"default\"].number,\n  loop: _propTypes[\"default\"].bool,\n  autoPlay: _propTypes[\"default\"].bool,\n  src: _propTypes[\"default\"].string,\n  poster: _propTypes[\"default\"].string,\n  preload: _propTypes[\"default\"].oneOf(['auto', 'metadata', 'none']),\n  onLoadStart: _propTypes[\"default\"].func,\n  onWaiting: _propTypes[\"default\"].func,\n  onCanPlay: _propTypes[\"default\"].func,\n  onCanPlayThrough: _propTypes[\"default\"].func,\n  onPlaying: _propTypes[\"default\"].func,\n  onEnded: _propTypes[\"default\"].func,\n  onSeeking: _propTypes[\"default\"].func,\n  onSeeked: _propTypes[\"default\"].func,\n  onPlay: _propTypes[\"default\"].func,\n  onPause: _propTypes[\"default\"].func,\n  onProgress: _propTypes[\"default\"].func,\n  onDurationChange: _propTypes[\"default\"].func,\n  onError: _propTypes[\"default\"].func,\n  onSuspend: _propTypes[\"default\"].func,\n  onAbort: _propTypes[\"default\"].func,\n  onEmptied: _propTypes[\"default\"].func,\n  onStalled: _propTypes[\"default\"].func,\n  onLoadedMetadata: _propTypes[\"default\"].func,\n  onLoadedData: _propTypes[\"default\"].func,\n  onTimeUpdate: _propTypes[\"default\"].func,\n  onRateChange: _propTypes[\"default\"].func,\n  onVolumeChange: _propTypes[\"default\"].func,\n  store: _propTypes[\"default\"].object\n};\nvar defaultProps = {\n  fluid: true,\n  muted: false,\n  playsInline: false,\n  preload: 'auto',\n  aspectRatio: 'auto'\n};\nvar Player = /*#__PURE__*/\nfunction (_Component) {\n  (0, _inherits2[\"default\"])(Player, _Component);\n  function Player(props) {\n    var _this;\n    (0, _classCallCheck2[\"default\"])(this, Player);\n    _this = (0, _possibleConstructorReturn2[\"default\"])(this, (0, _getPrototypeOf2[\"default\"])(Player).call(this, props));\n    _this.controlsHideTimer = null;\n    _this.video = null; // the Video component\n\n    _this.manager = new _Manager[\"default\"](props.store);\n    _this.actions = _this.manager.getActions();\n    _this.manager.subscribeToPlayerStateChange(_this.handleStateChange.bind((0, _assertThisInitialized2[\"default\"])(_this)));\n    _this.getStyle = _this.getStyle.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleResize = _this.handleResize.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.getChildren = _this.getChildren.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleMouseMove = (0, _utils.throttle)(_this.handleMouseMove.bind((0, _assertThisInitialized2[\"default\"])(_this)), 250);\n    _this.handleMouseDown = _this.handleMouseDown.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.startControlsTimer = _this.startControlsTimer.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleFullScreenChange = _this.handleFullScreenChange.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleKeyDown = _this.handleKeyDown.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleFocus = _this.handleFocus.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleBlur = _this.handleBlur.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    return _this;\n  }\n  (0, _createClass2[\"default\"])(Player, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      this.handleResize();\n      window.addEventListener('resize', this.handleResize);\n      _fullscreen[\"default\"].addEventListener(this.handleFullScreenChange);\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      // Remove event listener\n      window.removeEventListener('resize', this.handleResize);\n      _fullscreen[\"default\"].removeEventListener(this.handleFullScreenChange);\n      if (this.controlsHideTimer) {\n        window.clearTimeout(this.controlsHideTimer);\n      }\n    }\n  }, {\n    key: \"getDefaultChildren\",\n    value: function getDefaultChildren(originalChildren) {\n      var _this2 = this;\n      return [_react[\"default\"].createElement(_Video[\"default\"], {\n        ref: function ref(c) {\n          _this2.video = c;\n          _this2.manager.video = _this2.video;\n        },\n        key: \"video\",\n        order: 0.0\n      }, originalChildren), _react[\"default\"].createElement(_PosterImage[\"default\"], {\n        key: \"poster-image\",\n        order: 1.0\n      }), _react[\"default\"].createElement(_LoadingSpinner[\"default\"], {\n        key: \"loading-spinner\",\n        order: 2.0\n      }), _react[\"default\"].createElement(_Bezel[\"default\"], {\n        key: \"bezel\",\n        order: 3.0\n      }), _react[\"default\"].createElement(_BigPlayButton[\"default\"], {\n        key: \"big-play-button\",\n        order: 4.0\n      }), _react[\"default\"].createElement(_ControlBar[\"default\"], {\n        key: \"control-bar\",\n        order: 5.0\n      }), _react[\"default\"].createElement(_Shortcut[\"default\"], {\n        key: \"shortcut\",\n        order: 99.0\n      })];\n    }\n  }, {\n    key: \"getChildren\",\n    value: function getChildren(props) {\n      var _ = props.className,\n        originalChildren = props.children,\n        propsWithoutChildren = (0, _objectWithoutProperties2[\"default\"])(props, [\"className\", \"children\"]);\n      var children = _react[\"default\"].Children.toArray(this.props.children).filter(function (e) {\n        return !(0, _utils.isVideoChild)(e);\n      });\n      var defaultChildren = this.getDefaultChildren(originalChildren);\n      return (0, _utils.mergeAndSortChildren)(defaultChildren, children, propsWithoutChildren);\n    }\n  }, {\n    key: \"setWidthOrHeight\",\n    value: function setWidthOrHeight(style, name, value) {\n      var styleVal;\n      if (typeof value === 'string') {\n        if (value === 'auto') {\n          styleVal = 'auto';\n        } else if (value.match(/\\d+%/)) {\n          styleVal = value;\n        }\n      } else if (typeof value === 'number') {\n        styleVal = \"\".concat(value, \"px\");\n      }\n      Object.assign(style, (0, _defineProperty2[\"default\"])({}, name, styleVal));\n    }\n  }, {\n    key: \"getStyle\",\n    value: function getStyle() {\n      var _this$props = this.props,\n        fluid = _this$props.fluid,\n        propsAspectRatio = _this$props.aspectRatio,\n        propsHeight = _this$props.height,\n        propsWidth = _this$props.width;\n      var _this$manager$getStat = this.manager.getState(),\n        player = _this$manager$getStat.player;\n      var style = {};\n      var width;\n      var height;\n      var aspectRatio; // The aspect ratio is either used directly or to calculate width and height.\n\n      if (propsAspectRatio !== undefined && propsAspectRatio !== 'auto') {\n        // Use any aspectRatio that's been specifically set\n        aspectRatio = propsAspectRatio;\n      } else if (player.videoWidth) {\n        // Otherwise try to get the aspect ratio from the video metadata\n        aspectRatio = \"\".concat(player.videoWidth, \":\").concat(player.videoHeight);\n      } else {\n        // Or use a default. The video element's is 2:1, but 16:9 is more common.\n        aspectRatio = '16:9';\n      } // Get the ratio as a decimal we can use to calculate dimensions\n\n      var ratioParts = aspectRatio.split(':');\n      var ratioMultiplier = ratioParts[1] / ratioParts[0];\n      if (propsWidth !== undefined) {\n        // Use any width that's been specifically set\n        width = propsWidth;\n      } else if (propsHeight !== undefined) {\n        // Or calulate the width from the aspect ratio if a height has been set\n        width = propsHeight / ratioMultiplier;\n      } else {\n        // Or use the video's metadata, or use the video el's default of 300\n        width = player.videoWidth || 400;\n      }\n      if (propsHeight !== undefined) {\n        // Use any height that's been specifically set\n        height = propsHeight;\n      } else {\n        // Otherwise calculate the height from the ratio and the width\n        height = width * ratioMultiplier;\n      }\n      if (fluid) {\n        style.paddingTop = \"\".concat(ratioMultiplier * 100, \"%\");\n      } else {\n        // If Width contains \"auto\", set \"auto\" in style\n        this.setWidthOrHeight(style, 'width', width);\n        this.setWidthOrHeight(style, 'height', height);\n      }\n      return style;\n    } // get redux state\n    // { player, operation }\n  }, {\n    key: \"getState\",\n    value: function getState() {\n      return this.manager.getState();\n    } // get playback rate\n  }, {\n    key: \"play\",\n    // play the video\n    value: function play() {\n      this.video.play();\n    } // pause the video\n  }, {\n    key: \"pause\",\n    value: function pause() {\n      this.video.pause();\n    } // Change the video source and re-load the video:\n  }, {\n    key: \"load\",\n    value: function load() {\n      this.video.load();\n    } // Add a new text track to the video\n  }, {\n    key: \"addTextTrack\",\n    value: function addTextTrack() {\n      var _this$video;\n      (_this$video = this.video).addTextTrack.apply(_this$video, arguments);\n    } // Check if your browser can play different types of video:\n  }, {\n    key: \"canPlayType\",\n    value: function canPlayType() {\n      var _this$video2;\n      (_this$video2 = this.video).canPlayType.apply(_this$video2, arguments);\n    } // seek video by time\n  }, {\n    key: \"seek\",\n    value: function seek(time) {\n      this.video.seek(time);\n    } // jump forward x seconds\n  }, {\n    key: \"forward\",\n    value: function forward(seconds) {\n      this.video.forward(seconds);\n    } // jump back x seconds\n  }, {\n    key: \"replay\",\n    value: function replay(seconds) {\n      this.video.replay(seconds);\n    } // enter or exist full screen\n  }, {\n    key: \"toggleFullscreen\",\n    value: function toggleFullscreen() {\n      this.video.toggleFullscreen();\n    } // subscribe to player state change\n  }, {\n    key: \"subscribeToStateChange\",\n    value: function subscribeToStateChange(listener) {\n      return this.manager.subscribeToPlayerStateChange(listener);\n    } // player resize\n  }, {\n    key: \"handleResize\",\n    value: function handleResize() {}\n  }, {\n    key: \"handleFullScreenChange\",\n    value: function handleFullScreenChange(event) {\n      if (event.target === this.manager.rootElement) {\n        this.actions.handleFullscreenChange(_fullscreen[\"default\"].isFullscreen);\n      }\n    }\n  }, {\n    key: \"handleMouseDown\",\n    value: function handleMouseDown() {\n      this.startControlsTimer();\n    }\n  }, {\n    key: \"handleMouseMove\",\n    value: function handleMouseMove() {\n      this.startControlsTimer();\n    }\n  }, {\n    key: \"handleKeyDown\",\n    value: function handleKeyDown() {\n      this.startControlsTimer();\n    }\n  }, {\n    key: \"startControlsTimer\",\n    value: function startControlsTimer() {\n      var _this3 = this;\n      var controlBarActiveTime = 3000;\n      _react[\"default\"].Children.forEach(this.props.children, function (element) {\n        if (!_react[\"default\"].isValidElement(element) || element.type !== _ControlBar[\"default\"]) {\n          return;\n        }\n        var autoHideTime = element.props.autoHideTime;\n        if (typeof autoHideTime === 'number') {\n          controlBarActiveTime = autoHideTime;\n        }\n      });\n      this.actions.userActivate(true);\n      clearTimeout(this.controlsHideTimer);\n      this.controlsHideTimer = setTimeout(function () {\n        _this3.actions.userActivate(false);\n      }, controlBarActiveTime);\n    }\n  }, {\n    key: \"handleStateChange\",\n    value: function handleStateChange(state, prevState) {\n      if (state.isFullscreen !== prevState.isFullscreen) {\n        this.handleResize(); // focus root when switching fullscreen mode to avoid confusion #276\n\n        (0, _dom.focusNode)(this.manager.rootElement);\n      }\n      this.forceUpdate(); // re-render\n    }\n  }, {\n    key: \"handleFocus\",\n    value: function handleFocus() {\n      this.actions.activate(true);\n    }\n  }, {\n    key: \"handleBlur\",\n    value: function handleBlur() {\n      this.actions.activate(false);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this4 = this;\n      var fluid = this.props.fluid;\n      var _this$manager$getStat2 = this.manager.getState(),\n        player = _this$manager$getStat2.player;\n      var paused = player.paused,\n        hasStarted = player.hasStarted,\n        waiting = player.waiting,\n        seeking = player.seeking,\n        isFullscreen = player.isFullscreen,\n        userActivity = player.userActivity;\n      var props = (0, _objectSpread2[\"default\"])({}, this.props, {\n        player: player,\n        actions: this.actions,\n        manager: this.manager,\n        store: this.manager.store,\n        video: this.video ? this.video.video : null\n      });\n      var children = this.getChildren(props);\n      return _react[\"default\"].createElement(\"div\", {\n        className: (0, _classnames[\"default\"])({\n          'video-react-controls-enabled': true,\n          'video-react-has-started': hasStarted,\n          'video-react-paused': paused,\n          'video-react-playing': !paused,\n          'video-react-waiting': waiting,\n          'video-react-seeking': seeking,\n          'video-react-fluid': fluid,\n          'video-react-fullscreen': isFullscreen,\n          'video-react-user-inactive': !userActivity,\n          'video-react-user-active': userActivity,\n          'video-react-workinghover': !browser.IS_IOS\n        }, 'video-react', this.props.className),\n        style: this.getStyle(),\n        ref: function ref(c) {\n          _this4.manager.rootElement = c;\n        },\n        role: \"region\",\n        onTouchStart: this.handleMouseDown,\n        onMouseDown: this.handleMouseDown,\n        onTouchMove: this.handleMouseMove,\n        onMouseMove: this.handleMouseMove,\n        onKeyDown: this.handleKeyDown,\n        onFocus: this.handleFocus,\n        onBlur: this.handleBlur,\n        tabIndex: \"-1\"\n      }, children);\n    }\n  }, {\n    key: \"playbackRate\",\n    get: function get() {\n      return this.video.playbackRate;\n    } // set playback rate\n    // speed of video\n    ,\n\n    set: function set(rate) {\n      this.video.playbackRate = rate;\n    }\n  }, {\n    key: \"muted\",\n    get: function get() {\n      return this.video.muted;\n    },\n    set: function set(val) {\n      this.video.muted = val;\n    }\n  }, {\n    key: \"volume\",\n    get: function get() {\n      return this.video.volume;\n    },\n    set: function set(val) {\n      this.video.volume = val;\n    } // video width\n  }, {\n    key: \"videoWidth\",\n    get: function get() {\n      return this.video.videoWidth;\n    } // video height\n  }, {\n    key: \"videoHeight\",\n    get: function get() {\n      return this.video.videoHeight;\n    }\n  }]);\n  return Player;\n}(_react.Component);\nexports[\"default\"] = Player;\nPlayer.contextTypes = {\n  store: _propTypes[\"default\"].object\n};\nPlayer.propTypes = propTypes;\nPlayer.defaultProps = defaultProps;\nPlayer.displayName = 'Player';", "map": {"version": 3, "names": ["_interopRequireWildcard", "require", "_interopRequireDefault", "Object", "defineProperty", "exports", "value", "_objectSpread2", "_defineProperty2", "_objectWithoutProperties2", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_assertThisInitialized2", "_inherits2", "_propTypes", "_react", "_classnames", "_Manager", "_BigPlayButton", "_LoadingSpinner", "_PosterImage", "_Video", "_Bezel", "_Shortcut", "_ControlBar", "browser", "_dom", "_utils", "_fullscreen", "propTypes", "children", "any", "width", "oneOfType", "string", "number", "height", "fluid", "bool", "muted", "playsInline", "aspectRatio", "className", "videoId", "startTime", "loop", "autoPlay", "src", "poster", "preload", "oneOf", "onLoadStart", "func", "onWaiting", "onCanPlay", "onCanPlayThrough", "onPlaying", "onEnded", "onSeeking", "onSeeked", "onPlay", "onPause", "onProgress", "onDurationChange", "onError", "onSuspend", "onAbort", "onEmptied", "onStalled", "onLoadedMetadata", "onLoadedData", "onTimeUpdate", "onRateChange", "onVolumeChange", "store", "object", "defaultProps", "Player", "_Component", "props", "_this", "call", "controlsHideTimer", "video", "manager", "actions", "getActions", "subscribeToPlayerStateChange", "handleStateChange", "bind", "getStyle", "handleResize", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "handleMouseMove", "throttle", "handleMouseDown", "startControlsTimer", "handleFullScreenChange", "handleKeyDown", "handleFocus", "handleBlur", "key", "componentDidMount", "window", "addEventListener", "componentWillUnmount", "removeEventListener", "clearTimeout", "getDefaultChildren", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_this2", "createElement", "ref", "c", "order", "_", "props<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Children", "toArray", "filter", "e", "isVideoChild", "defaultChildren", "mergeAndSortChildren", "setWidthOrHeight", "style", "name", "styleVal", "match", "concat", "assign", "_this$props", "propsAspectRatio", "propsHeight", "propsWidth", "_this$manager$getStat", "getState", "player", "undefined", "videoWidth", "videoHeight", "ratioParts", "split", "ratioMultiplier", "paddingTop", "play", "pause", "load", "addTextTrack", "_this$video", "apply", "arguments", "canPlayType", "_this$video2", "seek", "time", "forward", "seconds", "replay", "toggleFullscreen", "subscribeToStateChange", "listener", "event", "target", "rootElement", "handleFullscreenChange", "isFullscreen", "_this3", "controlBarActiveTime", "for<PERSON>ach", "element", "isValidElement", "type", "autoHideTime", "userActivate", "setTimeout", "state", "prevState", "focusNode", "forceUpdate", "activate", "render", "_this4", "_this$manager$getStat2", "paused", "hasStarted", "waiting", "seeking", "userActivity", "IS_IOS", "role", "onTouchStart", "onMouseDown", "onTouchMove", "onMouseMove", "onKeyDown", "onFocus", "onBlur", "tabIndex", "get", "playbackRate", "set", "rate", "val", "volume", "Component", "contextTypes", "displayName"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/video-react/lib/components/Player.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\");\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _objectSpread2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectSpread\"));\n\nvar _defineProperty2 = _interopRequireDefault(require(\"@babel/runtime/helpers/defineProperty\"));\n\nvar _objectWithoutProperties2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectWithoutProperties\"));\n\nvar _classCallCheck2 = _interopRequireDefault(require(\"@babel/runtime/helpers/classCallCheck\"));\n\nvar _createClass2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createClass\"));\n\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"@babel/runtime/helpers/possibleConstructorReturn\"));\n\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"@babel/runtime/helpers/getPrototypeOf\"));\n\nvar _assertThisInitialized2 = _interopRequireDefault(require(\"@babel/runtime/helpers/assertThisInitialized\"));\n\nvar _inherits2 = _interopRequireDefault(require(\"@babel/runtime/helpers/inherits\"));\n\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\n\nvar _react = _interopRequireWildcard(require(\"react\"));\n\nvar _classnames = _interopRequireDefault(require(\"classnames\"));\n\nvar _Manager = _interopRequireDefault(require(\"../Manager\"));\n\nvar _BigPlayButton = _interopRequireDefault(require(\"./BigPlayButton\"));\n\nvar _LoadingSpinner = _interopRequireDefault(require(\"./LoadingSpinner\"));\n\nvar _PosterImage = _interopRequireDefault(require(\"./PosterImage\"));\n\nvar _Video = _interopRequireDefault(require(\"./Video\"));\n\nvar _Bezel = _interopRequireDefault(require(\"./Bezel\"));\n\nvar _Shortcut = _interopRequireDefault(require(\"./Shortcut\"));\n\nvar _ControlBar = _interopRequireDefault(require(\"./control-bar/ControlBar\"));\n\nvar browser = _interopRequireWildcard(require(\"../utils/browser\"));\n\nvar _dom = require(\"../utils/dom\");\n\nvar _utils = require(\"../utils\");\n\nvar _fullscreen = _interopRequireDefault(require(\"../utils/fullscreen\"));\n\nvar propTypes = {\n  children: _propTypes[\"default\"].any,\n  width: _propTypes[\"default\"].oneOfType([_propTypes[\"default\"].string, _propTypes[\"default\"].number]),\n  height: _propTypes[\"default\"].oneOfType([_propTypes[\"default\"].string, _propTypes[\"default\"].number]),\n  fluid: _propTypes[\"default\"].bool,\n  muted: _propTypes[\"default\"].bool,\n  playsInline: _propTypes[\"default\"].bool,\n  aspectRatio: _propTypes[\"default\"].string,\n  className: _propTypes[\"default\"].string,\n  videoId: _propTypes[\"default\"].string,\n  startTime: _propTypes[\"default\"].number,\n  loop: _propTypes[\"default\"].bool,\n  autoPlay: _propTypes[\"default\"].bool,\n  src: _propTypes[\"default\"].string,\n  poster: _propTypes[\"default\"].string,\n  preload: _propTypes[\"default\"].oneOf(['auto', 'metadata', 'none']),\n  onLoadStart: _propTypes[\"default\"].func,\n  onWaiting: _propTypes[\"default\"].func,\n  onCanPlay: _propTypes[\"default\"].func,\n  onCanPlayThrough: _propTypes[\"default\"].func,\n  onPlaying: _propTypes[\"default\"].func,\n  onEnded: _propTypes[\"default\"].func,\n  onSeeking: _propTypes[\"default\"].func,\n  onSeeked: _propTypes[\"default\"].func,\n  onPlay: _propTypes[\"default\"].func,\n  onPause: _propTypes[\"default\"].func,\n  onProgress: _propTypes[\"default\"].func,\n  onDurationChange: _propTypes[\"default\"].func,\n  onError: _propTypes[\"default\"].func,\n  onSuspend: _propTypes[\"default\"].func,\n  onAbort: _propTypes[\"default\"].func,\n  onEmptied: _propTypes[\"default\"].func,\n  onStalled: _propTypes[\"default\"].func,\n  onLoadedMetadata: _propTypes[\"default\"].func,\n  onLoadedData: _propTypes[\"default\"].func,\n  onTimeUpdate: _propTypes[\"default\"].func,\n  onRateChange: _propTypes[\"default\"].func,\n  onVolumeChange: _propTypes[\"default\"].func,\n  store: _propTypes[\"default\"].object\n};\nvar defaultProps = {\n  fluid: true,\n  muted: false,\n  playsInline: false,\n  preload: 'auto',\n  aspectRatio: 'auto'\n};\n\nvar Player =\n/*#__PURE__*/\nfunction (_Component) {\n  (0, _inherits2[\"default\"])(Player, _Component);\n\n  function Player(props) {\n    var _this;\n\n    (0, _classCallCheck2[\"default\"])(this, Player);\n    _this = (0, _possibleConstructorReturn2[\"default\"])(this, (0, _getPrototypeOf2[\"default\"])(Player).call(this, props));\n    _this.controlsHideTimer = null;\n    _this.video = null; // the Video component\n\n    _this.manager = new _Manager[\"default\"](props.store);\n    _this.actions = _this.manager.getActions();\n\n    _this.manager.subscribeToPlayerStateChange(_this.handleStateChange.bind((0, _assertThisInitialized2[\"default\"])(_this)));\n\n    _this.getStyle = _this.getStyle.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleResize = _this.handleResize.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.getChildren = _this.getChildren.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleMouseMove = (0, _utils.throttle)(_this.handleMouseMove.bind((0, _assertThisInitialized2[\"default\"])(_this)), 250);\n    _this.handleMouseDown = _this.handleMouseDown.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.startControlsTimer = _this.startControlsTimer.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleFullScreenChange = _this.handleFullScreenChange.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleKeyDown = _this.handleKeyDown.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleFocus = _this.handleFocus.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleBlur = _this.handleBlur.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    return _this;\n  }\n\n  (0, _createClass2[\"default\"])(Player, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      this.handleResize();\n      window.addEventListener('resize', this.handleResize);\n\n      _fullscreen[\"default\"].addEventListener(this.handleFullScreenChange);\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      // Remove event listener\n      window.removeEventListener('resize', this.handleResize);\n\n      _fullscreen[\"default\"].removeEventListener(this.handleFullScreenChange);\n\n      if (this.controlsHideTimer) {\n        window.clearTimeout(this.controlsHideTimer);\n      }\n    }\n  }, {\n    key: \"getDefaultChildren\",\n    value: function getDefaultChildren(originalChildren) {\n      var _this2 = this;\n\n      return [_react[\"default\"].createElement(_Video[\"default\"], {\n        ref: function ref(c) {\n          _this2.video = c;\n          _this2.manager.video = _this2.video;\n        },\n        key: \"video\",\n        order: 0.0\n      }, originalChildren), _react[\"default\"].createElement(_PosterImage[\"default\"], {\n        key: \"poster-image\",\n        order: 1.0\n      }), _react[\"default\"].createElement(_LoadingSpinner[\"default\"], {\n        key: \"loading-spinner\",\n        order: 2.0\n      }), _react[\"default\"].createElement(_Bezel[\"default\"], {\n        key: \"bezel\",\n        order: 3.0\n      }), _react[\"default\"].createElement(_BigPlayButton[\"default\"], {\n        key: \"big-play-button\",\n        order: 4.0\n      }), _react[\"default\"].createElement(_ControlBar[\"default\"], {\n        key: \"control-bar\",\n        order: 5.0\n      }), _react[\"default\"].createElement(_Shortcut[\"default\"], {\n        key: \"shortcut\",\n        order: 99.0\n      })];\n    }\n  }, {\n    key: \"getChildren\",\n    value: function getChildren(props) {\n      var _ = props.className,\n          originalChildren = props.children,\n          propsWithoutChildren = (0, _objectWithoutProperties2[\"default\"])(props, [\"className\", \"children\"]);\n\n      var children = _react[\"default\"].Children.toArray(this.props.children).filter(function (e) {\n        return !(0, _utils.isVideoChild)(e);\n      });\n\n      var defaultChildren = this.getDefaultChildren(originalChildren);\n      return (0, _utils.mergeAndSortChildren)(defaultChildren, children, propsWithoutChildren);\n    }\n  }, {\n    key: \"setWidthOrHeight\",\n    value: function setWidthOrHeight(style, name, value) {\n      var styleVal;\n\n      if (typeof value === 'string') {\n        if (value === 'auto') {\n          styleVal = 'auto';\n        } else if (value.match(/\\d+%/)) {\n          styleVal = value;\n        }\n      } else if (typeof value === 'number') {\n        styleVal = \"\".concat(value, \"px\");\n      }\n\n      Object.assign(style, (0, _defineProperty2[\"default\"])({}, name, styleVal));\n    }\n  }, {\n    key: \"getStyle\",\n    value: function getStyle() {\n      var _this$props = this.props,\n          fluid = _this$props.fluid,\n          propsAspectRatio = _this$props.aspectRatio,\n          propsHeight = _this$props.height,\n          propsWidth = _this$props.width;\n\n      var _this$manager$getStat = this.manager.getState(),\n          player = _this$manager$getStat.player;\n\n      var style = {};\n      var width;\n      var height;\n      var aspectRatio; // The aspect ratio is either used directly or to calculate width and height.\n\n      if (propsAspectRatio !== undefined && propsAspectRatio !== 'auto') {\n        // Use any aspectRatio that's been specifically set\n        aspectRatio = propsAspectRatio;\n      } else if (player.videoWidth) {\n        // Otherwise try to get the aspect ratio from the video metadata\n        aspectRatio = \"\".concat(player.videoWidth, \":\").concat(player.videoHeight);\n      } else {\n        // Or use a default. The video element's is 2:1, but 16:9 is more common.\n        aspectRatio = '16:9';\n      } // Get the ratio as a decimal we can use to calculate dimensions\n\n\n      var ratioParts = aspectRatio.split(':');\n      var ratioMultiplier = ratioParts[1] / ratioParts[0];\n\n      if (propsWidth !== undefined) {\n        // Use any width that's been specifically set\n        width = propsWidth;\n      } else if (propsHeight !== undefined) {\n        // Or calulate the width from the aspect ratio if a height has been set\n        width = propsHeight / ratioMultiplier;\n      } else {\n        // Or use the video's metadata, or use the video el's default of 300\n        width = player.videoWidth || 400;\n      }\n\n      if (propsHeight !== undefined) {\n        // Use any height that's been specifically set\n        height = propsHeight;\n      } else {\n        // Otherwise calculate the height from the ratio and the width\n        height = width * ratioMultiplier;\n      }\n\n      if (fluid) {\n        style.paddingTop = \"\".concat(ratioMultiplier * 100, \"%\");\n      } else {\n        // If Width contains \"auto\", set \"auto\" in style\n        this.setWidthOrHeight(style, 'width', width);\n        this.setWidthOrHeight(style, 'height', height);\n      }\n\n      return style;\n    } // get redux state\n    // { player, operation }\n\n  }, {\n    key: \"getState\",\n    value: function getState() {\n      return this.manager.getState();\n    } // get playback rate\n\n  }, {\n    key: \"play\",\n    // play the video\n    value: function play() {\n      this.video.play();\n    } // pause the video\n\n  }, {\n    key: \"pause\",\n    value: function pause() {\n      this.video.pause();\n    } // Change the video source and re-load the video:\n\n  }, {\n    key: \"load\",\n    value: function load() {\n      this.video.load();\n    } // Add a new text track to the video\n\n  }, {\n    key: \"addTextTrack\",\n    value: function addTextTrack() {\n      var _this$video;\n\n      (_this$video = this.video).addTextTrack.apply(_this$video, arguments);\n    } // Check if your browser can play different types of video:\n\n  }, {\n    key: \"canPlayType\",\n    value: function canPlayType() {\n      var _this$video2;\n\n      (_this$video2 = this.video).canPlayType.apply(_this$video2, arguments);\n    } // seek video by time\n\n  }, {\n    key: \"seek\",\n    value: function seek(time) {\n      this.video.seek(time);\n    } // jump forward x seconds\n\n  }, {\n    key: \"forward\",\n    value: function forward(seconds) {\n      this.video.forward(seconds);\n    } // jump back x seconds\n\n  }, {\n    key: \"replay\",\n    value: function replay(seconds) {\n      this.video.replay(seconds);\n    } // enter or exist full screen\n\n  }, {\n    key: \"toggleFullscreen\",\n    value: function toggleFullscreen() {\n      this.video.toggleFullscreen();\n    } // subscribe to player state change\n\n  }, {\n    key: \"subscribeToStateChange\",\n    value: function subscribeToStateChange(listener) {\n      return this.manager.subscribeToPlayerStateChange(listener);\n    } // player resize\n\n  }, {\n    key: \"handleResize\",\n    value: function handleResize() {}\n  }, {\n    key: \"handleFullScreenChange\",\n    value: function handleFullScreenChange(event) {\n      if (event.target === this.manager.rootElement) {\n        this.actions.handleFullscreenChange(_fullscreen[\"default\"].isFullscreen);\n      }\n    }\n  }, {\n    key: \"handleMouseDown\",\n    value: function handleMouseDown() {\n      this.startControlsTimer();\n    }\n  }, {\n    key: \"handleMouseMove\",\n    value: function handleMouseMove() {\n      this.startControlsTimer();\n    }\n  }, {\n    key: \"handleKeyDown\",\n    value: function handleKeyDown() {\n      this.startControlsTimer();\n    }\n  }, {\n    key: \"startControlsTimer\",\n    value: function startControlsTimer() {\n      var _this3 = this;\n\n      var controlBarActiveTime = 3000;\n\n      _react[\"default\"].Children.forEach(this.props.children, function (element) {\n        if (!_react[\"default\"].isValidElement(element) || element.type !== _ControlBar[\"default\"]) {\n          return;\n        }\n\n        var autoHideTime = element.props.autoHideTime;\n\n        if (typeof autoHideTime === 'number') {\n          controlBarActiveTime = autoHideTime;\n        }\n      });\n\n      this.actions.userActivate(true);\n      clearTimeout(this.controlsHideTimer);\n      this.controlsHideTimer = setTimeout(function () {\n        _this3.actions.userActivate(false);\n      }, controlBarActiveTime);\n    }\n  }, {\n    key: \"handleStateChange\",\n    value: function handleStateChange(state, prevState) {\n      if (state.isFullscreen !== prevState.isFullscreen) {\n        this.handleResize(); // focus root when switching fullscreen mode to avoid confusion #276\n\n        (0, _dom.focusNode)(this.manager.rootElement);\n      }\n\n      this.forceUpdate(); // re-render\n    }\n  }, {\n    key: \"handleFocus\",\n    value: function handleFocus() {\n      this.actions.activate(true);\n    }\n  }, {\n    key: \"handleBlur\",\n    value: function handleBlur() {\n      this.actions.activate(false);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this4 = this;\n\n      var fluid = this.props.fluid;\n\n      var _this$manager$getStat2 = this.manager.getState(),\n          player = _this$manager$getStat2.player;\n\n      var paused = player.paused,\n          hasStarted = player.hasStarted,\n          waiting = player.waiting,\n          seeking = player.seeking,\n          isFullscreen = player.isFullscreen,\n          userActivity = player.userActivity;\n      var props = (0, _objectSpread2[\"default\"])({}, this.props, {\n        player: player,\n        actions: this.actions,\n        manager: this.manager,\n        store: this.manager.store,\n        video: this.video ? this.video.video : null\n      });\n      var children = this.getChildren(props);\n      return _react[\"default\"].createElement(\"div\", {\n        className: (0, _classnames[\"default\"])({\n          'video-react-controls-enabled': true,\n          'video-react-has-started': hasStarted,\n          'video-react-paused': paused,\n          'video-react-playing': !paused,\n          'video-react-waiting': waiting,\n          'video-react-seeking': seeking,\n          'video-react-fluid': fluid,\n          'video-react-fullscreen': isFullscreen,\n          'video-react-user-inactive': !userActivity,\n          'video-react-user-active': userActivity,\n          'video-react-workinghover': !browser.IS_IOS\n        }, 'video-react', this.props.className),\n        style: this.getStyle(),\n        ref: function ref(c) {\n          _this4.manager.rootElement = c;\n        },\n        role: \"region\",\n        onTouchStart: this.handleMouseDown,\n        onMouseDown: this.handleMouseDown,\n        onTouchMove: this.handleMouseMove,\n        onMouseMove: this.handleMouseMove,\n        onKeyDown: this.handleKeyDown,\n        onFocus: this.handleFocus,\n        onBlur: this.handleBlur,\n        tabIndex: \"-1\"\n      }, children);\n    }\n  }, {\n    key: \"playbackRate\",\n    get: function get() {\n      return this.video.playbackRate;\n    } // set playback rate\n    // speed of video\n    ,\n    set: function set(rate) {\n      this.video.playbackRate = rate;\n    }\n  }, {\n    key: \"muted\",\n    get: function get() {\n      return this.video.muted;\n    },\n    set: function set(val) {\n      this.video.muted = val;\n    }\n  }, {\n    key: \"volume\",\n    get: function get() {\n      return this.video.volume;\n    },\n    set: function set(val) {\n      this.video.volume = val;\n    } // video width\n\n  }, {\n    key: \"videoWidth\",\n    get: function get() {\n      return this.video.videoWidth;\n    } // video height\n\n  }, {\n    key: \"videoHeight\",\n    get: function get() {\n      return this.video.videoHeight;\n    }\n  }]);\n  return Player;\n}(_react.Component);\n\nexports[\"default\"] = Player;\nPlayer.contextTypes = {\n  store: _propTypes[\"default\"].object\n};\nPlayer.propTypes = propTypes;\nPlayer.defaultProps = defaultProps;\nPlayer.displayName = 'Player';"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,uBAAuB,GAAGC,OAAO,CAAC,+CAA+C,CAAC;AAEtF,IAAIC,sBAAsB,GAAGD,OAAO,CAAC,8CAA8C,CAAC;AAEpFE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;AAE3B,IAAIE,cAAc,GAAGL,sBAAsB,CAACD,OAAO,CAAC,qCAAqC,CAAC,CAAC;AAE3F,IAAIO,gBAAgB,GAAGN,sBAAsB,CAACD,OAAO,CAAC,uCAAuC,CAAC,CAAC;AAE/F,IAAIQ,yBAAyB,GAAGP,sBAAsB,CAACD,OAAO,CAAC,gDAAgD,CAAC,CAAC;AAEjH,IAAIS,gBAAgB,GAAGR,sBAAsB,CAACD,OAAO,CAAC,uCAAuC,CAAC,CAAC;AAE/F,IAAIU,aAAa,GAAGT,sBAAsB,CAACD,OAAO,CAAC,oCAAoC,CAAC,CAAC;AAEzF,IAAIW,2BAA2B,GAAGV,sBAAsB,CAACD,OAAO,CAAC,kDAAkD,CAAC,CAAC;AAErH,IAAIY,gBAAgB,GAAGX,sBAAsB,CAACD,OAAO,CAAC,uCAAuC,CAAC,CAAC;AAE/F,IAAIa,uBAAuB,GAAGZ,sBAAsB,CAACD,OAAO,CAAC,8CAA8C,CAAC,CAAC;AAE7G,IAAIc,UAAU,GAAGb,sBAAsB,CAACD,OAAO,CAAC,iCAAiC,CAAC,CAAC;AAEnF,IAAIe,UAAU,GAAGd,sBAAsB,CAACD,OAAO,CAAC,YAAY,CAAC,CAAC;AAE9D,IAAIgB,MAAM,GAAGjB,uBAAuB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AAEtD,IAAIiB,WAAW,GAAGhB,sBAAsB,CAACD,OAAO,CAAC,YAAY,CAAC,CAAC;AAE/D,IAAIkB,QAAQ,GAAGjB,sBAAsB,CAACD,OAAO,CAAC,YAAY,CAAC,CAAC;AAE5D,IAAImB,cAAc,GAAGlB,sBAAsB,CAACD,OAAO,CAAC,iBAAiB,CAAC,CAAC;AAEvE,IAAIoB,eAAe,GAAGnB,sBAAsB,CAACD,OAAO,CAAC,kBAAkB,CAAC,CAAC;AAEzE,IAAIqB,YAAY,GAAGpB,sBAAsB,CAACD,OAAO,CAAC,eAAe,CAAC,CAAC;AAEnE,IAAIsB,MAAM,GAAGrB,sBAAsB,CAACD,OAAO,CAAC,SAAS,CAAC,CAAC;AAEvD,IAAIuB,MAAM,GAAGtB,sBAAsB,CAACD,OAAO,CAAC,SAAS,CAAC,CAAC;AAEvD,IAAIwB,SAAS,GAAGvB,sBAAsB,CAACD,OAAO,CAAC,YAAY,CAAC,CAAC;AAE7D,IAAIyB,WAAW,GAAGxB,sBAAsB,CAACD,OAAO,CAAC,0BAA0B,CAAC,CAAC;AAE7E,IAAI0B,OAAO,GAAG3B,uBAAuB,CAACC,OAAO,CAAC,kBAAkB,CAAC,CAAC;AAElE,IAAI2B,IAAI,GAAG3B,OAAO,CAAC,cAAc,CAAC;AAElC,IAAI4B,MAAM,GAAG5B,OAAO,CAAC,UAAU,CAAC;AAEhC,IAAI6B,WAAW,GAAG5B,sBAAsB,CAACD,OAAO,CAAC,qBAAqB,CAAC,CAAC;AAExE,IAAI8B,SAAS,GAAG;EACdC,QAAQ,EAAEhB,UAAU,CAAC,SAAS,CAAC,CAACiB,GAAG;EACnCC,KAAK,EAAElB,UAAU,CAAC,SAAS,CAAC,CAACmB,SAAS,CAAC,CAACnB,UAAU,CAAC,SAAS,CAAC,CAACoB,MAAM,EAAEpB,UAAU,CAAC,SAAS,CAAC,CAACqB,MAAM,CAAC,CAAC;EACpGC,MAAM,EAAEtB,UAAU,CAAC,SAAS,CAAC,CAACmB,SAAS,CAAC,CAACnB,UAAU,CAAC,SAAS,CAAC,CAACoB,MAAM,EAAEpB,UAAU,CAAC,SAAS,CAAC,CAACqB,MAAM,CAAC,CAAC;EACrGE,KAAK,EAAEvB,UAAU,CAAC,SAAS,CAAC,CAACwB,IAAI;EACjCC,KAAK,EAAEzB,UAAU,CAAC,SAAS,CAAC,CAACwB,IAAI;EACjCE,WAAW,EAAE1B,UAAU,CAAC,SAAS,CAAC,CAACwB,IAAI;EACvCG,WAAW,EAAE3B,UAAU,CAAC,SAAS,CAAC,CAACoB,MAAM;EACzCQ,SAAS,EAAE5B,UAAU,CAAC,SAAS,CAAC,CAACoB,MAAM;EACvCS,OAAO,EAAE7B,UAAU,CAAC,SAAS,CAAC,CAACoB,MAAM;EACrCU,SAAS,EAAE9B,UAAU,CAAC,SAAS,CAAC,CAACqB,MAAM;EACvCU,IAAI,EAAE/B,UAAU,CAAC,SAAS,CAAC,CAACwB,IAAI;EAChCQ,QAAQ,EAAEhC,UAAU,CAAC,SAAS,CAAC,CAACwB,IAAI;EACpCS,GAAG,EAAEjC,UAAU,CAAC,SAAS,CAAC,CAACoB,MAAM;EACjCc,MAAM,EAAElC,UAAU,CAAC,SAAS,CAAC,CAACoB,MAAM;EACpCe,OAAO,EAAEnC,UAAU,CAAC,SAAS,CAAC,CAACoC,KAAK,CAAC,CAAC,MAAM,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;EAClEC,WAAW,EAAErC,UAAU,CAAC,SAAS,CAAC,CAACsC,IAAI;EACvCC,SAAS,EAAEvC,UAAU,CAAC,SAAS,CAAC,CAACsC,IAAI;EACrCE,SAAS,EAAExC,UAAU,CAAC,SAAS,CAAC,CAACsC,IAAI;EACrCG,gBAAgB,EAAEzC,UAAU,CAAC,SAAS,CAAC,CAACsC,IAAI;EAC5CI,SAAS,EAAE1C,UAAU,CAAC,SAAS,CAAC,CAACsC,IAAI;EACrCK,OAAO,EAAE3C,UAAU,CAAC,SAAS,CAAC,CAACsC,IAAI;EACnCM,SAAS,EAAE5C,UAAU,CAAC,SAAS,CAAC,CAACsC,IAAI;EACrCO,QAAQ,EAAE7C,UAAU,CAAC,SAAS,CAAC,CAACsC,IAAI;EACpCQ,MAAM,EAAE9C,UAAU,CAAC,SAAS,CAAC,CAACsC,IAAI;EAClCS,OAAO,EAAE/C,UAAU,CAAC,SAAS,CAAC,CAACsC,IAAI;EACnCU,UAAU,EAAEhD,UAAU,CAAC,SAAS,CAAC,CAACsC,IAAI;EACtCW,gBAAgB,EAAEjD,UAAU,CAAC,SAAS,CAAC,CAACsC,IAAI;EAC5CY,OAAO,EAAElD,UAAU,CAAC,SAAS,CAAC,CAACsC,IAAI;EACnCa,SAAS,EAAEnD,UAAU,CAAC,SAAS,CAAC,CAACsC,IAAI;EACrCc,OAAO,EAAEpD,UAAU,CAAC,SAAS,CAAC,CAACsC,IAAI;EACnCe,SAAS,EAAErD,UAAU,CAAC,SAAS,CAAC,CAACsC,IAAI;EACrCgB,SAAS,EAAEtD,UAAU,CAAC,SAAS,CAAC,CAACsC,IAAI;EACrCiB,gBAAgB,EAAEvD,UAAU,CAAC,SAAS,CAAC,CAACsC,IAAI;EAC5CkB,YAAY,EAAExD,UAAU,CAAC,SAAS,CAAC,CAACsC,IAAI;EACxCmB,YAAY,EAAEzD,UAAU,CAAC,SAAS,CAAC,CAACsC,IAAI;EACxCoB,YAAY,EAAE1D,UAAU,CAAC,SAAS,CAAC,CAACsC,IAAI;EACxCqB,cAAc,EAAE3D,UAAU,CAAC,SAAS,CAAC,CAACsC,IAAI;EAC1CsB,KAAK,EAAE5D,UAAU,CAAC,SAAS,CAAC,CAAC6D;AAC/B,CAAC;AACD,IAAIC,YAAY,GAAG;EACjBvC,KAAK,EAAE,IAAI;EACXE,KAAK,EAAE,KAAK;EACZC,WAAW,EAAE,KAAK;EAClBS,OAAO,EAAE,MAAM;EACfR,WAAW,EAAE;AACf,CAAC;AAED,IAAIoC,MAAM,GACV;AACA,UAAUC,UAAU,EAAE;EACpB,CAAC,CAAC,EAAEjE,UAAU,CAAC,SAAS,CAAC,EAAEgE,MAAM,EAAEC,UAAU,CAAC;EAE9C,SAASD,MAAMA,CAACE,KAAK,EAAE;IACrB,IAAIC,KAAK;IAET,CAAC,CAAC,EAAExE,gBAAgB,CAAC,SAAS,CAAC,EAAE,IAAI,EAAEqE,MAAM,CAAC;IAC9CG,KAAK,GAAG,CAAC,CAAC,EAAEtE,2BAA2B,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAEC,gBAAgB,CAAC,SAAS,CAAC,EAAEkE,MAAM,CAAC,CAACI,IAAI,CAAC,IAAI,EAAEF,KAAK,CAAC,CAAC;IACrHC,KAAK,CAACE,iBAAiB,GAAG,IAAI;IAC9BF,KAAK,CAACG,KAAK,GAAG,IAAI,CAAC,CAAC;;IAEpBH,KAAK,CAACI,OAAO,GAAG,IAAInE,QAAQ,CAAC,SAAS,CAAC,CAAC8D,KAAK,CAACL,KAAK,CAAC;IACpDM,KAAK,CAACK,OAAO,GAAGL,KAAK,CAACI,OAAO,CAACE,UAAU,EAAE;IAE1CN,KAAK,CAACI,OAAO,CAACG,4BAA4B,CAACP,KAAK,CAACQ,iBAAiB,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE7E,uBAAuB,CAAC,SAAS,CAAC,EAAEoE,KAAK,CAAC,CAAC,CAAC;IAExHA,KAAK,CAACU,QAAQ,GAAGV,KAAK,CAACU,QAAQ,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE7E,uBAAuB,CAAC,SAAS,CAAC,EAAEoE,KAAK,CAAC,CAAC;IACpFA,KAAK,CAACW,YAAY,GAAGX,KAAK,CAACW,YAAY,CAACF,IAAI,CAAC,CAAC,CAAC,EAAE7E,uBAAuB,CAAC,SAAS,CAAC,EAAEoE,KAAK,CAAC,CAAC;IAC5FA,KAAK,CAACY,WAAW,GAAGZ,KAAK,CAACY,WAAW,CAACH,IAAI,CAAC,CAAC,CAAC,EAAE7E,uBAAuB,CAAC,SAAS,CAAC,EAAEoE,KAAK,CAAC,CAAC;IAC1FA,KAAK,CAACa,eAAe,GAAG,CAAC,CAAC,EAAElE,MAAM,CAACmE,QAAQ,EAAEd,KAAK,CAACa,eAAe,CAACJ,IAAI,CAAC,CAAC,CAAC,EAAE7E,uBAAuB,CAAC,SAAS,CAAC,EAAEoE,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC;IAC7HA,KAAK,CAACe,eAAe,GAAGf,KAAK,CAACe,eAAe,CAACN,IAAI,CAAC,CAAC,CAAC,EAAE7E,uBAAuB,CAAC,SAAS,CAAC,EAAEoE,KAAK,CAAC,CAAC;IAClGA,KAAK,CAACgB,kBAAkB,GAAGhB,KAAK,CAACgB,kBAAkB,CAACP,IAAI,CAAC,CAAC,CAAC,EAAE7E,uBAAuB,CAAC,SAAS,CAAC,EAAEoE,KAAK,CAAC,CAAC;IACxGA,KAAK,CAACiB,sBAAsB,GAAGjB,KAAK,CAACiB,sBAAsB,CAACR,IAAI,CAAC,CAAC,CAAC,EAAE7E,uBAAuB,CAAC,SAAS,CAAC,EAAEoE,KAAK,CAAC,CAAC;IAChHA,KAAK,CAACkB,aAAa,GAAGlB,KAAK,CAACkB,aAAa,CAACT,IAAI,CAAC,CAAC,CAAC,EAAE7E,uBAAuB,CAAC,SAAS,CAAC,EAAEoE,KAAK,CAAC,CAAC;IAC9FA,KAAK,CAACmB,WAAW,GAAGnB,KAAK,CAACmB,WAAW,CAACV,IAAI,CAAC,CAAC,CAAC,EAAE7E,uBAAuB,CAAC,SAAS,CAAC,EAAEoE,KAAK,CAAC,CAAC;IAC1FA,KAAK,CAACoB,UAAU,GAAGpB,KAAK,CAACoB,UAAU,CAACX,IAAI,CAAC,CAAC,CAAC,EAAE7E,uBAAuB,CAAC,SAAS,CAAC,EAAEoE,KAAK,CAAC,CAAC;IACxF,OAAOA,KAAK;EACd;EAEA,CAAC,CAAC,EAAEvE,aAAa,CAAC,SAAS,CAAC,EAAEoE,MAAM,EAAE,CAAC;IACrCwB,GAAG,EAAE,mBAAmB;IACxBjG,KAAK,EAAE,SAASkG,iBAAiBA,CAAA,EAAG;MAClC,IAAI,CAACX,YAAY,EAAE;MACnBY,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACb,YAAY,CAAC;MAEpD/D,WAAW,CAAC,SAAS,CAAC,CAAC4E,gBAAgB,CAAC,IAAI,CAACP,sBAAsB,CAAC;IACtE;EACF,CAAC,EAAE;IACDI,GAAG,EAAE,sBAAsB;IAC3BjG,KAAK,EAAE,SAASqG,oBAAoBA,CAAA,EAAG;MACrC;MACAF,MAAM,CAACG,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACf,YAAY,CAAC;MAEvD/D,WAAW,CAAC,SAAS,CAAC,CAAC8E,mBAAmB,CAAC,IAAI,CAACT,sBAAsB,CAAC;MAEvE,IAAI,IAAI,CAACf,iBAAiB,EAAE;QAC1BqB,MAAM,CAACI,YAAY,CAAC,IAAI,CAACzB,iBAAiB,CAAC;MAC7C;IACF;EACF,CAAC,EAAE;IACDmB,GAAG,EAAE,oBAAoB;IACzBjG,KAAK,EAAE,SAASwG,kBAAkBA,CAACC,gBAAgB,EAAE;MACnD,IAAIC,MAAM,GAAG,IAAI;MAEjB,OAAO,CAAC/F,MAAM,CAAC,SAAS,CAAC,CAACgG,aAAa,CAAC1F,MAAM,CAAC,SAAS,CAAC,EAAE;QACzD2F,GAAG,EAAE,SAASA,GAAGA,CAACC,CAAC,EAAE;UACnBH,MAAM,CAAC3B,KAAK,GAAG8B,CAAC;UAChBH,MAAM,CAAC1B,OAAO,CAACD,KAAK,GAAG2B,MAAM,CAAC3B,KAAK;QACrC,CAAC;QACDkB,GAAG,EAAE,OAAO;QACZa,KAAK,EAAE;MACT,CAAC,EAAEL,gBAAgB,CAAC,EAAE9F,MAAM,CAAC,SAAS,CAAC,CAACgG,aAAa,CAAC3F,YAAY,CAAC,SAAS,CAAC,EAAE;QAC7EiF,GAAG,EAAE,cAAc;QACnBa,KAAK,EAAE;MACT,CAAC,CAAC,EAAEnG,MAAM,CAAC,SAAS,CAAC,CAACgG,aAAa,CAAC5F,eAAe,CAAC,SAAS,CAAC,EAAE;QAC9DkF,GAAG,EAAE,iBAAiB;QACtBa,KAAK,EAAE;MACT,CAAC,CAAC,EAAEnG,MAAM,CAAC,SAAS,CAAC,CAACgG,aAAa,CAACzF,MAAM,CAAC,SAAS,CAAC,EAAE;QACrD+E,GAAG,EAAE,OAAO;QACZa,KAAK,EAAE;MACT,CAAC,CAAC,EAAEnG,MAAM,CAAC,SAAS,CAAC,CAACgG,aAAa,CAAC7F,cAAc,CAAC,SAAS,CAAC,EAAE;QAC7DmF,GAAG,EAAE,iBAAiB;QACtBa,KAAK,EAAE;MACT,CAAC,CAAC,EAAEnG,MAAM,CAAC,SAAS,CAAC,CAACgG,aAAa,CAACvF,WAAW,CAAC,SAAS,CAAC,EAAE;QAC1D6E,GAAG,EAAE,aAAa;QAClBa,KAAK,EAAE;MACT,CAAC,CAAC,EAAEnG,MAAM,CAAC,SAAS,CAAC,CAACgG,aAAa,CAACxF,SAAS,CAAC,SAAS,CAAC,EAAE;QACxD8E,GAAG,EAAE,UAAU;QACfa,KAAK,EAAE;MACT,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE;IACDb,GAAG,EAAE,aAAa;IAClBjG,KAAK,EAAE,SAASwF,WAAWA,CAACb,KAAK,EAAE;MACjC,IAAIoC,CAAC,GAAGpC,KAAK,CAACrC,SAAS;QACnBmE,gBAAgB,GAAG9B,KAAK,CAACjD,QAAQ;QACjCsF,oBAAoB,GAAG,CAAC,CAAC,EAAE7G,yBAAyB,CAAC,SAAS,CAAC,EAAEwE,KAAK,EAAE,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;MAEtG,IAAIjD,QAAQ,GAAGf,MAAM,CAAC,SAAS,CAAC,CAACsG,QAAQ,CAACC,OAAO,CAAC,IAAI,CAACvC,KAAK,CAACjD,QAAQ,CAAC,CAACyF,MAAM,CAAC,UAAUC,CAAC,EAAE;QACzF,OAAO,CAAC,CAAC,CAAC,EAAE7F,MAAM,CAAC8F,YAAY,EAAED,CAAC,CAAC;MACrC,CAAC,CAAC;MAEF,IAAIE,eAAe,GAAG,IAAI,CAACd,kBAAkB,CAACC,gBAAgB,CAAC;MAC/D,OAAO,CAAC,CAAC,EAAElF,MAAM,CAACgG,oBAAoB,EAAED,eAAe,EAAE5F,QAAQ,EAAEsF,oBAAoB,CAAC;IAC1F;EACF,CAAC,EAAE;IACDf,GAAG,EAAE,kBAAkB;IACvBjG,KAAK,EAAE,SAASwH,gBAAgBA,CAACC,KAAK,EAAEC,IAAI,EAAE1H,KAAK,EAAE;MACnD,IAAI2H,QAAQ;MAEZ,IAAI,OAAO3H,KAAK,KAAK,QAAQ,EAAE;QAC7B,IAAIA,KAAK,KAAK,MAAM,EAAE;UACpB2H,QAAQ,GAAG,MAAM;QACnB,CAAC,MAAM,IAAI3H,KAAK,CAAC4H,KAAK,CAAC,MAAM,CAAC,EAAE;UAC9BD,QAAQ,GAAG3H,KAAK;QAClB;MACF,CAAC,MAAM,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QACpC2H,QAAQ,GAAG,EAAE,CAACE,MAAM,CAAC7H,KAAK,EAAE,IAAI,CAAC;MACnC;MAEAH,MAAM,CAACiI,MAAM,CAACL,KAAK,EAAE,CAAC,CAAC,EAAEvH,gBAAgB,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAEwH,IAAI,EAAEC,QAAQ,CAAC,CAAC;IAC5E;EACF,CAAC,EAAE;IACD1B,GAAG,EAAE,UAAU;IACfjG,KAAK,EAAE,SAASsF,QAAQA,CAAA,EAAG;MACzB,IAAIyC,WAAW,GAAG,IAAI,CAACpD,KAAK;QACxB1C,KAAK,GAAG8F,WAAW,CAAC9F,KAAK;QACzB+F,gBAAgB,GAAGD,WAAW,CAAC1F,WAAW;QAC1C4F,WAAW,GAAGF,WAAW,CAAC/F,MAAM;QAChCkG,UAAU,GAAGH,WAAW,CAACnG,KAAK;MAElC,IAAIuG,qBAAqB,GAAG,IAAI,CAACnD,OAAO,CAACoD,QAAQ,EAAE;QAC/CC,MAAM,GAAGF,qBAAqB,CAACE,MAAM;MAEzC,IAAIZ,KAAK,GAAG,CAAC,CAAC;MACd,IAAI7F,KAAK;MACT,IAAII,MAAM;MACV,IAAIK,WAAW,CAAC,CAAC;;MAEjB,IAAI2F,gBAAgB,KAAKM,SAAS,IAAIN,gBAAgB,KAAK,MAAM,EAAE;QACjE;QACA3F,WAAW,GAAG2F,gBAAgB;MAChC,CAAC,MAAM,IAAIK,MAAM,CAACE,UAAU,EAAE;QAC5B;QACAlG,WAAW,GAAG,EAAE,CAACwF,MAAM,CAACQ,MAAM,CAACE,UAAU,EAAE,GAAG,CAAC,CAACV,MAAM,CAACQ,MAAM,CAACG,WAAW,CAAC;MAC5E,CAAC,MAAM;QACL;QACAnG,WAAW,GAAG,MAAM;MACtB,CAAC,CAAC;;MAGF,IAAIoG,UAAU,GAAGpG,WAAW,CAACqG,KAAK,CAAC,GAAG,CAAC;MACvC,IAAIC,eAAe,GAAGF,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC;MAEnD,IAAIP,UAAU,KAAKI,SAAS,EAAE;QAC5B;QACA1G,KAAK,GAAGsG,UAAU;MACpB,CAAC,MAAM,IAAID,WAAW,KAAKK,SAAS,EAAE;QACpC;QACA1G,KAAK,GAAGqG,WAAW,GAAGU,eAAe;MACvC,CAAC,MAAM;QACL;QACA/G,KAAK,GAAGyG,MAAM,CAACE,UAAU,IAAI,GAAG;MAClC;MAEA,IAAIN,WAAW,KAAKK,SAAS,EAAE;QAC7B;QACAtG,MAAM,GAAGiG,WAAW;MACtB,CAAC,MAAM;QACL;QACAjG,MAAM,GAAGJ,KAAK,GAAG+G,eAAe;MAClC;MAEA,IAAI1G,KAAK,EAAE;QACTwF,KAAK,CAACmB,UAAU,GAAG,EAAE,CAACf,MAAM,CAACc,eAAe,GAAG,GAAG,EAAE,GAAG,CAAC;MAC1D,CAAC,MAAM;QACL;QACA,IAAI,CAACnB,gBAAgB,CAACC,KAAK,EAAE,OAAO,EAAE7F,KAAK,CAAC;QAC5C,IAAI,CAAC4F,gBAAgB,CAACC,KAAK,EAAE,QAAQ,EAAEzF,MAAM,CAAC;MAChD;MAEA,OAAOyF,KAAK;IACd,CAAC,CAAC;IACF;EAEF,CAAC,EAAE;IACDxB,GAAG,EAAE,UAAU;IACfjG,KAAK,EAAE,SAASoI,QAAQA,CAAA,EAAG;MACzB,OAAO,IAAI,CAACpD,OAAO,CAACoD,QAAQ,EAAE;IAChC,CAAC,CAAC;EAEJ,CAAC,EAAE;IACDnC,GAAG,EAAE,MAAM;IACX;IACAjG,KAAK,EAAE,SAAS6I,IAAIA,CAAA,EAAG;MACrB,IAAI,CAAC9D,KAAK,CAAC8D,IAAI,EAAE;IACnB,CAAC,CAAC;EAEJ,CAAC,EAAE;IACD5C,GAAG,EAAE,OAAO;IACZjG,KAAK,EAAE,SAAS8I,KAAKA,CAAA,EAAG;MACtB,IAAI,CAAC/D,KAAK,CAAC+D,KAAK,EAAE;IACpB,CAAC,CAAC;EAEJ,CAAC,EAAE;IACD7C,GAAG,EAAE,MAAM;IACXjG,KAAK,EAAE,SAAS+I,IAAIA,CAAA,EAAG;MACrB,IAAI,CAAChE,KAAK,CAACgE,IAAI,EAAE;IACnB,CAAC,CAAC;EAEJ,CAAC,EAAE;IACD9C,GAAG,EAAE,cAAc;IACnBjG,KAAK,EAAE,SAASgJ,YAAYA,CAAA,EAAG;MAC7B,IAAIC,WAAW;MAEf,CAACA,WAAW,GAAG,IAAI,CAAClE,KAAK,EAAEiE,YAAY,CAACE,KAAK,CAACD,WAAW,EAAEE,SAAS,CAAC;IACvE,CAAC,CAAC;EAEJ,CAAC,EAAE;IACDlD,GAAG,EAAE,aAAa;IAClBjG,KAAK,EAAE,SAASoJ,WAAWA,CAAA,EAAG;MAC5B,IAAIC,YAAY;MAEhB,CAACA,YAAY,GAAG,IAAI,CAACtE,KAAK,EAAEqE,WAAW,CAACF,KAAK,CAACG,YAAY,EAAEF,SAAS,CAAC;IACxE,CAAC,CAAC;EAEJ,CAAC,EAAE;IACDlD,GAAG,EAAE,MAAM;IACXjG,KAAK,EAAE,SAASsJ,IAAIA,CAACC,IAAI,EAAE;MACzB,IAAI,CAACxE,KAAK,CAACuE,IAAI,CAACC,IAAI,CAAC;IACvB,CAAC,CAAC;EAEJ,CAAC,EAAE;IACDtD,GAAG,EAAE,SAAS;IACdjG,KAAK,EAAE,SAASwJ,OAAOA,CAACC,OAAO,EAAE;MAC/B,IAAI,CAAC1E,KAAK,CAACyE,OAAO,CAACC,OAAO,CAAC;IAC7B,CAAC,CAAC;EAEJ,CAAC,EAAE;IACDxD,GAAG,EAAE,QAAQ;IACbjG,KAAK,EAAE,SAAS0J,MAAMA,CAACD,OAAO,EAAE;MAC9B,IAAI,CAAC1E,KAAK,CAAC2E,MAAM,CAACD,OAAO,CAAC;IAC5B,CAAC,CAAC;EAEJ,CAAC,EAAE;IACDxD,GAAG,EAAE,kBAAkB;IACvBjG,KAAK,EAAE,SAAS2J,gBAAgBA,CAAA,EAAG;MACjC,IAAI,CAAC5E,KAAK,CAAC4E,gBAAgB,EAAE;IAC/B,CAAC,CAAC;EAEJ,CAAC,EAAE;IACD1D,GAAG,EAAE,wBAAwB;IAC7BjG,KAAK,EAAE,SAAS4J,sBAAsBA,CAACC,QAAQ,EAAE;MAC/C,OAAO,IAAI,CAAC7E,OAAO,CAACG,4BAA4B,CAAC0E,QAAQ,CAAC;IAC5D,CAAC,CAAC;EAEJ,CAAC,EAAE;IACD5D,GAAG,EAAE,cAAc;IACnBjG,KAAK,EAAE,SAASuF,YAAYA,CAAA,EAAG,CAAC;EAClC,CAAC,EAAE;IACDU,GAAG,EAAE,wBAAwB;IAC7BjG,KAAK,EAAE,SAAS6F,sBAAsBA,CAACiE,KAAK,EAAE;MAC5C,IAAIA,KAAK,CAACC,MAAM,KAAK,IAAI,CAAC/E,OAAO,CAACgF,WAAW,EAAE;QAC7C,IAAI,CAAC/E,OAAO,CAACgF,sBAAsB,CAACzI,WAAW,CAAC,SAAS,CAAC,CAAC0I,YAAY,CAAC;MAC1E;IACF;EACF,CAAC,EAAE;IACDjE,GAAG,EAAE,iBAAiB;IACtBjG,KAAK,EAAE,SAAS2F,eAAeA,CAAA,EAAG;MAChC,IAAI,CAACC,kBAAkB,EAAE;IAC3B;EACF,CAAC,EAAE;IACDK,GAAG,EAAE,iBAAiB;IACtBjG,KAAK,EAAE,SAASyF,eAAeA,CAAA,EAAG;MAChC,IAAI,CAACG,kBAAkB,EAAE;IAC3B;EACF,CAAC,EAAE;IACDK,GAAG,EAAE,eAAe;IACpBjG,KAAK,EAAE,SAAS8F,aAAaA,CAAA,EAAG;MAC9B,IAAI,CAACF,kBAAkB,EAAE;IAC3B;EACF,CAAC,EAAE;IACDK,GAAG,EAAE,oBAAoB;IACzBjG,KAAK,EAAE,SAAS4F,kBAAkBA,CAAA,EAAG;MACnC,IAAIuE,MAAM,GAAG,IAAI;MAEjB,IAAIC,oBAAoB,GAAG,IAAI;MAE/BzJ,MAAM,CAAC,SAAS,CAAC,CAACsG,QAAQ,CAACoD,OAAO,CAAC,IAAI,CAAC1F,KAAK,CAACjD,QAAQ,EAAE,UAAU4I,OAAO,EAAE;QACzE,IAAI,CAAC3J,MAAM,CAAC,SAAS,CAAC,CAAC4J,cAAc,CAACD,OAAO,CAAC,IAAIA,OAAO,CAACE,IAAI,KAAKpJ,WAAW,CAAC,SAAS,CAAC,EAAE;UACzF;QACF;QAEA,IAAIqJ,YAAY,GAAGH,OAAO,CAAC3F,KAAK,CAAC8F,YAAY;QAE7C,IAAI,OAAOA,YAAY,KAAK,QAAQ,EAAE;UACpCL,oBAAoB,GAAGK,YAAY;QACrC;MACF,CAAC,CAAC;MAEF,IAAI,CAACxF,OAAO,CAACyF,YAAY,CAAC,IAAI,CAAC;MAC/BnE,YAAY,CAAC,IAAI,CAACzB,iBAAiB,CAAC;MACpC,IAAI,CAACA,iBAAiB,GAAG6F,UAAU,CAAC,YAAY;QAC9CR,MAAM,CAAClF,OAAO,CAACyF,YAAY,CAAC,KAAK,CAAC;MACpC,CAAC,EAAEN,oBAAoB,CAAC;IAC1B;EACF,CAAC,EAAE;IACDnE,GAAG,EAAE,mBAAmB;IACxBjG,KAAK,EAAE,SAASoF,iBAAiBA,CAACwF,KAAK,EAAEC,SAAS,EAAE;MAClD,IAAID,KAAK,CAACV,YAAY,KAAKW,SAAS,CAACX,YAAY,EAAE;QACjD,IAAI,CAAC3E,YAAY,EAAE,CAAC,CAAC;;QAErB,CAAC,CAAC,EAAEjE,IAAI,CAACwJ,SAAS,EAAE,IAAI,CAAC9F,OAAO,CAACgF,WAAW,CAAC;MAC/C;MAEA,IAAI,CAACe,WAAW,EAAE,CAAC,CAAC;IACtB;EACF,CAAC,EAAE;IACD9E,GAAG,EAAE,aAAa;IAClBjG,KAAK,EAAE,SAAS+F,WAAWA,CAAA,EAAG;MAC5B,IAAI,CAACd,OAAO,CAAC+F,QAAQ,CAAC,IAAI,CAAC;IAC7B;EACF,CAAC,EAAE;IACD/E,GAAG,EAAE,YAAY;IACjBjG,KAAK,EAAE,SAASgG,UAAUA,CAAA,EAAG;MAC3B,IAAI,CAACf,OAAO,CAAC+F,QAAQ,CAAC,KAAK,CAAC;IAC9B;EACF,CAAC,EAAE;IACD/E,GAAG,EAAE,QAAQ;IACbjG,KAAK,EAAE,SAASiL,MAAMA,CAAA,EAAG;MACvB,IAAIC,MAAM,GAAG,IAAI;MAEjB,IAAIjJ,KAAK,GAAG,IAAI,CAAC0C,KAAK,CAAC1C,KAAK;MAE5B,IAAIkJ,sBAAsB,GAAG,IAAI,CAACnG,OAAO,CAACoD,QAAQ,EAAE;QAChDC,MAAM,GAAG8C,sBAAsB,CAAC9C,MAAM;MAE1C,IAAI+C,MAAM,GAAG/C,MAAM,CAAC+C,MAAM;QACtBC,UAAU,GAAGhD,MAAM,CAACgD,UAAU;QAC9BC,OAAO,GAAGjD,MAAM,CAACiD,OAAO;QACxBC,OAAO,GAAGlD,MAAM,CAACkD,OAAO;QACxBrB,YAAY,GAAG7B,MAAM,CAAC6B,YAAY;QAClCsB,YAAY,GAAGnD,MAAM,CAACmD,YAAY;MACtC,IAAI7G,KAAK,GAAG,CAAC,CAAC,EAAE1E,cAAc,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC0E,KAAK,EAAE;QACzD0D,MAAM,EAAEA,MAAM;QACdpD,OAAO,EAAE,IAAI,CAACA,OAAO;QACrBD,OAAO,EAAE,IAAI,CAACA,OAAO;QACrBV,KAAK,EAAE,IAAI,CAACU,OAAO,CAACV,KAAK;QACzBS,KAAK,EAAE,IAAI,CAACA,KAAK,GAAG,IAAI,CAACA,KAAK,CAACA,KAAK,GAAG;MACzC,CAAC,CAAC;MACF,IAAIrD,QAAQ,GAAG,IAAI,CAAC8D,WAAW,CAACb,KAAK,CAAC;MACtC,OAAOhE,MAAM,CAAC,SAAS,CAAC,CAACgG,aAAa,CAAC,KAAK,EAAE;QAC5CrE,SAAS,EAAE,CAAC,CAAC,EAAE1B,WAAW,CAAC,SAAS,CAAC,EAAE;UACrC,8BAA8B,EAAE,IAAI;UACpC,yBAAyB,EAAEyK,UAAU;UACrC,oBAAoB,EAAED,MAAM;UAC5B,qBAAqB,EAAE,CAACA,MAAM;UAC9B,qBAAqB,EAAEE,OAAO;UAC9B,qBAAqB,EAAEC,OAAO;UAC9B,mBAAmB,EAAEtJ,KAAK;UAC1B,wBAAwB,EAAEiI,YAAY;UACtC,2BAA2B,EAAE,CAACsB,YAAY;UAC1C,yBAAyB,EAAEA,YAAY;UACvC,0BAA0B,EAAE,CAACnK,OAAO,CAACoK;QACvC,CAAC,EAAE,aAAa,EAAE,IAAI,CAAC9G,KAAK,CAACrC,SAAS,CAAC;QACvCmF,KAAK,EAAE,IAAI,CAACnC,QAAQ,EAAE;QACtBsB,GAAG,EAAE,SAASA,GAAGA,CAACC,CAAC,EAAE;UACnBqE,MAAM,CAAClG,OAAO,CAACgF,WAAW,GAAGnD,CAAC;QAChC,CAAC;QACD6E,IAAI,EAAE,QAAQ;QACdC,YAAY,EAAE,IAAI,CAAChG,eAAe;QAClCiG,WAAW,EAAE,IAAI,CAACjG,eAAe;QACjCkG,WAAW,EAAE,IAAI,CAACpG,eAAe;QACjCqG,WAAW,EAAE,IAAI,CAACrG,eAAe;QACjCsG,SAAS,EAAE,IAAI,CAACjG,aAAa;QAC7BkG,OAAO,EAAE,IAAI,CAACjG,WAAW;QACzBkG,MAAM,EAAE,IAAI,CAACjG,UAAU;QACvBkG,QAAQ,EAAE;MACZ,CAAC,EAAExK,QAAQ,CAAC;IACd;EACF,CAAC,EAAE;IACDuE,GAAG,EAAE,cAAc;IACnBkG,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;MAClB,OAAO,IAAI,CAACpH,KAAK,CAACqH,YAAY;IAChC,CAAC,CAAC;IACF;IAAA;;IAEAC,GAAG,EAAE,SAASA,GAAGA,CAACC,IAAI,EAAE;MACtB,IAAI,CAACvH,KAAK,CAACqH,YAAY,GAAGE,IAAI;IAChC;EACF,CAAC,EAAE;IACDrG,GAAG,EAAE,OAAO;IACZkG,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;MAClB,OAAO,IAAI,CAACpH,KAAK,CAAC5C,KAAK;IACzB,CAAC;IACDkK,GAAG,EAAE,SAASA,GAAGA,CAACE,GAAG,EAAE;MACrB,IAAI,CAACxH,KAAK,CAAC5C,KAAK,GAAGoK,GAAG;IACxB;EACF,CAAC,EAAE;IACDtG,GAAG,EAAE,QAAQ;IACbkG,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;MAClB,OAAO,IAAI,CAACpH,KAAK,CAACyH,MAAM;IAC1B,CAAC;IACDH,GAAG,EAAE,SAASA,GAAGA,CAACE,GAAG,EAAE;MACrB,IAAI,CAACxH,KAAK,CAACyH,MAAM,GAAGD,GAAG;IACzB,CAAC,CAAC;EAEJ,CAAC,EAAE;IACDtG,GAAG,EAAE,YAAY;IACjBkG,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;MAClB,OAAO,IAAI,CAACpH,KAAK,CAACwD,UAAU;IAC9B,CAAC,CAAC;EAEJ,CAAC,EAAE;IACDtC,GAAG,EAAE,aAAa;IAClBkG,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;MAClB,OAAO,IAAI,CAACpH,KAAK,CAACyD,WAAW;IAC/B;EACF,CAAC,CAAC,CAAC;EACH,OAAO/D,MAAM;AACf,CAAC,CAAC9D,MAAM,CAAC8L,SAAS,CAAC;AAEnB1M,OAAO,CAAC,SAAS,CAAC,GAAG0E,MAAM;AAC3BA,MAAM,CAACiI,YAAY,GAAG;EACpBpI,KAAK,EAAE5D,UAAU,CAAC,SAAS,CAAC,CAAC6D;AAC/B,CAAC;AACDE,MAAM,CAAChD,SAAS,GAAGA,SAAS;AAC5BgD,MAAM,CAACD,YAAY,GAAGA,YAAY;AAClCC,MAAM,CAACkI,WAAW,GAAG,QAAQ"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}