{"ast": null, "code": "import createShadow from '../../shared/create-shadow.js';\nimport effectInit from '../../shared/effect-init.js';\nimport effectTarget from '../../shared/effect-target.js';\nimport effectVirtualTransitionEnd from '../../shared/effect-virtual-transition-end.js';\nimport { getSlideTransformEl } from '../../shared/utils.js';\nexport default function EffectFlip(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on\n  } = _ref;\n  extendParams({\n    flipEffect: {\n      slideShadows: true,\n      limitRotation: true\n    }\n  });\n  const createSlideShadows = (slideEl, progress, params) => {\n    let shadowBefore = swiper.isHorizontal() ? slideEl.querySelector('.swiper-slide-shadow-left') : slideEl.querySelector('.swiper-slide-shadow-top');\n    let shadowAfter = swiper.isHorizontal() ? slideEl.querySelector('.swiper-slide-shadow-right') : slideEl.querySelector('.swiper-slide-shadow-bottom');\n    if (!shadowBefore) {\n      shadowBefore = createShadow(params, slideEl, swiper.isHorizontal() ? 'left' : 'top');\n    }\n    if (!shadowAfter) {\n      shadowAfter = createShadow(params, slideEl, swiper.isHorizontal() ? 'right' : 'bottom');\n    }\n    if (shadowBefore) shadowBefore.style.opacity = Math.max(-progress, 0);\n    if (shadowAfter) shadowAfter.style.opacity = Math.max(progress, 0);\n  };\n  const recreateShadows = () => {\n    // Set shadows\n    const params = swiper.params.flipEffect;\n    swiper.slides.forEach(slideEl => {\n      let progress = slideEl.progress;\n      if (swiper.params.flipEffect.limitRotation) {\n        progress = Math.max(Math.min(slideEl.progress, 1), -1);\n      }\n      createSlideShadows(slideEl, progress, params);\n    });\n  };\n  const setTranslate = () => {\n    const {\n      slides,\n      rtlTranslate: rtl\n    } = swiper;\n    const params = swiper.params.flipEffect;\n    for (let i = 0; i < slides.length; i += 1) {\n      const slideEl = slides[i];\n      let progress = slideEl.progress;\n      if (swiper.params.flipEffect.limitRotation) {\n        progress = Math.max(Math.min(slideEl.progress, 1), -1);\n      }\n      const offset = slideEl.swiperSlideOffset;\n      const rotate = -180 * progress;\n      let rotateY = rotate;\n      let rotateX = 0;\n      let tx = swiper.params.cssMode ? -offset - swiper.translate : -offset;\n      let ty = 0;\n      if (!swiper.isHorizontal()) {\n        ty = tx;\n        tx = 0;\n        rotateX = -rotateY;\n        rotateY = 0;\n      } else if (rtl) {\n        rotateY = -rotateY;\n      }\n      slideEl.style.zIndex = -Math.abs(Math.round(progress)) + slides.length;\n      if (params.slideShadows) {\n        createSlideShadows(slideEl, progress, params);\n      }\n      const transform = `translate3d(${tx}px, ${ty}px, 0px) rotateX(${rotateX}deg) rotateY(${rotateY}deg)`;\n      const targetEl = effectTarget(params, slideEl);\n      targetEl.style.transform = transform;\n    }\n  };\n  const setTransition = duration => {\n    const transformElements = swiper.slides.map(slideEl => getSlideTransformEl(slideEl));\n    transformElements.forEach(el => {\n      el.style.transitionDuration = `${duration}ms`;\n      el.querySelectorAll('.swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left').forEach(shadowEl => {\n        shadowEl.style.transitionDuration = `${duration}ms`;\n      });\n    });\n    effectVirtualTransitionEnd({\n      swiper,\n      duration,\n      transformElements\n    });\n  };\n  effectInit({\n    effect: 'flip',\n    swiper,\n    on,\n    setTranslate,\n    setTransition,\n    recreateShadows,\n    getEffectParams: () => swiper.params.flipEffect,\n    perspective: () => true,\n    overwriteParams: () => ({\n      slidesPerView: 1,\n      slidesPerGroup: 1,\n      watchSlidesProgress: true,\n      spaceBetween: 0,\n      virtualTranslate: !swiper.params.cssMode\n    })\n  });\n}", "map": {"version": 3, "names": ["createShadow", "effectInit", "effect<PERSON>arget", "effectVirtualTransitionEnd", "getSlideTransformEl", "EffectFlip", "_ref", "swiper", "extendParams", "on", "flipEffect", "slideShadows", "limitRotation", "createSlideShadows", "slideEl", "progress", "params", "shadowBefore", "isHorizontal", "querySelector", "shadowAfter", "style", "opacity", "Math", "max", "recreateShadows", "slides", "for<PERSON>ach", "min", "setTranslate", "rtlTranslate", "rtl", "i", "length", "offset", "swiperSlideOffset", "rotate", "rotateY", "rotateX", "tx", "cssMode", "translate", "ty", "zIndex", "abs", "round", "transform", "targetEl", "setTransition", "duration", "transformElements", "map", "el", "transitionDuration", "querySelectorAll", "shadowEl", "effect", "getEffectParams", "perspective", "overwriteParams", "<PERSON><PERSON><PERSON><PERSON>iew", "slidesPerGroup", "watchSlidesProgress", "spaceBetween", "virtualTranslate"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/modules/effect-flip/effect-flip.js"], "sourcesContent": ["import createShadow from '../../shared/create-shadow.js';\nimport effectInit from '../../shared/effect-init.js';\nimport effectTarget from '../../shared/effect-target.js';\nimport effectVirtualTransitionEnd from '../../shared/effect-virtual-transition-end.js';\nimport { getSlideTransformEl } from '../../shared/utils.js';\nexport default function EffectFlip({\n  swiper,\n  extendParams,\n  on\n}) {\n  extendParams({\n    flipEffect: {\n      slideShadows: true,\n      limitRotation: true\n    }\n  });\n  const createSlideShadows = (slideEl, progress, params) => {\n    let shadowBefore = swiper.isHorizontal() ? slideEl.querySelector('.swiper-slide-shadow-left') : slideEl.querySelector('.swiper-slide-shadow-top');\n    let shadowAfter = swiper.isHorizontal() ? slideEl.querySelector('.swiper-slide-shadow-right') : slideEl.querySelector('.swiper-slide-shadow-bottom');\n    if (!shadowBefore) {\n      shadowBefore = createShadow(params, slideEl, swiper.isHorizontal() ? 'left' : 'top');\n    }\n    if (!shadowAfter) {\n      shadowAfter = createShadow(params, slideEl, swiper.isHorizontal() ? 'right' : 'bottom');\n    }\n    if (shadowBefore) shadowBefore.style.opacity = Math.max(-progress, 0);\n    if (shadowAfter) shadowAfter.style.opacity = Math.max(progress, 0);\n  };\n  const recreateShadows = () => {\n    // Set shadows\n    const params = swiper.params.flipEffect;\n    swiper.slides.forEach(slideEl => {\n      let progress = slideEl.progress;\n      if (swiper.params.flipEffect.limitRotation) {\n        progress = Math.max(Math.min(slideEl.progress, 1), -1);\n      }\n      createSlideShadows(slideEl, progress, params);\n    });\n  };\n  const setTranslate = () => {\n    const {\n      slides,\n      rtlTranslate: rtl\n    } = swiper;\n    const params = swiper.params.flipEffect;\n    for (let i = 0; i < slides.length; i += 1) {\n      const slideEl = slides[i];\n      let progress = slideEl.progress;\n      if (swiper.params.flipEffect.limitRotation) {\n        progress = Math.max(Math.min(slideEl.progress, 1), -1);\n      }\n      const offset = slideEl.swiperSlideOffset;\n      const rotate = -180 * progress;\n      let rotateY = rotate;\n      let rotateX = 0;\n      let tx = swiper.params.cssMode ? -offset - swiper.translate : -offset;\n      let ty = 0;\n      if (!swiper.isHorizontal()) {\n        ty = tx;\n        tx = 0;\n        rotateX = -rotateY;\n        rotateY = 0;\n      } else if (rtl) {\n        rotateY = -rotateY;\n      }\n      slideEl.style.zIndex = -Math.abs(Math.round(progress)) + slides.length;\n      if (params.slideShadows) {\n        createSlideShadows(slideEl, progress, params);\n      }\n      const transform = `translate3d(${tx}px, ${ty}px, 0px) rotateX(${rotateX}deg) rotateY(${rotateY}deg)`;\n      const targetEl = effectTarget(params, slideEl);\n      targetEl.style.transform = transform;\n    }\n  };\n  const setTransition = duration => {\n    const transformElements = swiper.slides.map(slideEl => getSlideTransformEl(slideEl));\n    transformElements.forEach(el => {\n      el.style.transitionDuration = `${duration}ms`;\n      el.querySelectorAll('.swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left').forEach(shadowEl => {\n        shadowEl.style.transitionDuration = `${duration}ms`;\n      });\n    });\n    effectVirtualTransitionEnd({\n      swiper,\n      duration,\n      transformElements\n    });\n  };\n  effectInit({\n    effect: 'flip',\n    swiper,\n    on,\n    setTranslate,\n    setTransition,\n    recreateShadows,\n    getEffectParams: () => swiper.params.flipEffect,\n    perspective: () => true,\n    overwriteParams: () => ({\n      slidesPerView: 1,\n      slidesPerGroup: 1,\n      watchSlidesProgress: true,\n      spaceBetween: 0,\n      virtualTranslate: !swiper.params.cssMode\n    })\n  });\n}"], "mappings": "AAAA,OAAOA,YAAY,MAAM,+BAA+B;AACxD,OAAOC,UAAU,MAAM,6BAA6B;AACpD,OAAOC,YAAY,MAAM,+BAA+B;AACxD,OAAOC,0BAA0B,MAAM,+CAA+C;AACtF,SAASC,mBAAmB,QAAQ,uBAAuB;AAC3D,eAAe,SAASC,UAAUA,CAAAC,IAAA,EAI/B;EAAA,IAJgC;IACjCC,MAAM;IACNC,YAAY;IACZC;EACF,CAAC,GAAAH,IAAA;EACCE,YAAY,CAAC;IACXE,UAAU,EAAE;MACVC,YAAY,EAAE,IAAI;MAClBC,aAAa,EAAE;IACjB;EACF,CAAC,CAAC;EACF,MAAMC,kBAAkB,GAAGA,CAACC,OAAO,EAAEC,QAAQ,EAAEC,MAAM,KAAK;IACxD,IAAIC,YAAY,GAAGV,MAAM,CAACW,YAAY,EAAE,GAAGJ,OAAO,CAACK,aAAa,CAAC,2BAA2B,CAAC,GAAGL,OAAO,CAACK,aAAa,CAAC,0BAA0B,CAAC;IACjJ,IAAIC,WAAW,GAAGb,MAAM,CAACW,YAAY,EAAE,GAAGJ,OAAO,CAACK,aAAa,CAAC,4BAA4B,CAAC,GAAGL,OAAO,CAACK,aAAa,CAAC,6BAA6B,CAAC;IACpJ,IAAI,CAACF,YAAY,EAAE;MACjBA,YAAY,GAAGjB,YAAY,CAACgB,MAAM,EAAEF,OAAO,EAAEP,MAAM,CAACW,YAAY,EAAE,GAAG,MAAM,GAAG,KAAK,CAAC;IACtF;IACA,IAAI,CAACE,WAAW,EAAE;MAChBA,WAAW,GAAGpB,YAAY,CAACgB,MAAM,EAAEF,OAAO,EAAEP,MAAM,CAACW,YAAY,EAAE,GAAG,OAAO,GAAG,QAAQ,CAAC;IACzF;IACA,IAAID,YAAY,EAAEA,YAAY,CAACI,KAAK,CAACC,OAAO,GAAGC,IAAI,CAACC,GAAG,CAAC,CAACT,QAAQ,EAAE,CAAC,CAAC;IACrE,IAAIK,WAAW,EAAEA,WAAW,CAACC,KAAK,CAACC,OAAO,GAAGC,IAAI,CAACC,GAAG,CAACT,QAAQ,EAAE,CAAC,CAAC;EACpE,CAAC;EACD,MAAMU,eAAe,GAAGA,CAAA,KAAM;IAC5B;IACA,MAAMT,MAAM,GAAGT,MAAM,CAACS,MAAM,CAACN,UAAU;IACvCH,MAAM,CAACmB,MAAM,CAACC,OAAO,CAACb,OAAO,IAAI;MAC/B,IAAIC,QAAQ,GAAGD,OAAO,CAACC,QAAQ;MAC/B,IAAIR,MAAM,CAACS,MAAM,CAACN,UAAU,CAACE,aAAa,EAAE;QAC1CG,QAAQ,GAAGQ,IAAI,CAACC,GAAG,CAACD,IAAI,CAACK,GAAG,CAACd,OAAO,CAACC,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACxD;MACAF,kBAAkB,CAACC,OAAO,EAAEC,QAAQ,EAAEC,MAAM,CAAC;IAC/C,CAAC,CAAC;EACJ,CAAC;EACD,MAAMa,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAM;MACJH,MAAM;MACNI,YAAY,EAAEC;IAChB,CAAC,GAAGxB,MAAM;IACV,MAAMS,MAAM,GAAGT,MAAM,CAACS,MAAM,CAACN,UAAU;IACvC,KAAK,IAAIsB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,MAAM,CAACO,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;MACzC,MAAMlB,OAAO,GAAGY,MAAM,CAACM,CAAC,CAAC;MACzB,IAAIjB,QAAQ,GAAGD,OAAO,CAACC,QAAQ;MAC/B,IAAIR,MAAM,CAACS,MAAM,CAACN,UAAU,CAACE,aAAa,EAAE;QAC1CG,QAAQ,GAAGQ,IAAI,CAACC,GAAG,CAACD,IAAI,CAACK,GAAG,CAACd,OAAO,CAACC,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACxD;MACA,MAAMmB,MAAM,GAAGpB,OAAO,CAACqB,iBAAiB;MACxC,MAAMC,MAAM,GAAG,CAAC,GAAG,GAAGrB,QAAQ;MAC9B,IAAIsB,OAAO,GAAGD,MAAM;MACpB,IAAIE,OAAO,GAAG,CAAC;MACf,IAAIC,EAAE,GAAGhC,MAAM,CAACS,MAAM,CAACwB,OAAO,GAAG,CAACN,MAAM,GAAG3B,MAAM,CAACkC,SAAS,GAAG,CAACP,MAAM;MACrE,IAAIQ,EAAE,GAAG,CAAC;MACV,IAAI,CAACnC,MAAM,CAACW,YAAY,EAAE,EAAE;QAC1BwB,EAAE,GAAGH,EAAE;QACPA,EAAE,GAAG,CAAC;QACND,OAAO,GAAG,CAACD,OAAO;QAClBA,OAAO,GAAG,CAAC;MACb,CAAC,MAAM,IAAIN,GAAG,EAAE;QACdM,OAAO,GAAG,CAACA,OAAO;MACpB;MACAvB,OAAO,CAACO,KAAK,CAACsB,MAAM,GAAG,CAACpB,IAAI,CAACqB,GAAG,CAACrB,IAAI,CAACsB,KAAK,CAAC9B,QAAQ,CAAC,CAAC,GAAGW,MAAM,CAACO,MAAM;MACtE,IAAIjB,MAAM,CAACL,YAAY,EAAE;QACvBE,kBAAkB,CAACC,OAAO,EAAEC,QAAQ,EAAEC,MAAM,CAAC;MAC/C;MACA,MAAM8B,SAAS,GAAI,eAAcP,EAAG,OAAMG,EAAG,oBAAmBJ,OAAQ,gBAAeD,OAAQ,MAAK;MACpG,MAAMU,QAAQ,GAAG7C,YAAY,CAACc,MAAM,EAAEF,OAAO,CAAC;MAC9CiC,QAAQ,CAAC1B,KAAK,CAACyB,SAAS,GAAGA,SAAS;IACtC;EACF,CAAC;EACD,MAAME,aAAa,GAAGC,QAAQ,IAAI;IAChC,MAAMC,iBAAiB,GAAG3C,MAAM,CAACmB,MAAM,CAACyB,GAAG,CAACrC,OAAO,IAAIV,mBAAmB,CAACU,OAAO,CAAC,CAAC;IACpFoC,iBAAiB,CAACvB,OAAO,CAACyB,EAAE,IAAI;MAC9BA,EAAE,CAAC/B,KAAK,CAACgC,kBAAkB,GAAI,GAAEJ,QAAS,IAAG;MAC7CG,EAAE,CAACE,gBAAgB,CAAC,8GAA8G,CAAC,CAAC3B,OAAO,CAAC4B,QAAQ,IAAI;QACtJA,QAAQ,CAAClC,KAAK,CAACgC,kBAAkB,GAAI,GAAEJ,QAAS,IAAG;MACrD,CAAC,CAAC;IACJ,CAAC,CAAC;IACF9C,0BAA0B,CAAC;MACzBI,MAAM;MACN0C,QAAQ;MACRC;IACF,CAAC,CAAC;EACJ,CAAC;EACDjD,UAAU,CAAC;IACTuD,MAAM,EAAE,MAAM;IACdjD,MAAM;IACNE,EAAE;IACFoB,YAAY;IACZmB,aAAa;IACbvB,eAAe;IACfgC,eAAe,EAAEA,CAAA,KAAMlD,MAAM,CAACS,MAAM,CAACN,UAAU;IAC/CgD,WAAW,EAAEA,CAAA,KAAM,IAAI;IACvBC,eAAe,EAAEA,CAAA,MAAO;MACtBC,aAAa,EAAE,CAAC;MAChBC,cAAc,EAAE,CAAC;MACjBC,mBAAmB,EAAE,IAAI;MACzBC,YAAY,EAAE,CAAC;MACfC,gBAAgB,EAAE,CAACzD,MAAM,CAACS,MAAM,CAACwB;IACnC,CAAC;EACH,CAAC,CAAC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}