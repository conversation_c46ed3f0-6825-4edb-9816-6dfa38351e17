{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar _react = _interopRequireDefault(require(\"react\"));\nvar _allowed = _interopRequireDefault(require(\"../utils/allowed\"));\nvar Tbody = function Tbody(props) {\n  return /*#__PURE__*/_react[\"default\"].createElement(\"tbody\", (0, _extends2[\"default\"])({\n    \"data-testid\": \"tbody\"\n  }, (0, _allowed[\"default\"])(props)));\n};\nvar _default = Tbody;\nexports[\"default\"] = _default;", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "_extends2", "_react", "_allowed", "Tbody", "props", "createElement", "_default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/react-super-responsive-table/dist/components/Tbody.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\n\nvar _react = _interopRequireDefault(require(\"react\"));\n\nvar _allowed = _interopRequireDefault(require(\"../utils/allowed\"));\n\nvar Tbody = function Tbody(props) {\n  return /*#__PURE__*/_react[\"default\"].createElement(\"tbody\", (0, _extends2[\"default\"])({\n    \"data-testid\": \"tbody\"\n  }, (0, _allowed[\"default\"])(props)));\n};\n\nvar _default = Tbody;\nexports[\"default\"] = _default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC;AAEpFC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;AAE3B,IAAIE,SAAS,GAAGN,sBAAsB,CAACC,OAAO,CAAC,gCAAgC,CAAC,CAAC;AAEjF,IAAIM,MAAM,GAAGP,sBAAsB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AAErD,IAAIO,QAAQ,GAAGR,sBAAsB,CAACC,OAAO,CAAC,kBAAkB,CAAC,CAAC;AAElE,IAAIQ,KAAK,GAAG,SAASA,KAAKA,CAACC,KAAK,EAAE;EAChC,OAAO,aAAaH,MAAM,CAAC,SAAS,CAAC,CAACI,aAAa,CAAC,OAAO,EAAE,CAAC,CAAC,EAAEL,SAAS,CAAC,SAAS,CAAC,EAAE;IACrF,aAAa,EAAE;EACjB,CAAC,EAAE,CAAC,CAAC,EAAEE,QAAQ,CAAC,SAAS,CAAC,EAAEE,KAAK,CAAC,CAAC,CAAC;AACtC,CAAC;AAED,IAAIE,QAAQ,GAAGH,KAAK;AACpBL,OAAO,CAAC,SAAS,CAAC,GAAGQ,QAAQ"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}