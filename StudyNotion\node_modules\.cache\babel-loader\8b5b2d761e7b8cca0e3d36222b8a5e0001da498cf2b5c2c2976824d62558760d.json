{"ast": null, "code": "/* eslint-disable no-underscore-dangle */\n\nexport default {\n  on(events, handler, priority) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (typeof handler !== 'function') return self;\n    const method = priority ? 'unshift' : 'push';\n    events.split(' ').forEach(event => {\n      if (!self.eventsListeners[event]) self.eventsListeners[event] = [];\n      self.eventsListeners[event][method](handler);\n    });\n    return self;\n  },\n  once(events, handler, priority) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (typeof handler !== 'function') return self;\n    function onceHandler() {\n      self.off(events, onceHandler);\n      if (onceHandler.__emitterProxy) {\n        delete onceHandler.__emitterProxy;\n      }\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      handler.apply(self, args);\n    }\n    onceHandler.__emitterProxy = handler;\n    return self.on(events, onceHandler, priority);\n  },\n  onAny(handler, priority) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (typeof handler !== 'function') return self;\n    const method = priority ? 'unshift' : 'push';\n    if (self.eventsAnyListeners.indexOf(handler) < 0) {\n      self.eventsAnyListeners[method](handler);\n    }\n    return self;\n  },\n  offAny(handler) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (!self.eventsAnyListeners) return self;\n    const index = self.eventsAnyListeners.indexOf(handler);\n    if (index >= 0) {\n      self.eventsAnyListeners.splice(index, 1);\n    }\n    return self;\n  },\n  off(events, handler) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (!self.eventsListeners) return self;\n    events.split(' ').forEach(event => {\n      if (typeof handler === 'undefined') {\n        self.eventsListeners[event] = [];\n      } else if (self.eventsListeners[event]) {\n        self.eventsListeners[event].forEach((eventHandler, index) => {\n          if (eventHandler === handler || eventHandler.__emitterProxy && eventHandler.__emitterProxy === handler) {\n            self.eventsListeners[event].splice(index, 1);\n          }\n        });\n      }\n    });\n    return self;\n  },\n  emit() {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (!self.eventsListeners) return self;\n    let events;\n    let data;\n    let context;\n    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      args[_key2] = arguments[_key2];\n    }\n    if (typeof args[0] === 'string' || Array.isArray(args[0])) {\n      events = args[0];\n      data = args.slice(1, args.length);\n      context = self;\n    } else {\n      events = args[0].events;\n      data = args[0].data;\n      context = args[0].context || self;\n    }\n    data.unshift(context);\n    const eventsArray = Array.isArray(events) ? events : events.split(' ');\n    eventsArray.forEach(event => {\n      if (self.eventsAnyListeners && self.eventsAnyListeners.length) {\n        self.eventsAnyListeners.forEach(eventHandler => {\n          eventHandler.apply(context, [event, ...data]);\n        });\n      }\n      if (self.eventsListeners && self.eventsListeners[event]) {\n        self.eventsListeners[event].forEach(eventHandler => {\n          eventHandler.apply(context, data);\n        });\n      }\n    });\n    return self;\n  }\n};", "map": {"version": 3, "names": ["on", "events", "handler", "priority", "self", "eventsListeners", "destroyed", "method", "split", "for<PERSON>ach", "event", "once", "once<PERSON><PERSON><PERSON>", "off", "__emitterProxy", "_len", "arguments", "length", "args", "Array", "_key", "apply", "onAny", "eventsAnyListeners", "indexOf", "offAny", "index", "splice", "<PERSON><PERSON><PERSON><PERSON>", "emit", "data", "context", "_len2", "_key2", "isArray", "slice", "unshift", "eventsArray"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/core/events-emitter.js"], "sourcesContent": ["/* eslint-disable no-underscore-dangle */\n\nexport default {\n  on(events, handler, priority) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (typeof handler !== 'function') return self;\n    const method = priority ? 'unshift' : 'push';\n    events.split(' ').forEach(event => {\n      if (!self.eventsListeners[event]) self.eventsListeners[event] = [];\n      self.eventsListeners[event][method](handler);\n    });\n    return self;\n  },\n  once(events, handler, priority) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (typeof handler !== 'function') return self;\n    function onceHandler(...args) {\n      self.off(events, onceHandler);\n      if (onceHandler.__emitterProxy) {\n        delete onceHandler.__emitterProxy;\n      }\n      handler.apply(self, args);\n    }\n    onceHandler.__emitterProxy = handler;\n    return self.on(events, onceHandler, priority);\n  },\n  onAny(handler, priority) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (typeof handler !== 'function') return self;\n    const method = priority ? 'unshift' : 'push';\n    if (self.eventsAnyListeners.indexOf(handler) < 0) {\n      self.eventsAnyListeners[method](handler);\n    }\n    return self;\n  },\n  offAny(handler) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (!self.eventsAnyListeners) return self;\n    const index = self.eventsAnyListeners.indexOf(handler);\n    if (index >= 0) {\n      self.eventsAnyListeners.splice(index, 1);\n    }\n    return self;\n  },\n  off(events, handler) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (!self.eventsListeners) return self;\n    events.split(' ').forEach(event => {\n      if (typeof handler === 'undefined') {\n        self.eventsListeners[event] = [];\n      } else if (self.eventsListeners[event]) {\n        self.eventsListeners[event].forEach((eventHandler, index) => {\n          if (eventHandler === handler || eventHandler.__emitterProxy && eventHandler.__emitterProxy === handler) {\n            self.eventsListeners[event].splice(index, 1);\n          }\n        });\n      }\n    });\n    return self;\n  },\n  emit(...args) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (!self.eventsListeners) return self;\n    let events;\n    let data;\n    let context;\n    if (typeof args[0] === 'string' || Array.isArray(args[0])) {\n      events = args[0];\n      data = args.slice(1, args.length);\n      context = self;\n    } else {\n      events = args[0].events;\n      data = args[0].data;\n      context = args[0].context || self;\n    }\n    data.unshift(context);\n    const eventsArray = Array.isArray(events) ? events : events.split(' ');\n    eventsArray.forEach(event => {\n      if (self.eventsAnyListeners && self.eventsAnyListeners.length) {\n        self.eventsAnyListeners.forEach(eventHandler => {\n          eventHandler.apply(context, [event, ...data]);\n        });\n      }\n      if (self.eventsListeners && self.eventsListeners[event]) {\n        self.eventsListeners[event].forEach(eventHandler => {\n          eventHandler.apply(context, data);\n        });\n      }\n    });\n    return self;\n  }\n};"], "mappings": "AAAA;;AAEA,eAAe;EACbA,EAAEA,CAACC,MAAM,EAAEC,OAAO,EAAEC,QAAQ,EAAE;IAC5B,MAAMC,IAAI,GAAG,IAAI;IACjB,IAAI,CAACA,IAAI,CAACC,eAAe,IAAID,IAAI,CAACE,SAAS,EAAE,OAAOF,IAAI;IACxD,IAAI,OAAOF,OAAO,KAAK,UAAU,EAAE,OAAOE,IAAI;IAC9C,MAAMG,MAAM,GAAGJ,QAAQ,GAAG,SAAS,GAAG,MAAM;IAC5CF,MAAM,CAACO,KAAK,CAAC,GAAG,CAAC,CAACC,OAAO,CAACC,KAAK,IAAI;MACjC,IAAI,CAACN,IAAI,CAACC,eAAe,CAACK,KAAK,CAAC,EAAEN,IAAI,CAACC,eAAe,CAACK,KAAK,CAAC,GAAG,EAAE;MAClEN,IAAI,CAACC,eAAe,CAACK,KAAK,CAAC,CAACH,MAAM,CAAC,CAACL,OAAO,CAAC;IAC9C,CAAC,CAAC;IACF,OAAOE,IAAI;EACb,CAAC;EACDO,IAAIA,CAACV,MAAM,EAAEC,OAAO,EAAEC,QAAQ,EAAE;IAC9B,MAAMC,IAAI,GAAG,IAAI;IACjB,IAAI,CAACA,IAAI,CAACC,eAAe,IAAID,IAAI,CAACE,SAAS,EAAE,OAAOF,IAAI;IACxD,IAAI,OAAOF,OAAO,KAAK,UAAU,EAAE,OAAOE,IAAI;IAC9C,SAASQ,WAAWA,CAAA,EAAU;MAC5BR,IAAI,CAACS,GAAG,CAACZ,MAAM,EAAEW,WAAW,CAAC;MAC7B,IAAIA,WAAW,CAACE,cAAc,EAAE;QAC9B,OAAOF,WAAW,CAACE,cAAc;MACnC;MAAC,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAJqBC,IAAI,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;QAAJF,IAAI,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;MAAA;MAK1BlB,OAAO,CAACmB,KAAK,CAACjB,IAAI,EAAEc,IAAI,CAAC;IAC3B;IACAN,WAAW,CAACE,cAAc,GAAGZ,OAAO;IACpC,OAAOE,IAAI,CAACJ,EAAE,CAACC,MAAM,EAAEW,WAAW,EAAET,QAAQ,CAAC;EAC/C,CAAC;EACDmB,KAAKA,CAACpB,OAAO,EAAEC,QAAQ,EAAE;IACvB,MAAMC,IAAI,GAAG,IAAI;IACjB,IAAI,CAACA,IAAI,CAACC,eAAe,IAAID,IAAI,CAACE,SAAS,EAAE,OAAOF,IAAI;IACxD,IAAI,OAAOF,OAAO,KAAK,UAAU,EAAE,OAAOE,IAAI;IAC9C,MAAMG,MAAM,GAAGJ,QAAQ,GAAG,SAAS,GAAG,MAAM;IAC5C,IAAIC,IAAI,CAACmB,kBAAkB,CAACC,OAAO,CAACtB,OAAO,CAAC,GAAG,CAAC,EAAE;MAChDE,IAAI,CAACmB,kBAAkB,CAAChB,MAAM,CAAC,CAACL,OAAO,CAAC;IAC1C;IACA,OAAOE,IAAI;EACb,CAAC;EACDqB,MAAMA,CAACvB,OAAO,EAAE;IACd,MAAME,IAAI,GAAG,IAAI;IACjB,IAAI,CAACA,IAAI,CAACC,eAAe,IAAID,IAAI,CAACE,SAAS,EAAE,OAAOF,IAAI;IACxD,IAAI,CAACA,IAAI,CAACmB,kBAAkB,EAAE,OAAOnB,IAAI;IACzC,MAAMsB,KAAK,GAAGtB,IAAI,CAACmB,kBAAkB,CAACC,OAAO,CAACtB,OAAO,CAAC;IACtD,IAAIwB,KAAK,IAAI,CAAC,EAAE;MACdtB,IAAI,CAACmB,kBAAkB,CAACI,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;IAC1C;IACA,OAAOtB,IAAI;EACb,CAAC;EACDS,GAAGA,CAACZ,MAAM,EAAEC,OAAO,EAAE;IACnB,MAAME,IAAI,GAAG,IAAI;IACjB,IAAI,CAACA,IAAI,CAACC,eAAe,IAAID,IAAI,CAACE,SAAS,EAAE,OAAOF,IAAI;IACxD,IAAI,CAACA,IAAI,CAACC,eAAe,EAAE,OAAOD,IAAI;IACtCH,MAAM,CAACO,KAAK,CAAC,GAAG,CAAC,CAACC,OAAO,CAACC,KAAK,IAAI;MACjC,IAAI,OAAOR,OAAO,KAAK,WAAW,EAAE;QAClCE,IAAI,CAACC,eAAe,CAACK,KAAK,CAAC,GAAG,EAAE;MAClC,CAAC,MAAM,IAAIN,IAAI,CAACC,eAAe,CAACK,KAAK,CAAC,EAAE;QACtCN,IAAI,CAACC,eAAe,CAACK,KAAK,CAAC,CAACD,OAAO,CAAC,CAACmB,YAAY,EAAEF,KAAK,KAAK;UAC3D,IAAIE,YAAY,KAAK1B,OAAO,IAAI0B,YAAY,CAACd,cAAc,IAAIc,YAAY,CAACd,cAAc,KAAKZ,OAAO,EAAE;YACtGE,IAAI,CAACC,eAAe,CAACK,KAAK,CAAC,CAACiB,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;UAC9C;QACF,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IACF,OAAOtB,IAAI;EACb,CAAC;EACDyB,IAAIA,CAAA,EAAU;IACZ,MAAMzB,IAAI,GAAG,IAAI;IACjB,IAAI,CAACA,IAAI,CAACC,eAAe,IAAID,IAAI,CAACE,SAAS,EAAE,OAAOF,IAAI;IACxD,IAAI,CAACA,IAAI,CAACC,eAAe,EAAE,OAAOD,IAAI;IACtC,IAAIH,MAAM;IACV,IAAI6B,IAAI;IACR,IAAIC,OAAO;IAAC,SAAAC,KAAA,GAAAhB,SAAA,CAAAC,MAAA,EANNC,IAAI,OAAAC,KAAA,CAAAa,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAJf,IAAI,CAAAe,KAAA,IAAAjB,SAAA,CAAAiB,KAAA;IAAA;IAOV,IAAI,OAAOf,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,IAAIC,KAAK,CAACe,OAAO,CAAChB,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;MACzDjB,MAAM,GAAGiB,IAAI,CAAC,CAAC,CAAC;MAChBY,IAAI,GAAGZ,IAAI,CAACiB,KAAK,CAAC,CAAC,EAAEjB,IAAI,CAACD,MAAM,CAAC;MACjCc,OAAO,GAAG3B,IAAI;IAChB,CAAC,MAAM;MACLH,MAAM,GAAGiB,IAAI,CAAC,CAAC,CAAC,CAACjB,MAAM;MACvB6B,IAAI,GAAGZ,IAAI,CAAC,CAAC,CAAC,CAACY,IAAI;MACnBC,OAAO,GAAGb,IAAI,CAAC,CAAC,CAAC,CAACa,OAAO,IAAI3B,IAAI;IACnC;IACA0B,IAAI,CAACM,OAAO,CAACL,OAAO,CAAC;IACrB,MAAMM,WAAW,GAAGlB,KAAK,CAACe,OAAO,CAACjC,MAAM,CAAC,GAAGA,MAAM,GAAGA,MAAM,CAACO,KAAK,CAAC,GAAG,CAAC;IACtE6B,WAAW,CAAC5B,OAAO,CAACC,KAAK,IAAI;MAC3B,IAAIN,IAAI,CAACmB,kBAAkB,IAAInB,IAAI,CAACmB,kBAAkB,CAACN,MAAM,EAAE;QAC7Db,IAAI,CAACmB,kBAAkB,CAACd,OAAO,CAACmB,YAAY,IAAI;UAC9CA,YAAY,CAACP,KAAK,CAACU,OAAO,EAAE,CAACrB,KAAK,EAAE,GAAGoB,IAAI,CAAC,CAAC;QAC/C,CAAC,CAAC;MACJ;MACA,IAAI1B,IAAI,CAACC,eAAe,IAAID,IAAI,CAACC,eAAe,CAACK,KAAK,CAAC,EAAE;QACvDN,IAAI,CAACC,eAAe,CAACK,KAAK,CAAC,CAACD,OAAO,CAACmB,YAAY,IAAI;UAClDA,YAAY,CAACP,KAAK,CAACU,OAAO,EAAED,IAAI,CAAC;QACnC,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IACF,OAAO1B,IAAI;EACb;AACF,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}