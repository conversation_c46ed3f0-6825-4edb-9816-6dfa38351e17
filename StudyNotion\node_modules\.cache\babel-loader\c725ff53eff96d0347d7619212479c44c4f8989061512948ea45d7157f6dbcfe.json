{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\temp\\\\StudyNotion\\\\src\\\\components\\\\core\\\\Dashboard\\\\AddCourse\\\\CourseInformation\\\\ChipInput.jsx\",\n  _s = $RefreshSig$();\n// Importing React hook for managing component state\nimport { useEffect, useState } from \"react\";\n// Importing React icon component\nimport { MdClose } from \"react-icons/md\";\nimport { useSelector } from \"react-redux\";\n\n// Defining a functional component ChipInput\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function ChipInput(_ref) {\n  _s();\n  let {\n    // Props to be passed to the component\n    label,\n    name,\n    placeholder,\n    register,\n    errors,\n    setValue,\n    getValues\n  } = _ref;\n  const {\n    editCourse,\n    course\n  } = useSelector(state => state.course);\n\n  // Setting up state for managing chips array\n  const [chips, setChips] = useState([]);\n  useEffect(() => {\n    if (editCourse) {\n      // console.log(course)\n      setChips(course === null || course === void 0 ? void 0 : course.tag);\n    }\n    register(name, {\n      required: true,\n      validate: value => value.length > 0\n    });\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  useEffect(() => {\n    setValue(name, chips);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [chips]);\n\n  // Function to handle user input when chips are added\n  const handleKeyDown = event => {\n    // Check if user presses \"Enter\" or \",\"\n    if (event.key === \"Enter\" || event.key === \",\") {\n      // Prevent the default behavior of the event\n      event.preventDefault();\n      // Get the input value and remove any leading/trailing spaces\n      const chipValue = event.target.value.trim();\n      // Check if the input value exists and is not already in the chips array\n      if (chipValue && !chips.includes(chipValue)) {\n        // Add the chip to the array and clear the input\n        const newChips = [...chips, chipValue];\n        setChips(newChips);\n        event.target.value = \"\";\n      }\n    }\n  };\n\n  // Function to handle deletion of a chip\n  const handleDeleteChip = chipIndex => {\n    // Filter the chips array to remove the chip with the given index\n    const newChips = chips.filter((_, index) => index !== chipIndex);\n    setChips(newChips);\n  };\n\n  // Render the component\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col space-y-2\",\n    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n      className: \"text-sm text-richblack-5\",\n      htmlFor: name,\n      children: [label, \" \", /*#__PURE__*/_jsxDEV(\"sup\", {\n        className: \"text-pink-200\",\n        children: \"*\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex w-full flex-wrap gap-y-2\",\n      children: [chips.map((chip, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"m-1 flex items-center rounded-full bg-yellow-400 px-2 py-1 text-sm text-richblack-5\",\n        children: [chip, /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          className: \"ml-2 focus:outline-none\",\n          onClick: () => handleDeleteChip(index),\n          children: /*#__PURE__*/_jsxDEV(MdClose, {\n            className: \"text-sm\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 13\n        }, this)]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 11\n      }, this)), /*#__PURE__*/_jsxDEV(\"input\", {\n        id: name,\n        name: name,\n        type: \"text\",\n        placeholder: placeholder,\n        onKeyDown: handleKeyDown,\n        className: \"form-style w-full\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 7\n    }, this), errors[name] && /*#__PURE__*/_jsxDEV(\"span\", {\n      className: \"ml-2 text-xs tracking-wide text-pink-200\",\n      children: [label, \" is required\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 64,\n    columnNumber: 5\n  }, this);\n}\n_s(ChipInput, \"QYB7px7gk8fUH/9IiSSwDl2smHk=\", false, function () {\n  return [useSelector];\n});\n_c = ChipInput;\nvar _c;\n$RefreshReg$(_c, \"ChipInput\");", "map": {"version": 3, "names": ["useEffect", "useState", "MdClose", "useSelector", "jsxDEV", "_jsxDEV", "ChipInput", "_ref", "_s", "label", "name", "placeholder", "register", "errors", "setValue", "getV<PERSON>ues", "editCourse", "course", "state", "chips", "setChips", "tag", "required", "validate", "value", "length", "handleKeyDown", "event", "key", "preventDefault", "chipValue", "target", "trim", "includes", "newChips", "handleDeleteChip", "chipIndex", "filter", "_", "index", "className", "children", "htmlFor", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "chip", "type", "onClick", "id", "onKeyDown", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/src/components/core/Dashboard/AddCourse/CourseInformation/ChipInput.jsx"], "sourcesContent": ["// Importing React hook for managing component state\r\nimport { useEffect, useState } from \"react\"\r\n// Importing React icon component\r\nimport { MdClose } from \"react-icons/md\"\r\nimport { useSelector } from \"react-redux\"\r\n\r\n// Defining a functional component ChipInput\r\nexport default function ChipInput({\r\n  // Props to be passed to the component\r\n  label,\r\n  name,\r\n  placeholder,\r\n  register,\r\n  errors,\r\n  setValue,\r\n  getValues,\r\n}) {\r\n  const { editCourse, course } = useSelector((state) => state.course)\r\n\r\n  // Setting up state for managing chips array\r\n  const [chips, setChips] = useState([])\r\n\r\n  useEffect(() => {\r\n    if (editCourse) {\r\n      // console.log(course)\r\n      setChips(course?.tag)\r\n    }\r\n    register(name, { required: true, validate: (value) => value.length > 0 })\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [])\r\n\r\n  useEffect(() => {\r\n    setValue(name, chips)\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [chips])\r\n\r\n  // Function to handle user input when chips are added\r\n  const handleKeyDown = (event) => {\r\n    // Check if user presses \"Enter\" or \",\"\r\n    if (event.key === \"Enter\" || event.key === \",\") {\r\n      // Prevent the default behavior of the event\r\n      event.preventDefault()\r\n      // Get the input value and remove any leading/trailing spaces\r\n      const chipValue = event.target.value.trim()\r\n      // Check if the input value exists and is not already in the chips array\r\n      if (chipValue && !chips.includes(chipValue)) {\r\n        // Add the chip to the array and clear the input\r\n        const newChips = [...chips, chipValue]\r\n        setChips(newChips)\r\n        event.target.value = \"\"\r\n      }\r\n    }\r\n  }\r\n\r\n  // Function to handle deletion of a chip\r\n  const handleDeleteChip = (chipIndex) => {\r\n    // Filter the chips array to remove the chip with the given index\r\n    const newChips = chips.filter((_, index) => index !== chipIndex)\r\n    setChips(newChips)\r\n  }\r\n\r\n  // Render the component\r\n  return (\r\n    <div className=\"flex flex-col space-y-2\">\r\n      {/* Render the label for the input */}\r\n      <label className=\"text-sm text-richblack-5\" htmlFor={name}>\r\n        {label} <sup className=\"text-pink-200\">*</sup>\r\n      </label>\r\n      {/* Render the chips and input */}\r\n      <div className=\"flex w-full flex-wrap gap-y-2\">\r\n        {/* Map over the chips array and render each chip */}\r\n        {chips.map((chip, index) => (\r\n          <div\r\n            key={index}\r\n            className=\"m-1 flex items-center rounded-full bg-yellow-400 px-2 py-1 text-sm text-richblack-5\"\r\n          >\r\n            {/* Render the chip value */}\r\n            {chip}\r\n            {/* Render the button to delete the chip */}\r\n            <button\r\n              type=\"button\"\r\n              className=\"ml-2 focus:outline-none\"\r\n              onClick={() => handleDeleteChip(index)}\r\n            >\r\n              <MdClose className=\"text-sm\" />\r\n            </button>\r\n          </div>\r\n        ))}\r\n        {/* Render the input for adding new chips */}\r\n        <input\r\n          id={name}\r\n          name={name}\r\n          type=\"text\"\r\n          placeholder={placeholder}\r\n          onKeyDown={handleKeyDown}\r\n          className=\"form-style w-full\"\r\n        />\r\n      </div>\r\n      {/* Render an error message if the input is required and not filled */}\r\n      {errors[name] && (\r\n        <span className=\"ml-2 text-xs tracking-wide text-pink-200\">\r\n          {label} is required\r\n        </span>\r\n      )}\r\n    </div>\r\n  )\r\n}"], "mappings": ";;AAAA;AACA,SAASA,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C;AACA,SAASC,OAAO,QAAQ,gBAAgB;AACxC,SAASC,WAAW,QAAQ,aAAa;;AAEzC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,eAAe,SAASC,SAASA,CAAAC,IAAA,EAS9B;EAAAC,EAAA;EAAA,IAT+B;IAChC;IACAC,KAAK;IACLC,IAAI;IACJC,WAAW;IACXC,QAAQ;IACRC,MAAM;IACNC,QAAQ;IACRC;EACF,CAAC,GAAAR,IAAA;EACC,MAAM;IAAES,UAAU;IAAEC;EAAO,CAAC,GAAGd,WAAW,CAAEe,KAAK,IAAKA,KAAK,CAACD,MAAM,CAAC;;EAEnE;EACA,MAAM,CAACE,KAAK,EAAEC,QAAQ,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAEtCD,SAAS,CAAC,MAAM;IACd,IAAIgB,UAAU,EAAE;MACd;MACAI,QAAQ,CAACH,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEI,GAAG,CAAC;IACvB;IACAT,QAAQ,CAACF,IAAI,EAAE;MAAEY,QAAQ,EAAE,IAAI;MAAEC,QAAQ,EAAGC,KAAK,IAAKA,KAAK,CAACC,MAAM,GAAG;IAAE,CAAC,CAAC;IACzE;EACF,CAAC,EAAE,EAAE,CAAC;EAENzB,SAAS,CAAC,MAAM;IACdc,QAAQ,CAACJ,IAAI,EAAES,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;;EAEX;EACA,MAAMO,aAAa,GAAIC,KAAK,IAAK;IAC/B;IACA,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,IAAID,KAAK,CAACC,GAAG,KAAK,GAAG,EAAE;MAC9C;MACAD,KAAK,CAACE,cAAc,EAAE;MACtB;MACA,MAAMC,SAAS,GAAGH,KAAK,CAACI,MAAM,CAACP,KAAK,CAACQ,IAAI,EAAE;MAC3C;MACA,IAAIF,SAAS,IAAI,CAACX,KAAK,CAACc,QAAQ,CAACH,SAAS,CAAC,EAAE;QAC3C;QACA,MAAMI,QAAQ,GAAG,CAAC,GAAGf,KAAK,EAAEW,SAAS,CAAC;QACtCV,QAAQ,CAACc,QAAQ,CAAC;QAClBP,KAAK,CAACI,MAAM,CAACP,KAAK,GAAG,EAAE;MACzB;IACF;EACF,CAAC;;EAED;EACA,MAAMW,gBAAgB,GAAIC,SAAS,IAAK;IACtC;IACA,MAAMF,QAAQ,GAAGf,KAAK,CAACkB,MAAM,CAAC,CAACC,CAAC,EAAEC,KAAK,KAAKA,KAAK,KAAKH,SAAS,CAAC;IAChEhB,QAAQ,CAACc,QAAQ,CAAC;EACpB,CAAC;;EAED;EACA,oBACE7B,OAAA;IAAKmC,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBAEtCpC,OAAA;MAAOmC,SAAS,EAAC,0BAA0B;MAACE,OAAO,EAAEhC,IAAK;MAAA+B,QAAA,GACvDhC,KAAK,EAAC,GAAC,eAAAJ,OAAA;QAAKmC,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAAM;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACxC,eAERzC,OAAA;MAAKmC,SAAS,EAAC,+BAA+B;MAAAC,QAAA,GAE3CtB,KAAK,CAAC4B,GAAG,CAAC,CAACC,IAAI,EAAET,KAAK,kBACrBlC,OAAA;QAEEmC,SAAS,EAAC,qFAAqF;QAAAC,QAAA,GAG9FO,IAAI,eAEL3C,OAAA;UACE4C,IAAI,EAAC,QAAQ;UACbT,SAAS,EAAC,yBAAyB;UACnCU,OAAO,EAAEA,CAAA,KAAMf,gBAAgB,CAACI,KAAK,CAAE;UAAAE,QAAA,eAEvCpC,OAAA,CAACH,OAAO;YAACsC,SAAS,EAAC;UAAS;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAG;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACxB;MAAA,GAZJP,KAAK;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAcb,CAAC,eAEFzC,OAAA;QACE8C,EAAE,EAAEzC,IAAK;QACTA,IAAI,EAAEA,IAAK;QACXuC,IAAI,EAAC,MAAM;QACXtC,WAAW,EAAEA,WAAY;QACzByC,SAAS,EAAE1B,aAAc;QACzBc,SAAS,EAAC;MAAmB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAC7B;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACE,EAELjC,MAAM,CAACH,IAAI,CAAC,iBACXL,OAAA;MAAMmC,SAAS,EAAC,0CAA0C;MAAAC,QAAA,GACvDhC,KAAK,EAAC,cACT;IAAA;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACD;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACG;AAEV;AAACtC,EAAA,CAnGuBF,SAAS;EAAA,QAUAH,WAAW;AAAA;AAAAkD,EAAA,GAVpB/C,SAAS;AAAA,IAAA+C,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}