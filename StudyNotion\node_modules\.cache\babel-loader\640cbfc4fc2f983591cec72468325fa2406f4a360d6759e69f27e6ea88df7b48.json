{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\");\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar _classCallCheck2 = _interopRequireDefault(require(\"@babel/runtime/helpers/classCallCheck\"));\nvar _createClass2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createClass\"));\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"@babel/runtime/helpers/possibleConstructorReturn\"));\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"@babel/runtime/helpers/getPrototypeOf\"));\nvar _assertThisInitialized2 = _interopRequireDefault(require(\"@babel/runtime/helpers/assertThisInitialized\"));\nvar _inherits2 = _interopRequireDefault(require(\"@babel/runtime/helpers/inherits\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _react = _interopRequireWildcard(require(\"react\"));\nvar _classnames = _interopRequireDefault(require(\"classnames\"));\nvar Dom = _interopRequireWildcard(require(\"../../utils/dom\"));\nvar _SeekBar = _interopRequireDefault(require(\"./SeekBar\"));\nvar propTypes = {\n  player: _propTypes[\"default\"].object,\n  className: _propTypes[\"default\"].string\n};\nvar ProgressControl = /*#__PURE__*/\nfunction (_Component) {\n  (0, _inherits2[\"default\"])(ProgressControl, _Component);\n  function ProgressControl(props, context) {\n    var _this;\n    (0, _classCallCheck2[\"default\"])(this, ProgressControl);\n    _this = (0, _possibleConstructorReturn2[\"default\"])(this, (0, _getPrototypeOf2[\"default\"])(ProgressControl).call(this, props, context));\n    _this.state = {\n      mouseTime: {\n        time: null,\n        position: 0\n      }\n    };\n    _this.handleMouseMoveThrottle = _this.handleMouseMove.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    return _this;\n  }\n  (0, _createClass2[\"default\"])(ProgressControl, [{\n    key: \"handleMouseMove\",\n    value: function handleMouseMove(event) {\n      if (!event.pageX) {\n        return;\n      }\n      var duration = this.props.player.duration;\n      var node = this.seekBar;\n      var newTime = Dom.getPointerPosition(node, event).x * duration;\n      var position = event.pageX - Dom.findElPosition(node).left;\n      this.setState({\n        mouseTime: {\n          time: newTime,\n          position: position\n        }\n      });\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this2 = this;\n      var className = this.props.className;\n      return _react[\"default\"].createElement(\"div\", {\n        onMouseMove: this.handleMouseMoveThrottle,\n        className: (0, _classnames[\"default\"])('video-react-progress-control video-react-control', className)\n      }, _react[\"default\"].createElement(_SeekBar[\"default\"], (0, _extends2[\"default\"])({\n        mouseTime: this.state.mouseTime,\n        ref: function ref(c) {\n          _this2.seekBar = c;\n        }\n      }, this.props)));\n    }\n  }]);\n  return ProgressControl;\n}(_react.Component);\nexports[\"default\"] = ProgressControl;\nProgressControl.propTypes = propTypes;\nProgressControl.displayName = 'ProgressControl';", "map": {"version": 3, "names": ["_interopRequireWildcard", "require", "_interopRequireDefault", "Object", "defineProperty", "exports", "value", "_extends2", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_assertThisInitialized2", "_inherits2", "_propTypes", "_react", "_classnames", "Dom", "_SeekBar", "propTypes", "player", "object", "className", "string", "ProgressControl", "_Component", "props", "context", "_this", "call", "state", "mouseTime", "time", "position", "handleMouseMoveThrottle", "handleMouseMove", "bind", "key", "event", "pageX", "duration", "node", "<PERSON><PERSON><PERSON>", "newTime", "getPointerPosition", "x", "findElPosition", "left", "setState", "render", "_this2", "createElement", "onMouseMove", "ref", "c", "Component", "displayName"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/video-react/lib/components/control-bar/ProgressControl.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\");\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\n\nvar _classCallCheck2 = _interopRequireDefault(require(\"@babel/runtime/helpers/classCallCheck\"));\n\nvar _createClass2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createClass\"));\n\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"@babel/runtime/helpers/possibleConstructorReturn\"));\n\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"@babel/runtime/helpers/getPrototypeOf\"));\n\nvar _assertThisInitialized2 = _interopRequireDefault(require(\"@babel/runtime/helpers/assertThisInitialized\"));\n\nvar _inherits2 = _interopRequireDefault(require(\"@babel/runtime/helpers/inherits\"));\n\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\n\nvar _react = _interopRequireWildcard(require(\"react\"));\n\nvar _classnames = _interopRequireDefault(require(\"classnames\"));\n\nvar Dom = _interopRequireWildcard(require(\"../../utils/dom\"));\n\nvar _SeekBar = _interopRequireDefault(require(\"./SeekBar\"));\n\nvar propTypes = {\n  player: _propTypes[\"default\"].object,\n  className: _propTypes[\"default\"].string\n};\n\nvar ProgressControl =\n/*#__PURE__*/\nfunction (_Component) {\n  (0, _inherits2[\"default\"])(ProgressControl, _Component);\n\n  function ProgressControl(props, context) {\n    var _this;\n\n    (0, _classCallCheck2[\"default\"])(this, ProgressControl);\n    _this = (0, _possibleConstructorReturn2[\"default\"])(this, (0, _getPrototypeOf2[\"default\"])(ProgressControl).call(this, props, context));\n    _this.state = {\n      mouseTime: {\n        time: null,\n        position: 0\n      }\n    };\n    _this.handleMouseMoveThrottle = _this.handleMouseMove.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    return _this;\n  }\n\n  (0, _createClass2[\"default\"])(ProgressControl, [{\n    key: \"handleMouseMove\",\n    value: function handleMouseMove(event) {\n      if (!event.pageX) {\n        return;\n      }\n\n      var duration = this.props.player.duration;\n      var node = this.seekBar;\n      var newTime = Dom.getPointerPosition(node, event).x * duration;\n      var position = event.pageX - Dom.findElPosition(node).left;\n      this.setState({\n        mouseTime: {\n          time: newTime,\n          position: position\n        }\n      });\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this2 = this;\n\n      var className = this.props.className;\n      return _react[\"default\"].createElement(\"div\", {\n        onMouseMove: this.handleMouseMoveThrottle,\n        className: (0, _classnames[\"default\"])('video-react-progress-control video-react-control', className)\n      }, _react[\"default\"].createElement(_SeekBar[\"default\"], (0, _extends2[\"default\"])({\n        mouseTime: this.state.mouseTime,\n        ref: function ref(c) {\n          _this2.seekBar = c;\n        }\n      }, this.props)));\n    }\n  }]);\n  return ProgressControl;\n}(_react.Component);\n\nexports[\"default\"] = ProgressControl;\nProgressControl.propTypes = propTypes;\nProgressControl.displayName = 'ProgressControl';"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,uBAAuB,GAAGC,OAAO,CAAC,+CAA+C,CAAC;AAEtF,IAAIC,sBAAsB,GAAGD,OAAO,CAAC,8CAA8C,CAAC;AAEpFE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;AAE3B,IAAIE,SAAS,GAAGL,sBAAsB,CAACD,OAAO,CAAC,gCAAgC,CAAC,CAAC;AAEjF,IAAIO,gBAAgB,GAAGN,sBAAsB,CAACD,OAAO,CAAC,uCAAuC,CAAC,CAAC;AAE/F,IAAIQ,aAAa,GAAGP,sBAAsB,CAACD,OAAO,CAAC,oCAAoC,CAAC,CAAC;AAEzF,IAAIS,2BAA2B,GAAGR,sBAAsB,CAACD,OAAO,CAAC,kDAAkD,CAAC,CAAC;AAErH,IAAIU,gBAAgB,GAAGT,sBAAsB,CAACD,OAAO,CAAC,uCAAuC,CAAC,CAAC;AAE/F,IAAIW,uBAAuB,GAAGV,sBAAsB,CAACD,OAAO,CAAC,8CAA8C,CAAC,CAAC;AAE7G,IAAIY,UAAU,GAAGX,sBAAsB,CAACD,OAAO,CAAC,iCAAiC,CAAC,CAAC;AAEnF,IAAIa,UAAU,GAAGZ,sBAAsB,CAACD,OAAO,CAAC,YAAY,CAAC,CAAC;AAE9D,IAAIc,MAAM,GAAGf,uBAAuB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AAEtD,IAAIe,WAAW,GAAGd,sBAAsB,CAACD,OAAO,CAAC,YAAY,CAAC,CAAC;AAE/D,IAAIgB,GAAG,GAAGjB,uBAAuB,CAACC,OAAO,CAAC,iBAAiB,CAAC,CAAC;AAE7D,IAAIiB,QAAQ,GAAGhB,sBAAsB,CAACD,OAAO,CAAC,WAAW,CAAC,CAAC;AAE3D,IAAIkB,SAAS,GAAG;EACdC,MAAM,EAAEN,UAAU,CAAC,SAAS,CAAC,CAACO,MAAM;EACpCC,SAAS,EAAER,UAAU,CAAC,SAAS,CAAC,CAACS;AACnC,CAAC;AAED,IAAIC,eAAe,GACnB;AACA,UAAUC,UAAU,EAAE;EACpB,CAAC,CAAC,EAAEZ,UAAU,CAAC,SAAS,CAAC,EAAEW,eAAe,EAAEC,UAAU,CAAC;EAEvD,SAASD,eAAeA,CAACE,KAAK,EAAEC,OAAO,EAAE;IACvC,IAAIC,KAAK;IAET,CAAC,CAAC,EAAEpB,gBAAgB,CAAC,SAAS,CAAC,EAAE,IAAI,EAAEgB,eAAe,CAAC;IACvDI,KAAK,GAAG,CAAC,CAAC,EAAElB,2BAA2B,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAEC,gBAAgB,CAAC,SAAS,CAAC,EAAEa,eAAe,CAAC,CAACK,IAAI,CAAC,IAAI,EAAEH,KAAK,EAAEC,OAAO,CAAC,CAAC;IACvIC,KAAK,CAACE,KAAK,GAAG;MACZC,SAAS,EAAE;QACTC,IAAI,EAAE,IAAI;QACVC,QAAQ,EAAE;MACZ;IACF,CAAC;IACDL,KAAK,CAACM,uBAAuB,GAAGN,KAAK,CAACO,eAAe,CAACC,IAAI,CAAC,CAAC,CAAC,EAAExB,uBAAuB,CAAC,SAAS,CAAC,EAAEgB,KAAK,CAAC,CAAC;IAC1G,OAAOA,KAAK;EACd;EAEA,CAAC,CAAC,EAAEnB,aAAa,CAAC,SAAS,CAAC,EAAEe,eAAe,EAAE,CAAC;IAC9Ca,GAAG,EAAE,iBAAiB;IACtB/B,KAAK,EAAE,SAAS6B,eAAeA,CAACG,KAAK,EAAE;MACrC,IAAI,CAACA,KAAK,CAACC,KAAK,EAAE;QAChB;MACF;MAEA,IAAIC,QAAQ,GAAG,IAAI,CAACd,KAAK,CAACN,MAAM,CAACoB,QAAQ;MACzC,IAAIC,IAAI,GAAG,IAAI,CAACC,OAAO;MACvB,IAAIC,OAAO,GAAG1B,GAAG,CAAC2B,kBAAkB,CAACH,IAAI,EAAEH,KAAK,CAAC,CAACO,CAAC,GAAGL,QAAQ;MAC9D,IAAIP,QAAQ,GAAGK,KAAK,CAACC,KAAK,GAAGtB,GAAG,CAAC6B,cAAc,CAACL,IAAI,CAAC,CAACM,IAAI;MAC1D,IAAI,CAACC,QAAQ,CAAC;QACZjB,SAAS,EAAE;UACTC,IAAI,EAAEW,OAAO;UACbV,QAAQ,EAAEA;QACZ;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDI,GAAG,EAAE,QAAQ;IACb/B,KAAK,EAAE,SAAS2C,MAAMA,CAAA,EAAG;MACvB,IAAIC,MAAM,GAAG,IAAI;MAEjB,IAAI5B,SAAS,GAAG,IAAI,CAACI,KAAK,CAACJ,SAAS;MACpC,OAAOP,MAAM,CAAC,SAAS,CAAC,CAACoC,aAAa,CAAC,KAAK,EAAE;QAC5CC,WAAW,EAAE,IAAI,CAAClB,uBAAuB;QACzCZ,SAAS,EAAE,CAAC,CAAC,EAAEN,WAAW,CAAC,SAAS,CAAC,EAAE,kDAAkD,EAAEM,SAAS;MACtG,CAAC,EAAEP,MAAM,CAAC,SAAS,CAAC,CAACoC,aAAa,CAACjC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAEX,SAAS,CAAC,SAAS,CAAC,EAAE;QAChFwB,SAAS,EAAE,IAAI,CAACD,KAAK,CAACC,SAAS;QAC/BsB,GAAG,EAAE,SAASA,GAAGA,CAACC,CAAC,EAAE;UACnBJ,MAAM,CAACR,OAAO,GAAGY,CAAC;QACpB;MACF,CAAC,EAAE,IAAI,CAAC5B,KAAK,CAAC,CAAC,CAAC;IAClB;EACF,CAAC,CAAC,CAAC;EACH,OAAOF,eAAe;AACxB,CAAC,CAACT,MAAM,CAACwC,SAAS,CAAC;AAEnBlD,OAAO,CAAC,SAAS,CAAC,GAAGmB,eAAe;AACpCA,eAAe,CAACL,SAAS,GAAGA,SAAS;AACrCK,eAAe,CAACgC,WAAW,GAAG,iBAAiB"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}