{"ast": null, "code": "import { extend } from '../../shared/utils.js';\nconst isGridEnabled = (swiper, params) => {\n  return swiper.grid && params.grid && params.grid.rows > 1;\n};\nexport default function setBreakpoint() {\n  const swiper = this;\n  const {\n    realIndex,\n    initialized,\n    params,\n    el\n  } = swiper;\n  const breakpoints = params.breakpoints;\n  if (!breakpoints || breakpoints && Object.keys(breakpoints).length === 0) return;\n\n  // Get breakpoint for window width and update parameters\n  const breakpoint = swiper.getBreakpoint(breakpoints, swiper.params.breakpointsBase, swiper.el);\n  if (!breakpoint || swiper.currentBreakpoint === breakpoint) return;\n  const breakpointOnlyParams = breakpoint in breakpoints ? breakpoints[breakpoint] : undefined;\n  const breakpointParams = breakpointOnlyParams || swiper.originalParams;\n  const wasMultiRow = isGridEnabled(swiper, params);\n  const isMultiRow = isGridEnabled(swiper, breakpointParams);\n  const wasEnabled = params.enabled;\n  if (wasMultiRow && !isMultiRow) {\n    el.classList.remove(`${params.containerModifierClass}grid`, `${params.containerModifierClass}grid-column`);\n    swiper.emitContainerClasses();\n  } else if (!wasMultiRow && isMultiRow) {\n    el.classList.add(`${params.containerModifierClass}grid`);\n    if (breakpointParams.grid.fill && breakpointParams.grid.fill === 'column' || !breakpointParams.grid.fill && params.grid.fill === 'column') {\n      el.classList.add(`${params.containerModifierClass}grid-column`);\n    }\n    swiper.emitContainerClasses();\n  }\n\n  // Toggle navigation, pagination, scrollbar\n  ['navigation', 'pagination', 'scrollbar'].forEach(prop => {\n    if (typeof breakpointParams[prop] === 'undefined') return;\n    const wasModuleEnabled = params[prop] && params[prop].enabled;\n    const isModuleEnabled = breakpointParams[prop] && breakpointParams[prop].enabled;\n    if (wasModuleEnabled && !isModuleEnabled) {\n      swiper[prop].disable();\n    }\n    if (!wasModuleEnabled && isModuleEnabled) {\n      swiper[prop].enable();\n    }\n  });\n  const directionChanged = breakpointParams.direction && breakpointParams.direction !== params.direction;\n  const needsReLoop = params.loop && (breakpointParams.slidesPerView !== params.slidesPerView || directionChanged);\n  if (directionChanged && initialized) {\n    swiper.changeDirection();\n  }\n  extend(swiper.params, breakpointParams);\n  const isEnabled = swiper.params.enabled;\n  Object.assign(swiper, {\n    allowTouchMove: swiper.params.allowTouchMove,\n    allowSlideNext: swiper.params.allowSlideNext,\n    allowSlidePrev: swiper.params.allowSlidePrev\n  });\n  if (wasEnabled && !isEnabled) {\n    swiper.disable();\n  } else if (!wasEnabled && isEnabled) {\n    swiper.enable();\n  }\n  swiper.currentBreakpoint = breakpoint;\n  swiper.emit('_beforeBreakpoint', breakpointParams);\n  if (needsReLoop && initialized) {\n    swiper.loopDestroy();\n    swiper.loopCreate(realIndex);\n    swiper.updateSlides();\n  }\n  swiper.emit('breakpoint', breakpointParams);\n}", "map": {"version": 3, "names": ["extend", "isGridEnabled", "swiper", "params", "grid", "rows", "setBreakpoint", "realIndex", "initialized", "el", "breakpoints", "Object", "keys", "length", "breakpoint", "getBreakpoint", "breakpointsBase", "currentBreakpoint", "breakpoint<PERSON>nly<PERSON><PERSON><PERSON>", "undefined", "breakpointP<PERSON>ms", "originalParams", "wasMultiRow", "isMultiRow", "wasEnabled", "enabled", "classList", "remove", "containerModifierClass", "emitContainerClasses", "add", "fill", "for<PERSON>ach", "prop", "wasModuleEnabled", "isModuleEnabled", "disable", "enable", "directionChanged", "direction", "needsReLoop", "loop", "<PERSON><PERSON><PERSON><PERSON>iew", "changeDirection", "isEnabled", "assign", "allowTouchMove", "allowSlideNext", "allowSlidePrev", "emit", "loop<PERSON><PERSON><PERSON>", "loopCreate", "updateSlides"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/core/breakpoints/setBreakpoint.js"], "sourcesContent": ["import { extend } from '../../shared/utils.js';\nconst isGridEnabled = (swiper, params) => {\n  return swiper.grid && params.grid && params.grid.rows > 1;\n};\nexport default function setBreakpoint() {\n  const swiper = this;\n  const {\n    realIndex,\n    initialized,\n    params,\n    el\n  } = swiper;\n  const breakpoints = params.breakpoints;\n  if (!breakpoints || breakpoints && Object.keys(breakpoints).length === 0) return;\n\n  // Get breakpoint for window width and update parameters\n  const breakpoint = swiper.getBreakpoint(breakpoints, swiper.params.breakpointsBase, swiper.el);\n  if (!breakpoint || swiper.currentBreakpoint === breakpoint) return;\n  const breakpointOnlyParams = breakpoint in breakpoints ? breakpoints[breakpoint] : undefined;\n  const breakpointParams = breakpointOnlyParams || swiper.originalParams;\n  const wasMultiRow = isGridEnabled(swiper, params);\n  const isMultiRow = isGridEnabled(swiper, breakpointParams);\n  const wasEnabled = params.enabled;\n  if (wasMultiRow && !isMultiRow) {\n    el.classList.remove(`${params.containerModifierClass}grid`, `${params.containerModifierClass}grid-column`);\n    swiper.emitContainerClasses();\n  } else if (!wasMultiRow && isMultiRow) {\n    el.classList.add(`${params.containerModifierClass}grid`);\n    if (breakpointParams.grid.fill && breakpointParams.grid.fill === 'column' || !breakpointParams.grid.fill && params.grid.fill === 'column') {\n      el.classList.add(`${params.containerModifierClass}grid-column`);\n    }\n    swiper.emitContainerClasses();\n  }\n\n  // Toggle navigation, pagination, scrollbar\n  ['navigation', 'pagination', 'scrollbar'].forEach(prop => {\n    if (typeof breakpointParams[prop] === 'undefined') return;\n    const wasModuleEnabled = params[prop] && params[prop].enabled;\n    const isModuleEnabled = breakpointParams[prop] && breakpointParams[prop].enabled;\n    if (wasModuleEnabled && !isModuleEnabled) {\n      swiper[prop].disable();\n    }\n    if (!wasModuleEnabled && isModuleEnabled) {\n      swiper[prop].enable();\n    }\n  });\n  const directionChanged = breakpointParams.direction && breakpointParams.direction !== params.direction;\n  const needsReLoop = params.loop && (breakpointParams.slidesPerView !== params.slidesPerView || directionChanged);\n  if (directionChanged && initialized) {\n    swiper.changeDirection();\n  }\n  extend(swiper.params, breakpointParams);\n  const isEnabled = swiper.params.enabled;\n  Object.assign(swiper, {\n    allowTouchMove: swiper.params.allowTouchMove,\n    allowSlideNext: swiper.params.allowSlideNext,\n    allowSlidePrev: swiper.params.allowSlidePrev\n  });\n  if (wasEnabled && !isEnabled) {\n    swiper.disable();\n  } else if (!wasEnabled && isEnabled) {\n    swiper.enable();\n  }\n  swiper.currentBreakpoint = breakpoint;\n  swiper.emit('_beforeBreakpoint', breakpointParams);\n  if (needsReLoop && initialized) {\n    swiper.loopDestroy();\n    swiper.loopCreate(realIndex);\n    swiper.updateSlides();\n  }\n  swiper.emit('breakpoint', breakpointParams);\n}"], "mappings": "AAAA,SAASA,MAAM,QAAQ,uBAAuB;AAC9C,MAAMC,aAAa,GAAGA,CAACC,MAAM,EAAEC,MAAM,KAAK;EACxC,OAAOD,MAAM,CAACE,IAAI,IAAID,MAAM,CAACC,IAAI,IAAID,MAAM,CAACC,IAAI,CAACC,IAAI,GAAG,CAAC;AAC3D,CAAC;AACD,eAAe,SAASC,aAAaA,CAAA,EAAG;EACtC,MAAMJ,MAAM,GAAG,IAAI;EACnB,MAAM;IACJK,SAAS;IACTC,WAAW;IACXL,MAAM;IACNM;EACF,CAAC,GAAGP,MAAM;EACV,MAAMQ,WAAW,GAAGP,MAAM,CAACO,WAAW;EACtC,IAAI,CAACA,WAAW,IAAIA,WAAW,IAAIC,MAAM,CAACC,IAAI,CAACF,WAAW,CAAC,CAACG,MAAM,KAAK,CAAC,EAAE;;EAE1E;EACA,MAAMC,UAAU,GAAGZ,MAAM,CAACa,aAAa,CAACL,WAAW,EAAER,MAAM,CAACC,MAAM,CAACa,eAAe,EAAEd,MAAM,CAACO,EAAE,CAAC;EAC9F,IAAI,CAACK,UAAU,IAAIZ,MAAM,CAACe,iBAAiB,KAAKH,UAAU,EAAE;EAC5D,MAAMI,oBAAoB,GAAGJ,UAAU,IAAIJ,WAAW,GAAGA,WAAW,CAACI,UAAU,CAAC,GAAGK,SAAS;EAC5F,MAAMC,gBAAgB,GAAGF,oBAAoB,IAAIhB,MAAM,CAACmB,cAAc;EACtE,MAAMC,WAAW,GAAGrB,aAAa,CAACC,MAAM,EAAEC,MAAM,CAAC;EACjD,MAAMoB,UAAU,GAAGtB,aAAa,CAACC,MAAM,EAAEkB,gBAAgB,CAAC;EAC1D,MAAMI,UAAU,GAAGrB,MAAM,CAACsB,OAAO;EACjC,IAAIH,WAAW,IAAI,CAACC,UAAU,EAAE;IAC9Bd,EAAE,CAACiB,SAAS,CAACC,MAAM,CAAE,GAAExB,MAAM,CAACyB,sBAAuB,MAAK,EAAG,GAAEzB,MAAM,CAACyB,sBAAuB,aAAY,CAAC;IAC1G1B,MAAM,CAAC2B,oBAAoB,EAAE;EAC/B,CAAC,MAAM,IAAI,CAACP,WAAW,IAAIC,UAAU,EAAE;IACrCd,EAAE,CAACiB,SAAS,CAACI,GAAG,CAAE,GAAE3B,MAAM,CAACyB,sBAAuB,MAAK,CAAC;IACxD,IAAIR,gBAAgB,CAAChB,IAAI,CAAC2B,IAAI,IAAIX,gBAAgB,CAAChB,IAAI,CAAC2B,IAAI,KAAK,QAAQ,IAAI,CAACX,gBAAgB,CAAChB,IAAI,CAAC2B,IAAI,IAAI5B,MAAM,CAACC,IAAI,CAAC2B,IAAI,KAAK,QAAQ,EAAE;MACzItB,EAAE,CAACiB,SAAS,CAACI,GAAG,CAAE,GAAE3B,MAAM,CAACyB,sBAAuB,aAAY,CAAC;IACjE;IACA1B,MAAM,CAAC2B,oBAAoB,EAAE;EAC/B;;EAEA;EACA,CAAC,YAAY,EAAE,YAAY,EAAE,WAAW,CAAC,CAACG,OAAO,CAACC,IAAI,IAAI;IACxD,IAAI,OAAOb,gBAAgB,CAACa,IAAI,CAAC,KAAK,WAAW,EAAE;IACnD,MAAMC,gBAAgB,GAAG/B,MAAM,CAAC8B,IAAI,CAAC,IAAI9B,MAAM,CAAC8B,IAAI,CAAC,CAACR,OAAO;IAC7D,MAAMU,eAAe,GAAGf,gBAAgB,CAACa,IAAI,CAAC,IAAIb,gBAAgB,CAACa,IAAI,CAAC,CAACR,OAAO;IAChF,IAAIS,gBAAgB,IAAI,CAACC,eAAe,EAAE;MACxCjC,MAAM,CAAC+B,IAAI,CAAC,CAACG,OAAO,EAAE;IACxB;IACA,IAAI,CAACF,gBAAgB,IAAIC,eAAe,EAAE;MACxCjC,MAAM,CAAC+B,IAAI,CAAC,CAACI,MAAM,EAAE;IACvB;EACF,CAAC,CAAC;EACF,MAAMC,gBAAgB,GAAGlB,gBAAgB,CAACmB,SAAS,IAAInB,gBAAgB,CAACmB,SAAS,KAAKpC,MAAM,CAACoC,SAAS;EACtG,MAAMC,WAAW,GAAGrC,MAAM,CAACsC,IAAI,KAAKrB,gBAAgB,CAACsB,aAAa,KAAKvC,MAAM,CAACuC,aAAa,IAAIJ,gBAAgB,CAAC;EAChH,IAAIA,gBAAgB,IAAI9B,WAAW,EAAE;IACnCN,MAAM,CAACyC,eAAe,EAAE;EAC1B;EACA3C,MAAM,CAACE,MAAM,CAACC,MAAM,EAAEiB,gBAAgB,CAAC;EACvC,MAAMwB,SAAS,GAAG1C,MAAM,CAACC,MAAM,CAACsB,OAAO;EACvCd,MAAM,CAACkC,MAAM,CAAC3C,MAAM,EAAE;IACpB4C,cAAc,EAAE5C,MAAM,CAACC,MAAM,CAAC2C,cAAc;IAC5CC,cAAc,EAAE7C,MAAM,CAACC,MAAM,CAAC4C,cAAc;IAC5CC,cAAc,EAAE9C,MAAM,CAACC,MAAM,CAAC6C;EAChC,CAAC,CAAC;EACF,IAAIxB,UAAU,IAAI,CAACoB,SAAS,EAAE;IAC5B1C,MAAM,CAACkC,OAAO,EAAE;EAClB,CAAC,MAAM,IAAI,CAACZ,UAAU,IAAIoB,SAAS,EAAE;IACnC1C,MAAM,CAACmC,MAAM,EAAE;EACjB;EACAnC,MAAM,CAACe,iBAAiB,GAAGH,UAAU;EACrCZ,MAAM,CAAC+C,IAAI,CAAC,mBAAmB,EAAE7B,gBAAgB,CAAC;EAClD,IAAIoB,WAAW,IAAIhC,WAAW,EAAE;IAC9BN,MAAM,CAACgD,WAAW,EAAE;IACpBhD,MAAM,CAACiD,UAAU,CAAC5C,SAAS,CAAC;IAC5BL,MAAM,CAACkD,YAAY,EAAE;EACvB;EACAlD,MAAM,CAAC+C,IAAI,CAAC,YAAY,EAAE7B,gBAAgB,CAAC;AAC7C"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}