{"ast": null, "code": "/**\n * Swiper React 9.4.1\n * Most modern mobile touch slider and framework with hardware accelerated transitions\n * https://swiperjs.com\n *\n * Copyright 2014-2023 <PERSON>\n *\n * Released under the MIT License\n *\n * Released on: June 13, 2023\n */\n\nimport { Swiper } from './swiper.js';\nimport { SwiperSlide } from './swiper-slide.js';\nexport { useSwiperSlide, useSwiper } from './context.js';\nexport { Swiper, SwiperSlide };", "map": {"version": 3, "names": ["Swiper", "SwiperSlide", "useSwiperSlide", "useSwiper"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/react/swiper-react.js"], "sourcesContent": ["/**\n * Swiper React 9.4.1\n * Most modern mobile touch slider and framework with hardware accelerated transitions\n * https://swiperjs.com\n *\n * Copyright 2014-2023 <PERSON>\n *\n * Released under the MIT License\n *\n * Released on: June 13, 2023\n */\n\nimport { Swiper } from './swiper.js';\nimport { SwiperSlide } from './swiper-slide.js';\nexport { useSwiperSlide, useSwiper } from './context.js';\nexport { Swiper, SwiperSlide };"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,MAAM,QAAQ,aAAa;AACpC,SAASC,WAAW,QAAQ,mBAAmB;AAC/C,SAASC,cAAc,EAAEC,SAAS,QAAQ,cAAc;AACxD,SAASH,MAAM,EAAEC,WAAW"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}