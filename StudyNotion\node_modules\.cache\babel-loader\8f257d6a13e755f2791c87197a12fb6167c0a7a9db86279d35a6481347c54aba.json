{"ast": null, "code": "export default function onResize() {\n  const swiper = this;\n  const {\n    params,\n    el\n  } = swiper;\n  if (el && el.offsetWidth === 0) return;\n\n  // Breakpoints\n  if (params.breakpoints) {\n    swiper.setBreakpoint();\n  }\n\n  // Save locks\n  const {\n    allowSlideNext,\n    allowSlidePrev,\n    snapGrid\n  } = swiper;\n  const isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n\n  // Disable locks on resize\n  swiper.allowSlideNext = true;\n  swiper.allowSlidePrev = true;\n  swiper.updateSize();\n  swiper.updateSlides();\n  swiper.updateSlidesClasses();\n  const isVirtualLoop = isVirtual && params.loop;\n  if ((params.slidesPerView === 'auto' || params.slidesPerView > 1) && swiper.isEnd && !swiper.isBeginning && !swiper.params.centeredSlides && !isVirtualLoop) {\n    swiper.slideTo(swiper.slides.length - 1, 0, false, true);\n  } else {\n    if (swiper.params.loop && !isVirtual) {\n      swiper.slideToLoop(swiper.realIndex, 0, false, true);\n    } else {\n      swiper.slideTo(swiper.activeIndex, 0, false, true);\n    }\n  }\n  if (swiper.autoplay && swiper.autoplay.running && swiper.autoplay.paused) {\n    clearTimeout(swiper.autoplay.resizeTimeout);\n    swiper.autoplay.resizeTimeout = setTimeout(() => {\n      if (swiper.autoplay && swiper.autoplay.running && swiper.autoplay.paused) {\n        swiper.autoplay.resume();\n      }\n    }, 500);\n  }\n  // Return locks after resize\n  swiper.allowSlidePrev = allowSlidePrev;\n  swiper.allowSlideNext = allowSlideNext;\n  if (swiper.params.watchOverflow && snapGrid !== swiper.snapGrid) {\n    swiper.checkOverflow();\n  }\n}", "map": {"version": 3, "names": ["onResize", "swiper", "params", "el", "offsetWidth", "breakpoints", "setBreakpoint", "allowSlideNext", "allowSlidePrev", "snapGrid", "isVirtual", "virtual", "enabled", "updateSize", "updateSlides", "updateSlidesClasses", "isVirtualLoop", "loop", "<PERSON><PERSON><PERSON><PERSON>iew", "isEnd", "isBeginning", "centeredSlides", "slideTo", "slides", "length", "slideToLoop", "realIndex", "activeIndex", "autoplay", "running", "paused", "clearTimeout", "resizeTimeout", "setTimeout", "resume", "watchOverflow", "checkOverflow"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/core/events/onResize.js"], "sourcesContent": ["export default function onResize() {\n  const swiper = this;\n  const {\n    params,\n    el\n  } = swiper;\n  if (el && el.offsetWidth === 0) return;\n\n  // Breakpoints\n  if (params.breakpoints) {\n    swiper.setBreakpoint();\n  }\n\n  // Save locks\n  const {\n    allowSlideNext,\n    allowSlidePrev,\n    snapGrid\n  } = swiper;\n  const isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n\n  // Disable locks on resize\n  swiper.allowSlideNext = true;\n  swiper.allowSlidePrev = true;\n  swiper.updateSize();\n  swiper.updateSlides();\n  swiper.updateSlidesClasses();\n  const isVirtualLoop = isVirtual && params.loop;\n  if ((params.slidesPerView === 'auto' || params.slidesPerView > 1) && swiper.isEnd && !swiper.isBeginning && !swiper.params.centeredSlides && !isVirtualLoop) {\n    swiper.slideTo(swiper.slides.length - 1, 0, false, true);\n  } else {\n    if (swiper.params.loop && !isVirtual) {\n      swiper.slideToLoop(swiper.realIndex, 0, false, true);\n    } else {\n      swiper.slideTo(swiper.activeIndex, 0, false, true);\n    }\n  }\n  if (swiper.autoplay && swiper.autoplay.running && swiper.autoplay.paused) {\n    clearTimeout(swiper.autoplay.resizeTimeout);\n    swiper.autoplay.resizeTimeout = setTimeout(() => {\n      if (swiper.autoplay && swiper.autoplay.running && swiper.autoplay.paused) {\n        swiper.autoplay.resume();\n      }\n    }, 500);\n  }\n  // Return locks after resize\n  swiper.allowSlidePrev = allowSlidePrev;\n  swiper.allowSlideNext = allowSlideNext;\n  if (swiper.params.watchOverflow && snapGrid !== swiper.snapGrid) {\n    swiper.checkOverflow();\n  }\n}"], "mappings": "AAAA,eAAe,SAASA,QAAQA,CAAA,EAAG;EACjC,MAAMC,MAAM,GAAG,IAAI;EACnB,MAAM;IACJC,MAAM;IACNC;EACF,CAAC,GAAGF,MAAM;EACV,IAAIE,EAAE,IAAIA,EAAE,CAACC,WAAW,KAAK,CAAC,EAAE;;EAEhC;EACA,IAAIF,MAAM,CAACG,WAAW,EAAE;IACtBJ,MAAM,CAACK,aAAa,EAAE;EACxB;;EAEA;EACA,MAAM;IACJC,cAAc;IACdC,cAAc;IACdC;EACF,CAAC,GAAGR,MAAM;EACV,MAAMS,SAAS,GAAGT,MAAM,CAACU,OAAO,IAAIV,MAAM,CAACC,MAAM,CAACS,OAAO,CAACC,OAAO;;EAEjE;EACAX,MAAM,CAACM,cAAc,GAAG,IAAI;EAC5BN,MAAM,CAACO,cAAc,GAAG,IAAI;EAC5BP,MAAM,CAACY,UAAU,EAAE;EACnBZ,MAAM,CAACa,YAAY,EAAE;EACrBb,MAAM,CAACc,mBAAmB,EAAE;EAC5B,MAAMC,aAAa,GAAGN,SAAS,IAAIR,MAAM,CAACe,IAAI;EAC9C,IAAI,CAACf,MAAM,CAACgB,aAAa,KAAK,MAAM,IAAIhB,MAAM,CAACgB,aAAa,GAAG,CAAC,KAAKjB,MAAM,CAACkB,KAAK,IAAI,CAAClB,MAAM,CAACmB,WAAW,IAAI,CAACnB,MAAM,CAACC,MAAM,CAACmB,cAAc,IAAI,CAACL,aAAa,EAAE;IAC3Jf,MAAM,CAACqB,OAAO,CAACrB,MAAM,CAACsB,MAAM,CAACC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;EAC1D,CAAC,MAAM;IACL,IAAIvB,MAAM,CAACC,MAAM,CAACe,IAAI,IAAI,CAACP,SAAS,EAAE;MACpCT,MAAM,CAACwB,WAAW,CAACxB,MAAM,CAACyB,SAAS,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;IACtD,CAAC,MAAM;MACLzB,MAAM,CAACqB,OAAO,CAACrB,MAAM,CAAC0B,WAAW,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;IACpD;EACF;EACA,IAAI1B,MAAM,CAAC2B,QAAQ,IAAI3B,MAAM,CAAC2B,QAAQ,CAACC,OAAO,IAAI5B,MAAM,CAAC2B,QAAQ,CAACE,MAAM,EAAE;IACxEC,YAAY,CAAC9B,MAAM,CAAC2B,QAAQ,CAACI,aAAa,CAAC;IAC3C/B,MAAM,CAAC2B,QAAQ,CAACI,aAAa,GAAGC,UAAU,CAAC,MAAM;MAC/C,IAAIhC,MAAM,CAAC2B,QAAQ,IAAI3B,MAAM,CAAC2B,QAAQ,CAACC,OAAO,IAAI5B,MAAM,CAAC2B,QAAQ,CAACE,MAAM,EAAE;QACxE7B,MAAM,CAAC2B,QAAQ,CAACM,MAAM,EAAE;MAC1B;IACF,CAAC,EAAE,GAAG,CAAC;EACT;EACA;EACAjC,MAAM,CAACO,cAAc,GAAGA,cAAc;EACtCP,MAAM,CAACM,cAAc,GAAGA,cAAc;EACtC,IAAIN,MAAM,CAACC,MAAM,CAACiC,aAAa,IAAI1B,QAAQ,KAAKR,MAAM,CAACQ,QAAQ,EAAE;IAC/DR,MAAM,CAACmC,aAAa,EAAE;EACxB;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}