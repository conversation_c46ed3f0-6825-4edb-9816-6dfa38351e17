{"ast": null, "code": "/**\n * @typedef {import('micromark-util-types').Construct} Construct\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Tokenizer} Tokenizer\n */\n\nimport { factorySpace } from 'micromark-factory-space';\nimport { markdownLineEnding, markdownSpace } from 'micromark-util-character';\nimport { codes } from 'micromark-util-symbol/codes.js';\nimport { constants } from 'micromark-util-symbol/constants.js';\nimport { types } from 'micromark-util-symbol/types.js';\nimport { ok as assert } from 'uvu/assert';\n\n/** @type {Construct} */\nexport const codeIndented = {\n  name: 'codeIndented',\n  tokenize: tokenizeCodeIndented\n};\n\n/** @type {Construct} */\nconst furtherStart = {\n  tokenize: tokenizeFurtherStart,\n  partial: true\n};\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeCodeIndented(effects, ok, nok) {\n  const self = this;\n  return start;\n\n  /**\n   * Start of code (indented).\n   *\n   * > **Parsing note**: it is not needed to check if this first line is a\n   * > filled line (that it has a non-whitespace character), because blank lines\n   * > are parsed already, so we never run into that.\n   *\n   * ```markdown\n   * > |     aaa\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    // To do: manually check if interrupting like `markdown-rs`.\n    assert(markdownSpace(code));\n    effects.enter(types.codeIndented);\n    // To do: use an improved `space_or_tab` function like `markdown-rs`,\n    // so that we can drop the next state.\n    return factorySpace(effects, afterPrefix, types.linePrefix, constants.tabSize + 1)(code);\n  }\n\n  /**\n   * At start, after 1 or 4 spaces.\n   *\n   * ```markdown\n   * > |     aaa\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function afterPrefix(code) {\n    const tail = self.events[self.events.length - 1];\n    return tail && tail[1].type === types.linePrefix && tail[2].sliceSerialize(tail[1], true).length >= constants.tabSize ? atBreak(code) : nok(code);\n  }\n\n  /**\n   * At a break.\n   *\n   * ```markdown\n   * > |     aaa\n   *         ^  ^\n   * ```\n   *\n   * @type {State}\n   */\n  function atBreak(code) {\n    if (code === codes.eof) {\n      return after(code);\n    }\n    if (markdownLineEnding(code)) {\n      return effects.attempt(furtherStart, atBreak, after)(code);\n    }\n    effects.enter(types.codeFlowValue);\n    return inside(code);\n  }\n\n  /**\n   * In code content.\n   *\n   * ```markdown\n   * > |     aaa\n   *         ^^^^\n   * ```\n   *\n   * @type {State}\n   */\n  function inside(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.codeFlowValue);\n      return atBreak(code);\n    }\n    effects.consume(code);\n    return inside;\n  }\n\n  /** @type {State} */\n  function after(code) {\n    effects.exit(types.codeIndented);\n    // To do: allow interrupting like `markdown-rs`.\n    // Feel free to interrupt.\n    // tokenizer.interrupt = false\n    return ok(code);\n  }\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeFurtherStart(effects, ok, nok) {\n  const self = this;\n  return furtherStart;\n\n  /**\n   * At eol, trying to parse another indent.\n   *\n   * ```markdown\n   * > |     aaa\n   *            ^\n   *   |     bbb\n   * ```\n   *\n   * @type {State}\n   */\n  function furtherStart(code) {\n    // To do: improve `lazy` / `pierce` handling.\n    // If this is a lazy line, it can’t be code.\n    if (self.parser.lazy[self.now().line]) {\n      return nok(code);\n    }\n    if (markdownLineEnding(code)) {\n      effects.enter(types.lineEnding);\n      effects.consume(code);\n      effects.exit(types.lineEnding);\n      return furtherStart;\n    }\n\n    // To do: the code here in `micromark-js` is a bit different from\n    // `markdown-rs` because there it can attempt spaces.\n    // We can’t yet.\n    //\n    // To do: use an improved `space_or_tab` function like `markdown-rs`,\n    // so that we can drop the next state.\n    return factorySpace(effects, afterPrefix, types.linePrefix, constants.tabSize + 1)(code);\n  }\n\n  /**\n   * At start, after 1 or 4 spaces.\n   *\n   * ```markdown\n   * > |     aaa\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function afterPrefix(code) {\n    const tail = self.events[self.events.length - 1];\n    return tail && tail[1].type === types.linePrefix && tail[2].sliceSerialize(tail[1], true).length >= constants.tabSize ? ok(code) : markdownLineEnding(code) ? furtherStart(code) : nok(code);\n  }\n}", "map": {"version": 3, "names": ["factorySpace", "markdownLineEnding", "markdownSpace", "codes", "constants", "types", "ok", "assert", "codeIndented", "name", "tokenize", "tokenizeCodeIndented", "furtherStart", "tokenizeFurtherStart", "partial", "effects", "nok", "self", "start", "code", "enter", "afterPrefix", "linePrefix", "tabSize", "tail", "events", "length", "type", "sliceSerialize", "atBreak", "eof", "after", "attempt", "codeFlowValue", "inside", "exit", "consume", "parser", "lazy", "now", "line", "lineEnding"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/micromark-core-commonmark/dev/lib/code-indented.js"], "sourcesContent": ["/**\n * @typedef {import('micromark-util-types').Construct} Construct\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Tokenizer} Tokenizer\n */\n\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownLineEnding, markdownSpace} from 'micromark-util-character'\nimport {codes} from 'micromark-util-symbol/codes.js'\nimport {constants} from 'micromark-util-symbol/constants.js'\nimport {types} from 'micromark-util-symbol/types.js'\nimport {ok as assert} from 'uvu/assert'\n\n/** @type {Construct} */\nexport const codeIndented = {\n  name: 'codeIndented',\n  tokenize: tokenizeCodeIndented\n}\n\n/** @type {Construct} */\nconst furtherStart = {tokenize: tokenizeFurtherStart, partial: true}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeCodeIndented(effects, ok, nok) {\n  const self = this\n  return start\n\n  /**\n   * Start of code (indented).\n   *\n   * > **Parsing note**: it is not needed to check if this first line is a\n   * > filled line (that it has a non-whitespace character), because blank lines\n   * > are parsed already, so we never run into that.\n   *\n   * ```markdown\n   * > |     aaa\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    // To do: manually check if interrupting like `markdown-rs`.\n    assert(markdownSpace(code))\n    effects.enter(types.codeIndented)\n    // To do: use an improved `space_or_tab` function like `markdown-rs`,\n    // so that we can drop the next state.\n    return factorySpace(\n      effects,\n      afterPrefix,\n      types.linePrefix,\n      constants.tabSize + 1\n    )(code)\n  }\n\n  /**\n   * At start, after 1 or 4 spaces.\n   *\n   * ```markdown\n   * > |     aaa\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function afterPrefix(code) {\n    const tail = self.events[self.events.length - 1]\n    return tail &&\n      tail[1].type === types.linePrefix &&\n      tail[2].sliceSerialize(tail[1], true).length >= constants.tabSize\n      ? atBreak(code)\n      : nok(code)\n  }\n\n  /**\n   * At a break.\n   *\n   * ```markdown\n   * > |     aaa\n   *         ^  ^\n   * ```\n   *\n   * @type {State}\n   */\n  function atBreak(code) {\n    if (code === codes.eof) {\n      return after(code)\n    }\n\n    if (markdownLineEnding(code)) {\n      return effects.attempt(furtherStart, atBreak, after)(code)\n    }\n\n    effects.enter(types.codeFlowValue)\n    return inside(code)\n  }\n\n  /**\n   * In code content.\n   *\n   * ```markdown\n   * > |     aaa\n   *         ^^^^\n   * ```\n   *\n   * @type {State}\n   */\n  function inside(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.codeFlowValue)\n      return atBreak(code)\n    }\n\n    effects.consume(code)\n    return inside\n  }\n\n  /** @type {State} */\n  function after(code) {\n    effects.exit(types.codeIndented)\n    // To do: allow interrupting like `markdown-rs`.\n    // Feel free to interrupt.\n    // tokenizer.interrupt = false\n    return ok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeFurtherStart(effects, ok, nok) {\n  const self = this\n\n  return furtherStart\n\n  /**\n   * At eol, trying to parse another indent.\n   *\n   * ```markdown\n   * > |     aaa\n   *            ^\n   *   |     bbb\n   * ```\n   *\n   * @type {State}\n   */\n  function furtherStart(code) {\n    // To do: improve `lazy` / `pierce` handling.\n    // If this is a lazy line, it can’t be code.\n    if (self.parser.lazy[self.now().line]) {\n      return nok(code)\n    }\n\n    if (markdownLineEnding(code)) {\n      effects.enter(types.lineEnding)\n      effects.consume(code)\n      effects.exit(types.lineEnding)\n      return furtherStart\n    }\n\n    // To do: the code here in `micromark-js` is a bit different from\n    // `markdown-rs` because there it can attempt spaces.\n    // We can’t yet.\n    //\n    // To do: use an improved `space_or_tab` function like `markdown-rs`,\n    // so that we can drop the next state.\n    return factorySpace(\n      effects,\n      afterPrefix,\n      types.linePrefix,\n      constants.tabSize + 1\n    )(code)\n  }\n\n  /**\n   * At start, after 1 or 4 spaces.\n   *\n   * ```markdown\n   * > |     aaa\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function afterPrefix(code) {\n    const tail = self.events[self.events.length - 1]\n    return tail &&\n      tail[1].type === types.linePrefix &&\n      tail[2].sliceSerialize(tail[1], true).length >= constants.tabSize\n      ? ok(code)\n      : markdownLineEnding(code)\n      ? furtherStart(code)\n      : nok(code)\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,YAAY,QAAO,yBAAyB;AACpD,SAAQC,kBAAkB,EAAEC,aAAa,QAAO,0BAA0B;AAC1E,SAAQC,KAAK,QAAO,gCAAgC;AACpD,SAAQC,SAAS,QAAO,oCAAoC;AAC5D,SAAQC,KAAK,QAAO,gCAAgC;AACpD,SAAQC,EAAE,IAAIC,MAAM,QAAO,YAAY;;AAEvC;AACA,OAAO,MAAMC,YAAY,GAAG;EAC1BC,IAAI,EAAE,cAAc;EACpBC,QAAQ,EAAEC;AACZ,CAAC;;AAED;AACA,MAAMC,YAAY,GAAG;EAACF,QAAQ,EAAEG,oBAAoB;EAAEC,OAAO,EAAE;AAAI,CAAC;;AAEpE;AACA;AACA;AACA;AACA,SAASH,oBAAoBA,CAACI,OAAO,EAAET,EAAE,EAAEU,GAAG,EAAE;EAC9C,MAAMC,IAAI,GAAG,IAAI;EACjB,OAAOC,KAAK;;EAEZ;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,KAAKA,CAACC,IAAI,EAAE;IACnB;IACAZ,MAAM,CAACL,aAAa,CAACiB,IAAI,CAAC,CAAC;IAC3BJ,OAAO,CAACK,KAAK,CAACf,KAAK,CAACG,YAAY,CAAC;IACjC;IACA;IACA,OAAOR,YAAY,CACjBe,OAAO,EACPM,WAAW,EACXhB,KAAK,CAACiB,UAAU,EAChBlB,SAAS,CAACmB,OAAO,GAAG,CAAC,CACtB,CAACJ,IAAI,CAAC;EACT;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASE,WAAWA,CAACF,IAAI,EAAE;IACzB,MAAMK,IAAI,GAAGP,IAAI,CAACQ,MAAM,CAACR,IAAI,CAACQ,MAAM,CAACC,MAAM,GAAG,CAAC,CAAC;IAChD,OAAOF,IAAI,IACTA,IAAI,CAAC,CAAC,CAAC,CAACG,IAAI,KAAKtB,KAAK,CAACiB,UAAU,IACjCE,IAAI,CAAC,CAAC,CAAC,CAACI,cAAc,CAACJ,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAACE,MAAM,IAAItB,SAAS,CAACmB,OAAO,GAC/DM,OAAO,CAACV,IAAI,CAAC,GACbH,GAAG,CAACG,IAAI,CAAC;EACf;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASU,OAAOA,CAACV,IAAI,EAAE;IACrB,IAAIA,IAAI,KAAKhB,KAAK,CAAC2B,GAAG,EAAE;MACtB,OAAOC,KAAK,CAACZ,IAAI,CAAC;IACpB;IAEA,IAAIlB,kBAAkB,CAACkB,IAAI,CAAC,EAAE;MAC5B,OAAOJ,OAAO,CAACiB,OAAO,CAACpB,YAAY,EAAEiB,OAAO,EAAEE,KAAK,CAAC,CAACZ,IAAI,CAAC;IAC5D;IAEAJ,OAAO,CAACK,KAAK,CAACf,KAAK,CAAC4B,aAAa,CAAC;IAClC,OAAOC,MAAM,CAACf,IAAI,CAAC;EACrB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASe,MAAMA,CAACf,IAAI,EAAE;IACpB,IAAIA,IAAI,KAAKhB,KAAK,CAAC2B,GAAG,IAAI7B,kBAAkB,CAACkB,IAAI,CAAC,EAAE;MAClDJ,OAAO,CAACoB,IAAI,CAAC9B,KAAK,CAAC4B,aAAa,CAAC;MACjC,OAAOJ,OAAO,CAACV,IAAI,CAAC;IACtB;IAEAJ,OAAO,CAACqB,OAAO,CAACjB,IAAI,CAAC;IACrB,OAAOe,MAAM;EACf;;EAEA;EACA,SAASH,KAAKA,CAACZ,IAAI,EAAE;IACnBJ,OAAO,CAACoB,IAAI,CAAC9B,KAAK,CAACG,YAAY,CAAC;IAChC;IACA;IACA;IACA,OAAOF,EAAE,CAACa,IAAI,CAAC;EACjB;AACF;;AAEA;AACA;AACA;AACA;AACA,SAASN,oBAAoBA,CAACE,OAAO,EAAET,EAAE,EAAEU,GAAG,EAAE;EAC9C,MAAMC,IAAI,GAAG,IAAI;EAEjB,OAAOL,YAAY;;EAEnB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,YAAYA,CAACO,IAAI,EAAE;IAC1B;IACA;IACA,IAAIF,IAAI,CAACoB,MAAM,CAACC,IAAI,CAACrB,IAAI,CAACsB,GAAG,EAAE,CAACC,IAAI,CAAC,EAAE;MACrC,OAAOxB,GAAG,CAACG,IAAI,CAAC;IAClB;IAEA,IAAIlB,kBAAkB,CAACkB,IAAI,CAAC,EAAE;MAC5BJ,OAAO,CAACK,KAAK,CAACf,KAAK,CAACoC,UAAU,CAAC;MAC/B1B,OAAO,CAACqB,OAAO,CAACjB,IAAI,CAAC;MACrBJ,OAAO,CAACoB,IAAI,CAAC9B,KAAK,CAACoC,UAAU,CAAC;MAC9B,OAAO7B,YAAY;IACrB;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA,OAAOZ,YAAY,CACjBe,OAAO,EACPM,WAAW,EACXhB,KAAK,CAACiB,UAAU,EAChBlB,SAAS,CAACmB,OAAO,GAAG,CAAC,CACtB,CAACJ,IAAI,CAAC;EACT;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASE,WAAWA,CAACF,IAAI,EAAE;IACzB,MAAMK,IAAI,GAAGP,IAAI,CAACQ,MAAM,CAACR,IAAI,CAACQ,MAAM,CAACC,MAAM,GAAG,CAAC,CAAC;IAChD,OAAOF,IAAI,IACTA,IAAI,CAAC,CAAC,CAAC,CAACG,IAAI,KAAKtB,KAAK,CAACiB,UAAU,IACjCE,IAAI,CAAC,CAAC,CAAC,CAACI,cAAc,CAACJ,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAACE,MAAM,IAAItB,SAAS,CAACmB,OAAO,GAC/DjB,EAAE,CAACa,IAAI,CAAC,GACRlB,kBAAkB,CAACkB,IAAI,CAAC,GACxBP,YAAY,CAACO,IAAI,CAAC,GAClBH,GAAG,CAACG,IAAI,CAAC;EACf;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}