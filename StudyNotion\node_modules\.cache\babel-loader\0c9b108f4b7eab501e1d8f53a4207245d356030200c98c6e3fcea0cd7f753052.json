{"ast": null, "code": "import { getTranslate } from '../../shared/utils.js';\nexport default function getSwiperTranslate() {\n  let axis = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : this.isHorizontal() ? 'x' : 'y';\n  const swiper = this;\n  const {\n    params,\n    rtlTranslate: rtl,\n    translate,\n    wrapperEl\n  } = swiper;\n  if (params.virtualTranslate) {\n    return rtl ? -translate : translate;\n  }\n  if (params.cssMode) {\n    return translate;\n  }\n  let currentTranslate = getTranslate(wrapperEl, axis);\n  currentTranslate += swiper.cssOverflowAdjustment();\n  if (rtl) currentTranslate = -currentTranslate;\n  return currentTranslate || 0;\n}", "map": {"version": 3, "names": ["getTranslate", "getSwiperTranslate", "axis", "arguments", "length", "undefined", "isHorizontal", "swiper", "params", "rtlTranslate", "rtl", "translate", "wrapperEl", "virtualTranslate", "cssMode", "currentTranslate", "cssOverflowAdjustment"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/core/translate/getTranslate.js"], "sourcesContent": ["import { getTranslate } from '../../shared/utils.js';\nexport default function getSwiperTranslate(axis = this.isHorizontal() ? 'x' : 'y') {\n  const swiper = this;\n  const {\n    params,\n    rtlTranslate: rtl,\n    translate,\n    wrapperEl\n  } = swiper;\n  if (params.virtualTranslate) {\n    return rtl ? -translate : translate;\n  }\n  if (params.cssMode) {\n    return translate;\n  }\n  let currentTranslate = getTranslate(wrapperEl, axis);\n  currentTranslate += swiper.cssOverflowAdjustment();\n  if (rtl) currentTranslate = -currentTranslate;\n  return currentTranslate || 0;\n}"], "mappings": "AAAA,SAASA,YAAY,QAAQ,uBAAuB;AACpD,eAAe,SAASC,kBAAkBA,CAAA,EAAyC;EAAA,IAAxCC,IAAI,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI,CAACG,YAAY,EAAE,GAAG,GAAG,GAAG,GAAG;EAC/E,MAAMC,MAAM,GAAG,IAAI;EACnB,MAAM;IACJC,MAAM;IACNC,YAAY,EAAEC,GAAG;IACjBC,SAAS;IACTC;EACF,CAAC,GAAGL,MAAM;EACV,IAAIC,MAAM,CAACK,gBAAgB,EAAE;IAC3B,OAAOH,GAAG,GAAG,CAACC,SAAS,GAAGA,SAAS;EACrC;EACA,IAAIH,MAAM,CAACM,OAAO,EAAE;IAClB,OAAOH,SAAS;EAClB;EACA,IAAII,gBAAgB,GAAGf,YAAY,CAACY,SAAS,EAAEV,IAAI,CAAC;EACpDa,gBAAgB,IAAIR,MAAM,CAACS,qBAAqB,EAAE;EAClD,IAAIN,GAAG,EAAEK,gBAAgB,GAAG,CAACA,gBAAgB;EAC7C,OAAOA,gBAAgB,IAAI,CAAC;AAC9B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}