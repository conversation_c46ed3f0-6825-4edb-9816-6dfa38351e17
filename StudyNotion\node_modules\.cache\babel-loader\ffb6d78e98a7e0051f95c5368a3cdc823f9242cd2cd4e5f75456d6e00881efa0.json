{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.formatTime = formatTime;\nexports.isVideoChild = isVideoChild;\nexports.mergeAndSortChildren = mergeAndSortChildren;\nexports.deprecatedWarning = deprecatedWarning;\nexports.throttle = throttle;\nexports.mediaProperties = void 0;\nvar _toConsumableArray2 = _interopRequireDefault(require(\"@babel/runtime/helpers/toConsumableArray\"));\nvar _objectSpread2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectSpread\"));\nvar _objectWithoutProperties2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectWithoutProperties\"));\nvar _react = _interopRequireDefault(require(\"react\"));\n\n// NaN is the only value in javascript which is not equal to itself.\n// eslint-disable-next-line no-self-compare\nvar isNaN = Number.isNaN || function (value) {\n  return value !== value;\n};\n/**\n * @file format-time.js\n *\n * Format seconds as a time string, H:MM:SS or M:SS\n * Supplying a guide (in seconds) will force a number of leading zeros\n * to cover the length of the guide\n *\n * @param  {Number} seconds Number of seconds to be turned into a string\n * @param  {Number} guide   Number (in seconds) to model the string after\n * @return {String}         Time formatted as H:MM:SS or M:SS\n * @private\n * @function formatTime\n */\n\nfunction formatTime() {\n  var seconds = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n  var guide = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : seconds;\n  var s = Math.floor(seconds % 60);\n  var m = Math.floor(seconds / 60 % 60);\n  var h = Math.floor(seconds / 3600);\n  var gm = Math.floor(guide / 60 % 60);\n  var gh = Math.floor(guide / 3600); // handle invalid times\n\n  if (isNaN(seconds) || seconds === Infinity) {\n    // '-' is false for all relational operators (e.g. <, >=) so this setting\n    // will add the minimum number of fields specified by the guide\n    h = '-';\n    m = '-';\n    s = '-';\n  } // Check if we need to show hours\n\n  h = h > 0 || gh > 0 ? \"\".concat(h, \":\") : ''; // If hours are showing, we may need to add a leading zero.\n  // Always show at least one digit of minutes.\n\n  m = \"\".concat((h || gm >= 10) && m < 10 ? \"0\".concat(m) : m, \":\"); // Check if leading zero is need for seconds\n\n  s = s < 10 ? \"0\".concat(s) : s;\n  return h + m + s;\n} // Check if the element belongs to a video element\n// only accept <source />, <track />,\n// <MyComponent isVideoChild />\n// elements\n\nfunction isVideoChild(c) {\n  if (c.props && c.props.isVideoChild) {\n    return true;\n  }\n  return c.type === 'source' || c.type === 'track';\n}\nvar find = function find(elements, func) {\n  return elements.filter(func)[0];\n}; // check if two components are the same type\n\nvar isTypeEqual = function isTypeEqual(component1, component2) {\n  var type1 = component1.type;\n  var type2 = component2.type;\n  if (typeof type1 === 'string' || typeof type2 === 'string') {\n    return type1 === type2;\n  }\n  if (typeof type1 === 'function' && typeof type2 === 'function') {\n    return type1.displayName === type2.displayName;\n  }\n  return false;\n}; // merge default children\n// sort them by `order` property\n// filter them by `disabled` property\n\nfunction mergeAndSortChildren(defaultChildren, _children, _parentProps) {\n  var defaultOrder = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 1;\n  var children = _react[\"default\"].Children.toArray(_children);\n  var order = _parentProps.order,\n    parentProps = (0, _objectWithoutProperties2[\"default\"])(_parentProps, [\"order\"]); // ignore order from parent\n\n  return children.filter(function (e) {\n    return !e.props.disabled;\n  }) // filter the disabled components\n  .concat(defaultChildren.filter(function (c) {\n    return !find(children, function (component) {\n      return isTypeEqual(component, c);\n    });\n  })).map(function (element) {\n    var defaultComponent = find(defaultChildren, function (c) {\n      return isTypeEqual(c, element);\n    });\n    var defaultProps = defaultComponent ? defaultComponent.props : {};\n    var props = (0, _objectSpread2[\"default\"])({}, parentProps, defaultProps, element.props);\n    var e = _react[\"default\"].cloneElement(element, props, element.props.children);\n    return e;\n  }).sort(function (a, b) {\n    return (a.props.order || defaultOrder) - (b.props.order || defaultOrder);\n  });\n}\n/**\n * Temporary utility for generating the warnings\n */\n\nfunction deprecatedWarning(oldMethodCall, newMethodCall) {\n  // eslint-disable-next-line no-console\n  console.warn(\"WARNING: \".concat(oldMethodCall, \" will be deprecated soon! Please use \").concat(newMethodCall, \" instead.\"));\n}\nfunction throttle(callback, limit) {\n  var _arguments = arguments;\n  var wait = false;\n  return function () {\n    if (!wait) {\n      // eslint-disable-next-line prefer-rest-params\n      callback.apply(void 0, (0, _toConsumableArray2[\"default\"])(_arguments));\n      wait = true;\n      setTimeout(function () {\n        wait = false;\n      }, limit);\n    }\n  };\n}\nvar mediaProperties = ['error', 'src', 'srcObject', 'currentSrc', 'crossOrigin', 'networkState', 'preload', 'buffered', 'readyState', 'seeking', 'currentTime', 'duration', 'paused', 'defaultPlaybackRate', 'playbackRate', 'played', 'seekable', 'ended', 'autoplay', 'loop', 'mediaGroup', 'controller', 'controls', 'volume', 'muted', 'defaultMuted', 'audioTracks', 'videoTracks', 'textTracks', 'width', 'height', 'videoWidth', 'videoHeight', 'poster'];\nexports.mediaProperties = mediaProperties;", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "formatTime", "isVideoChild", "mergeAndSortChildren", "deprecatedWarning", "throttle", "mediaProperties", "_toConsumableArray2", "_objectSpread2", "_objectWithoutProperties2", "_react", "isNaN", "Number", "seconds", "arguments", "length", "undefined", "guide", "s", "Math", "floor", "m", "h", "gm", "gh", "Infinity", "concat", "c", "props", "type", "find", "elements", "func", "filter", "isTypeEqual", "component1", "component2", "type1", "type2", "displayName", "defaultChildren", "_children", "_parentProps", "defaultOrder", "children", "Children", "toArray", "order", "parentProps", "e", "disabled", "component", "map", "element", "defaultComponent", "defaultProps", "cloneElement", "sort", "a", "b", "oldMethodCall", "newMethodCall", "console", "warn", "callback", "limit", "_arguments", "wait", "apply", "setTimeout"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/video-react/lib/utils/index.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.formatTime = formatTime;\nexports.isVideoChild = isVideoChild;\nexports.mergeAndSortChildren = mergeAndSortChildren;\nexports.deprecatedWarning = deprecatedWarning;\nexports.throttle = throttle;\nexports.mediaProperties = void 0;\n\nvar _toConsumableArray2 = _interopRequireDefault(require(\"@babel/runtime/helpers/toConsumableArray\"));\n\nvar _objectSpread2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectSpread\"));\n\nvar _objectWithoutProperties2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectWithoutProperties\"));\n\nvar _react = _interopRequireDefault(require(\"react\"));\n\n// NaN is the only value in javascript which is not equal to itself.\n// eslint-disable-next-line no-self-compare\nvar isNaN = Number.isNaN || function (value) {\n  return value !== value;\n};\n/**\n * @file format-time.js\n *\n * Format seconds as a time string, H:MM:SS or M:SS\n * Supplying a guide (in seconds) will force a number of leading zeros\n * to cover the length of the guide\n *\n * @param  {Number} seconds Number of seconds to be turned into a string\n * @param  {Number} guide   Number (in seconds) to model the string after\n * @return {String}         Time formatted as H:MM:SS or M:SS\n * @private\n * @function formatTime\n */\n\n\nfunction formatTime() {\n  var seconds = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n  var guide = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : seconds;\n  var s = Math.floor(seconds % 60);\n  var m = Math.floor(seconds / 60 % 60);\n  var h = Math.floor(seconds / 3600);\n  var gm = Math.floor(guide / 60 % 60);\n  var gh = Math.floor(guide / 3600); // handle invalid times\n\n  if (isNaN(seconds) || seconds === Infinity) {\n    // '-' is false for all relational operators (e.g. <, >=) so this setting\n    // will add the minimum number of fields specified by the guide\n    h = '-';\n    m = '-';\n    s = '-';\n  } // Check if we need to show hours\n\n\n  h = h > 0 || gh > 0 ? \"\".concat(h, \":\") : ''; // If hours are showing, we may need to add a leading zero.\n  // Always show at least one digit of minutes.\n\n  m = \"\".concat((h || gm >= 10) && m < 10 ? \"0\".concat(m) : m, \":\"); // Check if leading zero is need for seconds\n\n  s = s < 10 ? \"0\".concat(s) : s;\n  return h + m + s;\n} // Check if the element belongs to a video element\n// only accept <source />, <track />,\n// <MyComponent isVideoChild />\n// elements\n\n\nfunction isVideoChild(c) {\n  if (c.props && c.props.isVideoChild) {\n    return true;\n  }\n\n  return c.type === 'source' || c.type === 'track';\n}\n\nvar find = function find(elements, func) {\n  return elements.filter(func)[0];\n}; // check if two components are the same type\n\n\nvar isTypeEqual = function isTypeEqual(component1, component2) {\n  var type1 = component1.type;\n  var type2 = component2.type;\n\n  if (typeof type1 === 'string' || typeof type2 === 'string') {\n    return type1 === type2;\n  }\n\n  if (typeof type1 === 'function' && typeof type2 === 'function') {\n    return type1.displayName === type2.displayName;\n  }\n\n  return false;\n}; // merge default children\n// sort them by `order` property\n// filter them by `disabled` property\n\n\nfunction mergeAndSortChildren(defaultChildren, _children, _parentProps) {\n  var defaultOrder = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 1;\n\n  var children = _react[\"default\"].Children.toArray(_children);\n\n  var order = _parentProps.order,\n      parentProps = (0, _objectWithoutProperties2[\"default\"])(_parentProps, [\"order\"]); // ignore order from parent\n\n  return children.filter(function (e) {\n    return !e.props.disabled;\n  }) // filter the disabled components\n  .concat(defaultChildren.filter(function (c) {\n    return !find(children, function (component) {\n      return isTypeEqual(component, c);\n    });\n  })).map(function (element) {\n    var defaultComponent = find(defaultChildren, function (c) {\n      return isTypeEqual(c, element);\n    });\n    var defaultProps = defaultComponent ? defaultComponent.props : {};\n    var props = (0, _objectSpread2[\"default\"])({}, parentProps, defaultProps, element.props);\n\n    var e = _react[\"default\"].cloneElement(element, props, element.props.children);\n\n    return e;\n  }).sort(function (a, b) {\n    return (a.props.order || defaultOrder) - (b.props.order || defaultOrder);\n  });\n}\n/**\n * Temporary utility for generating the warnings\n */\n\n\nfunction deprecatedWarning(oldMethodCall, newMethodCall) {\n  // eslint-disable-next-line no-console\n  console.warn(\"WARNING: \".concat(oldMethodCall, \" will be deprecated soon! Please use \").concat(newMethodCall, \" instead.\"));\n}\n\nfunction throttle(callback, limit) {\n  var _arguments = arguments;\n  var wait = false;\n  return function () {\n    if (!wait) {\n      // eslint-disable-next-line prefer-rest-params\n      callback.apply(void 0, (0, _toConsumableArray2[\"default\"])(_arguments));\n      wait = true;\n      setTimeout(function () {\n        wait = false;\n      }, limit);\n    }\n  };\n}\n\nvar mediaProperties = ['error', 'src', 'srcObject', 'currentSrc', 'crossOrigin', 'networkState', 'preload', 'buffered', 'readyState', 'seeking', 'currentTime', 'duration', 'paused', 'defaultPlaybackRate', 'playbackRate', 'played', 'seekable', 'ended', 'autoplay', 'loop', 'mediaGroup', 'controller', 'controls', 'volume', 'muted', 'defaultMuted', 'audioTracks', 'videoTracks', 'textTracks', 'width', 'height', 'videoWidth', 'videoHeight', 'poster'];\nexports.mediaProperties = mediaProperties;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC;AAEpFC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,UAAU,GAAGA,UAAU;AAC/BF,OAAO,CAACG,YAAY,GAAGA,YAAY;AACnCH,OAAO,CAACI,oBAAoB,GAAGA,oBAAoB;AACnDJ,OAAO,CAACK,iBAAiB,GAAGA,iBAAiB;AAC7CL,OAAO,CAACM,QAAQ,GAAGA,QAAQ;AAC3BN,OAAO,CAACO,eAAe,GAAG,KAAK,CAAC;AAEhC,IAAIC,mBAAmB,GAAGZ,sBAAsB,CAACC,OAAO,CAAC,0CAA0C,CAAC,CAAC;AAErG,IAAIY,cAAc,GAAGb,sBAAsB,CAACC,OAAO,CAAC,qCAAqC,CAAC,CAAC;AAE3F,IAAIa,yBAAyB,GAAGd,sBAAsB,CAACC,OAAO,CAAC,gDAAgD,CAAC,CAAC;AAEjH,IAAIc,MAAM,GAAGf,sBAAsB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;;AAErD;AACA;AACA,IAAIe,KAAK,GAAGC,MAAM,CAACD,KAAK,IAAI,UAAUX,KAAK,EAAE;EAC3C,OAAOA,KAAK,KAAKA,KAAK;AACxB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA,SAASC,UAAUA,CAAA,EAAG;EACpB,IAAIY,OAAO,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;EACnF,IAAIG,KAAK,GAAGH,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAGD,OAAO;EACvF,IAAIK,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACP,OAAO,GAAG,EAAE,CAAC;EAChC,IAAIQ,CAAC,GAAGF,IAAI,CAACC,KAAK,CAACP,OAAO,GAAG,EAAE,GAAG,EAAE,CAAC;EACrC,IAAIS,CAAC,GAAGH,IAAI,CAACC,KAAK,CAACP,OAAO,GAAG,IAAI,CAAC;EAClC,IAAIU,EAAE,GAAGJ,IAAI,CAACC,KAAK,CAACH,KAAK,GAAG,EAAE,GAAG,EAAE,CAAC;EACpC,IAAIO,EAAE,GAAGL,IAAI,CAACC,KAAK,CAACH,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC;;EAEnC,IAAIN,KAAK,CAACE,OAAO,CAAC,IAAIA,OAAO,KAAKY,QAAQ,EAAE;IAC1C;IACA;IACAH,CAAC,GAAG,GAAG;IACPD,CAAC,GAAG,GAAG;IACPH,CAAC,GAAG,GAAG;EACT,CAAC,CAAC;;EAGFI,CAAC,GAAGA,CAAC,GAAG,CAAC,IAAIE,EAAE,GAAG,CAAC,GAAG,EAAE,CAACE,MAAM,CAACJ,CAAC,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC;EAC9C;;EAEAD,CAAC,GAAG,EAAE,CAACK,MAAM,CAAC,CAACJ,CAAC,IAAIC,EAAE,IAAI,EAAE,KAAKF,CAAC,GAAG,EAAE,GAAG,GAAG,CAACK,MAAM,CAACL,CAAC,CAAC,GAAGA,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;;EAEnEH,CAAC,GAAGA,CAAC,GAAG,EAAE,GAAG,GAAG,CAACQ,MAAM,CAACR,CAAC,CAAC,GAAGA,CAAC;EAC9B,OAAOI,CAAC,GAAGD,CAAC,GAAGH,CAAC;AAClB,CAAC,CAAC;AACF;AACA;AACA;;AAGA,SAAShB,YAAYA,CAACyB,CAAC,EAAE;EACvB,IAAIA,CAAC,CAACC,KAAK,IAAID,CAAC,CAACC,KAAK,CAAC1B,YAAY,EAAE;IACnC,OAAO,IAAI;EACb;EAEA,OAAOyB,CAAC,CAACE,IAAI,KAAK,QAAQ,IAAIF,CAAC,CAACE,IAAI,KAAK,OAAO;AAClD;AAEA,IAAIC,IAAI,GAAG,SAASA,IAAIA,CAACC,QAAQ,EAAEC,IAAI,EAAE;EACvC,OAAOD,QAAQ,CAACE,MAAM,CAACD,IAAI,CAAC,CAAC,CAAC,CAAC;AACjC,CAAC,CAAC,CAAC;;AAGH,IAAIE,WAAW,GAAG,SAASA,WAAWA,CAACC,UAAU,EAAEC,UAAU,EAAE;EAC7D,IAAIC,KAAK,GAAGF,UAAU,CAACN,IAAI;EAC3B,IAAIS,KAAK,GAAGF,UAAU,CAACP,IAAI;EAE3B,IAAI,OAAOQ,KAAK,KAAK,QAAQ,IAAI,OAAOC,KAAK,KAAK,QAAQ,EAAE;IAC1D,OAAOD,KAAK,KAAKC,KAAK;EACxB;EAEA,IAAI,OAAOD,KAAK,KAAK,UAAU,IAAI,OAAOC,KAAK,KAAK,UAAU,EAAE;IAC9D,OAAOD,KAAK,CAACE,WAAW,KAAKD,KAAK,CAACC,WAAW;EAChD;EAEA,OAAO,KAAK;AACd,CAAC,CAAC,CAAC;AACH;AACA;;AAGA,SAASpC,oBAAoBA,CAACqC,eAAe,EAAEC,SAAS,EAAEC,YAAY,EAAE;EACtE,IAAIC,YAAY,GAAG7B,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;EAExF,IAAI8B,QAAQ,GAAGlC,MAAM,CAAC,SAAS,CAAC,CAACmC,QAAQ,CAACC,OAAO,CAACL,SAAS,CAAC;EAE5D,IAAIM,KAAK,GAAGL,YAAY,CAACK,KAAK;IAC1BC,WAAW,GAAG,CAAC,CAAC,EAAEvC,yBAAyB,CAAC,SAAS,CAAC,EAAEiC,YAAY,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;;EAEtF,OAAOE,QAAQ,CAACX,MAAM,CAAC,UAAUgB,CAAC,EAAE;IAClC,OAAO,CAACA,CAAC,CAACrB,KAAK,CAACsB,QAAQ;EAC1B,CAAC,CAAC,CAAC;EAAA,CACFxB,MAAM,CAACc,eAAe,CAACP,MAAM,CAAC,UAAUN,CAAC,EAAE;IAC1C,OAAO,CAACG,IAAI,CAACc,QAAQ,EAAE,UAAUO,SAAS,EAAE;MAC1C,OAAOjB,WAAW,CAACiB,SAAS,EAAExB,CAAC,CAAC;IAClC,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC,CAACyB,GAAG,CAAC,UAAUC,OAAO,EAAE;IACzB,IAAIC,gBAAgB,GAAGxB,IAAI,CAACU,eAAe,EAAE,UAAUb,CAAC,EAAE;MACxD,OAAOO,WAAW,CAACP,CAAC,EAAE0B,OAAO,CAAC;IAChC,CAAC,CAAC;IACF,IAAIE,YAAY,GAAGD,gBAAgB,GAAGA,gBAAgB,CAAC1B,KAAK,GAAG,CAAC,CAAC;IACjE,IAAIA,KAAK,GAAG,CAAC,CAAC,EAAEpB,cAAc,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAEwC,WAAW,EAAEO,YAAY,EAAEF,OAAO,CAACzB,KAAK,CAAC;IAExF,IAAIqB,CAAC,GAAGvC,MAAM,CAAC,SAAS,CAAC,CAAC8C,YAAY,CAACH,OAAO,EAAEzB,KAAK,EAAEyB,OAAO,CAACzB,KAAK,CAACgB,QAAQ,CAAC;IAE9E,OAAOK,CAAC;EACV,CAAC,CAAC,CAACQ,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;IACtB,OAAO,CAACD,CAAC,CAAC9B,KAAK,CAACmB,KAAK,IAAIJ,YAAY,KAAKgB,CAAC,CAAC/B,KAAK,CAACmB,KAAK,IAAIJ,YAAY,CAAC;EAC1E,CAAC,CAAC;AACJ;AACA;AACA;AACA;;AAGA,SAASvC,iBAAiBA,CAACwD,aAAa,EAAEC,aAAa,EAAE;EACvD;EACAC,OAAO,CAACC,IAAI,CAAC,WAAW,CAACrC,MAAM,CAACkC,aAAa,EAAE,uCAAuC,CAAC,CAAClC,MAAM,CAACmC,aAAa,EAAE,WAAW,CAAC,CAAC;AAC7H;AAEA,SAASxD,QAAQA,CAAC2D,QAAQ,EAAEC,KAAK,EAAE;EACjC,IAAIC,UAAU,GAAGpD,SAAS;EAC1B,IAAIqD,IAAI,GAAG,KAAK;EAChB,OAAO,YAAY;IACjB,IAAI,CAACA,IAAI,EAAE;MACT;MACAH,QAAQ,CAACI,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE7D,mBAAmB,CAAC,SAAS,CAAC,EAAE2D,UAAU,CAAC,CAAC;MACvEC,IAAI,GAAG,IAAI;MACXE,UAAU,CAAC,YAAY;QACrBF,IAAI,GAAG,KAAK;MACd,CAAC,EAAEF,KAAK,CAAC;IACX;EACF,CAAC;AACH;AAEA,IAAI3D,eAAe,GAAG,CAAC,OAAO,EAAE,KAAK,EAAE,WAAW,EAAE,YAAY,EAAE,aAAa,EAAE,cAAc,EAAE,SAAS,EAAE,UAAU,EAAE,YAAY,EAAE,SAAS,EAAE,aAAa,EAAE,UAAU,EAAE,QAAQ,EAAE,qBAAqB,EAAE,cAAc,EAAE,QAAQ,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE,YAAY,EAAE,YAAY,EAAE,UAAU,EAAE,QAAQ,EAAE,OAAO,EAAE,cAAc,EAAE,aAAa,EAAE,aAAa,EAAE,YAAY,EAAE,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,aAAa,EAAE,QAAQ,CAAC;AAChcP,OAAO,CAACO,eAAe,GAAGA,eAAe"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}