{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\");\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nvar _classCallCheck2 = _interopRequireDefault(require(\"@babel/runtime/helpers/classCallCheck\"));\nvar _createClass2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createClass\"));\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"@babel/runtime/helpers/possibleConstructorReturn\"));\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"@babel/runtime/helpers/getPrototypeOf\"));\nvar _assertThisInitialized2 = _interopRequireDefault(require(\"@babel/runtime/helpers/assertThisInitialized\"));\nvar _inherits2 = _interopRequireDefault(require(\"@babel/runtime/helpers/inherits\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _react = _interopRequireWildcard(require(\"react\"));\nvar _classnames = _interopRequireDefault(require(\"classnames\"));\nvar _Menu = _interopRequireDefault(require(\"./Menu\"));\nvar _MenuItem = _interopRequireDefault(require(\"./MenuItem\"));\nvar _ClickableComponent = _interopRequireDefault(require(\"../ClickableComponent\"));\nvar propTypes = {\n  inline: _propTypes[\"default\"].bool,\n  items: _propTypes[\"default\"].array,\n  className: _propTypes[\"default\"].string,\n  onSelectItem: _propTypes[\"default\"].func,\n  children: _propTypes[\"default\"].any,\n  selectedIndex: _propTypes[\"default\"].number\n};\nvar MenuButton = /*#__PURE__*/\nfunction (_Component) {\n  (0, _inherits2[\"default\"])(MenuButton, _Component);\n  function MenuButton(props, context) {\n    var _this;\n    (0, _classCallCheck2[\"default\"])(this, MenuButton);\n    _this = (0, _possibleConstructorReturn2[\"default\"])(this, (0, _getPrototypeOf2[\"default\"])(MenuButton).call(this, props, context));\n    _this.state = {\n      active: false,\n      activateIndex: props.selectedIndex || 0\n    };\n    _this.commitSelection = _this.commitSelection.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.activateMenuItem = _this.activateMenuItem.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleClick = _this.handleClick.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.renderMenu = _this.renderMenu.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleFocus = _this.handleFocus.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleBlur = _this.handleBlur.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleUpArrow = _this.handleUpArrow.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleDownArrow = _this.handleDownArrow.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleEscape = _this.handleEscape.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleReturn = _this.handleReturn.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleTab = _this.handleTab.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleKeyPress = _this.handleKeyPress.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleSelectItem = _this.handleSelectItem.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleIndexChange = _this.handleIndexChange.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    return _this;\n  }\n  (0, _createClass2[\"default\"])(MenuButton, [{\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(prevProps) {\n      if (prevProps.selectedIndex !== this.props.selectedIndex) {\n        this.activateMenuItem(this.props.selectedIndex);\n      }\n    }\n  }, {\n    key: \"commitSelection\",\n    value: function commitSelection(index) {\n      this.setState({\n        activateIndex: index\n      });\n      this.handleIndexChange(index);\n    }\n  }, {\n    key: \"activateMenuItem\",\n    value: function activateMenuItem(index) {\n      this.setState({\n        activateIndex: index\n      });\n      this.handleIndexChange(index);\n    }\n  }, {\n    key: \"handleIndexChange\",\n    value: function handleIndexChange(index) {\n      var onSelectItem = this.props.onSelectItem;\n      onSelectItem(index);\n    }\n  }, {\n    key: \"handleClick\",\n    value: function handleClick() {\n      this.setState(function (prevState) {\n        return {\n          active: !prevState.active\n        };\n      });\n    }\n  }, {\n    key: \"handleFocus\",\n    value: function handleFocus() {\n      document.addEventListener('keydown', this.handleKeyPress);\n    }\n  }, {\n    key: \"handleBlur\",\n    value: function handleBlur() {\n      this.setState({\n        active: false\n      });\n      document.removeEventListener('keydown', this.handleKeyPress);\n    }\n  }, {\n    key: \"handleUpArrow\",\n    value: function handleUpArrow(e) {\n      var items = this.props.items;\n      if (this.state.active) {\n        e.preventDefault();\n        var newIndex = this.state.activateIndex - 1;\n        if (newIndex < 0) {\n          newIndex = items.length ? items.length - 1 : 0;\n        }\n        this.activateMenuItem(newIndex);\n      }\n    }\n  }, {\n    key: \"handleDownArrow\",\n    value: function handleDownArrow(e) {\n      var items = this.props.items;\n      if (this.state.active) {\n        e.preventDefault();\n        var newIndex = this.state.activateIndex + 1;\n        if (newIndex >= items.length) {\n          newIndex = 0;\n        }\n        this.activateMenuItem(newIndex);\n      }\n    }\n  }, {\n    key: \"handleTab\",\n    value: function handleTab(e) {\n      if (this.state.active) {\n        e.preventDefault();\n        this.commitSelection(this.state.activateIndex);\n      }\n    }\n  }, {\n    key: \"handleReturn\",\n    value: function handleReturn(e) {\n      e.preventDefault();\n      if (this.state.active) {\n        this.commitSelection(this.state.activateIndex);\n      } else {\n        this.setState({\n          active: true\n        });\n      }\n    }\n  }, {\n    key: \"handleEscape\",\n    value: function handleEscape() {\n      this.setState({\n        active: false,\n        activateIndex: 0\n      });\n    }\n  }, {\n    key: \"handleKeyPress\",\n    value: function handleKeyPress(event) {\n      // Escape (27) key\n      if (event.which === 27) {\n        this.handleEscape(event);\n      } else if (event.which === 9) {\n        // Tab (9) key\n        this.handleTab(event);\n      } else if (event.which === 13) {\n        // Enter (13) key\n        this.handleReturn(event);\n      } else if (event.which === 38) {\n        // Up (38) key\n        this.handleUpArrow(event);\n      } else if (event.which === 40) {\n        // Down (40) key press\n        this.handleDownArrow(event);\n      }\n    }\n  }, {\n    key: \"handleSelectItem\",\n    value: function handleSelectItem(i) {\n      this.commitSelection(i);\n    }\n  }, {\n    key: \"renderMenu\",\n    value: function renderMenu() {\n      var _this2 = this;\n      if (!this.state.active) {\n        return null;\n      }\n      var items = this.props.items;\n      return _react[\"default\"].createElement(_Menu[\"default\"], null, items.map(function (item, i) {\n        return _react[\"default\"].createElement(_MenuItem[\"default\"], {\n          item: item,\n          index: i,\n          onSelectItem: _this2.handleSelectItem,\n          activateIndex: _this2.state.activateIndex,\n          key: \"item-\".concat(i++)\n        });\n      }));\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this3 = this;\n      var _this$props = this.props,\n        inline = _this$props.inline,\n        className = _this$props.className;\n      return _react[\"default\"].createElement(_ClickableComponent[\"default\"], {\n        className: (0, _classnames[\"default\"])(className, {\n          'video-react-menu-button-inline': !!inline,\n          'video-react-menu-button-popup': !inline,\n          'video-react-menu-button-active': this.state.active\n        }, 'video-react-control video-react-button video-react-menu-button'),\n        role: \"button\",\n        tabIndex: \"0\",\n        ref: function ref(c) {\n          _this3.menuButton = c;\n        },\n        onClick: this.handleClick,\n        onFocus: this.handleFocus,\n        onBlur: this.handleBlur\n      }, this.props.children, this.renderMenu());\n    }\n  }]);\n  return MenuButton;\n}(_react.Component);\nexports[\"default\"] = MenuButton;\nMenuButton.propTypes = propTypes;\nMenuButton.displayName = 'MenuButton';", "map": {"version": 3, "names": ["_interopRequireWildcard", "require", "_interopRequireDefault", "Object", "defineProperty", "exports", "value", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_assertThisInitialized2", "_inherits2", "_propTypes", "_react", "_classnames", "_Menu", "_MenuItem", "_ClickableComponent", "propTypes", "inline", "bool", "items", "array", "className", "string", "onSelectItem", "func", "children", "any", "selectedIndex", "number", "MenuButton", "_Component", "props", "context", "_this", "call", "state", "active", "activateIndex", "commitSelection", "bind", "activateMenuItem", "handleClick", "renderMenu", "handleFocus", "handleBlur", "handleUpArrow", "handleDownArrow", "handleEscape", "handleReturn", "handleTab", "handleKeyPress", "handleSelectItem", "handleIndexChange", "key", "componentDidUpdate", "prevProps", "index", "setState", "prevState", "document", "addEventListener", "removeEventListener", "e", "preventDefault", "newIndex", "length", "event", "which", "i", "_this2", "createElement", "map", "item", "concat", "render", "_this3", "_this$props", "role", "tabIndex", "ref", "c", "menuButton", "onClick", "onFocus", "onBlur", "Component", "displayName"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/video-react/lib/components/menu/MenuButton.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\");\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _classCallCheck2 = _interopRequireDefault(require(\"@babel/runtime/helpers/classCallCheck\"));\n\nvar _createClass2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createClass\"));\n\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"@babel/runtime/helpers/possibleConstructorReturn\"));\n\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"@babel/runtime/helpers/getPrototypeOf\"));\n\nvar _assertThisInitialized2 = _interopRequireDefault(require(\"@babel/runtime/helpers/assertThisInitialized\"));\n\nvar _inherits2 = _interopRequireDefault(require(\"@babel/runtime/helpers/inherits\"));\n\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\n\nvar _react = _interopRequireWildcard(require(\"react\"));\n\nvar _classnames = _interopRequireDefault(require(\"classnames\"));\n\nvar _Menu = _interopRequireDefault(require(\"./Menu\"));\n\nvar _MenuItem = _interopRequireDefault(require(\"./MenuItem\"));\n\nvar _ClickableComponent = _interopRequireDefault(require(\"../ClickableComponent\"));\n\nvar propTypes = {\n  inline: _propTypes[\"default\"].bool,\n  items: _propTypes[\"default\"].array,\n  className: _propTypes[\"default\"].string,\n  onSelectItem: _propTypes[\"default\"].func,\n  children: _propTypes[\"default\"].any,\n  selectedIndex: _propTypes[\"default\"].number\n};\n\nvar MenuButton =\n/*#__PURE__*/\nfunction (_Component) {\n  (0, _inherits2[\"default\"])(MenuButton, _Component);\n\n  function MenuButton(props, context) {\n    var _this;\n\n    (0, _classCallCheck2[\"default\"])(this, MenuButton);\n    _this = (0, _possibleConstructorReturn2[\"default\"])(this, (0, _getPrototypeOf2[\"default\"])(MenuButton).call(this, props, context));\n    _this.state = {\n      active: false,\n      activateIndex: props.selectedIndex || 0\n    };\n    _this.commitSelection = _this.commitSelection.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.activateMenuItem = _this.activateMenuItem.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleClick = _this.handleClick.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.renderMenu = _this.renderMenu.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleFocus = _this.handleFocus.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleBlur = _this.handleBlur.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleUpArrow = _this.handleUpArrow.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleDownArrow = _this.handleDownArrow.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleEscape = _this.handleEscape.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleReturn = _this.handleReturn.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleTab = _this.handleTab.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleKeyPress = _this.handleKeyPress.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleSelectItem = _this.handleSelectItem.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleIndexChange = _this.handleIndexChange.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    return _this;\n  }\n\n  (0, _createClass2[\"default\"])(MenuButton, [{\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(prevProps) {\n      if (prevProps.selectedIndex !== this.props.selectedIndex) {\n        this.activateMenuItem(this.props.selectedIndex);\n      }\n    }\n  }, {\n    key: \"commitSelection\",\n    value: function commitSelection(index) {\n      this.setState({\n        activateIndex: index\n      });\n      this.handleIndexChange(index);\n    }\n  }, {\n    key: \"activateMenuItem\",\n    value: function activateMenuItem(index) {\n      this.setState({\n        activateIndex: index\n      });\n      this.handleIndexChange(index);\n    }\n  }, {\n    key: \"handleIndexChange\",\n    value: function handleIndexChange(index) {\n      var onSelectItem = this.props.onSelectItem;\n      onSelectItem(index);\n    }\n  }, {\n    key: \"handleClick\",\n    value: function handleClick() {\n      this.setState(function (prevState) {\n        return {\n          active: !prevState.active\n        };\n      });\n    }\n  }, {\n    key: \"handleFocus\",\n    value: function handleFocus() {\n      document.addEventListener('keydown', this.handleKeyPress);\n    }\n  }, {\n    key: \"handleBlur\",\n    value: function handleBlur() {\n      this.setState({\n        active: false\n      });\n      document.removeEventListener('keydown', this.handleKeyPress);\n    }\n  }, {\n    key: \"handleUpArrow\",\n    value: function handleUpArrow(e) {\n      var items = this.props.items;\n\n      if (this.state.active) {\n        e.preventDefault();\n        var newIndex = this.state.activateIndex - 1;\n\n        if (newIndex < 0) {\n          newIndex = items.length ? items.length - 1 : 0;\n        }\n\n        this.activateMenuItem(newIndex);\n      }\n    }\n  }, {\n    key: \"handleDownArrow\",\n    value: function handleDownArrow(e) {\n      var items = this.props.items;\n\n      if (this.state.active) {\n        e.preventDefault();\n        var newIndex = this.state.activateIndex + 1;\n\n        if (newIndex >= items.length) {\n          newIndex = 0;\n        }\n\n        this.activateMenuItem(newIndex);\n      }\n    }\n  }, {\n    key: \"handleTab\",\n    value: function handleTab(e) {\n      if (this.state.active) {\n        e.preventDefault();\n        this.commitSelection(this.state.activateIndex);\n      }\n    }\n  }, {\n    key: \"handleReturn\",\n    value: function handleReturn(e) {\n      e.preventDefault();\n\n      if (this.state.active) {\n        this.commitSelection(this.state.activateIndex);\n      } else {\n        this.setState({\n          active: true\n        });\n      }\n    }\n  }, {\n    key: \"handleEscape\",\n    value: function handleEscape() {\n      this.setState({\n        active: false,\n        activateIndex: 0\n      });\n    }\n  }, {\n    key: \"handleKeyPress\",\n    value: function handleKeyPress(event) {\n      // Escape (27) key\n      if (event.which === 27) {\n        this.handleEscape(event);\n      } else if (event.which === 9) {\n        // Tab (9) key\n        this.handleTab(event);\n      } else if (event.which === 13) {\n        // Enter (13) key\n        this.handleReturn(event);\n      } else if (event.which === 38) {\n        // Up (38) key\n        this.handleUpArrow(event);\n      } else if (event.which === 40) {\n        // Down (40) key press\n        this.handleDownArrow(event);\n      }\n    }\n  }, {\n    key: \"handleSelectItem\",\n    value: function handleSelectItem(i) {\n      this.commitSelection(i);\n    }\n  }, {\n    key: \"renderMenu\",\n    value: function renderMenu() {\n      var _this2 = this;\n\n      if (!this.state.active) {\n        return null;\n      }\n\n      var items = this.props.items;\n      return _react[\"default\"].createElement(_Menu[\"default\"], null, items.map(function (item, i) {\n        return _react[\"default\"].createElement(_MenuItem[\"default\"], {\n          item: item,\n          index: i,\n          onSelectItem: _this2.handleSelectItem,\n          activateIndex: _this2.state.activateIndex,\n          key: \"item-\".concat(i++)\n        });\n      }));\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this3 = this;\n\n      var _this$props = this.props,\n          inline = _this$props.inline,\n          className = _this$props.className;\n      return _react[\"default\"].createElement(_ClickableComponent[\"default\"], {\n        className: (0, _classnames[\"default\"])(className, {\n          'video-react-menu-button-inline': !!inline,\n          'video-react-menu-button-popup': !inline,\n          'video-react-menu-button-active': this.state.active\n        }, 'video-react-control video-react-button video-react-menu-button'),\n        role: \"button\",\n        tabIndex: \"0\",\n        ref: function ref(c) {\n          _this3.menuButton = c;\n        },\n        onClick: this.handleClick,\n        onFocus: this.handleFocus,\n        onBlur: this.handleBlur\n      }, this.props.children, this.renderMenu());\n    }\n  }]);\n  return MenuButton;\n}(_react.Component);\n\nexports[\"default\"] = MenuButton;\nMenuButton.propTypes = propTypes;\nMenuButton.displayName = 'MenuButton';"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,uBAAuB,GAAGC,OAAO,CAAC,+CAA+C,CAAC;AAEtF,IAAIC,sBAAsB,GAAGD,OAAO,CAAC,8CAA8C,CAAC;AAEpFE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;AAE3B,IAAIE,gBAAgB,GAAGL,sBAAsB,CAACD,OAAO,CAAC,uCAAuC,CAAC,CAAC;AAE/F,IAAIO,aAAa,GAAGN,sBAAsB,CAACD,OAAO,CAAC,oCAAoC,CAAC,CAAC;AAEzF,IAAIQ,2BAA2B,GAAGP,sBAAsB,CAACD,OAAO,CAAC,kDAAkD,CAAC,CAAC;AAErH,IAAIS,gBAAgB,GAAGR,sBAAsB,CAACD,OAAO,CAAC,uCAAuC,CAAC,CAAC;AAE/F,IAAIU,uBAAuB,GAAGT,sBAAsB,CAACD,OAAO,CAAC,8CAA8C,CAAC,CAAC;AAE7G,IAAIW,UAAU,GAAGV,sBAAsB,CAACD,OAAO,CAAC,iCAAiC,CAAC,CAAC;AAEnF,IAAIY,UAAU,GAAGX,sBAAsB,CAACD,OAAO,CAAC,YAAY,CAAC,CAAC;AAE9D,IAAIa,MAAM,GAAGd,uBAAuB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AAEtD,IAAIc,WAAW,GAAGb,sBAAsB,CAACD,OAAO,CAAC,YAAY,CAAC,CAAC;AAE/D,IAAIe,KAAK,GAAGd,sBAAsB,CAACD,OAAO,CAAC,QAAQ,CAAC,CAAC;AAErD,IAAIgB,SAAS,GAAGf,sBAAsB,CAACD,OAAO,CAAC,YAAY,CAAC,CAAC;AAE7D,IAAIiB,mBAAmB,GAAGhB,sBAAsB,CAACD,OAAO,CAAC,uBAAuB,CAAC,CAAC;AAElF,IAAIkB,SAAS,GAAG;EACdC,MAAM,EAAEP,UAAU,CAAC,SAAS,CAAC,CAACQ,IAAI;EAClCC,KAAK,EAAET,UAAU,CAAC,SAAS,CAAC,CAACU,KAAK;EAClCC,SAAS,EAAEX,UAAU,CAAC,SAAS,CAAC,CAACY,MAAM;EACvCC,YAAY,EAAEb,UAAU,CAAC,SAAS,CAAC,CAACc,IAAI;EACxCC,QAAQ,EAAEf,UAAU,CAAC,SAAS,CAAC,CAACgB,GAAG;EACnCC,aAAa,EAAEjB,UAAU,CAAC,SAAS,CAAC,CAACkB;AACvC,CAAC;AAED,IAAIC,UAAU,GACd;AACA,UAAUC,UAAU,EAAE;EACpB,CAAC,CAAC,EAAErB,UAAU,CAAC,SAAS,CAAC,EAAEoB,UAAU,EAAEC,UAAU,CAAC;EAElD,SAASD,UAAUA,CAACE,KAAK,EAAEC,OAAO,EAAE;IAClC,IAAIC,KAAK;IAET,CAAC,CAAC,EAAE7B,gBAAgB,CAAC,SAAS,CAAC,EAAE,IAAI,EAAEyB,UAAU,CAAC;IAClDI,KAAK,GAAG,CAAC,CAAC,EAAE3B,2BAA2B,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAEC,gBAAgB,CAAC,SAAS,CAAC,EAAEsB,UAAU,CAAC,CAACK,IAAI,CAAC,IAAI,EAAEH,KAAK,EAAEC,OAAO,CAAC,CAAC;IAClIC,KAAK,CAACE,KAAK,GAAG;MACZC,MAAM,EAAE,KAAK;MACbC,aAAa,EAAEN,KAAK,CAACJ,aAAa,IAAI;IACxC,CAAC;IACDM,KAAK,CAACK,eAAe,GAAGL,KAAK,CAACK,eAAe,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE/B,uBAAuB,CAAC,SAAS,CAAC,EAAEyB,KAAK,CAAC,CAAC;IAClGA,KAAK,CAACO,gBAAgB,GAAGP,KAAK,CAACO,gBAAgB,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE/B,uBAAuB,CAAC,SAAS,CAAC,EAAEyB,KAAK,CAAC,CAAC;IACpGA,KAAK,CAACQ,WAAW,GAAGR,KAAK,CAACQ,WAAW,CAACF,IAAI,CAAC,CAAC,CAAC,EAAE/B,uBAAuB,CAAC,SAAS,CAAC,EAAEyB,KAAK,CAAC,CAAC;IAC1FA,KAAK,CAACS,UAAU,GAAGT,KAAK,CAACS,UAAU,CAACH,IAAI,CAAC,CAAC,CAAC,EAAE/B,uBAAuB,CAAC,SAAS,CAAC,EAAEyB,KAAK,CAAC,CAAC;IACxFA,KAAK,CAACU,WAAW,GAAGV,KAAK,CAACU,WAAW,CAACJ,IAAI,CAAC,CAAC,CAAC,EAAE/B,uBAAuB,CAAC,SAAS,CAAC,EAAEyB,KAAK,CAAC,CAAC;IAC1FA,KAAK,CAACW,UAAU,GAAGX,KAAK,CAACW,UAAU,CAACL,IAAI,CAAC,CAAC,CAAC,EAAE/B,uBAAuB,CAAC,SAAS,CAAC,EAAEyB,KAAK,CAAC,CAAC;IACxFA,KAAK,CAACY,aAAa,GAAGZ,KAAK,CAACY,aAAa,CAACN,IAAI,CAAC,CAAC,CAAC,EAAE/B,uBAAuB,CAAC,SAAS,CAAC,EAAEyB,KAAK,CAAC,CAAC;IAC9FA,KAAK,CAACa,eAAe,GAAGb,KAAK,CAACa,eAAe,CAACP,IAAI,CAAC,CAAC,CAAC,EAAE/B,uBAAuB,CAAC,SAAS,CAAC,EAAEyB,KAAK,CAAC,CAAC;IAClGA,KAAK,CAACc,YAAY,GAAGd,KAAK,CAACc,YAAY,CAACR,IAAI,CAAC,CAAC,CAAC,EAAE/B,uBAAuB,CAAC,SAAS,CAAC,EAAEyB,KAAK,CAAC,CAAC;IAC5FA,KAAK,CAACe,YAAY,GAAGf,KAAK,CAACe,YAAY,CAACT,IAAI,CAAC,CAAC,CAAC,EAAE/B,uBAAuB,CAAC,SAAS,CAAC,EAAEyB,KAAK,CAAC,CAAC;IAC5FA,KAAK,CAACgB,SAAS,GAAGhB,KAAK,CAACgB,SAAS,CAACV,IAAI,CAAC,CAAC,CAAC,EAAE/B,uBAAuB,CAAC,SAAS,CAAC,EAAEyB,KAAK,CAAC,CAAC;IACtFA,KAAK,CAACiB,cAAc,GAAGjB,KAAK,CAACiB,cAAc,CAACX,IAAI,CAAC,CAAC,CAAC,EAAE/B,uBAAuB,CAAC,SAAS,CAAC,EAAEyB,KAAK,CAAC,CAAC;IAChGA,KAAK,CAACkB,gBAAgB,GAAGlB,KAAK,CAACkB,gBAAgB,CAACZ,IAAI,CAAC,CAAC,CAAC,EAAE/B,uBAAuB,CAAC,SAAS,CAAC,EAAEyB,KAAK,CAAC,CAAC;IACpGA,KAAK,CAACmB,iBAAiB,GAAGnB,KAAK,CAACmB,iBAAiB,CAACb,IAAI,CAAC,CAAC,CAAC,EAAE/B,uBAAuB,CAAC,SAAS,CAAC,EAAEyB,KAAK,CAAC,CAAC;IACtG,OAAOA,KAAK;EACd;EAEA,CAAC,CAAC,EAAE5B,aAAa,CAAC,SAAS,CAAC,EAAEwB,UAAU,EAAE,CAAC;IACzCwB,GAAG,EAAE,oBAAoB;IACzBlD,KAAK,EAAE,SAASmD,kBAAkBA,CAACC,SAAS,EAAE;MAC5C,IAAIA,SAAS,CAAC5B,aAAa,KAAK,IAAI,CAACI,KAAK,CAACJ,aAAa,EAAE;QACxD,IAAI,CAACa,gBAAgB,CAAC,IAAI,CAACT,KAAK,CAACJ,aAAa,CAAC;MACjD;IACF;EACF,CAAC,EAAE;IACD0B,GAAG,EAAE,iBAAiB;IACtBlD,KAAK,EAAE,SAASmC,eAAeA,CAACkB,KAAK,EAAE;MACrC,IAAI,CAACC,QAAQ,CAAC;QACZpB,aAAa,EAAEmB;MACjB,CAAC,CAAC;MACF,IAAI,CAACJ,iBAAiB,CAACI,KAAK,CAAC;IAC/B;EACF,CAAC,EAAE;IACDH,GAAG,EAAE,kBAAkB;IACvBlD,KAAK,EAAE,SAASqC,gBAAgBA,CAACgB,KAAK,EAAE;MACtC,IAAI,CAACC,QAAQ,CAAC;QACZpB,aAAa,EAAEmB;MACjB,CAAC,CAAC;MACF,IAAI,CAACJ,iBAAiB,CAACI,KAAK,CAAC;IAC/B;EACF,CAAC,EAAE;IACDH,GAAG,EAAE,mBAAmB;IACxBlD,KAAK,EAAE,SAASiD,iBAAiBA,CAACI,KAAK,EAAE;MACvC,IAAIjC,YAAY,GAAG,IAAI,CAACQ,KAAK,CAACR,YAAY;MAC1CA,YAAY,CAACiC,KAAK,CAAC;IACrB;EACF,CAAC,EAAE;IACDH,GAAG,EAAE,aAAa;IAClBlD,KAAK,EAAE,SAASsC,WAAWA,CAAA,EAAG;MAC5B,IAAI,CAACgB,QAAQ,CAAC,UAAUC,SAAS,EAAE;QACjC,OAAO;UACLtB,MAAM,EAAE,CAACsB,SAAS,CAACtB;QACrB,CAAC;MACH,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDiB,GAAG,EAAE,aAAa;IAClBlD,KAAK,EAAE,SAASwC,WAAWA,CAAA,EAAG;MAC5BgB,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAACV,cAAc,CAAC;IAC3D;EACF,CAAC,EAAE;IACDG,GAAG,EAAE,YAAY;IACjBlD,KAAK,EAAE,SAASyC,UAAUA,CAAA,EAAG;MAC3B,IAAI,CAACa,QAAQ,CAAC;QACZrB,MAAM,EAAE;MACV,CAAC,CAAC;MACFuB,QAAQ,CAACE,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAACX,cAAc,CAAC;IAC9D;EACF,CAAC,EAAE;IACDG,GAAG,EAAE,eAAe;IACpBlD,KAAK,EAAE,SAAS0C,aAAaA,CAACiB,CAAC,EAAE;MAC/B,IAAI3C,KAAK,GAAG,IAAI,CAACY,KAAK,CAACZ,KAAK;MAE5B,IAAI,IAAI,CAACgB,KAAK,CAACC,MAAM,EAAE;QACrB0B,CAAC,CAACC,cAAc,EAAE;QAClB,IAAIC,QAAQ,GAAG,IAAI,CAAC7B,KAAK,CAACE,aAAa,GAAG,CAAC;QAE3C,IAAI2B,QAAQ,GAAG,CAAC,EAAE;UAChBA,QAAQ,GAAG7C,KAAK,CAAC8C,MAAM,GAAG9C,KAAK,CAAC8C,MAAM,GAAG,CAAC,GAAG,CAAC;QAChD;QAEA,IAAI,CAACzB,gBAAgB,CAACwB,QAAQ,CAAC;MACjC;IACF;EACF,CAAC,EAAE;IACDX,GAAG,EAAE,iBAAiB;IACtBlD,KAAK,EAAE,SAAS2C,eAAeA,CAACgB,CAAC,EAAE;MACjC,IAAI3C,KAAK,GAAG,IAAI,CAACY,KAAK,CAACZ,KAAK;MAE5B,IAAI,IAAI,CAACgB,KAAK,CAACC,MAAM,EAAE;QACrB0B,CAAC,CAACC,cAAc,EAAE;QAClB,IAAIC,QAAQ,GAAG,IAAI,CAAC7B,KAAK,CAACE,aAAa,GAAG,CAAC;QAE3C,IAAI2B,QAAQ,IAAI7C,KAAK,CAAC8C,MAAM,EAAE;UAC5BD,QAAQ,GAAG,CAAC;QACd;QAEA,IAAI,CAACxB,gBAAgB,CAACwB,QAAQ,CAAC;MACjC;IACF;EACF,CAAC,EAAE;IACDX,GAAG,EAAE,WAAW;IAChBlD,KAAK,EAAE,SAAS8C,SAASA,CAACa,CAAC,EAAE;MAC3B,IAAI,IAAI,CAAC3B,KAAK,CAACC,MAAM,EAAE;QACrB0B,CAAC,CAACC,cAAc,EAAE;QAClB,IAAI,CAACzB,eAAe,CAAC,IAAI,CAACH,KAAK,CAACE,aAAa,CAAC;MAChD;IACF;EACF,CAAC,EAAE;IACDgB,GAAG,EAAE,cAAc;IACnBlD,KAAK,EAAE,SAAS6C,YAAYA,CAACc,CAAC,EAAE;MAC9BA,CAAC,CAACC,cAAc,EAAE;MAElB,IAAI,IAAI,CAAC5B,KAAK,CAACC,MAAM,EAAE;QACrB,IAAI,CAACE,eAAe,CAAC,IAAI,CAACH,KAAK,CAACE,aAAa,CAAC;MAChD,CAAC,MAAM;QACL,IAAI,CAACoB,QAAQ,CAAC;UACZrB,MAAM,EAAE;QACV,CAAC,CAAC;MACJ;IACF;EACF,CAAC,EAAE;IACDiB,GAAG,EAAE,cAAc;IACnBlD,KAAK,EAAE,SAAS4C,YAAYA,CAAA,EAAG;MAC7B,IAAI,CAACU,QAAQ,CAAC;QACZrB,MAAM,EAAE,KAAK;QACbC,aAAa,EAAE;MACjB,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDgB,GAAG,EAAE,gBAAgB;IACrBlD,KAAK,EAAE,SAAS+C,cAAcA,CAACgB,KAAK,EAAE;MACpC;MACA,IAAIA,KAAK,CAACC,KAAK,KAAK,EAAE,EAAE;QACtB,IAAI,CAACpB,YAAY,CAACmB,KAAK,CAAC;MAC1B,CAAC,MAAM,IAAIA,KAAK,CAACC,KAAK,KAAK,CAAC,EAAE;QAC5B;QACA,IAAI,CAAClB,SAAS,CAACiB,KAAK,CAAC;MACvB,CAAC,MAAM,IAAIA,KAAK,CAACC,KAAK,KAAK,EAAE,EAAE;QAC7B;QACA,IAAI,CAACnB,YAAY,CAACkB,KAAK,CAAC;MAC1B,CAAC,MAAM,IAAIA,KAAK,CAACC,KAAK,KAAK,EAAE,EAAE;QAC7B;QACA,IAAI,CAACtB,aAAa,CAACqB,KAAK,CAAC;MAC3B,CAAC,MAAM,IAAIA,KAAK,CAACC,KAAK,KAAK,EAAE,EAAE;QAC7B;QACA,IAAI,CAACrB,eAAe,CAACoB,KAAK,CAAC;MAC7B;IACF;EACF,CAAC,EAAE;IACDb,GAAG,EAAE,kBAAkB;IACvBlD,KAAK,EAAE,SAASgD,gBAAgBA,CAACiB,CAAC,EAAE;MAClC,IAAI,CAAC9B,eAAe,CAAC8B,CAAC,CAAC;IACzB;EACF,CAAC,EAAE;IACDf,GAAG,EAAE,YAAY;IACjBlD,KAAK,EAAE,SAASuC,UAAUA,CAAA,EAAG;MAC3B,IAAI2B,MAAM,GAAG,IAAI;MAEjB,IAAI,CAAC,IAAI,CAAClC,KAAK,CAACC,MAAM,EAAE;QACtB,OAAO,IAAI;MACb;MAEA,IAAIjB,KAAK,GAAG,IAAI,CAACY,KAAK,CAACZ,KAAK;MAC5B,OAAOR,MAAM,CAAC,SAAS,CAAC,CAAC2D,aAAa,CAACzD,KAAK,CAAC,SAAS,CAAC,EAAE,IAAI,EAAEM,KAAK,CAACoD,GAAG,CAAC,UAAUC,IAAI,EAAEJ,CAAC,EAAE;QAC1F,OAAOzD,MAAM,CAAC,SAAS,CAAC,CAAC2D,aAAa,CAACxD,SAAS,CAAC,SAAS,CAAC,EAAE;UAC3D0D,IAAI,EAAEA,IAAI;UACVhB,KAAK,EAAEY,CAAC;UACR7C,YAAY,EAAE8C,MAAM,CAAClB,gBAAgB;UACrCd,aAAa,EAAEgC,MAAM,CAAClC,KAAK,CAACE,aAAa;UACzCgB,GAAG,EAAE,OAAO,CAACoB,MAAM,CAACL,CAAC,EAAE;QACzB,CAAC,CAAC;MACJ,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE;IACDf,GAAG,EAAE,QAAQ;IACblD,KAAK,EAAE,SAASuE,MAAMA,CAAA,EAAG;MACvB,IAAIC,MAAM,GAAG,IAAI;MAEjB,IAAIC,WAAW,GAAG,IAAI,CAAC7C,KAAK;QACxBd,MAAM,GAAG2D,WAAW,CAAC3D,MAAM;QAC3BI,SAAS,GAAGuD,WAAW,CAACvD,SAAS;MACrC,OAAOV,MAAM,CAAC,SAAS,CAAC,CAAC2D,aAAa,CAACvD,mBAAmB,CAAC,SAAS,CAAC,EAAE;QACrEM,SAAS,EAAE,CAAC,CAAC,EAAET,WAAW,CAAC,SAAS,CAAC,EAAES,SAAS,EAAE;UAChD,gCAAgC,EAAE,CAAC,CAACJ,MAAM;UAC1C,+BAA+B,EAAE,CAACA,MAAM;UACxC,gCAAgC,EAAE,IAAI,CAACkB,KAAK,CAACC;QAC/C,CAAC,EAAE,gEAAgE,CAAC;QACpEyC,IAAI,EAAE,QAAQ;QACdC,QAAQ,EAAE,GAAG;QACbC,GAAG,EAAE,SAASA,GAAGA,CAACC,CAAC,EAAE;UACnBL,MAAM,CAACM,UAAU,GAAGD,CAAC;QACvB,CAAC;QACDE,OAAO,EAAE,IAAI,CAACzC,WAAW;QACzB0C,OAAO,EAAE,IAAI,CAACxC,WAAW;QACzByC,MAAM,EAAE,IAAI,CAACxC;MACf,CAAC,EAAE,IAAI,CAACb,KAAK,CAACN,QAAQ,EAAE,IAAI,CAACiB,UAAU,EAAE,CAAC;IAC5C;EACF,CAAC,CAAC,CAAC;EACH,OAAOb,UAAU;AACnB,CAAC,CAAClB,MAAM,CAAC0E,SAAS,CAAC;AAEnBnF,OAAO,CAAC,SAAS,CAAC,GAAG2B,UAAU;AAC/BA,UAAU,CAACb,SAAS,GAAGA,SAAS;AAChCa,UAAU,CAACyD,WAAW,GAAG,YAAY"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}