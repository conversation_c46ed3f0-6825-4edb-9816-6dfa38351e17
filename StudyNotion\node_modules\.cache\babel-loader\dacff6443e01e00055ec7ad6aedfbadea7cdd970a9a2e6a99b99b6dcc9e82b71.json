{"ast": null, "code": "import appendSlide from './methods/appendSlide.js';\nimport prependSlide from './methods/prependSlide.js';\nimport addSlide from './methods/addSlide.js';\nimport removeSlide from './methods/removeSlide.js';\nimport removeAllSlides from './methods/removeAllSlides.js';\nexport default function Manipulation(_ref) {\n  let {\n    swiper\n  } = _ref;\n  Object.assign(swiper, {\n    appendSlide: appendSlide.bind(swiper),\n    prependSlide: prependSlide.bind(swiper),\n    addSlide: addSlide.bind(swiper),\n    removeSlide: removeSlide.bind(swiper),\n    removeAllSlides: removeAllSlides.bind(swiper)\n  });\n}", "map": {"version": 3, "names": ["appendSlide", "prependSlide", "addSlide", "removeSlide", "removeAllSlides", "Manipulation", "_ref", "swiper", "Object", "assign", "bind"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/modules/manipulation/manipulation.js"], "sourcesContent": ["import appendSlide from './methods/appendSlide.js';\nimport prependSlide from './methods/prependSlide.js';\nimport addSlide from './methods/addSlide.js';\nimport removeSlide from './methods/removeSlide.js';\nimport removeAllSlides from './methods/removeAllSlides.js';\nexport default function Manipulation({\n  swiper\n}) {\n  Object.assign(swiper, {\n    appendSlide: appendSlide.bind(swiper),\n    prependSlide: prependSlide.bind(swiper),\n    addSlide: addSlide.bind(swiper),\n    removeSlide: removeSlide.bind(swiper),\n    removeAllSlides: removeAllSlides.bind(swiper)\n  });\n}"], "mappings": "AAAA,OAAOA,WAAW,MAAM,0BAA0B;AAClD,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,eAAe,MAAM,8BAA8B;AAC1D,eAAe,SAASC,YAAYA,CAAAC,IAAA,EAEjC;EAAA,IAFkC;IACnCC;EACF,CAAC,GAAAD,IAAA;EACCE,MAAM,CAACC,MAAM,CAACF,MAAM,EAAE;IACpBP,WAAW,EAAEA,WAAW,CAACU,IAAI,CAACH,MAAM,CAAC;IACrCN,YAAY,EAAEA,YAAY,CAACS,IAAI,CAACH,MAAM,CAAC;IACvCL,QAAQ,EAAEA,QAAQ,CAACQ,IAAI,CAACH,MAAM,CAAC;IAC/BJ,WAAW,EAAEA,WAAW,CAACO,IAAI,CAACH,MAAM,CAAC;IACrCH,eAAe,EAAEA,eAAe,CAACM,IAAI,CAACH,MAAM;EAC9C,CAAC,CAAC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}