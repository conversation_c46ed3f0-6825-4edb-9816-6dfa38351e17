{"ast": null, "code": "import { getWindow } from 'ssr-window';\nlet browser;\nfunction calcBrowser() {\n  const window = getWindow();\n  let needPerspectiveFix = false;\n  function isSafari() {\n    const ua = window.navigator.userAgent.toLowerCase();\n    return ua.indexOf('safari') >= 0 && ua.indexOf('chrome') < 0 && ua.indexOf('android') < 0;\n  }\n  if (isSafari()) {\n    const ua = String(window.navigator.userAgent);\n    if (ua.includes('Version/')) {\n      const [major, minor] = ua.split('Version/')[1].split(' ')[0].split('.').map(num => Number(num));\n      needPerspectiveFix = major < 16 || major === 16 && minor < 2;\n    }\n  }\n  return {\n    isSafari: needPerspectiveFix || isSafari(),\n    needPerspectiveFix,\n    isWebView: /(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(window.navigator.userAgent)\n  };\n}\nfunction getBrowser() {\n  if (!browser) {\n    browser = calcBrowser();\n  }\n  return browser;\n}\nexport { getBrowser };", "map": {"version": 3, "names": ["getWindow", "browser", "calcB<PERSON>er", "window", "needPerspectiveFix", "<PERSON><PERSON><PERSON><PERSON>", "ua", "navigator", "userAgent", "toLowerCase", "indexOf", "String", "includes", "major", "minor", "split", "map", "num", "Number", "isWebView", "test", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/shared/get-browser.js"], "sourcesContent": ["import { getWindow } from 'ssr-window';\nlet browser;\nfunction calcBrowser() {\n  const window = getWindow();\n  let needPerspectiveFix = false;\n  function isSafari() {\n    const ua = window.navigator.userAgent.toLowerCase();\n    return ua.indexOf('safari') >= 0 && ua.indexOf('chrome') < 0 && ua.indexOf('android') < 0;\n  }\n  if (isSafari()) {\n    const ua = String(window.navigator.userAgent);\n    if (ua.includes('Version/')) {\n      const [major, minor] = ua.split('Version/')[1].split(' ')[0].split('.').map(num => Number(num));\n      needPerspectiveFix = major < 16 || major === 16 && minor < 2;\n    }\n  }\n  return {\n    isSafari: needPerspectiveFix || isSafari(),\n    needPerspectiveFix,\n    isWebView: /(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(window.navigator.userAgent)\n  };\n}\nfunction getBrowser() {\n  if (!browser) {\n    browser = calcBrowser();\n  }\n  return browser;\n}\nexport { getBrowser };"], "mappings": "AAAA,SAASA,SAAS,QAAQ,YAAY;AACtC,IAAIC,OAAO;AACX,SAASC,WAAWA,CAAA,EAAG;EACrB,MAAMC,MAAM,GAAGH,SAAS,EAAE;EAC1B,IAAII,kBAAkB,GAAG,KAAK;EAC9B,SAASC,QAAQA,CAAA,EAAG;IAClB,MAAMC,EAAE,GAAGH,MAAM,CAACI,SAAS,CAACC,SAAS,CAACC,WAAW,EAAE;IACnD,OAAOH,EAAE,CAACI,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAIJ,EAAE,CAACI,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAIJ,EAAE,CAACI,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC;EAC3F;EACA,IAAIL,QAAQ,EAAE,EAAE;IACd,MAAMC,EAAE,GAAGK,MAAM,CAACR,MAAM,CAACI,SAAS,CAACC,SAAS,CAAC;IAC7C,IAAIF,EAAE,CAACM,QAAQ,CAAC,UAAU,CAAC,EAAE;MAC3B,MAAM,CAACC,KAAK,EAAEC,KAAK,CAAC,GAAGR,EAAE,CAACS,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAACA,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACA,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,GAAG,IAAIC,MAAM,CAACD,GAAG,CAAC,CAAC;MAC/Fb,kBAAkB,GAAGS,KAAK,GAAG,EAAE,IAAIA,KAAK,KAAK,EAAE,IAAIC,KAAK,GAAG,CAAC;IAC9D;EACF;EACA,OAAO;IACLT,QAAQ,EAAED,kBAAkB,IAAIC,QAAQ,EAAE;IAC1CD,kBAAkB;IAClBe,SAAS,EAAE,8CAA8C,CAACC,IAAI,CAACjB,MAAM,CAACI,SAAS,CAACC,SAAS;EAC3F,CAAC;AACH;AACA,SAASa,UAAUA,CAAA,EAAG;EACpB,IAAI,CAACpB,OAAO,EAAE;IACZA,OAAO,GAAGC,WAAW,EAAE;EACzB;EACA,OAAOD,OAAO;AAChB;AACA,SAASoB,UAAU"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}