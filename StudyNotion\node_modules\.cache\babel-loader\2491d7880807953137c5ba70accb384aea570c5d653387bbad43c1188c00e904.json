{"ast": null, "code": "import { getDocument } from 'ssr-window';\nimport onTouchStart from './onTouchStart.js';\nimport onTouchMove from './onTouchMove.js';\nimport onTouchEnd from './onTouchEnd.js';\nimport onResize from './onResize.js';\nimport onClick from './onClick.js';\nimport onScroll from './onScroll.js';\nimport onLoad from './onLoad.js';\nlet dummyEventAttached = false;\nfunction dummyEventListener() {}\nconst events = (swiper, method) => {\n  const document = getDocument();\n  const {\n    params,\n    el,\n    wrapperEl,\n    device\n  } = swiper;\n  const capture = !!params.nested;\n  const domMethod = method === 'on' ? 'addEventListener' : 'removeEventListener';\n  const swiperMethod = method;\n\n  // Touch Events\n  el[domMethod]('pointerdown', swiper.onTouchStart, {\n    passive: false\n  });\n  document[domMethod]('pointermove', swiper.onTouchMove, {\n    passive: false,\n    capture\n  });\n  document[domMethod]('pointerup', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('pointercancel', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('pointerout', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('pointerleave', swiper.onTouchEnd, {\n    passive: true\n  });\n\n  // Prevent Links Clicks\n  if (params.preventClicks || params.preventClicksPropagation) {\n    el[domMethod]('click', swiper.onClick, true);\n  }\n  if (params.cssMode) {\n    wrapperEl[domMethod]('scroll', swiper.onScroll);\n  }\n\n  // Resize handler\n  if (params.updateOnWindowResize) {\n    swiper[swiperMethod](device.ios || device.android ? 'resize orientationchange observerUpdate' : 'resize observerUpdate', onResize, true);\n  } else {\n    swiper[swiperMethod]('observerUpdate', onResize, true);\n  }\n\n  // Images loader\n  el[domMethod]('load', swiper.onLoad, {\n    capture: true\n  });\n};\nfunction attachEvents() {\n  const swiper = this;\n  const document = getDocument();\n  const {\n    params\n  } = swiper;\n  swiper.onTouchStart = onTouchStart.bind(swiper);\n  swiper.onTouchMove = onTouchMove.bind(swiper);\n  swiper.onTouchEnd = onTouchEnd.bind(swiper);\n  if (params.cssMode) {\n    swiper.onScroll = onScroll.bind(swiper);\n  }\n  swiper.onClick = onClick.bind(swiper);\n  swiper.onLoad = onLoad.bind(swiper);\n  if (!dummyEventAttached) {\n    document.addEventListener('touchstart', dummyEventListener);\n    dummyEventAttached = true;\n  }\n  events(swiper, 'on');\n}\nfunction detachEvents() {\n  const swiper = this;\n  events(swiper, 'off');\n}\nexport default {\n  attachEvents,\n  detachEvents\n};", "map": {"version": 3, "names": ["getDocument", "onTouchStart", "onTouchMove", "onTouchEnd", "onResize", "onClick", "onScroll", "onLoad", "dummy<PERSON><PERSON><PERSON>ttached", "dummyEventListener", "events", "swiper", "method", "document", "params", "el", "wrapperEl", "device", "capture", "nested", "dom<PERSON>ethod", "swiperMethod", "passive", "preventClicks", "preventClicksPropagation", "cssMode", "updateOnWindowResize", "ios", "android", "attachEvents", "bind", "addEventListener", "detachEvents"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/core/events/index.js"], "sourcesContent": ["import { getDocument } from 'ssr-window';\nimport onTouchStart from './onTouchStart.js';\nimport onTouchMove from './onTouchMove.js';\nimport onTouchEnd from './onTouchEnd.js';\nimport onResize from './onResize.js';\nimport onClick from './onClick.js';\nimport onScroll from './onScroll.js';\nimport onLoad from './onLoad.js';\nlet dummyEventAttached = false;\nfunction dummyEventListener() {}\nconst events = (swiper, method) => {\n  const document = getDocument();\n  const {\n    params,\n    el,\n    wrapperEl,\n    device\n  } = swiper;\n  const capture = !!params.nested;\n  const domMethod = method === 'on' ? 'addEventListener' : 'removeEventListener';\n  const swiperMethod = method;\n\n  // Touch Events\n  el[domMethod]('pointerdown', swiper.onTouchStart, {\n    passive: false\n  });\n  document[domMethod]('pointermove', swiper.onTouchMove, {\n    passive: false,\n    capture\n  });\n  document[domMethod]('pointerup', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('pointercancel', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('pointerout', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('pointerleave', swiper.onTouchEnd, {\n    passive: true\n  });\n\n  // Prevent Links Clicks\n  if (params.preventClicks || params.preventClicksPropagation) {\n    el[domMethod]('click', swiper.onClick, true);\n  }\n  if (params.cssMode) {\n    wrapperEl[domMethod]('scroll', swiper.onScroll);\n  }\n\n  // Resize handler\n  if (params.updateOnWindowResize) {\n    swiper[swiperMethod](device.ios || device.android ? 'resize orientationchange observerUpdate' : 'resize observerUpdate', onResize, true);\n  } else {\n    swiper[swiperMethod]('observerUpdate', onResize, true);\n  }\n\n  // Images loader\n  el[domMethod]('load', swiper.onLoad, {\n    capture: true\n  });\n};\nfunction attachEvents() {\n  const swiper = this;\n  const document = getDocument();\n  const {\n    params\n  } = swiper;\n  swiper.onTouchStart = onTouchStart.bind(swiper);\n  swiper.onTouchMove = onTouchMove.bind(swiper);\n  swiper.onTouchEnd = onTouchEnd.bind(swiper);\n  if (params.cssMode) {\n    swiper.onScroll = onScroll.bind(swiper);\n  }\n  swiper.onClick = onClick.bind(swiper);\n  swiper.onLoad = onLoad.bind(swiper);\n  if (!dummyEventAttached) {\n    document.addEventListener('touchstart', dummyEventListener);\n    dummyEventAttached = true;\n  }\n  events(swiper, 'on');\n}\nfunction detachEvents() {\n  const swiper = this;\n  events(swiper, 'off');\n}\nexport default {\n  attachEvents,\n  detachEvents\n};"], "mappings": "AAAA,SAASA,WAAW,QAAQ,YAAY;AACxC,OAAOC,YAAY,MAAM,mBAAmB;AAC5C,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,OAAOC,UAAU,MAAM,iBAAiB;AACxC,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAOC,OAAO,MAAM,cAAc;AAClC,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAOC,MAAM,MAAM,aAAa;AAChC,IAAIC,kBAAkB,GAAG,KAAK;AAC9B,SAASC,kBAAkBA,CAAA,EAAG,CAAC;AAC/B,MAAMC,MAAM,GAAGA,CAACC,MAAM,EAAEC,MAAM,KAAK;EACjC,MAAMC,QAAQ,GAAGb,WAAW,EAAE;EAC9B,MAAM;IACJc,MAAM;IACNC,EAAE;IACFC,SAAS;IACTC;EACF,CAAC,GAAGN,MAAM;EACV,MAAMO,OAAO,GAAG,CAAC,CAACJ,MAAM,CAACK,MAAM;EAC/B,MAAMC,SAAS,GAAGR,MAAM,KAAK,IAAI,GAAG,kBAAkB,GAAG,qBAAqB;EAC9E,MAAMS,YAAY,GAAGT,MAAM;;EAE3B;EACAG,EAAE,CAACK,SAAS,CAAC,CAAC,aAAa,EAAET,MAAM,CAACV,YAAY,EAAE;IAChDqB,OAAO,EAAE;EACX,CAAC,CAAC;EACFT,QAAQ,CAACO,SAAS,CAAC,CAAC,aAAa,EAAET,MAAM,CAACT,WAAW,EAAE;IACrDoB,OAAO,EAAE,KAAK;IACdJ;EACF,CAAC,CAAC;EACFL,QAAQ,CAACO,SAAS,CAAC,CAAC,WAAW,EAAET,MAAM,CAACR,UAAU,EAAE;IAClDmB,OAAO,EAAE;EACX,CAAC,CAAC;EACFT,QAAQ,CAACO,SAAS,CAAC,CAAC,eAAe,EAAET,MAAM,CAACR,UAAU,EAAE;IACtDmB,OAAO,EAAE;EACX,CAAC,CAAC;EACFT,QAAQ,CAACO,SAAS,CAAC,CAAC,YAAY,EAAET,MAAM,CAACR,UAAU,EAAE;IACnDmB,OAAO,EAAE;EACX,CAAC,CAAC;EACFT,QAAQ,CAACO,SAAS,CAAC,CAAC,cAAc,EAAET,MAAM,CAACR,UAAU,EAAE;IACrDmB,OAAO,EAAE;EACX,CAAC,CAAC;;EAEF;EACA,IAAIR,MAAM,CAACS,aAAa,IAAIT,MAAM,CAACU,wBAAwB,EAAE;IAC3DT,EAAE,CAACK,SAAS,CAAC,CAAC,OAAO,EAAET,MAAM,CAACN,OAAO,EAAE,IAAI,CAAC;EAC9C;EACA,IAAIS,MAAM,CAACW,OAAO,EAAE;IAClBT,SAAS,CAACI,SAAS,CAAC,CAAC,QAAQ,EAAET,MAAM,CAACL,QAAQ,CAAC;EACjD;;EAEA;EACA,IAAIQ,MAAM,CAACY,oBAAoB,EAAE;IAC/Bf,MAAM,CAACU,YAAY,CAAC,CAACJ,MAAM,CAACU,GAAG,IAAIV,MAAM,CAACW,OAAO,GAAG,yCAAyC,GAAG,uBAAuB,EAAExB,QAAQ,EAAE,IAAI,CAAC;EAC1I,CAAC,MAAM;IACLO,MAAM,CAACU,YAAY,CAAC,CAAC,gBAAgB,EAAEjB,QAAQ,EAAE,IAAI,CAAC;EACxD;;EAEA;EACAW,EAAE,CAACK,SAAS,CAAC,CAAC,MAAM,EAAET,MAAM,CAACJ,MAAM,EAAE;IACnCW,OAAO,EAAE;EACX,CAAC,CAAC;AACJ,CAAC;AACD,SAASW,YAAYA,CAAA,EAAG;EACtB,MAAMlB,MAAM,GAAG,IAAI;EACnB,MAAME,QAAQ,GAAGb,WAAW,EAAE;EAC9B,MAAM;IACJc;EACF,CAAC,GAAGH,MAAM;EACVA,MAAM,CAACV,YAAY,GAAGA,YAAY,CAAC6B,IAAI,CAACnB,MAAM,CAAC;EAC/CA,MAAM,CAACT,WAAW,GAAGA,WAAW,CAAC4B,IAAI,CAACnB,MAAM,CAAC;EAC7CA,MAAM,CAACR,UAAU,GAAGA,UAAU,CAAC2B,IAAI,CAACnB,MAAM,CAAC;EAC3C,IAAIG,MAAM,CAACW,OAAO,EAAE;IAClBd,MAAM,CAACL,QAAQ,GAAGA,QAAQ,CAACwB,IAAI,CAACnB,MAAM,CAAC;EACzC;EACAA,MAAM,CAACN,OAAO,GAAGA,OAAO,CAACyB,IAAI,CAACnB,MAAM,CAAC;EACrCA,MAAM,CAACJ,MAAM,GAAGA,MAAM,CAACuB,IAAI,CAACnB,MAAM,CAAC;EACnC,IAAI,CAACH,kBAAkB,EAAE;IACvBK,QAAQ,CAACkB,gBAAgB,CAAC,YAAY,EAAEtB,kBAAkB,CAAC;IAC3DD,kBAAkB,GAAG,IAAI;EAC3B;EACAE,MAAM,CAACC,MAAM,EAAE,IAAI,CAAC;AACtB;AACA,SAASqB,YAAYA,CAAA,EAAG;EACtB,MAAMrB,MAAM,GAAG,IAAI;EACnBD,MAAM,CAACC,MAAM,EAAE,KAAK,CAAC;AACvB;AACA,eAAe;EACbkB,YAAY;EACZG;AACF,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}