{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\");\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nvar _classCallCheck2 = _interopRequireDefault(require(\"@babel/runtime/helpers/classCallCheck\"));\nvar _createClass2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createClass\"));\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"@babel/runtime/helpers/possibleConstructorReturn\"));\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"@babel/runtime/helpers/getPrototypeOf\"));\nvar _assertThisInitialized2 = _interopRequireDefault(require(\"@babel/runtime/helpers/assertThisInitialized\"));\nvar _inherits2 = _interopRequireDefault(require(\"@babel/runtime/helpers/inherits\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _react = _interopRequireWildcard(require(\"react\"));\nvar _classnames = _interopRequireDefault(require(\"classnames\"));\nvar propTypes = {\n  item: _propTypes[\"default\"].object,\n  index: _propTypes[\"default\"].number,\n  activateIndex: _propTypes[\"default\"].number,\n  onSelectItem: _propTypes[\"default\"].func\n};\nvar MenuItem = /*#__PURE__*/\nfunction (_Component) {\n  (0, _inherits2[\"default\"])(MenuItem, _Component);\n  function MenuItem(props, context) {\n    var _this;\n    (0, _classCallCheck2[\"default\"])(this, MenuItem);\n    _this = (0, _possibleConstructorReturn2[\"default\"])(this, (0, _getPrototypeOf2[\"default\"])(MenuItem).call(this, props, context));\n    _this.handleClick = _this.handleClick.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    return _this;\n  }\n  (0, _createClass2[\"default\"])(MenuItem, [{\n    key: \"handleClick\",\n    value: function handleClick() {\n      var _this$props = this.props,\n        index = _this$props.index,\n        onSelectItem = _this$props.onSelectItem;\n      onSelectItem(index);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props2 = this.props,\n        item = _this$props2.item,\n        index = _this$props2.index,\n        activateIndex = _this$props2.activateIndex;\n      return _react[\"default\"].createElement(\"li\", {\n        className: (0, _classnames[\"default\"])({\n          'video-react-menu-item': true,\n          'video-react-selected': index === activateIndex\n        }),\n        role: \"menuitem\",\n        onClick: this.handleClick\n      }, item.label, _react[\"default\"].createElement(\"span\", {\n        className: \"video-react-control-text\"\n      }));\n    }\n  }]);\n  return MenuItem;\n}(_react.Component);\nexports[\"default\"] = MenuItem;\nMenuItem.propTypes = propTypes;\nMenuItem.displayName = 'MenuItem';", "map": {"version": 3, "names": ["_interopRequireWildcard", "require", "_interopRequireDefault", "Object", "defineProperty", "exports", "value", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_assertThisInitialized2", "_inherits2", "_propTypes", "_react", "_classnames", "propTypes", "item", "object", "index", "number", "activateIndex", "onSelectItem", "func", "MenuItem", "_Component", "props", "context", "_this", "call", "handleClick", "bind", "key", "_this$props", "render", "_this$props2", "createElement", "className", "role", "onClick", "label", "Component", "displayName"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/video-react/lib/components/menu/MenuItem.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\");\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _classCallCheck2 = _interopRequireDefault(require(\"@babel/runtime/helpers/classCallCheck\"));\n\nvar _createClass2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createClass\"));\n\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"@babel/runtime/helpers/possibleConstructorReturn\"));\n\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"@babel/runtime/helpers/getPrototypeOf\"));\n\nvar _assertThisInitialized2 = _interopRequireDefault(require(\"@babel/runtime/helpers/assertThisInitialized\"));\n\nvar _inherits2 = _interopRequireDefault(require(\"@babel/runtime/helpers/inherits\"));\n\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\n\nvar _react = _interopRequireWildcard(require(\"react\"));\n\nvar _classnames = _interopRequireDefault(require(\"classnames\"));\n\nvar propTypes = {\n  item: _propTypes[\"default\"].object,\n  index: _propTypes[\"default\"].number,\n  activateIndex: _propTypes[\"default\"].number,\n  onSelectItem: _propTypes[\"default\"].func\n};\n\nvar MenuItem =\n/*#__PURE__*/\nfunction (_Component) {\n  (0, _inherits2[\"default\"])(MenuItem, _Component);\n\n  function MenuItem(props, context) {\n    var _this;\n\n    (0, _classCallCheck2[\"default\"])(this, MenuItem);\n    _this = (0, _possibleConstructorReturn2[\"default\"])(this, (0, _getPrototypeOf2[\"default\"])(MenuItem).call(this, props, context));\n    _this.handleClick = _this.handleClick.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    return _this;\n  }\n\n  (0, _createClass2[\"default\"])(MenuItem, [{\n    key: \"handleClick\",\n    value: function handleClick() {\n      var _this$props = this.props,\n          index = _this$props.index,\n          onSelectItem = _this$props.onSelectItem;\n      onSelectItem(index);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props2 = this.props,\n          item = _this$props2.item,\n          index = _this$props2.index,\n          activateIndex = _this$props2.activateIndex;\n      return _react[\"default\"].createElement(\"li\", {\n        className: (0, _classnames[\"default\"])({\n          'video-react-menu-item': true,\n          'video-react-selected': index === activateIndex\n        }),\n        role: \"menuitem\",\n        onClick: this.handleClick\n      }, item.label, _react[\"default\"].createElement(\"span\", {\n        className: \"video-react-control-text\"\n      }));\n    }\n  }]);\n  return MenuItem;\n}(_react.Component);\n\nexports[\"default\"] = MenuItem;\nMenuItem.propTypes = propTypes;\nMenuItem.displayName = 'MenuItem';"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,uBAAuB,GAAGC,OAAO,CAAC,+CAA+C,CAAC;AAEtF,IAAIC,sBAAsB,GAAGD,OAAO,CAAC,8CAA8C,CAAC;AAEpFE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;AAE3B,IAAIE,gBAAgB,GAAGL,sBAAsB,CAACD,OAAO,CAAC,uCAAuC,CAAC,CAAC;AAE/F,IAAIO,aAAa,GAAGN,sBAAsB,CAACD,OAAO,CAAC,oCAAoC,CAAC,CAAC;AAEzF,IAAIQ,2BAA2B,GAAGP,sBAAsB,CAACD,OAAO,CAAC,kDAAkD,CAAC,CAAC;AAErH,IAAIS,gBAAgB,GAAGR,sBAAsB,CAACD,OAAO,CAAC,uCAAuC,CAAC,CAAC;AAE/F,IAAIU,uBAAuB,GAAGT,sBAAsB,CAACD,OAAO,CAAC,8CAA8C,CAAC,CAAC;AAE7G,IAAIW,UAAU,GAAGV,sBAAsB,CAACD,OAAO,CAAC,iCAAiC,CAAC,CAAC;AAEnF,IAAIY,UAAU,GAAGX,sBAAsB,CAACD,OAAO,CAAC,YAAY,CAAC,CAAC;AAE9D,IAAIa,MAAM,GAAGd,uBAAuB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AAEtD,IAAIc,WAAW,GAAGb,sBAAsB,CAACD,OAAO,CAAC,YAAY,CAAC,CAAC;AAE/D,IAAIe,SAAS,GAAG;EACdC,IAAI,EAAEJ,UAAU,CAAC,SAAS,CAAC,CAACK,MAAM;EAClCC,KAAK,EAAEN,UAAU,CAAC,SAAS,CAAC,CAACO,MAAM;EACnCC,aAAa,EAAER,UAAU,CAAC,SAAS,CAAC,CAACO,MAAM;EAC3CE,YAAY,EAAET,UAAU,CAAC,SAAS,CAAC,CAACU;AACtC,CAAC;AAED,IAAIC,QAAQ,GACZ;AACA,UAAUC,UAAU,EAAE;EACpB,CAAC,CAAC,EAAEb,UAAU,CAAC,SAAS,CAAC,EAAEY,QAAQ,EAAEC,UAAU,CAAC;EAEhD,SAASD,QAAQA,CAACE,KAAK,EAAEC,OAAO,EAAE;IAChC,IAAIC,KAAK;IAET,CAAC,CAAC,EAAErB,gBAAgB,CAAC,SAAS,CAAC,EAAE,IAAI,EAAEiB,QAAQ,CAAC;IAChDI,KAAK,GAAG,CAAC,CAAC,EAAEnB,2BAA2B,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAEC,gBAAgB,CAAC,SAAS,CAAC,EAAEc,QAAQ,CAAC,CAACK,IAAI,CAAC,IAAI,EAAEH,KAAK,EAAEC,OAAO,CAAC,CAAC;IAChIC,KAAK,CAACE,WAAW,GAAGF,KAAK,CAACE,WAAW,CAACC,IAAI,CAAC,CAAC,CAAC,EAAEpB,uBAAuB,CAAC,SAAS,CAAC,EAAEiB,KAAK,CAAC,CAAC;IAC1F,OAAOA,KAAK;EACd;EAEA,CAAC,CAAC,EAAEpB,aAAa,CAAC,SAAS,CAAC,EAAEgB,QAAQ,EAAE,CAAC;IACvCQ,GAAG,EAAE,aAAa;IAClB1B,KAAK,EAAE,SAASwB,WAAWA,CAAA,EAAG;MAC5B,IAAIG,WAAW,GAAG,IAAI,CAACP,KAAK;QACxBP,KAAK,GAAGc,WAAW,CAACd,KAAK;QACzBG,YAAY,GAAGW,WAAW,CAACX,YAAY;MAC3CA,YAAY,CAACH,KAAK,CAAC;IACrB;EACF,CAAC,EAAE;IACDa,GAAG,EAAE,QAAQ;IACb1B,KAAK,EAAE,SAAS4B,MAAMA,CAAA,EAAG;MACvB,IAAIC,YAAY,GAAG,IAAI,CAACT,KAAK;QACzBT,IAAI,GAAGkB,YAAY,CAAClB,IAAI;QACxBE,KAAK,GAAGgB,YAAY,CAAChB,KAAK;QAC1BE,aAAa,GAAGc,YAAY,CAACd,aAAa;MAC9C,OAAOP,MAAM,CAAC,SAAS,CAAC,CAACsB,aAAa,CAAC,IAAI,EAAE;QAC3CC,SAAS,EAAE,CAAC,CAAC,EAAEtB,WAAW,CAAC,SAAS,CAAC,EAAE;UACrC,uBAAuB,EAAE,IAAI;UAC7B,sBAAsB,EAAEI,KAAK,KAAKE;QACpC,CAAC,CAAC;QACFiB,IAAI,EAAE,UAAU;QAChBC,OAAO,EAAE,IAAI,CAACT;MAChB,CAAC,EAAEb,IAAI,CAACuB,KAAK,EAAE1B,MAAM,CAAC,SAAS,CAAC,CAACsB,aAAa,CAAC,MAAM,EAAE;QACrDC,SAAS,EAAE;MACb,CAAC,CAAC,CAAC;IACL;EACF,CAAC,CAAC,CAAC;EACH,OAAOb,QAAQ;AACjB,CAAC,CAACV,MAAM,CAAC2B,SAAS,CAAC;AAEnBpC,OAAO,CAAC,SAAS,CAAC,GAAGmB,QAAQ;AAC7BA,QAAQ,CAACR,SAAS,GAAGA,SAAS;AAC9BQ,QAAQ,CAACkB,WAAW,GAAG,UAAU"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}