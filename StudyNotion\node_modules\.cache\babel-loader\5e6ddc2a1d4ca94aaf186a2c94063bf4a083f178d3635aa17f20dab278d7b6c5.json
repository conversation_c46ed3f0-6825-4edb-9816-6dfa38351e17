{"ast": null, "code": "import React from 'react';\nvar isStyleObject = function (obj) {\n  return typeof obj === 'object' && obj !== null;\n};\nvar OTPInput = function (_a) {\n  var _b = _a.value,\n    value = _b === void 0 ? '' : _b,\n    _c = _a.numInputs,\n    numInputs = _c === void 0 ? 4 : _c,\n    onChange = _a.onChange,\n    renderInput = _a.renderInput,\n    _d = _a.shouldAutoFocus,\n    shouldAutoFocus = _d === void 0 ? false : _d,\n    _e = _a.inputType,\n    inputType = _e === void 0 ? 'text' : _e,\n    renderSeparator = _a.renderSeparator,\n    placeholder = _a.placeholder,\n    containerStyle = _a.containerStyle,\n    inputStyle = _a.inputStyle;\n  var _f = React.useState(0),\n    activeInput = _f[0],\n    setActiveInput = _f[1];\n  var inputRefs = React.useRef([]);\n  var getOTPValue = function () {\n    return value ? value.toString().split('') : [];\n  };\n  var isInputNum = inputType === 'number' || inputType === 'tel';\n  React.useEffect(function () {\n    inputRefs.current = inputRefs.current.slice(0, numInputs);\n  }, [numInputs]);\n  React.useEffect(function () {\n    var _a;\n    if (shouldAutoFocus) {\n      (_a = inputRefs.current[0]) === null || _a === void 0 ? void 0 : _a.focus();\n    }\n  }, [shouldAutoFocus]);\n  var getPlaceholderValue = function () {\n    if (typeof placeholder === 'string') {\n      if (placeholder.length === numInputs) {\n        return placeholder;\n      }\n      if (placeholder.length > 0) {\n        console.error('Length of the placeholder should be equal to the number of inputs.');\n      }\n    }\n    return undefined;\n  };\n  var isInputValueValid = function (value) {\n    var isTypeValid = isInputNum ? !isNaN(Number(value)) : typeof value === 'string';\n    return isTypeValid && value.trim().length === 1;\n  };\n  var handleChange = function (event) {\n    var value = event.target.value;\n    if (isInputValueValid(value)) {\n      changeCodeAtFocus(value);\n      focusInput(activeInput + 1);\n    } else {\n      var nativeEvent = event.nativeEvent;\n      // @ts-expect-error - This was added previosly to handle and edge case\n      // for dealing with keyCode \"229 Unidentified\" on Android. Check if this is\n      // still needed.\n      if (nativeEvent.data === null && nativeEvent.inputType === 'deleteContentBackward') {\n        event.preventDefault();\n        changeCodeAtFocus('');\n        focusInput(activeInput - 1);\n      }\n    }\n  };\n  var handleFocus = function (event) {\n    return function (index) {\n      setActiveInput(index);\n      event.target.select();\n    };\n  };\n  var handleBlur = function () {\n    setActiveInput(activeInput - 1);\n  };\n  var handleKeyDown = function (event) {\n    var otp = getOTPValue();\n    if ([event.code, event.key].includes('Backspace')) {\n      event.preventDefault();\n      changeCodeAtFocus('');\n      focusInput(activeInput - 1);\n    } else if (event.code === 'Delete') {\n      event.preventDefault();\n      changeCodeAtFocus('');\n    } else if (event.code === 'ArrowLeft') {\n      event.preventDefault();\n      focusInput(activeInput - 1);\n    } else if (event.code === 'ArrowRight') {\n      event.preventDefault();\n      focusInput(activeInput + 1);\n    }\n    // React does not trigger onChange when the same value is entered\n    // again. So we need to focus the next input manually in this case.\n    else if (event.key === otp[activeInput]) {\n      event.preventDefault();\n      focusInput(activeInput + 1);\n    } else if (event.code === 'Spacebar' || event.code === 'Space' || event.code === 'ArrowUp' || event.code === 'ArrowDown') {\n      event.preventDefault();\n    } else if (isInputNum && !isInputValueValid(event.key)) {\n      event.preventDefault();\n    }\n  };\n  var focusInput = function (index) {\n    var _a, _b;\n    var activeInput = Math.max(Math.min(numInputs - 1, index), 0);\n    if (inputRefs.current[activeInput]) {\n      (_a = inputRefs.current[activeInput]) === null || _a === void 0 ? void 0 : _a.focus();\n      (_b = inputRefs.current[activeInput]) === null || _b === void 0 ? void 0 : _b.select();\n      setActiveInput(activeInput);\n    }\n  };\n  var changeCodeAtFocus = function (value) {\n    var otp = getOTPValue();\n    otp[activeInput] = value[0];\n    handleOTPChange(otp);\n  };\n  var handleOTPChange = function (otp) {\n    var otpValue = otp.join('');\n    onChange(otpValue);\n  };\n  var handlePaste = function (event) {\n    var _a;\n    event.preventDefault();\n    var otp = getOTPValue();\n    var nextActiveInput = activeInput;\n    // Get pastedData in an array of max size (num of inputs - current position)\n    var pastedData = event.clipboardData.getData('text/plain').slice(0, numInputs - activeInput).split('');\n    // Prevent pasting if the clipboard data contains non-numeric values for number inputs\n    if (isInputNum && pastedData.some(function (value) {\n      return isNaN(Number(value));\n    })) {\n      return;\n    }\n    // Paste data from focused input onwards\n    for (var pos = 0; pos < numInputs; ++pos) {\n      if (pos >= activeInput && pastedData.length > 0) {\n        otp[pos] = (_a = pastedData.shift()) !== null && _a !== void 0 ? _a : '';\n        nextActiveInput++;\n      }\n    }\n    focusInput(nextActiveInput);\n    handleOTPChange(otp);\n  };\n  return React.createElement(\"div\", {\n    style: Object.assign({\n      display: 'flex',\n      alignItems: 'center'\n    }, isStyleObject(containerStyle) && containerStyle),\n    className: typeof containerStyle === 'string' ? containerStyle : undefined\n  }, Array.from({\n    length: numInputs\n  }, function (_, index) {\n    return index;\n  }).map(function (index) {\n    var _a, _b, _c;\n    return React.createElement(React.Fragment, {\n      key: index\n    }, renderInput({\n      value: (_a = getOTPValue()[index]) !== null && _a !== void 0 ? _a : '',\n      placeholder: (_c = (_b = getPlaceholderValue()) === null || _b === void 0 ? void 0 : _b[index]) !== null && _c !== void 0 ? _c : undefined,\n      ref: function (element) {\n        return inputRefs.current[index] = element;\n      },\n      onChange: handleChange,\n      onFocus: function (event) {\n        return handleFocus(event)(index);\n      },\n      onBlur: handleBlur,\n      onKeyDown: handleKeyDown,\n      onPaste: handlePaste,\n      autoComplete: 'off',\n      maxLength: 1,\n      'aria-label': \"Please enter OTP character \".concat(index + 1),\n      style: Object.assign({\n        width: '1em',\n        textAlign: 'center'\n      }, isStyleObject(inputStyle) && inputStyle),\n      className: typeof inputStyle === 'string' ? inputStyle : undefined,\n      type: inputType\n    }, index), index < numInputs - 1 && (typeof renderSeparator === 'function' ? renderSeparator(index) : renderSeparator));\n  }));\n};\nexport { OTPInput as default };", "map": {"version": 3, "names": ["isStyleObject", "obj", "OTPInput", "_a", "_b", "value", "_c", "numInputs", "onChange", "renderInput", "_d", "shouldAutoFocus", "_e", "inputType", "renderSeparator", "placeholder", "containerStyle", "inputStyle", "_f", "React", "useState", "activeInput", "setActiveInput", "inputRefs", "useRef", "getOTPValue", "toString", "split", "isInputNum", "useEffect", "current", "slice", "focus", "getPlaceholderValue", "length", "console", "error", "undefined", "isInputValueValid", "isTypeValid", "isNaN", "Number", "trim", "handleChange", "event", "target", "changeCodeAtFocus", "focusInput", "nativeEvent", "data", "preventDefault", "handleFocus", "index", "select", "handleBlur", "handleKeyDown", "otp", "code", "key", "includes", "Math", "max", "min", "handleOTPChange", "otpValue", "join", "handlePaste", "nextActiveInput", "pastedData", "clipboardData", "getData", "some", "pos", "shift", "createElement", "style", "Object", "assign", "display", "alignItems", "className", "Array", "from", "_", "map", "Fragment", "ref", "element", "onFocus", "onBlur", "onKeyDown", "onPaste", "autoComplete", "max<PERSON><PERSON><PERSON>", "concat", "width", "textAlign", "type"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\temp\\StudyNotion\\node_modules\\src\\index.tsx"], "sourcesContent": ["import React from 'react';\n\ntype AllowedInputTypes = 'password' | 'text' | 'number' | 'tel';\n\ntype InputProps = Required<\n  Pick<\n    React.InputHTMLAttributes<HTMLInputElement>,\n    | 'value'\n    | 'onChange'\n    | 'onFocus'\n    | 'onBlur'\n    | 'onKeyDown'\n    | 'onPaste'\n    | 'aria-label'\n    | 'maxLength'\n    | 'autoComplete'\n    | 'style'\n  > & {\n    ref: React.RefCallback<HTMLInputElement>;\n    placeholder: string | undefined;\n    className: string | undefined;\n    type: AllowedInputTypes;\n  }\n>;\n\ninterface OTPInputProps {\n  /** Value of the OTP input */\n  value?: string;\n  /** Number of OTP inputs to be rendered */\n  numInputs?: number;\n  /** Callback to be called when the OTP value changes */\n  onChange: (otp: string) => void;\n  /** Function to render the input */\n  renderInput: (inputProps: InputProps, index: number) => React.ReactNode;\n  /** Whether the first input should be auto focused */\n  shouldAutoFocus?: boolean;\n  /** Placeholder for the inputs */\n  placeholder?: string;\n  /** Function to render the separator */\n  renderSeparator?: ((index: number) => React.ReactNode) | React.ReactNode;\n  /** Style for the container */\n  containerStyle?: React.CSSProperties | string;\n  /** Style for the input */\n  inputStyle?: React.CSSProperties | string;\n  /** The type that will be passed to the input being rendered */\n  inputType?: AllowedInputTypes;\n}\n\nconst isStyleObject = (obj: unknown) => typeof obj === 'object' && obj !== null;\n\nconst OTPInput = ({\n  value = '',\n  numInputs = 4,\n  onChange,\n  renderInput,\n  shouldAutoFocus = false,\n  inputType = 'text',\n  renderSeparator,\n  placeholder,\n  containerStyle,\n  inputStyle,\n}: OTPInputProps) => {\n  const [activeInput, setActiveInput] = React.useState(0);\n  const inputRefs = React.useRef<Array<HTMLInputElement | null>>([]);\n\n  const getOTPValue = () => (value ? value.toString().split('') : []);\n\n  const isInputNum = inputType === 'number' || inputType === 'tel';\n\n  React.useEffect(() => {\n    inputRefs.current = inputRefs.current.slice(0, numInputs);\n  }, [numInputs]);\n\n  React.useEffect(() => {\n    if (shouldAutoFocus) {\n      inputRefs.current[0]?.focus();\n    }\n  }, [shouldAutoFocus]);\n\n  const getPlaceholderValue = () => {\n    if (typeof placeholder === 'string') {\n      if (placeholder.length === numInputs) {\n        return placeholder;\n      }\n\n      if (placeholder.length > 0) {\n        console.error('Length of the placeholder should be equal to the number of inputs.');\n      }\n    }\n    return undefined;\n  };\n\n  const isInputValueValid = (value: string) => {\n    const isTypeValid = isInputNum ? !isNaN(Number(value)) : typeof value === 'string';\n    return isTypeValid && value.trim().length === 1;\n  };\n\n  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const { value } = event.target;\n\n    if (isInputValueValid(value)) {\n      changeCodeAtFocus(value);\n      focusInput(activeInput + 1);\n    } else {\n      const { nativeEvent } = event;\n      // @ts-expect-error - This was added previosly to handle and edge case\n      // for dealing with keyCode \"229 Unidentified\" on Android. Check if this is\n      // still needed.\n      if (nativeEvent.data === null && nativeEvent.inputType === 'deleteContentBackward') {\n        event.preventDefault();\n        changeCodeAtFocus('');\n        focusInput(activeInput - 1);\n      }\n    }\n  };\n\n  const handleFocus = (event: React.FocusEvent<HTMLInputElement>) => (index: number) => {\n    setActiveInput(index);\n    event.target.select();\n  };\n\n  const handleBlur = () => {\n    setActiveInput(activeInput - 1);\n  };\n\n  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {\n    const otp = getOTPValue();\n    if ([event.code, event.key].includes('Backspace')) {\n      event.preventDefault();\n      changeCodeAtFocus('');\n      focusInput(activeInput - 1);\n    } else if (event.code === 'Delete') {\n      event.preventDefault();\n      changeCodeAtFocus('');\n    } else if (event.code === 'ArrowLeft') {\n      event.preventDefault();\n      focusInput(activeInput - 1);\n    } else if (event.code === 'ArrowRight') {\n      event.preventDefault();\n      focusInput(activeInput + 1);\n    }\n    // React does not trigger onChange when the same value is entered\n    // again. So we need to focus the next input manually in this case.\n    else if (event.key === otp[activeInput]) {\n      event.preventDefault();\n      focusInput(activeInput + 1);\n    } else if (\n      event.code === 'Spacebar' ||\n      event.code === 'Space' ||\n      event.code === 'ArrowUp' ||\n      event.code === 'ArrowDown'\n    ) {\n      event.preventDefault();\n    } else if (isInputNum && !isInputValueValid(event.key)) {\n      event.preventDefault();\n    }\n  };\n\n  const focusInput = (index: number) => {\n    const activeInput = Math.max(Math.min(numInputs - 1, index), 0);\n\n    if (inputRefs.current[activeInput]) {\n      inputRefs.current[activeInput]?.focus();\n      inputRefs.current[activeInput]?.select();\n      setActiveInput(activeInput);\n    }\n  };\n\n  const changeCodeAtFocus = (value: string) => {\n    const otp = getOTPValue();\n    otp[activeInput] = value[0];\n    handleOTPChange(otp);\n  };\n\n  const handleOTPChange = (otp: Array<string>) => {\n    const otpValue = otp.join('');\n    onChange(otpValue);\n  };\n\n  const handlePaste = (event: React.ClipboardEvent<HTMLInputElement>) => {\n    event.preventDefault();\n\n    const otp = getOTPValue();\n    let nextActiveInput = activeInput;\n\n    // Get pastedData in an array of max size (num of inputs - current position)\n    const pastedData = event.clipboardData\n      .getData('text/plain')\n      .slice(0, numInputs - activeInput)\n      .split('');\n\n    // Prevent pasting if the clipboard data contains non-numeric values for number inputs\n    if (isInputNum && pastedData.some((value) => isNaN(Number(value)))) {\n      return;\n    }\n\n    // Paste data from focused input onwards\n    for (let pos = 0; pos < numInputs; ++pos) {\n      if (pos >= activeInput && pastedData.length > 0) {\n        otp[pos] = pastedData.shift() ?? '';\n        nextActiveInput++;\n      }\n    }\n\n    focusInput(nextActiveInput);\n    handleOTPChange(otp);\n  };\n\n  return (\n    <div\n      style={Object.assign({ display: 'flex', alignItems: 'center' }, isStyleObject(containerStyle) && containerStyle)}\n      className={typeof containerStyle === 'string' ? containerStyle : undefined}\n    >\n      {Array.from({ length: numInputs }, (_, index) => index).map((index) => (\n        <React.Fragment key={index}>\n          {renderInput(\n            {\n              value: getOTPValue()[index] ?? '',\n              placeholder: getPlaceholderValue()?.[index] ?? undefined,\n              ref: (element) => (inputRefs.current[index] = element),\n              onChange: handleChange,\n              onFocus: (event) => handleFocus(event)(index),\n              onBlur: handleBlur,\n              onKeyDown: handleKeyDown,\n              onPaste: handlePaste,\n              autoComplete: 'off',\n              maxLength: 1,\n              'aria-label': `Please enter OTP character ${index + 1}`,\n              style: Object.assign(\n                { width: '1em', textAlign: 'center' } as const,\n                isStyleObject(inputStyle) && inputStyle\n              ),\n              className: typeof inputStyle === 'string' ? inputStyle : undefined,\n              type: inputType,\n            },\n            index\n          )}\n          {index < numInputs - 1 && (typeof renderSeparator === 'function' ? renderSeparator(index) : renderSeparator)}\n        </React.Fragment>\n      ))}\n    </div>\n  );\n};\n\nexport default OTPInput;\n"], "mappings": ";AAgDA,IAAMA,aAAa,GAAG,SAAAA,CAACC,GAAY;EAAK,cAAOA,GAAG,KAAK,QAAQ,IAAIA,GAAG,KAAK,IAAI;AAAA;AAEzE,IAAAC,QAAQ,GAAG,SAAAA,CAACC,EAWF;EAVd,IAAAC,EAAA,GAAAD,EAAA,CAAAE,KAAU;IAAVA,KAAK,GAAAD,EAAA,cAAG,EAAE,GAAAA,EAAA;IACVE,EAAA,GAAAH,EAAA,CAAAI,SAAa;IAAbA,SAAS,GAAAD,EAAA,cAAG,CAAC,GAAAA,EAAA;IACbE,QAAQ,GAAAL,EAAA,CAAAK,QAAA;IACRC,WAAW,GAAAN,EAAA,CAAAM,WAAA;IACXC,EAAuB,GAAAP,EAAA,CAAAQ,eAAA;IAAvBA,eAAe,GAAGD,EAAA,mBAAK,GAAAA,EAAA;IACvBE,EAAA,GAAAT,EAAA,CAAAU,SAAkB;IAAlBA,SAAS,GAAAD,EAAA,cAAG,MAAM,GAAAA,EAAA;IAClBE,eAAe,GAAAX,EAAA,CAAAW,eAAA;IACfC,WAAW,GAAAZ,EAAA,CAAAY,WAAA;IACXC,cAAc,GAAAb,EAAA,CAAAa,cAAA;IACdC,UAAU,GAAAd,EAAA,CAAAc,UAAA;EAEJ,IAAAC,EAAgC,GAAAC,KAAK,CAACC,QAAQ,CAAC,CAAC,CAAC;IAAhDC,WAAW,GAAAH,EAAA;IAAEI,cAAc,GAAAJ,EAAA,GAAqB;EACvD,IAAMK,SAAS,GAAGJ,KAAK,CAACK,MAAM,CAAiC,EAAE,CAAC;EAElE,IAAMC,WAAW,GAAG,SAAAA,CAAA;IAAM,OAACpB,KAAK,GAAGA,KAAK,CAACqB,QAAQ,EAAE,CAACC,KAAK,CAAC,EAAE,CAAC,GAAG,EAAE;EAAxC,CAAyC;EAEnE,IAAMC,UAAU,GAAGf,SAAS,KAAK,QAAQ,IAAIA,SAAS,KAAK,KAAK;EAEhEM,KAAK,CAACU,SAAS,CAAC;IACdN,SAAS,CAACO,OAAO,GAAGP,SAAS,CAACO,OAAO,CAACC,KAAK,CAAC,CAAC,EAAExB,SAAS,CAAC;EAC3D,CAAC,EAAE,CAACA,SAAS,CAAC,CAAC;EAEfY,KAAK,CAACU,SAAS,CAAC;;IACd,IAAIlB,eAAe,EAAE;MACnB,CAAAR,EAAA,GAAAoB,SAAS,CAACO,OAAO,CAAC,CAAC,CAAC,cAAA3B,EAAA,uBAAAA,EAAA,CAAE6B,KAAK,EAAE;IAC9B;EACH,CAAC,EAAE,CAACrB,eAAe,CAAC,CAAC;EAErB,IAAMsB,mBAAmB,GAAG,SAAAA,CAAA;IAC1B,IAAI,OAAOlB,WAAW,KAAK,QAAQ,EAAE;MACnC,IAAIA,WAAW,CAACmB,MAAM,KAAK3B,SAAS,EAAE;QACpC,OAAOQ,WAAW;MACnB;MAED,IAAIA,WAAW,CAACmB,MAAM,GAAG,CAAC,EAAE;QAC1BC,OAAO,CAACC,KAAK,CAAC,oEAAoE,CAAC;MACpF;IACF;IACD,OAAOC,SAAS;EAClB,CAAC;EAED,IAAMC,iBAAiB,GAAG,SAAAA,CAACjC,KAAa;IACtC,IAAMkC,WAAW,GAAGX,UAAU,GAAG,CAACY,KAAK,CAACC,MAAM,CAACpC,KAAK,CAAC,CAAC,GAAG,OAAOA,KAAK,KAAK,QAAQ;IAClF,OAAOkC,WAAW,IAAIlC,KAAK,CAACqC,IAAI,EAAE,CAACR,MAAM,KAAK,CAAC;EACjD,CAAC;EAED,IAAMS,YAAY,GAAG,SAAAA,CAACC,KAA0C;IACtD,IAAAvC,KAAK,GAAKuC,KAAK,CAACC,MAAM,CAAAxC,KAAjB;IAEb,IAAIiC,iBAAiB,CAACjC,KAAK,CAAC,EAAE;MAC5ByC,iBAAiB,CAACzC,KAAK,CAAC;MACxB0C,UAAU,CAAC1B,WAAW,GAAG,CAAC,CAAC;IAC5B,OAAM;MACG,IAAA2B,WAAW,GAAKJ,KAAK,CAAAI,WAAV;;;;MAInB,IAAIA,WAAW,CAACC,IAAI,KAAK,IAAI,IAAID,WAAW,CAACnC,SAAS,KAAK,uBAAuB,EAAE;QAClF+B,KAAK,CAACM,cAAc,EAAE;QACtBJ,iBAAiB,CAAC,EAAE,CAAC;QACrBC,UAAU,CAAC1B,WAAW,GAAG,CAAC,CAAC;MAC5B;IACF;EACH,CAAC;EAED,IAAM8B,WAAW,GAAG,SAAAA,CAACP,KAAyC,EAAK;IAAA,iBAACQ,KAAa;MAC/E9B,cAAc,CAAC8B,KAAK,CAAC;MACrBR,KAAK,CAACC,MAAM,CAACQ,MAAM,EAAE;KACtB;EAAA;EAED,IAAMC,UAAU,GAAG,SAAAA,CAAA;IACjBhC,cAAc,CAACD,WAAW,GAAG,CAAC,CAAC;EACjC,CAAC;EAED,IAAMkC,aAAa,GAAG,SAAAA,CAACX,KAA4C;IACjE,IAAMY,GAAG,GAAG/B,WAAW,EAAE;IACzB,IAAI,CAACmB,KAAK,CAACa,IAAI,EAAEb,KAAK,CAACc,GAAG,CAAC,CAACC,QAAQ,CAAC,WAAW,CAAC,EAAE;MACjDf,KAAK,CAACM,cAAc,EAAE;MACtBJ,iBAAiB,CAAC,EAAE,CAAC;MACrBC,UAAU,CAAC1B,WAAW,GAAG,CAAC,CAAC;IAC5B,OAAM,IAAIuB,KAAK,CAACa,IAAI,KAAK,QAAQ,EAAE;MAClCb,KAAK,CAACM,cAAc,EAAE;MACtBJ,iBAAiB,CAAC,EAAE,CAAC;IACtB,OAAM,IAAIF,KAAK,CAACa,IAAI,KAAK,WAAW,EAAE;MACrCb,KAAK,CAACM,cAAc,EAAE;MACtBH,UAAU,CAAC1B,WAAW,GAAG,CAAC,CAAC;IAC5B,OAAM,IAAIuB,KAAK,CAACa,IAAI,KAAK,YAAY,EAAE;MACtCb,KAAK,CAACM,cAAc,EAAE;MACtBH,UAAU,CAAC1B,WAAW,GAAG,CAAC,CAAC;IAC5B;;;SAGI,IAAIuB,KAAK,CAACc,GAAG,KAAKF,GAAG,CAACnC,WAAW,CAAC,EAAE;MACvCuB,KAAK,CAACM,cAAc,EAAE;MACtBH,UAAU,CAAC1B,WAAW,GAAG,CAAC,CAAC;IAC5B,OAAM,IACLuB,KAAK,CAACa,IAAI,KAAK,UAAU,IACzBb,KAAK,CAACa,IAAI,KAAK,OAAO,IACtBb,KAAK,CAACa,IAAI,KAAK,SAAS,IACxBb,KAAK,CAACa,IAAI,KAAK,WAAW,EAC1B;MACAb,KAAK,CAACM,cAAc,EAAE;IACvB,OAAM,IAAItB,UAAU,IAAI,CAACU,iBAAiB,CAACM,KAAK,CAACc,GAAG,CAAC,EAAE;MACtDd,KAAK,CAACM,cAAc,EAAE;IACvB;EACH,CAAC;EAED,IAAMH,UAAU,GAAG,SAAAA,CAACK,KAAa;;IAC/B,IAAM/B,WAAW,GAAGuC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACvD,SAAS,GAAG,CAAC,EAAE6C,KAAK,CAAC,EAAE,CAAC,CAAC;IAE/D,IAAI7B,SAAS,CAACO,OAAO,CAACT,WAAW,CAAC,EAAE;MAClC,CAAAlB,EAAA,GAAAoB,SAAS,CAACO,OAAO,CAACT,WAAW,CAAC,cAAAlB,EAAA,uBAAAA,EAAA,CAAE6B,KAAK,EAAE;MACvC,CAAA5B,EAAA,GAAAmB,SAAS,CAACO,OAAO,CAACT,WAAW,CAAC,cAAAjB,EAAA,uBAAAA,EAAA,CAAEiD,MAAM,EAAE;MACxC/B,cAAc,CAACD,WAAW,CAAC;IAC5B;EACH,CAAC;EAED,IAAMyB,iBAAiB,GAAG,SAAAA,CAACzC,KAAa;IACtC,IAAMmD,GAAG,GAAG/B,WAAW,EAAE;IACzB+B,GAAG,CAACnC,WAAW,CAAC,GAAGhB,KAAK,CAAC,CAAC,CAAC;IAC3B0D,eAAe,CAACP,GAAG,CAAC;EACtB,CAAC;EAED,IAAMO,eAAe,GAAG,SAAAA,CAACP,GAAkB;IACzC,IAAMQ,QAAQ,GAAGR,GAAG,CAACS,IAAI,CAAC,EAAE,CAAC;IAC7BzD,QAAQ,CAACwD,QAAQ,CAAC;EACpB,CAAC;EAED,IAAME,WAAW,GAAG,SAAAA,CAACtB,KAA6C;;IAChEA,KAAK,CAACM,cAAc,EAAE;IAEtB,IAAMM,GAAG,GAAG/B,WAAW,EAAE;IACzB,IAAI0C,eAAe,GAAG9C,WAAW;;IAGjC,IAAM+C,UAAU,GAAGxB,KAAK,CAACyB,aAAa,CACnCC,OAAO,CAAC,YAAY,CAAC,CACrBvC,KAAK,CAAC,CAAC,EAAExB,SAAS,GAAGc,WAAW,CAAC,CACjCM,KAAK,CAAC,EAAE,CAAC;;IAGZ,IAAIC,UAAU,IAAIwC,UAAU,CAACG,IAAI,CAAC,UAAClE,KAAK,EAAK;MAAA,OAAAmC,KAAK,CAACC,MAAM,CAACpC,KAAK,CAAC,CAAC;IAApB,CAAoB,CAAC,EAAE;MAClE;IACD;;IAGD,KAAK,IAAImE,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGjE,SAAS,EAAE,EAAEiE,GAAG,EAAE;MACxC,IAAIA,GAAG,IAAInD,WAAW,IAAI+C,UAAU,CAAClC,MAAM,GAAG,CAAC,EAAE;QAC/CsB,GAAG,CAACgB,GAAG,CAAC,GAAG,CAAArE,EAAA,GAAAiE,UAAU,CAACK,KAAK,EAAE,MAAI,QAAAtE,EAAA,cAAAA,EAAA,KAAE;QACnCgE,eAAe,EAAE;MAClB;IACF;IAEDpB,UAAU,CAACoB,eAAe,CAAC;IAC3BJ,eAAe,CAACP,GAAG,CAAC;EACtB,CAAC;EAED,OACErC,KAAA,CAAAuD,aAAA;IACEC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;MAAEC,OAAO,EAAE,MAAM;MAAEC,UAAU,EAAE;IAAQ,CAAE,EAAE/E,aAAa,CAACgB,cAAc,CAAC,IAAIA,cAAc,CAAC;IAChHgE,SAAS,EAAE,OAAOhE,cAAc,KAAK,QAAQ,GAAGA,cAAc,GAAGqB;EAAS,CAEzE,EAAA4C,KAAK,CAACC,IAAI,CAAC;IAAEhD,MAAM,EAAE3B;EAAS,CAAE,EAAE,UAAC4E,CAAC,EAAE/B,KAAK;IAAK,OAAAA,KAAK;EAAL,CAAK,CAAC,CAACgC,GAAG,CAAC,UAAChC,KAAK;;IAAK,OACrEjC,KAAA,CAAAuD,aAAA,CAACvD,KAAK,CAACkE,QAAQ,EAAC;MAAA3B,GAAG,EAAEN;IAAK,GACvB3C,WAAW,CACV;MACEJ,KAAK,EAAE,CAAAF,EAAA,GAAAsB,WAAW,EAAE,CAAC2B,KAAK,CAAC,cAAAjD,EAAA,cAAAA,EAAA,GAAI,EAAE;MACjCY,WAAW,EAAE,CAAAT,EAAA,IAAAF,EAAA,GAAA6B,mBAAmB,EAAE,MAAG,QAAA7B,EAAA,uBAAAA,EAAA,CAAAgD,KAAK,CAAC,cAAA9C,EAAA,cAAAA,EAAA,GAAI+B,SAAS;MACxDiD,GAAG,EAAE,SAAAA,CAACC,OAAO,EAAK;QAAA,OAAChE,SAAS,CAACO,OAAO,CAACsB,KAAK,CAAC,GAAGmC,OAAO;MAAA,CAAC;MACtD/E,QAAQ,EAAEmC,YAAY;MACtB6C,OAAO,EAAE,SAAAA,CAAC5C,KAAK;QAAK,OAAAO,WAAW,CAACP,KAAK,CAAC,CAACQ,KAAK,CAAC;MAAA;MAC7CqC,MAAM,EAAEnC,UAAU;MAClBoC,SAAS,EAAEnC,aAAa;MACxBoC,OAAO,EAAEzB,WAAW;MACpB0B,YAAY,EAAE,KAAK;MACnBC,SAAS,EAAE,CAAC;MACZ,YAAY,EAAE,8BAAAC,MAAA,CAA8B1C,KAAK,GAAG,CAAC,CAAE;MACvDuB,KAAK,EAAEC,MAAM,CAACC,MAAM,CAClB;QAAEkB,KAAK,EAAE,KAAK;QAAEC,SAAS,EAAE;MAAQ,CAAW,EAC9ChG,aAAa,CAACiB,UAAU,CAAC,IAAIA,UAAU,CACxC;MACD+D,SAAS,EAAE,OAAO/D,UAAU,KAAK,QAAQ,GAAGA,UAAU,GAAGoB,SAAS;MAClE4D,IAAI,EAAEpF;IACP,GACDuC,KAAK,CACN,EACAA,KAAK,GAAG7C,SAAS,GAAG,CAAC,KAAK,OAAOO,eAAe,KAAK,UAAU,GAAGA,eAAe,CAACsC,KAAK,CAAC,GAAGtC,eAAe,CAAC,CAC7F;GAClB,CAAC,CACE;AAEV"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}