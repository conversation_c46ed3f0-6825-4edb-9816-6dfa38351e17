{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\");\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nvar _classCallCheck2 = _interopRequireDefault(require(\"@babel/runtime/helpers/classCallCheck\"));\nvar _createClass2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createClass\"));\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"@babel/runtime/helpers/possibleConstructorReturn\"));\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"@babel/runtime/helpers/getPrototypeOf\"));\nvar _assertThisInitialized2 = _interopRequireDefault(require(\"@babel/runtime/helpers/assertThisInitialized\"));\nvar _inherits2 = _interopRequireDefault(require(\"@babel/runtime/helpers/inherits\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _react = _interopRequireWildcard(require(\"react\"));\nvar _classnames = _interopRequireDefault(require(\"classnames\"));\nvar propTypes = {\n  manager: _propTypes[\"default\"].object,\n  className: _propTypes[\"default\"].string\n};\nvar Bezel = /*#__PURE__*/\nfunction (_Component) {\n  (0, _inherits2[\"default\"])(Bezel, _Component);\n  function Bezel(props, context) {\n    var _this;\n    (0, _classCallCheck2[\"default\"])(this, Bezel);\n    _this = (0, _possibleConstructorReturn2[\"default\"])(this, (0, _getPrototypeOf2[\"default\"])(Bezel).call(this, props, context));\n    _this.timer = null;\n    props.manager.subscribeToOperationStateChange(_this.handleStateChange.bind((0, _assertThisInitialized2[\"default\"])(_this)));\n    _this.state = {\n      hidden: true,\n      operation: {}\n    };\n    return _this;\n  }\n  (0, _createClass2[\"default\"])(Bezel, [{\n    key: \"handleStateChange\",\n    value: function handleStateChange(state, prevState) {\n      var _this2 = this;\n      if (state.count !== prevState.count && state.operation.source === 'shortcut') {\n        if (this.timer) {\n          // previous animation is not finished\n          clearTimeout(this.timer); // cancel it\n\n          this.timer = null;\n        } // show it\n        // update operation\n\n        this.setState({\n          hidden: false,\n          count: state.count,\n          operation: state.operation\n        }); // hide it after 0.5s\n\n        this.timer = setTimeout(function () {\n          _this2.setState({\n            hidden: true\n          });\n          _this2.timer = null;\n        }, 500);\n      }\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      // only displays for shortcut so far\n      if (this.state.operation.source !== 'shortcut') {\n        return null;\n      }\n      var style = this.state.hidden ? {\n        display: 'none'\n      } : null;\n      return _react[\"default\"].createElement(\"div\", {\n        className: (0, _classnames[\"default\"])({\n          'video-react-bezel': true,\n          'video-react-bezel-animation': this.state.count % 2 === 0,\n          'video-react-bezel-animation-alt': this.state.count % 2 === 1\n        }, this.props.className),\n        style: style,\n        role: \"status\",\n        \"aria-label\": this.state.operation.action\n      }, _react[\"default\"].createElement(\"div\", {\n        className: (0, _classnames[\"default\"])('video-react-bezel-icon', \"video-react-bezel-icon-\".concat(this.state.operation.action))\n      }));\n    }\n  }]);\n  return Bezel;\n}(_react.Component);\nexports[\"default\"] = Bezel;\nBezel.propTypes = propTypes;\nBezel.displayName = 'Bezel';", "map": {"version": 3, "names": ["_interopRequireWildcard", "require", "_interopRequireDefault", "Object", "defineProperty", "exports", "value", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_assertThisInitialized2", "_inherits2", "_propTypes", "_react", "_classnames", "propTypes", "manager", "object", "className", "string", "<PERSON><PERSON>", "_Component", "props", "context", "_this", "call", "timer", "subscribeToOperationStateChange", "handleStateChange", "bind", "state", "hidden", "operation", "key", "prevState", "_this2", "count", "source", "clearTimeout", "setState", "setTimeout", "render", "style", "display", "createElement", "role", "action", "concat", "Component", "displayName"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/video-react/lib/components/Bezel.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\");\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _classCallCheck2 = _interopRequireDefault(require(\"@babel/runtime/helpers/classCallCheck\"));\n\nvar _createClass2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createClass\"));\n\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"@babel/runtime/helpers/possibleConstructorReturn\"));\n\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"@babel/runtime/helpers/getPrototypeOf\"));\n\nvar _assertThisInitialized2 = _interopRequireDefault(require(\"@babel/runtime/helpers/assertThisInitialized\"));\n\nvar _inherits2 = _interopRequireDefault(require(\"@babel/runtime/helpers/inherits\"));\n\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\n\nvar _react = _interopRequireWildcard(require(\"react\"));\n\nvar _classnames = _interopRequireDefault(require(\"classnames\"));\n\nvar propTypes = {\n  manager: _propTypes[\"default\"].object,\n  className: _propTypes[\"default\"].string\n};\n\nvar Bezel =\n/*#__PURE__*/\nfunction (_Component) {\n  (0, _inherits2[\"default\"])(Bezel, _Component);\n\n  function Bezel(props, context) {\n    var _this;\n\n    (0, _classCallCheck2[\"default\"])(this, Bezel);\n    _this = (0, _possibleConstructorReturn2[\"default\"])(this, (0, _getPrototypeOf2[\"default\"])(Bezel).call(this, props, context));\n    _this.timer = null;\n    props.manager.subscribeToOperationStateChange(_this.handleStateChange.bind((0, _assertThisInitialized2[\"default\"])(_this)));\n    _this.state = {\n      hidden: true,\n      operation: {}\n    };\n    return _this;\n  }\n\n  (0, _createClass2[\"default\"])(Bezel, [{\n    key: \"handleStateChange\",\n    value: function handleStateChange(state, prevState) {\n      var _this2 = this;\n\n      if (state.count !== prevState.count && state.operation.source === 'shortcut') {\n        if (this.timer) {\n          // previous animation is not finished\n          clearTimeout(this.timer); // cancel it\n\n          this.timer = null;\n        } // show it\n        // update operation\n\n\n        this.setState({\n          hidden: false,\n          count: state.count,\n          operation: state.operation\n        }); // hide it after 0.5s\n\n        this.timer = setTimeout(function () {\n          _this2.setState({\n            hidden: true\n          });\n\n          _this2.timer = null;\n        }, 500);\n      }\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      // only displays for shortcut so far\n      if (this.state.operation.source !== 'shortcut') {\n        return null;\n      }\n\n      var style = this.state.hidden ? {\n        display: 'none'\n      } : null;\n      return _react[\"default\"].createElement(\"div\", {\n        className: (0, _classnames[\"default\"])({\n          'video-react-bezel': true,\n          'video-react-bezel-animation': this.state.count % 2 === 0,\n          'video-react-bezel-animation-alt': this.state.count % 2 === 1\n        }, this.props.className),\n        style: style,\n        role: \"status\",\n        \"aria-label\": this.state.operation.action\n      }, _react[\"default\"].createElement(\"div\", {\n        className: (0, _classnames[\"default\"])('video-react-bezel-icon', \"video-react-bezel-icon-\".concat(this.state.operation.action))\n      }));\n    }\n  }]);\n  return Bezel;\n}(_react.Component);\n\nexports[\"default\"] = Bezel;\nBezel.propTypes = propTypes;\nBezel.displayName = 'Bezel';"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,uBAAuB,GAAGC,OAAO,CAAC,+CAA+C,CAAC;AAEtF,IAAIC,sBAAsB,GAAGD,OAAO,CAAC,8CAA8C,CAAC;AAEpFE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;AAE3B,IAAIE,gBAAgB,GAAGL,sBAAsB,CAACD,OAAO,CAAC,uCAAuC,CAAC,CAAC;AAE/F,IAAIO,aAAa,GAAGN,sBAAsB,CAACD,OAAO,CAAC,oCAAoC,CAAC,CAAC;AAEzF,IAAIQ,2BAA2B,GAAGP,sBAAsB,CAACD,OAAO,CAAC,kDAAkD,CAAC,CAAC;AAErH,IAAIS,gBAAgB,GAAGR,sBAAsB,CAACD,OAAO,CAAC,uCAAuC,CAAC,CAAC;AAE/F,IAAIU,uBAAuB,GAAGT,sBAAsB,CAACD,OAAO,CAAC,8CAA8C,CAAC,CAAC;AAE7G,IAAIW,UAAU,GAAGV,sBAAsB,CAACD,OAAO,CAAC,iCAAiC,CAAC,CAAC;AAEnF,IAAIY,UAAU,GAAGX,sBAAsB,CAACD,OAAO,CAAC,YAAY,CAAC,CAAC;AAE9D,IAAIa,MAAM,GAAGd,uBAAuB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AAEtD,IAAIc,WAAW,GAAGb,sBAAsB,CAACD,OAAO,CAAC,YAAY,CAAC,CAAC;AAE/D,IAAIe,SAAS,GAAG;EACdC,OAAO,EAAEJ,UAAU,CAAC,SAAS,CAAC,CAACK,MAAM;EACrCC,SAAS,EAAEN,UAAU,CAAC,SAAS,CAAC,CAACO;AACnC,CAAC;AAED,IAAIC,KAAK,GACT;AACA,UAAUC,UAAU,EAAE;EACpB,CAAC,CAAC,EAAEV,UAAU,CAAC,SAAS,CAAC,EAAES,KAAK,EAAEC,UAAU,CAAC;EAE7C,SAASD,KAAKA,CAACE,KAAK,EAAEC,OAAO,EAAE;IAC7B,IAAIC,KAAK;IAET,CAAC,CAAC,EAAElB,gBAAgB,CAAC,SAAS,CAAC,EAAE,IAAI,EAAEc,KAAK,CAAC;IAC7CI,KAAK,GAAG,CAAC,CAAC,EAAEhB,2BAA2B,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAEC,gBAAgB,CAAC,SAAS,CAAC,EAAEW,KAAK,CAAC,CAACK,IAAI,CAAC,IAAI,EAAEH,KAAK,EAAEC,OAAO,CAAC,CAAC;IAC7HC,KAAK,CAACE,KAAK,GAAG,IAAI;IAClBJ,KAAK,CAACN,OAAO,CAACW,+BAA+B,CAACH,KAAK,CAACI,iBAAiB,CAACC,IAAI,CAAC,CAAC,CAAC,EAAEnB,uBAAuB,CAAC,SAAS,CAAC,EAAEc,KAAK,CAAC,CAAC,CAAC;IAC3HA,KAAK,CAACM,KAAK,GAAG;MACZC,MAAM,EAAE,IAAI;MACZC,SAAS,EAAE,CAAC;IACd,CAAC;IACD,OAAOR,KAAK;EACd;EAEA,CAAC,CAAC,EAAEjB,aAAa,CAAC,SAAS,CAAC,EAAEa,KAAK,EAAE,CAAC;IACpCa,GAAG,EAAE,mBAAmB;IACxB5B,KAAK,EAAE,SAASuB,iBAAiBA,CAACE,KAAK,EAAEI,SAAS,EAAE;MAClD,IAAIC,MAAM,GAAG,IAAI;MAEjB,IAAIL,KAAK,CAACM,KAAK,KAAKF,SAAS,CAACE,KAAK,IAAIN,KAAK,CAACE,SAAS,CAACK,MAAM,KAAK,UAAU,EAAE;QAC5E,IAAI,IAAI,CAACX,KAAK,EAAE;UACd;UACAY,YAAY,CAAC,IAAI,CAACZ,KAAK,CAAC,CAAC,CAAC;;UAE1B,IAAI,CAACA,KAAK,GAAG,IAAI;QACnB,CAAC,CAAC;QACF;;QAGA,IAAI,CAACa,QAAQ,CAAC;UACZR,MAAM,EAAE,KAAK;UACbK,KAAK,EAAEN,KAAK,CAACM,KAAK;UAClBJ,SAAS,EAAEF,KAAK,CAACE;QACnB,CAAC,CAAC,CAAC,CAAC;;QAEJ,IAAI,CAACN,KAAK,GAAGc,UAAU,CAAC,YAAY;UAClCL,MAAM,CAACI,QAAQ,CAAC;YACdR,MAAM,EAAE;UACV,CAAC,CAAC;UAEFI,MAAM,CAACT,KAAK,GAAG,IAAI;QACrB,CAAC,EAAE,GAAG,CAAC;MACT;IACF;EACF,CAAC,EAAE;IACDO,GAAG,EAAE,QAAQ;IACb5B,KAAK,EAAE,SAASoC,MAAMA,CAAA,EAAG;MACvB;MACA,IAAI,IAAI,CAACX,KAAK,CAACE,SAAS,CAACK,MAAM,KAAK,UAAU,EAAE;QAC9C,OAAO,IAAI;MACb;MAEA,IAAIK,KAAK,GAAG,IAAI,CAACZ,KAAK,CAACC,MAAM,GAAG;QAC9BY,OAAO,EAAE;MACX,CAAC,GAAG,IAAI;MACR,OAAO9B,MAAM,CAAC,SAAS,CAAC,CAAC+B,aAAa,CAAC,KAAK,EAAE;QAC5C1B,SAAS,EAAE,CAAC,CAAC,EAAEJ,WAAW,CAAC,SAAS,CAAC,EAAE;UACrC,mBAAmB,EAAE,IAAI;UACzB,6BAA6B,EAAE,IAAI,CAACgB,KAAK,CAACM,KAAK,GAAG,CAAC,KAAK,CAAC;UACzD,iCAAiC,EAAE,IAAI,CAACN,KAAK,CAACM,KAAK,GAAG,CAAC,KAAK;QAC9D,CAAC,EAAE,IAAI,CAACd,KAAK,CAACJ,SAAS,CAAC;QACxBwB,KAAK,EAAEA,KAAK;QACZG,IAAI,EAAE,QAAQ;QACd,YAAY,EAAE,IAAI,CAACf,KAAK,CAACE,SAAS,CAACc;MACrC,CAAC,EAAEjC,MAAM,CAAC,SAAS,CAAC,CAAC+B,aAAa,CAAC,KAAK,EAAE;QACxC1B,SAAS,EAAE,CAAC,CAAC,EAAEJ,WAAW,CAAC,SAAS,CAAC,EAAE,wBAAwB,EAAE,yBAAyB,CAACiC,MAAM,CAAC,IAAI,CAACjB,KAAK,CAACE,SAAS,CAACc,MAAM,CAAC;MAChI,CAAC,CAAC,CAAC;IACL;EACF,CAAC,CAAC,CAAC;EACH,OAAO1B,KAAK;AACd,CAAC,CAACP,MAAM,CAACmC,SAAS,CAAC;AAEnB5C,OAAO,CAAC,SAAS,CAAC,GAAGgB,KAAK;AAC1BA,KAAK,CAACL,SAAS,GAAGA,SAAS;AAC3BK,KAAK,CAAC6B,WAAW,GAAG,OAAO"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}