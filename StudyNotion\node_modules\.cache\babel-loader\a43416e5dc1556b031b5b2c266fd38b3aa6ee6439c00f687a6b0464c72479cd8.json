{"ast": null, "code": "import effectInit from '../../shared/effect-init.js';\nimport { createElement } from '../../shared/utils.js';\nexport default function EffectCube(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on\n  } = _ref;\n  extendParams({\n    cubeEffect: {\n      slideShadows: true,\n      shadow: true,\n      shadowOffset: 20,\n      shadowScale: 0.94\n    }\n  });\n  const createSlideShadows = (slideEl, progress, isHorizontal) => {\n    let shadowBefore = isHorizontal ? slideEl.querySelector('.swiper-slide-shadow-left') : slideEl.querySelector('.swiper-slide-shadow-top');\n    let shadowAfter = isHorizontal ? slideEl.querySelector('.swiper-slide-shadow-right') : slideEl.querySelector('.swiper-slide-shadow-bottom');\n    if (!shadowBefore) {\n      shadowBefore = createElement('div', `swiper-slide-shadow-${isHorizontal ? 'left' : 'top'}`);\n      slideEl.append(shadowBefore);\n    }\n    if (!shadowAfter) {\n      shadowAfter = createElement('div', `swiper-slide-shadow-${isHorizontal ? 'right' : 'bottom'}`);\n      slideEl.append(shadowAfter);\n    }\n    if (shadowBefore) shadowBefore.style.opacity = Math.max(-progress, 0);\n    if (shadowAfter) shadowAfter.style.opacity = Math.max(progress, 0);\n  };\n  const recreateShadows = () => {\n    // create new ones\n    const isHorizontal = swiper.isHorizontal();\n    swiper.slides.forEach(slideEl => {\n      const progress = Math.max(Math.min(slideEl.progress, 1), -1);\n      createSlideShadows(slideEl, progress, isHorizontal);\n    });\n  };\n  const setTranslate = () => {\n    const {\n      el,\n      wrapperEl,\n      slides,\n      width: swiperWidth,\n      height: swiperHeight,\n      rtlTranslate: rtl,\n      size: swiperSize,\n      browser\n    } = swiper;\n    const params = swiper.params.cubeEffect;\n    const isHorizontal = swiper.isHorizontal();\n    const isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n    let wrapperRotate = 0;\n    let cubeShadowEl;\n    if (params.shadow) {\n      if (isHorizontal) {\n        cubeShadowEl = swiper.slidesEl.querySelector('.swiper-cube-shadow');\n        if (!cubeShadowEl) {\n          cubeShadowEl = createElement('div', 'swiper-cube-shadow');\n          swiper.slidesEl.append(cubeShadowEl);\n        }\n        cubeShadowEl.style.height = `${swiperWidth}px`;\n      } else {\n        cubeShadowEl = el.querySelector('.swiper-cube-shadow');\n        if (!cubeShadowEl) {\n          cubeShadowEl = createElement('div', 'swiper-cube-shadow');\n          el.append(cubeShadowEl);\n        }\n      }\n    }\n    for (let i = 0; i < slides.length; i += 1) {\n      const slideEl = slides[i];\n      let slideIndex = i;\n      if (isVirtual) {\n        slideIndex = parseInt(slideEl.getAttribute('data-swiper-slide-index'), 10);\n      }\n      let slideAngle = slideIndex * 90;\n      let round = Math.floor(slideAngle / 360);\n      if (rtl) {\n        slideAngle = -slideAngle;\n        round = Math.floor(-slideAngle / 360);\n      }\n      const progress = Math.max(Math.min(slideEl.progress, 1), -1);\n      let tx = 0;\n      let ty = 0;\n      let tz = 0;\n      if (slideIndex % 4 === 0) {\n        tx = -round * 4 * swiperSize;\n        tz = 0;\n      } else if ((slideIndex - 1) % 4 === 0) {\n        tx = 0;\n        tz = -round * 4 * swiperSize;\n      } else if ((slideIndex - 2) % 4 === 0) {\n        tx = swiperSize + round * 4 * swiperSize;\n        tz = swiperSize;\n      } else if ((slideIndex - 3) % 4 === 0) {\n        tx = -swiperSize;\n        tz = 3 * swiperSize + swiperSize * 4 * round;\n      }\n      if (rtl) {\n        tx = -tx;\n      }\n      if (!isHorizontal) {\n        ty = tx;\n        tx = 0;\n      }\n      const transform = `rotateX(${isHorizontal ? 0 : -slideAngle}deg) rotateY(${isHorizontal ? slideAngle : 0}deg) translate3d(${tx}px, ${ty}px, ${tz}px)`;\n      if (progress <= 1 && progress > -1) {\n        wrapperRotate = slideIndex * 90 + progress * 90;\n        if (rtl) wrapperRotate = -slideIndex * 90 - progress * 90;\n      }\n      slideEl.style.transform = transform;\n      if (params.slideShadows) {\n        createSlideShadows(slideEl, progress, isHorizontal);\n      }\n    }\n    wrapperEl.style.transformOrigin = `50% 50% -${swiperSize / 2}px`;\n    wrapperEl.style['-webkit-transform-origin'] = `50% 50% -${swiperSize / 2}px`;\n    if (params.shadow) {\n      if (isHorizontal) {\n        cubeShadowEl.style.transform = `translate3d(0px, ${swiperWidth / 2 + params.shadowOffset}px, ${-swiperWidth / 2}px) rotateX(90deg) rotateZ(0deg) scale(${params.shadowScale})`;\n      } else {\n        const shadowAngle = Math.abs(wrapperRotate) - Math.floor(Math.abs(wrapperRotate) / 90) * 90;\n        const multiplier = 1.5 - (Math.sin(shadowAngle * 2 * Math.PI / 360) / 2 + Math.cos(shadowAngle * 2 * Math.PI / 360) / 2);\n        const scale1 = params.shadowScale;\n        const scale2 = params.shadowScale / multiplier;\n        const offset = params.shadowOffset;\n        cubeShadowEl.style.transform = `scale3d(${scale1}, 1, ${scale2}) translate3d(0px, ${swiperHeight / 2 + offset}px, ${-swiperHeight / 2 / scale2}px) rotateX(-90deg)`;\n      }\n    }\n    const zFactor = (browser.isSafari || browser.isWebView) && browser.needPerspectiveFix ? -swiperSize / 2 : 0;\n    wrapperEl.style.transform = `translate3d(0px,0,${zFactor}px) rotateX(${swiper.isHorizontal() ? 0 : wrapperRotate}deg) rotateY(${swiper.isHorizontal() ? -wrapperRotate : 0}deg)`;\n    wrapperEl.style.setProperty('--swiper-cube-translate-z', `${zFactor}px`);\n  };\n  const setTransition = duration => {\n    const {\n      el,\n      slides\n    } = swiper;\n    slides.forEach(slideEl => {\n      slideEl.style.transitionDuration = `${duration}ms`;\n      slideEl.querySelectorAll('.swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left').forEach(subEl => {\n        subEl.style.transitionDuration = `${duration}ms`;\n      });\n    });\n    if (swiper.params.cubeEffect.shadow && !swiper.isHorizontal()) {\n      const shadowEl = el.querySelector('.swiper-cube-shadow');\n      if (shadowEl) shadowEl.style.transitionDuration = `${duration}ms`;\n    }\n  };\n  effectInit({\n    effect: 'cube',\n    swiper,\n    on,\n    setTranslate,\n    setTransition,\n    recreateShadows,\n    getEffectParams: () => swiper.params.cubeEffect,\n    perspective: () => true,\n    overwriteParams: () => ({\n      slidesPerView: 1,\n      slidesPerGroup: 1,\n      watchSlidesProgress: true,\n      resistanceRatio: 0,\n      spaceBetween: 0,\n      centeredSlides: false,\n      virtualTranslate: true\n    })\n  });\n}", "map": {"version": 3, "names": ["effectInit", "createElement", "EffectCube", "_ref", "swiper", "extendParams", "on", "cubeEffect", "slideShadows", "shadow", "shadowOffset", "shadowScale", "createSlideShadows", "slideEl", "progress", "isHorizontal", "shadowBefore", "querySelector", "shadowAfter", "append", "style", "opacity", "Math", "max", "recreateShadows", "slides", "for<PERSON>ach", "min", "setTranslate", "el", "wrapperEl", "width", "swiper<PERSON><PERSON><PERSON>", "height", "swiperHeight", "rtlTranslate", "rtl", "size", "swiperSize", "browser", "params", "isVirtual", "virtual", "enabled", "wrapperRotate", "cubeShadowEl", "slidesEl", "i", "length", "slideIndex", "parseInt", "getAttribute", "slideAngle", "round", "floor", "tx", "ty", "tz", "transform", "transform<PERSON><PERSON>in", "shadowAngle", "abs", "multiplier", "sin", "PI", "cos", "scale1", "scale2", "offset", "zFactor", "<PERSON><PERSON><PERSON><PERSON>", "isWebView", "needPerspectiveFix", "setProperty", "setTransition", "duration", "transitionDuration", "querySelectorAll", "subEl", "shadowEl", "effect", "getEffectParams", "perspective", "overwriteParams", "<PERSON><PERSON><PERSON><PERSON>iew", "slidesPerGroup", "watchSlidesProgress", "resistanceRatio", "spaceBetween", "centeredSlides", "virtualTranslate"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/modules/effect-cube/effect-cube.js"], "sourcesContent": ["import effectInit from '../../shared/effect-init.js';\nimport { createElement } from '../../shared/utils.js';\nexport default function EffectCube({\n  swiper,\n  extendParams,\n  on\n}) {\n  extendParams({\n    cubeEffect: {\n      slideShadows: true,\n      shadow: true,\n      shadowOffset: 20,\n      shadowScale: 0.94\n    }\n  });\n  const createSlideShadows = (slideEl, progress, isHorizontal) => {\n    let shadowBefore = isHorizontal ? slideEl.querySelector('.swiper-slide-shadow-left') : slideEl.querySelector('.swiper-slide-shadow-top');\n    let shadowAfter = isHorizontal ? slideEl.querySelector('.swiper-slide-shadow-right') : slideEl.querySelector('.swiper-slide-shadow-bottom');\n    if (!shadowBefore) {\n      shadowBefore = createElement('div', `swiper-slide-shadow-${isHorizontal ? 'left' : 'top'}`);\n      slideEl.append(shadowBefore);\n    }\n    if (!shadowAfter) {\n      shadowAfter = createElement('div', `swiper-slide-shadow-${isHorizontal ? 'right' : 'bottom'}`);\n      slideEl.append(shadowAfter);\n    }\n    if (shadowBefore) shadowBefore.style.opacity = Math.max(-progress, 0);\n    if (shadowAfter) shadowAfter.style.opacity = Math.max(progress, 0);\n  };\n  const recreateShadows = () => {\n    // create new ones\n    const isHorizontal = swiper.isHorizontal();\n    swiper.slides.forEach(slideEl => {\n      const progress = Math.max(Math.min(slideEl.progress, 1), -1);\n      createSlideShadows(slideEl, progress, isHorizontal);\n    });\n  };\n  const setTranslate = () => {\n    const {\n      el,\n      wrapperEl,\n      slides,\n      width: swiperWidth,\n      height: swiperHeight,\n      rtlTranslate: rtl,\n      size: swiperSize,\n      browser\n    } = swiper;\n    const params = swiper.params.cubeEffect;\n    const isHorizontal = swiper.isHorizontal();\n    const isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n    let wrapperRotate = 0;\n    let cubeShadowEl;\n    if (params.shadow) {\n      if (isHorizontal) {\n        cubeShadowEl = swiper.slidesEl.querySelector('.swiper-cube-shadow');\n        if (!cubeShadowEl) {\n          cubeShadowEl = createElement('div', 'swiper-cube-shadow');\n          swiper.slidesEl.append(cubeShadowEl);\n        }\n        cubeShadowEl.style.height = `${swiperWidth}px`;\n      } else {\n        cubeShadowEl = el.querySelector('.swiper-cube-shadow');\n        if (!cubeShadowEl) {\n          cubeShadowEl = createElement('div', 'swiper-cube-shadow');\n          el.append(cubeShadowEl);\n        }\n      }\n    }\n    for (let i = 0; i < slides.length; i += 1) {\n      const slideEl = slides[i];\n      let slideIndex = i;\n      if (isVirtual) {\n        slideIndex = parseInt(slideEl.getAttribute('data-swiper-slide-index'), 10);\n      }\n      let slideAngle = slideIndex * 90;\n      let round = Math.floor(slideAngle / 360);\n      if (rtl) {\n        slideAngle = -slideAngle;\n        round = Math.floor(-slideAngle / 360);\n      }\n      const progress = Math.max(Math.min(slideEl.progress, 1), -1);\n      let tx = 0;\n      let ty = 0;\n      let tz = 0;\n      if (slideIndex % 4 === 0) {\n        tx = -round * 4 * swiperSize;\n        tz = 0;\n      } else if ((slideIndex - 1) % 4 === 0) {\n        tx = 0;\n        tz = -round * 4 * swiperSize;\n      } else if ((slideIndex - 2) % 4 === 0) {\n        tx = swiperSize + round * 4 * swiperSize;\n        tz = swiperSize;\n      } else if ((slideIndex - 3) % 4 === 0) {\n        tx = -swiperSize;\n        tz = 3 * swiperSize + swiperSize * 4 * round;\n      }\n      if (rtl) {\n        tx = -tx;\n      }\n      if (!isHorizontal) {\n        ty = tx;\n        tx = 0;\n      }\n      const transform = `rotateX(${isHorizontal ? 0 : -slideAngle}deg) rotateY(${isHorizontal ? slideAngle : 0}deg) translate3d(${tx}px, ${ty}px, ${tz}px)`;\n      if (progress <= 1 && progress > -1) {\n        wrapperRotate = slideIndex * 90 + progress * 90;\n        if (rtl) wrapperRotate = -slideIndex * 90 - progress * 90;\n      }\n      slideEl.style.transform = transform;\n      if (params.slideShadows) {\n        createSlideShadows(slideEl, progress, isHorizontal);\n      }\n    }\n    wrapperEl.style.transformOrigin = `50% 50% -${swiperSize / 2}px`;\n    wrapperEl.style['-webkit-transform-origin'] = `50% 50% -${swiperSize / 2}px`;\n    if (params.shadow) {\n      if (isHorizontal) {\n        cubeShadowEl.style.transform = `translate3d(0px, ${swiperWidth / 2 + params.shadowOffset}px, ${-swiperWidth / 2}px) rotateX(90deg) rotateZ(0deg) scale(${params.shadowScale})`;\n      } else {\n        const shadowAngle = Math.abs(wrapperRotate) - Math.floor(Math.abs(wrapperRotate) / 90) * 90;\n        const multiplier = 1.5 - (Math.sin(shadowAngle * 2 * Math.PI / 360) / 2 + Math.cos(shadowAngle * 2 * Math.PI / 360) / 2);\n        const scale1 = params.shadowScale;\n        const scale2 = params.shadowScale / multiplier;\n        const offset = params.shadowOffset;\n        cubeShadowEl.style.transform = `scale3d(${scale1}, 1, ${scale2}) translate3d(0px, ${swiperHeight / 2 + offset}px, ${-swiperHeight / 2 / scale2}px) rotateX(-90deg)`;\n      }\n    }\n    const zFactor = (browser.isSafari || browser.isWebView) && browser.needPerspectiveFix ? -swiperSize / 2 : 0;\n    wrapperEl.style.transform = `translate3d(0px,0,${zFactor}px) rotateX(${swiper.isHorizontal() ? 0 : wrapperRotate}deg) rotateY(${swiper.isHorizontal() ? -wrapperRotate : 0}deg)`;\n    wrapperEl.style.setProperty('--swiper-cube-translate-z', `${zFactor}px`);\n  };\n  const setTransition = duration => {\n    const {\n      el,\n      slides\n    } = swiper;\n    slides.forEach(slideEl => {\n      slideEl.style.transitionDuration = `${duration}ms`;\n      slideEl.querySelectorAll('.swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left').forEach(subEl => {\n        subEl.style.transitionDuration = `${duration}ms`;\n      });\n    });\n    if (swiper.params.cubeEffect.shadow && !swiper.isHorizontal()) {\n      const shadowEl = el.querySelector('.swiper-cube-shadow');\n      if (shadowEl) shadowEl.style.transitionDuration = `${duration}ms`;\n    }\n  };\n  effectInit({\n    effect: 'cube',\n    swiper,\n    on,\n    setTranslate,\n    setTransition,\n    recreateShadows,\n    getEffectParams: () => swiper.params.cubeEffect,\n    perspective: () => true,\n    overwriteParams: () => ({\n      slidesPerView: 1,\n      slidesPerGroup: 1,\n      watchSlidesProgress: true,\n      resistanceRatio: 0,\n      spaceBetween: 0,\n      centeredSlides: false,\n      virtualTranslate: true\n    })\n  });\n}"], "mappings": "AAAA,OAAOA,UAAU,MAAM,6BAA6B;AACpD,SAASC,aAAa,QAAQ,uBAAuB;AACrD,eAAe,SAASC,UAAUA,CAAAC,IAAA,EAI/B;EAAA,IAJgC;IACjCC,MAAM;IACNC,YAAY;IACZC;EACF,CAAC,GAAAH,IAAA;EACCE,YAAY,CAAC;IACXE,UAAU,EAAE;MACVC,YAAY,EAAE,IAAI;MAClBC,MAAM,EAAE,IAAI;MACZC,YAAY,EAAE,EAAE;MAChBC,WAAW,EAAE;IACf;EACF,CAAC,CAAC;EACF,MAAMC,kBAAkB,GAAGA,CAACC,OAAO,EAAEC,QAAQ,EAAEC,YAAY,KAAK;IAC9D,IAAIC,YAAY,GAAGD,YAAY,GAAGF,OAAO,CAACI,aAAa,CAAC,2BAA2B,CAAC,GAAGJ,OAAO,CAACI,aAAa,CAAC,0BAA0B,CAAC;IACxI,IAAIC,WAAW,GAAGH,YAAY,GAAGF,OAAO,CAACI,aAAa,CAAC,4BAA4B,CAAC,GAAGJ,OAAO,CAACI,aAAa,CAAC,6BAA6B,CAAC;IAC3I,IAAI,CAACD,YAAY,EAAE;MACjBA,YAAY,GAAGf,aAAa,CAAC,KAAK,EAAG,uBAAsBc,YAAY,GAAG,MAAM,GAAG,KAAM,EAAC,CAAC;MAC3FF,OAAO,CAACM,MAAM,CAACH,YAAY,CAAC;IAC9B;IACA,IAAI,CAACE,WAAW,EAAE;MAChBA,WAAW,GAAGjB,aAAa,CAAC,KAAK,EAAG,uBAAsBc,YAAY,GAAG,OAAO,GAAG,QAAS,EAAC,CAAC;MAC9FF,OAAO,CAACM,MAAM,CAACD,WAAW,CAAC;IAC7B;IACA,IAAIF,YAAY,EAAEA,YAAY,CAACI,KAAK,CAACC,OAAO,GAAGC,IAAI,CAACC,GAAG,CAAC,CAACT,QAAQ,EAAE,CAAC,CAAC;IACrE,IAAII,WAAW,EAAEA,WAAW,CAACE,KAAK,CAACC,OAAO,GAAGC,IAAI,CAACC,GAAG,CAACT,QAAQ,EAAE,CAAC,CAAC;EACpE,CAAC;EACD,MAAMU,eAAe,GAAGA,CAAA,KAAM;IAC5B;IACA,MAAMT,YAAY,GAAGX,MAAM,CAACW,YAAY,EAAE;IAC1CX,MAAM,CAACqB,MAAM,CAACC,OAAO,CAACb,OAAO,IAAI;MAC/B,MAAMC,QAAQ,GAAGQ,IAAI,CAACC,GAAG,CAACD,IAAI,CAACK,GAAG,CAACd,OAAO,CAACC,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAC5DF,kBAAkB,CAACC,OAAO,EAAEC,QAAQ,EAAEC,YAAY,CAAC;IACrD,CAAC,CAAC;EACJ,CAAC;EACD,MAAMa,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAM;MACJC,EAAE;MACFC,SAAS;MACTL,MAAM;MACNM,KAAK,EAAEC,WAAW;MAClBC,MAAM,EAAEC,YAAY;MACpBC,YAAY,EAAEC,GAAG;MACjBC,IAAI,EAAEC,UAAU;MAChBC;IACF,CAAC,GAAGnC,MAAM;IACV,MAAMoC,MAAM,GAAGpC,MAAM,CAACoC,MAAM,CAACjC,UAAU;IACvC,MAAMQ,YAAY,GAAGX,MAAM,CAACW,YAAY,EAAE;IAC1C,MAAM0B,SAAS,GAAGrC,MAAM,CAACsC,OAAO,IAAItC,MAAM,CAACoC,MAAM,CAACE,OAAO,CAACC,OAAO;IACjE,IAAIC,aAAa,GAAG,CAAC;IACrB,IAAIC,YAAY;IAChB,IAAIL,MAAM,CAAC/B,MAAM,EAAE;MACjB,IAAIM,YAAY,EAAE;QAChB8B,YAAY,GAAGzC,MAAM,CAAC0C,QAAQ,CAAC7B,aAAa,CAAC,qBAAqB,CAAC;QACnE,IAAI,CAAC4B,YAAY,EAAE;UACjBA,YAAY,GAAG5C,aAAa,CAAC,KAAK,EAAE,oBAAoB,CAAC;UACzDG,MAAM,CAAC0C,QAAQ,CAAC3B,MAAM,CAAC0B,YAAY,CAAC;QACtC;QACAA,YAAY,CAACzB,KAAK,CAACa,MAAM,GAAI,GAAED,WAAY,IAAG;MAChD,CAAC,MAAM;QACLa,YAAY,GAAGhB,EAAE,CAACZ,aAAa,CAAC,qBAAqB,CAAC;QACtD,IAAI,CAAC4B,YAAY,EAAE;UACjBA,YAAY,GAAG5C,aAAa,CAAC,KAAK,EAAE,oBAAoB,CAAC;UACzD4B,EAAE,CAACV,MAAM,CAAC0B,YAAY,CAAC;QACzB;MACF;IACF;IACA,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtB,MAAM,CAACuB,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;MACzC,MAAMlC,OAAO,GAAGY,MAAM,CAACsB,CAAC,CAAC;MACzB,IAAIE,UAAU,GAAGF,CAAC;MAClB,IAAIN,SAAS,EAAE;QACbQ,UAAU,GAAGC,QAAQ,CAACrC,OAAO,CAACsC,YAAY,CAAC,yBAAyB,CAAC,EAAE,EAAE,CAAC;MAC5E;MACA,IAAIC,UAAU,GAAGH,UAAU,GAAG,EAAE;MAChC,IAAII,KAAK,GAAG/B,IAAI,CAACgC,KAAK,CAACF,UAAU,GAAG,GAAG,CAAC;MACxC,IAAIhB,GAAG,EAAE;QACPgB,UAAU,GAAG,CAACA,UAAU;QACxBC,KAAK,GAAG/B,IAAI,CAACgC,KAAK,CAAC,CAACF,UAAU,GAAG,GAAG,CAAC;MACvC;MACA,MAAMtC,QAAQ,GAAGQ,IAAI,CAACC,GAAG,CAACD,IAAI,CAACK,GAAG,CAACd,OAAO,CAACC,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAC5D,IAAIyC,EAAE,GAAG,CAAC;MACV,IAAIC,EAAE,GAAG,CAAC;MACV,IAAIC,EAAE,GAAG,CAAC;MACV,IAAIR,UAAU,GAAG,CAAC,KAAK,CAAC,EAAE;QACxBM,EAAE,GAAG,CAACF,KAAK,GAAG,CAAC,GAAGf,UAAU;QAC5BmB,EAAE,GAAG,CAAC;MACR,CAAC,MAAM,IAAI,CAACR,UAAU,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;QACrCM,EAAE,GAAG,CAAC;QACNE,EAAE,GAAG,CAACJ,KAAK,GAAG,CAAC,GAAGf,UAAU;MAC9B,CAAC,MAAM,IAAI,CAACW,UAAU,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;QACrCM,EAAE,GAAGjB,UAAU,GAAGe,KAAK,GAAG,CAAC,GAAGf,UAAU;QACxCmB,EAAE,GAAGnB,UAAU;MACjB,CAAC,MAAM,IAAI,CAACW,UAAU,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;QACrCM,EAAE,GAAG,CAACjB,UAAU;QAChBmB,EAAE,GAAG,CAAC,GAAGnB,UAAU,GAAGA,UAAU,GAAG,CAAC,GAAGe,KAAK;MAC9C;MACA,IAAIjB,GAAG,EAAE;QACPmB,EAAE,GAAG,CAACA,EAAE;MACV;MACA,IAAI,CAACxC,YAAY,EAAE;QACjByC,EAAE,GAAGD,EAAE;QACPA,EAAE,GAAG,CAAC;MACR;MACA,MAAMG,SAAS,GAAI,WAAU3C,YAAY,GAAG,CAAC,GAAG,CAACqC,UAAW,gBAAerC,YAAY,GAAGqC,UAAU,GAAG,CAAE,oBAAmBG,EAAG,OAAMC,EAAG,OAAMC,EAAG,KAAI;MACrJ,IAAI3C,QAAQ,IAAI,CAAC,IAAIA,QAAQ,GAAG,CAAC,CAAC,EAAE;QAClC8B,aAAa,GAAGK,UAAU,GAAG,EAAE,GAAGnC,QAAQ,GAAG,EAAE;QAC/C,IAAIsB,GAAG,EAAEQ,aAAa,GAAG,CAACK,UAAU,GAAG,EAAE,GAAGnC,QAAQ,GAAG,EAAE;MAC3D;MACAD,OAAO,CAACO,KAAK,CAACsC,SAAS,GAAGA,SAAS;MACnC,IAAIlB,MAAM,CAAChC,YAAY,EAAE;QACvBI,kBAAkB,CAACC,OAAO,EAAEC,QAAQ,EAAEC,YAAY,CAAC;MACrD;IACF;IACAe,SAAS,CAACV,KAAK,CAACuC,eAAe,GAAI,YAAWrB,UAAU,GAAG,CAAE,IAAG;IAChER,SAAS,CAACV,KAAK,CAAC,0BAA0B,CAAC,GAAI,YAAWkB,UAAU,GAAG,CAAE,IAAG;IAC5E,IAAIE,MAAM,CAAC/B,MAAM,EAAE;MACjB,IAAIM,YAAY,EAAE;QAChB8B,YAAY,CAACzB,KAAK,CAACsC,SAAS,GAAI,oBAAmB1B,WAAW,GAAG,CAAC,GAAGQ,MAAM,CAAC9B,YAAa,OAAM,CAACsB,WAAW,GAAG,CAAE,0CAAyCQ,MAAM,CAAC7B,WAAY,GAAE;MAChL,CAAC,MAAM;QACL,MAAMiD,WAAW,GAAGtC,IAAI,CAACuC,GAAG,CAACjB,aAAa,CAAC,GAAGtB,IAAI,CAACgC,KAAK,CAAChC,IAAI,CAACuC,GAAG,CAACjB,aAAa,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE;QAC3F,MAAMkB,UAAU,GAAG,GAAG,IAAIxC,IAAI,CAACyC,GAAG,CAACH,WAAW,GAAG,CAAC,GAAGtC,IAAI,CAAC0C,EAAE,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG1C,IAAI,CAAC2C,GAAG,CAACL,WAAW,GAAG,CAAC,GAAGtC,IAAI,CAAC0C,EAAE,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;QACxH,MAAME,MAAM,GAAG1B,MAAM,CAAC7B,WAAW;QACjC,MAAMwD,MAAM,GAAG3B,MAAM,CAAC7B,WAAW,GAAGmD,UAAU;QAC9C,MAAMM,MAAM,GAAG5B,MAAM,CAAC9B,YAAY;QAClCmC,YAAY,CAACzB,KAAK,CAACsC,SAAS,GAAI,WAAUQ,MAAO,QAAOC,MAAO,sBAAqBjC,YAAY,GAAG,CAAC,GAAGkC,MAAO,OAAM,CAAClC,YAAY,GAAG,CAAC,GAAGiC,MAAO,qBAAoB;MACrK;IACF;IACA,MAAME,OAAO,GAAG,CAAC9B,OAAO,CAAC+B,QAAQ,IAAI/B,OAAO,CAACgC,SAAS,KAAKhC,OAAO,CAACiC,kBAAkB,GAAG,CAAClC,UAAU,GAAG,CAAC,GAAG,CAAC;IAC3GR,SAAS,CAACV,KAAK,CAACsC,SAAS,GAAI,qBAAoBW,OAAQ,eAAcjE,MAAM,CAACW,YAAY,EAAE,GAAG,CAAC,GAAG6B,aAAc,gBAAexC,MAAM,CAACW,YAAY,EAAE,GAAG,CAAC6B,aAAa,GAAG,CAAE,MAAK;IAChLd,SAAS,CAACV,KAAK,CAACqD,WAAW,CAAC,2BAA2B,EAAG,GAAEJ,OAAQ,IAAG,CAAC;EAC1E,CAAC;EACD,MAAMK,aAAa,GAAGC,QAAQ,IAAI;IAChC,MAAM;MACJ9C,EAAE;MACFJ;IACF,CAAC,GAAGrB,MAAM;IACVqB,MAAM,CAACC,OAAO,CAACb,OAAO,IAAI;MACxBA,OAAO,CAACO,KAAK,CAACwD,kBAAkB,GAAI,GAAED,QAAS,IAAG;MAClD9D,OAAO,CAACgE,gBAAgB,CAAC,8GAA8G,CAAC,CAACnD,OAAO,CAACoD,KAAK,IAAI;QACxJA,KAAK,CAAC1D,KAAK,CAACwD,kBAAkB,GAAI,GAAED,QAAS,IAAG;MAClD,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,IAAIvE,MAAM,CAACoC,MAAM,CAACjC,UAAU,CAACE,MAAM,IAAI,CAACL,MAAM,CAACW,YAAY,EAAE,EAAE;MAC7D,MAAMgE,QAAQ,GAAGlD,EAAE,CAACZ,aAAa,CAAC,qBAAqB,CAAC;MACxD,IAAI8D,QAAQ,EAAEA,QAAQ,CAAC3D,KAAK,CAACwD,kBAAkB,GAAI,GAAED,QAAS,IAAG;IACnE;EACF,CAAC;EACD3E,UAAU,CAAC;IACTgF,MAAM,EAAE,MAAM;IACd5E,MAAM;IACNE,EAAE;IACFsB,YAAY;IACZ8C,aAAa;IACblD,eAAe;IACfyD,eAAe,EAAEA,CAAA,KAAM7E,MAAM,CAACoC,MAAM,CAACjC,UAAU;IAC/C2E,WAAW,EAAEA,CAAA,KAAM,IAAI;IACvBC,eAAe,EAAEA,CAAA,MAAO;MACtBC,aAAa,EAAE,CAAC;MAChBC,cAAc,EAAE,CAAC;MACjBC,mBAAmB,EAAE,IAAI;MACzBC,eAAe,EAAE,CAAC;MAClBC,YAAY,EAAE,CAAC;MACfC,cAAc,EAAE,KAAK;MACrBC,gBAAgB,EAAE;IACpB,CAAC;EACH,CAAC,CAAC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}