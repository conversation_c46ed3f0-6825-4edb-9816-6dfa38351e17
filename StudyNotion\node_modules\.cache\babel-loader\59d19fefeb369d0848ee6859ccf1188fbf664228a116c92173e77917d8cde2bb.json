{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nvar _ForwardReplayControl = _interopRequireDefault(require(\"./ForwardReplayControl\"));\n\n// Pass mode into parent function\nvar ReplayControl = (0, _ForwardReplayControl[\"default\"])('replay');\nReplayControl.displayName = 'ReplayControl';\nvar _default = ReplayControl;\nexports[\"default\"] = _default;", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "_ForwardReplayControl", "ReplayControl", "displayName", "_default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/video-react/lib/components/control-bar/ReplayControl.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _ForwardReplayControl = _interopRequireDefault(require(\"./ForwardReplayControl\"));\n\n// Pass mode into parent function\nvar ReplayControl = (0, _ForwardReplayControl[\"default\"])('replay');\nReplayControl.displayName = 'ReplayControl';\nvar _default = ReplayControl;\nexports[\"default\"] = _default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC;AAEpFC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;AAE3B,IAAIE,qBAAqB,GAAGN,sBAAsB,CAACC,OAAO,CAAC,wBAAwB,CAAC,CAAC;;AAErF;AACA,IAAIM,aAAa,GAAG,CAAC,CAAC,EAAED,qBAAqB,CAAC,SAAS,CAAC,EAAE,QAAQ,CAAC;AACnEC,aAAa,CAACC,WAAW,GAAG,eAAe;AAC3C,IAAIC,QAAQ,GAAGF,aAAa;AAC5BH,OAAO,CAAC,SAAS,CAAC,GAAGK,QAAQ"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}