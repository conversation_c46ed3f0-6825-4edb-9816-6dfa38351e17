{"ast": null, "code": "/**\n * @typedef {import('micromark-util-types').Construct} Construct\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Tokenizer} Tokenizer\n */\n\nimport { asciiAlpha, asciiAlphanumeric, asciiAtext, asciiControl } from 'micromark-util-character';\nimport { codes } from 'micromark-util-symbol/codes.js';\nimport { constants } from 'micromark-util-symbol/constants.js';\nimport { types } from 'micromark-util-symbol/types.js';\nimport { ok as assert } from 'uvu/assert';\n\n/** @type {Construct} */\nexport const autolink = {\n  name: 'autolink',\n  tokenize: tokenizeAutolink\n};\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeAutolink(effects, ok, nok) {\n  let size = 0;\n  return start;\n\n  /**\n   * Start of an autolink.\n   *\n   * ```markdown\n   * > | a<https://example.com>b\n   *      ^\n   * > | a<<EMAIL>>b\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.lessThan, 'expected `<`');\n    effects.enter(types.autolink);\n    effects.enter(types.autolinkMarker);\n    effects.consume(code);\n    effects.exit(types.autolinkMarker);\n    effects.enter(types.autolinkProtocol);\n    return open;\n  }\n\n  /**\n   * After `<`, at protocol or atext.\n   *\n   * ```markdown\n   * > | a<https://example.com>b\n   *       ^\n   * > | a<<EMAIL>>b\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function open(code) {\n    if (asciiAlpha(code)) {\n      effects.consume(code);\n      return schemeOrEmailAtext;\n    }\n    return emailAtext(code);\n  }\n\n  /**\n   * At second byte of protocol or atext.\n   *\n   * ```markdown\n   * > | a<https://example.com>b\n   *        ^\n   * > | a<<EMAIL>>b\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function schemeOrEmailAtext(code) {\n    // ASCII alphanumeric and `+`, `-`, and `.`.\n    if (code === codes.plusSign || code === codes.dash || code === codes.dot || asciiAlphanumeric(code)) {\n      // Count the previous alphabetical from `open` too.\n      size = 1;\n      return schemeInsideOrEmailAtext(code);\n    }\n    return emailAtext(code);\n  }\n\n  /**\n   * In ambiguous protocol or atext.\n   *\n   * ```markdown\n   * > | a<https://example.com>b\n   *        ^\n   * > | a<<EMAIL>>b\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function schemeInsideOrEmailAtext(code) {\n    if (code === codes.colon) {\n      effects.consume(code);\n      size = 0;\n      return urlInside;\n    }\n\n    // ASCII alphanumeric and `+`, `-`, and `.`.\n    if ((code === codes.plusSign || code === codes.dash || code === codes.dot || asciiAlphanumeric(code)) && size++ < constants.autolinkSchemeSizeMax) {\n      effects.consume(code);\n      return schemeInsideOrEmailAtext;\n    }\n    size = 0;\n    return emailAtext(code);\n  }\n\n  /**\n   * After protocol, in URL.\n   *\n   * ```markdown\n   * > | a<https://example.com>b\n   *             ^\n   * ```\n   *\n   * @type {State}\n   */\n  function urlInside(code) {\n    if (code === codes.greaterThan) {\n      effects.exit(types.autolinkProtocol);\n      effects.enter(types.autolinkMarker);\n      effects.consume(code);\n      effects.exit(types.autolinkMarker);\n      effects.exit(types.autolink);\n      return ok;\n    }\n\n    // ASCII control, space, or `<`.\n    if (code === codes.eof || code === codes.space || code === codes.lessThan || asciiControl(code)) {\n      return nok(code);\n    }\n    effects.consume(code);\n    return urlInside;\n  }\n\n  /**\n   * In email atext.\n   *\n   * ```markdown\n   * > | a<<EMAIL>>b\n   *              ^\n   * ```\n   *\n   * @type {State}\n   */\n  function emailAtext(code) {\n    if (code === codes.atSign) {\n      effects.consume(code);\n      return emailAtSignOrDot;\n    }\n    if (asciiAtext(code)) {\n      effects.consume(code);\n      return emailAtext;\n    }\n    return nok(code);\n  }\n\n  /**\n   * In label, after at-sign or dot.\n   *\n   * ```markdown\n   * > | a<<EMAIL>>b\n   *                 ^       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function emailAtSignOrDot(code) {\n    return asciiAlphanumeric(code) ? emailLabel(code) : nok(code);\n  }\n\n  /**\n   * In label, where `.` and `>` are allowed.\n   *\n   * ```markdown\n   * > | a<<EMAIL>>b\n   *                   ^\n   * ```\n   *\n   * @type {State}\n   */\n  function emailLabel(code) {\n    if (code === codes.dot) {\n      effects.consume(code);\n      size = 0;\n      return emailAtSignOrDot;\n    }\n    if (code === codes.greaterThan) {\n      // Exit, then change the token type.\n      effects.exit(types.autolinkProtocol).type = types.autolinkEmail;\n      effects.enter(types.autolinkMarker);\n      effects.consume(code);\n      effects.exit(types.autolinkMarker);\n      effects.exit(types.autolink);\n      return ok;\n    }\n    return emailValue(code);\n  }\n\n  /**\n   * In label, where `.` and `>` are *not* allowed.\n   *\n   * Though, this is also used in `emailLabel` to parse other values.\n   *\n   * ```markdown\n   * > | a<<EMAIL>>b\n   *                    ^\n   * ```\n   *\n   * @type {State}\n   */\n  function emailValue(code) {\n    // ASCII alphanumeric or `-`.\n    if ((code === codes.dash || asciiAlphanumeric(code)) && size++ < constants.autolinkDomainSizeMax) {\n      const next = code === codes.dash ? emailValue : emailLabel;\n      effects.consume(code);\n      return next;\n    }\n    return nok(code);\n  }\n}", "map": {"version": 3, "names": ["asciiAlpha", "asciiAlphanumeric", "asciiAtext", "asciiControl", "codes", "constants", "types", "ok", "assert", "autolink", "name", "tokenize", "tokenizeAutolink", "effects", "nok", "size", "start", "code", "lessThan", "enter", "autolinkMarker", "consume", "exit", "autolinkProtocol", "open", "schemeOrEmailAtext", "emailAtext", "plusSign", "dash", "dot", "schemeInsideOrEmailAtext", "colon", "urlInside", "autolinkSchemeSizeMax", "greaterThan", "eof", "space", "atSign", "emailAtSignOrDot", "emailLabel", "type", "autolinkEmail", "emailValue", "autolinkDomainSizeMax", "next"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/micromark-core-commonmark/dev/lib/autolink.js"], "sourcesContent": ["/**\n * @typedef {import('micromark-util-types').Construct} Construct\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Tokenizer} Tokenizer\n */\n\nimport {\n  asciiAlpha,\n  asciiAlphanumeric,\n  asciiAtext,\n  asciiControl\n} from 'micromark-util-character'\nimport {codes} from 'micromark-util-symbol/codes.js'\nimport {constants} from 'micromark-util-symbol/constants.js'\nimport {types} from 'micromark-util-symbol/types.js'\nimport {ok as assert} from 'uvu/assert'\n\n/** @type {Construct} */\nexport const autolink = {name: 'autolink', tokenize: tokenizeAutolink}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeAutolink(effects, ok, nok) {\n  let size = 0\n\n  return start\n\n  /**\n   * Start of an autolink.\n   *\n   * ```markdown\n   * > | a<https://example.com>b\n   *      ^\n   * > | a<<EMAIL>>b\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.lessThan, 'expected `<`')\n    effects.enter(types.autolink)\n    effects.enter(types.autolinkMarker)\n    effects.consume(code)\n    effects.exit(types.autolinkMarker)\n    effects.enter(types.autolinkProtocol)\n    return open\n  }\n\n  /**\n   * After `<`, at protocol or atext.\n   *\n   * ```markdown\n   * > | a<https://example.com>b\n   *       ^\n   * > | a<<EMAIL>>b\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function open(code) {\n    if (asciiAlpha(code)) {\n      effects.consume(code)\n      return schemeOrEmailAtext\n    }\n\n    return emailAtext(code)\n  }\n\n  /**\n   * At second byte of protocol or atext.\n   *\n   * ```markdown\n   * > | a<https://example.com>b\n   *        ^\n   * > | a<<EMAIL>>b\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function schemeOrEmailAtext(code) {\n    // ASCII alphanumeric and `+`, `-`, and `.`.\n    if (\n      code === codes.plusSign ||\n      code === codes.dash ||\n      code === codes.dot ||\n      asciiAlphanumeric(code)\n    ) {\n      // Count the previous alphabetical from `open` too.\n      size = 1\n      return schemeInsideOrEmailAtext(code)\n    }\n\n    return emailAtext(code)\n  }\n\n  /**\n   * In ambiguous protocol or atext.\n   *\n   * ```markdown\n   * > | a<https://example.com>b\n   *        ^\n   * > | a<<EMAIL>>b\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function schemeInsideOrEmailAtext(code) {\n    if (code === codes.colon) {\n      effects.consume(code)\n      size = 0\n      return urlInside\n    }\n\n    // ASCII alphanumeric and `+`, `-`, and `.`.\n    if (\n      (code === codes.plusSign ||\n        code === codes.dash ||\n        code === codes.dot ||\n        asciiAlphanumeric(code)) &&\n      size++ < constants.autolinkSchemeSizeMax\n    ) {\n      effects.consume(code)\n      return schemeInsideOrEmailAtext\n    }\n\n    size = 0\n    return emailAtext(code)\n  }\n\n  /**\n   * After protocol, in URL.\n   *\n   * ```markdown\n   * > | a<https://example.com>b\n   *             ^\n   * ```\n   *\n   * @type {State}\n   */\n  function urlInside(code) {\n    if (code === codes.greaterThan) {\n      effects.exit(types.autolinkProtocol)\n      effects.enter(types.autolinkMarker)\n      effects.consume(code)\n      effects.exit(types.autolinkMarker)\n      effects.exit(types.autolink)\n      return ok\n    }\n\n    // ASCII control, space, or `<`.\n    if (\n      code === codes.eof ||\n      code === codes.space ||\n      code === codes.lessThan ||\n      asciiControl(code)\n    ) {\n      return nok(code)\n    }\n\n    effects.consume(code)\n    return urlInside\n  }\n\n  /**\n   * In email atext.\n   *\n   * ```markdown\n   * > | a<<EMAIL>>b\n   *              ^\n   * ```\n   *\n   * @type {State}\n   */\n  function emailAtext(code) {\n    if (code === codes.atSign) {\n      effects.consume(code)\n      return emailAtSignOrDot\n    }\n\n    if (asciiAtext(code)) {\n      effects.consume(code)\n      return emailAtext\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In label, after at-sign or dot.\n   *\n   * ```markdown\n   * > | a<<EMAIL>>b\n   *                 ^       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function emailAtSignOrDot(code) {\n    return asciiAlphanumeric(code) ? emailLabel(code) : nok(code)\n  }\n\n  /**\n   * In label, where `.` and `>` are allowed.\n   *\n   * ```markdown\n   * > | a<<EMAIL>>b\n   *                   ^\n   * ```\n   *\n   * @type {State}\n   */\n  function emailLabel(code) {\n    if (code === codes.dot) {\n      effects.consume(code)\n      size = 0\n      return emailAtSignOrDot\n    }\n\n    if (code === codes.greaterThan) {\n      // Exit, then change the token type.\n      effects.exit(types.autolinkProtocol).type = types.autolinkEmail\n      effects.enter(types.autolinkMarker)\n      effects.consume(code)\n      effects.exit(types.autolinkMarker)\n      effects.exit(types.autolink)\n      return ok\n    }\n\n    return emailValue(code)\n  }\n\n  /**\n   * In label, where `.` and `>` are *not* allowed.\n   *\n   * Though, this is also used in `emailLabel` to parse other values.\n   *\n   * ```markdown\n   * > | a<<EMAIL>>b\n   *                    ^\n   * ```\n   *\n   * @type {State}\n   */\n  function emailValue(code) {\n    // ASCII alphanumeric or `-`.\n    if (\n      (code === codes.dash || asciiAlphanumeric(code)) &&\n      size++ < constants.autolinkDomainSizeMax\n    ) {\n      const next = code === codes.dash ? emailValue : emailLabel\n      effects.consume(code)\n      return next\n    }\n\n    return nok(code)\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,SACEA,UAAU,EACVC,iBAAiB,EACjBC,UAAU,EACVC,YAAY,QACP,0BAA0B;AACjC,SAAQC,KAAK,QAAO,gCAAgC;AACpD,SAAQC,SAAS,QAAO,oCAAoC;AAC5D,SAAQC,KAAK,QAAO,gCAAgC;AACpD,SAAQC,EAAE,IAAIC,MAAM,QAAO,YAAY;;AAEvC;AACA,OAAO,MAAMC,QAAQ,GAAG;EAACC,IAAI,EAAE,UAAU;EAAEC,QAAQ,EAAEC;AAAgB,CAAC;;AAEtE;AACA;AACA;AACA;AACA,SAASA,gBAAgBA,CAACC,OAAO,EAAEN,EAAE,EAAEO,GAAG,EAAE;EAC1C,IAAIC,IAAI,GAAG,CAAC;EAEZ,OAAOC,KAAK;;EAEZ;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,KAAKA,CAACC,IAAI,EAAE;IACnBT,MAAM,CAACS,IAAI,KAAKb,KAAK,CAACc,QAAQ,EAAE,cAAc,CAAC;IAC/CL,OAAO,CAACM,KAAK,CAACb,KAAK,CAACG,QAAQ,CAAC;IAC7BI,OAAO,CAACM,KAAK,CAACb,KAAK,CAACc,cAAc,CAAC;IACnCP,OAAO,CAACQ,OAAO,CAACJ,IAAI,CAAC;IACrBJ,OAAO,CAACS,IAAI,CAAChB,KAAK,CAACc,cAAc,CAAC;IAClCP,OAAO,CAACM,KAAK,CAACb,KAAK,CAACiB,gBAAgB,CAAC;IACrC,OAAOC,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,IAAIA,CAACP,IAAI,EAAE;IAClB,IAAIjB,UAAU,CAACiB,IAAI,CAAC,EAAE;MACpBJ,OAAO,CAACQ,OAAO,CAACJ,IAAI,CAAC;MACrB,OAAOQ,kBAAkB;IAC3B;IAEA,OAAOC,UAAU,CAACT,IAAI,CAAC;EACzB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASQ,kBAAkBA,CAACR,IAAI,EAAE;IAChC;IACA,IACEA,IAAI,KAAKb,KAAK,CAACuB,QAAQ,IACvBV,IAAI,KAAKb,KAAK,CAACwB,IAAI,IACnBX,IAAI,KAAKb,KAAK,CAACyB,GAAG,IAClB5B,iBAAiB,CAACgB,IAAI,CAAC,EACvB;MACA;MACAF,IAAI,GAAG,CAAC;MACR,OAAOe,wBAAwB,CAACb,IAAI,CAAC;IACvC;IAEA,OAAOS,UAAU,CAACT,IAAI,CAAC;EACzB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASa,wBAAwBA,CAACb,IAAI,EAAE;IACtC,IAAIA,IAAI,KAAKb,KAAK,CAAC2B,KAAK,EAAE;MACxBlB,OAAO,CAACQ,OAAO,CAACJ,IAAI,CAAC;MACrBF,IAAI,GAAG,CAAC;MACR,OAAOiB,SAAS;IAClB;;IAEA;IACA,IACE,CAACf,IAAI,KAAKb,KAAK,CAACuB,QAAQ,IACtBV,IAAI,KAAKb,KAAK,CAACwB,IAAI,IACnBX,IAAI,KAAKb,KAAK,CAACyB,GAAG,IAClB5B,iBAAiB,CAACgB,IAAI,CAAC,KACzBF,IAAI,EAAE,GAAGV,SAAS,CAAC4B,qBAAqB,EACxC;MACApB,OAAO,CAACQ,OAAO,CAACJ,IAAI,CAAC;MACrB,OAAOa,wBAAwB;IACjC;IAEAf,IAAI,GAAG,CAAC;IACR,OAAOW,UAAU,CAACT,IAAI,CAAC;EACzB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASe,SAASA,CAACf,IAAI,EAAE;IACvB,IAAIA,IAAI,KAAKb,KAAK,CAAC8B,WAAW,EAAE;MAC9BrB,OAAO,CAACS,IAAI,CAAChB,KAAK,CAACiB,gBAAgB,CAAC;MACpCV,OAAO,CAACM,KAAK,CAACb,KAAK,CAACc,cAAc,CAAC;MACnCP,OAAO,CAACQ,OAAO,CAACJ,IAAI,CAAC;MACrBJ,OAAO,CAACS,IAAI,CAAChB,KAAK,CAACc,cAAc,CAAC;MAClCP,OAAO,CAACS,IAAI,CAAChB,KAAK,CAACG,QAAQ,CAAC;MAC5B,OAAOF,EAAE;IACX;;IAEA;IACA,IACEU,IAAI,KAAKb,KAAK,CAAC+B,GAAG,IAClBlB,IAAI,KAAKb,KAAK,CAACgC,KAAK,IACpBnB,IAAI,KAAKb,KAAK,CAACc,QAAQ,IACvBf,YAAY,CAACc,IAAI,CAAC,EAClB;MACA,OAAOH,GAAG,CAACG,IAAI,CAAC;IAClB;IAEAJ,OAAO,CAACQ,OAAO,CAACJ,IAAI,CAAC;IACrB,OAAOe,SAAS;EAClB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASN,UAAUA,CAACT,IAAI,EAAE;IACxB,IAAIA,IAAI,KAAKb,KAAK,CAACiC,MAAM,EAAE;MACzBxB,OAAO,CAACQ,OAAO,CAACJ,IAAI,CAAC;MACrB,OAAOqB,gBAAgB;IACzB;IAEA,IAAIpC,UAAU,CAACe,IAAI,CAAC,EAAE;MACpBJ,OAAO,CAACQ,OAAO,CAACJ,IAAI,CAAC;MACrB,OAAOS,UAAU;IACnB;IAEA,OAAOZ,GAAG,CAACG,IAAI,CAAC;EAClB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASqB,gBAAgBA,CAACrB,IAAI,EAAE;IAC9B,OAAOhB,iBAAiB,CAACgB,IAAI,CAAC,GAAGsB,UAAU,CAACtB,IAAI,CAAC,GAAGH,GAAG,CAACG,IAAI,CAAC;EAC/D;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASsB,UAAUA,CAACtB,IAAI,EAAE;IACxB,IAAIA,IAAI,KAAKb,KAAK,CAACyB,GAAG,EAAE;MACtBhB,OAAO,CAACQ,OAAO,CAACJ,IAAI,CAAC;MACrBF,IAAI,GAAG,CAAC;MACR,OAAOuB,gBAAgB;IACzB;IAEA,IAAIrB,IAAI,KAAKb,KAAK,CAAC8B,WAAW,EAAE;MAC9B;MACArB,OAAO,CAACS,IAAI,CAAChB,KAAK,CAACiB,gBAAgB,CAAC,CAACiB,IAAI,GAAGlC,KAAK,CAACmC,aAAa;MAC/D5B,OAAO,CAACM,KAAK,CAACb,KAAK,CAACc,cAAc,CAAC;MACnCP,OAAO,CAACQ,OAAO,CAACJ,IAAI,CAAC;MACrBJ,OAAO,CAACS,IAAI,CAAChB,KAAK,CAACc,cAAc,CAAC;MAClCP,OAAO,CAACS,IAAI,CAAChB,KAAK,CAACG,QAAQ,CAAC;MAC5B,OAAOF,EAAE;IACX;IAEA,OAAOmC,UAAU,CAACzB,IAAI,CAAC;EACzB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASyB,UAAUA,CAACzB,IAAI,EAAE;IACxB;IACA,IACE,CAACA,IAAI,KAAKb,KAAK,CAACwB,IAAI,IAAI3B,iBAAiB,CAACgB,IAAI,CAAC,KAC/CF,IAAI,EAAE,GAAGV,SAAS,CAACsC,qBAAqB,EACxC;MACA,MAAMC,IAAI,GAAG3B,IAAI,KAAKb,KAAK,CAACwB,IAAI,GAAGc,UAAU,GAAGH,UAAU;MAC1D1B,OAAO,CAACQ,OAAO,CAACJ,IAAI,CAAC;MACrB,OAAO2B,IAAI;IACb;IAEA,OAAO9B,GAAG,CAACG,IAAI,CAAC;EAClB;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}