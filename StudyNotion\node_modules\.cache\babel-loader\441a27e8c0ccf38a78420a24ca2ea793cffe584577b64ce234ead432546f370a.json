{"ast": null, "code": "export default function updateSlidesProgress() {\n  let translate = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : this && this.translate || 0;\n  const swiper = this;\n  const params = swiper.params;\n  const {\n    slides,\n    rtlTranslate: rtl,\n    snapGrid\n  } = swiper;\n  if (slides.length === 0) return;\n  if (typeof slides[0].swiperSlideOffset === 'undefined') swiper.updateSlidesOffset();\n  let offsetCenter = -translate;\n  if (rtl) offsetCenter = translate;\n\n  // Visible Slides\n  slides.forEach(slideEl => {\n    slideEl.classList.remove(params.slideVisibleClass);\n  });\n  swiper.visibleSlidesIndexes = [];\n  swiper.visibleSlides = [];\n  let spaceBetween = params.spaceBetween;\n  if (typeof spaceBetween === 'string' && spaceBetween.indexOf('%') >= 0) {\n    spaceBetween = parseFloat(spaceBetween.replace('%', '')) / 100 * swiper.size;\n  } else if (typeof spaceBetween === 'string') {\n    spaceBetween = parseFloat(spaceBetween);\n  }\n  for (let i = 0; i < slides.length; i += 1) {\n    const slide = slides[i];\n    let slideOffset = slide.swiperSlideOffset;\n    if (params.cssMode && params.centeredSlides) {\n      slideOffset -= slides[0].swiperSlideOffset;\n    }\n    const slideProgress = (offsetCenter + (params.centeredSlides ? swiper.minTranslate() : 0) - slideOffset) / (slide.swiperSlideSize + spaceBetween);\n    const originalSlideProgress = (offsetCenter - snapGrid[0] + (params.centeredSlides ? swiper.minTranslate() : 0) - slideOffset) / (slide.swiperSlideSize + spaceBetween);\n    const slideBefore = -(offsetCenter - slideOffset);\n    const slideAfter = slideBefore + swiper.slidesSizesGrid[i];\n    const isVisible = slideBefore >= 0 && slideBefore < swiper.size - 1 || slideAfter > 1 && slideAfter <= swiper.size || slideBefore <= 0 && slideAfter >= swiper.size;\n    if (isVisible) {\n      swiper.visibleSlides.push(slide);\n      swiper.visibleSlidesIndexes.push(i);\n      slides[i].classList.add(params.slideVisibleClass);\n    }\n    slide.progress = rtl ? -slideProgress : slideProgress;\n    slide.originalProgress = rtl ? -originalSlideProgress : originalSlideProgress;\n  }\n}", "map": {"version": 3, "names": ["updateSlidesProgress", "translate", "arguments", "length", "undefined", "swiper", "params", "slides", "rtlTranslate", "rtl", "snapGrid", "swiperSlideOffset", "updateSlidesOffset", "offsetCenter", "for<PERSON>ach", "slideEl", "classList", "remove", "slideVisibleClass", "visibleSlidesIndexes", "visibleSlides", "spaceBetween", "indexOf", "parseFloat", "replace", "size", "i", "slide", "slideOffset", "cssMode", "centeredSlides", "slideProgress", "minTranslate", "swiperSlideSize", "originalSlideProgress", "slideBefore", "slideAfter", "slidesSizesGrid", "isVisible", "push", "add", "progress", "originalProgress"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/core/update/updateSlidesProgress.js"], "sourcesContent": ["export default function updateSlidesProgress(translate = this && this.translate || 0) {\n  const swiper = this;\n  const params = swiper.params;\n  const {\n    slides,\n    rtlTranslate: rtl,\n    snapGrid\n  } = swiper;\n  if (slides.length === 0) return;\n  if (typeof slides[0].swiperSlideOffset === 'undefined') swiper.updateSlidesOffset();\n  let offsetCenter = -translate;\n  if (rtl) offsetCenter = translate;\n\n  // Visible Slides\n  slides.forEach(slideEl => {\n    slideEl.classList.remove(params.slideVisibleClass);\n  });\n  swiper.visibleSlidesIndexes = [];\n  swiper.visibleSlides = [];\n  let spaceBetween = params.spaceBetween;\n  if (typeof spaceBetween === 'string' && spaceBetween.indexOf('%') >= 0) {\n    spaceBetween = parseFloat(spaceBetween.replace('%', '')) / 100 * swiper.size;\n  } else if (typeof spaceBetween === 'string') {\n    spaceBetween = parseFloat(spaceBetween);\n  }\n  for (let i = 0; i < slides.length; i += 1) {\n    const slide = slides[i];\n    let slideOffset = slide.swiperSlideOffset;\n    if (params.cssMode && params.centeredSlides) {\n      slideOffset -= slides[0].swiperSlideOffset;\n    }\n    const slideProgress = (offsetCenter + (params.centeredSlides ? swiper.minTranslate() : 0) - slideOffset) / (slide.swiperSlideSize + spaceBetween);\n    const originalSlideProgress = (offsetCenter - snapGrid[0] + (params.centeredSlides ? swiper.minTranslate() : 0) - slideOffset) / (slide.swiperSlideSize + spaceBetween);\n    const slideBefore = -(offsetCenter - slideOffset);\n    const slideAfter = slideBefore + swiper.slidesSizesGrid[i];\n    const isVisible = slideBefore >= 0 && slideBefore < swiper.size - 1 || slideAfter > 1 && slideAfter <= swiper.size || slideBefore <= 0 && slideAfter >= swiper.size;\n    if (isVisible) {\n      swiper.visibleSlides.push(slide);\n      swiper.visibleSlidesIndexes.push(i);\n      slides[i].classList.add(params.slideVisibleClass);\n    }\n    slide.progress = rtl ? -slideProgress : slideProgress;\n    slide.originalProgress = rtl ? -originalSlideProgress : originalSlideProgress;\n  }\n}"], "mappings": "AAAA,eAAe,SAASA,oBAAoBA,CAAA,EAA0C;EAAA,IAAzCC,SAAS,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI,IAAI,IAAI,CAACD,SAAS,IAAI,CAAC;EAClF,MAAMI,MAAM,GAAG,IAAI;EACnB,MAAMC,MAAM,GAAGD,MAAM,CAACC,MAAM;EAC5B,MAAM;IACJC,MAAM;IACNC,YAAY,EAAEC,GAAG;IACjBC;EACF,CAAC,GAAGL,MAAM;EACV,IAAIE,MAAM,CAACJ,MAAM,KAAK,CAAC,EAAE;EACzB,IAAI,OAAOI,MAAM,CAAC,CAAC,CAAC,CAACI,iBAAiB,KAAK,WAAW,EAAEN,MAAM,CAACO,kBAAkB,EAAE;EACnF,IAAIC,YAAY,GAAG,CAACZ,SAAS;EAC7B,IAAIQ,GAAG,EAAEI,YAAY,GAAGZ,SAAS;;EAEjC;EACAM,MAAM,CAACO,OAAO,CAACC,OAAO,IAAI;IACxBA,OAAO,CAACC,SAAS,CAACC,MAAM,CAACX,MAAM,CAACY,iBAAiB,CAAC;EACpD,CAAC,CAAC;EACFb,MAAM,CAACc,oBAAoB,GAAG,EAAE;EAChCd,MAAM,CAACe,aAAa,GAAG,EAAE;EACzB,IAAIC,YAAY,GAAGf,MAAM,CAACe,YAAY;EACtC,IAAI,OAAOA,YAAY,KAAK,QAAQ,IAAIA,YAAY,CAACC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;IACtED,YAAY,GAAGE,UAAU,CAACF,YAAY,CAACG,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,GAAG,GAAGnB,MAAM,CAACoB,IAAI;EAC9E,CAAC,MAAM,IAAI,OAAOJ,YAAY,KAAK,QAAQ,EAAE;IAC3CA,YAAY,GAAGE,UAAU,CAACF,YAAY,CAAC;EACzC;EACA,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnB,MAAM,CAACJ,MAAM,EAAEuB,CAAC,IAAI,CAAC,EAAE;IACzC,MAAMC,KAAK,GAAGpB,MAAM,CAACmB,CAAC,CAAC;IACvB,IAAIE,WAAW,GAAGD,KAAK,CAAChB,iBAAiB;IACzC,IAAIL,MAAM,CAACuB,OAAO,IAAIvB,MAAM,CAACwB,cAAc,EAAE;MAC3CF,WAAW,IAAIrB,MAAM,CAAC,CAAC,CAAC,CAACI,iBAAiB;IAC5C;IACA,MAAMoB,aAAa,GAAG,CAAClB,YAAY,IAAIP,MAAM,CAACwB,cAAc,GAAGzB,MAAM,CAAC2B,YAAY,EAAE,GAAG,CAAC,CAAC,GAAGJ,WAAW,KAAKD,KAAK,CAACM,eAAe,GAAGZ,YAAY,CAAC;IACjJ,MAAMa,qBAAqB,GAAG,CAACrB,YAAY,GAAGH,QAAQ,CAAC,CAAC,CAAC,IAAIJ,MAAM,CAACwB,cAAc,GAAGzB,MAAM,CAAC2B,YAAY,EAAE,GAAG,CAAC,CAAC,GAAGJ,WAAW,KAAKD,KAAK,CAACM,eAAe,GAAGZ,YAAY,CAAC;IACvK,MAAMc,WAAW,GAAG,EAAEtB,YAAY,GAAGe,WAAW,CAAC;IACjD,MAAMQ,UAAU,GAAGD,WAAW,GAAG9B,MAAM,CAACgC,eAAe,CAACX,CAAC,CAAC;IAC1D,MAAMY,SAAS,GAAGH,WAAW,IAAI,CAAC,IAAIA,WAAW,GAAG9B,MAAM,CAACoB,IAAI,GAAG,CAAC,IAAIW,UAAU,GAAG,CAAC,IAAIA,UAAU,IAAI/B,MAAM,CAACoB,IAAI,IAAIU,WAAW,IAAI,CAAC,IAAIC,UAAU,IAAI/B,MAAM,CAACoB,IAAI;IACnK,IAAIa,SAAS,EAAE;MACbjC,MAAM,CAACe,aAAa,CAACmB,IAAI,CAACZ,KAAK,CAAC;MAChCtB,MAAM,CAACc,oBAAoB,CAACoB,IAAI,CAACb,CAAC,CAAC;MACnCnB,MAAM,CAACmB,CAAC,CAAC,CAACV,SAAS,CAACwB,GAAG,CAAClC,MAAM,CAACY,iBAAiB,CAAC;IACnD;IACAS,KAAK,CAACc,QAAQ,GAAGhC,GAAG,GAAG,CAACsB,aAAa,GAAGA,aAAa;IACrDJ,KAAK,CAACe,gBAAgB,GAAGjC,GAAG,GAAG,CAACyB,qBAAqB,GAAGA,qBAAqB;EAC/E;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}