{"ast": null, "code": "export default function updateProgress(translate) {\n  const swiper = this;\n  if (typeof translate === 'undefined') {\n    const multiplier = swiper.rtlTranslate ? -1 : 1;\n    // eslint-disable-next-line\n    translate = swiper && swiper.translate && swiper.translate * multiplier || 0;\n  }\n  const params = swiper.params;\n  const translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n  let {\n    progress,\n    isBeginning,\n    isEnd,\n    progressLoop\n  } = swiper;\n  const wasBeginning = isBeginning;\n  const wasEnd = isEnd;\n  if (translatesDiff === 0) {\n    progress = 0;\n    isBeginning = true;\n    isEnd = true;\n  } else {\n    progress = (translate - swiper.minTranslate()) / translatesDiff;\n    const isBeginningRounded = Math.abs(translate - swiper.minTranslate()) < 1;\n    const isEndRounded = Math.abs(translate - swiper.maxTranslate()) < 1;\n    isBeginning = isBeginningRounded || progress <= 0;\n    isEnd = isEndRounded || progress >= 1;\n    if (isBeginningRounded) progress = 0;\n    if (isEndRounded) progress = 1;\n  }\n  if (params.loop) {\n    const firstSlideIndex = swiper.getSlideIndexByData(0);\n    const lastSlideIndex = swiper.getSlideIndexByData(swiper.slides.length - 1);\n    const firstSlideTranslate = swiper.slidesGrid[firstSlideIndex];\n    const lastSlideTranslate = swiper.slidesGrid[lastSlideIndex];\n    const translateMax = swiper.slidesGrid[swiper.slidesGrid.length - 1];\n    const translateAbs = Math.abs(translate);\n    if (translateAbs >= firstSlideTranslate) {\n      progressLoop = (translateAbs - firstSlideTranslate) / translateMax;\n    } else {\n      progressLoop = (translateAbs + translateMax - lastSlideTranslate) / translateMax;\n    }\n    if (progressLoop > 1) progressLoop -= 1;\n  }\n  Object.assign(swiper, {\n    progress,\n    progressLoop,\n    isBeginning,\n    isEnd\n  });\n  if (params.watchSlidesProgress || params.centeredSlides && params.autoHeight) swiper.updateSlidesProgress(translate);\n  if (isBeginning && !wasBeginning) {\n    swiper.emit('reachBeginning toEdge');\n  }\n  if (isEnd && !wasEnd) {\n    swiper.emit('reachEnd toEdge');\n  }\n  if (wasBeginning && !isBeginning || wasEnd && !isEnd) {\n    swiper.emit('fromEdge');\n  }\n  swiper.emit('progress', progress);\n}", "map": {"version": 3, "names": ["updateProgress", "translate", "swiper", "multiplier", "rtlTranslate", "params", "translatesDiff", "maxTranslate", "minTranslate", "progress", "isBeginning", "isEnd", "progressLoop", "wasBeginning", "wasEnd", "isBeginningRounded", "Math", "abs", "isEndRounded", "loop", "firstSlideIndex", "getSlideIndexByData", "lastSlideIndex", "slides", "length", "firstSlideTranslate", "slidesGrid", "lastSlideTranslate", "translateMax", "translateAbs", "Object", "assign", "watchSlidesProgress", "centeredSlides", "autoHeight", "updateSlidesProgress", "emit"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/core/update/updateProgress.js"], "sourcesContent": ["export default function updateProgress(translate) {\n  const swiper = this;\n  if (typeof translate === 'undefined') {\n    const multiplier = swiper.rtlTranslate ? -1 : 1;\n    // eslint-disable-next-line\n    translate = swiper && swiper.translate && swiper.translate * multiplier || 0;\n  }\n  const params = swiper.params;\n  const translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n  let {\n    progress,\n    isBeginning,\n    isEnd,\n    progressLoop\n  } = swiper;\n  const wasBeginning = isBeginning;\n  const wasEnd = isEnd;\n  if (translatesDiff === 0) {\n    progress = 0;\n    isBeginning = true;\n    isEnd = true;\n  } else {\n    progress = (translate - swiper.minTranslate()) / translatesDiff;\n    const isBeginningRounded = Math.abs(translate - swiper.minTranslate()) < 1;\n    const isEndRounded = Math.abs(translate - swiper.maxTranslate()) < 1;\n    isBeginning = isBeginningRounded || progress <= 0;\n    isEnd = isEndRounded || progress >= 1;\n    if (isBeginningRounded) progress = 0;\n    if (isEndRounded) progress = 1;\n  }\n  if (params.loop) {\n    const firstSlideIndex = swiper.getSlideIndexByData(0);\n    const lastSlideIndex = swiper.getSlideIndexByData(swiper.slides.length - 1);\n    const firstSlideTranslate = swiper.slidesGrid[firstSlideIndex];\n    const lastSlideTranslate = swiper.slidesGrid[lastSlideIndex];\n    const translateMax = swiper.slidesGrid[swiper.slidesGrid.length - 1];\n    const translateAbs = Math.abs(translate);\n    if (translateAbs >= firstSlideTranslate) {\n      progressLoop = (translateAbs - firstSlideTranslate) / translateMax;\n    } else {\n      progressLoop = (translateAbs + translateMax - lastSlideTranslate) / translateMax;\n    }\n    if (progressLoop > 1) progressLoop -= 1;\n  }\n  Object.assign(swiper, {\n    progress,\n    progressLoop,\n    isBeginning,\n    isEnd\n  });\n  if (params.watchSlidesProgress || params.centeredSlides && params.autoHeight) swiper.updateSlidesProgress(translate);\n  if (isBeginning && !wasBeginning) {\n    swiper.emit('reachBeginning toEdge');\n  }\n  if (isEnd && !wasEnd) {\n    swiper.emit('reachEnd toEdge');\n  }\n  if (wasBeginning && !isBeginning || wasEnd && !isEnd) {\n    swiper.emit('fromEdge');\n  }\n  swiper.emit('progress', progress);\n}"], "mappings": "AAAA,eAAe,SAASA,cAAcA,CAACC,SAAS,EAAE;EAChD,MAAMC,MAAM,GAAG,IAAI;EACnB,IAAI,OAAOD,SAAS,KAAK,WAAW,EAAE;IACpC,MAAME,UAAU,GAAGD,MAAM,CAACE,YAAY,GAAG,CAAC,CAAC,GAAG,CAAC;IAC/C;IACAH,SAAS,GAAGC,MAAM,IAAIA,MAAM,CAACD,SAAS,IAAIC,MAAM,CAACD,SAAS,GAAGE,UAAU,IAAI,CAAC;EAC9E;EACA,MAAME,MAAM,GAAGH,MAAM,CAACG,MAAM;EAC5B,MAAMC,cAAc,GAAGJ,MAAM,CAACK,YAAY,EAAE,GAAGL,MAAM,CAACM,YAAY,EAAE;EACpE,IAAI;IACFC,QAAQ;IACRC,WAAW;IACXC,KAAK;IACLC;EACF,CAAC,GAAGV,MAAM;EACV,MAAMW,YAAY,GAAGH,WAAW;EAChC,MAAMI,MAAM,GAAGH,KAAK;EACpB,IAAIL,cAAc,KAAK,CAAC,EAAE;IACxBG,QAAQ,GAAG,CAAC;IACZC,WAAW,GAAG,IAAI;IAClBC,KAAK,GAAG,IAAI;EACd,CAAC,MAAM;IACLF,QAAQ,GAAG,CAACR,SAAS,GAAGC,MAAM,CAACM,YAAY,EAAE,IAAIF,cAAc;IAC/D,MAAMS,kBAAkB,GAAGC,IAAI,CAACC,GAAG,CAAChB,SAAS,GAAGC,MAAM,CAACM,YAAY,EAAE,CAAC,GAAG,CAAC;IAC1E,MAAMU,YAAY,GAAGF,IAAI,CAACC,GAAG,CAAChB,SAAS,GAAGC,MAAM,CAACK,YAAY,EAAE,CAAC,GAAG,CAAC;IACpEG,WAAW,GAAGK,kBAAkB,IAAIN,QAAQ,IAAI,CAAC;IACjDE,KAAK,GAAGO,YAAY,IAAIT,QAAQ,IAAI,CAAC;IACrC,IAAIM,kBAAkB,EAAEN,QAAQ,GAAG,CAAC;IACpC,IAAIS,YAAY,EAAET,QAAQ,GAAG,CAAC;EAChC;EACA,IAAIJ,MAAM,CAACc,IAAI,EAAE;IACf,MAAMC,eAAe,GAAGlB,MAAM,CAACmB,mBAAmB,CAAC,CAAC,CAAC;IACrD,MAAMC,cAAc,GAAGpB,MAAM,CAACmB,mBAAmB,CAACnB,MAAM,CAACqB,MAAM,CAACC,MAAM,GAAG,CAAC,CAAC;IAC3E,MAAMC,mBAAmB,GAAGvB,MAAM,CAACwB,UAAU,CAACN,eAAe,CAAC;IAC9D,MAAMO,kBAAkB,GAAGzB,MAAM,CAACwB,UAAU,CAACJ,cAAc,CAAC;IAC5D,MAAMM,YAAY,GAAG1B,MAAM,CAACwB,UAAU,CAACxB,MAAM,CAACwB,UAAU,CAACF,MAAM,GAAG,CAAC,CAAC;IACpE,MAAMK,YAAY,GAAGb,IAAI,CAACC,GAAG,CAAChB,SAAS,CAAC;IACxC,IAAI4B,YAAY,IAAIJ,mBAAmB,EAAE;MACvCb,YAAY,GAAG,CAACiB,YAAY,GAAGJ,mBAAmB,IAAIG,YAAY;IACpE,CAAC,MAAM;MACLhB,YAAY,GAAG,CAACiB,YAAY,GAAGD,YAAY,GAAGD,kBAAkB,IAAIC,YAAY;IAClF;IACA,IAAIhB,YAAY,GAAG,CAAC,EAAEA,YAAY,IAAI,CAAC;EACzC;EACAkB,MAAM,CAACC,MAAM,CAAC7B,MAAM,EAAE;IACpBO,QAAQ;IACRG,YAAY;IACZF,WAAW;IACXC;EACF,CAAC,CAAC;EACF,IAAIN,MAAM,CAAC2B,mBAAmB,IAAI3B,MAAM,CAAC4B,cAAc,IAAI5B,MAAM,CAAC6B,UAAU,EAAEhC,MAAM,CAACiC,oBAAoB,CAAClC,SAAS,CAAC;EACpH,IAAIS,WAAW,IAAI,CAACG,YAAY,EAAE;IAChCX,MAAM,CAACkC,IAAI,CAAC,uBAAuB,CAAC;EACtC;EACA,IAAIzB,KAAK,IAAI,CAACG,MAAM,EAAE;IACpBZ,MAAM,CAACkC,IAAI,CAAC,iBAAiB,CAAC;EAChC;EACA,IAAIvB,YAAY,IAAI,CAACH,WAAW,IAAII,MAAM,IAAI,CAACH,KAAK,EAAE;IACpDT,MAAM,CAACkC,IAAI,CAAC,UAAU,CAAC;EACzB;EACAlC,MAAM,CAACkC,IAAI,CAAC,UAAU,EAAE3B,QAAQ,CAAC;AACnC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}