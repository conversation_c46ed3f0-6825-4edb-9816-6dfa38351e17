{"ast": null, "code": "function _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport React, { useRef, useState, forwardRef } from 'react';\nimport { uniqueClasses } from '../components-shared/utils.js';\nimport { useIsomorphicLayoutEffect } from './use-isomorphic-layout-effect.js';\nimport { SwiperSlideContext } from './context.js';\nconst SwiperSlide = /*#__PURE__*/forwardRef(function (_temp, externalRef) {\n  let {\n    tag: Tag = 'div',\n    children,\n    className = '',\n    swiper,\n    zoom,\n    lazy,\n    virtualIndex,\n    swiperSlideIndex,\n    ...rest\n  } = _temp === void 0 ? {} : _temp;\n  const slideElRef = useRef(null);\n  const [slideClasses, setSlideClasses] = useState('swiper-slide');\n  const [lazyLoaded, setLazyLoaded] = useState(false);\n  function updateClasses(_s, el, classNames) {\n    if (el === slideElRef.current) {\n      setSlideClasses(classNames);\n    }\n  }\n  useIsomorphicLayoutEffect(() => {\n    if (typeof swiperSlideIndex !== 'undefined') {\n      slideElRef.current.swiperSlideIndex = swiperSlideIndex;\n    }\n    if (externalRef) {\n      externalRef.current = slideElRef.current;\n    }\n    if (!slideElRef.current || !swiper) {\n      return;\n    }\n    if (swiper.destroyed) {\n      if (slideClasses !== 'swiper-slide') {\n        setSlideClasses('swiper-slide');\n      }\n      return;\n    }\n    swiper.on('_slideClass', updateClasses);\n    // eslint-disable-next-line\n    return () => {\n      if (!swiper) return;\n      swiper.off('_slideClass', updateClasses);\n    };\n  });\n  useIsomorphicLayoutEffect(() => {\n    if (swiper && slideElRef.current && !swiper.destroyed) {\n      setSlideClasses(swiper.getSlideClasses(slideElRef.current));\n    }\n  }, [swiper]);\n  const slideData = {\n    isActive: slideClasses.indexOf('swiper-slide-active') >= 0,\n    isVisible: slideClasses.indexOf('swiper-slide-visible') >= 0,\n    isPrev: slideClasses.indexOf('swiper-slide-prev') >= 0,\n    isNext: slideClasses.indexOf('swiper-slide-next') >= 0\n  };\n  const renderChildren = () => {\n    return typeof children === 'function' ? children(slideData) : children;\n  };\n  const onLoad = () => {\n    setLazyLoaded(true);\n  };\n  return /*#__PURE__*/React.createElement(Tag, _extends({\n    ref: slideElRef,\n    className: uniqueClasses(`${slideClasses}${className ? ` ${className}` : ''}`),\n    \"data-swiper-slide-index\": virtualIndex,\n    onLoad: onLoad\n  }, rest), zoom && /*#__PURE__*/React.createElement(SwiperSlideContext.Provider, {\n    value: slideData\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"swiper-zoom-container\",\n    \"data-swiper-zoom\": typeof zoom === 'number' ? zoom : undefined\n  }, renderChildren(), lazy && !lazyLoaded && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"swiper-lazy-preloader\"\n  }))), !zoom && /*#__PURE__*/React.createElement(SwiperSlideContext.Provider, {\n    value: slideData\n  }, renderChildren(), lazy && !lazyLoaded && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"swiper-lazy-preloader\"\n  })));\n});\nSwiperSlide.displayName = 'SwiperSlide';\nexport { SwiperSlide };", "map": {"version": 3, "names": ["_extends", "Object", "assign", "bind", "target", "i", "arguments", "length", "source", "key", "prototype", "hasOwnProperty", "call", "apply", "React", "useRef", "useState", "forwardRef", "uniqueClasses", "useIsomorphicLayoutEffect", "SwiperSlideContext", "SwiperSlide", "_temp", "externalRef", "tag", "Tag", "children", "className", "swiper", "zoom", "lazy", "virtualIndex", "swiperSlideIndex", "rest", "slideElRef", "slideClasses", "setSlideClasses", "lazyLoaded", "setLazyLoaded", "updateClasses", "_s", "el", "classNames", "current", "destroyed", "on", "off", "getSlideClasses", "slideData", "isActive", "indexOf", "isVisible", "isPrev", "isNext", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onLoad", "createElement", "ref", "Provider", "value", "undefined", "displayName"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/react/swiper-slide.js"], "sourcesContent": ["function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\nimport React, { useRef, useState, forwardRef } from 'react';\nimport { uniqueClasses } from '../components-shared/utils.js';\nimport { useIsomorphicLayoutEffect } from './use-isomorphic-layout-effect.js';\nimport { SwiperSlideContext } from './context.js';\nconst SwiperSlide = /*#__PURE__*/forwardRef(function (_temp, externalRef) {\n  let {\n    tag: Tag = 'div',\n    children,\n    className = '',\n    swiper,\n    zoom,\n    lazy,\n    virtualIndex,\n    swiperSlideIndex,\n    ...rest\n  } = _temp === void 0 ? {} : _temp;\n  const slideElRef = useRef(null);\n  const [slideClasses, setSlideClasses] = useState('swiper-slide');\n  const [lazyLoaded, setLazyLoaded] = useState(false);\n  function updateClasses(_s, el, classNames) {\n    if (el === slideElRef.current) {\n      setSlideClasses(classNames);\n    }\n  }\n  useIsomorphicLayoutEffect(() => {\n    if (typeof swiperSlideIndex !== 'undefined') {\n      slideElRef.current.swiperSlideIndex = swiperSlideIndex;\n    }\n    if (externalRef) {\n      externalRef.current = slideElRef.current;\n    }\n    if (!slideElRef.current || !swiper) {\n      return;\n    }\n    if (swiper.destroyed) {\n      if (slideClasses !== 'swiper-slide') {\n        setSlideClasses('swiper-slide');\n      }\n      return;\n    }\n    swiper.on('_slideClass', updateClasses);\n    // eslint-disable-next-line\n    return () => {\n      if (!swiper) return;\n      swiper.off('_slideClass', updateClasses);\n    };\n  });\n  useIsomorphicLayoutEffect(() => {\n    if (swiper && slideElRef.current && !swiper.destroyed) {\n      setSlideClasses(swiper.getSlideClasses(slideElRef.current));\n    }\n  }, [swiper]);\n  const slideData = {\n    isActive: slideClasses.indexOf('swiper-slide-active') >= 0,\n    isVisible: slideClasses.indexOf('swiper-slide-visible') >= 0,\n    isPrev: slideClasses.indexOf('swiper-slide-prev') >= 0,\n    isNext: slideClasses.indexOf('swiper-slide-next') >= 0\n  };\n  const renderChildren = () => {\n    return typeof children === 'function' ? children(slideData) : children;\n  };\n  const onLoad = () => {\n    setLazyLoaded(true);\n  };\n  return /*#__PURE__*/React.createElement(Tag, _extends({\n    ref: slideElRef,\n    className: uniqueClasses(`${slideClasses}${className ? ` ${className}` : ''}`),\n    \"data-swiper-slide-index\": virtualIndex,\n    onLoad: onLoad\n  }, rest), zoom && /*#__PURE__*/React.createElement(SwiperSlideContext.Provider, {\n    value: slideData\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"swiper-zoom-container\",\n    \"data-swiper-zoom\": typeof zoom === 'number' ? zoom : undefined\n  }, renderChildren(), lazy && !lazyLoaded && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"swiper-lazy-preloader\"\n  }))), !zoom && /*#__PURE__*/React.createElement(SwiperSlideContext.Provider, {\n    value: slideData\n  }, renderChildren(), lazy && !lazyLoaded && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"swiper-lazy-preloader\"\n  })));\n});\nSwiperSlide.displayName = 'SwiperSlide';\nexport { SwiperSlide };"], "mappings": "AAAA,SAASA,QAAQA,CAAA,EAAG;EAAEA,QAAQ,GAAGC,MAAM,CAACC,MAAM,GAAGD,MAAM,CAACC,MAAM,CAACC,IAAI,EAAE,GAAG,UAAUC,MAAM,EAAE;IAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MAAE,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC;MAAE,KAAK,IAAII,GAAG,IAAID,MAAM,EAAE;QAAE,IAAIP,MAAM,CAACS,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,MAAM,EAAEC,GAAG,CAAC,EAAE;UAAEL,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;QAAE;MAAE;IAAE;IAAE,OAAOL,MAAM;EAAE,CAAC;EAAE,OAAOJ,QAAQ,CAACa,KAAK,CAAC,IAAI,EAAEP,SAAS,CAAC;AAAE;AAClV,OAAOQ,KAAK,IAAIC,MAAM,EAAEC,QAAQ,EAAEC,UAAU,QAAQ,OAAO;AAC3D,SAASC,aAAa,QAAQ,+BAA+B;AAC7D,SAASC,yBAAyB,QAAQ,mCAAmC;AAC7E,SAASC,kBAAkB,QAAQ,cAAc;AACjD,MAAMC,WAAW,GAAG,aAAaJ,UAAU,CAAC,UAAUK,KAAK,EAAEC,WAAW,EAAE;EACxE,IAAI;IACFC,GAAG,EAAEC,GAAG,GAAG,KAAK;IAChBC,QAAQ;IACRC,SAAS,GAAG,EAAE;IACdC,MAAM;IACNC,IAAI;IACJC,IAAI;IACJC,YAAY;IACZC,gBAAgB;IAChB,GAAGC;EACL,CAAC,GAAGX,KAAK,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,KAAK;EACjC,MAAMY,UAAU,GAAGnB,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAM,CAACoB,YAAY,EAAEC,eAAe,CAAC,GAAGpB,QAAQ,CAAC,cAAc,CAAC;EAChE,MAAM,CAACqB,UAAU,EAAEC,aAAa,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EACnD,SAASuB,aAAaA,CAACC,EAAE,EAAEC,EAAE,EAAEC,UAAU,EAAE;IACzC,IAAID,EAAE,KAAKP,UAAU,CAACS,OAAO,EAAE;MAC7BP,eAAe,CAACM,UAAU,CAAC;IAC7B;EACF;EACAvB,yBAAyB,CAAC,MAAM;IAC9B,IAAI,OAAOa,gBAAgB,KAAK,WAAW,EAAE;MAC3CE,UAAU,CAACS,OAAO,CAACX,gBAAgB,GAAGA,gBAAgB;IACxD;IACA,IAAIT,WAAW,EAAE;MACfA,WAAW,CAACoB,OAAO,GAAGT,UAAU,CAACS,OAAO;IAC1C;IACA,IAAI,CAACT,UAAU,CAACS,OAAO,IAAI,CAACf,MAAM,EAAE;MAClC;IACF;IACA,IAAIA,MAAM,CAACgB,SAAS,EAAE;MACpB,IAAIT,YAAY,KAAK,cAAc,EAAE;QACnCC,eAAe,CAAC,cAAc,CAAC;MACjC;MACA;IACF;IACAR,MAAM,CAACiB,EAAE,CAAC,aAAa,EAAEN,aAAa,CAAC;IACvC;IACA,OAAO,MAAM;MACX,IAAI,CAACX,MAAM,EAAE;MACbA,MAAM,CAACkB,GAAG,CAAC,aAAa,EAAEP,aAAa,CAAC;IAC1C,CAAC;EACH,CAAC,CAAC;EACFpB,yBAAyB,CAAC,MAAM;IAC9B,IAAIS,MAAM,IAAIM,UAAU,CAACS,OAAO,IAAI,CAACf,MAAM,CAACgB,SAAS,EAAE;MACrDR,eAAe,CAACR,MAAM,CAACmB,eAAe,CAACb,UAAU,CAACS,OAAO,CAAC,CAAC;IAC7D;EACF,CAAC,EAAE,CAACf,MAAM,CAAC,CAAC;EACZ,MAAMoB,SAAS,GAAG;IAChBC,QAAQ,EAAEd,YAAY,CAACe,OAAO,CAAC,qBAAqB,CAAC,IAAI,CAAC;IAC1DC,SAAS,EAAEhB,YAAY,CAACe,OAAO,CAAC,sBAAsB,CAAC,IAAI,CAAC;IAC5DE,MAAM,EAAEjB,YAAY,CAACe,OAAO,CAAC,mBAAmB,CAAC,IAAI,CAAC;IACtDG,MAAM,EAAElB,YAAY,CAACe,OAAO,CAAC,mBAAmB,CAAC,IAAI;EACvD,CAAC;EACD,MAAMI,cAAc,GAAGA,CAAA,KAAM;IAC3B,OAAO,OAAO5B,QAAQ,KAAK,UAAU,GAAGA,QAAQ,CAACsB,SAAS,CAAC,GAAGtB,QAAQ;EACxE,CAAC;EACD,MAAM6B,MAAM,GAAGA,CAAA,KAAM;IACnBjB,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EACD,OAAO,aAAaxB,KAAK,CAAC0C,aAAa,CAAC/B,GAAG,EAAEzB,QAAQ,CAAC;IACpDyD,GAAG,EAAEvB,UAAU;IACfP,SAAS,EAAET,aAAa,CAAE,GAAEiB,YAAa,GAAER,SAAS,GAAI,IAAGA,SAAU,EAAC,GAAG,EAAG,EAAC,CAAC;IAC9E,yBAAyB,EAAEI,YAAY;IACvCwB,MAAM,EAAEA;EACV,CAAC,EAAEtB,IAAI,CAAC,EAAEJ,IAAI,IAAI,aAAaf,KAAK,CAAC0C,aAAa,CAACpC,kBAAkB,CAACsC,QAAQ,EAAE;IAC9EC,KAAK,EAAEX;EACT,CAAC,EAAE,aAAalC,KAAK,CAAC0C,aAAa,CAAC,KAAK,EAAE;IACzC7B,SAAS,EAAE,uBAAuB;IAClC,kBAAkB,EAAE,OAAOE,IAAI,KAAK,QAAQ,GAAGA,IAAI,GAAG+B;EACxD,CAAC,EAAEN,cAAc,EAAE,EAAExB,IAAI,IAAI,CAACO,UAAU,IAAI,aAAavB,KAAK,CAAC0C,aAAa,CAAC,KAAK,EAAE;IAClF7B,SAAS,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAACE,IAAI,IAAI,aAAaf,KAAK,CAAC0C,aAAa,CAACpC,kBAAkB,CAACsC,QAAQ,EAAE;IAC3EC,KAAK,EAAEX;EACT,CAAC,EAAEM,cAAc,EAAE,EAAExB,IAAI,IAAI,CAACO,UAAU,IAAI,aAAavB,KAAK,CAAC0C,aAAa,CAAC,KAAK,EAAE;IAClF7B,SAAS,EAAE;EACb,CAAC,CAAC,CAAC,CAAC;AACN,CAAC,CAAC;AACFN,WAAW,CAACwC,WAAW,GAAG,aAAa;AACvC,SAASxC,WAAW"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}