{"ast": null, "code": "import { isObject, extend } from './utils.js';\nfunction updateSwiper(_ref) {\n  let {\n    swiper,\n    slides,\n    passedParams,\n    changedParams,\n    nextEl,\n    prevEl,\n    scrollbarEl,\n    paginationEl\n  } = _ref;\n  const updateParams = changedParams.filter(key => key !== 'children' && key !== 'direction' && key !== 'wrapperClass');\n  const {\n    params: currentParams,\n    pagination,\n    navigation,\n    scrollbar,\n    virtual,\n    thumbs\n  } = swiper;\n  let needThumbsInit;\n  let needControllerInit;\n  let needPaginationInit;\n  let needScrollbarInit;\n  let needNavigationInit;\n  let loopNeedDestroy;\n  let loopNeedEnable;\n  let loopNeedReloop;\n  if (changedParams.includes('thumbs') && passedParams.thumbs && passedParams.thumbs.swiper && currentParams.thumbs && !currentParams.thumbs.swiper) {\n    needThumbsInit = true;\n  }\n  if (changedParams.includes('controller') && passedParams.controller && passedParams.controller.control && currentParams.controller && !currentParams.controller.control) {\n    needControllerInit = true;\n  }\n  if (changedParams.includes('pagination') && passedParams.pagination && (passedParams.pagination.el || paginationEl) && (currentParams.pagination || currentParams.pagination === false) && pagination && !pagination.el) {\n    needPaginationInit = true;\n  }\n  if (changedParams.includes('scrollbar') && passedParams.scrollbar && (passedParams.scrollbar.el || scrollbarEl) && (currentParams.scrollbar || currentParams.scrollbar === false) && scrollbar && !scrollbar.el) {\n    needScrollbarInit = true;\n  }\n  if (changedParams.includes('navigation') && passedParams.navigation && (passedParams.navigation.prevEl || prevEl) && (passedParams.navigation.nextEl || nextEl) && (currentParams.navigation || currentParams.navigation === false) && navigation && !navigation.prevEl && !navigation.nextEl) {\n    needNavigationInit = true;\n  }\n  const destroyModule = mod => {\n    if (!swiper[mod]) return;\n    swiper[mod].destroy();\n    if (mod === 'navigation') {\n      if (swiper.isElement) {\n        swiper[mod].prevEl.remove();\n        swiper[mod].nextEl.remove();\n      }\n      currentParams[mod].prevEl = undefined;\n      currentParams[mod].nextEl = undefined;\n      swiper[mod].prevEl = undefined;\n      swiper[mod].nextEl = undefined;\n    } else {\n      if (swiper.isElement) {\n        swiper[mod].el.remove();\n      }\n      currentParams[mod].el = undefined;\n      swiper[mod].el = undefined;\n    }\n  };\n  if (changedParams.includes('loop') && swiper.isElement) {\n    if (currentParams.loop && !passedParams.loop) {\n      loopNeedDestroy = true;\n    } else if (!currentParams.loop && passedParams.loop) {\n      loopNeedEnable = true;\n    } else {\n      loopNeedReloop = true;\n    }\n  }\n  updateParams.forEach(key => {\n    if (isObject(currentParams[key]) && isObject(passedParams[key])) {\n      extend(currentParams[key], passedParams[key]);\n      if ((key === 'navigation' || key === 'pagination' || key === 'scrollbar') && 'enabled' in passedParams[key] && !passedParams[key].enabled) {\n        destroyModule(key);\n      }\n    } else {\n      const newValue = passedParams[key];\n      if ((newValue === true || newValue === false) && (key === 'navigation' || key === 'pagination' || key === 'scrollbar')) {\n        if (newValue === false) {\n          destroyModule(key);\n        }\n      } else {\n        currentParams[key] = passedParams[key];\n      }\n    }\n  });\n  if (updateParams.includes('controller') && !needControllerInit && swiper.controller && swiper.controller.control && currentParams.controller && currentParams.controller.control) {\n    swiper.controller.control = currentParams.controller.control;\n  }\n  if (changedParams.includes('children') && slides && virtual && currentParams.virtual.enabled) {\n    virtual.slides = slides;\n    virtual.update(true);\n  }\n  if (changedParams.includes('children') && slides && currentParams.loop) {\n    loopNeedReloop = true;\n  }\n  if (needThumbsInit) {\n    const initialized = thumbs.init();\n    if (initialized) thumbs.update(true);\n  }\n  if (needControllerInit) {\n    swiper.controller.control = currentParams.controller.control;\n  }\n  if (needPaginationInit) {\n    if (swiper.isElement && (!paginationEl || typeof paginationEl === 'string')) {\n      paginationEl = document.createElement('div');\n      paginationEl.classList.add('swiper-pagination');\n      swiper.el.shadowEl.appendChild(paginationEl);\n    }\n    if (paginationEl) currentParams.pagination.el = paginationEl;\n    pagination.init();\n    pagination.render();\n    pagination.update();\n  }\n  if (needScrollbarInit) {\n    if (swiper.isElement && (!scrollbarEl || typeof scrollbarEl === 'string')) {\n      scrollbarEl = document.createElement('div');\n      scrollbarEl.classList.add('swiper-scrollbar');\n      swiper.el.shadowEl.appendChild(scrollbarEl);\n    }\n    if (scrollbarEl) currentParams.scrollbar.el = scrollbarEl;\n    scrollbar.init();\n    scrollbar.updateSize();\n    scrollbar.setTranslate();\n  }\n  if (needNavigationInit) {\n    if (swiper.isElement) {\n      if (!nextEl || typeof nextEl === 'string') {\n        nextEl = document.createElement('div');\n        nextEl.classList.add('swiper-button-next');\n        swiper.el.shadowEl.appendChild(nextEl);\n      }\n      if (!prevEl || typeof prevEl === 'string') {\n        prevEl = document.createElement('div');\n        prevEl.classList.add('swiper-button-prev');\n        swiper.el.shadowEl.appendChild(prevEl);\n      }\n    }\n    if (nextEl) currentParams.navigation.nextEl = nextEl;\n    if (prevEl) currentParams.navigation.prevEl = prevEl;\n    navigation.init();\n    navigation.update();\n  }\n  if (changedParams.includes('allowSlideNext')) {\n    swiper.allowSlideNext = passedParams.allowSlideNext;\n  }\n  if (changedParams.includes('allowSlidePrev')) {\n    swiper.allowSlidePrev = passedParams.allowSlidePrev;\n  }\n  if (changedParams.includes('direction')) {\n    swiper.changeDirection(passedParams.direction, false);\n  }\n  if (loopNeedDestroy || loopNeedReloop) {\n    swiper.loopDestroy();\n  }\n  if (loopNeedEnable || loopNeedReloop) {\n    swiper.loopCreate();\n  }\n  swiper.update();\n}\nexport { updateSwiper };", "map": {"version": 3, "names": ["isObject", "extend", "updateSwiper", "_ref", "swiper", "slides", "passedParams", "changedParams", "nextEl", "prevEl", "scrollbarEl", "paginationEl", "updateParams", "filter", "key", "params", "currentParams", "pagination", "navigation", "scrollbar", "virtual", "thumbs", "needThumbsInit", "needControllerInit", "needPaginationInit", "needScrollbarInit", "needNavigationInit", "loopNeedDestroy", "loopNeedEnable", "loopNeedReloop", "includes", "controller", "control", "el", "destroyModule", "mod", "destroy", "isElement", "remove", "undefined", "loop", "for<PERSON>ach", "enabled", "newValue", "update", "initialized", "init", "document", "createElement", "classList", "add", "shadowEl", "append<PERSON><PERSON><PERSON>", "render", "updateSize", "setTranslate", "allowSlideNext", "allowSlidePrev", "changeDirection", "direction", "loop<PERSON><PERSON><PERSON>", "loopCreate"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/components-shared/update-swiper.js"], "sourcesContent": ["import { isObject, extend } from './utils.js';\nfunction updateSwiper({\n  swiper,\n  slides,\n  passedParams,\n  changedParams,\n  nextEl,\n  prevEl,\n  scrollbarEl,\n  paginationEl\n}) {\n  const updateParams = changedParams.filter(key => key !== 'children' && key !== 'direction' && key !== 'wrapperClass');\n  const {\n    params: currentParams,\n    pagination,\n    navigation,\n    scrollbar,\n    virtual,\n    thumbs\n  } = swiper;\n  let needThumbsInit;\n  let needControllerInit;\n  let needPaginationInit;\n  let needScrollbarInit;\n  let needNavigationInit;\n  let loopNeedDestroy;\n  let loopNeedEnable;\n  let loopNeedReloop;\n  if (changedParams.includes('thumbs') && passedParams.thumbs && passedParams.thumbs.swiper && currentParams.thumbs && !currentParams.thumbs.swiper) {\n    needThumbsInit = true;\n  }\n  if (changedParams.includes('controller') && passedParams.controller && passedParams.controller.control && currentParams.controller && !currentParams.controller.control) {\n    needControllerInit = true;\n  }\n  if (changedParams.includes('pagination') && passedParams.pagination && (passedParams.pagination.el || paginationEl) && (currentParams.pagination || currentParams.pagination === false) && pagination && !pagination.el) {\n    needPaginationInit = true;\n  }\n  if (changedParams.includes('scrollbar') && passedParams.scrollbar && (passedParams.scrollbar.el || scrollbarEl) && (currentParams.scrollbar || currentParams.scrollbar === false) && scrollbar && !scrollbar.el) {\n    needScrollbarInit = true;\n  }\n  if (changedParams.includes('navigation') && passedParams.navigation && (passedParams.navigation.prevEl || prevEl) && (passedParams.navigation.nextEl || nextEl) && (currentParams.navigation || currentParams.navigation === false) && navigation && !navigation.prevEl && !navigation.nextEl) {\n    needNavigationInit = true;\n  }\n  const destroyModule = mod => {\n    if (!swiper[mod]) return;\n    swiper[mod].destroy();\n    if (mod === 'navigation') {\n      if (swiper.isElement) {\n        swiper[mod].prevEl.remove();\n        swiper[mod].nextEl.remove();\n      }\n      currentParams[mod].prevEl = undefined;\n      currentParams[mod].nextEl = undefined;\n      swiper[mod].prevEl = undefined;\n      swiper[mod].nextEl = undefined;\n    } else {\n      if (swiper.isElement) {\n        swiper[mod].el.remove();\n      }\n      currentParams[mod].el = undefined;\n      swiper[mod].el = undefined;\n    }\n  };\n  if (changedParams.includes('loop') && swiper.isElement) {\n    if (currentParams.loop && !passedParams.loop) {\n      loopNeedDestroy = true;\n    } else if (!currentParams.loop && passedParams.loop) {\n      loopNeedEnable = true;\n    } else {\n      loopNeedReloop = true;\n    }\n  }\n  updateParams.forEach(key => {\n    if (isObject(currentParams[key]) && isObject(passedParams[key])) {\n      extend(currentParams[key], passedParams[key]);\n      if ((key === 'navigation' || key === 'pagination' || key === 'scrollbar') && 'enabled' in passedParams[key] && !passedParams[key].enabled) {\n        destroyModule(key);\n      }\n    } else {\n      const newValue = passedParams[key];\n      if ((newValue === true || newValue === false) && (key === 'navigation' || key === 'pagination' || key === 'scrollbar')) {\n        if (newValue === false) {\n          destroyModule(key);\n        }\n      } else {\n        currentParams[key] = passedParams[key];\n      }\n    }\n  });\n  if (updateParams.includes('controller') && !needControllerInit && swiper.controller && swiper.controller.control && currentParams.controller && currentParams.controller.control) {\n    swiper.controller.control = currentParams.controller.control;\n  }\n  if (changedParams.includes('children') && slides && virtual && currentParams.virtual.enabled) {\n    virtual.slides = slides;\n    virtual.update(true);\n  }\n  if (changedParams.includes('children') && slides && currentParams.loop) {\n    loopNeedReloop = true;\n  }\n  if (needThumbsInit) {\n    const initialized = thumbs.init();\n    if (initialized) thumbs.update(true);\n  }\n  if (needControllerInit) {\n    swiper.controller.control = currentParams.controller.control;\n  }\n  if (needPaginationInit) {\n    if (swiper.isElement && (!paginationEl || typeof paginationEl === 'string')) {\n      paginationEl = document.createElement('div');\n      paginationEl.classList.add('swiper-pagination');\n      swiper.el.shadowEl.appendChild(paginationEl);\n    }\n    if (paginationEl) currentParams.pagination.el = paginationEl;\n    pagination.init();\n    pagination.render();\n    pagination.update();\n  }\n  if (needScrollbarInit) {\n    if (swiper.isElement && (!scrollbarEl || typeof scrollbarEl === 'string')) {\n      scrollbarEl = document.createElement('div');\n      scrollbarEl.classList.add('swiper-scrollbar');\n      swiper.el.shadowEl.appendChild(scrollbarEl);\n    }\n    if (scrollbarEl) currentParams.scrollbar.el = scrollbarEl;\n    scrollbar.init();\n    scrollbar.updateSize();\n    scrollbar.setTranslate();\n  }\n  if (needNavigationInit) {\n    if (swiper.isElement) {\n      if (!nextEl || typeof nextEl === 'string') {\n        nextEl = document.createElement('div');\n        nextEl.classList.add('swiper-button-next');\n        swiper.el.shadowEl.appendChild(nextEl);\n      }\n      if (!prevEl || typeof prevEl === 'string') {\n        prevEl = document.createElement('div');\n        prevEl.classList.add('swiper-button-prev');\n        swiper.el.shadowEl.appendChild(prevEl);\n      }\n    }\n    if (nextEl) currentParams.navigation.nextEl = nextEl;\n    if (prevEl) currentParams.navigation.prevEl = prevEl;\n    navigation.init();\n    navigation.update();\n  }\n  if (changedParams.includes('allowSlideNext')) {\n    swiper.allowSlideNext = passedParams.allowSlideNext;\n  }\n  if (changedParams.includes('allowSlidePrev')) {\n    swiper.allowSlidePrev = passedParams.allowSlidePrev;\n  }\n  if (changedParams.includes('direction')) {\n    swiper.changeDirection(passedParams.direction, false);\n  }\n  if (loopNeedDestroy || loopNeedReloop) {\n    swiper.loopDestroy();\n  }\n  if (loopNeedEnable || loopNeedReloop) {\n    swiper.loopCreate();\n  }\n  swiper.update();\n}\nexport { updateSwiper };"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,MAAM,QAAQ,YAAY;AAC7C,SAASC,YAAYA,CAAAC,IAAA,EASlB;EAAA,IATmB;IACpBC,MAAM;IACNC,MAAM;IACNC,YAAY;IACZC,aAAa;IACbC,MAAM;IACNC,MAAM;IACNC,WAAW;IACXC;EACF,CAAC,GAAAR,IAAA;EACC,MAAMS,YAAY,GAAGL,aAAa,CAACM,MAAM,CAACC,GAAG,IAAIA,GAAG,KAAK,UAAU,IAAIA,GAAG,KAAK,WAAW,IAAIA,GAAG,KAAK,cAAc,CAAC;EACrH,MAAM;IACJC,MAAM,EAAEC,aAAa;IACrBC,UAAU;IACVC,UAAU;IACVC,SAAS;IACTC,OAAO;IACPC;EACF,CAAC,GAAGjB,MAAM;EACV,IAAIkB,cAAc;EAClB,IAAIC,kBAAkB;EACtB,IAAIC,kBAAkB;EACtB,IAAIC,iBAAiB;EACrB,IAAIC,kBAAkB;EACtB,IAAIC,eAAe;EACnB,IAAIC,cAAc;EAClB,IAAIC,cAAc;EAClB,IAAItB,aAAa,CAACuB,QAAQ,CAAC,QAAQ,CAAC,IAAIxB,YAAY,CAACe,MAAM,IAAIf,YAAY,CAACe,MAAM,CAACjB,MAAM,IAAIY,aAAa,CAACK,MAAM,IAAI,CAACL,aAAa,CAACK,MAAM,CAACjB,MAAM,EAAE;IACjJkB,cAAc,GAAG,IAAI;EACvB;EACA,IAAIf,aAAa,CAACuB,QAAQ,CAAC,YAAY,CAAC,IAAIxB,YAAY,CAACyB,UAAU,IAAIzB,YAAY,CAACyB,UAAU,CAACC,OAAO,IAAIhB,aAAa,CAACe,UAAU,IAAI,CAACf,aAAa,CAACe,UAAU,CAACC,OAAO,EAAE;IACvKT,kBAAkB,GAAG,IAAI;EAC3B;EACA,IAAIhB,aAAa,CAACuB,QAAQ,CAAC,YAAY,CAAC,IAAIxB,YAAY,CAACW,UAAU,KAAKX,YAAY,CAACW,UAAU,CAACgB,EAAE,IAAItB,YAAY,CAAC,KAAKK,aAAa,CAACC,UAAU,IAAID,aAAa,CAACC,UAAU,KAAK,KAAK,CAAC,IAAIA,UAAU,IAAI,CAACA,UAAU,CAACgB,EAAE,EAAE;IACvNT,kBAAkB,GAAG,IAAI;EAC3B;EACA,IAAIjB,aAAa,CAACuB,QAAQ,CAAC,WAAW,CAAC,IAAIxB,YAAY,CAACa,SAAS,KAAKb,YAAY,CAACa,SAAS,CAACc,EAAE,IAAIvB,WAAW,CAAC,KAAKM,aAAa,CAACG,SAAS,IAAIH,aAAa,CAACG,SAAS,KAAK,KAAK,CAAC,IAAIA,SAAS,IAAI,CAACA,SAAS,CAACc,EAAE,EAAE;IAC/MR,iBAAiB,GAAG,IAAI;EAC1B;EACA,IAAIlB,aAAa,CAACuB,QAAQ,CAAC,YAAY,CAAC,IAAIxB,YAAY,CAACY,UAAU,KAAKZ,YAAY,CAACY,UAAU,CAACT,MAAM,IAAIA,MAAM,CAAC,KAAKH,YAAY,CAACY,UAAU,CAACV,MAAM,IAAIA,MAAM,CAAC,KAAKQ,aAAa,CAACE,UAAU,IAAIF,aAAa,CAACE,UAAU,KAAK,KAAK,CAAC,IAAIA,UAAU,IAAI,CAACA,UAAU,CAACT,MAAM,IAAI,CAACS,UAAU,CAACV,MAAM,EAAE;IAC7RkB,kBAAkB,GAAG,IAAI;EAC3B;EACA,MAAMQ,aAAa,GAAGC,GAAG,IAAI;IAC3B,IAAI,CAAC/B,MAAM,CAAC+B,GAAG,CAAC,EAAE;IAClB/B,MAAM,CAAC+B,GAAG,CAAC,CAACC,OAAO,EAAE;IACrB,IAAID,GAAG,KAAK,YAAY,EAAE;MACxB,IAAI/B,MAAM,CAACiC,SAAS,EAAE;QACpBjC,MAAM,CAAC+B,GAAG,CAAC,CAAC1B,MAAM,CAAC6B,MAAM,EAAE;QAC3BlC,MAAM,CAAC+B,GAAG,CAAC,CAAC3B,MAAM,CAAC8B,MAAM,EAAE;MAC7B;MACAtB,aAAa,CAACmB,GAAG,CAAC,CAAC1B,MAAM,GAAG8B,SAAS;MACrCvB,aAAa,CAACmB,GAAG,CAAC,CAAC3B,MAAM,GAAG+B,SAAS;MACrCnC,MAAM,CAAC+B,GAAG,CAAC,CAAC1B,MAAM,GAAG8B,SAAS;MAC9BnC,MAAM,CAAC+B,GAAG,CAAC,CAAC3B,MAAM,GAAG+B,SAAS;IAChC,CAAC,MAAM;MACL,IAAInC,MAAM,CAACiC,SAAS,EAAE;QACpBjC,MAAM,CAAC+B,GAAG,CAAC,CAACF,EAAE,CAACK,MAAM,EAAE;MACzB;MACAtB,aAAa,CAACmB,GAAG,CAAC,CAACF,EAAE,GAAGM,SAAS;MACjCnC,MAAM,CAAC+B,GAAG,CAAC,CAACF,EAAE,GAAGM,SAAS;IAC5B;EACF,CAAC;EACD,IAAIhC,aAAa,CAACuB,QAAQ,CAAC,MAAM,CAAC,IAAI1B,MAAM,CAACiC,SAAS,EAAE;IACtD,IAAIrB,aAAa,CAACwB,IAAI,IAAI,CAAClC,YAAY,CAACkC,IAAI,EAAE;MAC5Cb,eAAe,GAAG,IAAI;IACxB,CAAC,MAAM,IAAI,CAACX,aAAa,CAACwB,IAAI,IAAIlC,YAAY,CAACkC,IAAI,EAAE;MACnDZ,cAAc,GAAG,IAAI;IACvB,CAAC,MAAM;MACLC,cAAc,GAAG,IAAI;IACvB;EACF;EACAjB,YAAY,CAAC6B,OAAO,CAAC3B,GAAG,IAAI;IAC1B,IAAId,QAAQ,CAACgB,aAAa,CAACF,GAAG,CAAC,CAAC,IAAId,QAAQ,CAACM,YAAY,CAACQ,GAAG,CAAC,CAAC,EAAE;MAC/Db,MAAM,CAACe,aAAa,CAACF,GAAG,CAAC,EAAER,YAAY,CAACQ,GAAG,CAAC,CAAC;MAC7C,IAAI,CAACA,GAAG,KAAK,YAAY,IAAIA,GAAG,KAAK,YAAY,IAAIA,GAAG,KAAK,WAAW,KAAK,SAAS,IAAIR,YAAY,CAACQ,GAAG,CAAC,IAAI,CAACR,YAAY,CAACQ,GAAG,CAAC,CAAC4B,OAAO,EAAE;QACzIR,aAAa,CAACpB,GAAG,CAAC;MACpB;IACF,CAAC,MAAM;MACL,MAAM6B,QAAQ,GAAGrC,YAAY,CAACQ,GAAG,CAAC;MAClC,IAAI,CAAC6B,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,MAAM7B,GAAG,KAAK,YAAY,IAAIA,GAAG,KAAK,YAAY,IAAIA,GAAG,KAAK,WAAW,CAAC,EAAE;QACtH,IAAI6B,QAAQ,KAAK,KAAK,EAAE;UACtBT,aAAa,CAACpB,GAAG,CAAC;QACpB;MACF,CAAC,MAAM;QACLE,aAAa,CAACF,GAAG,CAAC,GAAGR,YAAY,CAACQ,GAAG,CAAC;MACxC;IACF;EACF,CAAC,CAAC;EACF,IAAIF,YAAY,CAACkB,QAAQ,CAAC,YAAY,CAAC,IAAI,CAACP,kBAAkB,IAAInB,MAAM,CAAC2B,UAAU,IAAI3B,MAAM,CAAC2B,UAAU,CAACC,OAAO,IAAIhB,aAAa,CAACe,UAAU,IAAIf,aAAa,CAACe,UAAU,CAACC,OAAO,EAAE;IAChL5B,MAAM,CAAC2B,UAAU,CAACC,OAAO,GAAGhB,aAAa,CAACe,UAAU,CAACC,OAAO;EAC9D;EACA,IAAIzB,aAAa,CAACuB,QAAQ,CAAC,UAAU,CAAC,IAAIzB,MAAM,IAAIe,OAAO,IAAIJ,aAAa,CAACI,OAAO,CAACsB,OAAO,EAAE;IAC5FtB,OAAO,CAACf,MAAM,GAAGA,MAAM;IACvBe,OAAO,CAACwB,MAAM,CAAC,IAAI,CAAC;EACtB;EACA,IAAIrC,aAAa,CAACuB,QAAQ,CAAC,UAAU,CAAC,IAAIzB,MAAM,IAAIW,aAAa,CAACwB,IAAI,EAAE;IACtEX,cAAc,GAAG,IAAI;EACvB;EACA,IAAIP,cAAc,EAAE;IAClB,MAAMuB,WAAW,GAAGxB,MAAM,CAACyB,IAAI,EAAE;IACjC,IAAID,WAAW,EAAExB,MAAM,CAACuB,MAAM,CAAC,IAAI,CAAC;EACtC;EACA,IAAIrB,kBAAkB,EAAE;IACtBnB,MAAM,CAAC2B,UAAU,CAACC,OAAO,GAAGhB,aAAa,CAACe,UAAU,CAACC,OAAO;EAC9D;EACA,IAAIR,kBAAkB,EAAE;IACtB,IAAIpB,MAAM,CAACiC,SAAS,KAAK,CAAC1B,YAAY,IAAI,OAAOA,YAAY,KAAK,QAAQ,CAAC,EAAE;MAC3EA,YAAY,GAAGoC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAC5CrC,YAAY,CAACsC,SAAS,CAACC,GAAG,CAAC,mBAAmB,CAAC;MAC/C9C,MAAM,CAAC6B,EAAE,CAACkB,QAAQ,CAACC,WAAW,CAACzC,YAAY,CAAC;IAC9C;IACA,IAAIA,YAAY,EAAEK,aAAa,CAACC,UAAU,CAACgB,EAAE,GAAGtB,YAAY;IAC5DM,UAAU,CAAC6B,IAAI,EAAE;IACjB7B,UAAU,CAACoC,MAAM,EAAE;IACnBpC,UAAU,CAAC2B,MAAM,EAAE;EACrB;EACA,IAAInB,iBAAiB,EAAE;IACrB,IAAIrB,MAAM,CAACiC,SAAS,KAAK,CAAC3B,WAAW,IAAI,OAAOA,WAAW,KAAK,QAAQ,CAAC,EAAE;MACzEA,WAAW,GAAGqC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAC3CtC,WAAW,CAACuC,SAAS,CAACC,GAAG,CAAC,kBAAkB,CAAC;MAC7C9C,MAAM,CAAC6B,EAAE,CAACkB,QAAQ,CAACC,WAAW,CAAC1C,WAAW,CAAC;IAC7C;IACA,IAAIA,WAAW,EAAEM,aAAa,CAACG,SAAS,CAACc,EAAE,GAAGvB,WAAW;IACzDS,SAAS,CAAC2B,IAAI,EAAE;IAChB3B,SAAS,CAACmC,UAAU,EAAE;IACtBnC,SAAS,CAACoC,YAAY,EAAE;EAC1B;EACA,IAAI7B,kBAAkB,EAAE;IACtB,IAAItB,MAAM,CAACiC,SAAS,EAAE;MACpB,IAAI,CAAC7B,MAAM,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;QACzCA,MAAM,GAAGuC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACtCxC,MAAM,CAACyC,SAAS,CAACC,GAAG,CAAC,oBAAoB,CAAC;QAC1C9C,MAAM,CAAC6B,EAAE,CAACkB,QAAQ,CAACC,WAAW,CAAC5C,MAAM,CAAC;MACxC;MACA,IAAI,CAACC,MAAM,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;QACzCA,MAAM,GAAGsC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACtCvC,MAAM,CAACwC,SAAS,CAACC,GAAG,CAAC,oBAAoB,CAAC;QAC1C9C,MAAM,CAAC6B,EAAE,CAACkB,QAAQ,CAACC,WAAW,CAAC3C,MAAM,CAAC;MACxC;IACF;IACA,IAAID,MAAM,EAAEQ,aAAa,CAACE,UAAU,CAACV,MAAM,GAAGA,MAAM;IACpD,IAAIC,MAAM,EAAEO,aAAa,CAACE,UAAU,CAACT,MAAM,GAAGA,MAAM;IACpDS,UAAU,CAAC4B,IAAI,EAAE;IACjB5B,UAAU,CAAC0B,MAAM,EAAE;EACrB;EACA,IAAIrC,aAAa,CAACuB,QAAQ,CAAC,gBAAgB,CAAC,EAAE;IAC5C1B,MAAM,CAACoD,cAAc,GAAGlD,YAAY,CAACkD,cAAc;EACrD;EACA,IAAIjD,aAAa,CAACuB,QAAQ,CAAC,gBAAgB,CAAC,EAAE;IAC5C1B,MAAM,CAACqD,cAAc,GAAGnD,YAAY,CAACmD,cAAc;EACrD;EACA,IAAIlD,aAAa,CAACuB,QAAQ,CAAC,WAAW,CAAC,EAAE;IACvC1B,MAAM,CAACsD,eAAe,CAACpD,YAAY,CAACqD,SAAS,EAAE,KAAK,CAAC;EACvD;EACA,IAAIhC,eAAe,IAAIE,cAAc,EAAE;IACrCzB,MAAM,CAACwD,WAAW,EAAE;EACtB;EACA,IAAIhC,cAAc,IAAIC,cAAc,EAAE;IACpCzB,MAAM,CAACyD,UAAU,EAAE;EACrB;EACAzD,MAAM,CAACwC,MAAM,EAAE;AACjB;AACA,SAAS1C,YAAY"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}