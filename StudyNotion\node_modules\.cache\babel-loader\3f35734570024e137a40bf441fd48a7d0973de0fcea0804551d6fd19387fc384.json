{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useEffect } from \"react\";\n\n// This hook detects clicks outside of the specified component and calls the provided handler function.\nexport default function useOnClickOutside(ref, handler) {\n  _s();\n  useEffect(() => {\n    // Define the listener function to be called on click/touch events\n    const listener = event => {\n      // If the click/touch event originated inside the ref element, do nothing\n      if (!ref.current || ref.current.contains(event.target)) {\n        return;\n      }\n      // Otherwise, call the provided handler function\n      handler(event);\n    };\n\n    // Add event listeners for mousedown and touchstart events on the document\n    document.addEventListener(\"mousedown\", listener);\n    document.addEventListener(\"touchstart\", listener);\n\n    // Cleanup function to remove the event listeners when the component unmounts or when the ref/handler dependencies change\n    return () => {\n      document.removeEventListener(\"mousedown\", listener);\n      document.removeEventListener(\"touchstart\", listener);\n    };\n  }, [ref, handler]); // Only run this effect when the ref or handler function changes\n}\n_s(useOn<PERSON>lickOutside, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");", "map": {"version": 3, "names": ["useEffect", "useOnClickOutside", "ref", "handler", "_s", "listener", "event", "current", "contains", "target", "document", "addEventListener", "removeEventListener"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/src/hooks/useOnClickOutside.js"], "sourcesContent": ["import { useEffect } from \"react\";\r\n\r\n// This hook detects clicks outside of the specified component and calls the provided handler function.\r\nexport default function useOnClickOutside(ref, handler) {\r\n  useEffect(() => {\r\n    // Define the listener function to be called on click/touch events\r\n    const listener = (event) => {\r\n      // If the click/touch event originated inside the ref element, do nothing\r\n      if (!ref.current || ref.current.contains(event.target)) {\r\n        return;\r\n      }\r\n      // Otherwise, call the provided handler function\r\n      handler(event);\r\n    };\r\n\r\n    // Add event listeners for mousedown and touchstart events on the document\r\n    document.addEventListener(\"mousedown\", listener);\r\n    document.addEventListener(\"touchstart\", listener);\r\n\r\n    // Cleanup function to remove the event listeners when the component unmounts or when the ref/handler dependencies change\r\n    return () => {\r\n      document.removeEventListener(\"mousedown\", listener);\r\n      document.removeEventListener(\"touchstart\", listener);\r\n    };\r\n  }, [ref, handler]); // Only run this effect when the ref or handler function changes\r\n}"], "mappings": ";AAAA,SAASA,SAAS,QAAQ,OAAO;;AAEjC;AACA,eAAe,SAASC,iBAAiBA,CAACC,GAAG,EAAEC,OAAO,EAAE;EAAAC,EAAA;EACtDJ,SAAS,CAAC,MAAM;IACd;IACA,MAAMK,QAAQ,GAAIC,KAAK,IAAK;MAC1B;MACA,IAAI,CAACJ,GAAG,CAACK,OAAO,IAAIL,GAAG,CAACK,OAAO,CAACC,QAAQ,CAACF,KAAK,CAACG,MAAM,CAAC,EAAE;QACtD;MACF;MACA;MACAN,OAAO,CAACG,KAAK,CAAC;IAChB,CAAC;;IAED;IACAI,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEN,QAAQ,CAAC;IAChDK,QAAQ,CAACC,gBAAgB,CAAC,YAAY,EAAEN,QAAQ,CAAC;;IAEjD;IACA,OAAO,MAAM;MACXK,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEP,QAAQ,CAAC;MACnDK,QAAQ,CAACE,mBAAmB,CAAC,YAAY,EAAEP,QAAQ,CAAC;IACtD,CAAC;EACH,CAAC,EAAE,CAACH,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,CAAC;AACtB;AAACC,EAAA,CAtBuBH,iBAAiB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}