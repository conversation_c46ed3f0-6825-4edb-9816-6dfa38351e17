{"ast": null, "code": "export default function removeClasses() {\n  const swiper = this;\n  const {\n    el,\n    classNames\n  } = swiper;\n  el.classList.remove(...classNames);\n  swiper.emitContainerClasses();\n}", "map": {"version": 3, "names": ["removeClasses", "swiper", "el", "classNames", "classList", "remove", "emitContainerClasses"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/core/classes/removeClasses.js"], "sourcesContent": ["export default function removeClasses() {\n  const swiper = this;\n  const {\n    el,\n    classNames\n  } = swiper;\n  el.classList.remove(...classNames);\n  swiper.emitContainerClasses();\n}"], "mappings": "AAAA,eAAe,SAASA,aAAaA,CAAA,EAAG;EACtC,MAAMC,MAAM,GAAG,IAAI;EACnB,MAAM;IACJC,EAAE;IACFC;EACF,CAAC,GAAGF,MAAM;EACVC,EAAE,CAACE,SAAS,CAACC,MAAM,CAAC,GAAGF,UAAU,CAAC;EAClCF,MAAM,CAACK,oBAAoB,EAAE;AAC/B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}