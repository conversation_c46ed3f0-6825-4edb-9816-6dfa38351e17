{"ast": null, "code": "/**\n * @typedef {import('micromark-util-types').Construct} Construct\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Tokenizer} Tokenizer\n */\n\nimport { factorySpace } from 'micromark-factory-space';\nimport { markdownLineEnding } from 'micromark-util-character';\nimport { types } from 'micromark-util-symbol/types.js';\nimport { ok as assert } from 'uvu/assert';\n\n/** @type {Construct} */\nexport const lineEnding = {\n  name: 'lineEnding',\n  tokenize: tokenizeLineEnding\n};\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeLineEnding(effects, ok) {\n  return start;\n\n  /** @type {State} */\n  function start(code) {\n    assert(markdownLineEnding(code), 'expected eol');\n    effects.enter(types.lineEnding);\n    effects.consume(code);\n    effects.exit(types.lineEnding);\n    return factorySpace(effects, ok, types.linePrefix);\n  }\n}", "map": {"version": 3, "names": ["factorySpace", "markdownLineEnding", "types", "ok", "assert", "lineEnding", "name", "tokenize", "tokenizeLineEnding", "effects", "start", "code", "enter", "consume", "exit", "linePrefix"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/micromark-core-commonmark/dev/lib/line-ending.js"], "sourcesContent": ["/**\n * @typedef {import('micromark-util-types').Construct} Construct\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Tokenizer} Tokenizer\n */\n\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownLineEnding} from 'micromark-util-character'\nimport {types} from 'micromark-util-symbol/types.js'\nimport {ok as assert} from 'uvu/assert'\n\n/** @type {Construct} */\nexport const lineEnding = {name: 'lineEnding', tokenize: tokenizeLineEnding}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeLineEnding(effects, ok) {\n  return start\n\n  /** @type {State} */\n  function start(code) {\n    assert(markdownLineEnding(code), 'expected eol')\n    effects.enter(types.lineEnding)\n    effects.consume(code)\n    effects.exit(types.lineEnding)\n    return factorySpace(effects, ok, types.linePrefix)\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,YAAY,QAAO,yBAAyB;AACpD,SAAQC,kBAAkB,QAAO,0BAA0B;AAC3D,SAAQC,KAAK,QAAO,gCAAgC;AACpD,SAAQC,EAAE,IAAIC,MAAM,QAAO,YAAY;;AAEvC;AACA,OAAO,MAAMC,UAAU,GAAG;EAACC,IAAI,EAAE,YAAY;EAAEC,QAAQ,EAAEC;AAAkB,CAAC;;AAE5E;AACA;AACA;AACA;AACA,SAASA,kBAAkBA,CAACC,OAAO,EAAEN,EAAE,EAAE;EACvC,OAAOO,KAAK;;EAEZ;EACA,SAASA,KAAKA,CAACC,IAAI,EAAE;IACnBP,MAAM,CAACH,kBAAkB,CAACU,IAAI,CAAC,EAAE,cAAc,CAAC;IAChDF,OAAO,CAACG,KAAK,CAACV,KAAK,CAACG,UAAU,CAAC;IAC/BI,OAAO,CAACI,OAAO,CAACF,IAAI,CAAC;IACrBF,OAAO,CAACK,IAAI,CAACZ,KAAK,CAACG,UAAU,CAAC;IAC9B,OAAOL,YAAY,CAACS,OAAO,EAAEN,EAAE,EAAED,KAAK,CAACa,UAAU,CAAC;EACpD;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}