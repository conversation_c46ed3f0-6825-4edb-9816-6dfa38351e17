{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\");\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nvar _objectSpread2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectSpread\"));\nvar _classCallCheck2 = _interopRequireDefault(require(\"@babel/runtime/helpers/classCallCheck\"));\nvar _createClass2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createClass\"));\nvar _redux = require(\"redux\");\nvar _reducers = _interopRequireDefault(require(\"./reducers\"));\nvar playerActions = _interopRequireWildcard(require(\"./actions/player\"));\nvar videoActions = _interopRequireWildcard(require(\"./actions/video\"));\nvar Manager = /*#__PURE__*/\nfunction () {\n  function Manager(store) {\n    (0, _classCallCheck2[\"default\"])(this, Manager);\n    this.store = store || (0, _redux.createStore)(_reducers[\"default\"]);\n    this.video = null;\n    this.rootElement = null;\n  }\n  (0, _createClass2[\"default\"])(Manager, [{\n    key: \"getActions\",\n    value: function getActions() {\n      var manager = this;\n      var dispatch = this.store.dispatch;\n      var actions = (0, _objectSpread2[\"default\"])({}, playerActions, videoActions);\n      function bindActionCreator(actionCreator) {\n        return function bindAction() {\n          // eslint-disable-next-line prefer-rest-params\n          var action = actionCreator.apply(manager, arguments);\n          if (typeof action !== 'undefined') {\n            dispatch(action);\n          }\n        };\n      }\n      return Object.keys(actions).filter(function (key) {\n        return typeof actions[key] === 'function';\n      }).reduce(function (boundActions, key) {\n        boundActions[key] = bindActionCreator(actions[key]);\n        return boundActions;\n      }, {});\n    }\n  }, {\n    key: \"getState\",\n    value: function getState() {\n      return this.store.getState();\n    } // subscribe state change\n  }, {\n    key: \"subscribeToStateChange\",\n    value: function subscribeToStateChange(listener, getState) {\n      if (!getState) {\n        getState = this.getState.bind(this);\n      }\n      var prevState = getState();\n      var handleChange = function handleChange() {\n        var state = getState();\n        if (state === prevState) {\n          return;\n        }\n        var prevStateCopy = prevState;\n        prevState = state;\n        listener(state, prevStateCopy);\n      };\n      return this.store.subscribe(handleChange);\n    } // subscribe to operation state change\n  }, {\n    key: \"subscribeToOperationStateChange\",\n    value: function subscribeToOperationStateChange(listener) {\n      var _this = this;\n      return this.subscribeToStateChange(listener, function () {\n        return _this.getState().operation;\n      });\n    } // subscribe to player state change\n  }, {\n    key: \"subscribeToPlayerStateChange\",\n    value: function subscribeToPlayerStateChange(listener) {\n      var _this2 = this;\n      return this.subscribeToStateChange(listener, function () {\n        return _this2.getState().player;\n      });\n    }\n  }]);\n  return Manager;\n}();\nexports[\"default\"] = Manager;", "map": {"version": 3, "names": ["_interopRequireWildcard", "require", "_interopRequireDefault", "Object", "defineProperty", "exports", "value", "_objectSpread2", "_classCallCheck2", "_createClass2", "_redux", "_reducers", "playerActions", "videoActions", "Manager", "store", "createStore", "video", "rootElement", "key", "getActions", "manager", "dispatch", "actions", "bindActionCreator", "actionCreator", "bindAction", "action", "apply", "arguments", "keys", "filter", "reduce", "boundActions", "getState", "subscribeToStateChange", "listener", "bind", "prevState", "handleChange", "state", "prevStateCopy", "subscribe", "subscribeToOperationStateChange", "_this", "operation", "subscribeToPlayerStateChange", "_this2", "player"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/video-react/lib/Manager.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\");\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _objectSpread2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectSpread\"));\n\nvar _classCallCheck2 = _interopRequireDefault(require(\"@babel/runtime/helpers/classCallCheck\"));\n\nvar _createClass2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createClass\"));\n\nvar _redux = require(\"redux\");\n\nvar _reducers = _interopRequireDefault(require(\"./reducers\"));\n\nvar playerActions = _interopRequireWildcard(require(\"./actions/player\"));\n\nvar videoActions = _interopRequireWildcard(require(\"./actions/video\"));\n\nvar Manager =\n/*#__PURE__*/\nfunction () {\n  function Manager(store) {\n    (0, _classCallCheck2[\"default\"])(this, Manager);\n    this.store = store || (0, _redux.createStore)(_reducers[\"default\"]);\n    this.video = null;\n    this.rootElement = null;\n  }\n\n  (0, _createClass2[\"default\"])(Manager, [{\n    key: \"getActions\",\n    value: function getActions() {\n      var manager = this;\n      var dispatch = this.store.dispatch;\n      var actions = (0, _objectSpread2[\"default\"])({}, playerActions, videoActions);\n\n      function bindActionCreator(actionCreator) {\n        return function bindAction() {\n          // eslint-disable-next-line prefer-rest-params\n          var action = actionCreator.apply(manager, arguments);\n\n          if (typeof action !== 'undefined') {\n            dispatch(action);\n          }\n        };\n      }\n\n      return Object.keys(actions).filter(function (key) {\n        return typeof actions[key] === 'function';\n      }).reduce(function (boundActions, key) {\n        boundActions[key] = bindActionCreator(actions[key]);\n        return boundActions;\n      }, {});\n    }\n  }, {\n    key: \"getState\",\n    value: function getState() {\n      return this.store.getState();\n    } // subscribe state change\n\n  }, {\n    key: \"subscribeToStateChange\",\n    value: function subscribeToStateChange(listener, getState) {\n      if (!getState) {\n        getState = this.getState.bind(this);\n      }\n\n      var prevState = getState();\n\n      var handleChange = function handleChange() {\n        var state = getState();\n\n        if (state === prevState) {\n          return;\n        }\n\n        var prevStateCopy = prevState;\n        prevState = state;\n        listener(state, prevStateCopy);\n      };\n\n      return this.store.subscribe(handleChange);\n    } // subscribe to operation state change\n\n  }, {\n    key: \"subscribeToOperationStateChange\",\n    value: function subscribeToOperationStateChange(listener) {\n      var _this = this;\n\n      return this.subscribeToStateChange(listener, function () {\n        return _this.getState().operation;\n      });\n    } // subscribe to player state change\n\n  }, {\n    key: \"subscribeToPlayerStateChange\",\n    value: function subscribeToPlayerStateChange(listener) {\n      var _this2 = this;\n\n      return this.subscribeToStateChange(listener, function () {\n        return _this2.getState().player;\n      });\n    }\n  }]);\n  return Manager;\n}();\n\nexports[\"default\"] = Manager;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,uBAAuB,GAAGC,OAAO,CAAC,+CAA+C,CAAC;AAEtF,IAAIC,sBAAsB,GAAGD,OAAO,CAAC,8CAA8C,CAAC;AAEpFE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;AAE3B,IAAIE,cAAc,GAAGL,sBAAsB,CAACD,OAAO,CAAC,qCAAqC,CAAC,CAAC;AAE3F,IAAIO,gBAAgB,GAAGN,sBAAsB,CAACD,OAAO,CAAC,uCAAuC,CAAC,CAAC;AAE/F,IAAIQ,aAAa,GAAGP,sBAAsB,CAACD,OAAO,CAAC,oCAAoC,CAAC,CAAC;AAEzF,IAAIS,MAAM,GAAGT,OAAO,CAAC,OAAO,CAAC;AAE7B,IAAIU,SAAS,GAAGT,sBAAsB,CAACD,OAAO,CAAC,YAAY,CAAC,CAAC;AAE7D,IAAIW,aAAa,GAAGZ,uBAAuB,CAACC,OAAO,CAAC,kBAAkB,CAAC,CAAC;AAExE,IAAIY,YAAY,GAAGb,uBAAuB,CAACC,OAAO,CAAC,iBAAiB,CAAC,CAAC;AAEtE,IAAIa,OAAO,GACX;AACA,YAAY;EACV,SAASA,OAAOA,CAACC,KAAK,EAAE;IACtB,CAAC,CAAC,EAAEP,gBAAgB,CAAC,SAAS,CAAC,EAAE,IAAI,EAAEM,OAAO,CAAC;IAC/C,IAAI,CAACC,KAAK,GAAGA,KAAK,IAAI,CAAC,CAAC,EAAEL,MAAM,CAACM,WAAW,EAAEL,SAAS,CAAC,SAAS,CAAC,CAAC;IACnE,IAAI,CAACM,KAAK,GAAG,IAAI;IACjB,IAAI,CAACC,WAAW,GAAG,IAAI;EACzB;EAEA,CAAC,CAAC,EAAET,aAAa,CAAC,SAAS,CAAC,EAAEK,OAAO,EAAE,CAAC;IACtCK,GAAG,EAAE,YAAY;IACjBb,KAAK,EAAE,SAASc,UAAUA,CAAA,EAAG;MAC3B,IAAIC,OAAO,GAAG,IAAI;MAClB,IAAIC,QAAQ,GAAG,IAAI,CAACP,KAAK,CAACO,QAAQ;MAClC,IAAIC,OAAO,GAAG,CAAC,CAAC,EAAEhB,cAAc,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAEK,aAAa,EAAEC,YAAY,CAAC;MAE7E,SAASW,iBAAiBA,CAACC,aAAa,EAAE;QACxC,OAAO,SAASC,UAAUA,CAAA,EAAG;UAC3B;UACA,IAAIC,MAAM,GAAGF,aAAa,CAACG,KAAK,CAACP,OAAO,EAAEQ,SAAS,CAAC;UAEpD,IAAI,OAAOF,MAAM,KAAK,WAAW,EAAE;YACjCL,QAAQ,CAACK,MAAM,CAAC;UAClB;QACF,CAAC;MACH;MAEA,OAAOxB,MAAM,CAAC2B,IAAI,CAACP,OAAO,CAAC,CAACQ,MAAM,CAAC,UAAUZ,GAAG,EAAE;QAChD,OAAO,OAAOI,OAAO,CAACJ,GAAG,CAAC,KAAK,UAAU;MAC3C,CAAC,CAAC,CAACa,MAAM,CAAC,UAAUC,YAAY,EAAEd,GAAG,EAAE;QACrCc,YAAY,CAACd,GAAG,CAAC,GAAGK,iBAAiB,CAACD,OAAO,CAACJ,GAAG,CAAC,CAAC;QACnD,OAAOc,YAAY;MACrB,CAAC,EAAE,CAAC,CAAC,CAAC;IACR;EACF,CAAC,EAAE;IACDd,GAAG,EAAE,UAAU;IACfb,KAAK,EAAE,SAAS4B,QAAQA,CAAA,EAAG;MACzB,OAAO,IAAI,CAACnB,KAAK,CAACmB,QAAQ,EAAE;IAC9B,CAAC,CAAC;EAEJ,CAAC,EAAE;IACDf,GAAG,EAAE,wBAAwB;IAC7Bb,KAAK,EAAE,SAAS6B,sBAAsBA,CAACC,QAAQ,EAAEF,QAAQ,EAAE;MACzD,IAAI,CAACA,QAAQ,EAAE;QACbA,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACG,IAAI,CAAC,IAAI,CAAC;MACrC;MAEA,IAAIC,SAAS,GAAGJ,QAAQ,EAAE;MAE1B,IAAIK,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAG;QACzC,IAAIC,KAAK,GAAGN,QAAQ,EAAE;QAEtB,IAAIM,KAAK,KAAKF,SAAS,EAAE;UACvB;QACF;QAEA,IAAIG,aAAa,GAAGH,SAAS;QAC7BA,SAAS,GAAGE,KAAK;QACjBJ,QAAQ,CAACI,KAAK,EAAEC,aAAa,CAAC;MAChC,CAAC;MAED,OAAO,IAAI,CAAC1B,KAAK,CAAC2B,SAAS,CAACH,YAAY,CAAC;IAC3C,CAAC,CAAC;EAEJ,CAAC,EAAE;IACDpB,GAAG,EAAE,iCAAiC;IACtCb,KAAK,EAAE,SAASqC,+BAA+BA,CAACP,QAAQ,EAAE;MACxD,IAAIQ,KAAK,GAAG,IAAI;MAEhB,OAAO,IAAI,CAACT,sBAAsB,CAACC,QAAQ,EAAE,YAAY;QACvD,OAAOQ,KAAK,CAACV,QAAQ,EAAE,CAACW,SAAS;MACnC,CAAC,CAAC;IACJ,CAAC,CAAC;EAEJ,CAAC,EAAE;IACD1B,GAAG,EAAE,8BAA8B;IACnCb,KAAK,EAAE,SAASwC,4BAA4BA,CAACV,QAAQ,EAAE;MACrD,IAAIW,MAAM,GAAG,IAAI;MAEjB,OAAO,IAAI,CAACZ,sBAAsB,CAACC,QAAQ,EAAE,YAAY;QACvD,OAAOW,MAAM,CAACb,QAAQ,EAAE,CAACc,MAAM;MACjC,CAAC,CAAC;IACJ;EACF,CAAC,CAAC,CAAC;EACH,OAAOlC,OAAO;AAChB,CAAC,EAAE;AAEHT,OAAO,CAAC,SAAS,CAAC,GAAGS,OAAO"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}