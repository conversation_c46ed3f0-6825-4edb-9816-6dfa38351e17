{"ast": null, "code": "import { getSlideTransformEl } from './utils.js';\nexport default function effectTarget(effectParams, slideEl) {\n  const transformEl = getSlideTransformEl(slideEl);\n  if (transformEl !== slideEl) {\n    transformEl.style.backfaceVisibility = 'hidden';\n    transformEl.style['-webkit-backface-visibility'] = 'hidden';\n  }\n  return transformEl;\n}", "map": {"version": 3, "names": ["getSlideTransformEl", "effect<PERSON>arget", "effectParams", "slideEl", "transformEl", "style", "backfaceVisibility"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/shared/effect-target.js"], "sourcesContent": ["import { getSlideTransformEl } from './utils.js';\nexport default function effectTarget(effectParams, slideEl) {\n  const transformEl = getSlideTransformEl(slideEl);\n  if (transformEl !== slideEl) {\n    transformEl.style.backfaceVisibility = 'hidden';\n    transformEl.style['-webkit-backface-visibility'] = 'hidden';\n  }\n  return transformEl;\n}"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,YAAY;AAChD,eAAe,SAASC,YAAYA,CAACC,YAAY,EAAEC,OAAO,EAAE;EAC1D,MAAMC,WAAW,GAAGJ,mBAAmB,CAACG,OAAO,CAAC;EAChD,IAAIC,WAAW,KAAKD,OAAO,EAAE;IAC3BC,WAAW,CAACC,KAAK,CAACC,kBAAkB,GAAG,QAAQ;IAC/CF,WAAW,CAACC,KAAK,CAAC,6BAA6B,CAAC,GAAG,QAAQ;EAC7D;EACA,OAAOD,WAAW;AACpB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}