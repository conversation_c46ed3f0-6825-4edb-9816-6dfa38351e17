{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\temp\\\\StudyNotion\\\\src\\\\components\\\\core\\\\Dashboard\\\\Cart\\\\RenderTotalAmount.jsx\",\n  _s = $RefreshSig$();\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useNavigate } from \"react-router-dom\";\nimport IconBtn from \"../../../common/IconBtn\";\nimport { buyCourse } from \"../../../../services/operations/studentFeaturesAPI\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function RenderTotalAmount() {\n  _s();\n  const {\n    total,\n    cart\n  } = useSelector(state => state.cart);\n  const {\n    token\n  } = useSelector(state => state.auth);\n  const {\n    user\n  } = useSelector(state => state.profile);\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const handleBuyCourse = () => {\n    const courses = cart.map(course => course._id);\n    buyCourse(token, courses, user, navigate, dispatch);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-w-[280px] rounded-md border-[1px] border-richblack-700 bg-richblack-800 p-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"mb-1 text-sm font-medium text-richblack-300\",\n      children: \"Total:\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"mb-6 text-3xl font-medium text-yellow-100\",\n      children: [\"\\u20B9 \", total]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(IconBtn, {\n      text: \"Buy Now\",\n      onclick: handleBuyCourse,\n      customClasses: \"w-full justify-center\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 20,\n    columnNumber: 5\n  }, this);\n}\n_s(RenderTotalAmount, \"DM5Z6l1vnoTDOW4ccEP/ZMcQMc0=\", false, function () {\n  return [useSelector, useSelector, useSelector, useNavigate, useDispatch];\n});\n_c = RenderTotalAmount;\nvar _c;\n$RefreshReg$(_c, \"RenderTotalAmount\");", "map": {"version": 3, "names": ["useDispatch", "useSelector", "useNavigate", "IconBtn", "buyCourse", "jsxDEV", "_jsxDEV", "RenderTotalAmount", "_s", "total", "cart", "state", "token", "auth", "user", "profile", "navigate", "dispatch", "handleBuyCourse", "courses", "map", "course", "_id", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "text", "onclick", "customClasses", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/src/components/core/Dashboard/Cart/RenderTotalAmount.jsx"], "sourcesContent": ["import { useDispatch, useSelector } from \"react-redux\"\r\nimport { useNavigate } from \"react-router-dom\"\r\n\r\nimport IconBtn from \"../../../common/IconBtn\"\r\nimport { buyCourse } from \"../../../../services/operations/studentFeaturesAPI\"\r\n\r\nexport default function RenderTotalAmount() {\r\n  const { total, cart } = useSelector((state) => state.cart)\r\n  const { token } = useSelector((state) => state.auth)\r\n  const { user } = useSelector((state) => state.profile)\r\n  const navigate = useNavigate()\r\n  const dispatch = useDispatch()\r\n\r\n  const handleBuyCourse = () => {\r\n    const courses = cart.map((course) => course._id)\r\n    buyCourse(token, courses, user, navigate, dispatch)\r\n  }\r\n\r\n  return (\r\n    <div className=\"min-w-[280px] rounded-md border-[1px] border-richblack-700 bg-richblack-800 p-6\">\r\n      <p className=\"mb-1 text-sm font-medium text-richblack-300\">Total:</p>\r\n      <p className=\"mb-6 text-3xl font-medium text-yellow-100\">₹ {total}</p>\r\n      <IconBtn\r\n        text=\"Buy Now\"\r\n        onclick={handleBuyCourse}\r\n        customClasses=\"w-full justify-center\"\r\n      />\r\n    </div>\r\n  )\r\n}"], "mappings": ";;AAAA,SAASA,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,QAAQ,kBAAkB;AAE9C,OAAOC,OAAO,MAAM,yBAAyB;AAC7C,SAASC,SAAS,QAAQ,oDAAoD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAE9E,eAAe,SAASC,iBAAiBA,CAAA,EAAG;EAAAC,EAAA;EAC1C,MAAM;IAAEC,KAAK;IAAEC;EAAK,CAAC,GAAGT,WAAW,CAAEU,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EAC1D,MAAM;IAAEE;EAAM,CAAC,GAAGX,WAAW,CAAEU,KAAK,IAAKA,KAAK,CAACE,IAAI,CAAC;EACpD,MAAM;IAAEC;EAAK,CAAC,GAAGb,WAAW,CAAEU,KAAK,IAAKA,KAAK,CAACI,OAAO,CAAC;EACtD,MAAMC,QAAQ,GAAGd,WAAW,EAAE;EAC9B,MAAMe,QAAQ,GAAGjB,WAAW,EAAE;EAE9B,MAAMkB,eAAe,GAAGA,CAAA,KAAM;IAC5B,MAAMC,OAAO,GAAGT,IAAI,CAACU,GAAG,CAAEC,MAAM,IAAKA,MAAM,CAACC,GAAG,CAAC;IAChDlB,SAAS,CAACQ,KAAK,EAAEO,OAAO,EAAEL,IAAI,EAAEE,QAAQ,EAAEC,QAAQ,CAAC;EACrD,CAAC;EAED,oBACEX,OAAA;IAAKiB,SAAS,EAAC,iFAAiF;IAAAC,QAAA,gBAC9FlB,OAAA;MAAGiB,SAAS,EAAC,6CAA6C;MAAAC,QAAA,EAAC;IAAM;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAI,eACrEtB,OAAA;MAAGiB,SAAS,EAAC,2CAA2C;MAAAC,QAAA,GAAC,SAAE,EAACf,KAAK;IAAA;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAK,eACtEtB,OAAA,CAACH,OAAO;MACN0B,IAAI,EAAC,SAAS;MACdC,OAAO,EAAEZ,eAAgB;MACzBa,aAAa,EAAC;IAAuB;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACrC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACE;AAEV;AAACpB,EAAA,CAvBuBD,iBAAiB;EAAA,QACfN,WAAW,EACjBA,WAAW,EACZA,WAAW,EACXC,WAAW,EACXF,WAAW;AAAA;AAAAgC,EAAA,GALNzB,iBAAiB;AAAA,IAAAyB,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}