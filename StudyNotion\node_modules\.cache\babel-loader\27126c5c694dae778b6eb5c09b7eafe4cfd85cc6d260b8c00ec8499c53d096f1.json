{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\");\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nvar _classCallCheck2 = _interopRequireDefault(require(\"@babel/runtime/helpers/classCallCheck\"));\nvar _createClass2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createClass\"));\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"@babel/runtime/helpers/possibleConstructorReturn\"));\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"@babel/runtime/helpers/getPrototypeOf\"));\nvar _assertThisInitialized2 = _interopRequireDefault(require(\"@babel/runtime/helpers/assertThisInitialized\"));\nvar _inherits2 = _interopRequireDefault(require(\"@babel/runtime/helpers/inherits\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _react = _interopRequireWildcard(require(\"react\"));\nvar propTypes = {\n  children: _propTypes[\"default\"].any\n};\nvar Menu = /*#__PURE__*/\nfunction (_Component) {\n  (0, _inherits2[\"default\"])(Menu, _Component);\n  function Menu(props, context) {\n    var _this;\n    (0, _classCallCheck2[\"default\"])(this, Menu);\n    _this = (0, _possibleConstructorReturn2[\"default\"])(this, (0, _getPrototypeOf2[\"default\"])(Menu).call(this, props, context));\n    _this.handleClick = _this.handleClick.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    return _this;\n  }\n  (0, _createClass2[\"default\"])(Menu, [{\n    key: \"handleClick\",\n    value: function handleClick(event) {\n      event.preventDefault(); // event.stopPropagation();\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      return _react[\"default\"].createElement(\"div\", {\n        className: \"video-react-menu video-react-lock-showing\",\n        role: \"presentation\",\n        onClick: this.handleClick\n      }, _react[\"default\"].createElement(\"ul\", {\n        className: \"video-react-menu-content\"\n      }, this.props.children));\n    }\n  }]);\n  return Menu;\n}(_react.Component);\nexports[\"default\"] = Menu;\nMenu.propTypes = propTypes;\nMenu.displayName = 'Menu';", "map": {"version": 3, "names": ["_interopRequireWildcard", "require", "_interopRequireDefault", "Object", "defineProperty", "exports", "value", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_assertThisInitialized2", "_inherits2", "_propTypes", "_react", "propTypes", "children", "any", "<PERSON><PERSON>", "_Component", "props", "context", "_this", "call", "handleClick", "bind", "key", "event", "preventDefault", "render", "createElement", "className", "role", "onClick", "Component", "displayName"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/video-react/lib/components/menu/Menu.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\");\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _classCallCheck2 = _interopRequireDefault(require(\"@babel/runtime/helpers/classCallCheck\"));\n\nvar _createClass2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createClass\"));\n\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"@babel/runtime/helpers/possibleConstructorReturn\"));\n\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"@babel/runtime/helpers/getPrototypeOf\"));\n\nvar _assertThisInitialized2 = _interopRequireDefault(require(\"@babel/runtime/helpers/assertThisInitialized\"));\n\nvar _inherits2 = _interopRequireDefault(require(\"@babel/runtime/helpers/inherits\"));\n\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\n\nvar _react = _interopRequireWildcard(require(\"react\"));\n\nvar propTypes = {\n  children: _propTypes[\"default\"].any\n};\n\nvar Menu =\n/*#__PURE__*/\nfunction (_Component) {\n  (0, _inherits2[\"default\"])(Menu, _Component);\n\n  function Menu(props, context) {\n    var _this;\n\n    (0, _classCallCheck2[\"default\"])(this, Menu);\n    _this = (0, _possibleConstructorReturn2[\"default\"])(this, (0, _getPrototypeOf2[\"default\"])(Menu).call(this, props, context));\n    _this.handleClick = _this.handleClick.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    return _this;\n  }\n\n  (0, _createClass2[\"default\"])(Menu, [{\n    key: \"handleClick\",\n    value: function handleClick(event) {\n      event.preventDefault(); // event.stopPropagation();\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      return _react[\"default\"].createElement(\"div\", {\n        className: \"video-react-menu video-react-lock-showing\",\n        role: \"presentation\",\n        onClick: this.handleClick\n      }, _react[\"default\"].createElement(\"ul\", {\n        className: \"video-react-menu-content\"\n      }, this.props.children));\n    }\n  }]);\n  return Menu;\n}(_react.Component);\n\nexports[\"default\"] = Menu;\nMenu.propTypes = propTypes;\nMenu.displayName = 'Menu';"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,uBAAuB,GAAGC,OAAO,CAAC,+CAA+C,CAAC;AAEtF,IAAIC,sBAAsB,GAAGD,OAAO,CAAC,8CAA8C,CAAC;AAEpFE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;AAE3B,IAAIE,gBAAgB,GAAGL,sBAAsB,CAACD,OAAO,CAAC,uCAAuC,CAAC,CAAC;AAE/F,IAAIO,aAAa,GAAGN,sBAAsB,CAACD,OAAO,CAAC,oCAAoC,CAAC,CAAC;AAEzF,IAAIQ,2BAA2B,GAAGP,sBAAsB,CAACD,OAAO,CAAC,kDAAkD,CAAC,CAAC;AAErH,IAAIS,gBAAgB,GAAGR,sBAAsB,CAACD,OAAO,CAAC,uCAAuC,CAAC,CAAC;AAE/F,IAAIU,uBAAuB,GAAGT,sBAAsB,CAACD,OAAO,CAAC,8CAA8C,CAAC,CAAC;AAE7G,IAAIW,UAAU,GAAGV,sBAAsB,CAACD,OAAO,CAAC,iCAAiC,CAAC,CAAC;AAEnF,IAAIY,UAAU,GAAGX,sBAAsB,CAACD,OAAO,CAAC,YAAY,CAAC,CAAC;AAE9D,IAAIa,MAAM,GAAGd,uBAAuB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AAEtD,IAAIc,SAAS,GAAG;EACdC,QAAQ,EAAEH,UAAU,CAAC,SAAS,CAAC,CAACI;AAClC,CAAC;AAED,IAAIC,IAAI,GACR;AACA,UAAUC,UAAU,EAAE;EACpB,CAAC,CAAC,EAAEP,UAAU,CAAC,SAAS,CAAC,EAAEM,IAAI,EAAEC,UAAU,CAAC;EAE5C,SAASD,IAAIA,CAACE,KAAK,EAAEC,OAAO,EAAE;IAC5B,IAAIC,KAAK;IAET,CAAC,CAAC,EAAEf,gBAAgB,CAAC,SAAS,CAAC,EAAE,IAAI,EAAEW,IAAI,CAAC;IAC5CI,KAAK,GAAG,CAAC,CAAC,EAAEb,2BAA2B,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAEC,gBAAgB,CAAC,SAAS,CAAC,EAAEQ,IAAI,CAAC,CAACK,IAAI,CAAC,IAAI,EAAEH,KAAK,EAAEC,OAAO,CAAC,CAAC;IAC5HC,KAAK,CAACE,WAAW,GAAGF,KAAK,CAACE,WAAW,CAACC,IAAI,CAAC,CAAC,CAAC,EAAEd,uBAAuB,CAAC,SAAS,CAAC,EAAEW,KAAK,CAAC,CAAC;IAC1F,OAAOA,KAAK;EACd;EAEA,CAAC,CAAC,EAAEd,aAAa,CAAC,SAAS,CAAC,EAAEU,IAAI,EAAE,CAAC;IACnCQ,GAAG,EAAE,aAAa;IAClBpB,KAAK,EAAE,SAASkB,WAAWA,CAACG,KAAK,EAAE;MACjCA,KAAK,CAACC,cAAc,EAAE,CAAC,CAAC;IAC1B;EACF,CAAC,EAAE;IACDF,GAAG,EAAE,QAAQ;IACbpB,KAAK,EAAE,SAASuB,MAAMA,CAAA,EAAG;MACvB,OAAOf,MAAM,CAAC,SAAS,CAAC,CAACgB,aAAa,CAAC,KAAK,EAAE;QAC5CC,SAAS,EAAE,2CAA2C;QACtDC,IAAI,EAAE,cAAc;QACpBC,OAAO,EAAE,IAAI,CAACT;MAChB,CAAC,EAAEV,MAAM,CAAC,SAAS,CAAC,CAACgB,aAAa,CAAC,IAAI,EAAE;QACvCC,SAAS,EAAE;MACb,CAAC,EAAE,IAAI,CAACX,KAAK,CAACJ,QAAQ,CAAC,CAAC;IAC1B;EACF,CAAC,CAAC,CAAC;EACH,OAAOE,IAAI;AACb,CAAC,CAACJ,MAAM,CAACoB,SAAS,CAAC;AAEnB7B,OAAO,CAAC,SAAS,CAAC,GAAGa,IAAI;AACzBA,IAAI,CAACH,SAAS,GAAGA,SAAS;AAC1BG,IAAI,CAACiB,WAAW,GAAG,MAAM"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}