{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\");\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nvar _objectSpread2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectSpread\"));\nvar _classCallCheck2 = _interopRequireDefault(require(\"@babel/runtime/helpers/classCallCheck\"));\nvar _createClass2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createClass\"));\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"@babel/runtime/helpers/possibleConstructorReturn\"));\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"@babel/runtime/helpers/getPrototypeOf\"));\nvar _assertThisInitialized2 = _interopRequireDefault(require(\"@babel/runtime/helpers/assertThisInitialized\"));\nvar _inherits2 = _interopRequireDefault(require(\"@babel/runtime/helpers/inherits\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _react = _interopRequireWildcard(require(\"react\"));\nvar _classnames = _interopRequireDefault(require(\"classnames\"));\nvar _utils = require(\"../utils\");\nvar propTypes = {\n  actions: _propTypes[\"default\"].object,\n  player: _propTypes[\"default\"].object,\n  children: _propTypes[\"default\"].any,\n  startTime: _propTypes[\"default\"].number,\n  loop: _propTypes[\"default\"].bool,\n  muted: _propTypes[\"default\"].bool,\n  autoPlay: _propTypes[\"default\"].bool,\n  playsInline: _propTypes[\"default\"].bool,\n  src: _propTypes[\"default\"].string,\n  poster: _propTypes[\"default\"].string,\n  className: _propTypes[\"default\"].string,\n  preload: _propTypes[\"default\"].oneOf(['auto', 'metadata', 'none']),\n  crossOrigin: _propTypes[\"default\"].string,\n  onLoadStart: _propTypes[\"default\"].func,\n  onWaiting: _propTypes[\"default\"].func,\n  onCanPlay: _propTypes[\"default\"].func,\n  onCanPlayThrough: _propTypes[\"default\"].func,\n  onPlaying: _propTypes[\"default\"].func,\n  onEnded: _propTypes[\"default\"].func,\n  onSeeking: _propTypes[\"default\"].func,\n  onSeeked: _propTypes[\"default\"].func,\n  onPlay: _propTypes[\"default\"].func,\n  onPause: _propTypes[\"default\"].func,\n  onProgress: _propTypes[\"default\"].func,\n  onDurationChange: _propTypes[\"default\"].func,\n  onError: _propTypes[\"default\"].func,\n  onSuspend: _propTypes[\"default\"].func,\n  onAbort: _propTypes[\"default\"].func,\n  onEmptied: _propTypes[\"default\"].func,\n  onStalled: _propTypes[\"default\"].func,\n  onLoadedMetadata: _propTypes[\"default\"].func,\n  onLoadedData: _propTypes[\"default\"].func,\n  onTimeUpdate: _propTypes[\"default\"].func,\n  onRateChange: _propTypes[\"default\"].func,\n  onVolumeChange: _propTypes[\"default\"].func,\n  onResize: _propTypes[\"default\"].func\n};\nvar Video = /*#__PURE__*/\nfunction (_Component) {\n  (0, _inherits2[\"default\"])(Video, _Component);\n  function Video(props) {\n    var _this;\n    (0, _classCallCheck2[\"default\"])(this, Video);\n    _this = (0, _possibleConstructorReturn2[\"default\"])(this, (0, _getPrototypeOf2[\"default\"])(Video).call(this, props));\n    _this.video = null; // the html5 video\n\n    _this.play = _this.play.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.pause = _this.pause.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.seek = _this.seek.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.forward = _this.forward.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.replay = _this.replay.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.toggleFullscreen = _this.toggleFullscreen.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.getProperties = _this.getProperties.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.renderChildren = _this.renderChildren.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleLoadStart = _this.handleLoadStart.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleCanPlay = _this.handleCanPlay.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleCanPlayThrough = _this.handleCanPlayThrough.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handlePlay = _this.handlePlay.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handlePlaying = _this.handlePlaying.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handlePause = _this.handlePause.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleEnded = _this.handleEnded.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleWaiting = _this.handleWaiting.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleSeeking = _this.handleSeeking.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleSeeked = _this.handleSeeked.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleFullscreenChange = _this.handleFullscreenChange.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleError = _this.handleError.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleSuspend = _this.handleSuspend.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleAbort = _this.handleAbort.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleEmptied = _this.handleEmptied.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleStalled = _this.handleStalled.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleLoadedMetaData = _this.handleLoadedMetaData.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleLoadedData = _this.handleLoadedData.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleTimeUpdate = _this.handleTimeUpdate.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleRateChange = _this.handleRateChange.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleVolumeChange = _this.handleVolumeChange.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleDurationChange = _this.handleDurationChange.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleProgress = (0, _utils.throttle)(_this.handleProgress.bind((0, _assertThisInitialized2[\"default\"])(_this)), 250);\n    _this.handleKeypress = _this.handleKeypress.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleTextTrackChange = _this.handleTextTrackChange.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    return _this;\n  }\n  (0, _createClass2[\"default\"])(Video, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      this.forceUpdate(); // make sure the children can get the video property\n\n      if (this.video && this.video.textTracks) {\n        this.video.textTracks.onaddtrack = this.handleTextTrackChange;\n        this.video.textTracks.onremovetrack = this.handleTextTrackChange;\n      }\n    } // get all video properties\n  }, {\n    key: \"getProperties\",\n    value: function getProperties() {\n      var _this2 = this;\n      if (!this.video) {\n        return null;\n      }\n      return _utils.mediaProperties.reduce(function (properties, key) {\n        properties[key] = _this2.video[key];\n        return properties;\n      }, {});\n    } // get playback rate\n  }, {\n    key: \"handleTextTrackChange\",\n    value: function handleTextTrackChange() {\n      var _this$props = this.props,\n        actions = _this$props.actions,\n        player = _this$props.player;\n      if (this.video && this.video.textTracks) {\n        var activeTextTrack = Array.from(this.video.textTracks).find(function (textTrack) {\n          return textTrack.mode === 'showing';\n        });\n        if (activeTextTrack !== player.activeTextTrack) {\n          actions.activateTextTrack(activeTextTrack);\n        }\n      }\n    } // play the video\n  }, {\n    key: \"play\",\n    value: function play() {\n      var promise = this.video.play();\n      if (promise !== undefined) {\n        promise[\"catch\"](function () {}).then(function () {});\n      }\n    } // pause the video\n  }, {\n    key: \"pause\",\n    value: function pause() {\n      var promise = this.video.pause();\n      if (promise !== undefined) {\n        promise[\"catch\"](function () {}).then(function () {});\n      }\n    } // Change the video source and re-load the video:\n  }, {\n    key: \"load\",\n    value: function load() {\n      this.video.load();\n    } // Add a new text track to the video\n  }, {\n    key: \"addTextTrack\",\n    value: function addTextTrack() {\n      var _this$video;\n      (_this$video = this.video).addTextTrack.apply(_this$video, arguments);\n    } // Check if your browser can play different types of video:\n  }, {\n    key: \"canPlayType\",\n    value: function canPlayType() {\n      var _this$video2;\n      (_this$video2 = this.video).canPlayType.apply(_this$video2, arguments);\n    } // toggle play\n  }, {\n    key: \"togglePlay\",\n    value: function togglePlay() {\n      if (this.video.paused) {\n        this.play();\n      } else {\n        this.pause();\n      }\n    } // seek video by time\n  }, {\n    key: \"seek\",\n    value: function seek(time) {\n      try {\n        this.video.currentTime = time;\n      } catch (e) {// console.log(e, 'Video is not ready.')\n      }\n    } // jump forward x seconds\n  }, {\n    key: \"forward\",\n    value: function forward(seconds) {\n      this.seek(this.video.currentTime + seconds);\n    } // jump back x seconds\n  }, {\n    key: \"replay\",\n    value: function replay(seconds) {\n      this.forward(-seconds);\n    } // enter or exist full screen\n  }, {\n    key: \"toggleFullscreen\",\n    value: function toggleFullscreen() {\n      var _this$props2 = this.props,\n        player = _this$props2.player,\n        actions = _this$props2.actions;\n      actions.toggleFullscreen(player);\n    } // Fired when the user agent\n    // begins looking for media data\n  }, {\n    key: \"handleLoadStart\",\n    value: function handleLoadStart() {\n      var _this$props3 = this.props,\n        actions = _this$props3.actions,\n        onLoadStart = _this$props3.onLoadStart;\n      actions.handleLoadStart(this.getProperties());\n      if (onLoadStart) {\n        onLoadStart.apply(void 0, arguments);\n      }\n    } // A handler for events that\n    // signal that waiting has ended\n  }, {\n    key: \"handleCanPlay\",\n    value: function handleCanPlay() {\n      var _this$props4 = this.props,\n        actions = _this$props4.actions,\n        onCanPlay = _this$props4.onCanPlay;\n      actions.handleCanPlay(this.getProperties());\n      if (onCanPlay) {\n        onCanPlay.apply(void 0, arguments);\n      }\n    } // A handler for events that\n    // signal that waiting has ended\n  }, {\n    key: \"handleCanPlayThrough\",\n    value: function handleCanPlayThrough() {\n      var _this$props5 = this.props,\n        actions = _this$props5.actions,\n        onCanPlayThrough = _this$props5.onCanPlayThrough;\n      actions.handleCanPlayThrough(this.getProperties());\n      if (onCanPlayThrough) {\n        onCanPlayThrough.apply(void 0, arguments);\n      }\n    } // A handler for events that\n    // signal that waiting has ended\n  }, {\n    key: \"handlePlaying\",\n    value: function handlePlaying() {\n      var _this$props6 = this.props,\n        actions = _this$props6.actions,\n        onPlaying = _this$props6.onPlaying;\n      actions.handlePlaying(this.getProperties());\n      if (onPlaying) {\n        onPlaying.apply(void 0, arguments);\n      }\n    } // Fired whenever the media has been started\n  }, {\n    key: \"handlePlay\",\n    value: function handlePlay() {\n      var _this$props7 = this.props,\n        actions = _this$props7.actions,\n        onPlay = _this$props7.onPlay;\n      actions.handlePlay(this.getProperties());\n      if (onPlay) {\n        onPlay.apply(void 0, arguments);\n      }\n    } // Fired whenever the media has been paused\n  }, {\n    key: \"handlePause\",\n    value: function handlePause() {\n      var _this$props8 = this.props,\n        actions = _this$props8.actions,\n        onPause = _this$props8.onPause;\n      actions.handlePause(this.getProperties());\n      if (onPause) {\n        onPause.apply(void 0, arguments);\n      }\n    } // Fired when the duration of\n    // the media resource is first known or changed\n  }, {\n    key: \"handleDurationChange\",\n    value: function handleDurationChange() {\n      var _this$props9 = this.props,\n        actions = _this$props9.actions,\n        onDurationChange = _this$props9.onDurationChange;\n      actions.handleDurationChange(this.getProperties());\n      if (onDurationChange) {\n        onDurationChange.apply(void 0, arguments);\n      }\n    } // Fired while the user agent\n    // is downloading media data\n  }, {\n    key: \"handleProgress\",\n    value: function handleProgress() {\n      var _this$props10 = this.props,\n        actions = _this$props10.actions,\n        onProgress = _this$props10.onProgress;\n      if (this.video) {\n        actions.handleProgressChange(this.getProperties());\n      }\n      if (onProgress) {\n        onProgress.apply(void 0, arguments);\n      }\n    } // Fired when the end of the media resource\n    // is reached (currentTime == duration)\n  }, {\n    key: \"handleEnded\",\n    value: function handleEnded() {\n      var _this$props11 = this.props,\n        loop = _this$props11.loop,\n        player = _this$props11.player,\n        actions = _this$props11.actions,\n        onEnded = _this$props11.onEnded;\n      if (loop) {\n        this.seek(0);\n        this.play();\n      } else if (!player.paused) {\n        this.pause();\n      }\n      actions.handleEnd(this.getProperties());\n      if (onEnded) {\n        onEnded.apply(void 0, arguments);\n      }\n    } // Fired whenever the media begins waiting\n  }, {\n    key: \"handleWaiting\",\n    value: function handleWaiting() {\n      var _this$props12 = this.props,\n        actions = _this$props12.actions,\n        onWaiting = _this$props12.onWaiting;\n      actions.handleWaiting(this.getProperties());\n      if (onWaiting) {\n        onWaiting.apply(void 0, arguments);\n      }\n    } // Fired whenever the player\n    // is jumping to a new time\n  }, {\n    key: \"handleSeeking\",\n    value: function handleSeeking() {\n      var _this$props13 = this.props,\n        actions = _this$props13.actions,\n        onSeeking = _this$props13.onSeeking;\n      actions.handleSeeking(this.getProperties());\n      if (onSeeking) {\n        onSeeking.apply(void 0, arguments);\n      }\n    } // Fired when the player has\n    // finished jumping to a new time\n  }, {\n    key: \"handleSeeked\",\n    value: function handleSeeked() {\n      var _this$props14 = this.props,\n        actions = _this$props14.actions,\n        onSeeked = _this$props14.onSeeked;\n      actions.handleSeeked(this.getProperties());\n      if (onSeeked) {\n        onSeeked.apply(void 0, arguments);\n      }\n    } // Handle Fullscreen Change\n  }, {\n    key: \"handleFullscreenChange\",\n    value: function handleFullscreenChange() {} // Fires when the browser is\n    // intentionally not getting media data\n  }, {\n    key: \"handleSuspend\",\n    value: function handleSuspend() {\n      var _this$props15 = this.props,\n        actions = _this$props15.actions,\n        onSuspend = _this$props15.onSuspend;\n      actions.handleSuspend(this.getProperties());\n      if (onSuspend) {\n        onSuspend.apply(void 0, arguments);\n      }\n    } // Fires when the loading of an audio/video is aborted\n  }, {\n    key: \"handleAbort\",\n    value: function handleAbort() {\n      var _this$props16 = this.props,\n        actions = _this$props16.actions,\n        onAbort = _this$props16.onAbort;\n      actions.handleAbort(this.getProperties());\n      if (onAbort) {\n        onAbort.apply(void 0, arguments);\n      }\n    } // Fires when the current playlist is empty\n  }, {\n    key: \"handleEmptied\",\n    value: function handleEmptied() {\n      var _this$props17 = this.props,\n        actions = _this$props17.actions,\n        onEmptied = _this$props17.onEmptied;\n      actions.handleEmptied(this.getProperties());\n      if (onEmptied) {\n        onEmptied.apply(void 0, arguments);\n      }\n    } // Fires when the browser is trying to\n    // get media data, but data is not available\n  }, {\n    key: \"handleStalled\",\n    value: function handleStalled() {\n      var _this$props18 = this.props,\n        actions = _this$props18.actions,\n        onStalled = _this$props18.onStalled;\n      actions.handleStalled(this.getProperties());\n      if (onStalled) {\n        onStalled.apply(void 0, arguments);\n      }\n    } // Fires when the browser has loaded\n    // meta data for the audio/video\n  }, {\n    key: \"handleLoadedMetaData\",\n    value: function handleLoadedMetaData() {\n      var _this$props19 = this.props,\n        actions = _this$props19.actions,\n        onLoadedMetadata = _this$props19.onLoadedMetadata,\n        startTime = _this$props19.startTime;\n      if (startTime && startTime > 0) {\n        this.video.currentTime = startTime;\n      }\n      actions.handleLoadedMetaData(this.getProperties());\n      if (onLoadedMetadata) {\n        onLoadedMetadata.apply(void 0, arguments);\n      }\n    } // Fires when the browser has loaded\n    // the current frame of the audio/video\n  }, {\n    key: \"handleLoadedData\",\n    value: function handleLoadedData() {\n      var _this$props20 = this.props,\n        actions = _this$props20.actions,\n        onLoadedData = _this$props20.onLoadedData;\n      actions.handleLoadedData(this.getProperties());\n      if (onLoadedData) {\n        onLoadedData.apply(void 0, arguments);\n      }\n    } // Fires when the current\n    // playback position has changed\n  }, {\n    key: \"handleTimeUpdate\",\n    value: function handleTimeUpdate() {\n      var _this$props21 = this.props,\n        actions = _this$props21.actions,\n        onTimeUpdate = _this$props21.onTimeUpdate;\n      actions.handleTimeUpdate(this.getProperties());\n      if (onTimeUpdate) {\n        onTimeUpdate.apply(void 0, arguments);\n      }\n    }\n    /**\n     * Fires when the playing speed of the audio/video is changed\n     */\n  }, {\n    key: \"handleRateChange\",\n    value: function handleRateChange() {\n      var _this$props22 = this.props,\n        actions = _this$props22.actions,\n        onRateChange = _this$props22.onRateChange;\n      actions.handleRateChange(this.getProperties());\n      if (onRateChange) {\n        onRateChange.apply(void 0, arguments);\n      }\n    } // Fires when the volume has been changed\n  }, {\n    key: \"handleVolumeChange\",\n    value: function handleVolumeChange() {\n      var _this$props23 = this.props,\n        actions = _this$props23.actions,\n        onVolumeChange = _this$props23.onVolumeChange;\n      actions.handleVolumeChange(this.getProperties());\n      if (onVolumeChange) {\n        onVolumeChange.apply(void 0, arguments);\n      }\n    } // Fires when an error occurred\n    // during the loading of an audio/video\n  }, {\n    key: \"handleError\",\n    value: function handleError() {\n      var _this$props24 = this.props,\n        actions = _this$props24.actions,\n        onError = _this$props24.onError;\n      actions.handleError(this.getProperties());\n      if (onError) {\n        onError.apply(void 0, arguments);\n      }\n    }\n  }, {\n    key: \"handleResize\",\n    value: function handleResize() {\n      var _this$props25 = this.props,\n        actions = _this$props25.actions,\n        onResize = _this$props25.onResize;\n      actions.handleResize(this.getProperties());\n      if (onResize) {\n        onResize.apply(void 0, arguments);\n      }\n    }\n  }, {\n    key: \"handleKeypress\",\n    value: function handleKeypress() {}\n  }, {\n    key: \"renderChildren\",\n    value: function renderChildren() {\n      var _this3 = this;\n      var props = (0, _objectSpread2[\"default\"])({}, this.props, {\n        video: this.video\n      }); // to make sure the children can get video property\n\n      if (!this.video) {\n        return null;\n      } // only keep <source />, <track />, <MyComponent isVideoChild /> elements\n\n      return _react[\"default\"].Children.toArray(this.props.children).filter(_utils.isVideoChild).map(function (c) {\n        var cprops;\n        if (typeof c.type === 'string') {\n          // add onError to <source />\n          if (c.type === 'source') {\n            cprops = (0, _objectSpread2[\"default\"])({}, c.props);\n            var preOnError = cprops.onError;\n            cprops.onError = function () {\n              if (preOnError) {\n                preOnError.apply(void 0, arguments);\n              }\n              _this3.handleError.apply(_this3, arguments);\n            };\n          }\n        } else {\n          cprops = props;\n        }\n        return _react[\"default\"].cloneElement(c, cprops);\n      });\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this4 = this;\n      var _this$props26 = this.props,\n        loop = _this$props26.loop,\n        poster = _this$props26.poster,\n        preload = _this$props26.preload,\n        src = _this$props26.src,\n        autoPlay = _this$props26.autoPlay,\n        playsInline = _this$props26.playsInline,\n        muted = _this$props26.muted,\n        crossOrigin = _this$props26.crossOrigin,\n        videoId = _this$props26.videoId;\n      return _react[\"default\"].createElement(\"video\", {\n        className: (0, _classnames[\"default\"])('video-react-video', this.props.className),\n        id: videoId,\n        crossOrigin: crossOrigin,\n        ref: function ref(c) {\n          _this4.video = c;\n        },\n        muted: muted,\n        preload: preload,\n        loop: loop,\n        playsInline: playsInline,\n        autoPlay: autoPlay,\n        poster: poster,\n        src: src,\n        onLoadStart: this.handleLoadStart,\n        onWaiting: this.handleWaiting,\n        onCanPlay: this.handleCanPlay,\n        onCanPlayThrough: this.handleCanPlayThrough,\n        onPlaying: this.handlePlaying,\n        onEnded: this.handleEnded,\n        onSeeking: this.handleSeeking,\n        onSeeked: this.handleSeeked,\n        onPlay: this.handlePlay,\n        onPause: this.handlePause,\n        onProgress: this.handleProgress,\n        onDurationChange: this.handleDurationChange,\n        onError: this.handleError,\n        onSuspend: this.handleSuspend,\n        onAbort: this.handleAbort,\n        onEmptied: this.handleEmptied,\n        onStalled: this.handleStalled,\n        onLoadedMetadata: this.handleLoadedMetaData,\n        onLoadedData: this.handleLoadedData,\n        onTimeUpdate: this.handleTimeUpdate,\n        onRateChange: this.handleRateChange,\n        onVolumeChange: this.handleVolumeChange,\n        tabIndex: \"-1\"\n      }, this.renderChildren());\n    }\n  }, {\n    key: \"playbackRate\",\n    get: function get() {\n      return this.video.playbackRate;\n    } // set playback rate\n    // speed of video\n    ,\n\n    set: function set(rate) {\n      this.video.playbackRate = rate;\n    }\n  }, {\n    key: \"muted\",\n    get: function get() {\n      return this.video.muted;\n    },\n    set: function set(val) {\n      this.video.muted = val;\n    }\n  }, {\n    key: \"volume\",\n    get: function get() {\n      return this.video.volume;\n    },\n    set: function set(val) {\n      if (val > 1) {\n        val = 1;\n      }\n      if (val < 0) {\n        val = 0;\n      }\n      this.video.volume = val;\n    } // video width\n  }, {\n    key: \"videoWidth\",\n    get: function get() {\n      return this.video.videoWidth;\n    } // video height\n  }, {\n    key: \"videoHeight\",\n    get: function get() {\n      return this.video.videoHeight;\n    }\n  }]);\n  return Video;\n}(_react.Component);\nexports[\"default\"] = Video;\nVideo.propTypes = propTypes;\nVideo.displayName = 'Video';", "map": {"version": 3, "names": ["_interopRequireWildcard", "require", "_interopRequireDefault", "Object", "defineProperty", "exports", "value", "_objectSpread2", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_assertThisInitialized2", "_inherits2", "_propTypes", "_react", "_classnames", "_utils", "propTypes", "actions", "object", "player", "children", "any", "startTime", "number", "loop", "bool", "muted", "autoPlay", "playsInline", "src", "string", "poster", "className", "preload", "oneOf", "crossOrigin", "onLoadStart", "func", "onWaiting", "onCanPlay", "onCanPlayThrough", "onPlaying", "onEnded", "onSeeking", "onSeeked", "onPlay", "onPause", "onProgress", "onDurationChange", "onError", "onSuspend", "onAbort", "onEmptied", "onStalled", "onLoadedMetadata", "onLoadedData", "onTimeUpdate", "onRateChange", "onVolumeChange", "onResize", "Video", "_Component", "props", "_this", "call", "video", "play", "bind", "pause", "seek", "forward", "replay", "toggleFullscreen", "getProperties", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "handleLoadStart", "handleCanPlay", "handleCanPlayThrough", "handlePlay", "handlePlaying", "handlePause", "handleEnded", "handleWaiting", "handleSeeking", "handleSeeked", "handleFullscreenChange", "handleError", "handleSuspend", "handleAbort", "handleEmptied", "handleStalled", "handleLoadedMetaData", "handleLoadedData", "handleTimeUpdate", "handleRateChange", "handleVolumeChange", "handleDurationChange", "handleProgress", "throttle", "handleKeypress", "handleTextTrackChange", "key", "componentDidMount", "forceUpdate", "textTracks", "onaddtrack", "onremovetrack", "_this2", "mediaProperties", "reduce", "properties", "_this$props", "activeTextTrack", "Array", "from", "find", "textTrack", "mode", "activateTextTrack", "promise", "undefined", "then", "load", "addTextTrack", "_this$video", "apply", "arguments", "canPlayType", "_this$video2", "togglePlay", "paused", "time", "currentTime", "e", "seconds", "_this$props2", "_this$props3", "_this$props4", "_this$props5", "_this$props6", "_this$props7", "_this$props8", "_this$props9", "_this$props10", "handleProgressChange", "_this$props11", "handleEnd", "_this$props12", "_this$props13", "_this$props14", "_this$props15", "_this$props16", "_this$props17", "_this$props18", "_this$props19", "_this$props20", "_this$props21", "_this$props22", "_this$props23", "_this$props24", "handleResize", "_this$props25", "_this3", "Children", "toArray", "filter", "isVideoChild", "map", "c", "cprops", "type", "preOnError", "cloneElement", "render", "_this4", "_this$props26", "videoId", "createElement", "id", "ref", "tabIndex", "get", "playbackRate", "set", "rate", "val", "volume", "videoWidth", "videoHeight", "Component", "displayName"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/video-react/lib/components/Video.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\");\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _objectSpread2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectSpread\"));\n\nvar _classCallCheck2 = _interopRequireDefault(require(\"@babel/runtime/helpers/classCallCheck\"));\n\nvar _createClass2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createClass\"));\n\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"@babel/runtime/helpers/possibleConstructorReturn\"));\n\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"@babel/runtime/helpers/getPrototypeOf\"));\n\nvar _assertThisInitialized2 = _interopRequireDefault(require(\"@babel/runtime/helpers/assertThisInitialized\"));\n\nvar _inherits2 = _interopRequireDefault(require(\"@babel/runtime/helpers/inherits\"));\n\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\n\nvar _react = _interopRequireWildcard(require(\"react\"));\n\nvar _classnames = _interopRequireDefault(require(\"classnames\"));\n\nvar _utils = require(\"../utils\");\n\nvar propTypes = {\n  actions: _propTypes[\"default\"].object,\n  player: _propTypes[\"default\"].object,\n  children: _propTypes[\"default\"].any,\n  startTime: _propTypes[\"default\"].number,\n  loop: _propTypes[\"default\"].bool,\n  muted: _propTypes[\"default\"].bool,\n  autoPlay: _propTypes[\"default\"].bool,\n  playsInline: _propTypes[\"default\"].bool,\n  src: _propTypes[\"default\"].string,\n  poster: _propTypes[\"default\"].string,\n  className: _propTypes[\"default\"].string,\n  preload: _propTypes[\"default\"].oneOf(['auto', 'metadata', 'none']),\n  crossOrigin: _propTypes[\"default\"].string,\n  onLoadStart: _propTypes[\"default\"].func,\n  onWaiting: _propTypes[\"default\"].func,\n  onCanPlay: _propTypes[\"default\"].func,\n  onCanPlayThrough: _propTypes[\"default\"].func,\n  onPlaying: _propTypes[\"default\"].func,\n  onEnded: _propTypes[\"default\"].func,\n  onSeeking: _propTypes[\"default\"].func,\n  onSeeked: _propTypes[\"default\"].func,\n  onPlay: _propTypes[\"default\"].func,\n  onPause: _propTypes[\"default\"].func,\n  onProgress: _propTypes[\"default\"].func,\n  onDurationChange: _propTypes[\"default\"].func,\n  onError: _propTypes[\"default\"].func,\n  onSuspend: _propTypes[\"default\"].func,\n  onAbort: _propTypes[\"default\"].func,\n  onEmptied: _propTypes[\"default\"].func,\n  onStalled: _propTypes[\"default\"].func,\n  onLoadedMetadata: _propTypes[\"default\"].func,\n  onLoadedData: _propTypes[\"default\"].func,\n  onTimeUpdate: _propTypes[\"default\"].func,\n  onRateChange: _propTypes[\"default\"].func,\n  onVolumeChange: _propTypes[\"default\"].func,\n  onResize: _propTypes[\"default\"].func\n};\n\nvar Video =\n/*#__PURE__*/\nfunction (_Component) {\n  (0, _inherits2[\"default\"])(Video, _Component);\n\n  function Video(props) {\n    var _this;\n\n    (0, _classCallCheck2[\"default\"])(this, Video);\n    _this = (0, _possibleConstructorReturn2[\"default\"])(this, (0, _getPrototypeOf2[\"default\"])(Video).call(this, props));\n    _this.video = null; // the html5 video\n\n    _this.play = _this.play.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.pause = _this.pause.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.seek = _this.seek.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.forward = _this.forward.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.replay = _this.replay.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.toggleFullscreen = _this.toggleFullscreen.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.getProperties = _this.getProperties.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.renderChildren = _this.renderChildren.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleLoadStart = _this.handleLoadStart.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleCanPlay = _this.handleCanPlay.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleCanPlayThrough = _this.handleCanPlayThrough.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handlePlay = _this.handlePlay.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handlePlaying = _this.handlePlaying.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handlePause = _this.handlePause.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleEnded = _this.handleEnded.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleWaiting = _this.handleWaiting.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleSeeking = _this.handleSeeking.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleSeeked = _this.handleSeeked.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleFullscreenChange = _this.handleFullscreenChange.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleError = _this.handleError.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleSuspend = _this.handleSuspend.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleAbort = _this.handleAbort.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleEmptied = _this.handleEmptied.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleStalled = _this.handleStalled.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleLoadedMetaData = _this.handleLoadedMetaData.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleLoadedData = _this.handleLoadedData.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleTimeUpdate = _this.handleTimeUpdate.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleRateChange = _this.handleRateChange.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleVolumeChange = _this.handleVolumeChange.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleDurationChange = _this.handleDurationChange.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleProgress = (0, _utils.throttle)(_this.handleProgress.bind((0, _assertThisInitialized2[\"default\"])(_this)), 250);\n    _this.handleKeypress = _this.handleKeypress.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    _this.handleTextTrackChange = _this.handleTextTrackChange.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    return _this;\n  }\n\n  (0, _createClass2[\"default\"])(Video, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      this.forceUpdate(); // make sure the children can get the video property\n\n      if (this.video && this.video.textTracks) {\n        this.video.textTracks.onaddtrack = this.handleTextTrackChange;\n        this.video.textTracks.onremovetrack = this.handleTextTrackChange;\n      }\n    } // get all video properties\n\n  }, {\n    key: \"getProperties\",\n    value: function getProperties() {\n      var _this2 = this;\n\n      if (!this.video) {\n        return null;\n      }\n\n      return _utils.mediaProperties.reduce(function (properties, key) {\n        properties[key] = _this2.video[key];\n        return properties;\n      }, {});\n    } // get playback rate\n\n  }, {\n    key: \"handleTextTrackChange\",\n    value: function handleTextTrackChange() {\n      var _this$props = this.props,\n          actions = _this$props.actions,\n          player = _this$props.player;\n\n      if (this.video && this.video.textTracks) {\n        var activeTextTrack = Array.from(this.video.textTracks).find(function (textTrack) {\n          return textTrack.mode === 'showing';\n        });\n\n        if (activeTextTrack !== player.activeTextTrack) {\n          actions.activateTextTrack(activeTextTrack);\n        }\n      }\n    } // play the video\n\n  }, {\n    key: \"play\",\n    value: function play() {\n      var promise = this.video.play();\n\n      if (promise !== undefined) {\n        promise[\"catch\"](function () {}).then(function () {});\n      }\n    } // pause the video\n\n  }, {\n    key: \"pause\",\n    value: function pause() {\n      var promise = this.video.pause();\n\n      if (promise !== undefined) {\n        promise[\"catch\"](function () {}).then(function () {});\n      }\n    } // Change the video source and re-load the video:\n\n  }, {\n    key: \"load\",\n    value: function load() {\n      this.video.load();\n    } // Add a new text track to the video\n\n  }, {\n    key: \"addTextTrack\",\n    value: function addTextTrack() {\n      var _this$video;\n\n      (_this$video = this.video).addTextTrack.apply(_this$video, arguments);\n    } // Check if your browser can play different types of video:\n\n  }, {\n    key: \"canPlayType\",\n    value: function canPlayType() {\n      var _this$video2;\n\n      (_this$video2 = this.video).canPlayType.apply(_this$video2, arguments);\n    } // toggle play\n\n  }, {\n    key: \"togglePlay\",\n    value: function togglePlay() {\n      if (this.video.paused) {\n        this.play();\n      } else {\n        this.pause();\n      }\n    } // seek video by time\n\n  }, {\n    key: \"seek\",\n    value: function seek(time) {\n      try {\n        this.video.currentTime = time;\n      } catch (e) {// console.log(e, 'Video is not ready.')\n      }\n    } // jump forward x seconds\n\n  }, {\n    key: \"forward\",\n    value: function forward(seconds) {\n      this.seek(this.video.currentTime + seconds);\n    } // jump back x seconds\n\n  }, {\n    key: \"replay\",\n    value: function replay(seconds) {\n      this.forward(-seconds);\n    } // enter or exist full screen\n\n  }, {\n    key: \"toggleFullscreen\",\n    value: function toggleFullscreen() {\n      var _this$props2 = this.props,\n          player = _this$props2.player,\n          actions = _this$props2.actions;\n      actions.toggleFullscreen(player);\n    } // Fired when the user agent\n    // begins looking for media data\n\n  }, {\n    key: \"handleLoadStart\",\n    value: function handleLoadStart() {\n      var _this$props3 = this.props,\n          actions = _this$props3.actions,\n          onLoadStart = _this$props3.onLoadStart;\n      actions.handleLoadStart(this.getProperties());\n\n      if (onLoadStart) {\n        onLoadStart.apply(void 0, arguments);\n      }\n    } // A handler for events that\n    // signal that waiting has ended\n\n  }, {\n    key: \"handleCanPlay\",\n    value: function handleCanPlay() {\n      var _this$props4 = this.props,\n          actions = _this$props4.actions,\n          onCanPlay = _this$props4.onCanPlay;\n      actions.handleCanPlay(this.getProperties());\n\n      if (onCanPlay) {\n        onCanPlay.apply(void 0, arguments);\n      }\n    } // A handler for events that\n    // signal that waiting has ended\n\n  }, {\n    key: \"handleCanPlayThrough\",\n    value: function handleCanPlayThrough() {\n      var _this$props5 = this.props,\n          actions = _this$props5.actions,\n          onCanPlayThrough = _this$props5.onCanPlayThrough;\n      actions.handleCanPlayThrough(this.getProperties());\n\n      if (onCanPlayThrough) {\n        onCanPlayThrough.apply(void 0, arguments);\n      }\n    } // A handler for events that\n    // signal that waiting has ended\n\n  }, {\n    key: \"handlePlaying\",\n    value: function handlePlaying() {\n      var _this$props6 = this.props,\n          actions = _this$props6.actions,\n          onPlaying = _this$props6.onPlaying;\n      actions.handlePlaying(this.getProperties());\n\n      if (onPlaying) {\n        onPlaying.apply(void 0, arguments);\n      }\n    } // Fired whenever the media has been started\n\n  }, {\n    key: \"handlePlay\",\n    value: function handlePlay() {\n      var _this$props7 = this.props,\n          actions = _this$props7.actions,\n          onPlay = _this$props7.onPlay;\n      actions.handlePlay(this.getProperties());\n\n      if (onPlay) {\n        onPlay.apply(void 0, arguments);\n      }\n    } // Fired whenever the media has been paused\n\n  }, {\n    key: \"handlePause\",\n    value: function handlePause() {\n      var _this$props8 = this.props,\n          actions = _this$props8.actions,\n          onPause = _this$props8.onPause;\n      actions.handlePause(this.getProperties());\n\n      if (onPause) {\n        onPause.apply(void 0, arguments);\n      }\n    } // Fired when the duration of\n    // the media resource is first known or changed\n\n  }, {\n    key: \"handleDurationChange\",\n    value: function handleDurationChange() {\n      var _this$props9 = this.props,\n          actions = _this$props9.actions,\n          onDurationChange = _this$props9.onDurationChange;\n      actions.handleDurationChange(this.getProperties());\n\n      if (onDurationChange) {\n        onDurationChange.apply(void 0, arguments);\n      }\n    } // Fired while the user agent\n    // is downloading media data\n\n  }, {\n    key: \"handleProgress\",\n    value: function handleProgress() {\n      var _this$props10 = this.props,\n          actions = _this$props10.actions,\n          onProgress = _this$props10.onProgress;\n\n      if (this.video) {\n        actions.handleProgressChange(this.getProperties());\n      }\n\n      if (onProgress) {\n        onProgress.apply(void 0, arguments);\n      }\n    } // Fired when the end of the media resource\n    // is reached (currentTime == duration)\n\n  }, {\n    key: \"handleEnded\",\n    value: function handleEnded() {\n      var _this$props11 = this.props,\n          loop = _this$props11.loop,\n          player = _this$props11.player,\n          actions = _this$props11.actions,\n          onEnded = _this$props11.onEnded;\n\n      if (loop) {\n        this.seek(0);\n        this.play();\n      } else if (!player.paused) {\n        this.pause();\n      }\n\n      actions.handleEnd(this.getProperties());\n\n      if (onEnded) {\n        onEnded.apply(void 0, arguments);\n      }\n    } // Fired whenever the media begins waiting\n\n  }, {\n    key: \"handleWaiting\",\n    value: function handleWaiting() {\n      var _this$props12 = this.props,\n          actions = _this$props12.actions,\n          onWaiting = _this$props12.onWaiting;\n      actions.handleWaiting(this.getProperties());\n\n      if (onWaiting) {\n        onWaiting.apply(void 0, arguments);\n      }\n    } // Fired whenever the player\n    // is jumping to a new time\n\n  }, {\n    key: \"handleSeeking\",\n    value: function handleSeeking() {\n      var _this$props13 = this.props,\n          actions = _this$props13.actions,\n          onSeeking = _this$props13.onSeeking;\n      actions.handleSeeking(this.getProperties());\n\n      if (onSeeking) {\n        onSeeking.apply(void 0, arguments);\n      }\n    } // Fired when the player has\n    // finished jumping to a new time\n\n  }, {\n    key: \"handleSeeked\",\n    value: function handleSeeked() {\n      var _this$props14 = this.props,\n          actions = _this$props14.actions,\n          onSeeked = _this$props14.onSeeked;\n      actions.handleSeeked(this.getProperties());\n\n      if (onSeeked) {\n        onSeeked.apply(void 0, arguments);\n      }\n    } // Handle Fullscreen Change\n\n  }, {\n    key: \"handleFullscreenChange\",\n    value: function handleFullscreenChange() {} // Fires when the browser is\n    // intentionally not getting media data\n\n  }, {\n    key: \"handleSuspend\",\n    value: function handleSuspend() {\n      var _this$props15 = this.props,\n          actions = _this$props15.actions,\n          onSuspend = _this$props15.onSuspend;\n      actions.handleSuspend(this.getProperties());\n\n      if (onSuspend) {\n        onSuspend.apply(void 0, arguments);\n      }\n    } // Fires when the loading of an audio/video is aborted\n\n  }, {\n    key: \"handleAbort\",\n    value: function handleAbort() {\n      var _this$props16 = this.props,\n          actions = _this$props16.actions,\n          onAbort = _this$props16.onAbort;\n      actions.handleAbort(this.getProperties());\n\n      if (onAbort) {\n        onAbort.apply(void 0, arguments);\n      }\n    } // Fires when the current playlist is empty\n\n  }, {\n    key: \"handleEmptied\",\n    value: function handleEmptied() {\n      var _this$props17 = this.props,\n          actions = _this$props17.actions,\n          onEmptied = _this$props17.onEmptied;\n      actions.handleEmptied(this.getProperties());\n\n      if (onEmptied) {\n        onEmptied.apply(void 0, arguments);\n      }\n    } // Fires when the browser is trying to\n    // get media data, but data is not available\n\n  }, {\n    key: \"handleStalled\",\n    value: function handleStalled() {\n      var _this$props18 = this.props,\n          actions = _this$props18.actions,\n          onStalled = _this$props18.onStalled;\n      actions.handleStalled(this.getProperties());\n\n      if (onStalled) {\n        onStalled.apply(void 0, arguments);\n      }\n    } // Fires when the browser has loaded\n    // meta data for the audio/video\n\n  }, {\n    key: \"handleLoadedMetaData\",\n    value: function handleLoadedMetaData() {\n      var _this$props19 = this.props,\n          actions = _this$props19.actions,\n          onLoadedMetadata = _this$props19.onLoadedMetadata,\n          startTime = _this$props19.startTime;\n\n      if (startTime && startTime > 0) {\n        this.video.currentTime = startTime;\n      }\n\n      actions.handleLoadedMetaData(this.getProperties());\n\n      if (onLoadedMetadata) {\n        onLoadedMetadata.apply(void 0, arguments);\n      }\n    } // Fires when the browser has loaded\n    // the current frame of the audio/video\n\n  }, {\n    key: \"handleLoadedData\",\n    value: function handleLoadedData() {\n      var _this$props20 = this.props,\n          actions = _this$props20.actions,\n          onLoadedData = _this$props20.onLoadedData;\n      actions.handleLoadedData(this.getProperties());\n\n      if (onLoadedData) {\n        onLoadedData.apply(void 0, arguments);\n      }\n    } // Fires when the current\n    // playback position has changed\n\n  }, {\n    key: \"handleTimeUpdate\",\n    value: function handleTimeUpdate() {\n      var _this$props21 = this.props,\n          actions = _this$props21.actions,\n          onTimeUpdate = _this$props21.onTimeUpdate;\n      actions.handleTimeUpdate(this.getProperties());\n\n      if (onTimeUpdate) {\n        onTimeUpdate.apply(void 0, arguments);\n      }\n    }\n    /**\n     * Fires when the playing speed of the audio/video is changed\n     */\n\n  }, {\n    key: \"handleRateChange\",\n    value: function handleRateChange() {\n      var _this$props22 = this.props,\n          actions = _this$props22.actions,\n          onRateChange = _this$props22.onRateChange;\n      actions.handleRateChange(this.getProperties());\n\n      if (onRateChange) {\n        onRateChange.apply(void 0, arguments);\n      }\n    } // Fires when the volume has been changed\n\n  }, {\n    key: \"handleVolumeChange\",\n    value: function handleVolumeChange() {\n      var _this$props23 = this.props,\n          actions = _this$props23.actions,\n          onVolumeChange = _this$props23.onVolumeChange;\n      actions.handleVolumeChange(this.getProperties());\n\n      if (onVolumeChange) {\n        onVolumeChange.apply(void 0, arguments);\n      }\n    } // Fires when an error occurred\n    // during the loading of an audio/video\n\n  }, {\n    key: \"handleError\",\n    value: function handleError() {\n      var _this$props24 = this.props,\n          actions = _this$props24.actions,\n          onError = _this$props24.onError;\n      actions.handleError(this.getProperties());\n\n      if (onError) {\n        onError.apply(void 0, arguments);\n      }\n    }\n  }, {\n    key: \"handleResize\",\n    value: function handleResize() {\n      var _this$props25 = this.props,\n          actions = _this$props25.actions,\n          onResize = _this$props25.onResize;\n      actions.handleResize(this.getProperties());\n\n      if (onResize) {\n        onResize.apply(void 0, arguments);\n      }\n    }\n  }, {\n    key: \"handleKeypress\",\n    value: function handleKeypress() {}\n  }, {\n    key: \"renderChildren\",\n    value: function renderChildren() {\n      var _this3 = this;\n\n      var props = (0, _objectSpread2[\"default\"])({}, this.props, {\n        video: this.video\n      }); // to make sure the children can get video property\n\n      if (!this.video) {\n        return null;\n      } // only keep <source />, <track />, <MyComponent isVideoChild /> elements\n\n\n      return _react[\"default\"].Children.toArray(this.props.children).filter(_utils.isVideoChild).map(function (c) {\n        var cprops;\n\n        if (typeof c.type === 'string') {\n          // add onError to <source />\n          if (c.type === 'source') {\n            cprops = (0, _objectSpread2[\"default\"])({}, c.props);\n            var preOnError = cprops.onError;\n\n            cprops.onError = function () {\n              if (preOnError) {\n                preOnError.apply(void 0, arguments);\n              }\n\n              _this3.handleError.apply(_this3, arguments);\n            };\n          }\n        } else {\n          cprops = props;\n        }\n\n        return _react[\"default\"].cloneElement(c, cprops);\n      });\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this4 = this;\n\n      var _this$props26 = this.props,\n          loop = _this$props26.loop,\n          poster = _this$props26.poster,\n          preload = _this$props26.preload,\n          src = _this$props26.src,\n          autoPlay = _this$props26.autoPlay,\n          playsInline = _this$props26.playsInline,\n          muted = _this$props26.muted,\n          crossOrigin = _this$props26.crossOrigin,\n          videoId = _this$props26.videoId;\n      return _react[\"default\"].createElement(\"video\", {\n        className: (0, _classnames[\"default\"])('video-react-video', this.props.className),\n        id: videoId,\n        crossOrigin: crossOrigin,\n        ref: function ref(c) {\n          _this4.video = c;\n        },\n        muted: muted,\n        preload: preload,\n        loop: loop,\n        playsInline: playsInline,\n        autoPlay: autoPlay,\n        poster: poster,\n        src: src,\n        onLoadStart: this.handleLoadStart,\n        onWaiting: this.handleWaiting,\n        onCanPlay: this.handleCanPlay,\n        onCanPlayThrough: this.handleCanPlayThrough,\n        onPlaying: this.handlePlaying,\n        onEnded: this.handleEnded,\n        onSeeking: this.handleSeeking,\n        onSeeked: this.handleSeeked,\n        onPlay: this.handlePlay,\n        onPause: this.handlePause,\n        onProgress: this.handleProgress,\n        onDurationChange: this.handleDurationChange,\n        onError: this.handleError,\n        onSuspend: this.handleSuspend,\n        onAbort: this.handleAbort,\n        onEmptied: this.handleEmptied,\n        onStalled: this.handleStalled,\n        onLoadedMetadata: this.handleLoadedMetaData,\n        onLoadedData: this.handleLoadedData,\n        onTimeUpdate: this.handleTimeUpdate,\n        onRateChange: this.handleRateChange,\n        onVolumeChange: this.handleVolumeChange,\n        tabIndex: \"-1\"\n      }, this.renderChildren());\n    }\n  }, {\n    key: \"playbackRate\",\n    get: function get() {\n      return this.video.playbackRate;\n    } // set playback rate\n    // speed of video\n    ,\n    set: function set(rate) {\n      this.video.playbackRate = rate;\n    }\n  }, {\n    key: \"muted\",\n    get: function get() {\n      return this.video.muted;\n    },\n    set: function set(val) {\n      this.video.muted = val;\n    }\n  }, {\n    key: \"volume\",\n    get: function get() {\n      return this.video.volume;\n    },\n    set: function set(val) {\n      if (val > 1) {\n        val = 1;\n      }\n\n      if (val < 0) {\n        val = 0;\n      }\n\n      this.video.volume = val;\n    } // video width\n\n  }, {\n    key: \"videoWidth\",\n    get: function get() {\n      return this.video.videoWidth;\n    } // video height\n\n  }, {\n    key: \"videoHeight\",\n    get: function get() {\n      return this.video.videoHeight;\n    }\n  }]);\n  return Video;\n}(_react.Component);\n\nexports[\"default\"] = Video;\nVideo.propTypes = propTypes;\nVideo.displayName = 'Video';"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,uBAAuB,GAAGC,OAAO,CAAC,+CAA+C,CAAC;AAEtF,IAAIC,sBAAsB,GAAGD,OAAO,CAAC,8CAA8C,CAAC;AAEpFE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;AAE3B,IAAIE,cAAc,GAAGL,sBAAsB,CAACD,OAAO,CAAC,qCAAqC,CAAC,CAAC;AAE3F,IAAIO,gBAAgB,GAAGN,sBAAsB,CAACD,OAAO,CAAC,uCAAuC,CAAC,CAAC;AAE/F,IAAIQ,aAAa,GAAGP,sBAAsB,CAACD,OAAO,CAAC,oCAAoC,CAAC,CAAC;AAEzF,IAAIS,2BAA2B,GAAGR,sBAAsB,CAACD,OAAO,CAAC,kDAAkD,CAAC,CAAC;AAErH,IAAIU,gBAAgB,GAAGT,sBAAsB,CAACD,OAAO,CAAC,uCAAuC,CAAC,CAAC;AAE/F,IAAIW,uBAAuB,GAAGV,sBAAsB,CAACD,OAAO,CAAC,8CAA8C,CAAC,CAAC;AAE7G,IAAIY,UAAU,GAAGX,sBAAsB,CAACD,OAAO,CAAC,iCAAiC,CAAC,CAAC;AAEnF,IAAIa,UAAU,GAAGZ,sBAAsB,CAACD,OAAO,CAAC,YAAY,CAAC,CAAC;AAE9D,IAAIc,MAAM,GAAGf,uBAAuB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AAEtD,IAAIe,WAAW,GAAGd,sBAAsB,CAACD,OAAO,CAAC,YAAY,CAAC,CAAC;AAE/D,IAAIgB,MAAM,GAAGhB,OAAO,CAAC,UAAU,CAAC;AAEhC,IAAIiB,SAAS,GAAG;EACdC,OAAO,EAAEL,UAAU,CAAC,SAAS,CAAC,CAACM,MAAM;EACrCC,MAAM,EAAEP,UAAU,CAAC,SAAS,CAAC,CAACM,MAAM;EACpCE,QAAQ,EAAER,UAAU,CAAC,SAAS,CAAC,CAACS,GAAG;EACnCC,SAAS,EAAEV,UAAU,CAAC,SAAS,CAAC,CAACW,MAAM;EACvCC,IAAI,EAAEZ,UAAU,CAAC,SAAS,CAAC,CAACa,IAAI;EAChCC,KAAK,EAAEd,UAAU,CAAC,SAAS,CAAC,CAACa,IAAI;EACjCE,QAAQ,EAAEf,UAAU,CAAC,SAAS,CAAC,CAACa,IAAI;EACpCG,WAAW,EAAEhB,UAAU,CAAC,SAAS,CAAC,CAACa,IAAI;EACvCI,GAAG,EAAEjB,UAAU,CAAC,SAAS,CAAC,CAACkB,MAAM;EACjCC,MAAM,EAAEnB,UAAU,CAAC,SAAS,CAAC,CAACkB,MAAM;EACpCE,SAAS,EAAEpB,UAAU,CAAC,SAAS,CAAC,CAACkB,MAAM;EACvCG,OAAO,EAAErB,UAAU,CAAC,SAAS,CAAC,CAACsB,KAAK,CAAC,CAAC,MAAM,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;EAClEC,WAAW,EAAEvB,UAAU,CAAC,SAAS,CAAC,CAACkB,MAAM;EACzCM,WAAW,EAAExB,UAAU,CAAC,SAAS,CAAC,CAACyB,IAAI;EACvCC,SAAS,EAAE1B,UAAU,CAAC,SAAS,CAAC,CAACyB,IAAI;EACrCE,SAAS,EAAE3B,UAAU,CAAC,SAAS,CAAC,CAACyB,IAAI;EACrCG,gBAAgB,EAAE5B,UAAU,CAAC,SAAS,CAAC,CAACyB,IAAI;EAC5CI,SAAS,EAAE7B,UAAU,CAAC,SAAS,CAAC,CAACyB,IAAI;EACrCK,OAAO,EAAE9B,UAAU,CAAC,SAAS,CAAC,CAACyB,IAAI;EACnCM,SAAS,EAAE/B,UAAU,CAAC,SAAS,CAAC,CAACyB,IAAI;EACrCO,QAAQ,EAAEhC,UAAU,CAAC,SAAS,CAAC,CAACyB,IAAI;EACpCQ,MAAM,EAAEjC,UAAU,CAAC,SAAS,CAAC,CAACyB,IAAI;EAClCS,OAAO,EAAElC,UAAU,CAAC,SAAS,CAAC,CAACyB,IAAI;EACnCU,UAAU,EAAEnC,UAAU,CAAC,SAAS,CAAC,CAACyB,IAAI;EACtCW,gBAAgB,EAAEpC,UAAU,CAAC,SAAS,CAAC,CAACyB,IAAI;EAC5CY,OAAO,EAAErC,UAAU,CAAC,SAAS,CAAC,CAACyB,IAAI;EACnCa,SAAS,EAAEtC,UAAU,CAAC,SAAS,CAAC,CAACyB,IAAI;EACrCc,OAAO,EAAEvC,UAAU,CAAC,SAAS,CAAC,CAACyB,IAAI;EACnCe,SAAS,EAAExC,UAAU,CAAC,SAAS,CAAC,CAACyB,IAAI;EACrCgB,SAAS,EAAEzC,UAAU,CAAC,SAAS,CAAC,CAACyB,IAAI;EACrCiB,gBAAgB,EAAE1C,UAAU,CAAC,SAAS,CAAC,CAACyB,IAAI;EAC5CkB,YAAY,EAAE3C,UAAU,CAAC,SAAS,CAAC,CAACyB,IAAI;EACxCmB,YAAY,EAAE5C,UAAU,CAAC,SAAS,CAAC,CAACyB,IAAI;EACxCoB,YAAY,EAAE7C,UAAU,CAAC,SAAS,CAAC,CAACyB,IAAI;EACxCqB,cAAc,EAAE9C,UAAU,CAAC,SAAS,CAAC,CAACyB,IAAI;EAC1CsB,QAAQ,EAAE/C,UAAU,CAAC,SAAS,CAAC,CAACyB;AAClC,CAAC;AAED,IAAIuB,KAAK,GACT;AACA,UAAUC,UAAU,EAAE;EACpB,CAAC,CAAC,EAAElD,UAAU,CAAC,SAAS,CAAC,EAAEiD,KAAK,EAAEC,UAAU,CAAC;EAE7C,SAASD,KAAKA,CAACE,KAAK,EAAE;IACpB,IAAIC,KAAK;IAET,CAAC,CAAC,EAAEzD,gBAAgB,CAAC,SAAS,CAAC,EAAE,IAAI,EAAEsD,KAAK,CAAC;IAC7CG,KAAK,GAAG,CAAC,CAAC,EAAEvD,2BAA2B,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAEC,gBAAgB,CAAC,SAAS,CAAC,EAAEmD,KAAK,CAAC,CAACI,IAAI,CAAC,IAAI,EAAEF,KAAK,CAAC,CAAC;IACpHC,KAAK,CAACE,KAAK,GAAG,IAAI,CAAC,CAAC;;IAEpBF,KAAK,CAACG,IAAI,GAAGH,KAAK,CAACG,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,EAAEzD,uBAAuB,CAAC,SAAS,CAAC,EAAEqD,KAAK,CAAC,CAAC;IAC5EA,KAAK,CAACK,KAAK,GAAGL,KAAK,CAACK,KAAK,CAACD,IAAI,CAAC,CAAC,CAAC,EAAEzD,uBAAuB,CAAC,SAAS,CAAC,EAAEqD,KAAK,CAAC,CAAC;IAC9EA,KAAK,CAACM,IAAI,GAAGN,KAAK,CAACM,IAAI,CAACF,IAAI,CAAC,CAAC,CAAC,EAAEzD,uBAAuB,CAAC,SAAS,CAAC,EAAEqD,KAAK,CAAC,CAAC;IAC5EA,KAAK,CAACO,OAAO,GAAGP,KAAK,CAACO,OAAO,CAACH,IAAI,CAAC,CAAC,CAAC,EAAEzD,uBAAuB,CAAC,SAAS,CAAC,EAAEqD,KAAK,CAAC,CAAC;IAClFA,KAAK,CAACQ,MAAM,GAAGR,KAAK,CAACQ,MAAM,CAACJ,IAAI,CAAC,CAAC,CAAC,EAAEzD,uBAAuB,CAAC,SAAS,CAAC,EAAEqD,KAAK,CAAC,CAAC;IAChFA,KAAK,CAACS,gBAAgB,GAAGT,KAAK,CAACS,gBAAgB,CAACL,IAAI,CAAC,CAAC,CAAC,EAAEzD,uBAAuB,CAAC,SAAS,CAAC,EAAEqD,KAAK,CAAC,CAAC;IACpGA,KAAK,CAACU,aAAa,GAAGV,KAAK,CAACU,aAAa,CAACN,IAAI,CAAC,CAAC,CAAC,EAAEzD,uBAAuB,CAAC,SAAS,CAAC,EAAEqD,KAAK,CAAC,CAAC;IAC9FA,KAAK,CAACW,cAAc,GAAGX,KAAK,CAACW,cAAc,CAACP,IAAI,CAAC,CAAC,CAAC,EAAEzD,uBAAuB,CAAC,SAAS,CAAC,EAAEqD,KAAK,CAAC,CAAC;IAChGA,KAAK,CAACY,eAAe,GAAGZ,KAAK,CAACY,eAAe,CAACR,IAAI,CAAC,CAAC,CAAC,EAAEzD,uBAAuB,CAAC,SAAS,CAAC,EAAEqD,KAAK,CAAC,CAAC;IAClGA,KAAK,CAACa,aAAa,GAAGb,KAAK,CAACa,aAAa,CAACT,IAAI,CAAC,CAAC,CAAC,EAAEzD,uBAAuB,CAAC,SAAS,CAAC,EAAEqD,KAAK,CAAC,CAAC;IAC9FA,KAAK,CAACc,oBAAoB,GAAGd,KAAK,CAACc,oBAAoB,CAACV,IAAI,CAAC,CAAC,CAAC,EAAEzD,uBAAuB,CAAC,SAAS,CAAC,EAAEqD,KAAK,CAAC,CAAC;IAC5GA,KAAK,CAACe,UAAU,GAAGf,KAAK,CAACe,UAAU,CAACX,IAAI,CAAC,CAAC,CAAC,EAAEzD,uBAAuB,CAAC,SAAS,CAAC,EAAEqD,KAAK,CAAC,CAAC;IACxFA,KAAK,CAACgB,aAAa,GAAGhB,KAAK,CAACgB,aAAa,CAACZ,IAAI,CAAC,CAAC,CAAC,EAAEzD,uBAAuB,CAAC,SAAS,CAAC,EAAEqD,KAAK,CAAC,CAAC;IAC9FA,KAAK,CAACiB,WAAW,GAAGjB,KAAK,CAACiB,WAAW,CAACb,IAAI,CAAC,CAAC,CAAC,EAAEzD,uBAAuB,CAAC,SAAS,CAAC,EAAEqD,KAAK,CAAC,CAAC;IAC1FA,KAAK,CAACkB,WAAW,GAAGlB,KAAK,CAACkB,WAAW,CAACd,IAAI,CAAC,CAAC,CAAC,EAAEzD,uBAAuB,CAAC,SAAS,CAAC,EAAEqD,KAAK,CAAC,CAAC;IAC1FA,KAAK,CAACmB,aAAa,GAAGnB,KAAK,CAACmB,aAAa,CAACf,IAAI,CAAC,CAAC,CAAC,EAAEzD,uBAAuB,CAAC,SAAS,CAAC,EAAEqD,KAAK,CAAC,CAAC;IAC9FA,KAAK,CAACoB,aAAa,GAAGpB,KAAK,CAACoB,aAAa,CAAChB,IAAI,CAAC,CAAC,CAAC,EAAEzD,uBAAuB,CAAC,SAAS,CAAC,EAAEqD,KAAK,CAAC,CAAC;IAC9FA,KAAK,CAACqB,YAAY,GAAGrB,KAAK,CAACqB,YAAY,CAACjB,IAAI,CAAC,CAAC,CAAC,EAAEzD,uBAAuB,CAAC,SAAS,CAAC,EAAEqD,KAAK,CAAC,CAAC;IAC5FA,KAAK,CAACsB,sBAAsB,GAAGtB,KAAK,CAACsB,sBAAsB,CAAClB,IAAI,CAAC,CAAC,CAAC,EAAEzD,uBAAuB,CAAC,SAAS,CAAC,EAAEqD,KAAK,CAAC,CAAC;IAChHA,KAAK,CAACuB,WAAW,GAAGvB,KAAK,CAACuB,WAAW,CAACnB,IAAI,CAAC,CAAC,CAAC,EAAEzD,uBAAuB,CAAC,SAAS,CAAC,EAAEqD,KAAK,CAAC,CAAC;IAC1FA,KAAK,CAACwB,aAAa,GAAGxB,KAAK,CAACwB,aAAa,CAACpB,IAAI,CAAC,CAAC,CAAC,EAAEzD,uBAAuB,CAAC,SAAS,CAAC,EAAEqD,KAAK,CAAC,CAAC;IAC9FA,KAAK,CAACyB,WAAW,GAAGzB,KAAK,CAACyB,WAAW,CAACrB,IAAI,CAAC,CAAC,CAAC,EAAEzD,uBAAuB,CAAC,SAAS,CAAC,EAAEqD,KAAK,CAAC,CAAC;IAC1FA,KAAK,CAAC0B,aAAa,GAAG1B,KAAK,CAAC0B,aAAa,CAACtB,IAAI,CAAC,CAAC,CAAC,EAAEzD,uBAAuB,CAAC,SAAS,CAAC,EAAEqD,KAAK,CAAC,CAAC;IAC9FA,KAAK,CAAC2B,aAAa,GAAG3B,KAAK,CAAC2B,aAAa,CAACvB,IAAI,CAAC,CAAC,CAAC,EAAEzD,uBAAuB,CAAC,SAAS,CAAC,EAAEqD,KAAK,CAAC,CAAC;IAC9FA,KAAK,CAAC4B,oBAAoB,GAAG5B,KAAK,CAAC4B,oBAAoB,CAACxB,IAAI,CAAC,CAAC,CAAC,EAAEzD,uBAAuB,CAAC,SAAS,CAAC,EAAEqD,KAAK,CAAC,CAAC;IAC5GA,KAAK,CAAC6B,gBAAgB,GAAG7B,KAAK,CAAC6B,gBAAgB,CAACzB,IAAI,CAAC,CAAC,CAAC,EAAEzD,uBAAuB,CAAC,SAAS,CAAC,EAAEqD,KAAK,CAAC,CAAC;IACpGA,KAAK,CAAC8B,gBAAgB,GAAG9B,KAAK,CAAC8B,gBAAgB,CAAC1B,IAAI,CAAC,CAAC,CAAC,EAAEzD,uBAAuB,CAAC,SAAS,CAAC,EAAEqD,KAAK,CAAC,CAAC;IACpGA,KAAK,CAAC+B,gBAAgB,GAAG/B,KAAK,CAAC+B,gBAAgB,CAAC3B,IAAI,CAAC,CAAC,CAAC,EAAEzD,uBAAuB,CAAC,SAAS,CAAC,EAAEqD,KAAK,CAAC,CAAC;IACpGA,KAAK,CAACgC,kBAAkB,GAAGhC,KAAK,CAACgC,kBAAkB,CAAC5B,IAAI,CAAC,CAAC,CAAC,EAAEzD,uBAAuB,CAAC,SAAS,CAAC,EAAEqD,KAAK,CAAC,CAAC;IACxGA,KAAK,CAACiC,oBAAoB,GAAGjC,KAAK,CAACiC,oBAAoB,CAAC7B,IAAI,CAAC,CAAC,CAAC,EAAEzD,uBAAuB,CAAC,SAAS,CAAC,EAAEqD,KAAK,CAAC,CAAC;IAC5GA,KAAK,CAACkC,cAAc,GAAG,CAAC,CAAC,EAAElF,MAAM,CAACmF,QAAQ,EAAEnC,KAAK,CAACkC,cAAc,CAAC9B,IAAI,CAAC,CAAC,CAAC,EAAEzD,uBAAuB,CAAC,SAAS,CAAC,EAAEqD,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC;IAC3HA,KAAK,CAACoC,cAAc,GAAGpC,KAAK,CAACoC,cAAc,CAAChC,IAAI,CAAC,CAAC,CAAC,EAAEzD,uBAAuB,CAAC,SAAS,CAAC,EAAEqD,KAAK,CAAC,CAAC;IAChGA,KAAK,CAACqC,qBAAqB,GAAGrC,KAAK,CAACqC,qBAAqB,CAACjC,IAAI,CAAC,CAAC,CAAC,EAAEzD,uBAAuB,CAAC,SAAS,CAAC,EAAEqD,KAAK,CAAC,CAAC;IAC9G,OAAOA,KAAK;EACd;EAEA,CAAC,CAAC,EAAExD,aAAa,CAAC,SAAS,CAAC,EAAEqD,KAAK,EAAE,CAAC;IACpCyC,GAAG,EAAE,mBAAmB;IACxBjG,KAAK,EAAE,SAASkG,iBAAiBA,CAAA,EAAG;MAClC,IAAI,CAACC,WAAW,EAAE,CAAC,CAAC;;MAEpB,IAAI,IAAI,CAACtC,KAAK,IAAI,IAAI,CAACA,KAAK,CAACuC,UAAU,EAAE;QACvC,IAAI,CAACvC,KAAK,CAACuC,UAAU,CAACC,UAAU,GAAG,IAAI,CAACL,qBAAqB;QAC7D,IAAI,CAACnC,KAAK,CAACuC,UAAU,CAACE,aAAa,GAAG,IAAI,CAACN,qBAAqB;MAClE;IACF,CAAC,CAAC;EAEJ,CAAC,EAAE;IACDC,GAAG,EAAE,eAAe;IACpBjG,KAAK,EAAE,SAASqE,aAAaA,CAAA,EAAG;MAC9B,IAAIkC,MAAM,GAAG,IAAI;MAEjB,IAAI,CAAC,IAAI,CAAC1C,KAAK,EAAE;QACf,OAAO,IAAI;MACb;MAEA,OAAOlD,MAAM,CAAC6F,eAAe,CAACC,MAAM,CAAC,UAAUC,UAAU,EAAET,GAAG,EAAE;QAC9DS,UAAU,CAACT,GAAG,CAAC,GAAGM,MAAM,CAAC1C,KAAK,CAACoC,GAAG,CAAC;QACnC,OAAOS,UAAU;MACnB,CAAC,EAAE,CAAC,CAAC,CAAC;IACR,CAAC,CAAC;EAEJ,CAAC,EAAE;IACDT,GAAG,EAAE,uBAAuB;IAC5BjG,KAAK,EAAE,SAASgG,qBAAqBA,CAAA,EAAG;MACtC,IAAIW,WAAW,GAAG,IAAI,CAACjD,KAAK;QACxB7C,OAAO,GAAG8F,WAAW,CAAC9F,OAAO;QAC7BE,MAAM,GAAG4F,WAAW,CAAC5F,MAAM;MAE/B,IAAI,IAAI,CAAC8C,KAAK,IAAI,IAAI,CAACA,KAAK,CAACuC,UAAU,EAAE;QACvC,IAAIQ,eAAe,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAI,CAACjD,KAAK,CAACuC,UAAU,CAAC,CAACW,IAAI,CAAC,UAAUC,SAAS,EAAE;UAChF,OAAOA,SAAS,CAACC,IAAI,KAAK,SAAS;QACrC,CAAC,CAAC;QAEF,IAAIL,eAAe,KAAK7F,MAAM,CAAC6F,eAAe,EAAE;UAC9C/F,OAAO,CAACqG,iBAAiB,CAACN,eAAe,CAAC;QAC5C;MACF;IACF,CAAC,CAAC;EAEJ,CAAC,EAAE;IACDX,GAAG,EAAE,MAAM;IACXjG,KAAK,EAAE,SAAS8D,IAAIA,CAAA,EAAG;MACrB,IAAIqD,OAAO,GAAG,IAAI,CAACtD,KAAK,CAACC,IAAI,EAAE;MAE/B,IAAIqD,OAAO,KAAKC,SAAS,EAAE;QACzBD,OAAO,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAACE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;MACvD;IACF,CAAC,CAAC;EAEJ,CAAC,EAAE;IACDpB,GAAG,EAAE,OAAO;IACZjG,KAAK,EAAE,SAASgE,KAAKA,CAAA,EAAG;MACtB,IAAImD,OAAO,GAAG,IAAI,CAACtD,KAAK,CAACG,KAAK,EAAE;MAEhC,IAAImD,OAAO,KAAKC,SAAS,EAAE;QACzBD,OAAO,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAACE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;MACvD;IACF,CAAC,CAAC;EAEJ,CAAC,EAAE;IACDpB,GAAG,EAAE,MAAM;IACXjG,KAAK,EAAE,SAASsH,IAAIA,CAAA,EAAG;MACrB,IAAI,CAACzD,KAAK,CAACyD,IAAI,EAAE;IACnB,CAAC,CAAC;EAEJ,CAAC,EAAE;IACDrB,GAAG,EAAE,cAAc;IACnBjG,KAAK,EAAE,SAASuH,YAAYA,CAAA,EAAG;MAC7B,IAAIC,WAAW;MAEf,CAACA,WAAW,GAAG,IAAI,CAAC3D,KAAK,EAAE0D,YAAY,CAACE,KAAK,CAACD,WAAW,EAAEE,SAAS,CAAC;IACvE,CAAC,CAAC;EAEJ,CAAC,EAAE;IACDzB,GAAG,EAAE,aAAa;IAClBjG,KAAK,EAAE,SAAS2H,WAAWA,CAAA,EAAG;MAC5B,IAAIC,YAAY;MAEhB,CAACA,YAAY,GAAG,IAAI,CAAC/D,KAAK,EAAE8D,WAAW,CAACF,KAAK,CAACG,YAAY,EAAEF,SAAS,CAAC;IACxE,CAAC,CAAC;EAEJ,CAAC,EAAE;IACDzB,GAAG,EAAE,YAAY;IACjBjG,KAAK,EAAE,SAAS6H,UAAUA,CAAA,EAAG;MAC3B,IAAI,IAAI,CAAChE,KAAK,CAACiE,MAAM,EAAE;QACrB,IAAI,CAAChE,IAAI,EAAE;MACb,CAAC,MAAM;QACL,IAAI,CAACE,KAAK,EAAE;MACd;IACF,CAAC,CAAC;EAEJ,CAAC,EAAE;IACDiC,GAAG,EAAE,MAAM;IACXjG,KAAK,EAAE,SAASiE,IAAIA,CAAC8D,IAAI,EAAE;MACzB,IAAI;QACF,IAAI,CAAClE,KAAK,CAACmE,WAAW,GAAGD,IAAI;MAC/B,CAAC,CAAC,OAAOE,CAAC,EAAE,CAAC;MAAA;IAEf,CAAC,CAAC;EAEJ,CAAC,EAAE;IACDhC,GAAG,EAAE,SAAS;IACdjG,KAAK,EAAE,SAASkE,OAAOA,CAACgE,OAAO,EAAE;MAC/B,IAAI,CAACjE,IAAI,CAAC,IAAI,CAACJ,KAAK,CAACmE,WAAW,GAAGE,OAAO,CAAC;IAC7C,CAAC,CAAC;EAEJ,CAAC,EAAE;IACDjC,GAAG,EAAE,QAAQ;IACbjG,KAAK,EAAE,SAASmE,MAAMA,CAAC+D,OAAO,EAAE;MAC9B,IAAI,CAAChE,OAAO,CAAC,CAACgE,OAAO,CAAC;IACxB,CAAC,CAAC;EAEJ,CAAC,EAAE;IACDjC,GAAG,EAAE,kBAAkB;IACvBjG,KAAK,EAAE,SAASoE,gBAAgBA,CAAA,EAAG;MACjC,IAAI+D,YAAY,GAAG,IAAI,CAACzE,KAAK;QACzB3C,MAAM,GAAGoH,YAAY,CAACpH,MAAM;QAC5BF,OAAO,GAAGsH,YAAY,CAACtH,OAAO;MAClCA,OAAO,CAACuD,gBAAgB,CAACrD,MAAM,CAAC;IAClC,CAAC,CAAC;IACF;EAEF,CAAC,EAAE;IACDkF,GAAG,EAAE,iBAAiB;IACtBjG,KAAK,EAAE,SAASuE,eAAeA,CAAA,EAAG;MAChC,IAAI6D,YAAY,GAAG,IAAI,CAAC1E,KAAK;QACzB7C,OAAO,GAAGuH,YAAY,CAACvH,OAAO;QAC9BmB,WAAW,GAAGoG,YAAY,CAACpG,WAAW;MAC1CnB,OAAO,CAAC0D,eAAe,CAAC,IAAI,CAACF,aAAa,EAAE,CAAC;MAE7C,IAAIrC,WAAW,EAAE;QACfA,WAAW,CAACyF,KAAK,CAAC,KAAK,CAAC,EAAEC,SAAS,CAAC;MACtC;IACF,CAAC,CAAC;IACF;EAEF,CAAC,EAAE;IACDzB,GAAG,EAAE,eAAe;IACpBjG,KAAK,EAAE,SAASwE,aAAaA,CAAA,EAAG;MAC9B,IAAI6D,YAAY,GAAG,IAAI,CAAC3E,KAAK;QACzB7C,OAAO,GAAGwH,YAAY,CAACxH,OAAO;QAC9BsB,SAAS,GAAGkG,YAAY,CAAClG,SAAS;MACtCtB,OAAO,CAAC2D,aAAa,CAAC,IAAI,CAACH,aAAa,EAAE,CAAC;MAE3C,IAAIlC,SAAS,EAAE;QACbA,SAAS,CAACsF,KAAK,CAAC,KAAK,CAAC,EAAEC,SAAS,CAAC;MACpC;IACF,CAAC,CAAC;IACF;EAEF,CAAC,EAAE;IACDzB,GAAG,EAAE,sBAAsB;IAC3BjG,KAAK,EAAE,SAASyE,oBAAoBA,CAAA,EAAG;MACrC,IAAI6D,YAAY,GAAG,IAAI,CAAC5E,KAAK;QACzB7C,OAAO,GAAGyH,YAAY,CAACzH,OAAO;QAC9BuB,gBAAgB,GAAGkG,YAAY,CAAClG,gBAAgB;MACpDvB,OAAO,CAAC4D,oBAAoB,CAAC,IAAI,CAACJ,aAAa,EAAE,CAAC;MAElD,IAAIjC,gBAAgB,EAAE;QACpBA,gBAAgB,CAACqF,KAAK,CAAC,KAAK,CAAC,EAAEC,SAAS,CAAC;MAC3C;IACF,CAAC,CAAC;IACF;EAEF,CAAC,EAAE;IACDzB,GAAG,EAAE,eAAe;IACpBjG,KAAK,EAAE,SAAS2E,aAAaA,CAAA,EAAG;MAC9B,IAAI4D,YAAY,GAAG,IAAI,CAAC7E,KAAK;QACzB7C,OAAO,GAAG0H,YAAY,CAAC1H,OAAO;QAC9BwB,SAAS,GAAGkG,YAAY,CAAClG,SAAS;MACtCxB,OAAO,CAAC8D,aAAa,CAAC,IAAI,CAACN,aAAa,EAAE,CAAC;MAE3C,IAAIhC,SAAS,EAAE;QACbA,SAAS,CAACoF,KAAK,CAAC,KAAK,CAAC,EAAEC,SAAS,CAAC;MACpC;IACF,CAAC,CAAC;EAEJ,CAAC,EAAE;IACDzB,GAAG,EAAE,YAAY;IACjBjG,KAAK,EAAE,SAAS0E,UAAUA,CAAA,EAAG;MAC3B,IAAI8D,YAAY,GAAG,IAAI,CAAC9E,KAAK;QACzB7C,OAAO,GAAG2H,YAAY,CAAC3H,OAAO;QAC9B4B,MAAM,GAAG+F,YAAY,CAAC/F,MAAM;MAChC5B,OAAO,CAAC6D,UAAU,CAAC,IAAI,CAACL,aAAa,EAAE,CAAC;MAExC,IAAI5B,MAAM,EAAE;QACVA,MAAM,CAACgF,KAAK,CAAC,KAAK,CAAC,EAAEC,SAAS,CAAC;MACjC;IACF,CAAC,CAAC;EAEJ,CAAC,EAAE;IACDzB,GAAG,EAAE,aAAa;IAClBjG,KAAK,EAAE,SAAS4E,WAAWA,CAAA,EAAG;MAC5B,IAAI6D,YAAY,GAAG,IAAI,CAAC/E,KAAK;QACzB7C,OAAO,GAAG4H,YAAY,CAAC5H,OAAO;QAC9B6B,OAAO,GAAG+F,YAAY,CAAC/F,OAAO;MAClC7B,OAAO,CAAC+D,WAAW,CAAC,IAAI,CAACP,aAAa,EAAE,CAAC;MAEzC,IAAI3B,OAAO,EAAE;QACXA,OAAO,CAAC+E,KAAK,CAAC,KAAK,CAAC,EAAEC,SAAS,CAAC;MAClC;IACF,CAAC,CAAC;IACF;EAEF,CAAC,EAAE;IACDzB,GAAG,EAAE,sBAAsB;IAC3BjG,KAAK,EAAE,SAAS4F,oBAAoBA,CAAA,EAAG;MACrC,IAAI8C,YAAY,GAAG,IAAI,CAAChF,KAAK;QACzB7C,OAAO,GAAG6H,YAAY,CAAC7H,OAAO;QAC9B+B,gBAAgB,GAAG8F,YAAY,CAAC9F,gBAAgB;MACpD/B,OAAO,CAAC+E,oBAAoB,CAAC,IAAI,CAACvB,aAAa,EAAE,CAAC;MAElD,IAAIzB,gBAAgB,EAAE;QACpBA,gBAAgB,CAAC6E,KAAK,CAAC,KAAK,CAAC,EAAEC,SAAS,CAAC;MAC3C;IACF,CAAC,CAAC;IACF;EAEF,CAAC,EAAE;IACDzB,GAAG,EAAE,gBAAgB;IACrBjG,KAAK,EAAE,SAAS6F,cAAcA,CAAA,EAAG;MAC/B,IAAI8C,aAAa,GAAG,IAAI,CAACjF,KAAK;QAC1B7C,OAAO,GAAG8H,aAAa,CAAC9H,OAAO;QAC/B8B,UAAU,GAAGgG,aAAa,CAAChG,UAAU;MAEzC,IAAI,IAAI,CAACkB,KAAK,EAAE;QACdhD,OAAO,CAAC+H,oBAAoB,CAAC,IAAI,CAACvE,aAAa,EAAE,CAAC;MACpD;MAEA,IAAI1B,UAAU,EAAE;QACdA,UAAU,CAAC8E,KAAK,CAAC,KAAK,CAAC,EAAEC,SAAS,CAAC;MACrC;IACF,CAAC,CAAC;IACF;EAEF,CAAC,EAAE;IACDzB,GAAG,EAAE,aAAa;IAClBjG,KAAK,EAAE,SAAS6E,WAAWA,CAAA,EAAG;MAC5B,IAAIgE,aAAa,GAAG,IAAI,CAACnF,KAAK;QAC1BtC,IAAI,GAAGyH,aAAa,CAACzH,IAAI;QACzBL,MAAM,GAAG8H,aAAa,CAAC9H,MAAM;QAC7BF,OAAO,GAAGgI,aAAa,CAAChI,OAAO;QAC/ByB,OAAO,GAAGuG,aAAa,CAACvG,OAAO;MAEnC,IAAIlB,IAAI,EAAE;QACR,IAAI,CAAC6C,IAAI,CAAC,CAAC,CAAC;QACZ,IAAI,CAACH,IAAI,EAAE;MACb,CAAC,MAAM,IAAI,CAAC/C,MAAM,CAAC+G,MAAM,EAAE;QACzB,IAAI,CAAC9D,KAAK,EAAE;MACd;MAEAnD,OAAO,CAACiI,SAAS,CAAC,IAAI,CAACzE,aAAa,EAAE,CAAC;MAEvC,IAAI/B,OAAO,EAAE;QACXA,OAAO,CAACmF,KAAK,CAAC,KAAK,CAAC,EAAEC,SAAS,CAAC;MAClC;IACF,CAAC,CAAC;EAEJ,CAAC,EAAE;IACDzB,GAAG,EAAE,eAAe;IACpBjG,KAAK,EAAE,SAAS8E,aAAaA,CAAA,EAAG;MAC9B,IAAIiE,aAAa,GAAG,IAAI,CAACrF,KAAK;QAC1B7C,OAAO,GAAGkI,aAAa,CAAClI,OAAO;QAC/BqB,SAAS,GAAG6G,aAAa,CAAC7G,SAAS;MACvCrB,OAAO,CAACiE,aAAa,CAAC,IAAI,CAACT,aAAa,EAAE,CAAC;MAE3C,IAAInC,SAAS,EAAE;QACbA,SAAS,CAACuF,KAAK,CAAC,KAAK,CAAC,EAAEC,SAAS,CAAC;MACpC;IACF,CAAC,CAAC;IACF;EAEF,CAAC,EAAE;IACDzB,GAAG,EAAE,eAAe;IACpBjG,KAAK,EAAE,SAAS+E,aAAaA,CAAA,EAAG;MAC9B,IAAIiE,aAAa,GAAG,IAAI,CAACtF,KAAK;QAC1B7C,OAAO,GAAGmI,aAAa,CAACnI,OAAO;QAC/B0B,SAAS,GAAGyG,aAAa,CAACzG,SAAS;MACvC1B,OAAO,CAACkE,aAAa,CAAC,IAAI,CAACV,aAAa,EAAE,CAAC;MAE3C,IAAI9B,SAAS,EAAE;QACbA,SAAS,CAACkF,KAAK,CAAC,KAAK,CAAC,EAAEC,SAAS,CAAC;MACpC;IACF,CAAC,CAAC;IACF;EAEF,CAAC,EAAE;IACDzB,GAAG,EAAE,cAAc;IACnBjG,KAAK,EAAE,SAASgF,YAAYA,CAAA,EAAG;MAC7B,IAAIiE,aAAa,GAAG,IAAI,CAACvF,KAAK;QAC1B7C,OAAO,GAAGoI,aAAa,CAACpI,OAAO;QAC/B2B,QAAQ,GAAGyG,aAAa,CAACzG,QAAQ;MACrC3B,OAAO,CAACmE,YAAY,CAAC,IAAI,CAACX,aAAa,EAAE,CAAC;MAE1C,IAAI7B,QAAQ,EAAE;QACZA,QAAQ,CAACiF,KAAK,CAAC,KAAK,CAAC,EAAEC,SAAS,CAAC;MACnC;IACF,CAAC,CAAC;EAEJ,CAAC,EAAE;IACDzB,GAAG,EAAE,wBAAwB;IAC7BjG,KAAK,EAAE,SAASiF,sBAAsBA,CAAA,EAAG,CAAC,CAAC,CAAC;IAC5C;EAEF,CAAC,EAAE;IACDgB,GAAG,EAAE,eAAe;IACpBjG,KAAK,EAAE,SAASmF,aAAaA,CAAA,EAAG;MAC9B,IAAI+D,aAAa,GAAG,IAAI,CAACxF,KAAK;QAC1B7C,OAAO,GAAGqI,aAAa,CAACrI,OAAO;QAC/BiC,SAAS,GAAGoG,aAAa,CAACpG,SAAS;MACvCjC,OAAO,CAACsE,aAAa,CAAC,IAAI,CAACd,aAAa,EAAE,CAAC;MAE3C,IAAIvB,SAAS,EAAE;QACbA,SAAS,CAAC2E,KAAK,CAAC,KAAK,CAAC,EAAEC,SAAS,CAAC;MACpC;IACF,CAAC,CAAC;EAEJ,CAAC,EAAE;IACDzB,GAAG,EAAE,aAAa;IAClBjG,KAAK,EAAE,SAASoF,WAAWA,CAAA,EAAG;MAC5B,IAAI+D,aAAa,GAAG,IAAI,CAACzF,KAAK;QAC1B7C,OAAO,GAAGsI,aAAa,CAACtI,OAAO;QAC/BkC,OAAO,GAAGoG,aAAa,CAACpG,OAAO;MACnClC,OAAO,CAACuE,WAAW,CAAC,IAAI,CAACf,aAAa,EAAE,CAAC;MAEzC,IAAItB,OAAO,EAAE;QACXA,OAAO,CAAC0E,KAAK,CAAC,KAAK,CAAC,EAAEC,SAAS,CAAC;MAClC;IACF,CAAC,CAAC;EAEJ,CAAC,EAAE;IACDzB,GAAG,EAAE,eAAe;IACpBjG,KAAK,EAAE,SAASqF,aAAaA,CAAA,EAAG;MAC9B,IAAI+D,aAAa,GAAG,IAAI,CAAC1F,KAAK;QAC1B7C,OAAO,GAAGuI,aAAa,CAACvI,OAAO;QAC/BmC,SAAS,GAAGoG,aAAa,CAACpG,SAAS;MACvCnC,OAAO,CAACwE,aAAa,CAAC,IAAI,CAAChB,aAAa,EAAE,CAAC;MAE3C,IAAIrB,SAAS,EAAE;QACbA,SAAS,CAACyE,KAAK,CAAC,KAAK,CAAC,EAAEC,SAAS,CAAC;MACpC;IACF,CAAC,CAAC;IACF;EAEF,CAAC,EAAE;IACDzB,GAAG,EAAE,eAAe;IACpBjG,KAAK,EAAE,SAASsF,aAAaA,CAAA,EAAG;MAC9B,IAAI+D,aAAa,GAAG,IAAI,CAAC3F,KAAK;QAC1B7C,OAAO,GAAGwI,aAAa,CAACxI,OAAO;QAC/BoC,SAAS,GAAGoG,aAAa,CAACpG,SAAS;MACvCpC,OAAO,CAACyE,aAAa,CAAC,IAAI,CAACjB,aAAa,EAAE,CAAC;MAE3C,IAAIpB,SAAS,EAAE;QACbA,SAAS,CAACwE,KAAK,CAAC,KAAK,CAAC,EAAEC,SAAS,CAAC;MACpC;IACF,CAAC,CAAC;IACF;EAEF,CAAC,EAAE;IACDzB,GAAG,EAAE,sBAAsB;IAC3BjG,KAAK,EAAE,SAASuF,oBAAoBA,CAAA,EAAG;MACrC,IAAI+D,aAAa,GAAG,IAAI,CAAC5F,KAAK;QAC1B7C,OAAO,GAAGyI,aAAa,CAACzI,OAAO;QAC/BqC,gBAAgB,GAAGoG,aAAa,CAACpG,gBAAgB;QACjDhC,SAAS,GAAGoI,aAAa,CAACpI,SAAS;MAEvC,IAAIA,SAAS,IAAIA,SAAS,GAAG,CAAC,EAAE;QAC9B,IAAI,CAAC2C,KAAK,CAACmE,WAAW,GAAG9G,SAAS;MACpC;MAEAL,OAAO,CAAC0E,oBAAoB,CAAC,IAAI,CAAClB,aAAa,EAAE,CAAC;MAElD,IAAInB,gBAAgB,EAAE;QACpBA,gBAAgB,CAACuE,KAAK,CAAC,KAAK,CAAC,EAAEC,SAAS,CAAC;MAC3C;IACF,CAAC,CAAC;IACF;EAEF,CAAC,EAAE;IACDzB,GAAG,EAAE,kBAAkB;IACvBjG,KAAK,EAAE,SAASwF,gBAAgBA,CAAA,EAAG;MACjC,IAAI+D,aAAa,GAAG,IAAI,CAAC7F,KAAK;QAC1B7C,OAAO,GAAG0I,aAAa,CAAC1I,OAAO;QAC/BsC,YAAY,GAAGoG,aAAa,CAACpG,YAAY;MAC7CtC,OAAO,CAAC2E,gBAAgB,CAAC,IAAI,CAACnB,aAAa,EAAE,CAAC;MAE9C,IAAIlB,YAAY,EAAE;QAChBA,YAAY,CAACsE,KAAK,CAAC,KAAK,CAAC,EAAEC,SAAS,CAAC;MACvC;IACF,CAAC,CAAC;IACF;EAEF,CAAC,EAAE;IACDzB,GAAG,EAAE,kBAAkB;IACvBjG,KAAK,EAAE,SAASyF,gBAAgBA,CAAA,EAAG;MACjC,IAAI+D,aAAa,GAAG,IAAI,CAAC9F,KAAK;QAC1B7C,OAAO,GAAG2I,aAAa,CAAC3I,OAAO;QAC/BuC,YAAY,GAAGoG,aAAa,CAACpG,YAAY;MAC7CvC,OAAO,CAAC4E,gBAAgB,CAAC,IAAI,CAACpB,aAAa,EAAE,CAAC;MAE9C,IAAIjB,YAAY,EAAE;QAChBA,YAAY,CAACqE,KAAK,CAAC,KAAK,CAAC,EAAEC,SAAS,CAAC;MACvC;IACF;IACA;AACJ;AACA;EAEE,CAAC,EAAE;IACDzB,GAAG,EAAE,kBAAkB;IACvBjG,KAAK,EAAE,SAAS0F,gBAAgBA,CAAA,EAAG;MACjC,IAAI+D,aAAa,GAAG,IAAI,CAAC/F,KAAK;QAC1B7C,OAAO,GAAG4I,aAAa,CAAC5I,OAAO;QAC/BwC,YAAY,GAAGoG,aAAa,CAACpG,YAAY;MAC7CxC,OAAO,CAAC6E,gBAAgB,CAAC,IAAI,CAACrB,aAAa,EAAE,CAAC;MAE9C,IAAIhB,YAAY,EAAE;QAChBA,YAAY,CAACoE,KAAK,CAAC,KAAK,CAAC,EAAEC,SAAS,CAAC;MACvC;IACF,CAAC,CAAC;EAEJ,CAAC,EAAE;IACDzB,GAAG,EAAE,oBAAoB;IACzBjG,KAAK,EAAE,SAAS2F,kBAAkBA,CAAA,EAAG;MACnC,IAAI+D,aAAa,GAAG,IAAI,CAAChG,KAAK;QAC1B7C,OAAO,GAAG6I,aAAa,CAAC7I,OAAO;QAC/ByC,cAAc,GAAGoG,aAAa,CAACpG,cAAc;MACjDzC,OAAO,CAAC8E,kBAAkB,CAAC,IAAI,CAACtB,aAAa,EAAE,CAAC;MAEhD,IAAIf,cAAc,EAAE;QAClBA,cAAc,CAACmE,KAAK,CAAC,KAAK,CAAC,EAAEC,SAAS,CAAC;MACzC;IACF,CAAC,CAAC;IACF;EAEF,CAAC,EAAE;IACDzB,GAAG,EAAE,aAAa;IAClBjG,KAAK,EAAE,SAASkF,WAAWA,CAAA,EAAG;MAC5B,IAAIyE,aAAa,GAAG,IAAI,CAACjG,KAAK;QAC1B7C,OAAO,GAAG8I,aAAa,CAAC9I,OAAO;QAC/BgC,OAAO,GAAG8G,aAAa,CAAC9G,OAAO;MACnChC,OAAO,CAACqE,WAAW,CAAC,IAAI,CAACb,aAAa,EAAE,CAAC;MAEzC,IAAIxB,OAAO,EAAE;QACXA,OAAO,CAAC4E,KAAK,CAAC,KAAK,CAAC,EAAEC,SAAS,CAAC;MAClC;IACF;EACF,CAAC,EAAE;IACDzB,GAAG,EAAE,cAAc;IACnBjG,KAAK,EAAE,SAAS4J,YAAYA,CAAA,EAAG;MAC7B,IAAIC,aAAa,GAAG,IAAI,CAACnG,KAAK;QAC1B7C,OAAO,GAAGgJ,aAAa,CAAChJ,OAAO;QAC/B0C,QAAQ,GAAGsG,aAAa,CAACtG,QAAQ;MACrC1C,OAAO,CAAC+I,YAAY,CAAC,IAAI,CAACvF,aAAa,EAAE,CAAC;MAE1C,IAAId,QAAQ,EAAE;QACZA,QAAQ,CAACkE,KAAK,CAAC,KAAK,CAAC,EAAEC,SAAS,CAAC;MACnC;IACF;EACF,CAAC,EAAE;IACDzB,GAAG,EAAE,gBAAgB;IACrBjG,KAAK,EAAE,SAAS+F,cAAcA,CAAA,EAAG,CAAC;EACpC,CAAC,EAAE;IACDE,GAAG,EAAE,gBAAgB;IACrBjG,KAAK,EAAE,SAASsE,cAAcA,CAAA,EAAG;MAC/B,IAAIwF,MAAM,GAAG,IAAI;MAEjB,IAAIpG,KAAK,GAAG,CAAC,CAAC,EAAEzD,cAAc,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAACyD,KAAK,EAAE;QACzDG,KAAK,EAAE,IAAI,CAACA;MACd,CAAC,CAAC,CAAC,CAAC;;MAEJ,IAAI,CAAC,IAAI,CAACA,KAAK,EAAE;QACf,OAAO,IAAI;MACb,CAAC,CAAC;;MAGF,OAAOpD,MAAM,CAAC,SAAS,CAAC,CAACsJ,QAAQ,CAACC,OAAO,CAAC,IAAI,CAACtG,KAAK,CAAC1C,QAAQ,CAAC,CAACiJ,MAAM,CAACtJ,MAAM,CAACuJ,YAAY,CAAC,CAACC,GAAG,CAAC,UAAUC,CAAC,EAAE;QAC1G,IAAIC,MAAM;QAEV,IAAI,OAAOD,CAAC,CAACE,IAAI,KAAK,QAAQ,EAAE;UAC9B;UACA,IAAIF,CAAC,CAACE,IAAI,KAAK,QAAQ,EAAE;YACvBD,MAAM,GAAG,CAAC,CAAC,EAAEpK,cAAc,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAEmK,CAAC,CAAC1G,KAAK,CAAC;YACpD,IAAI6G,UAAU,GAAGF,MAAM,CAACxH,OAAO;YAE/BwH,MAAM,CAACxH,OAAO,GAAG,YAAY;cAC3B,IAAI0H,UAAU,EAAE;gBACdA,UAAU,CAAC9C,KAAK,CAAC,KAAK,CAAC,EAAEC,SAAS,CAAC;cACrC;cAEAoC,MAAM,CAAC5E,WAAW,CAACuC,KAAK,CAACqC,MAAM,EAAEpC,SAAS,CAAC;YAC7C,CAAC;UACH;QACF,CAAC,MAAM;UACL2C,MAAM,GAAG3G,KAAK;QAChB;QAEA,OAAOjD,MAAM,CAAC,SAAS,CAAC,CAAC+J,YAAY,CAACJ,CAAC,EAAEC,MAAM,CAAC;MAClD,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDpE,GAAG,EAAE,QAAQ;IACbjG,KAAK,EAAE,SAASyK,MAAMA,CAAA,EAAG;MACvB,IAAIC,MAAM,GAAG,IAAI;MAEjB,IAAIC,aAAa,GAAG,IAAI,CAACjH,KAAK;QAC1BtC,IAAI,GAAGuJ,aAAa,CAACvJ,IAAI;QACzBO,MAAM,GAAGgJ,aAAa,CAAChJ,MAAM;QAC7BE,OAAO,GAAG8I,aAAa,CAAC9I,OAAO;QAC/BJ,GAAG,GAAGkJ,aAAa,CAAClJ,GAAG;QACvBF,QAAQ,GAAGoJ,aAAa,CAACpJ,QAAQ;QACjCC,WAAW,GAAGmJ,aAAa,CAACnJ,WAAW;QACvCF,KAAK,GAAGqJ,aAAa,CAACrJ,KAAK;QAC3BS,WAAW,GAAG4I,aAAa,CAAC5I,WAAW;QACvC6I,OAAO,GAAGD,aAAa,CAACC,OAAO;MACnC,OAAOnK,MAAM,CAAC,SAAS,CAAC,CAACoK,aAAa,CAAC,OAAO,EAAE;QAC9CjJ,SAAS,EAAE,CAAC,CAAC,EAAElB,WAAW,CAAC,SAAS,CAAC,EAAE,mBAAmB,EAAE,IAAI,CAACgD,KAAK,CAAC9B,SAAS,CAAC;QACjFkJ,EAAE,EAAEF,OAAO;QACX7I,WAAW,EAAEA,WAAW;QACxBgJ,GAAG,EAAE,SAASA,GAAGA,CAACX,CAAC,EAAE;UACnBM,MAAM,CAAC7G,KAAK,GAAGuG,CAAC;QAClB,CAAC;QACD9I,KAAK,EAAEA,KAAK;QACZO,OAAO,EAAEA,OAAO;QAChBT,IAAI,EAAEA,IAAI;QACVI,WAAW,EAAEA,WAAW;QACxBD,QAAQ,EAAEA,QAAQ;QAClBI,MAAM,EAAEA,MAAM;QACdF,GAAG,EAAEA,GAAG;QACRO,WAAW,EAAE,IAAI,CAACuC,eAAe;QACjCrC,SAAS,EAAE,IAAI,CAAC4C,aAAa;QAC7B3C,SAAS,EAAE,IAAI,CAACqC,aAAa;QAC7BpC,gBAAgB,EAAE,IAAI,CAACqC,oBAAoB;QAC3CpC,SAAS,EAAE,IAAI,CAACsC,aAAa;QAC7BrC,OAAO,EAAE,IAAI,CAACuC,WAAW;QACzBtC,SAAS,EAAE,IAAI,CAACwC,aAAa;QAC7BvC,QAAQ,EAAE,IAAI,CAACwC,YAAY;QAC3BvC,MAAM,EAAE,IAAI,CAACiC,UAAU;QACvBhC,OAAO,EAAE,IAAI,CAACkC,WAAW;QACzBjC,UAAU,EAAE,IAAI,CAACkD,cAAc;QAC/BjD,gBAAgB,EAAE,IAAI,CAACgD,oBAAoB;QAC3C/C,OAAO,EAAE,IAAI,CAACqC,WAAW;QACzBpC,SAAS,EAAE,IAAI,CAACqC,aAAa;QAC7BpC,OAAO,EAAE,IAAI,CAACqC,WAAW;QACzBpC,SAAS,EAAE,IAAI,CAACqC,aAAa;QAC7BpC,SAAS,EAAE,IAAI,CAACqC,aAAa;QAC7BpC,gBAAgB,EAAE,IAAI,CAACqC,oBAAoB;QAC3CpC,YAAY,EAAE,IAAI,CAACqC,gBAAgB;QACnCpC,YAAY,EAAE,IAAI,CAACqC,gBAAgB;QACnCpC,YAAY,EAAE,IAAI,CAACqC,gBAAgB;QACnCpC,cAAc,EAAE,IAAI,CAACqC,kBAAkB;QACvCqF,QAAQ,EAAE;MACZ,CAAC,EAAE,IAAI,CAAC1G,cAAc,EAAE,CAAC;IAC3B;EACF,CAAC,EAAE;IACD2B,GAAG,EAAE,cAAc;IACnBgF,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;MAClB,OAAO,IAAI,CAACpH,KAAK,CAACqH,YAAY;IAChC,CAAC,CAAC;IACF;IAAA;;IAEAC,GAAG,EAAE,SAASA,GAAGA,CAACC,IAAI,EAAE;MACtB,IAAI,CAACvH,KAAK,CAACqH,YAAY,GAAGE,IAAI;IAChC;EACF,CAAC,EAAE;IACDnF,GAAG,EAAE,OAAO;IACZgF,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;MAClB,OAAO,IAAI,CAACpH,KAAK,CAACvC,KAAK;IACzB,CAAC;IACD6J,GAAG,EAAE,SAASA,GAAGA,CAACE,GAAG,EAAE;MACrB,IAAI,CAACxH,KAAK,CAACvC,KAAK,GAAG+J,GAAG;IACxB;EACF,CAAC,EAAE;IACDpF,GAAG,EAAE,QAAQ;IACbgF,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;MAClB,OAAO,IAAI,CAACpH,KAAK,CAACyH,MAAM;IAC1B,CAAC;IACDH,GAAG,EAAE,SAASA,GAAGA,CAACE,GAAG,EAAE;MACrB,IAAIA,GAAG,GAAG,CAAC,EAAE;QACXA,GAAG,GAAG,CAAC;MACT;MAEA,IAAIA,GAAG,GAAG,CAAC,EAAE;QACXA,GAAG,GAAG,CAAC;MACT;MAEA,IAAI,CAACxH,KAAK,CAACyH,MAAM,GAAGD,GAAG;IACzB,CAAC,CAAC;EAEJ,CAAC,EAAE;IACDpF,GAAG,EAAE,YAAY;IACjBgF,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;MAClB,OAAO,IAAI,CAACpH,KAAK,CAAC0H,UAAU;IAC9B,CAAC,CAAC;EAEJ,CAAC,EAAE;IACDtF,GAAG,EAAE,aAAa;IAClBgF,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;MAClB,OAAO,IAAI,CAACpH,KAAK,CAAC2H,WAAW;IAC/B;EACF,CAAC,CAAC,CAAC;EACH,OAAOhI,KAAK;AACd,CAAC,CAAC/C,MAAM,CAACgL,SAAS,CAAC;AAEnB1L,OAAO,CAAC,SAAS,CAAC,GAAGyD,KAAK;AAC1BA,KAAK,CAAC5C,SAAS,GAAGA,SAAS;AAC3B4C,KAAK,CAACkI,WAAW,GAAG,OAAO"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}