{"ast": null, "code": "import setBreakpoint from './setBreakpoint.js';\nimport getBreakpoint from './getBreakpoint.js';\nexport default {\n  setBreakpoint,\n  getBreakpoint\n};", "map": {"version": 3, "names": ["setBreakpoint", "getBreakpoint"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/core/breakpoints/index.js"], "sourcesContent": ["import setBreakpoint from './setBreakpoint.js';\nimport getBreakpoint from './getBreakpoint.js';\nexport default {\n  setBreakpoint,\n  getBreakpoint\n};"], "mappings": "AAAA,OAAOA,aAAa,MAAM,oBAAoB;AAC9C,OAAOC,aAAa,MAAM,oBAAoB;AAC9C,eAAe;EACbD,aAAa;EACbC;AACF,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}