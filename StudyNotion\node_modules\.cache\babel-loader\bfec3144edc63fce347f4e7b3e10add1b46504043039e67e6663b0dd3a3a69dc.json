{"ast": null, "code": "import { toast } from \"react-hot-toast\";\nimport { setUser } from \"../../slices/profileSlice\";\nimport { apiConnector } from \"../apiconnector\";\nimport { settingsEndpoints } from \"../apis\";\nimport { logout } from \"./authAPI\";\nconst {\n  UPDATE_DISPLAY_PICTURE_API,\n  UPDATE_PROFILE_API,\n  CHANGE_PASSWORD_API,\n  DELETE_PROFILE_API\n} = settingsEndpoints;\nexport function updateDisplayPicture(token, formData) {\n  return async dispatch => {\n    const toastId = toast.loading(\"Loading...\");\n    try {\n      const response = await apiConnector(\"PUT\", UPDATE_DISPLAY_PICTURE_API, formData, {\n        \"Content-Type\": \"multipart/form-data\",\n        Authorization: `Bearer ${token}`\n      });\n      console.log(\"UPDATE_DISPLAY_PICTURE_API API RESPONSE............\", response);\n      if (!response.data.success) {\n        throw new Error(response.data.message);\n      }\n      toast.success(\"Display Picture Updated Successfully\");\n      dispatch(setUser(response.data.data));\n    } catch (error) {\n      console.log(\"UPDATE_DISPLAY_PICTURE_API API ERROR............\", error);\n      toast.error(\"Could Not Update Display Picture\");\n    }\n    toast.dismiss(toastId);\n  };\n}\nexport function updateProfile(token, formData) {\n  return async dispatch => {\n    const toastId = toast.loading(\"Loading...\");\n    try {\n      const response = await apiConnector(\"PUT\", UPDATE_PROFILE_API, formData, {\n        Authorization: `Bearer ${token}`\n      });\n      console.log(\"UPDATE_PROFILE_API API RESPONSE............\", response);\n      if (!response.data.success) {\n        throw new Error(response.data.message);\n      }\n      const userImage = response.data.updatedUserDetails.image ? response.data.updatedUserDetails.image : `https://api.dicebear.com/5.x/initials/svg?seed=${response.data.updatedUserDetails.firstName} ${response.data.updatedUserDetails.lastName}`;\n      dispatch(setUser({\n        ...response.data.updatedUserDetails,\n        image: userImage\n      }));\n      toast.success(\"Profile Updated Successfully\");\n    } catch (error) {\n      console.log(\"UPDATE_PROFILE_API API ERROR............\", error);\n      toast.error(\"Could Not Update Profile\");\n    }\n    toast.dismiss(toastId);\n  };\n}\nexport async function changePassword(token, formData) {\n  const toastId = toast.loading(\"Loading...\");\n  try {\n    const response = await apiConnector(\"POST\", CHANGE_PASSWORD_API, formData, {\n      Authorization: `Bearer ${token}`\n    });\n    console.log(\"CHANGE_PASSWORD_API API RESPONSE............\", response);\n    if (!response.data.success) {\n      throw new Error(response.data.message);\n    }\n    toast.success(\"Password Changed Successfully\");\n  } catch (error) {\n    console.log(\"CHANGE_PASSWORD_API API ERROR............\", error);\n    toast.error(error.response.data.message);\n  }\n  toast.dismiss(toastId);\n}\nexport function deleteProfile(token, navigate) {\n  return async dispatch => {\n    const toastId = toast.loading(\"Loading...\");\n    try {\n      const response = await apiConnector(\"DELETE\", DELETE_PROFILE_API, null, {\n        Authorization: `Bearer ${token}`\n      });\n      console.log(\"DELETE_PROFILE_API API RESPONSE............\", response);\n      if (!response.data.success) {\n        throw new Error(response.data.message);\n      }\n      toast.success(\"Profile Deleted Successfully\");\n      dispatch(logout(navigate));\n    } catch (error) {\n      console.log(\"DELETE_PROFILE_API API ERROR............\", error);\n      toast.error(\"Could Not Delete Profile\");\n    }\n    toast.dismiss(toastId);\n  };\n}", "map": {"version": 3, "names": ["toast", "setUser", "apiConnector", "settingsEndpoints", "logout", "UPDATE_DISPLAY_PICTURE_API", "UPDATE_PROFILE_API", "CHANGE_PASSWORD_API", "DELETE_PROFILE_API", "updateDisplayPicture", "token", "formData", "dispatch", "toastId", "loading", "response", "Authorization", "console", "log", "data", "success", "Error", "message", "error", "dismiss", "updateProfile", "userImage", "updatedUserDetails", "image", "firstName", "lastName", "changePassword", "deleteProfile", "navigate"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/src/services/operations/SettingsAPI.js"], "sourcesContent": ["import { toast } from \"react-hot-toast\"\r\n\r\nimport { setUser } from \"../../slices/profileSlice\"\r\nimport { apiConnector } from \"../apiconnector\"\r\nimport { settingsEndpoints } from \"../apis\"\r\nimport { logout } from \"./authAPI\"\r\n\r\nconst {\r\n  UPDATE_DISPLAY_PICTURE_API,\r\n  UPDATE_PROFILE_API,\r\n  CHANGE_PASSWORD_API,\r\n  DELETE_PROFILE_API,\r\n} = settingsEndpoints\r\n\r\nexport function updateDisplayPicture(token, formData) {\r\n  return async (dispatch) => {\r\n    const toastId = toast.loading(\"Loading...\")\r\n    try {\r\n      const response = await apiConnector(\r\n        \"PUT\",\r\n        UPDATE_DISPLAY_PICTURE_API,\r\n        formData,\r\n        {\r\n          \"Content-Type\": \"multipart/form-data\",\r\n          Authorization: `Bearer ${token}`,\r\n        }\r\n      )\r\n      console.log(\r\n        \"UPDATE_DISPLAY_PICTURE_API API RESPONSE............\",\r\n        response\r\n      )\r\n\r\n      if (!response.data.success) {\r\n        throw new Error(response.data.message)\r\n      }\r\n      toast.success(\"Display Picture Updated Successfully\")\r\n      dispatch(setUser(response.data.data))\r\n    } catch (error) {\r\n      console.log(\"UPDATE_DISPLAY_PICTURE_API API ERROR............\", error)\r\n      toast.error(\"Could Not Update Display Picture\")\r\n    }\r\n    toast.dismiss(toastId)\r\n  }\r\n}\r\n\r\nexport function updateProfile(token, formData) {\r\n  return async (dispatch) => {\r\n    const toastId = toast.loading(\"Loading...\")\r\n    try {\r\n      const response = await apiConnector(\"PUT\", UPDATE_PROFILE_API, formData, {\r\n        Authorization: `Bearer ${token}`,\r\n      })\r\n      console.log(\"UPDATE_PROFILE_API API RESPONSE............\", response)\r\n\r\n      if (!response.data.success) {\r\n        throw new Error(response.data.message)\r\n      }\r\n      const userImage = response.data.updatedUserDetails.image\r\n        ? response.data.updatedUserDetails.image\r\n        : `https://api.dicebear.com/5.x/initials/svg?seed=${response.data.updatedUserDetails.firstName} ${response.data.updatedUserDetails.lastName}`\r\n      dispatch(\r\n        setUser({ ...response.data.updatedUserDetails, image: userImage })\r\n      )\r\n      toast.success(\"Profile Updated Successfully\")\r\n    } catch (error) {\r\n      console.log(\"UPDATE_PROFILE_API API ERROR............\", error)\r\n      toast.error(\"Could Not Update Profile\")\r\n    }\r\n    toast.dismiss(toastId)\r\n  }\r\n}\r\n\r\nexport async function changePassword(token, formData) {\r\n  const toastId = toast.loading(\"Loading...\")\r\n  try {\r\n    const response = await apiConnector(\"POST\", CHANGE_PASSWORD_API, formData, {\r\n      Authorization: `Bearer ${token}`,\r\n    })\r\n    console.log(\"CHANGE_PASSWORD_API API RESPONSE............\", response)\r\n\r\n    if (!response.data.success) {\r\n      throw new Error(response.data.message)\r\n    }\r\n    toast.success(\"Password Changed Successfully\")\r\n  } catch (error) {\r\n    console.log(\"CHANGE_PASSWORD_API API ERROR............\", error)\r\n    toast.error(error.response.data.message)\r\n  }\r\n  toast.dismiss(toastId)\r\n}\r\n\r\nexport function deleteProfile(token, navigate) {\r\n  return async (dispatch) => {\r\n    const toastId = toast.loading(\"Loading...\")\r\n    try {\r\n      const response = await apiConnector(\"DELETE\", DELETE_PROFILE_API, null, {\r\n        Authorization: `Bearer ${token}`,\r\n      })\r\n      console.log(\"DELETE_PROFILE_API API RESPONSE............\", response)\r\n\r\n      if (!response.data.success) {\r\n        throw new Error(response.data.message)\r\n      }\r\n      toast.success(\"Profile Deleted Successfully\")\r\n      dispatch(logout(navigate))\r\n    } catch (error) {\r\n      console.log(\"DELETE_PROFILE_API API ERROR............\", error)\r\n      toast.error(\"Could Not Delete Profile\")\r\n    }\r\n    toast.dismiss(toastId)\r\n  }\r\n}"], "mappings": "AAAA,SAASA,KAAK,QAAQ,iBAAiB;AAEvC,SAASC,OAAO,QAAQ,2BAA2B;AACnD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,iBAAiB,QAAQ,SAAS;AAC3C,SAASC,MAAM,QAAQ,WAAW;AAElC,MAAM;EACJC,0BAA0B;EAC1BC,kBAAkB;EAClBC,mBAAmB;EACnBC;AACF,CAAC,GAAGL,iBAAiB;AAErB,OAAO,SAASM,oBAAoBA,CAACC,KAAK,EAAEC,QAAQ,EAAE;EACpD,OAAO,MAAOC,QAAQ,IAAK;IACzB,MAAMC,OAAO,GAAGb,KAAK,CAACc,OAAO,CAAC,YAAY,CAAC;IAC3C,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMb,YAAY,CACjC,KAAK,EACLG,0BAA0B,EAC1BM,QAAQ,EACR;QACE,cAAc,EAAE,qBAAqB;QACrCK,aAAa,EAAG,UAASN,KAAM;MACjC,CAAC,CACF;MACDO,OAAO,CAACC,GAAG,CACT,qDAAqD,EACrDH,QAAQ,CACT;MAED,IAAI,CAACA,QAAQ,CAACI,IAAI,CAACC,OAAO,EAAE;QAC1B,MAAM,IAAIC,KAAK,CAACN,QAAQ,CAACI,IAAI,CAACG,OAAO,CAAC;MACxC;MACAtB,KAAK,CAACoB,OAAO,CAAC,sCAAsC,CAAC;MACrDR,QAAQ,CAACX,OAAO,CAACc,QAAQ,CAACI,IAAI,CAACA,IAAI,CAAC,CAAC;IACvC,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdN,OAAO,CAACC,GAAG,CAAC,kDAAkD,EAAEK,KAAK,CAAC;MACtEvB,KAAK,CAACuB,KAAK,CAAC,kCAAkC,CAAC;IACjD;IACAvB,KAAK,CAACwB,OAAO,CAACX,OAAO,CAAC;EACxB,CAAC;AACH;AAEA,OAAO,SAASY,aAAaA,CAACf,KAAK,EAAEC,QAAQ,EAAE;EAC7C,OAAO,MAAOC,QAAQ,IAAK;IACzB,MAAMC,OAAO,GAAGb,KAAK,CAACc,OAAO,CAAC,YAAY,CAAC;IAC3C,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMb,YAAY,CAAC,KAAK,EAAEI,kBAAkB,EAAEK,QAAQ,EAAE;QACvEK,aAAa,EAAG,UAASN,KAAM;MACjC,CAAC,CAAC;MACFO,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAEH,QAAQ,CAAC;MAEpE,IAAI,CAACA,QAAQ,CAACI,IAAI,CAACC,OAAO,EAAE;QAC1B,MAAM,IAAIC,KAAK,CAACN,QAAQ,CAACI,IAAI,CAACG,OAAO,CAAC;MACxC;MACA,MAAMI,SAAS,GAAGX,QAAQ,CAACI,IAAI,CAACQ,kBAAkB,CAACC,KAAK,GACpDb,QAAQ,CAACI,IAAI,CAACQ,kBAAkB,CAACC,KAAK,GACrC,kDAAiDb,QAAQ,CAACI,IAAI,CAACQ,kBAAkB,CAACE,SAAU,IAAGd,QAAQ,CAACI,IAAI,CAACQ,kBAAkB,CAACG,QAAS,EAAC;MAC/IlB,QAAQ,CACNX,OAAO,CAAC;QAAE,GAAGc,QAAQ,CAACI,IAAI,CAACQ,kBAAkB;QAAEC,KAAK,EAAEF;MAAU,CAAC,CAAC,CACnE;MACD1B,KAAK,CAACoB,OAAO,CAAC,8BAA8B,CAAC;IAC/C,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdN,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEK,KAAK,CAAC;MAC9DvB,KAAK,CAACuB,KAAK,CAAC,0BAA0B,CAAC;IACzC;IACAvB,KAAK,CAACwB,OAAO,CAACX,OAAO,CAAC;EACxB,CAAC;AACH;AAEA,OAAO,eAAekB,cAAcA,CAACrB,KAAK,EAAEC,QAAQ,EAAE;EACpD,MAAME,OAAO,GAAGb,KAAK,CAACc,OAAO,CAAC,YAAY,CAAC;EAC3C,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMb,YAAY,CAAC,MAAM,EAAEK,mBAAmB,EAAEI,QAAQ,EAAE;MACzEK,aAAa,EAAG,UAASN,KAAM;IACjC,CAAC,CAAC;IACFO,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEH,QAAQ,CAAC;IAErE,IAAI,CAACA,QAAQ,CAACI,IAAI,CAACC,OAAO,EAAE;MAC1B,MAAM,IAAIC,KAAK,CAACN,QAAQ,CAACI,IAAI,CAACG,OAAO,CAAC;IACxC;IACAtB,KAAK,CAACoB,OAAO,CAAC,+BAA+B,CAAC;EAChD,CAAC,CAAC,OAAOG,KAAK,EAAE;IACdN,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAEK,KAAK,CAAC;IAC/DvB,KAAK,CAACuB,KAAK,CAACA,KAAK,CAACR,QAAQ,CAACI,IAAI,CAACG,OAAO,CAAC;EAC1C;EACAtB,KAAK,CAACwB,OAAO,CAACX,OAAO,CAAC;AACxB;AAEA,OAAO,SAASmB,aAAaA,CAACtB,KAAK,EAAEuB,QAAQ,EAAE;EAC7C,OAAO,MAAOrB,QAAQ,IAAK;IACzB,MAAMC,OAAO,GAAGb,KAAK,CAACc,OAAO,CAAC,YAAY,CAAC;IAC3C,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMb,YAAY,CAAC,QAAQ,EAAEM,kBAAkB,EAAE,IAAI,EAAE;QACtEQ,aAAa,EAAG,UAASN,KAAM;MACjC,CAAC,CAAC;MACFO,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAEH,QAAQ,CAAC;MAEpE,IAAI,CAACA,QAAQ,CAACI,IAAI,CAACC,OAAO,EAAE;QAC1B,MAAM,IAAIC,KAAK,CAACN,QAAQ,CAACI,IAAI,CAACG,OAAO,CAAC;MACxC;MACAtB,KAAK,CAACoB,OAAO,CAAC,8BAA8B,CAAC;MAC7CR,QAAQ,CAACR,MAAM,CAAC6B,QAAQ,CAAC,CAAC;IAC5B,CAAC,CAAC,OAAOV,KAAK,EAAE;MACdN,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEK,KAAK,CAAC;MAC9DvB,KAAK,CAACuB,KAAK,CAAC,0BAA0B,CAAC;IACzC;IACAvB,KAAK,CAACwB,OAAO,CAACX,OAAO,CAAC;EACxB,CAAC;AACH"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}