{"ast": null, "code": "import { elementChildren, elementNextAll, elementPrevAll } from '../../shared/utils.js';\nexport default function updateSlidesClasses() {\n  const swiper = this;\n  const {\n    slides,\n    params,\n    slidesEl,\n    activeIndex\n  } = swiper;\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  const getFilteredSlide = selector => {\n    return elementChildren(slidesEl, `.${params.slideClass}${selector}, swiper-slide${selector}`)[0];\n  };\n  slides.forEach(slideEl => {\n    slideEl.classList.remove(params.slideActiveClass, params.slideNextClass, params.slidePrevClass);\n  });\n  let activeSlide;\n  if (isVirtual) {\n    if (params.loop) {\n      let slideIndex = activeIndex - swiper.virtual.slidesBefore;\n      if (slideIndex < 0) slideIndex = swiper.virtual.slides.length + slideIndex;\n      if (slideIndex >= swiper.virtual.slides.length) slideIndex -= swiper.virtual.slides.length;\n      activeSlide = getFilteredSlide(`[data-swiper-slide-index=\"${slideIndex}\"]`);\n    } else {\n      activeSlide = getFilteredSlide(`[data-swiper-slide-index=\"${activeIndex}\"]`);\n    }\n  } else {\n    activeSlide = slides[activeIndex];\n  }\n  if (activeSlide) {\n    // Active classes\n    activeSlide.classList.add(params.slideActiveClass);\n\n    // Next Slide\n    let nextSlide = elementNextAll(activeSlide, `.${params.slideClass}, swiper-slide`)[0];\n    if (params.loop && !nextSlide) {\n      nextSlide = slides[0];\n    }\n    if (nextSlide) {\n      nextSlide.classList.add(params.slideNextClass);\n    }\n    // Prev Slide\n    let prevSlide = elementPrevAll(activeSlide, `.${params.slideClass}, swiper-slide`)[0];\n    if (params.loop && !prevSlide === 0) {\n      prevSlide = slides[slides.length - 1];\n    }\n    if (prevSlide) {\n      prevSlide.classList.add(params.slidePrevClass);\n    }\n  }\n  swiper.emitSlidesClasses();\n}", "map": {"version": 3, "names": ["elementChildren", "elementNextAll", "elementPrevAll", "updateSlidesClasses", "swiper", "slides", "params", "slidesEl", "activeIndex", "isVirtual", "virtual", "enabled", "getFilteredSlide", "selector", "slideClass", "for<PERSON>ach", "slideEl", "classList", "remove", "slideActiveClass", "slideNextClass", "slidePrevClass", "activeSlide", "loop", "slideIndex", "slidesBefore", "length", "add", "nextSlide", "prevSlide", "emitSlidesClasses"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/core/update/updateSlidesClasses.js"], "sourcesContent": ["import { elementChildren, elementNextAll, elementPrevAll } from '../../shared/utils.js';\nexport default function updateSlidesClasses() {\n  const swiper = this;\n  const {\n    slides,\n    params,\n    slidesEl,\n    activeIndex\n  } = swiper;\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  const getFilteredSlide = selector => {\n    return elementChildren(slidesEl, `.${params.slideClass}${selector}, swiper-slide${selector}`)[0];\n  };\n  slides.forEach(slideEl => {\n    slideEl.classList.remove(params.slideActiveClass, params.slideNextClass, params.slidePrevClass);\n  });\n  let activeSlide;\n  if (isVirtual) {\n    if (params.loop) {\n      let slideIndex = activeIndex - swiper.virtual.slidesBefore;\n      if (slideIndex < 0) slideIndex = swiper.virtual.slides.length + slideIndex;\n      if (slideIndex >= swiper.virtual.slides.length) slideIndex -= swiper.virtual.slides.length;\n      activeSlide = getFilteredSlide(`[data-swiper-slide-index=\"${slideIndex}\"]`);\n    } else {\n      activeSlide = getFilteredSlide(`[data-swiper-slide-index=\"${activeIndex}\"]`);\n    }\n  } else {\n    activeSlide = slides[activeIndex];\n  }\n  if (activeSlide) {\n    // Active classes\n    activeSlide.classList.add(params.slideActiveClass);\n\n    // Next Slide\n    let nextSlide = elementNextAll(activeSlide, `.${params.slideClass}, swiper-slide`)[0];\n    if (params.loop && !nextSlide) {\n      nextSlide = slides[0];\n    }\n    if (nextSlide) {\n      nextSlide.classList.add(params.slideNextClass);\n    }\n    // Prev Slide\n    let prevSlide = elementPrevAll(activeSlide, `.${params.slideClass}, swiper-slide`)[0];\n    if (params.loop && !prevSlide === 0) {\n      prevSlide = slides[slides.length - 1];\n    }\n    if (prevSlide) {\n      prevSlide.classList.add(params.slidePrevClass);\n    }\n  }\n  swiper.emitSlidesClasses();\n}"], "mappings": "AAAA,SAASA,eAAe,EAAEC,cAAc,EAAEC,cAAc,QAAQ,uBAAuB;AACvF,eAAe,SAASC,mBAAmBA,CAAA,EAAG;EAC5C,MAAMC,MAAM,GAAG,IAAI;EACnB,MAAM;IACJC,MAAM;IACNC,MAAM;IACNC,QAAQ;IACRC;EACF,CAAC,GAAGJ,MAAM;EACV,MAAMK,SAAS,GAAGL,MAAM,CAACM,OAAO,IAAIJ,MAAM,CAACI,OAAO,CAACC,OAAO;EAC1D,MAAMC,gBAAgB,GAAGC,QAAQ,IAAI;IACnC,OAAOb,eAAe,CAACO,QAAQ,EAAG,IAAGD,MAAM,CAACQ,UAAW,GAAED,QAAS,iBAAgBA,QAAS,EAAC,CAAC,CAAC,CAAC,CAAC;EAClG,CAAC;EACDR,MAAM,CAACU,OAAO,CAACC,OAAO,IAAI;IACxBA,OAAO,CAACC,SAAS,CAACC,MAAM,CAACZ,MAAM,CAACa,gBAAgB,EAAEb,MAAM,CAACc,cAAc,EAAEd,MAAM,CAACe,cAAc,CAAC;EACjG,CAAC,CAAC;EACF,IAAIC,WAAW;EACf,IAAIb,SAAS,EAAE;IACb,IAAIH,MAAM,CAACiB,IAAI,EAAE;MACf,IAAIC,UAAU,GAAGhB,WAAW,GAAGJ,MAAM,CAACM,OAAO,CAACe,YAAY;MAC1D,IAAID,UAAU,GAAG,CAAC,EAAEA,UAAU,GAAGpB,MAAM,CAACM,OAAO,CAACL,MAAM,CAACqB,MAAM,GAAGF,UAAU;MAC1E,IAAIA,UAAU,IAAIpB,MAAM,CAACM,OAAO,CAACL,MAAM,CAACqB,MAAM,EAAEF,UAAU,IAAIpB,MAAM,CAACM,OAAO,CAACL,MAAM,CAACqB,MAAM;MAC1FJ,WAAW,GAAGV,gBAAgB,CAAE,6BAA4BY,UAAW,IAAG,CAAC;IAC7E,CAAC,MAAM;MACLF,WAAW,GAAGV,gBAAgB,CAAE,6BAA4BJ,WAAY,IAAG,CAAC;IAC9E;EACF,CAAC,MAAM;IACLc,WAAW,GAAGjB,MAAM,CAACG,WAAW,CAAC;EACnC;EACA,IAAIc,WAAW,EAAE;IACf;IACAA,WAAW,CAACL,SAAS,CAACU,GAAG,CAACrB,MAAM,CAACa,gBAAgB,CAAC;;IAElD;IACA,IAAIS,SAAS,GAAG3B,cAAc,CAACqB,WAAW,EAAG,IAAGhB,MAAM,CAACQ,UAAW,gBAAe,CAAC,CAAC,CAAC,CAAC;IACrF,IAAIR,MAAM,CAACiB,IAAI,IAAI,CAACK,SAAS,EAAE;MAC7BA,SAAS,GAAGvB,MAAM,CAAC,CAAC,CAAC;IACvB;IACA,IAAIuB,SAAS,EAAE;MACbA,SAAS,CAACX,SAAS,CAACU,GAAG,CAACrB,MAAM,CAACc,cAAc,CAAC;IAChD;IACA;IACA,IAAIS,SAAS,GAAG3B,cAAc,CAACoB,WAAW,EAAG,IAAGhB,MAAM,CAACQ,UAAW,gBAAe,CAAC,CAAC,CAAC,CAAC;IACrF,IAAIR,MAAM,CAACiB,IAAI,IAAI,CAACM,SAAS,KAAK,CAAC,EAAE;MACnCA,SAAS,GAAGxB,MAAM,CAACA,MAAM,CAACqB,MAAM,GAAG,CAAC,CAAC;IACvC;IACA,IAAIG,SAAS,EAAE;MACbA,SAAS,CAACZ,SAAS,CAACU,GAAG,CAACrB,MAAM,CAACe,cAAc,CAAC;IAChD;EACF;EACAjB,MAAM,CAAC0B,iBAAiB,EAAE;AAC5B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}