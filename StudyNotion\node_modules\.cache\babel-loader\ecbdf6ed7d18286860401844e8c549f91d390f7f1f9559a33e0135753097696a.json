{"ast": null, "code": "export default function classesToSelector() {\n  let classes = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '';\n  return `.${classes.trim().replace(/([\\.:!+\\/])/g, '\\\\$1') // eslint-disable-line\n  .replace(/ /g, '.')}`;\n}", "map": {"version": 3, "names": ["classesToSelector", "classes", "arguments", "length", "undefined", "trim", "replace"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/shared/classes-to-selector.js"], "sourcesContent": ["export default function classesToSelector(classes = '') {\n  return `.${classes.trim().replace(/([\\.:!+\\/])/g, '\\\\$1') // eslint-disable-line\n  .replace(/ /g, '.')}`;\n}"], "mappings": "AAAA,eAAe,SAASA,iBAAiBA,CAAA,EAAe;EAAA,IAAdC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;EACpD,OAAQ,IAAGD,OAAO,CAACI,IAAI,EAAE,CAACC,OAAO,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;EAAA,CACzDA,OAAO,CAAC,IAAI,EAAE,GAAG,CAAE,EAAC;AACvB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}