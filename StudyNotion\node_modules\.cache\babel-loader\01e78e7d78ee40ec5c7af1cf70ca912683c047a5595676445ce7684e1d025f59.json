{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.Provider = exports.Consumer = void 0;\nvar _react = _interopRequireDefault(require(\"react\"));\nvar _React$createContext = /*#__PURE__*/_react[\"default\"].createContext({}),\n  Provider = _React$createContext.Provider,\n  Consumer = _React$createContext.Consumer;\nexports.Consumer = Consumer;\nexports.Provider = Provider;", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "Provider", "Consumer", "_react", "_React$createContext", "createContext"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/react-super-responsive-table/dist/utils/tableContext.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.Provider = exports.Consumer = void 0;\n\nvar _react = _interopRequireDefault(require(\"react\"));\n\nvar _React$createContext = /*#__PURE__*/_react[\"default\"].createContext({}),\n    Provider = _React$createContext.Provider,\n    Consumer = _React$createContext.Consumer;\n\nexports.Consumer = Consumer;\nexports.Provider = Provider;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC;AAEpFC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,QAAQ,GAAGF,OAAO,CAACG,QAAQ,GAAG,KAAK,CAAC;AAE5C,IAAIC,MAAM,GAAGR,sBAAsB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AAErD,IAAIQ,oBAAoB,GAAG,aAAaD,MAAM,CAAC,SAAS,CAAC,CAACE,aAAa,CAAC,CAAC,CAAC,CAAC;EACvEJ,QAAQ,GAAGG,oBAAoB,CAACH,QAAQ;EACxCC,QAAQ,GAAGE,oBAAoB,CAACF,QAAQ;AAE5CH,OAAO,CAACG,QAAQ,GAAGA,QAAQ;AAC3BH,OAAO,CAACE,QAAQ,GAAGA,QAAQ"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}