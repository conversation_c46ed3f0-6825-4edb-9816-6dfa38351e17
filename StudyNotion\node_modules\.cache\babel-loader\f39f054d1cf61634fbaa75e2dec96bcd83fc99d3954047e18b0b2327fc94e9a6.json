{"ast": null, "code": "import { createElement, elementChildren } from './utils.js';\nexport default function createElementIfNotDefined(swiper, originalParams, params, checkProps) {\n  if (swiper.params.createElements) {\n    Object.keys(checkProps).forEach(key => {\n      if (!params[key] && params.auto === true) {\n        let element = elementChildren(swiper.el, `.${checkProps[key]}`)[0];\n        if (!element) {\n          element = createElement('div', checkProps[key]);\n          element.className = checkProps[key];\n          swiper.el.append(element);\n        }\n        params[key] = element;\n        originalParams[key] = element;\n      }\n    });\n  }\n  return params;\n}", "map": {"version": 3, "names": ["createElement", "elementChildren", "createElementIfNotDefined", "swiper", "originalParams", "params", "checkProps", "createElements", "Object", "keys", "for<PERSON>ach", "key", "auto", "element", "el", "className", "append"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/shared/create-element-if-not-defined.js"], "sourcesContent": ["import { createElement, elementChildren } from './utils.js';\nexport default function createElementIfNotDefined(swiper, originalParams, params, checkProps) {\n  if (swiper.params.createElements) {\n    Object.keys(checkProps).forEach(key => {\n      if (!params[key] && params.auto === true) {\n        let element = elementChildren(swiper.el, `.${checkProps[key]}`)[0];\n        if (!element) {\n          element = createElement('div', checkProps[key]);\n          element.className = checkProps[key];\n          swiper.el.append(element);\n        }\n        params[key] = element;\n        originalParams[key] = element;\n      }\n    });\n  }\n  return params;\n}"], "mappings": "AAAA,SAASA,aAAa,EAAEC,eAAe,QAAQ,YAAY;AAC3D,eAAe,SAASC,yBAAyBA,CAACC,MAAM,EAAEC,cAAc,EAAEC,MAAM,EAAEC,UAAU,EAAE;EAC5F,IAAIH,MAAM,CAACE,MAAM,CAACE,cAAc,EAAE;IAChCC,MAAM,CAACC,IAAI,CAACH,UAAU,CAAC,CAACI,OAAO,CAACC,GAAG,IAAI;MACrC,IAAI,CAACN,MAAM,CAACM,GAAG,CAAC,IAAIN,MAAM,CAACO,IAAI,KAAK,IAAI,EAAE;QACxC,IAAIC,OAAO,GAAGZ,eAAe,CAACE,MAAM,CAACW,EAAE,EAAG,IAAGR,UAAU,CAACK,GAAG,CAAE,EAAC,CAAC,CAAC,CAAC,CAAC;QAClE,IAAI,CAACE,OAAO,EAAE;UACZA,OAAO,GAAGb,aAAa,CAAC,KAAK,EAAEM,UAAU,CAACK,GAAG,CAAC,CAAC;UAC/CE,OAAO,CAACE,SAAS,GAAGT,UAAU,CAACK,GAAG,CAAC;UACnCR,MAAM,CAACW,EAAE,CAACE,MAAM,CAACH,OAAO,CAAC;QAC3B;QACAR,MAAM,CAACM,GAAG,CAAC,GAAGE,OAAO;QACrBT,cAAc,CAACO,GAAG,CAAC,GAAGE,OAAO;MAC/B;IACF,CAAC,CAAC;EACJ;EACA,OAAOR,MAAM;AACf"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}