{"ast": null, "code": "import setTransition from './setTransition.js';\nimport transitionStart from './transitionStart.js';\nimport transitionEnd from './transitionEnd.js';\nexport default {\n  setTransition,\n  transitionStart,\n  transitionEnd\n};", "map": {"version": 3, "names": ["setTransition", "transitionStart", "transitionEnd"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/core/transition/index.js"], "sourcesContent": ["import setTransition from './setTransition.js';\nimport transitionStart from './transitionStart.js';\nimport transitionEnd from './transitionEnd.js';\nexport default {\n  setTransition,\n  transitionStart,\n  transitionEnd\n};"], "mappings": "AAAA,OAAOA,aAAa,MAAM,oBAAoB;AAC9C,OAAOC,eAAe,MAAM,sBAAsB;AAClD,OAAOC,aAAa,MAAM,oBAAoB;AAC9C,eAAe;EACbF,aAAa;EACbC,eAAe;EACfC;AACF,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}