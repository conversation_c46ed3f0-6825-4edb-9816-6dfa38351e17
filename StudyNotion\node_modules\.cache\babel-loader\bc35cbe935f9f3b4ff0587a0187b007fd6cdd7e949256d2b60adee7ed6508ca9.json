{"ast": null, "code": "export default function onClick(e) {\n  const swiper = this;\n  if (!swiper.enabled) return;\n  if (!swiper.allowClick) {\n    if (swiper.params.preventClicks) e.preventDefault();\n    if (swiper.params.preventClicksPropagation && swiper.animating) {\n      e.stopPropagation();\n      e.stopImmediatePropagation();\n    }\n  }\n}", "map": {"version": 3, "names": ["onClick", "e", "swiper", "enabled", "allowClick", "params", "preventClicks", "preventDefault", "preventClicksPropagation", "animating", "stopPropagation", "stopImmediatePropagation"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/core/events/onClick.js"], "sourcesContent": ["export default function onClick(e) {\n  const swiper = this;\n  if (!swiper.enabled) return;\n  if (!swiper.allowClick) {\n    if (swiper.params.preventClicks) e.preventDefault();\n    if (swiper.params.preventClicksPropagation && swiper.animating) {\n      e.stopPropagation();\n      e.stopImmediatePropagation();\n    }\n  }\n}"], "mappings": "AAAA,eAAe,SAASA,OAAOA,CAACC,CAAC,EAAE;EACjC,MAAMC,MAAM,GAAG,IAAI;EACnB,IAAI,CAACA,MAAM,CAACC,OAAO,EAAE;EACrB,IAAI,CAACD,MAAM,CAACE,UAAU,EAAE;IACtB,IAAIF,MAAM,CAACG,MAAM,CAACC,aAAa,EAAEL,CAAC,CAACM,cAAc,EAAE;IACnD,IAAIL,MAAM,CAACG,MAAM,CAACG,wBAAwB,IAAIN,MAAM,CAACO,SAAS,EAAE;MAC9DR,CAAC,CAACS,eAAe,EAAE;MACnBT,CAAC,CAACU,wBAAwB,EAAE;IAC9B;EACF;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}