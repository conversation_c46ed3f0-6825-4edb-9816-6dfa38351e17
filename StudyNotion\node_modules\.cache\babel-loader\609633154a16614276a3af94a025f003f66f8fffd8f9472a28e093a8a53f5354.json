{"ast": null, "code": "export default function minTranslate() {\n  return -this.snapGrid[0];\n}", "map": {"version": 3, "names": ["minTranslate", "snapGrid"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/core/translate/minTranslate.js"], "sourcesContent": ["export default function minTranslate() {\n  return -this.snapGrid[0];\n}"], "mappings": "AAAA,eAAe,SAASA,YAAYA,CAAA,EAAG;EACrC,OAAO,CAAC,IAAI,CAACC,QAAQ,CAAC,CAAC,CAAC;AAC1B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}