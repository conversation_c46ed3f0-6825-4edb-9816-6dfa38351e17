{"ast": null, "code": "export default function loopDestroy() {\n  const swiper = this;\n  const {\n    params,\n    slidesEl\n  } = swiper;\n  if (!params.loop || swiper.virtual && swiper.params.virtual.enabled) return;\n  swiper.recalcSlides();\n  const newSlidesOrder = [];\n  swiper.slides.forEach(slideEl => {\n    const index = typeof slideEl.swiperSlideIndex === 'undefined' ? slideEl.getAttribute('data-swiper-slide-index') * 1 : slideEl.swiperSlideIndex;\n    newSlidesOrder[index] = slideEl;\n  });\n  swiper.slides.forEach(slideEl => {\n    slideEl.removeAttribute('data-swiper-slide-index');\n  });\n  newSlidesOrder.forEach(slideEl => {\n    slidesEl.append(slideEl);\n  });\n  swiper.recalcSlides();\n  swiper.slideTo(swiper.realIndex, 0);\n}", "map": {"version": 3, "names": ["loop<PERSON><PERSON><PERSON>", "swiper", "params", "slidesEl", "loop", "virtual", "enabled", "recalcSlides", "newSlidesOrder", "slides", "for<PERSON>ach", "slideEl", "index", "swiperSlideIndex", "getAttribute", "removeAttribute", "append", "slideTo", "realIndex"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/core/loop/loopDestroy.js"], "sourcesContent": ["export default function loopDestroy() {\n  const swiper = this;\n  const {\n    params,\n    slidesEl\n  } = swiper;\n  if (!params.loop || swiper.virtual && swiper.params.virtual.enabled) return;\n  swiper.recalcSlides();\n  const newSlidesOrder = [];\n  swiper.slides.forEach(slideEl => {\n    const index = typeof slideEl.swiperSlideIndex === 'undefined' ? slideEl.getAttribute('data-swiper-slide-index') * 1 : slideEl.swiperSlideIndex;\n    newSlidesOrder[index] = slideEl;\n  });\n  swiper.slides.forEach(slideEl => {\n    slideEl.removeAttribute('data-swiper-slide-index');\n  });\n  newSlidesOrder.forEach(slideEl => {\n    slidesEl.append(slideEl);\n  });\n  swiper.recalcSlides();\n  swiper.slideTo(swiper.realIndex, 0);\n}"], "mappings": "AAAA,eAAe,SAASA,WAAWA,CAAA,EAAG;EACpC,MAAMC,MAAM,GAAG,IAAI;EACnB,MAAM;IACJC,MAAM;IACNC;EACF,CAAC,GAAGF,MAAM;EACV,IAAI,CAACC,MAAM,CAACE,IAAI,IAAIH,MAAM,CAACI,OAAO,IAAIJ,MAAM,CAACC,MAAM,CAACG,OAAO,CAACC,OAAO,EAAE;EACrEL,MAAM,CAACM,YAAY,EAAE;EACrB,MAAMC,cAAc,GAAG,EAAE;EACzBP,MAAM,CAACQ,MAAM,CAACC,OAAO,CAACC,OAAO,IAAI;IAC/B,MAAMC,KAAK,GAAG,OAAOD,OAAO,CAACE,gBAAgB,KAAK,WAAW,GAAGF,OAAO,CAACG,YAAY,CAAC,yBAAyB,CAAC,GAAG,CAAC,GAAGH,OAAO,CAACE,gBAAgB;IAC9IL,cAAc,CAACI,KAAK,CAAC,GAAGD,OAAO;EACjC,CAAC,CAAC;EACFV,MAAM,CAACQ,MAAM,CAACC,OAAO,CAACC,OAAO,IAAI;IAC/BA,OAAO,CAACI,eAAe,CAAC,yBAAyB,CAAC;EACpD,CAAC,CAAC;EACFP,cAAc,CAACE,OAAO,CAACC,OAAO,IAAI;IAChCR,QAAQ,CAACa,MAAM,CAACL,OAAO,CAAC;EAC1B,CAAC,CAAC;EACFV,MAAM,CAACM,YAAY,EAAE;EACrBN,MAAM,CAACgB,OAAO,CAAChB,MAAM,CAACiB,SAAS,EAAE,CAAC,CAAC;AACrC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}