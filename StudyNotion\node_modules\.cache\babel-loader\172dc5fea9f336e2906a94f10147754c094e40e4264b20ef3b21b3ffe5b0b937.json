{"ast": null, "code": "let powers = 0;\nexport const boolean = increment();\nexport const booleanish = increment();\nexport const overloadedBoolean = increment();\nexport const number = increment();\nexport const spaceSeparated = increment();\nexport const commaSeparated = increment();\nexport const commaOrSpaceSeparated = increment();\nfunction increment() {\n  return 2 ** ++powers;\n}", "map": {"version": 3, "names": ["powers", "boolean", "increment", "booleanish", "overloadedBoolean", "number", "spaceSeparated", "commaSeparated", "commaOrSpaceSeparated"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/property-information/lib/util/types.js"], "sourcesContent": ["let powers = 0\n\nexport const boolean = increment()\nexport const booleanish = increment()\nexport const overloadedBoolean = increment()\nexport const number = increment()\nexport const spaceSeparated = increment()\nexport const commaSeparated = increment()\nexport const commaOrSpaceSeparated = increment()\n\nfunction increment() {\n  return 2 ** ++powers\n}\n"], "mappings": "AAAA,IAAIA,MAAM,GAAG,CAAC;AAEd,OAAO,MAAMC,OAAO,GAAGC,SAAS,EAAE;AAClC,OAAO,MAAMC,UAAU,GAAGD,SAAS,EAAE;AACrC,OAAO,MAAME,iBAAiB,GAAGF,SAAS,EAAE;AAC5C,OAAO,MAAMG,MAAM,GAAGH,SAAS,EAAE;AACjC,OAAO,MAAMI,cAAc,GAAGJ,SAAS,EAAE;AACzC,OAAO,MAAMK,cAAc,GAAGL,SAAS,EAAE;AACzC,OAAO,MAAMM,qBAAqB,GAAGN,SAAS,EAAE;AAEhD,SAASA,SAASA,CAAA,EAAG;EACnB,OAAO,CAAC,IAAI,EAAEF,MAAM;AACtB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}