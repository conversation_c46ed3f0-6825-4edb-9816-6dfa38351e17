{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nvar _extends = Object.assign || function (target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i];\n    for (var key in source) {\n      if (Object.prototype.hasOwnProperty.call(source, key)) {\n        target[key] = source[key];\n      }\n    }\n  }\n  return target;\n};\nexports.default = Star;\nvar _react = require(\"react\");\nvar _react2 = _interopRequireDefault(_react);\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nvar defaultStyles = {\n  position: \"relative\",\n  overflow: \"hidden\",\n  cursor: \"pointer\",\n  display: \"block\",\n  float: \"left\"\n};\nfunction Star(props) {\n  var index = props.index,\n    active = props.active,\n    config = props.config,\n    onMouseOver = props.onMouseOver,\n    onMouseLeave = props.onMouseLeave,\n    onClick = props.onClick,\n    halfStarHidden = props.halfStarHidden,\n    halfStarAt = props.halfStarAt,\n    isUsingIcons = props.isUsingIcons,\n    uniqueness = props.uniqueness;\n  var color = config.color,\n    activeColor = config.activeColor,\n    size = config.size,\n    char = config.char,\n    isHalf = config.isHalf,\n    edit = config.edit,\n    halfIcon = config.halfIcon,\n    emptyIcon = config.emptyIcon,\n    filledIcon = config.filledIcon;\n  var starClass = '';\n  var half = false;\n  if (isHalf && !halfStarHidden && halfStarAt === index) {\n    if (!isUsingIcons) starClass = \"react-stars-\" + uniqueness;else starClass = 'react-stars-half';\n    half = true;\n  }\n  var style = _extends({}, defaultStyles, {\n    color: active ? activeColor : color,\n    cursor: edit ? 'pointer' : 'default',\n    fontSize: size + \"px\"\n  });\n  function renderIcon() {\n    if (!isUsingIcons) {\n      return char;\n    } else {\n      if (active) {\n        return filledIcon;\n      } else if (!active && half) {\n        return halfIcon;\n      } else {\n        return emptyIcon;\n      }\n    }\n  }\n  return _react2.default.createElement(\"span\", {\n    className: starClass,\n    style: style,\n    key: index,\n    \"data-index\": index,\n    \"data-forhalf\": filledIcon ? index : char,\n    onMouseOver: onMouseOver,\n    onMouseMove: onMouseOver,\n    onMouseLeave: onMouseLeave,\n    onClick: onClick\n  }, renderIcon());\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "_extends", "assign", "target", "i", "arguments", "length", "source", "key", "prototype", "hasOwnProperty", "call", "default", "Star", "_react", "require", "_react2", "_interopRequireDefault", "obj", "__esModule", "defaultStyles", "position", "overflow", "cursor", "display", "float", "props", "index", "active", "config", "onMouseOver", "onMouseLeave", "onClick", "halfStarHidden", "halfStarAt", "isUsingIcons", "uniqueness", "color", "activeColor", "size", "char", "is<PERSON>alf", "edit", "halfIcon", "emptyIcon", "filledIcon", "starClass", "half", "style", "fontSize", "renderIcon", "createElement", "className", "onMouseMove"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/react-rating-stars-component/dist/star.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\n\nvar _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; };\n\nexports.default = Star;\n\nvar _react = require(\"react\");\n\nvar _react2 = _interopRequireDefault(_react);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar defaultStyles = {\n    position: \"relative\",\n    overflow: \"hidden\",\n    cursor: \"pointer\",\n    display: \"block\",\n    float: \"left\"\n};\n\nfunction Star(props) {\n    var index = props.index,\n        active = props.active,\n        config = props.config,\n        onMouseOver = props.onMouseOver,\n        onMouseLeave = props.onMouseLeave,\n        onClick = props.onClick,\n        halfStarHidden = props.halfStarHidden,\n        halfStarAt = props.halfStarAt,\n        isUsingIcons = props.isUsingIcons,\n        uniqueness = props.uniqueness;\n    var color = config.color,\n        activeColor = config.activeColor,\n        size = config.size,\n        char = config.char,\n        isHalf = config.isHalf,\n        edit = config.edit,\n        halfIcon = config.halfIcon,\n        emptyIcon = config.emptyIcon,\n        filledIcon = config.filledIcon;\n\n\n    var starClass = '';\n    var half = false;\n\n    if (isHalf && !halfStarHidden && halfStarAt === index) {\n        if (!isUsingIcons) starClass = \"react-stars-\" + uniqueness;else starClass = 'react-stars-half';\n        half = true;\n    }\n\n    var style = _extends({}, defaultStyles, {\n        color: active ? activeColor : color,\n        cursor: edit ? 'pointer' : 'default',\n        fontSize: size + \"px\"\n    });\n\n    function renderIcon() {\n        if (!isUsingIcons) {\n            return char;\n        } else {\n            if (active) {\n                return filledIcon;\n            } else if (!active && half) {\n                return halfIcon;\n            } else {\n                return emptyIcon;\n            }\n        }\n    }\n\n    return _react2.default.createElement(\n        \"span\",\n        {\n            className: starClass,\n            style: style,\n            key: index,\n            \"data-index\": index,\n            \"data-forhalf\": filledIcon ? index : char,\n            onMouseOver: onMouseOver,\n            onMouseMove: onMouseOver,\n            onMouseLeave: onMouseLeave,\n            onClick: onClick },\n        renderIcon()\n    );\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EACzCC,KAAK,EAAE;AACX,CAAC,CAAC;AAEF,IAAIC,QAAQ,GAAGJ,MAAM,CAACK,MAAM,IAAI,UAAUC,MAAM,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC;IAAE,KAAK,IAAII,GAAG,IAAID,MAAM,EAAE;MAAE,IAAIV,MAAM,CAACY,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,MAAM,EAAEC,GAAG,CAAC,EAAE;QAAEL,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;MAAE;IAAE;EAAE;EAAE,OAAOL,MAAM;AAAE,CAAC;AAEhQJ,OAAO,CAACa,OAAO,GAAGC,IAAI;AAEtB,IAAIC,MAAM,GAAGC,OAAO,CAAC,OAAO,CAAC;AAE7B,IAAIC,OAAO,GAAGC,sBAAsB,CAACH,MAAM,CAAC;AAE5C,SAASG,sBAAsBA,CAACC,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEN,OAAO,EAAEM;EAAI,CAAC;AAAE;AAE9F,IAAIE,aAAa,GAAG;EAChBC,QAAQ,EAAE,UAAU;EACpBC,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,SAAS;EACjBC,OAAO,EAAE,OAAO;EAChBC,KAAK,EAAE;AACX,CAAC;AAED,SAASZ,IAAIA,CAACa,KAAK,EAAE;EACjB,IAAIC,KAAK,GAAGD,KAAK,CAACC,KAAK;IACnBC,MAAM,GAAGF,KAAK,CAACE,MAAM;IACrBC,MAAM,GAAGH,KAAK,CAACG,MAAM;IACrBC,WAAW,GAAGJ,KAAK,CAACI,WAAW;IAC/BC,YAAY,GAAGL,KAAK,CAACK,YAAY;IACjCC,OAAO,GAAGN,KAAK,CAACM,OAAO;IACvBC,cAAc,GAAGP,KAAK,CAACO,cAAc;IACrCC,UAAU,GAAGR,KAAK,CAACQ,UAAU;IAC7BC,YAAY,GAAGT,KAAK,CAACS,YAAY;IACjCC,UAAU,GAAGV,KAAK,CAACU,UAAU;EACjC,IAAIC,KAAK,GAAGR,MAAM,CAACQ,KAAK;IACpBC,WAAW,GAAGT,MAAM,CAACS,WAAW;IAChCC,IAAI,GAAGV,MAAM,CAACU,IAAI;IAClBC,IAAI,GAAGX,MAAM,CAACW,IAAI;IAClBC,MAAM,GAAGZ,MAAM,CAACY,MAAM;IACtBC,IAAI,GAAGb,MAAM,CAACa,IAAI;IAClBC,QAAQ,GAAGd,MAAM,CAACc,QAAQ;IAC1BC,SAAS,GAAGf,MAAM,CAACe,SAAS;IAC5BC,UAAU,GAAGhB,MAAM,CAACgB,UAAU;EAGlC,IAAIC,SAAS,GAAG,EAAE;EAClB,IAAIC,IAAI,GAAG,KAAK;EAEhB,IAAIN,MAAM,IAAI,CAACR,cAAc,IAAIC,UAAU,KAAKP,KAAK,EAAE;IACnD,IAAI,CAACQ,YAAY,EAAEW,SAAS,GAAG,cAAc,GAAGV,UAAU,CAAC,KAAKU,SAAS,GAAG,kBAAkB;IAC9FC,IAAI,GAAG,IAAI;EACf;EAEA,IAAIC,KAAK,GAAG/C,QAAQ,CAAC,CAAC,CAAC,EAAEmB,aAAa,EAAE;IACpCiB,KAAK,EAAET,MAAM,GAAGU,WAAW,GAAGD,KAAK;IACnCd,MAAM,EAAEmB,IAAI,GAAG,SAAS,GAAG,SAAS;IACpCO,QAAQ,EAAEV,IAAI,GAAG;EACrB,CAAC,CAAC;EAEF,SAASW,UAAUA,CAAA,EAAG;IAClB,IAAI,CAACf,YAAY,EAAE;MACf,OAAOK,IAAI;IACf,CAAC,MAAM;MACH,IAAIZ,MAAM,EAAE;QACR,OAAOiB,UAAU;MACrB,CAAC,MAAM,IAAI,CAACjB,MAAM,IAAImB,IAAI,EAAE;QACxB,OAAOJ,QAAQ;MACnB,CAAC,MAAM;QACH,OAAOC,SAAS;MACpB;IACJ;EACJ;EAEA,OAAO5B,OAAO,CAACJ,OAAO,CAACuC,aAAa,CAChC,MAAM,EACN;IACIC,SAAS,EAAEN,SAAS;IACpBE,KAAK,EAAEA,KAAK;IACZxC,GAAG,EAAEmB,KAAK;IACV,YAAY,EAAEA,KAAK;IACnB,cAAc,EAAEkB,UAAU,GAAGlB,KAAK,GAAGa,IAAI;IACzCV,WAAW,EAAEA,WAAW;IACxBuB,WAAW,EAAEvB,WAAW;IACxBC,YAAY,EAAEA,YAAY;IAC1BC,OAAO,EAAEA;EAAQ,CAAC,EACtBkB,UAAU,EAAE,CACf;AACL"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}