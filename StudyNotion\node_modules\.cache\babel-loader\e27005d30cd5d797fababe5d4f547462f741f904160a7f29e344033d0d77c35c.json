{"ast": null, "code": "var documentAll = typeof document == 'object' && document.all;\n\n// https://tc39.es/ecma262/#sec-IsHTMLDDA-internal-slot\n// eslint-disable-next-line unicorn/no-typeof-undefined -- required for testing\nvar IS_HTMLDDA = typeof documentAll == 'undefined' && documentAll !== undefined;\nmodule.exports = {\n  all: documentAll,\n  IS_HTMLDDA: IS_HTMLDDA\n};", "map": {"version": 3, "names": ["documentAll", "document", "all", "IS_HTMLDDA", "undefined", "module", "exports"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/core-js-pure/internals/document-all.js"], "sourcesContent": ["var documentAll = typeof document == 'object' && document.all;\n\n// https://tc39.es/ecma262/#sec-IsHTMLDDA-internal-slot\n// eslint-disable-next-line unicorn/no-typeof-undefined -- required for testing\nvar IS_HTMLDDA = typeof documentAll == 'undefined' && documentAll !== undefined;\n\nmodule.exports = {\n  all: documentAll,\n  IS_HTMLDDA: IS_HTMLDDA\n};\n"], "mappings": "AAAA,IAAIA,WAAW,GAAG,OAAOC,QAAQ,IAAI,QAAQ,IAAIA,QAAQ,CAACC,GAAG;;AAE7D;AACA;AACA,IAAIC,UAAU,GAAG,OAAOH,WAAW,IAAI,WAAW,IAAIA,WAAW,KAAKI,SAAS;AAE/EC,MAAM,CAACC,OAAO,GAAG;EACfJ,GAAG,EAAEF,WAAW;EAChBG,UAAU,EAAEA;AACd,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}