{"ast": null, "code": "import { processLazyPreloader } from '../../shared/process-lazy-preloader.js';\nexport default function onLoad(e) {\n  const swiper = this;\n  processLazyPreloader(swiper, e.target);\n  if (swiper.params.cssMode || swiper.params.slidesPerView !== 'auto' && !swiper.params.autoHeight) {\n    return;\n  }\n  swiper.update();\n}", "map": {"version": 3, "names": ["processLazyPreloader", "onLoad", "e", "swiper", "target", "params", "cssMode", "<PERSON><PERSON><PERSON><PERSON>iew", "autoHeight", "update"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/core/events/onLoad.js"], "sourcesContent": ["import { processLazyPreloader } from '../../shared/process-lazy-preloader.js';\nexport default function onLoad(e) {\n  const swiper = this;\n  processLazyPreloader(swiper, e.target);\n  if (swiper.params.cssMode || swiper.params.slidesPerView !== 'auto' && !swiper.params.autoHeight) {\n    return;\n  }\n  swiper.update();\n}"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,wCAAwC;AAC7E,eAAe,SAASC,MAAMA,CAACC,CAAC,EAAE;EAChC,MAAMC,MAAM,GAAG,IAAI;EACnBH,oBAAoB,CAACG,MAAM,EAAED,CAAC,CAACE,MAAM,CAAC;EACtC,IAAID,MAAM,CAACE,MAAM,CAACC,OAAO,IAAIH,MAAM,CAACE,MAAM,CAACE,aAAa,KAAK,MAAM,IAAI,CAACJ,MAAM,CAACE,MAAM,CAACG,UAAU,EAAE;IAChG;EACF;EACAL,MAAM,CAACM,MAAM,EAAE;AACjB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}