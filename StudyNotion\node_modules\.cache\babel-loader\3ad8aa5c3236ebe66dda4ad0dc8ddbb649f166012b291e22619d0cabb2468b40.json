{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\");\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nvar _classCallCheck2 = _interopRequireDefault(require(\"@babel/runtime/helpers/classCallCheck\"));\nvar _createClass2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createClass\"));\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"@babel/runtime/helpers/possibleConstructorReturn\"));\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"@babel/runtime/helpers/getPrototypeOf\"));\nvar _assertThisInitialized2 = _interopRequireDefault(require(\"@babel/runtime/helpers/assertThisInitialized\"));\nvar _inherits2 = _interopRequireDefault(require(\"@babel/runtime/helpers/inherits\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _react = _interopRequireWildcard(require(\"react\"));\nvar propTypes = {\n  player: _propTypes[\"default\"].object,\n  children: _propTypes[\"default\"].any\n};\nvar Popup = /*#__PURE__*/\nfunction (_Component) {\n  (0, _inherits2[\"default\"])(Popup, _Component);\n  function Popup(props, context) {\n    var _this;\n    (0, _classCallCheck2[\"default\"])(this, Popup);\n    _this = (0, _possibleConstructorReturn2[\"default\"])(this, (0, _getPrototypeOf2[\"default\"])(Popup).call(this, props, context));\n    _this.handleClick = _this.handleClick.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    return _this;\n  }\n  (0, _createClass2[\"default\"])(Popup, [{\n    key: \"handleClick\",\n    value: function handleClick(event) {\n      event.preventDefault(); // event.stopPropagation();\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var children = this.props.children;\n      return _react[\"default\"].createElement(\"div\", {\n        className: \"video-react-menu\",\n        onClick: this.handleClick\n      }, _react[\"default\"].createElement(\"div\", {\n        className: \"video-react-menu-content\"\n      }, children));\n    }\n  }]);\n  return Popup;\n}(_react.Component);\nexports[\"default\"] = Popup;\nPopup.propTypes = propTypes;\nPopup.displayName = 'Popup';", "map": {"version": 3, "names": ["_interopRequireWildcard", "require", "_interopRequireDefault", "Object", "defineProperty", "exports", "value", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_assertThisInitialized2", "_inherits2", "_propTypes", "_react", "propTypes", "player", "object", "children", "any", "Popup", "_Component", "props", "context", "_this", "call", "handleClick", "bind", "key", "event", "preventDefault", "render", "createElement", "className", "onClick", "Component", "displayName"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/video-react/lib/components/popup/Popup.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\");\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _classCallCheck2 = _interopRequireDefault(require(\"@babel/runtime/helpers/classCallCheck\"));\n\nvar _createClass2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createClass\"));\n\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"@babel/runtime/helpers/possibleConstructorReturn\"));\n\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"@babel/runtime/helpers/getPrototypeOf\"));\n\nvar _assertThisInitialized2 = _interopRequireDefault(require(\"@babel/runtime/helpers/assertThisInitialized\"));\n\nvar _inherits2 = _interopRequireDefault(require(\"@babel/runtime/helpers/inherits\"));\n\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\n\nvar _react = _interopRequireWildcard(require(\"react\"));\n\nvar propTypes = {\n  player: _propTypes[\"default\"].object,\n  children: _propTypes[\"default\"].any\n};\n\nvar Popup =\n/*#__PURE__*/\nfunction (_Component) {\n  (0, _inherits2[\"default\"])(Popup, _Component);\n\n  function Popup(props, context) {\n    var _this;\n\n    (0, _classCallCheck2[\"default\"])(this, Popup);\n    _this = (0, _possibleConstructorReturn2[\"default\"])(this, (0, _getPrototypeOf2[\"default\"])(Popup).call(this, props, context));\n    _this.handleClick = _this.handleClick.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    return _this;\n  }\n\n  (0, _createClass2[\"default\"])(Popup, [{\n    key: \"handleClick\",\n    value: function handleClick(event) {\n      event.preventDefault(); // event.stopPropagation();\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var children = this.props.children;\n      return _react[\"default\"].createElement(\"div\", {\n        className: \"video-react-menu\",\n        onClick: this.handleClick\n      }, _react[\"default\"].createElement(\"div\", {\n        className: \"video-react-menu-content\"\n      }, children));\n    }\n  }]);\n  return Popup;\n}(_react.Component);\n\nexports[\"default\"] = Popup;\nPopup.propTypes = propTypes;\nPopup.displayName = 'Popup';"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,uBAAuB,GAAGC,OAAO,CAAC,+CAA+C,CAAC;AAEtF,IAAIC,sBAAsB,GAAGD,OAAO,CAAC,8CAA8C,CAAC;AAEpFE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;AAE3B,IAAIE,gBAAgB,GAAGL,sBAAsB,CAACD,OAAO,CAAC,uCAAuC,CAAC,CAAC;AAE/F,IAAIO,aAAa,GAAGN,sBAAsB,CAACD,OAAO,CAAC,oCAAoC,CAAC,CAAC;AAEzF,IAAIQ,2BAA2B,GAAGP,sBAAsB,CAACD,OAAO,CAAC,kDAAkD,CAAC,CAAC;AAErH,IAAIS,gBAAgB,GAAGR,sBAAsB,CAACD,OAAO,CAAC,uCAAuC,CAAC,CAAC;AAE/F,IAAIU,uBAAuB,GAAGT,sBAAsB,CAACD,OAAO,CAAC,8CAA8C,CAAC,CAAC;AAE7G,IAAIW,UAAU,GAAGV,sBAAsB,CAACD,OAAO,CAAC,iCAAiC,CAAC,CAAC;AAEnF,IAAIY,UAAU,GAAGX,sBAAsB,CAACD,OAAO,CAAC,YAAY,CAAC,CAAC;AAE9D,IAAIa,MAAM,GAAGd,uBAAuB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AAEtD,IAAIc,SAAS,GAAG;EACdC,MAAM,EAAEH,UAAU,CAAC,SAAS,CAAC,CAACI,MAAM;EACpCC,QAAQ,EAAEL,UAAU,CAAC,SAAS,CAAC,CAACM;AAClC,CAAC;AAED,IAAIC,KAAK,GACT;AACA,UAAUC,UAAU,EAAE;EACpB,CAAC,CAAC,EAAET,UAAU,CAAC,SAAS,CAAC,EAAEQ,KAAK,EAAEC,UAAU,CAAC;EAE7C,SAASD,KAAKA,CAACE,KAAK,EAAEC,OAAO,EAAE;IAC7B,IAAIC,KAAK;IAET,CAAC,CAAC,EAAEjB,gBAAgB,CAAC,SAAS,CAAC,EAAE,IAAI,EAAEa,KAAK,CAAC;IAC7CI,KAAK,GAAG,CAAC,CAAC,EAAEf,2BAA2B,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAEC,gBAAgB,CAAC,SAAS,CAAC,EAAEU,KAAK,CAAC,CAACK,IAAI,CAAC,IAAI,EAAEH,KAAK,EAAEC,OAAO,CAAC,CAAC;IAC7HC,KAAK,CAACE,WAAW,GAAGF,KAAK,CAACE,WAAW,CAACC,IAAI,CAAC,CAAC,CAAC,EAAEhB,uBAAuB,CAAC,SAAS,CAAC,EAAEa,KAAK,CAAC,CAAC;IAC1F,OAAOA,KAAK;EACd;EAEA,CAAC,CAAC,EAAEhB,aAAa,CAAC,SAAS,CAAC,EAAEY,KAAK,EAAE,CAAC;IACpCQ,GAAG,EAAE,aAAa;IAClBtB,KAAK,EAAE,SAASoB,WAAWA,CAACG,KAAK,EAAE;MACjCA,KAAK,CAACC,cAAc,EAAE,CAAC,CAAC;IAC1B;EACF,CAAC,EAAE;IACDF,GAAG,EAAE,QAAQ;IACbtB,KAAK,EAAE,SAASyB,MAAMA,CAAA,EAAG;MACvB,IAAIb,QAAQ,GAAG,IAAI,CAACI,KAAK,CAACJ,QAAQ;MAClC,OAAOJ,MAAM,CAAC,SAAS,CAAC,CAACkB,aAAa,CAAC,KAAK,EAAE;QAC5CC,SAAS,EAAE,kBAAkB;QAC7BC,OAAO,EAAE,IAAI,CAACR;MAChB,CAAC,EAAEZ,MAAM,CAAC,SAAS,CAAC,CAACkB,aAAa,CAAC,KAAK,EAAE;QACxCC,SAAS,EAAE;MACb,CAAC,EAAEf,QAAQ,CAAC,CAAC;IACf;EACF,CAAC,CAAC,CAAC;EACH,OAAOE,KAAK;AACd,CAAC,CAACN,MAAM,CAACqB,SAAS,CAAC;AAEnB9B,OAAO,CAAC,SAAS,CAAC,GAAGe,KAAK;AAC1BA,KAAK,CAACL,SAAS,GAAGA,SAAS;AAC3BK,KAAK,CAACgB,WAAW,GAAG,OAAO"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}