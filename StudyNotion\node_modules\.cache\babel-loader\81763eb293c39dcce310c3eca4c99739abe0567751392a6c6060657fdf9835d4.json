{"ast": null, "code": "/* eslint no-param-reassign: \"off\" */\nimport { getDocument } from 'ssr-window';\nimport { extend, deleteProps, createElement, elementChildren, elementStyle, elementIndex } from '../shared/utils.js';\nimport { getSupport } from '../shared/get-support.js';\nimport { getDevice } from '../shared/get-device.js';\nimport { getBrowser } from '../shared/get-browser.js';\nimport Resize from './modules/resize/resize.js';\nimport Observer from './modules/observer/observer.js';\nimport eventsEmitter from './events-emitter.js';\nimport update from './update/index.js';\nimport translate from './translate/index.js';\nimport transition from './transition/index.js';\nimport slide from './slide/index.js';\nimport loop from './loop/index.js';\nimport grabCursor from './grab-cursor/index.js';\nimport events from './events/index.js';\nimport breakpoints from './breakpoints/index.js';\nimport classes from './classes/index.js';\nimport checkOverflow from './check-overflow/index.js';\nimport defaults from './defaults.js';\nimport moduleExtendParams from './moduleExtendParams.js';\nimport { processLazyPreloader, preload } from '../shared/process-lazy-preloader.js';\nconst prototypes = {\n  eventsEmitter,\n  update,\n  translate,\n  transition,\n  slide,\n  loop,\n  grabCursor,\n  events,\n  breakpoints,\n  checkOverflow,\n  classes\n};\nconst extendedDefaults = {};\nclass Swiper {\n  constructor() {\n    let el;\n    let params;\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    if (args.length === 1 && args[0].constructor && Object.prototype.toString.call(args[0]).slice(8, -1) === 'Object') {\n      params = args[0];\n    } else {\n      [el, params] = args;\n    }\n    if (!params) params = {};\n    params = extend({}, params);\n    if (el && !params.el) params.el = el;\n    const document = getDocument();\n    if (params.el && typeof params.el === 'string' && document.querySelectorAll(params.el).length > 1) {\n      const swipers = [];\n      document.querySelectorAll(params.el).forEach(containerEl => {\n        const newParams = extend({}, params, {\n          el: containerEl\n        });\n        swipers.push(new Swiper(newParams));\n      });\n      // eslint-disable-next-line no-constructor-return\n      return swipers;\n    }\n\n    // Swiper Instance\n    const swiper = this;\n    swiper.__swiper__ = true;\n    swiper.support = getSupport();\n    swiper.device = getDevice({\n      userAgent: params.userAgent\n    });\n    swiper.browser = getBrowser();\n    swiper.eventsListeners = {};\n    swiper.eventsAnyListeners = [];\n    swiper.modules = [...swiper.__modules__];\n    if (params.modules && Array.isArray(params.modules)) {\n      swiper.modules.push(...params.modules);\n    }\n    const allModulesParams = {};\n    swiper.modules.forEach(mod => {\n      mod({\n        params,\n        swiper,\n        extendParams: moduleExtendParams(params, allModulesParams),\n        on: swiper.on.bind(swiper),\n        once: swiper.once.bind(swiper),\n        off: swiper.off.bind(swiper),\n        emit: swiper.emit.bind(swiper)\n      });\n    });\n\n    // Extend defaults with modules params\n    const swiperParams = extend({}, defaults, allModulesParams);\n\n    // Extend defaults with passed params\n    swiper.params = extend({}, swiperParams, extendedDefaults, params);\n    swiper.originalParams = extend({}, swiper.params);\n    swiper.passedParams = extend({}, params);\n\n    // add event listeners\n    if (swiper.params && swiper.params.on) {\n      Object.keys(swiper.params.on).forEach(eventName => {\n        swiper.on(eventName, swiper.params.on[eventName]);\n      });\n    }\n    if (swiper.params && swiper.params.onAny) {\n      swiper.onAny(swiper.params.onAny);\n    }\n\n    // Extend Swiper\n    Object.assign(swiper, {\n      enabled: swiper.params.enabled,\n      el,\n      // Classes\n      classNames: [],\n      // Slides\n      slides: [],\n      slidesGrid: [],\n      snapGrid: [],\n      slidesSizesGrid: [],\n      // isDirection\n      isHorizontal() {\n        return swiper.params.direction === 'horizontal';\n      },\n      isVertical() {\n        return swiper.params.direction === 'vertical';\n      },\n      // Indexes\n      activeIndex: 0,\n      realIndex: 0,\n      //\n      isBeginning: true,\n      isEnd: false,\n      // Props\n      translate: 0,\n      previousTranslate: 0,\n      progress: 0,\n      velocity: 0,\n      animating: false,\n      cssOverflowAdjustment() {\n        // Returns 0 unless `translate` is > 2**23\n        // Should be subtracted from css values to prevent overflow\n        return Math.trunc(this.translate / 2 ** 23) * 2 ** 23;\n      },\n      // Locks\n      allowSlideNext: swiper.params.allowSlideNext,\n      allowSlidePrev: swiper.params.allowSlidePrev,\n      // Touch Events\n      touchEventsData: {\n        isTouched: undefined,\n        isMoved: undefined,\n        allowTouchCallbacks: undefined,\n        touchStartTime: undefined,\n        isScrolling: undefined,\n        currentTranslate: undefined,\n        startTranslate: undefined,\n        allowThresholdMove: undefined,\n        // Form elements to match\n        focusableElements: swiper.params.focusableElements,\n        // Last click time\n        lastClickTime: 0,\n        clickTimeout: undefined,\n        // Velocities\n        velocities: [],\n        allowMomentumBounce: undefined,\n        startMoving: undefined,\n        evCache: []\n      },\n      // Clicks\n      allowClick: true,\n      // Touches\n      allowTouchMove: swiper.params.allowTouchMove,\n      touches: {\n        startX: 0,\n        startY: 0,\n        currentX: 0,\n        currentY: 0,\n        diff: 0\n      },\n      // Images\n      imagesToLoad: [],\n      imagesLoaded: 0\n    });\n    swiper.emit('_swiper');\n\n    // Init\n    if (swiper.params.init) {\n      swiper.init();\n    }\n\n    // Return app instance\n    // eslint-disable-next-line no-constructor-return\n    return swiper;\n  }\n  getSlideIndex(slideEl) {\n    const {\n      slidesEl,\n      params\n    } = this;\n    const slides = elementChildren(slidesEl, `.${params.slideClass}, swiper-slide`);\n    const firstSlideIndex = elementIndex(slides[0]);\n    return elementIndex(slideEl) - firstSlideIndex;\n  }\n  getSlideIndexByData(index) {\n    return this.getSlideIndex(this.slides.filter(slideEl => slideEl.getAttribute('data-swiper-slide-index') * 1 === index)[0]);\n  }\n  recalcSlides() {\n    const swiper = this;\n    const {\n      slidesEl,\n      params\n    } = swiper;\n    swiper.slides = elementChildren(slidesEl, `.${params.slideClass}, swiper-slide`);\n  }\n  enable() {\n    const swiper = this;\n    if (swiper.enabled) return;\n    swiper.enabled = true;\n    if (swiper.params.grabCursor) {\n      swiper.setGrabCursor();\n    }\n    swiper.emit('enable');\n  }\n  disable() {\n    const swiper = this;\n    if (!swiper.enabled) return;\n    swiper.enabled = false;\n    if (swiper.params.grabCursor) {\n      swiper.unsetGrabCursor();\n    }\n    swiper.emit('disable');\n  }\n  setProgress(progress, speed) {\n    const swiper = this;\n    progress = Math.min(Math.max(progress, 0), 1);\n    const min = swiper.minTranslate();\n    const max = swiper.maxTranslate();\n    const current = (max - min) * progress + min;\n    swiper.translateTo(current, typeof speed === 'undefined' ? 0 : speed);\n    swiper.updateActiveIndex();\n    swiper.updateSlidesClasses();\n  }\n  emitContainerClasses() {\n    const swiper = this;\n    if (!swiper.params._emitClasses || !swiper.el) return;\n    const cls = swiper.el.className.split(' ').filter(className => {\n      return className.indexOf('swiper') === 0 || className.indexOf(swiper.params.containerModifierClass) === 0;\n    });\n    swiper.emit('_containerClasses', cls.join(' '));\n  }\n  getSlideClasses(slideEl) {\n    const swiper = this;\n    if (swiper.destroyed) return '';\n    return slideEl.className.split(' ').filter(className => {\n      return className.indexOf('swiper-slide') === 0 || className.indexOf(swiper.params.slideClass) === 0;\n    }).join(' ');\n  }\n  emitSlidesClasses() {\n    const swiper = this;\n    if (!swiper.params._emitClasses || !swiper.el) return;\n    const updates = [];\n    swiper.slides.forEach(slideEl => {\n      const classNames = swiper.getSlideClasses(slideEl);\n      updates.push({\n        slideEl,\n        classNames\n      });\n      swiper.emit('_slideClass', slideEl, classNames);\n    });\n    swiper.emit('_slideClasses', updates);\n  }\n  slidesPerViewDynamic() {\n    let view = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'current';\n    let exact = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    const swiper = this;\n    const {\n      params,\n      slides,\n      slidesGrid,\n      slidesSizesGrid,\n      size: swiperSize,\n      activeIndex\n    } = swiper;\n    let spv = 1;\n    if (params.centeredSlides) {\n      let slideSize = slides[activeIndex] ? slides[activeIndex].swiperSlideSize : 0;\n      let breakLoop;\n      for (let i = activeIndex + 1; i < slides.length; i += 1) {\n        if (slides[i] && !breakLoop) {\n          slideSize += slides[i].swiperSlideSize;\n          spv += 1;\n          if (slideSize > swiperSize) breakLoop = true;\n        }\n      }\n      for (let i = activeIndex - 1; i >= 0; i -= 1) {\n        if (slides[i] && !breakLoop) {\n          slideSize += slides[i].swiperSlideSize;\n          spv += 1;\n          if (slideSize > swiperSize) breakLoop = true;\n        }\n      }\n    } else {\n      // eslint-disable-next-line\n      if (view === 'current') {\n        for (let i = activeIndex + 1; i < slides.length; i += 1) {\n          const slideInView = exact ? slidesGrid[i] + slidesSizesGrid[i] - slidesGrid[activeIndex] < swiperSize : slidesGrid[i] - slidesGrid[activeIndex] < swiperSize;\n          if (slideInView) {\n            spv += 1;\n          }\n        }\n      } else {\n        // previous\n        for (let i = activeIndex - 1; i >= 0; i -= 1) {\n          const slideInView = slidesGrid[activeIndex] - slidesGrid[i] < swiperSize;\n          if (slideInView) {\n            spv += 1;\n          }\n        }\n      }\n    }\n    return spv;\n  }\n  update() {\n    const swiper = this;\n    if (!swiper || swiper.destroyed) return;\n    const {\n      snapGrid,\n      params\n    } = swiper;\n    // Breakpoints\n    if (params.breakpoints) {\n      swiper.setBreakpoint();\n    }\n    [...swiper.el.querySelectorAll('[loading=\"lazy\"]')].forEach(imageEl => {\n      if (imageEl.complete) {\n        processLazyPreloader(swiper, imageEl);\n      }\n    });\n    swiper.updateSize();\n    swiper.updateSlides();\n    swiper.updateProgress();\n    swiper.updateSlidesClasses();\n    function setTranslate() {\n      const translateValue = swiper.rtlTranslate ? swiper.translate * -1 : swiper.translate;\n      const newTranslate = Math.min(Math.max(translateValue, swiper.maxTranslate()), swiper.minTranslate());\n      swiper.setTranslate(newTranslate);\n      swiper.updateActiveIndex();\n      swiper.updateSlidesClasses();\n    }\n    let translated;\n    if (params.freeMode && params.freeMode.enabled && !params.cssMode) {\n      setTranslate();\n      if (params.autoHeight) {\n        swiper.updateAutoHeight();\n      }\n    } else {\n      if ((params.slidesPerView === 'auto' || params.slidesPerView > 1) && swiper.isEnd && !params.centeredSlides) {\n        const slides = swiper.virtual && params.virtual.enabled ? swiper.virtual.slides : swiper.slides;\n        translated = swiper.slideTo(slides.length - 1, 0, false, true);\n      } else {\n        translated = swiper.slideTo(swiper.activeIndex, 0, false, true);\n      }\n      if (!translated) {\n        setTranslate();\n      }\n    }\n    if (params.watchOverflow && snapGrid !== swiper.snapGrid) {\n      swiper.checkOverflow();\n    }\n    swiper.emit('update');\n  }\n  changeDirection(newDirection) {\n    let needUpdate = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n    const swiper = this;\n    const currentDirection = swiper.params.direction;\n    if (!newDirection) {\n      // eslint-disable-next-line\n      newDirection = currentDirection === 'horizontal' ? 'vertical' : 'horizontal';\n    }\n    if (newDirection === currentDirection || newDirection !== 'horizontal' && newDirection !== 'vertical') {\n      return swiper;\n    }\n    swiper.el.classList.remove(`${swiper.params.containerModifierClass}${currentDirection}`);\n    swiper.el.classList.add(`${swiper.params.containerModifierClass}${newDirection}`);\n    swiper.emitContainerClasses();\n    swiper.params.direction = newDirection;\n    swiper.slides.forEach(slideEl => {\n      if (newDirection === 'vertical') {\n        slideEl.style.width = '';\n      } else {\n        slideEl.style.height = '';\n      }\n    });\n    swiper.emit('changeDirection');\n    if (needUpdate) swiper.update();\n    return swiper;\n  }\n  changeLanguageDirection(direction) {\n    const swiper = this;\n    if (swiper.rtl && direction === 'rtl' || !swiper.rtl && direction === 'ltr') return;\n    swiper.rtl = direction === 'rtl';\n    swiper.rtlTranslate = swiper.params.direction === 'horizontal' && swiper.rtl;\n    if (swiper.rtl) {\n      swiper.el.classList.add(`${swiper.params.containerModifierClass}rtl`);\n      swiper.el.dir = 'rtl';\n    } else {\n      swiper.el.classList.remove(`${swiper.params.containerModifierClass}rtl`);\n      swiper.el.dir = 'ltr';\n    }\n    swiper.update();\n  }\n  mount(element) {\n    const swiper = this;\n    if (swiper.mounted) return true;\n\n    // Find el\n    let el = element || swiper.params.el;\n    if (typeof el === 'string') {\n      el = document.querySelector(el);\n    }\n    if (!el) {\n      return false;\n    }\n    el.swiper = swiper;\n    if (el.shadowEl) {\n      swiper.isElement = true;\n    }\n    const getWrapperSelector = () => {\n      return `.${(swiper.params.wrapperClass || '').trim().split(' ').join('.')}`;\n    };\n    const getWrapper = () => {\n      if (el && el.shadowRoot && el.shadowRoot.querySelector) {\n        const res = el.shadowRoot.querySelector(getWrapperSelector());\n        // Children needs to return slot items\n        return res;\n      }\n      return elementChildren(el, getWrapperSelector())[0];\n    };\n    // Find Wrapper\n    let wrapperEl = getWrapper();\n    if (!wrapperEl && swiper.params.createElements) {\n      wrapperEl = createElement('div', swiper.params.wrapperClass);\n      el.append(wrapperEl);\n      elementChildren(el, `.${swiper.params.slideClass}`).forEach(slideEl => {\n        wrapperEl.append(slideEl);\n      });\n    }\n    Object.assign(swiper, {\n      el,\n      wrapperEl,\n      slidesEl: swiper.isElement ? el : wrapperEl,\n      mounted: true,\n      // RTL\n      rtl: el.dir.toLowerCase() === 'rtl' || elementStyle(el, 'direction') === 'rtl',\n      rtlTranslate: swiper.params.direction === 'horizontal' && (el.dir.toLowerCase() === 'rtl' || elementStyle(el, 'direction') === 'rtl'),\n      wrongRTL: elementStyle(wrapperEl, 'display') === '-webkit-box'\n    });\n    return true;\n  }\n  init(el) {\n    const swiper = this;\n    if (swiper.initialized) return swiper;\n    const mounted = swiper.mount(el);\n    if (mounted === false) return swiper;\n    swiper.emit('beforeInit');\n\n    // Set breakpoint\n    if (swiper.params.breakpoints) {\n      swiper.setBreakpoint();\n    }\n\n    // Add Classes\n    swiper.addClasses();\n\n    // Update size\n    swiper.updateSize();\n\n    // Update slides\n    swiper.updateSlides();\n    if (swiper.params.watchOverflow) {\n      swiper.checkOverflow();\n    }\n\n    // Set Grab Cursor\n    if (swiper.params.grabCursor && swiper.enabled) {\n      swiper.setGrabCursor();\n    }\n\n    // Slide To Initial Slide\n    if (swiper.params.loop && swiper.virtual && swiper.params.virtual.enabled) {\n      swiper.slideTo(swiper.params.initialSlide + swiper.virtual.slidesBefore, 0, swiper.params.runCallbacksOnInit, false, true);\n    } else {\n      swiper.slideTo(swiper.params.initialSlide, 0, swiper.params.runCallbacksOnInit, false, true);\n    }\n\n    // Create loop\n    if (swiper.params.loop) {\n      swiper.loopCreate();\n    }\n\n    // Attach events\n    swiper.attachEvents();\n    [...swiper.el.querySelectorAll('[loading=\"lazy\"]')].forEach(imageEl => {\n      if (imageEl.complete) {\n        processLazyPreloader(swiper, imageEl);\n      } else {\n        imageEl.addEventListener('load', e => {\n          processLazyPreloader(swiper, e.target);\n        });\n      }\n    });\n    preload(swiper);\n\n    // Init Flag\n    swiper.initialized = true;\n    preload(swiper);\n\n    // Emit\n    swiper.emit('init');\n    swiper.emit('afterInit');\n    return swiper;\n  }\n  destroy() {\n    let deleteInstance = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;\n    let cleanStyles = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n    const swiper = this;\n    const {\n      params,\n      el,\n      wrapperEl,\n      slides\n    } = swiper;\n    if (typeof swiper.params === 'undefined' || swiper.destroyed) {\n      return null;\n    }\n    swiper.emit('beforeDestroy');\n\n    // Init Flag\n    swiper.initialized = false;\n\n    // Detach events\n    swiper.detachEvents();\n\n    // Destroy loop\n    if (params.loop) {\n      swiper.loopDestroy();\n    }\n\n    // Cleanup styles\n    if (cleanStyles) {\n      swiper.removeClasses();\n      el.removeAttribute('style');\n      wrapperEl.removeAttribute('style');\n      if (slides && slides.length) {\n        slides.forEach(slideEl => {\n          slideEl.classList.remove(params.slideVisibleClass, params.slideActiveClass, params.slideNextClass, params.slidePrevClass);\n          slideEl.removeAttribute('style');\n          slideEl.removeAttribute('data-swiper-slide-index');\n        });\n      }\n    }\n    swiper.emit('destroy');\n\n    // Detach emitter events\n    Object.keys(swiper.eventsListeners).forEach(eventName => {\n      swiper.off(eventName);\n    });\n    if (deleteInstance !== false) {\n      swiper.el.swiper = null;\n      deleteProps(swiper);\n    }\n    swiper.destroyed = true;\n    return null;\n  }\n  static extendDefaults(newDefaults) {\n    extend(extendedDefaults, newDefaults);\n  }\n  static get extendedDefaults() {\n    return extendedDefaults;\n  }\n  static get defaults() {\n    return defaults;\n  }\n  static installModule(mod) {\n    if (!Swiper.prototype.__modules__) Swiper.prototype.__modules__ = [];\n    const modules = Swiper.prototype.__modules__;\n    if (typeof mod === 'function' && modules.indexOf(mod) < 0) {\n      modules.push(mod);\n    }\n  }\n  static use(module) {\n    if (Array.isArray(module)) {\n      module.forEach(m => Swiper.installModule(m));\n      return Swiper;\n    }\n    Swiper.installModule(module);\n    return Swiper;\n  }\n}\nObject.keys(prototypes).forEach(prototypeGroup => {\n  Object.keys(prototypes[prototypeGroup]).forEach(protoMethod => {\n    Swiper.prototype[protoMethod] = prototypes[prototypeGroup][protoMethod];\n  });\n});\nSwiper.use([Resize, Observer]);\nexport default Swiper;", "map": {"version": 3, "names": ["getDocument", "extend", "deleteProps", "createElement", "elementChildren", "elementStyle", "elementIndex", "getSupport", "getDevice", "<PERSON><PERSON><PERSON><PERSON>", "Resize", "Observer", "eventsEmitter", "update", "translate", "transition", "slide", "loop", "grabCursor", "events", "breakpoints", "classes", "checkOverflow", "defaults", "moduleExtendParams", "processLazyPreloader", "preload", "prototypes", "extendedDefaults", "Swiper", "constructor", "el", "params", "_len", "arguments", "length", "args", "Array", "_key", "Object", "prototype", "toString", "call", "slice", "document", "querySelectorAll", "swipers", "for<PERSON>ach", "containerEl", "newParams", "push", "swiper", "__swiper__", "support", "device", "userAgent", "browser", "eventsListeners", "eventsAnyListeners", "modules", "__modules__", "isArray", "allModulesParams", "mod", "extendParams", "on", "bind", "once", "off", "emit", "swiperParams", "originalParams", "passedParams", "keys", "eventName", "onAny", "assign", "enabled", "classNames", "slides", "slidesGrid", "snapGrid", "slidesSizesGrid", "isHorizontal", "direction", "isVertical", "activeIndex", "realIndex", "isBeginning", "isEnd", "previousTranslate", "progress", "velocity", "animating", "cssOverflowAdjustment", "Math", "trunc", "allowSlideNext", "allowSlidePrev", "touchEventsData", "isTouched", "undefined", "isMoved", "allowTouchCallbacks", "touchStartTime", "isScrolling", "currentTranslate", "startTranslate", "allowThresholdMove", "focusableElements", "lastClickTime", "clickTimeout", "velocities", "allowMomentumBounce", "startMoving", "ev<PERSON><PERSON>", "allowClick", "allowTouchMove", "touches", "startX", "startY", "currentX", "currentY", "diff", "imagesToLoad", "imagesLoaded", "init", "getSlideIndex", "slideEl", "slidesEl", "slideClass", "firstSlideIndex", "getSlideIndexByData", "index", "filter", "getAttribute", "recalcSlides", "enable", "setGrabCursor", "disable", "unsetGrabCursor", "setProgress", "speed", "min", "max", "minTranslate", "maxTranslate", "current", "translateTo", "updateActiveIndex", "updateSlidesClasses", "emitContainerClasses", "_emitClasses", "cls", "className", "split", "indexOf", "containerModifierClass", "join", "getSlideClasses", "destroyed", "emitSlidesClasses", "updates", "slidesPerViewDynamic", "view", "exact", "size", "swiperSize", "spv", "centeredSlides", "slideSize", "swiperSlideSize", "breakLoop", "i", "slideInView", "setBreakpoint", "imageEl", "complete", "updateSize", "updateSlides", "updateProgress", "setTranslate", "translateValue", "rtlTranslate", "newTranslate", "translated", "freeMode", "cssMode", "autoHeight", "updateAutoHeight", "<PERSON><PERSON><PERSON><PERSON>iew", "virtual", "slideTo", "watchOverflow", "changeDirection", "newDirection", "needUpdate", "currentDirection", "classList", "remove", "add", "style", "width", "height", "changeLanguageDirection", "rtl", "dir", "mount", "element", "mounted", "querySelector", "shadowEl", "isElement", "getWrapperSelector", "wrapperClass", "trim", "getWrapper", "shadowRoot", "res", "wrapperEl", "createElements", "append", "toLowerCase", "wrongRTL", "initialized", "addClasses", "initialSlide", "slidesBefore", "runCallbacksOnInit", "loopCreate", "attachEvents", "addEventListener", "e", "target", "destroy", "deleteInstance", "cleanStyles", "detachEvents", "loop<PERSON><PERSON><PERSON>", "removeClasses", "removeAttribute", "slideVisibleClass", "slideActiveClass", "slideNextClass", "slidePrevClass", "extendDefaults", "newDefaults", "installModule", "use", "module", "m", "prototypeGroup", "protoMethod"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/core/core.js"], "sourcesContent": ["/* eslint no-param-reassign: \"off\" */\nimport { getDocument } from 'ssr-window';\nimport { extend, deleteProps, createElement, elementChildren, elementStyle, elementIndex } from '../shared/utils.js';\nimport { getSupport } from '../shared/get-support.js';\nimport { getDevice } from '../shared/get-device.js';\nimport { getBrowser } from '../shared/get-browser.js';\nimport Resize from './modules/resize/resize.js';\nimport Observer from './modules/observer/observer.js';\nimport eventsEmitter from './events-emitter.js';\nimport update from './update/index.js';\nimport translate from './translate/index.js';\nimport transition from './transition/index.js';\nimport slide from './slide/index.js';\nimport loop from './loop/index.js';\nimport grabCursor from './grab-cursor/index.js';\nimport events from './events/index.js';\nimport breakpoints from './breakpoints/index.js';\nimport classes from './classes/index.js';\nimport checkOverflow from './check-overflow/index.js';\nimport defaults from './defaults.js';\nimport moduleExtendParams from './moduleExtendParams.js';\nimport { processLazyPreloader, preload } from '../shared/process-lazy-preloader.js';\nconst prototypes = {\n  eventsEmitter,\n  update,\n  translate,\n  transition,\n  slide,\n  loop,\n  grabCursor,\n  events,\n  breakpoints,\n  checkOverflow,\n  classes\n};\nconst extendedDefaults = {};\nclass Swiper {\n  constructor(...args) {\n    let el;\n    let params;\n    if (args.length === 1 && args[0].constructor && Object.prototype.toString.call(args[0]).slice(8, -1) === 'Object') {\n      params = args[0];\n    } else {\n      [el, params] = args;\n    }\n    if (!params) params = {};\n    params = extend({}, params);\n    if (el && !params.el) params.el = el;\n    const document = getDocument();\n    if (params.el && typeof params.el === 'string' && document.querySelectorAll(params.el).length > 1) {\n      const swipers = [];\n      document.querySelectorAll(params.el).forEach(containerEl => {\n        const newParams = extend({}, params, {\n          el: containerEl\n        });\n        swipers.push(new Swiper(newParams));\n      });\n      // eslint-disable-next-line no-constructor-return\n      return swipers;\n    }\n\n    // Swiper Instance\n    const swiper = this;\n    swiper.__swiper__ = true;\n    swiper.support = getSupport();\n    swiper.device = getDevice({\n      userAgent: params.userAgent\n    });\n    swiper.browser = getBrowser();\n    swiper.eventsListeners = {};\n    swiper.eventsAnyListeners = [];\n    swiper.modules = [...swiper.__modules__];\n    if (params.modules && Array.isArray(params.modules)) {\n      swiper.modules.push(...params.modules);\n    }\n    const allModulesParams = {};\n    swiper.modules.forEach(mod => {\n      mod({\n        params,\n        swiper,\n        extendParams: moduleExtendParams(params, allModulesParams),\n        on: swiper.on.bind(swiper),\n        once: swiper.once.bind(swiper),\n        off: swiper.off.bind(swiper),\n        emit: swiper.emit.bind(swiper)\n      });\n    });\n\n    // Extend defaults with modules params\n    const swiperParams = extend({}, defaults, allModulesParams);\n\n    // Extend defaults with passed params\n    swiper.params = extend({}, swiperParams, extendedDefaults, params);\n    swiper.originalParams = extend({}, swiper.params);\n    swiper.passedParams = extend({}, params);\n\n    // add event listeners\n    if (swiper.params && swiper.params.on) {\n      Object.keys(swiper.params.on).forEach(eventName => {\n        swiper.on(eventName, swiper.params.on[eventName]);\n      });\n    }\n    if (swiper.params && swiper.params.onAny) {\n      swiper.onAny(swiper.params.onAny);\n    }\n\n    // Extend Swiper\n    Object.assign(swiper, {\n      enabled: swiper.params.enabled,\n      el,\n      // Classes\n      classNames: [],\n      // Slides\n      slides: [],\n      slidesGrid: [],\n      snapGrid: [],\n      slidesSizesGrid: [],\n      // isDirection\n      isHorizontal() {\n        return swiper.params.direction === 'horizontal';\n      },\n      isVertical() {\n        return swiper.params.direction === 'vertical';\n      },\n      // Indexes\n      activeIndex: 0,\n      realIndex: 0,\n      //\n      isBeginning: true,\n      isEnd: false,\n      // Props\n      translate: 0,\n      previousTranslate: 0,\n      progress: 0,\n      velocity: 0,\n      animating: false,\n      cssOverflowAdjustment() {\n        // Returns 0 unless `translate` is > 2**23\n        // Should be subtracted from css values to prevent overflow\n        return Math.trunc(this.translate / 2 ** 23) * 2 ** 23;\n      },\n      // Locks\n      allowSlideNext: swiper.params.allowSlideNext,\n      allowSlidePrev: swiper.params.allowSlidePrev,\n      // Touch Events\n      touchEventsData: {\n        isTouched: undefined,\n        isMoved: undefined,\n        allowTouchCallbacks: undefined,\n        touchStartTime: undefined,\n        isScrolling: undefined,\n        currentTranslate: undefined,\n        startTranslate: undefined,\n        allowThresholdMove: undefined,\n        // Form elements to match\n        focusableElements: swiper.params.focusableElements,\n        // Last click time\n        lastClickTime: 0,\n        clickTimeout: undefined,\n        // Velocities\n        velocities: [],\n        allowMomentumBounce: undefined,\n        startMoving: undefined,\n        evCache: []\n      },\n      // Clicks\n      allowClick: true,\n      // Touches\n      allowTouchMove: swiper.params.allowTouchMove,\n      touches: {\n        startX: 0,\n        startY: 0,\n        currentX: 0,\n        currentY: 0,\n        diff: 0\n      },\n      // Images\n      imagesToLoad: [],\n      imagesLoaded: 0\n    });\n    swiper.emit('_swiper');\n\n    // Init\n    if (swiper.params.init) {\n      swiper.init();\n    }\n\n    // Return app instance\n    // eslint-disable-next-line no-constructor-return\n    return swiper;\n  }\n  getSlideIndex(slideEl) {\n    const {\n      slidesEl,\n      params\n    } = this;\n    const slides = elementChildren(slidesEl, `.${params.slideClass}, swiper-slide`);\n    const firstSlideIndex = elementIndex(slides[0]);\n    return elementIndex(slideEl) - firstSlideIndex;\n  }\n  getSlideIndexByData(index) {\n    return this.getSlideIndex(this.slides.filter(slideEl => slideEl.getAttribute('data-swiper-slide-index') * 1 === index)[0]);\n  }\n  recalcSlides() {\n    const swiper = this;\n    const {\n      slidesEl,\n      params\n    } = swiper;\n    swiper.slides = elementChildren(slidesEl, `.${params.slideClass}, swiper-slide`);\n  }\n  enable() {\n    const swiper = this;\n    if (swiper.enabled) return;\n    swiper.enabled = true;\n    if (swiper.params.grabCursor) {\n      swiper.setGrabCursor();\n    }\n    swiper.emit('enable');\n  }\n  disable() {\n    const swiper = this;\n    if (!swiper.enabled) return;\n    swiper.enabled = false;\n    if (swiper.params.grabCursor) {\n      swiper.unsetGrabCursor();\n    }\n    swiper.emit('disable');\n  }\n  setProgress(progress, speed) {\n    const swiper = this;\n    progress = Math.min(Math.max(progress, 0), 1);\n    const min = swiper.minTranslate();\n    const max = swiper.maxTranslate();\n    const current = (max - min) * progress + min;\n    swiper.translateTo(current, typeof speed === 'undefined' ? 0 : speed);\n    swiper.updateActiveIndex();\n    swiper.updateSlidesClasses();\n  }\n  emitContainerClasses() {\n    const swiper = this;\n    if (!swiper.params._emitClasses || !swiper.el) return;\n    const cls = swiper.el.className.split(' ').filter(className => {\n      return className.indexOf('swiper') === 0 || className.indexOf(swiper.params.containerModifierClass) === 0;\n    });\n    swiper.emit('_containerClasses', cls.join(' '));\n  }\n  getSlideClasses(slideEl) {\n    const swiper = this;\n    if (swiper.destroyed) return '';\n    return slideEl.className.split(' ').filter(className => {\n      return className.indexOf('swiper-slide') === 0 || className.indexOf(swiper.params.slideClass) === 0;\n    }).join(' ');\n  }\n  emitSlidesClasses() {\n    const swiper = this;\n    if (!swiper.params._emitClasses || !swiper.el) return;\n    const updates = [];\n    swiper.slides.forEach(slideEl => {\n      const classNames = swiper.getSlideClasses(slideEl);\n      updates.push({\n        slideEl,\n        classNames\n      });\n      swiper.emit('_slideClass', slideEl, classNames);\n    });\n    swiper.emit('_slideClasses', updates);\n  }\n  slidesPerViewDynamic(view = 'current', exact = false) {\n    const swiper = this;\n    const {\n      params,\n      slides,\n      slidesGrid,\n      slidesSizesGrid,\n      size: swiperSize,\n      activeIndex\n    } = swiper;\n    let spv = 1;\n    if (params.centeredSlides) {\n      let slideSize = slides[activeIndex] ? slides[activeIndex].swiperSlideSize : 0;\n      let breakLoop;\n      for (let i = activeIndex + 1; i < slides.length; i += 1) {\n        if (slides[i] && !breakLoop) {\n          slideSize += slides[i].swiperSlideSize;\n          spv += 1;\n          if (slideSize > swiperSize) breakLoop = true;\n        }\n      }\n      for (let i = activeIndex - 1; i >= 0; i -= 1) {\n        if (slides[i] && !breakLoop) {\n          slideSize += slides[i].swiperSlideSize;\n          spv += 1;\n          if (slideSize > swiperSize) breakLoop = true;\n        }\n      }\n    } else {\n      // eslint-disable-next-line\n      if (view === 'current') {\n        for (let i = activeIndex + 1; i < slides.length; i += 1) {\n          const slideInView = exact ? slidesGrid[i] + slidesSizesGrid[i] - slidesGrid[activeIndex] < swiperSize : slidesGrid[i] - slidesGrid[activeIndex] < swiperSize;\n          if (slideInView) {\n            spv += 1;\n          }\n        }\n      } else {\n        // previous\n        for (let i = activeIndex - 1; i >= 0; i -= 1) {\n          const slideInView = slidesGrid[activeIndex] - slidesGrid[i] < swiperSize;\n          if (slideInView) {\n            spv += 1;\n          }\n        }\n      }\n    }\n    return spv;\n  }\n  update() {\n    const swiper = this;\n    if (!swiper || swiper.destroyed) return;\n    const {\n      snapGrid,\n      params\n    } = swiper;\n    // Breakpoints\n    if (params.breakpoints) {\n      swiper.setBreakpoint();\n    }\n    [...swiper.el.querySelectorAll('[loading=\"lazy\"]')].forEach(imageEl => {\n      if (imageEl.complete) {\n        processLazyPreloader(swiper, imageEl);\n      }\n    });\n    swiper.updateSize();\n    swiper.updateSlides();\n    swiper.updateProgress();\n    swiper.updateSlidesClasses();\n    function setTranslate() {\n      const translateValue = swiper.rtlTranslate ? swiper.translate * -1 : swiper.translate;\n      const newTranslate = Math.min(Math.max(translateValue, swiper.maxTranslate()), swiper.minTranslate());\n      swiper.setTranslate(newTranslate);\n      swiper.updateActiveIndex();\n      swiper.updateSlidesClasses();\n    }\n    let translated;\n    if (params.freeMode && params.freeMode.enabled && !params.cssMode) {\n      setTranslate();\n      if (params.autoHeight) {\n        swiper.updateAutoHeight();\n      }\n    } else {\n      if ((params.slidesPerView === 'auto' || params.slidesPerView > 1) && swiper.isEnd && !params.centeredSlides) {\n        const slides = swiper.virtual && params.virtual.enabled ? swiper.virtual.slides : swiper.slides;\n        translated = swiper.slideTo(slides.length - 1, 0, false, true);\n      } else {\n        translated = swiper.slideTo(swiper.activeIndex, 0, false, true);\n      }\n      if (!translated) {\n        setTranslate();\n      }\n    }\n    if (params.watchOverflow && snapGrid !== swiper.snapGrid) {\n      swiper.checkOverflow();\n    }\n    swiper.emit('update');\n  }\n  changeDirection(newDirection, needUpdate = true) {\n    const swiper = this;\n    const currentDirection = swiper.params.direction;\n    if (!newDirection) {\n      // eslint-disable-next-line\n      newDirection = currentDirection === 'horizontal' ? 'vertical' : 'horizontal';\n    }\n    if (newDirection === currentDirection || newDirection !== 'horizontal' && newDirection !== 'vertical') {\n      return swiper;\n    }\n    swiper.el.classList.remove(`${swiper.params.containerModifierClass}${currentDirection}`);\n    swiper.el.classList.add(`${swiper.params.containerModifierClass}${newDirection}`);\n    swiper.emitContainerClasses();\n    swiper.params.direction = newDirection;\n    swiper.slides.forEach(slideEl => {\n      if (newDirection === 'vertical') {\n        slideEl.style.width = '';\n      } else {\n        slideEl.style.height = '';\n      }\n    });\n    swiper.emit('changeDirection');\n    if (needUpdate) swiper.update();\n    return swiper;\n  }\n  changeLanguageDirection(direction) {\n    const swiper = this;\n    if (swiper.rtl && direction === 'rtl' || !swiper.rtl && direction === 'ltr') return;\n    swiper.rtl = direction === 'rtl';\n    swiper.rtlTranslate = swiper.params.direction === 'horizontal' && swiper.rtl;\n    if (swiper.rtl) {\n      swiper.el.classList.add(`${swiper.params.containerModifierClass}rtl`);\n      swiper.el.dir = 'rtl';\n    } else {\n      swiper.el.classList.remove(`${swiper.params.containerModifierClass}rtl`);\n      swiper.el.dir = 'ltr';\n    }\n    swiper.update();\n  }\n  mount(element) {\n    const swiper = this;\n    if (swiper.mounted) return true;\n\n    // Find el\n    let el = element || swiper.params.el;\n    if (typeof el === 'string') {\n      el = document.querySelector(el);\n    }\n    if (!el) {\n      return false;\n    }\n    el.swiper = swiper;\n    if (el.shadowEl) {\n      swiper.isElement = true;\n    }\n    const getWrapperSelector = () => {\n      return `.${(swiper.params.wrapperClass || '').trim().split(' ').join('.')}`;\n    };\n    const getWrapper = () => {\n      if (el && el.shadowRoot && el.shadowRoot.querySelector) {\n        const res = el.shadowRoot.querySelector(getWrapperSelector());\n        // Children needs to return slot items\n        return res;\n      }\n      return elementChildren(el, getWrapperSelector())[0];\n    };\n    // Find Wrapper\n    let wrapperEl = getWrapper();\n    if (!wrapperEl && swiper.params.createElements) {\n      wrapperEl = createElement('div', swiper.params.wrapperClass);\n      el.append(wrapperEl);\n      elementChildren(el, `.${swiper.params.slideClass}`).forEach(slideEl => {\n        wrapperEl.append(slideEl);\n      });\n    }\n    Object.assign(swiper, {\n      el,\n      wrapperEl,\n      slidesEl: swiper.isElement ? el : wrapperEl,\n      mounted: true,\n      // RTL\n      rtl: el.dir.toLowerCase() === 'rtl' || elementStyle(el, 'direction') === 'rtl',\n      rtlTranslate: swiper.params.direction === 'horizontal' && (el.dir.toLowerCase() === 'rtl' || elementStyle(el, 'direction') === 'rtl'),\n      wrongRTL: elementStyle(wrapperEl, 'display') === '-webkit-box'\n    });\n    return true;\n  }\n  init(el) {\n    const swiper = this;\n    if (swiper.initialized) return swiper;\n    const mounted = swiper.mount(el);\n    if (mounted === false) return swiper;\n    swiper.emit('beforeInit');\n\n    // Set breakpoint\n    if (swiper.params.breakpoints) {\n      swiper.setBreakpoint();\n    }\n\n    // Add Classes\n    swiper.addClasses();\n\n    // Update size\n    swiper.updateSize();\n\n    // Update slides\n    swiper.updateSlides();\n    if (swiper.params.watchOverflow) {\n      swiper.checkOverflow();\n    }\n\n    // Set Grab Cursor\n    if (swiper.params.grabCursor && swiper.enabled) {\n      swiper.setGrabCursor();\n    }\n\n    // Slide To Initial Slide\n    if (swiper.params.loop && swiper.virtual && swiper.params.virtual.enabled) {\n      swiper.slideTo(swiper.params.initialSlide + swiper.virtual.slidesBefore, 0, swiper.params.runCallbacksOnInit, false, true);\n    } else {\n      swiper.slideTo(swiper.params.initialSlide, 0, swiper.params.runCallbacksOnInit, false, true);\n    }\n\n    // Create loop\n    if (swiper.params.loop) {\n      swiper.loopCreate();\n    }\n\n    // Attach events\n    swiper.attachEvents();\n    [...swiper.el.querySelectorAll('[loading=\"lazy\"]')].forEach(imageEl => {\n      if (imageEl.complete) {\n        processLazyPreloader(swiper, imageEl);\n      } else {\n        imageEl.addEventListener('load', e => {\n          processLazyPreloader(swiper, e.target);\n        });\n      }\n    });\n    preload(swiper);\n\n    // Init Flag\n    swiper.initialized = true;\n    preload(swiper);\n\n    // Emit\n    swiper.emit('init');\n    swiper.emit('afterInit');\n    return swiper;\n  }\n  destroy(deleteInstance = true, cleanStyles = true) {\n    const swiper = this;\n    const {\n      params,\n      el,\n      wrapperEl,\n      slides\n    } = swiper;\n    if (typeof swiper.params === 'undefined' || swiper.destroyed) {\n      return null;\n    }\n    swiper.emit('beforeDestroy');\n\n    // Init Flag\n    swiper.initialized = false;\n\n    // Detach events\n    swiper.detachEvents();\n\n    // Destroy loop\n    if (params.loop) {\n      swiper.loopDestroy();\n    }\n\n    // Cleanup styles\n    if (cleanStyles) {\n      swiper.removeClasses();\n      el.removeAttribute('style');\n      wrapperEl.removeAttribute('style');\n      if (slides && slides.length) {\n        slides.forEach(slideEl => {\n          slideEl.classList.remove(params.slideVisibleClass, params.slideActiveClass, params.slideNextClass, params.slidePrevClass);\n          slideEl.removeAttribute('style');\n          slideEl.removeAttribute('data-swiper-slide-index');\n        });\n      }\n    }\n    swiper.emit('destroy');\n\n    // Detach emitter events\n    Object.keys(swiper.eventsListeners).forEach(eventName => {\n      swiper.off(eventName);\n    });\n    if (deleteInstance !== false) {\n      swiper.el.swiper = null;\n      deleteProps(swiper);\n    }\n    swiper.destroyed = true;\n    return null;\n  }\n  static extendDefaults(newDefaults) {\n    extend(extendedDefaults, newDefaults);\n  }\n  static get extendedDefaults() {\n    return extendedDefaults;\n  }\n  static get defaults() {\n    return defaults;\n  }\n  static installModule(mod) {\n    if (!Swiper.prototype.__modules__) Swiper.prototype.__modules__ = [];\n    const modules = Swiper.prototype.__modules__;\n    if (typeof mod === 'function' && modules.indexOf(mod) < 0) {\n      modules.push(mod);\n    }\n  }\n  static use(module) {\n    if (Array.isArray(module)) {\n      module.forEach(m => Swiper.installModule(m));\n      return Swiper;\n    }\n    Swiper.installModule(module);\n    return Swiper;\n  }\n}\nObject.keys(prototypes).forEach(prototypeGroup => {\n  Object.keys(prototypes[prototypeGroup]).forEach(protoMethod => {\n    Swiper.prototype[protoMethod] = prototypes[prototypeGroup][protoMethod];\n  });\n});\nSwiper.use([Resize, Observer]);\nexport default Swiper;"], "mappings": "AAAA;AACA,SAASA,WAAW,QAAQ,YAAY;AACxC,SAASC,MAAM,EAAEC,WAAW,EAAEC,aAAa,EAAEC,eAAe,EAAEC,YAAY,EAAEC,YAAY,QAAQ,oBAAoB;AACpH,SAASC,UAAU,QAAQ,0BAA0B;AACrD,SAASC,SAAS,QAAQ,yBAAyB;AACnD,SAASC,UAAU,QAAQ,0BAA0B;AACrD,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,OAAOC,QAAQ,MAAM,gCAAgC;AACrD,OAAOC,aAAa,MAAM,qBAAqB;AAC/C,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,OAAOC,UAAU,MAAM,uBAAuB;AAC9C,OAAOC,KAAK,MAAM,kBAAkB;AACpC,OAAOC,IAAI,MAAM,iBAAiB;AAClC,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,WAAW,MAAM,wBAAwB;AAChD,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,aAAa,MAAM,2BAA2B;AACrD,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAOC,kBAAkB,MAAM,yBAAyB;AACxD,SAASC,oBAAoB,EAAEC,OAAO,QAAQ,qCAAqC;AACnF,MAAMC,UAAU,GAAG;EACjBf,aAAa;EACbC,MAAM;EACNC,SAAS;EACTC,UAAU;EACVC,KAAK;EACLC,IAAI;EACJC,UAAU;EACVC,MAAM;EACNC,WAAW;EACXE,aAAa;EACbD;AACF,CAAC;AACD,MAAMO,gBAAgB,GAAG,CAAC,CAAC;AAC3B,MAAMC,MAAM,CAAC;EACXC,WAAWA,CAAA,EAAU;IACnB,IAAIC,EAAE;IACN,IAAIC,MAAM;IAAC,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAFEC,IAAI,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;MAAJF,IAAI,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;IAAA;IAGjB,IAAIF,IAAI,CAACD,MAAM,KAAK,CAAC,IAAIC,IAAI,CAAC,CAAC,CAAC,CAACN,WAAW,IAAIS,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACN,IAAI,CAAC,CAAC,CAAC,CAAC,CAACO,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;MACjHX,MAAM,GAAGI,IAAI,CAAC,CAAC,CAAC;IAClB,CAAC,MAAM;MACL,CAACL,EAAE,EAAEC,MAAM,CAAC,GAAGI,IAAI;IACrB;IACA,IAAI,CAACJ,MAAM,EAAEA,MAAM,GAAG,CAAC,CAAC;IACxBA,MAAM,GAAG/B,MAAM,CAAC,CAAC,CAAC,EAAE+B,MAAM,CAAC;IAC3B,IAAID,EAAE,IAAI,CAACC,MAAM,CAACD,EAAE,EAAEC,MAAM,CAACD,EAAE,GAAGA,EAAE;IACpC,MAAMa,QAAQ,GAAG5C,WAAW,EAAE;IAC9B,IAAIgC,MAAM,CAACD,EAAE,IAAI,OAAOC,MAAM,CAACD,EAAE,KAAK,QAAQ,IAAIa,QAAQ,CAACC,gBAAgB,CAACb,MAAM,CAACD,EAAE,CAAC,CAACI,MAAM,GAAG,CAAC,EAAE;MACjG,MAAMW,OAAO,GAAG,EAAE;MAClBF,QAAQ,CAACC,gBAAgB,CAACb,MAAM,CAACD,EAAE,CAAC,CAACgB,OAAO,CAACC,WAAW,IAAI;QAC1D,MAAMC,SAAS,GAAGhD,MAAM,CAAC,CAAC,CAAC,EAAE+B,MAAM,EAAE;UACnCD,EAAE,EAAEiB;QACN,CAAC,CAAC;QACFF,OAAO,CAACI,IAAI,CAAC,IAAIrB,MAAM,CAACoB,SAAS,CAAC,CAAC;MACrC,CAAC,CAAC;MACF;MACA,OAAOH,OAAO;IAChB;;IAEA;IACA,MAAMK,MAAM,GAAG,IAAI;IACnBA,MAAM,CAACC,UAAU,GAAG,IAAI;IACxBD,MAAM,CAACE,OAAO,GAAG9C,UAAU,EAAE;IAC7B4C,MAAM,CAACG,MAAM,GAAG9C,SAAS,CAAC;MACxB+C,SAAS,EAAEvB,MAAM,CAACuB;IACpB,CAAC,CAAC;IACFJ,MAAM,CAACK,OAAO,GAAG/C,UAAU,EAAE;IAC7B0C,MAAM,CAACM,eAAe,GAAG,CAAC,CAAC;IAC3BN,MAAM,CAACO,kBAAkB,GAAG,EAAE;IAC9BP,MAAM,CAACQ,OAAO,GAAG,CAAC,GAAGR,MAAM,CAACS,WAAW,CAAC;IACxC,IAAI5B,MAAM,CAAC2B,OAAO,IAAItB,KAAK,CAACwB,OAAO,CAAC7B,MAAM,CAAC2B,OAAO,CAAC,EAAE;MACnDR,MAAM,CAACQ,OAAO,CAACT,IAAI,CAAC,GAAGlB,MAAM,CAAC2B,OAAO,CAAC;IACxC;IACA,MAAMG,gBAAgB,GAAG,CAAC,CAAC;IAC3BX,MAAM,CAACQ,OAAO,CAACZ,OAAO,CAACgB,GAAG,IAAI;MAC5BA,GAAG,CAAC;QACF/B,MAAM;QACNmB,MAAM;QACNa,YAAY,EAAExC,kBAAkB,CAACQ,MAAM,EAAE8B,gBAAgB,CAAC;QAC1DG,EAAE,EAAEd,MAAM,CAACc,EAAE,CAACC,IAAI,CAACf,MAAM,CAAC;QAC1BgB,IAAI,EAAEhB,MAAM,CAACgB,IAAI,CAACD,IAAI,CAACf,MAAM,CAAC;QAC9BiB,GAAG,EAAEjB,MAAM,CAACiB,GAAG,CAACF,IAAI,CAACf,MAAM,CAAC;QAC5BkB,IAAI,EAAElB,MAAM,CAACkB,IAAI,CAACH,IAAI,CAACf,MAAM;MAC/B,CAAC,CAAC;IACJ,CAAC,CAAC;;IAEF;IACA,MAAMmB,YAAY,GAAGrE,MAAM,CAAC,CAAC,CAAC,EAAEsB,QAAQ,EAAEuC,gBAAgB,CAAC;;IAE3D;IACAX,MAAM,CAACnB,MAAM,GAAG/B,MAAM,CAAC,CAAC,CAAC,EAAEqE,YAAY,EAAE1C,gBAAgB,EAAEI,MAAM,CAAC;IAClEmB,MAAM,CAACoB,cAAc,GAAGtE,MAAM,CAAC,CAAC,CAAC,EAAEkD,MAAM,CAACnB,MAAM,CAAC;IACjDmB,MAAM,CAACqB,YAAY,GAAGvE,MAAM,CAAC,CAAC,CAAC,EAAE+B,MAAM,CAAC;;IAExC;IACA,IAAImB,MAAM,CAACnB,MAAM,IAAImB,MAAM,CAACnB,MAAM,CAACiC,EAAE,EAAE;MACrC1B,MAAM,CAACkC,IAAI,CAACtB,MAAM,CAACnB,MAAM,CAACiC,EAAE,CAAC,CAAClB,OAAO,CAAC2B,SAAS,IAAI;QACjDvB,MAAM,CAACc,EAAE,CAACS,SAAS,EAAEvB,MAAM,CAACnB,MAAM,CAACiC,EAAE,CAACS,SAAS,CAAC,CAAC;MACnD,CAAC,CAAC;IACJ;IACA,IAAIvB,MAAM,CAACnB,MAAM,IAAImB,MAAM,CAACnB,MAAM,CAAC2C,KAAK,EAAE;MACxCxB,MAAM,CAACwB,KAAK,CAACxB,MAAM,CAACnB,MAAM,CAAC2C,KAAK,CAAC;IACnC;;IAEA;IACApC,MAAM,CAACqC,MAAM,CAACzB,MAAM,EAAE;MACpB0B,OAAO,EAAE1B,MAAM,CAACnB,MAAM,CAAC6C,OAAO;MAC9B9C,EAAE;MACF;MACA+C,UAAU,EAAE,EAAE;MACd;MACAC,MAAM,EAAE,EAAE;MACVC,UAAU,EAAE,EAAE;MACdC,QAAQ,EAAE,EAAE;MACZC,eAAe,EAAE,EAAE;MACnB;MACAC,YAAYA,CAAA,EAAG;QACb,OAAOhC,MAAM,CAACnB,MAAM,CAACoD,SAAS,KAAK,YAAY;MACjD,CAAC;MACDC,UAAUA,CAAA,EAAG;QACX,OAAOlC,MAAM,CAACnB,MAAM,CAACoD,SAAS,KAAK,UAAU;MAC/C,CAAC;MACD;MACAE,WAAW,EAAE,CAAC;MACdC,SAAS,EAAE,CAAC;MACZ;MACAC,WAAW,EAAE,IAAI;MACjBC,KAAK,EAAE,KAAK;MACZ;MACA3E,SAAS,EAAE,CAAC;MACZ4E,iBAAiB,EAAE,CAAC;MACpBC,QAAQ,EAAE,CAAC;MACXC,QAAQ,EAAE,CAAC;MACXC,SAAS,EAAE,KAAK;MAChBC,qBAAqBA,CAAA,EAAG;QACtB;QACA;QACA,OAAOC,IAAI,CAACC,KAAK,CAAC,IAAI,CAAClF,SAAS,GAAG,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE;MACvD,CAAC;MACD;MACAmF,cAAc,EAAE9C,MAAM,CAACnB,MAAM,CAACiE,cAAc;MAC5CC,cAAc,EAAE/C,MAAM,CAACnB,MAAM,CAACkE,cAAc;MAC5C;MACAC,eAAe,EAAE;QACfC,SAAS,EAAEC,SAAS;QACpBC,OAAO,EAAED,SAAS;QAClBE,mBAAmB,EAAEF,SAAS;QAC9BG,cAAc,EAAEH,SAAS;QACzBI,WAAW,EAAEJ,SAAS;QACtBK,gBAAgB,EAAEL,SAAS;QAC3BM,cAAc,EAAEN,SAAS;QACzBO,kBAAkB,EAAEP,SAAS;QAC7B;QACAQ,iBAAiB,EAAE1D,MAAM,CAACnB,MAAM,CAAC6E,iBAAiB;QAClD;QACAC,aAAa,EAAE,CAAC;QAChBC,YAAY,EAAEV,SAAS;QACvB;QACAW,UAAU,EAAE,EAAE;QACdC,mBAAmB,EAAEZ,SAAS;QAC9Ba,WAAW,EAAEb,SAAS;QACtBc,OAAO,EAAE;MACX,CAAC;MACD;MACAC,UAAU,EAAE,IAAI;MAChB;MACAC,cAAc,EAAElE,MAAM,CAACnB,MAAM,CAACqF,cAAc;MAC5CC,OAAO,EAAE;QACPC,MAAM,EAAE,CAAC;QACTC,MAAM,EAAE,CAAC;QACTC,QAAQ,EAAE,CAAC;QACXC,QAAQ,EAAE,CAAC;QACXC,IAAI,EAAE;MACR,CAAC;MACD;MACAC,YAAY,EAAE,EAAE;MAChBC,YAAY,EAAE;IAChB,CAAC,CAAC;IACF1E,MAAM,CAACkB,IAAI,CAAC,SAAS,CAAC;;IAEtB;IACA,IAAIlB,MAAM,CAACnB,MAAM,CAAC8F,IAAI,EAAE;MACtB3E,MAAM,CAAC2E,IAAI,EAAE;IACf;;IAEA;IACA;IACA,OAAO3E,MAAM;EACf;EACA4E,aAAaA,CAACC,OAAO,EAAE;IACrB,MAAM;MACJC,QAAQ;MACRjG;IACF,CAAC,GAAG,IAAI;IACR,MAAM+C,MAAM,GAAG3E,eAAe,CAAC6H,QAAQ,EAAG,IAAGjG,MAAM,CAACkG,UAAW,gBAAe,CAAC;IAC/E,MAAMC,eAAe,GAAG7H,YAAY,CAACyE,MAAM,CAAC,CAAC,CAAC,CAAC;IAC/C,OAAOzE,YAAY,CAAC0H,OAAO,CAAC,GAAGG,eAAe;EAChD;EACAC,mBAAmBA,CAACC,KAAK,EAAE;IACzB,OAAO,IAAI,CAACN,aAAa,CAAC,IAAI,CAAChD,MAAM,CAACuD,MAAM,CAACN,OAAO,IAAIA,OAAO,CAACO,YAAY,CAAC,yBAAyB,CAAC,GAAG,CAAC,KAAKF,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5H;EACAG,YAAYA,CAAA,EAAG;IACb,MAAMrF,MAAM,GAAG,IAAI;IACnB,MAAM;MACJ8E,QAAQ;MACRjG;IACF,CAAC,GAAGmB,MAAM;IACVA,MAAM,CAAC4B,MAAM,GAAG3E,eAAe,CAAC6H,QAAQ,EAAG,IAAGjG,MAAM,CAACkG,UAAW,gBAAe,CAAC;EAClF;EACAO,MAAMA,CAAA,EAAG;IACP,MAAMtF,MAAM,GAAG,IAAI;IACnB,IAAIA,MAAM,CAAC0B,OAAO,EAAE;IACpB1B,MAAM,CAAC0B,OAAO,GAAG,IAAI;IACrB,IAAI1B,MAAM,CAACnB,MAAM,CAACd,UAAU,EAAE;MAC5BiC,MAAM,CAACuF,aAAa,EAAE;IACxB;IACAvF,MAAM,CAACkB,IAAI,CAAC,QAAQ,CAAC;EACvB;EACAsE,OAAOA,CAAA,EAAG;IACR,MAAMxF,MAAM,GAAG,IAAI;IACnB,IAAI,CAACA,MAAM,CAAC0B,OAAO,EAAE;IACrB1B,MAAM,CAAC0B,OAAO,GAAG,KAAK;IACtB,IAAI1B,MAAM,CAACnB,MAAM,CAACd,UAAU,EAAE;MAC5BiC,MAAM,CAACyF,eAAe,EAAE;IAC1B;IACAzF,MAAM,CAACkB,IAAI,CAAC,SAAS,CAAC;EACxB;EACAwE,WAAWA,CAAClD,QAAQ,EAAEmD,KAAK,EAAE;IAC3B,MAAM3F,MAAM,GAAG,IAAI;IACnBwC,QAAQ,GAAGI,IAAI,CAACgD,GAAG,CAAChD,IAAI,CAACiD,GAAG,CAACrD,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAC7C,MAAMoD,GAAG,GAAG5F,MAAM,CAAC8F,YAAY,EAAE;IACjC,MAAMD,GAAG,GAAG7F,MAAM,CAAC+F,YAAY,EAAE;IACjC,MAAMC,OAAO,GAAG,CAACH,GAAG,GAAGD,GAAG,IAAIpD,QAAQ,GAAGoD,GAAG;IAC5C5F,MAAM,CAACiG,WAAW,CAACD,OAAO,EAAE,OAAOL,KAAK,KAAK,WAAW,GAAG,CAAC,GAAGA,KAAK,CAAC;IACrE3F,MAAM,CAACkG,iBAAiB,EAAE;IAC1BlG,MAAM,CAACmG,mBAAmB,EAAE;EAC9B;EACAC,oBAAoBA,CAAA,EAAG;IACrB,MAAMpG,MAAM,GAAG,IAAI;IACnB,IAAI,CAACA,MAAM,CAACnB,MAAM,CAACwH,YAAY,IAAI,CAACrG,MAAM,CAACpB,EAAE,EAAE;IAC/C,MAAM0H,GAAG,GAAGtG,MAAM,CAACpB,EAAE,CAAC2H,SAAS,CAACC,KAAK,CAAC,GAAG,CAAC,CAACrB,MAAM,CAACoB,SAAS,IAAI;MAC7D,OAAOA,SAAS,CAACE,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAIF,SAAS,CAACE,OAAO,CAACzG,MAAM,CAACnB,MAAM,CAAC6H,sBAAsB,CAAC,KAAK,CAAC;IAC3G,CAAC,CAAC;IACF1G,MAAM,CAACkB,IAAI,CAAC,mBAAmB,EAAEoF,GAAG,CAACK,IAAI,CAAC,GAAG,CAAC,CAAC;EACjD;EACAC,eAAeA,CAAC/B,OAAO,EAAE;IACvB,MAAM7E,MAAM,GAAG,IAAI;IACnB,IAAIA,MAAM,CAAC6G,SAAS,EAAE,OAAO,EAAE;IAC/B,OAAOhC,OAAO,CAAC0B,SAAS,CAACC,KAAK,CAAC,GAAG,CAAC,CAACrB,MAAM,CAACoB,SAAS,IAAI;MACtD,OAAOA,SAAS,CAACE,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,IAAIF,SAAS,CAACE,OAAO,CAACzG,MAAM,CAACnB,MAAM,CAACkG,UAAU,CAAC,KAAK,CAAC;IACrG,CAAC,CAAC,CAAC4B,IAAI,CAAC,GAAG,CAAC;EACd;EACAG,iBAAiBA,CAAA,EAAG;IAClB,MAAM9G,MAAM,GAAG,IAAI;IACnB,IAAI,CAACA,MAAM,CAACnB,MAAM,CAACwH,YAAY,IAAI,CAACrG,MAAM,CAACpB,EAAE,EAAE;IAC/C,MAAMmI,OAAO,GAAG,EAAE;IAClB/G,MAAM,CAAC4B,MAAM,CAAChC,OAAO,CAACiF,OAAO,IAAI;MAC/B,MAAMlD,UAAU,GAAG3B,MAAM,CAAC4G,eAAe,CAAC/B,OAAO,CAAC;MAClDkC,OAAO,CAAChH,IAAI,CAAC;QACX8E,OAAO;QACPlD;MACF,CAAC,CAAC;MACF3B,MAAM,CAACkB,IAAI,CAAC,aAAa,EAAE2D,OAAO,EAAElD,UAAU,CAAC;IACjD,CAAC,CAAC;IACF3B,MAAM,CAACkB,IAAI,CAAC,eAAe,EAAE6F,OAAO,CAAC;EACvC;EACAC,oBAAoBA,CAAA,EAAkC;IAAA,IAAjCC,IAAI,GAAAlI,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAmE,SAAA,GAAAnE,SAAA,MAAG,SAAS;IAAA,IAAEmI,KAAK,GAAAnI,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAmE,SAAA,GAAAnE,SAAA,MAAG,KAAK;IAClD,MAAMiB,MAAM,GAAG,IAAI;IACnB,MAAM;MACJnB,MAAM;MACN+C,MAAM;MACNC,UAAU;MACVE,eAAe;MACfoF,IAAI,EAAEC,UAAU;MAChBjF;IACF,CAAC,GAAGnC,MAAM;IACV,IAAIqH,GAAG,GAAG,CAAC;IACX,IAAIxI,MAAM,CAACyI,cAAc,EAAE;MACzB,IAAIC,SAAS,GAAG3F,MAAM,CAACO,WAAW,CAAC,GAAGP,MAAM,CAACO,WAAW,CAAC,CAACqF,eAAe,GAAG,CAAC;MAC7E,IAAIC,SAAS;MACb,KAAK,IAAIC,CAAC,GAAGvF,WAAW,GAAG,CAAC,EAAEuF,CAAC,GAAG9F,MAAM,CAAC5C,MAAM,EAAE0I,CAAC,IAAI,CAAC,EAAE;QACvD,IAAI9F,MAAM,CAAC8F,CAAC,CAAC,IAAI,CAACD,SAAS,EAAE;UAC3BF,SAAS,IAAI3F,MAAM,CAAC8F,CAAC,CAAC,CAACF,eAAe;UACtCH,GAAG,IAAI,CAAC;UACR,IAAIE,SAAS,GAAGH,UAAU,EAAEK,SAAS,GAAG,IAAI;QAC9C;MACF;MACA,KAAK,IAAIC,CAAC,GAAGvF,WAAW,GAAG,CAAC,EAAEuF,CAAC,IAAI,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;QAC5C,IAAI9F,MAAM,CAAC8F,CAAC,CAAC,IAAI,CAACD,SAAS,EAAE;UAC3BF,SAAS,IAAI3F,MAAM,CAAC8F,CAAC,CAAC,CAACF,eAAe;UACtCH,GAAG,IAAI,CAAC;UACR,IAAIE,SAAS,GAAGH,UAAU,EAAEK,SAAS,GAAG,IAAI;QAC9C;MACF;IACF,CAAC,MAAM;MACL;MACA,IAAIR,IAAI,KAAK,SAAS,EAAE;QACtB,KAAK,IAAIS,CAAC,GAAGvF,WAAW,GAAG,CAAC,EAAEuF,CAAC,GAAG9F,MAAM,CAAC5C,MAAM,EAAE0I,CAAC,IAAI,CAAC,EAAE;UACvD,MAAMC,WAAW,GAAGT,KAAK,GAAGrF,UAAU,CAAC6F,CAAC,CAAC,GAAG3F,eAAe,CAAC2F,CAAC,CAAC,GAAG7F,UAAU,CAACM,WAAW,CAAC,GAAGiF,UAAU,GAAGvF,UAAU,CAAC6F,CAAC,CAAC,GAAG7F,UAAU,CAACM,WAAW,CAAC,GAAGiF,UAAU;UAC5J,IAAIO,WAAW,EAAE;YACfN,GAAG,IAAI,CAAC;UACV;QACF;MACF,CAAC,MAAM;QACL;QACA,KAAK,IAAIK,CAAC,GAAGvF,WAAW,GAAG,CAAC,EAAEuF,CAAC,IAAI,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;UAC5C,MAAMC,WAAW,GAAG9F,UAAU,CAACM,WAAW,CAAC,GAAGN,UAAU,CAAC6F,CAAC,CAAC,GAAGN,UAAU;UACxE,IAAIO,WAAW,EAAE;YACfN,GAAG,IAAI,CAAC;UACV;QACF;MACF;IACF;IACA,OAAOA,GAAG;EACZ;EACA3J,MAAMA,CAAA,EAAG;IACP,MAAMsC,MAAM,GAAG,IAAI;IACnB,IAAI,CAACA,MAAM,IAAIA,MAAM,CAAC6G,SAAS,EAAE;IACjC,MAAM;MACJ/E,QAAQ;MACRjD;IACF,CAAC,GAAGmB,MAAM;IACV;IACA,IAAInB,MAAM,CAACZ,WAAW,EAAE;MACtB+B,MAAM,CAAC4H,aAAa,EAAE;IACxB;IACA,CAAC,GAAG5H,MAAM,CAACpB,EAAE,CAACc,gBAAgB,CAAC,kBAAkB,CAAC,CAAC,CAACE,OAAO,CAACiI,OAAO,IAAI;MACrE,IAAIA,OAAO,CAACC,QAAQ,EAAE;QACpBxJ,oBAAoB,CAAC0B,MAAM,EAAE6H,OAAO,CAAC;MACvC;IACF,CAAC,CAAC;IACF7H,MAAM,CAAC+H,UAAU,EAAE;IACnB/H,MAAM,CAACgI,YAAY,EAAE;IACrBhI,MAAM,CAACiI,cAAc,EAAE;IACvBjI,MAAM,CAACmG,mBAAmB,EAAE;IAC5B,SAAS+B,YAAYA,CAAA,EAAG;MACtB,MAAMC,cAAc,GAAGnI,MAAM,CAACoI,YAAY,GAAGpI,MAAM,CAACrC,SAAS,GAAG,CAAC,CAAC,GAAGqC,MAAM,CAACrC,SAAS;MACrF,MAAM0K,YAAY,GAAGzF,IAAI,CAACgD,GAAG,CAAChD,IAAI,CAACiD,GAAG,CAACsC,cAAc,EAAEnI,MAAM,CAAC+F,YAAY,EAAE,CAAC,EAAE/F,MAAM,CAAC8F,YAAY,EAAE,CAAC;MACrG9F,MAAM,CAACkI,YAAY,CAACG,YAAY,CAAC;MACjCrI,MAAM,CAACkG,iBAAiB,EAAE;MAC1BlG,MAAM,CAACmG,mBAAmB,EAAE;IAC9B;IACA,IAAImC,UAAU;IACd,IAAIzJ,MAAM,CAAC0J,QAAQ,IAAI1J,MAAM,CAAC0J,QAAQ,CAAC7G,OAAO,IAAI,CAAC7C,MAAM,CAAC2J,OAAO,EAAE;MACjEN,YAAY,EAAE;MACd,IAAIrJ,MAAM,CAAC4J,UAAU,EAAE;QACrBzI,MAAM,CAAC0I,gBAAgB,EAAE;MAC3B;IACF,CAAC,MAAM;MACL,IAAI,CAAC7J,MAAM,CAAC8J,aAAa,KAAK,MAAM,IAAI9J,MAAM,CAAC8J,aAAa,GAAG,CAAC,KAAK3I,MAAM,CAACsC,KAAK,IAAI,CAACzD,MAAM,CAACyI,cAAc,EAAE;QAC3G,MAAM1F,MAAM,GAAG5B,MAAM,CAAC4I,OAAO,IAAI/J,MAAM,CAAC+J,OAAO,CAAClH,OAAO,GAAG1B,MAAM,CAAC4I,OAAO,CAAChH,MAAM,GAAG5B,MAAM,CAAC4B,MAAM;QAC/F0G,UAAU,GAAGtI,MAAM,CAAC6I,OAAO,CAACjH,MAAM,CAAC5C,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;MAChE,CAAC,MAAM;QACLsJ,UAAU,GAAGtI,MAAM,CAAC6I,OAAO,CAAC7I,MAAM,CAACmC,WAAW,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;MACjE;MACA,IAAI,CAACmG,UAAU,EAAE;QACfJ,YAAY,EAAE;MAChB;IACF;IACA,IAAIrJ,MAAM,CAACiK,aAAa,IAAIhH,QAAQ,KAAK9B,MAAM,CAAC8B,QAAQ,EAAE;MACxD9B,MAAM,CAAC7B,aAAa,EAAE;IACxB;IACA6B,MAAM,CAACkB,IAAI,CAAC,QAAQ,CAAC;EACvB;EACA6H,eAAeA,CAACC,YAAY,EAAqB;IAAA,IAAnBC,UAAU,GAAAlK,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAmE,SAAA,GAAAnE,SAAA,MAAG,IAAI;IAC7C,MAAMiB,MAAM,GAAG,IAAI;IACnB,MAAMkJ,gBAAgB,GAAGlJ,MAAM,CAACnB,MAAM,CAACoD,SAAS;IAChD,IAAI,CAAC+G,YAAY,EAAE;MACjB;MACAA,YAAY,GAAGE,gBAAgB,KAAK,YAAY,GAAG,UAAU,GAAG,YAAY;IAC9E;IACA,IAAIF,YAAY,KAAKE,gBAAgB,IAAIF,YAAY,KAAK,YAAY,IAAIA,YAAY,KAAK,UAAU,EAAE;MACrG,OAAOhJ,MAAM;IACf;IACAA,MAAM,CAACpB,EAAE,CAACuK,SAAS,CAACC,MAAM,CAAE,GAAEpJ,MAAM,CAACnB,MAAM,CAAC6H,sBAAuB,GAAEwC,gBAAiB,EAAC,CAAC;IACxFlJ,MAAM,CAACpB,EAAE,CAACuK,SAAS,CAACE,GAAG,CAAE,GAAErJ,MAAM,CAACnB,MAAM,CAAC6H,sBAAuB,GAAEsC,YAAa,EAAC,CAAC;IACjFhJ,MAAM,CAACoG,oBAAoB,EAAE;IAC7BpG,MAAM,CAACnB,MAAM,CAACoD,SAAS,GAAG+G,YAAY;IACtChJ,MAAM,CAAC4B,MAAM,CAAChC,OAAO,CAACiF,OAAO,IAAI;MAC/B,IAAImE,YAAY,KAAK,UAAU,EAAE;QAC/BnE,OAAO,CAACyE,KAAK,CAACC,KAAK,GAAG,EAAE;MAC1B,CAAC,MAAM;QACL1E,OAAO,CAACyE,KAAK,CAACE,MAAM,GAAG,EAAE;MAC3B;IACF,CAAC,CAAC;IACFxJ,MAAM,CAACkB,IAAI,CAAC,iBAAiB,CAAC;IAC9B,IAAI+H,UAAU,EAAEjJ,MAAM,CAACtC,MAAM,EAAE;IAC/B,OAAOsC,MAAM;EACf;EACAyJ,uBAAuBA,CAACxH,SAAS,EAAE;IACjC,MAAMjC,MAAM,GAAG,IAAI;IACnB,IAAIA,MAAM,CAAC0J,GAAG,IAAIzH,SAAS,KAAK,KAAK,IAAI,CAACjC,MAAM,CAAC0J,GAAG,IAAIzH,SAAS,KAAK,KAAK,EAAE;IAC7EjC,MAAM,CAAC0J,GAAG,GAAGzH,SAAS,KAAK,KAAK;IAChCjC,MAAM,CAACoI,YAAY,GAAGpI,MAAM,CAACnB,MAAM,CAACoD,SAAS,KAAK,YAAY,IAAIjC,MAAM,CAAC0J,GAAG;IAC5E,IAAI1J,MAAM,CAAC0J,GAAG,EAAE;MACd1J,MAAM,CAACpB,EAAE,CAACuK,SAAS,CAACE,GAAG,CAAE,GAAErJ,MAAM,CAACnB,MAAM,CAAC6H,sBAAuB,KAAI,CAAC;MACrE1G,MAAM,CAACpB,EAAE,CAAC+K,GAAG,GAAG,KAAK;IACvB,CAAC,MAAM;MACL3J,MAAM,CAACpB,EAAE,CAACuK,SAAS,CAACC,MAAM,CAAE,GAAEpJ,MAAM,CAACnB,MAAM,CAAC6H,sBAAuB,KAAI,CAAC;MACxE1G,MAAM,CAACpB,EAAE,CAAC+K,GAAG,GAAG,KAAK;IACvB;IACA3J,MAAM,CAACtC,MAAM,EAAE;EACjB;EACAkM,KAAKA,CAACC,OAAO,EAAE;IACb,MAAM7J,MAAM,GAAG,IAAI;IACnB,IAAIA,MAAM,CAAC8J,OAAO,EAAE,OAAO,IAAI;;IAE/B;IACA,IAAIlL,EAAE,GAAGiL,OAAO,IAAI7J,MAAM,CAACnB,MAAM,CAACD,EAAE;IACpC,IAAI,OAAOA,EAAE,KAAK,QAAQ,EAAE;MAC1BA,EAAE,GAAGa,QAAQ,CAACsK,aAAa,CAACnL,EAAE,CAAC;IACjC;IACA,IAAI,CAACA,EAAE,EAAE;MACP,OAAO,KAAK;IACd;IACAA,EAAE,CAACoB,MAAM,GAAGA,MAAM;IAClB,IAAIpB,EAAE,CAACoL,QAAQ,EAAE;MACfhK,MAAM,CAACiK,SAAS,GAAG,IAAI;IACzB;IACA,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;MAC/B,OAAQ,IAAG,CAAClK,MAAM,CAACnB,MAAM,CAACsL,YAAY,IAAI,EAAE,EAAEC,IAAI,EAAE,CAAC5D,KAAK,CAAC,GAAG,CAAC,CAACG,IAAI,CAAC,GAAG,CAAE,EAAC;IAC7E,CAAC;IACD,MAAM0D,UAAU,GAAGA,CAAA,KAAM;MACvB,IAAIzL,EAAE,IAAIA,EAAE,CAAC0L,UAAU,IAAI1L,EAAE,CAAC0L,UAAU,CAACP,aAAa,EAAE;QACtD,MAAMQ,GAAG,GAAG3L,EAAE,CAAC0L,UAAU,CAACP,aAAa,CAACG,kBAAkB,EAAE,CAAC;QAC7D;QACA,OAAOK,GAAG;MACZ;MACA,OAAOtN,eAAe,CAAC2B,EAAE,EAAEsL,kBAAkB,EAAE,CAAC,CAAC,CAAC,CAAC;IACrD,CAAC;IACD;IACA,IAAIM,SAAS,GAAGH,UAAU,EAAE;IAC5B,IAAI,CAACG,SAAS,IAAIxK,MAAM,CAACnB,MAAM,CAAC4L,cAAc,EAAE;MAC9CD,SAAS,GAAGxN,aAAa,CAAC,KAAK,EAAEgD,MAAM,CAACnB,MAAM,CAACsL,YAAY,CAAC;MAC5DvL,EAAE,CAAC8L,MAAM,CAACF,SAAS,CAAC;MACpBvN,eAAe,CAAC2B,EAAE,EAAG,IAAGoB,MAAM,CAACnB,MAAM,CAACkG,UAAW,EAAC,CAAC,CAACnF,OAAO,CAACiF,OAAO,IAAI;QACrE2F,SAAS,CAACE,MAAM,CAAC7F,OAAO,CAAC;MAC3B,CAAC,CAAC;IACJ;IACAzF,MAAM,CAACqC,MAAM,CAACzB,MAAM,EAAE;MACpBpB,EAAE;MACF4L,SAAS;MACT1F,QAAQ,EAAE9E,MAAM,CAACiK,SAAS,GAAGrL,EAAE,GAAG4L,SAAS;MAC3CV,OAAO,EAAE,IAAI;MACb;MACAJ,GAAG,EAAE9K,EAAE,CAAC+K,GAAG,CAACgB,WAAW,EAAE,KAAK,KAAK,IAAIzN,YAAY,CAAC0B,EAAE,EAAE,WAAW,CAAC,KAAK,KAAK;MAC9EwJ,YAAY,EAAEpI,MAAM,CAACnB,MAAM,CAACoD,SAAS,KAAK,YAAY,KAAKrD,EAAE,CAAC+K,GAAG,CAACgB,WAAW,EAAE,KAAK,KAAK,IAAIzN,YAAY,CAAC0B,EAAE,EAAE,WAAW,CAAC,KAAK,KAAK,CAAC;MACrIgM,QAAQ,EAAE1N,YAAY,CAACsN,SAAS,EAAE,SAAS,CAAC,KAAK;IACnD,CAAC,CAAC;IACF,OAAO,IAAI;EACb;EACA7F,IAAIA,CAAC/F,EAAE,EAAE;IACP,MAAMoB,MAAM,GAAG,IAAI;IACnB,IAAIA,MAAM,CAAC6K,WAAW,EAAE,OAAO7K,MAAM;IACrC,MAAM8J,OAAO,GAAG9J,MAAM,CAAC4J,KAAK,CAAChL,EAAE,CAAC;IAChC,IAAIkL,OAAO,KAAK,KAAK,EAAE,OAAO9J,MAAM;IACpCA,MAAM,CAACkB,IAAI,CAAC,YAAY,CAAC;;IAEzB;IACA,IAAIlB,MAAM,CAACnB,MAAM,CAACZ,WAAW,EAAE;MAC7B+B,MAAM,CAAC4H,aAAa,EAAE;IACxB;;IAEA;IACA5H,MAAM,CAAC8K,UAAU,EAAE;;IAEnB;IACA9K,MAAM,CAAC+H,UAAU,EAAE;;IAEnB;IACA/H,MAAM,CAACgI,YAAY,EAAE;IACrB,IAAIhI,MAAM,CAACnB,MAAM,CAACiK,aAAa,EAAE;MAC/B9I,MAAM,CAAC7B,aAAa,EAAE;IACxB;;IAEA;IACA,IAAI6B,MAAM,CAACnB,MAAM,CAACd,UAAU,IAAIiC,MAAM,CAAC0B,OAAO,EAAE;MAC9C1B,MAAM,CAACuF,aAAa,EAAE;IACxB;;IAEA;IACA,IAAIvF,MAAM,CAACnB,MAAM,CAACf,IAAI,IAAIkC,MAAM,CAAC4I,OAAO,IAAI5I,MAAM,CAACnB,MAAM,CAAC+J,OAAO,CAAClH,OAAO,EAAE;MACzE1B,MAAM,CAAC6I,OAAO,CAAC7I,MAAM,CAACnB,MAAM,CAACkM,YAAY,GAAG/K,MAAM,CAAC4I,OAAO,CAACoC,YAAY,EAAE,CAAC,EAAEhL,MAAM,CAACnB,MAAM,CAACoM,kBAAkB,EAAE,KAAK,EAAE,IAAI,CAAC;IAC5H,CAAC,MAAM;MACLjL,MAAM,CAAC6I,OAAO,CAAC7I,MAAM,CAACnB,MAAM,CAACkM,YAAY,EAAE,CAAC,EAAE/K,MAAM,CAACnB,MAAM,CAACoM,kBAAkB,EAAE,KAAK,EAAE,IAAI,CAAC;IAC9F;;IAEA;IACA,IAAIjL,MAAM,CAACnB,MAAM,CAACf,IAAI,EAAE;MACtBkC,MAAM,CAACkL,UAAU,EAAE;IACrB;;IAEA;IACAlL,MAAM,CAACmL,YAAY,EAAE;IACrB,CAAC,GAAGnL,MAAM,CAACpB,EAAE,CAACc,gBAAgB,CAAC,kBAAkB,CAAC,CAAC,CAACE,OAAO,CAACiI,OAAO,IAAI;MACrE,IAAIA,OAAO,CAACC,QAAQ,EAAE;QACpBxJ,oBAAoB,CAAC0B,MAAM,EAAE6H,OAAO,CAAC;MACvC,CAAC,MAAM;QACLA,OAAO,CAACuD,gBAAgB,CAAC,MAAM,EAAEC,CAAC,IAAI;UACpC/M,oBAAoB,CAAC0B,MAAM,EAAEqL,CAAC,CAACC,MAAM,CAAC;QACxC,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IACF/M,OAAO,CAACyB,MAAM,CAAC;;IAEf;IACAA,MAAM,CAAC6K,WAAW,GAAG,IAAI;IACzBtM,OAAO,CAACyB,MAAM,CAAC;;IAEf;IACAA,MAAM,CAACkB,IAAI,CAAC,MAAM,CAAC;IACnBlB,MAAM,CAACkB,IAAI,CAAC,WAAW,CAAC;IACxB,OAAOlB,MAAM;EACf;EACAuL,OAAOA,CAAA,EAA4C;IAAA,IAA3CC,cAAc,GAAAzM,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAmE,SAAA,GAAAnE,SAAA,MAAG,IAAI;IAAA,IAAE0M,WAAW,GAAA1M,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAmE,SAAA,GAAAnE,SAAA,MAAG,IAAI;IAC/C,MAAMiB,MAAM,GAAG,IAAI;IACnB,MAAM;MACJnB,MAAM;MACND,EAAE;MACF4L,SAAS;MACT5I;IACF,CAAC,GAAG5B,MAAM;IACV,IAAI,OAAOA,MAAM,CAACnB,MAAM,KAAK,WAAW,IAAImB,MAAM,CAAC6G,SAAS,EAAE;MAC5D,OAAO,IAAI;IACb;IACA7G,MAAM,CAACkB,IAAI,CAAC,eAAe,CAAC;;IAE5B;IACAlB,MAAM,CAAC6K,WAAW,GAAG,KAAK;;IAE1B;IACA7K,MAAM,CAAC0L,YAAY,EAAE;;IAErB;IACA,IAAI7M,MAAM,CAACf,IAAI,EAAE;MACfkC,MAAM,CAAC2L,WAAW,EAAE;IACtB;;IAEA;IACA,IAAIF,WAAW,EAAE;MACfzL,MAAM,CAAC4L,aAAa,EAAE;MACtBhN,EAAE,CAACiN,eAAe,CAAC,OAAO,CAAC;MAC3BrB,SAAS,CAACqB,eAAe,CAAC,OAAO,CAAC;MAClC,IAAIjK,MAAM,IAAIA,MAAM,CAAC5C,MAAM,EAAE;QAC3B4C,MAAM,CAAChC,OAAO,CAACiF,OAAO,IAAI;UACxBA,OAAO,CAACsE,SAAS,CAACC,MAAM,CAACvK,MAAM,CAACiN,iBAAiB,EAAEjN,MAAM,CAACkN,gBAAgB,EAAElN,MAAM,CAACmN,cAAc,EAAEnN,MAAM,CAACoN,cAAc,CAAC;UACzHpH,OAAO,CAACgH,eAAe,CAAC,OAAO,CAAC;UAChChH,OAAO,CAACgH,eAAe,CAAC,yBAAyB,CAAC;QACpD,CAAC,CAAC;MACJ;IACF;IACA7L,MAAM,CAACkB,IAAI,CAAC,SAAS,CAAC;;IAEtB;IACA9B,MAAM,CAACkC,IAAI,CAACtB,MAAM,CAACM,eAAe,CAAC,CAACV,OAAO,CAAC2B,SAAS,IAAI;MACvDvB,MAAM,CAACiB,GAAG,CAACM,SAAS,CAAC;IACvB,CAAC,CAAC;IACF,IAAIiK,cAAc,KAAK,KAAK,EAAE;MAC5BxL,MAAM,CAACpB,EAAE,CAACoB,MAAM,GAAG,IAAI;MACvBjD,WAAW,CAACiD,MAAM,CAAC;IACrB;IACAA,MAAM,CAAC6G,SAAS,GAAG,IAAI;IACvB,OAAO,IAAI;EACb;EACA,OAAOqF,cAAcA,CAACC,WAAW,EAAE;IACjCrP,MAAM,CAAC2B,gBAAgB,EAAE0N,WAAW,CAAC;EACvC;EACA,WAAW1N,gBAAgBA,CAAA,EAAG;IAC5B,OAAOA,gBAAgB;EACzB;EACA,WAAWL,QAAQA,CAAA,EAAG;IACpB,OAAOA,QAAQ;EACjB;EACA,OAAOgO,aAAaA,CAACxL,GAAG,EAAE;IACxB,IAAI,CAAClC,MAAM,CAACW,SAAS,CAACoB,WAAW,EAAE/B,MAAM,CAACW,SAAS,CAACoB,WAAW,GAAG,EAAE;IACpE,MAAMD,OAAO,GAAG9B,MAAM,CAACW,SAAS,CAACoB,WAAW;IAC5C,IAAI,OAAOG,GAAG,KAAK,UAAU,IAAIJ,OAAO,CAACiG,OAAO,CAAC7F,GAAG,CAAC,GAAG,CAAC,EAAE;MACzDJ,OAAO,CAACT,IAAI,CAACa,GAAG,CAAC;IACnB;EACF;EACA,OAAOyL,GAAGA,CAACC,MAAM,EAAE;IACjB,IAAIpN,KAAK,CAACwB,OAAO,CAAC4L,MAAM,CAAC,EAAE;MACzBA,MAAM,CAAC1M,OAAO,CAAC2M,CAAC,IAAI7N,MAAM,CAAC0N,aAAa,CAACG,CAAC,CAAC,CAAC;MAC5C,OAAO7N,MAAM;IACf;IACAA,MAAM,CAAC0N,aAAa,CAACE,MAAM,CAAC;IAC5B,OAAO5N,MAAM;EACf;AACF;AACAU,MAAM,CAACkC,IAAI,CAAC9C,UAAU,CAAC,CAACoB,OAAO,CAAC4M,cAAc,IAAI;EAChDpN,MAAM,CAACkC,IAAI,CAAC9C,UAAU,CAACgO,cAAc,CAAC,CAAC,CAAC5M,OAAO,CAAC6M,WAAW,IAAI;IAC7D/N,MAAM,CAACW,SAAS,CAACoN,WAAW,CAAC,GAAGjO,UAAU,CAACgO,cAAc,CAAC,CAACC,WAAW,CAAC;EACzE,CAAC,CAAC;AACJ,CAAC,CAAC;AACF/N,MAAM,CAAC2N,GAAG,CAAC,CAAC9O,MAAM,EAAEC,QAAQ,CAAC,CAAC;AAC9B,eAAekB,MAAM"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}