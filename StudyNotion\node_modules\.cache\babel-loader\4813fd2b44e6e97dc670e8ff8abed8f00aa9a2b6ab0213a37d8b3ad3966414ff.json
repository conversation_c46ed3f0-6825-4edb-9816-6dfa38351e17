{"ast": null, "code": "/**\n * @typedef {import('micromark-util-types').Create} Create\n * @typedef {import('micromark-util-types').FullNormalizedExtension} FullNormalizedExtension\n * @typedef {import('micromark-util-types').InitialConstruct} InitialConstruct\n * @typedef {import('micromark-util-types').ParseContext} ParseContext\n * @typedef {import('micromark-util-types').ParseOptions} ParseOptions\n */\n\nimport { combineExtensions } from 'micromark-util-combine-extensions';\nimport { content } from './initialize/content.js';\nimport { document } from './initialize/document.js';\nimport { flow } from './initialize/flow.js';\nimport { text, string } from './initialize/text.js';\nimport { createTokenizer } from './create-tokenizer.js';\nimport * as defaultConstructs from './constructs.js';\n\n/**\n * @param {ParseOptions | null | undefined} [options]\n * @returns {ParseContext}\n */\nexport function parse(options) {\n  const settings = options || {};\n  const constructs = /** @type {FullNormalizedExtension} */\n  combineExtensions([defaultConstructs, ...(settings.extensions || [])]);\n\n  /** @type {ParseContext} */\n  const parser = {\n    defined: [],\n    lazy: {},\n    constructs,\n    content: create(content),\n    document: create(document),\n    flow: create(flow),\n    string: create(string),\n    text: create(text)\n  };\n  return parser;\n\n  /**\n   * @param {InitialConstruct} initial\n   */\n  function create(initial) {\n    return creator;\n    /** @type {Create} */\n    function creator(from) {\n      return createTokenizer(parser, initial, from);\n    }\n  }\n}", "map": {"version": 3, "names": ["combineExtensions", "content", "document", "flow", "text", "string", "createTokenizer", "defaultConstructs", "parse", "options", "settings", "constructs", "extensions", "parser", "defined", "lazy", "create", "initial", "creator", "from"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/micromark/dev/lib/parse.js"], "sourcesContent": ["/**\n * @typedef {import('micromark-util-types').Create} Create\n * @typedef {import('micromark-util-types').FullNormalizedExtension} FullNormalizedExtension\n * @typedef {import('micromark-util-types').InitialConstruct} InitialConstruct\n * @typedef {import('micromark-util-types').ParseContext} ParseContext\n * @typedef {import('micromark-util-types').ParseOptions} ParseOptions\n */\n\nimport {combineExtensions} from 'micromark-util-combine-extensions'\nimport {content} from './initialize/content.js'\nimport {document} from './initialize/document.js'\nimport {flow} from './initialize/flow.js'\nimport {text, string} from './initialize/text.js'\nimport {createTokenizer} from './create-tokenizer.js'\nimport * as defaultConstructs from './constructs.js'\n\n/**\n * @param {ParseOptions | null | undefined} [options]\n * @returns {ParseContext}\n */\nexport function parse(options) {\n  const settings = options || {}\n  const constructs = /** @type {FullNormalizedExtension} */ (\n    combineExtensions([defaultConstructs, ...(settings.extensions || [])])\n  )\n\n  /** @type {ParseContext} */\n  const parser = {\n    defined: [],\n    lazy: {},\n    constructs,\n    content: create(content),\n    document: create(document),\n    flow: create(flow),\n    string: create(string),\n    text: create(text)\n  }\n\n  return parser\n\n  /**\n   * @param {InitialConstruct} initial\n   */\n  function create(initial) {\n    return creator\n    /** @type {Create} */\n    function creator(from) {\n      return createTokenizer(parser, initial, from)\n    }\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,iBAAiB,QAAO,mCAAmC;AACnE,SAAQC,OAAO,QAAO,yBAAyB;AAC/C,SAAQC,QAAQ,QAAO,0BAA0B;AACjD,SAAQC,IAAI,QAAO,sBAAsB;AACzC,SAAQC,IAAI,EAAEC,MAAM,QAAO,sBAAsB;AACjD,SAAQC,eAAe,QAAO,uBAAuB;AACrD,OAAO,KAAKC,iBAAiB,MAAM,iBAAiB;;AAEpD;AACA;AACA;AACA;AACA,OAAO,SAASC,KAAKA,CAACC,OAAO,EAAE;EAC7B,MAAMC,QAAQ,GAAGD,OAAO,IAAI,CAAC,CAAC;EAC9B,MAAME,UAAU,GAAG;EACjBX,iBAAiB,CAAC,CAACO,iBAAiB,EAAE,IAAIG,QAAQ,CAACE,UAAU,IAAI,EAAE,CAAC,CAAC,CACtE;;EAED;EACA,MAAMC,MAAM,GAAG;IACbC,OAAO,EAAE,EAAE;IACXC,IAAI,EAAE,CAAC,CAAC;IACRJ,UAAU;IACVV,OAAO,EAAEe,MAAM,CAACf,OAAO,CAAC;IACxBC,QAAQ,EAAEc,MAAM,CAACd,QAAQ,CAAC;IAC1BC,IAAI,EAAEa,MAAM,CAACb,IAAI,CAAC;IAClBE,MAAM,EAAEW,MAAM,CAACX,MAAM,CAAC;IACtBD,IAAI,EAAEY,MAAM,CAACZ,IAAI;EACnB,CAAC;EAED,OAAOS,MAAM;;EAEb;AACF;AACA;EACE,SAASG,MAAMA,CAACC,OAAO,EAAE;IACvB,OAAOC,OAAO;IACd;IACA,SAASA,OAAOA,CAACC,IAAI,EAAE;MACrB,OAAOb,eAAe,CAACO,MAAM,EAAEI,OAAO,EAAEE,IAAI,CAAC;IAC/C;EACF;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}