{"ast": null, "code": "/**\n * @typedef {import('./schema.js').Properties} Properties\n * @typedef {import('./schema.js').Normal} Normal\n *\n * @typedef {Record<string, string>} Attributes\n *\n * @typedef {Object} Definition\n * @property {Record<string, number|null>} properties\n * @property {(attributes: Attributes, property: string) => string} transform\n * @property {string} [space]\n * @property {Attributes} [attributes]\n * @property {Array<string>} [mustUseProperty]\n */\n\nimport { normalize } from '../normalize.js';\nimport { Schema } from './schema.js';\nimport { DefinedInfo } from './defined-info.js';\nconst own = {}.hasOwnProperty;\n\n/**\n * @param {Definition} definition\n * @returns {Schema}\n */\nexport function create(definition) {\n  /** @type {Properties} */\n  const property = {};\n  /** @type {Normal} */\n  const normal = {};\n  /** @type {string} */\n  let prop;\n  for (prop in definition.properties) {\n    if (own.call(definition.properties, prop)) {\n      const value = definition.properties[prop];\n      const info = new DefinedInfo(prop, definition.transform(definition.attributes || {}, prop), value, definition.space);\n      if (definition.mustUseProperty && definition.mustUseProperty.includes(prop)) {\n        info.mustUseProperty = true;\n      }\n      property[prop] = info;\n      normal[normalize(prop)] = prop;\n      normal[normalize(info.attribute)] = prop;\n    }\n  }\n  return new Schema(property, normal, definition.space);\n}", "map": {"version": 3, "names": ["normalize", "<PERSON><PERSON><PERSON>", "DefinedInfo", "own", "hasOwnProperty", "create", "definition", "property", "normal", "prop", "properties", "call", "value", "info", "transform", "attributes", "space", "mustUseProperty", "includes", "attribute"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/property-information/lib/util/create.js"], "sourcesContent": ["/**\n * @typedef {import('./schema.js').Properties} Properties\n * @typedef {import('./schema.js').Normal} Normal\n *\n * @typedef {Record<string, string>} Attributes\n *\n * @typedef {Object} Definition\n * @property {Record<string, number|null>} properties\n * @property {(attributes: Attributes, property: string) => string} transform\n * @property {string} [space]\n * @property {Attributes} [attributes]\n * @property {Array<string>} [mustUseProperty]\n */\n\nimport {normalize} from '../normalize.js'\nimport {Schema} from './schema.js'\nimport {DefinedInfo} from './defined-info.js'\n\nconst own = {}.hasOwnProperty\n\n/**\n * @param {Definition} definition\n * @returns {Schema}\n */\nexport function create(definition) {\n  /** @type {Properties} */\n  const property = {}\n  /** @type {Normal} */\n  const normal = {}\n  /** @type {string} */\n  let prop\n\n  for (prop in definition.properties) {\n    if (own.call(definition.properties, prop)) {\n      const value = definition.properties[prop]\n      const info = new DefinedInfo(\n        prop,\n        definition.transform(definition.attributes || {}, prop),\n        value,\n        definition.space\n      )\n\n      if (\n        definition.mustUseProperty &&\n        definition.mustUseProperty.includes(prop)\n      ) {\n        info.mustUseProperty = true\n      }\n\n      property[prop] = info\n\n      normal[normalize(prop)] = prop\n      normal[normalize(info.attribute)] = prop\n    }\n  }\n\n  return new Schema(property, normal, definition.space)\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,SAAS,QAAO,iBAAiB;AACzC,SAAQC,MAAM,QAAO,aAAa;AAClC,SAAQC,WAAW,QAAO,mBAAmB;AAE7C,MAAMC,GAAG,GAAG,CAAC,CAAC,CAACC,cAAc;;AAE7B;AACA;AACA;AACA;AACA,OAAO,SAASC,MAAMA,CAACC,UAAU,EAAE;EACjC;EACA,MAAMC,QAAQ,GAAG,CAAC,CAAC;EACnB;EACA,MAAMC,MAAM,GAAG,CAAC,CAAC;EACjB;EACA,IAAIC,IAAI;EAER,KAAKA,IAAI,IAAIH,UAAU,CAACI,UAAU,EAAE;IAClC,IAAIP,GAAG,CAACQ,IAAI,CAACL,UAAU,CAACI,UAAU,EAAED,IAAI,CAAC,EAAE;MACzC,MAAMG,KAAK,GAAGN,UAAU,CAACI,UAAU,CAACD,IAAI,CAAC;MACzC,MAAMI,IAAI,GAAG,IAAIX,WAAW,CAC1BO,IAAI,EACJH,UAAU,CAACQ,SAAS,CAACR,UAAU,CAACS,UAAU,IAAI,CAAC,CAAC,EAAEN,IAAI,CAAC,EACvDG,KAAK,EACLN,UAAU,CAACU,KAAK,CACjB;MAED,IACEV,UAAU,CAACW,eAAe,IAC1BX,UAAU,CAACW,eAAe,CAACC,QAAQ,CAACT,IAAI,CAAC,EACzC;QACAI,IAAI,CAACI,eAAe,GAAG,IAAI;MAC7B;MAEAV,QAAQ,CAACE,IAAI,CAAC,GAAGI,IAAI;MAErBL,MAAM,CAACR,SAAS,CAACS,IAAI,CAAC,CAAC,GAAGA,IAAI;MAC9BD,MAAM,CAACR,SAAS,CAACa,IAAI,CAACM,SAAS,CAAC,CAAC,GAAGV,IAAI;IAC1C;EACF;EAEA,OAAO,IAAIR,MAAM,CAACM,QAAQ,EAAEC,MAAM,EAAEF,UAAU,CAACU,KAAK,CAAC;AACvD"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}