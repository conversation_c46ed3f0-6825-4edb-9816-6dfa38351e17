{"ast": null, "code": "function Diff() {}\nDiff.prototype = {\n  diff: function diff(oldString, newString) {\n    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    var callback = options.callback;\n    if (typeof options === 'function') {\n      callback = options;\n      options = {};\n    }\n    this.options = options;\n    var self = this;\n    function done(value) {\n      if (callback) {\n        setTimeout(function () {\n          callback(undefined, value);\n        }, 0);\n        return true;\n      } else {\n        return value;\n      }\n    } // Allow subclasses to massage the input prior to running\n\n    oldString = this.castInput(oldString);\n    newString = this.castInput(newString);\n    oldString = this.removeEmpty(this.tokenize(oldString));\n    newString = this.removeEmpty(this.tokenize(newString));\n    var newLen = newString.length,\n      oldLen = oldString.length;\n    var editLength = 1;\n    var maxEditLength = newLen + oldLen;\n    if (options.maxEditLength) {\n      maxEditLength = Math.min(maxEditLength, options.maxEditLength);\n    }\n    var bestPath = [{\n      newPos: -1,\n      components: []\n    }]; // Seed editLength = 0, i.e. the content starts with the same values\n\n    var oldPos = this.extractCommon(bestPath[0], newString, oldString, 0);\n    if (bestPath[0].newPos + 1 >= newLen && oldPos + 1 >= oldLen) {\n      // Identity per the equality and tokenizer\n      return done([{\n        value: this.join(newString),\n        count: newString.length\n      }]);\n    } // Main worker method. checks all permutations of a given edit length for acceptance.\n\n    function execEditLength() {\n      for (var diagonalPath = -1 * editLength; diagonalPath <= editLength; diagonalPath += 2) {\n        var basePath = void 0;\n        var addPath = bestPath[diagonalPath - 1],\n          removePath = bestPath[diagonalPath + 1],\n          _oldPos = (removePath ? removePath.newPos : 0) - diagonalPath;\n        if (addPath) {\n          // No one else is going to attempt to use this value, clear it\n          bestPath[diagonalPath - 1] = undefined;\n        }\n        var canAdd = addPath && addPath.newPos + 1 < newLen,\n          canRemove = removePath && 0 <= _oldPos && _oldPos < oldLen;\n        if (!canAdd && !canRemove) {\n          // If this path is a terminal then prune\n          bestPath[diagonalPath] = undefined;\n          continue;\n        } // Select the diagonal that we want to branch from. We select the prior\n        // path whose position in the new string is the farthest from the origin\n        // and does not pass the bounds of the diff graph\n\n        if (!canAdd || canRemove && addPath.newPos < removePath.newPos) {\n          basePath = clonePath(removePath);\n          self.pushComponent(basePath.components, undefined, true);\n        } else {\n          basePath = addPath; // No need to clone, we've pulled it from the list\n\n          basePath.newPos++;\n          self.pushComponent(basePath.components, true, undefined);\n        }\n        _oldPos = self.extractCommon(basePath, newString, oldString, diagonalPath); // If we have hit the end of both strings, then we are done\n\n        if (basePath.newPos + 1 >= newLen && _oldPos + 1 >= oldLen) {\n          return done(buildValues(self, basePath.components, newString, oldString, self.useLongestToken));\n        } else {\n          // Otherwise track this path as a potential candidate and continue.\n          bestPath[diagonalPath] = basePath;\n        }\n      }\n      editLength++;\n    } // Performs the length of edit iteration. Is a bit fugly as this has to support the\n    // sync and async mode which is never fun. Loops over execEditLength until a value\n    // is produced, or until the edit length exceeds options.maxEditLength (if given),\n    // in which case it will return undefined.\n\n    if (callback) {\n      (function exec() {\n        setTimeout(function () {\n          if (editLength > maxEditLength) {\n            return callback();\n          }\n          if (!execEditLength()) {\n            exec();\n          }\n        }, 0);\n      })();\n    } else {\n      while (editLength <= maxEditLength) {\n        var ret = execEditLength();\n        if (ret) {\n          return ret;\n        }\n      }\n    }\n  },\n  pushComponent: function pushComponent(components, added, removed) {\n    var last = components[components.length - 1];\n    if (last && last.added === added && last.removed === removed) {\n      // We need to clone here as the component clone operation is just\n      // as shallow array clone\n      components[components.length - 1] = {\n        count: last.count + 1,\n        added: added,\n        removed: removed\n      };\n    } else {\n      components.push({\n        count: 1,\n        added: added,\n        removed: removed\n      });\n    }\n  },\n  extractCommon: function extractCommon(basePath, newString, oldString, diagonalPath) {\n    var newLen = newString.length,\n      oldLen = oldString.length,\n      newPos = basePath.newPos,\n      oldPos = newPos - diagonalPath,\n      commonCount = 0;\n    while (newPos + 1 < newLen && oldPos + 1 < oldLen && this.equals(newString[newPos + 1], oldString[oldPos + 1])) {\n      newPos++;\n      oldPos++;\n      commonCount++;\n    }\n    if (commonCount) {\n      basePath.components.push({\n        count: commonCount\n      });\n    }\n    basePath.newPos = newPos;\n    return oldPos;\n  },\n  equals: function equals(left, right) {\n    if (this.options.comparator) {\n      return this.options.comparator(left, right);\n    } else {\n      return left === right || this.options.ignoreCase && left.toLowerCase() === right.toLowerCase();\n    }\n  },\n  removeEmpty: function removeEmpty(array) {\n    var ret = [];\n    for (var i = 0; i < array.length; i++) {\n      if (array[i]) {\n        ret.push(array[i]);\n      }\n    }\n    return ret;\n  },\n  castInput: function castInput(value) {\n    return value;\n  },\n  tokenize: function tokenize(value) {\n    return value.split('');\n  },\n  join: function join(chars) {\n    return chars.join('');\n  }\n};\nfunction buildValues(diff, components, newString, oldString, useLongestToken) {\n  var componentPos = 0,\n    componentLen = components.length,\n    newPos = 0,\n    oldPos = 0;\n  for (; componentPos < componentLen; componentPos++) {\n    var component = components[componentPos];\n    if (!component.removed) {\n      if (!component.added && useLongestToken) {\n        var value = newString.slice(newPos, newPos + component.count);\n        value = value.map(function (value, i) {\n          var oldValue = oldString[oldPos + i];\n          return oldValue.length > value.length ? oldValue : value;\n        });\n        component.value = diff.join(value);\n      } else {\n        component.value = diff.join(newString.slice(newPos, newPos + component.count));\n      }\n      newPos += component.count; // Common case\n\n      if (!component.added) {\n        oldPos += component.count;\n      }\n    } else {\n      component.value = diff.join(oldString.slice(oldPos, oldPos + component.count));\n      oldPos += component.count; // Reverse add and remove so removes are output first to match common convention\n      // The diffing algorithm is tied to add then remove output and this is the simplest\n      // route to get the desired output with minimal overhead.\n\n      if (componentPos && components[componentPos - 1].added) {\n        var tmp = components[componentPos - 1];\n        components[componentPos - 1] = components[componentPos];\n        components[componentPos] = tmp;\n      }\n    }\n  } // Special case handle for when one terminal is ignored (i.e. whitespace).\n  // For this case we merge the terminal into the prior string and drop the change.\n  // This is only available for string mode.\n\n  var lastComponent = components[componentLen - 1];\n  if (componentLen > 1 && typeof lastComponent.value === 'string' && (lastComponent.added || lastComponent.removed) && diff.equals('', lastComponent.value)) {\n    components[componentLen - 2].value += lastComponent.value;\n    components.pop();\n  }\n  return components;\n}\nfunction clonePath(path) {\n  return {\n    newPos: path.newPos,\n    components: path.components.slice(0)\n  };\n}\nvar characterDiff = new Diff();\nfunction diffChars(oldStr, newStr, options) {\n  return characterDiff.diff(oldStr, newStr, options);\n}\nfunction generateOptions(options, defaults) {\n  if (typeof options === 'function') {\n    defaults.callback = options;\n  } else if (options) {\n    for (var name in options) {\n      /* istanbul ignore else */\n      if (options.hasOwnProperty(name)) {\n        defaults[name] = options[name];\n      }\n    }\n  }\n  return defaults;\n}\n\n//\n// Ranges and exceptions:\n// Latin-1 Supplement, 0080–00FF\n//  - U+00D7  × Multiplication sign\n//  - U+00F7  ÷ Division sign\n// Latin Extended-A, 0100–017F\n// Latin Extended-B, 0180–024F\n// IPA Extensions, 0250–02AF\n// Spacing Modifier Letters, 02B0–02FF\n//  - U+02C7  ˇ &#711;  Caron\n//  - U+02D8  ˘ &#728;  Breve\n//  - U+02D9  ˙ &#729;  Dot Above\n//  - U+02DA  ˚ &#730;  Ring Above\n//  - U+02DB  ˛ &#731;  Ogonek\n//  - U+02DC  ˜ &#732;  Small Tilde\n//  - U+02DD  ˝ &#733;  Double Acute Accent\n// Latin Extended Additional, 1E00–1EFF\n\nvar extendedWordChars = /^[A-Za-z\\xC0-\\u02C6\\u02C8-\\u02D7\\u02DE-\\u02FF\\u1E00-\\u1EFF]+$/;\nvar reWhitespace = /\\S/;\nvar wordDiff = new Diff();\nwordDiff.equals = function (left, right) {\n  if (this.options.ignoreCase) {\n    left = left.toLowerCase();\n    right = right.toLowerCase();\n  }\n  return left === right || this.options.ignoreWhitespace && !reWhitespace.test(left) && !reWhitespace.test(right);\n};\nwordDiff.tokenize = function (value) {\n  // All whitespace symbols except newline group into one token, each newline - in separate token\n  var tokens = value.split(/([^\\S\\r\\n]+|[()[\\]{}'\"\\r\\n]|\\b)/); // Join the boundary splits that we do not consider to be boundaries. This is primarily the extended Latin character set.\n\n  for (var i = 0; i < tokens.length - 1; i++) {\n    // If we have an empty string in the next field and we have only word chars before and after, merge\n    if (!tokens[i + 1] && tokens[i + 2] && extendedWordChars.test(tokens[i]) && extendedWordChars.test(tokens[i + 2])) {\n      tokens[i] += tokens[i + 2];\n      tokens.splice(i + 1, 2);\n      i--;\n    }\n  }\n  return tokens;\n};\nfunction diffWords(oldStr, newStr, options) {\n  options = generateOptions(options, {\n    ignoreWhitespace: true\n  });\n  return wordDiff.diff(oldStr, newStr, options);\n}\nfunction diffWordsWithSpace(oldStr, newStr, options) {\n  return wordDiff.diff(oldStr, newStr, options);\n}\nvar lineDiff = new Diff();\nlineDiff.tokenize = function (value) {\n  var retLines = [],\n    linesAndNewlines = value.split(/(\\n|\\r\\n)/); // Ignore the final empty token that occurs if the string ends with a new line\n\n  if (!linesAndNewlines[linesAndNewlines.length - 1]) {\n    linesAndNewlines.pop();\n  } // Merge the content and line separators into single tokens\n\n  for (var i = 0; i < linesAndNewlines.length; i++) {\n    var line = linesAndNewlines[i];\n    if (i % 2 && !this.options.newlineIsToken) {\n      retLines[retLines.length - 1] += line;\n    } else {\n      if (this.options.ignoreWhitespace) {\n        line = line.trim();\n      }\n      retLines.push(line);\n    }\n  }\n  return retLines;\n};\nfunction diffLines(oldStr, newStr, callback) {\n  return lineDiff.diff(oldStr, newStr, callback);\n}\nfunction diffTrimmedLines(oldStr, newStr, callback) {\n  var options = generateOptions(callback, {\n    ignoreWhitespace: true\n  });\n  return lineDiff.diff(oldStr, newStr, options);\n}\nvar sentenceDiff = new Diff();\nsentenceDiff.tokenize = function (value) {\n  return value.split(/(\\S.+?[.!?])(?=\\s+|$)/);\n};\nfunction diffSentences(oldStr, newStr, callback) {\n  return sentenceDiff.diff(oldStr, newStr, callback);\n}\nvar cssDiff = new Diff();\ncssDiff.tokenize = function (value) {\n  return value.split(/([{}:;,]|\\s+)/);\n};\nfunction diffCss(oldStr, newStr, callback) {\n  return cssDiff.diff(oldStr, newStr, callback);\n}\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n    _typeof = function (obj) {\n      return typeof obj;\n    };\n  } else {\n    _typeof = function (obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n  }\n  return _typeof(obj);\n}\nfunction _toConsumableArray(arr) {\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\nfunction _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && Symbol.iterator in Object(iter)) return Array.from(iter);\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n  return arr2;\n}\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nvar objectPrototypeToString = Object.prototype.toString;\nvar jsonDiff = new Diff(); // Discriminate between two lines of pretty-printed, serialized JSON where one of them has a\n// dangling comma and the other doesn't. Turns out including the dangling comma yields the nicest output:\n\njsonDiff.useLongestToken = true;\njsonDiff.tokenize = lineDiff.tokenize;\njsonDiff.castInput = function (value) {\n  var _this$options = this.options,\n    undefinedReplacement = _this$options.undefinedReplacement,\n    _this$options$stringi = _this$options.stringifyReplacer,\n    stringifyReplacer = _this$options$stringi === void 0 ? function (k, v) {\n      return typeof v === 'undefined' ? undefinedReplacement : v;\n    } : _this$options$stringi;\n  return typeof value === 'string' ? value : JSON.stringify(canonicalize(value, null, null, stringifyReplacer), stringifyReplacer, '  ');\n};\njsonDiff.equals = function (left, right) {\n  return Diff.prototype.equals.call(jsonDiff, left.replace(/,([\\r\\n])/g, '$1'), right.replace(/,([\\r\\n])/g, '$1'));\n};\nfunction diffJson(oldObj, newObj, options) {\n  return jsonDiff.diff(oldObj, newObj, options);\n} // This function handles the presence of circular references by bailing out when encountering an\n// object that is already on the \"stack\" of items being processed. Accepts an optional replacer\n\nfunction canonicalize(obj, stack, replacementStack, replacer, key) {\n  stack = stack || [];\n  replacementStack = replacementStack || [];\n  if (replacer) {\n    obj = replacer(key, obj);\n  }\n  var i;\n  for (i = 0; i < stack.length; i += 1) {\n    if (stack[i] === obj) {\n      return replacementStack[i];\n    }\n  }\n  var canonicalizedObj;\n  if ('[object Array]' === objectPrototypeToString.call(obj)) {\n    stack.push(obj);\n    canonicalizedObj = new Array(obj.length);\n    replacementStack.push(canonicalizedObj);\n    for (i = 0; i < obj.length; i += 1) {\n      canonicalizedObj[i] = canonicalize(obj[i], stack, replacementStack, replacer, key);\n    }\n    stack.pop();\n    replacementStack.pop();\n    return canonicalizedObj;\n  }\n  if (obj && obj.toJSON) {\n    obj = obj.toJSON();\n  }\n  if (_typeof(obj) === 'object' && obj !== null) {\n    stack.push(obj);\n    canonicalizedObj = {};\n    replacementStack.push(canonicalizedObj);\n    var sortedKeys = [],\n      _key;\n    for (_key in obj) {\n      /* istanbul ignore else */\n      if (obj.hasOwnProperty(_key)) {\n        sortedKeys.push(_key);\n      }\n    }\n    sortedKeys.sort();\n    for (i = 0; i < sortedKeys.length; i += 1) {\n      _key = sortedKeys[i];\n      canonicalizedObj[_key] = canonicalize(obj[_key], stack, replacementStack, replacer, _key);\n    }\n    stack.pop();\n    replacementStack.pop();\n  } else {\n    canonicalizedObj = obj;\n  }\n  return canonicalizedObj;\n}\nvar arrayDiff = new Diff();\narrayDiff.tokenize = function (value) {\n  return value.slice();\n};\narrayDiff.join = arrayDiff.removeEmpty = function (value) {\n  return value;\n};\nfunction diffArrays(oldArr, newArr, callback) {\n  return arrayDiff.diff(oldArr, newArr, callback);\n}\nfunction parsePatch(uniDiff) {\n  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var diffstr = uniDiff.split(/\\r\\n|[\\n\\v\\f\\r\\x85]/),\n    delimiters = uniDiff.match(/\\r\\n|[\\n\\v\\f\\r\\x85]/g) || [],\n    list = [],\n    i = 0;\n  function parseIndex() {\n    var index = {};\n    list.push(index); // Parse diff metadata\n\n    while (i < diffstr.length) {\n      var line = diffstr[i]; // File header found, end parsing diff metadata\n\n      if (/^(\\-\\-\\-|\\+\\+\\+|@@)\\s/.test(line)) {\n        break;\n      } // Diff index\n\n      var header = /^(?:Index:|diff(?: -r \\w+)+)\\s+(.+?)\\s*$/.exec(line);\n      if (header) {\n        index.index = header[1];\n      }\n      i++;\n    } // Parse file headers if they are defined. Unified diff requires them, but\n    // there's no technical issues to have an isolated hunk without file header\n\n    parseFileHeader(index);\n    parseFileHeader(index); // Parse hunks\n\n    index.hunks = [];\n    while (i < diffstr.length) {\n      var _line = diffstr[i];\n      if (/^(Index:|diff|\\-\\-\\-|\\+\\+\\+)\\s/.test(_line)) {\n        break;\n      } else if (/^@@/.test(_line)) {\n        index.hunks.push(parseHunk());\n      } else if (_line && options.strict) {\n        // Ignore unexpected content unless in strict mode\n        throw new Error('Unknown line ' + (i + 1) + ' ' + JSON.stringify(_line));\n      } else {\n        i++;\n      }\n    }\n  } // Parses the --- and +++ headers, if none are found, no lines\n  // are consumed.\n\n  function parseFileHeader(index) {\n    var fileHeader = /^(---|\\+\\+\\+)\\s+(.*)$/.exec(diffstr[i]);\n    if (fileHeader) {\n      var keyPrefix = fileHeader[1] === '---' ? 'old' : 'new';\n      var data = fileHeader[2].split('\\t', 2);\n      var fileName = data[0].replace(/\\\\\\\\/g, '\\\\');\n      if (/^\".*\"$/.test(fileName)) {\n        fileName = fileName.substr(1, fileName.length - 2);\n      }\n      index[keyPrefix + 'FileName'] = fileName;\n      index[keyPrefix + 'Header'] = (data[1] || '').trim();\n      i++;\n    }\n  } // Parses a hunk\n  // This assumes that we are at the start of a hunk.\n\n  function parseHunk() {\n    var chunkHeaderIndex = i,\n      chunkHeaderLine = diffstr[i++],\n      chunkHeader = chunkHeaderLine.split(/@@ -(\\d+)(?:,(\\d+))? \\+(\\d+)(?:,(\\d+))? @@/);\n    var hunk = {\n      oldStart: +chunkHeader[1],\n      oldLines: typeof chunkHeader[2] === 'undefined' ? 1 : +chunkHeader[2],\n      newStart: +chunkHeader[3],\n      newLines: typeof chunkHeader[4] === 'undefined' ? 1 : +chunkHeader[4],\n      lines: [],\n      linedelimiters: []\n    }; // Unified Diff Format quirk: If the chunk size is 0,\n    // the first number is one lower than one would expect.\n    // https://www.artima.com/weblogs/viewpost.jsp?thread=164293\n\n    if (hunk.oldLines === 0) {\n      hunk.oldStart += 1;\n    }\n    if (hunk.newLines === 0) {\n      hunk.newStart += 1;\n    }\n    var addCount = 0,\n      removeCount = 0;\n    for (; i < diffstr.length; i++) {\n      // Lines starting with '---' could be mistaken for the \"remove line\" operation\n      // But they could be the header for the next file. Therefore prune such cases out.\n      if (diffstr[i].indexOf('--- ') === 0 && i + 2 < diffstr.length && diffstr[i + 1].indexOf('+++ ') === 0 && diffstr[i + 2].indexOf('@@') === 0) {\n        break;\n      }\n      var operation = diffstr[i].length == 0 && i != diffstr.length - 1 ? ' ' : diffstr[i][0];\n      if (operation === '+' || operation === '-' || operation === ' ' || operation === '\\\\') {\n        hunk.lines.push(diffstr[i]);\n        hunk.linedelimiters.push(delimiters[i] || '\\n');\n        if (operation === '+') {\n          addCount++;\n        } else if (operation === '-') {\n          removeCount++;\n        } else if (operation === ' ') {\n          addCount++;\n          removeCount++;\n        }\n      } else {\n        break;\n      }\n    } // Handle the empty block count case\n\n    if (!addCount && hunk.newLines === 1) {\n      hunk.newLines = 0;\n    }\n    if (!removeCount && hunk.oldLines === 1) {\n      hunk.oldLines = 0;\n    } // Perform optional sanity checking\n\n    if (options.strict) {\n      if (addCount !== hunk.newLines) {\n        throw new Error('Added line count did not match for hunk at line ' + (chunkHeaderIndex + 1));\n      }\n      if (removeCount !== hunk.oldLines) {\n        throw new Error('Removed line count did not match for hunk at line ' + (chunkHeaderIndex + 1));\n      }\n    }\n    return hunk;\n  }\n  while (i < diffstr.length) {\n    parseIndex();\n  }\n  return list;\n}\n\n// Iterator that traverses in the range of [min, max], stepping\n// by distance from a given start position. I.e. for [0, 4], with\n// start of 2, this will iterate 2, 3, 1, 4, 0.\nfunction distanceIterator(start, minLine, maxLine) {\n  var wantForward = true,\n    backwardExhausted = false,\n    forwardExhausted = false,\n    localOffset = 1;\n  return function iterator() {\n    if (wantForward && !forwardExhausted) {\n      if (backwardExhausted) {\n        localOffset++;\n      } else {\n        wantForward = false;\n      } // Check if trying to fit beyond text length, and if not, check it fits\n      // after offset location (or desired location on first iteration)\n\n      if (start + localOffset <= maxLine) {\n        return localOffset;\n      }\n      forwardExhausted = true;\n    }\n    if (!backwardExhausted) {\n      if (!forwardExhausted) {\n        wantForward = true;\n      } // Check if trying to fit before text beginning, and if not, check it fits\n      // before offset location\n\n      if (minLine <= start - localOffset) {\n        return -localOffset++;\n      }\n      backwardExhausted = true;\n      return iterator();\n    } // We tried to fit hunk before text beginning and beyond text length, then\n    // hunk can't fit on the text. Return undefined\n  };\n}\n\nfunction applyPatch(source, uniDiff) {\n  var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  if (typeof uniDiff === 'string') {\n    uniDiff = parsePatch(uniDiff);\n  }\n  if (Array.isArray(uniDiff)) {\n    if (uniDiff.length > 1) {\n      throw new Error('applyPatch only works with a single input.');\n    }\n    uniDiff = uniDiff[0];\n  } // Apply the diff to the input\n\n  var lines = source.split(/\\r\\n|[\\n\\v\\f\\r\\x85]/),\n    delimiters = source.match(/\\r\\n|[\\n\\v\\f\\r\\x85]/g) || [],\n    hunks = uniDiff.hunks,\n    compareLine = options.compareLine || function (lineNumber, line, operation, patchContent) {\n      return line === patchContent;\n    },\n    errorCount = 0,\n    fuzzFactor = options.fuzzFactor || 0,\n    minLine = 0,\n    offset = 0,\n    removeEOFNL,\n    addEOFNL;\n  /**\n   * Checks if the hunk exactly fits on the provided location\n   */\n\n  function hunkFits(hunk, toPos) {\n    for (var j = 0; j < hunk.lines.length; j++) {\n      var line = hunk.lines[j],\n        operation = line.length > 0 ? line[0] : ' ',\n        content = line.length > 0 ? line.substr(1) : line;\n      if (operation === ' ' || operation === '-') {\n        // Context sanity check\n        if (!compareLine(toPos + 1, lines[toPos], operation, content)) {\n          errorCount++;\n          if (errorCount > fuzzFactor) {\n            return false;\n          }\n        }\n        toPos++;\n      }\n    }\n    return true;\n  } // Search best fit offsets for each hunk based on the previous ones\n\n  for (var i = 0; i < hunks.length; i++) {\n    var hunk = hunks[i],\n      maxLine = lines.length - hunk.oldLines,\n      localOffset = 0,\n      toPos = offset + hunk.oldStart - 1;\n    var iterator = distanceIterator(toPos, minLine, maxLine);\n    for (; localOffset !== undefined; localOffset = iterator()) {\n      if (hunkFits(hunk, toPos + localOffset)) {\n        hunk.offset = offset += localOffset;\n        break;\n      }\n    }\n    if (localOffset === undefined) {\n      return false;\n    } // Set lower text limit to end of the current hunk, so next ones don't try\n    // to fit over already patched text\n\n    minLine = hunk.offset + hunk.oldStart + hunk.oldLines;\n  } // Apply patch hunks\n\n  var diffOffset = 0;\n  for (var _i = 0; _i < hunks.length; _i++) {\n    var _hunk = hunks[_i],\n      _toPos = _hunk.oldStart + _hunk.offset + diffOffset - 1;\n    diffOffset += _hunk.newLines - _hunk.oldLines;\n    for (var j = 0; j < _hunk.lines.length; j++) {\n      var line = _hunk.lines[j],\n        operation = line.length > 0 ? line[0] : ' ',\n        content = line.length > 0 ? line.substr(1) : line,\n        delimiter = _hunk.linedelimiters[j];\n      if (operation === ' ') {\n        _toPos++;\n      } else if (operation === '-') {\n        lines.splice(_toPos, 1);\n        delimiters.splice(_toPos, 1);\n        /* istanbul ignore else */\n      } else if (operation === '+') {\n        lines.splice(_toPos, 0, content);\n        delimiters.splice(_toPos, 0, delimiter);\n        _toPos++;\n      } else if (operation === '\\\\') {\n        var previousOperation = _hunk.lines[j - 1] ? _hunk.lines[j - 1][0] : null;\n        if (previousOperation === '+') {\n          removeEOFNL = true;\n        } else if (previousOperation === '-') {\n          addEOFNL = true;\n        }\n      }\n    }\n  } // Handle EOFNL insertion/removal\n\n  if (removeEOFNL) {\n    while (!lines[lines.length - 1]) {\n      lines.pop();\n      delimiters.pop();\n    }\n  } else if (addEOFNL) {\n    lines.push('');\n    delimiters.push('\\n');\n  }\n  for (var _k = 0; _k < lines.length - 1; _k++) {\n    lines[_k] = lines[_k] + delimiters[_k];\n  }\n  return lines.join('');\n} // Wrapper that supports multiple file patches via callbacks.\n\nfunction applyPatches(uniDiff, options) {\n  if (typeof uniDiff === 'string') {\n    uniDiff = parsePatch(uniDiff);\n  }\n  var currentIndex = 0;\n  function processIndex() {\n    var index = uniDiff[currentIndex++];\n    if (!index) {\n      return options.complete();\n    }\n    options.loadFile(index, function (err, data) {\n      if (err) {\n        return options.complete(err);\n      }\n      var updatedContent = applyPatch(data, index, options);\n      options.patched(index, updatedContent, function (err) {\n        if (err) {\n          return options.complete(err);\n        }\n        processIndex();\n      });\n    });\n  }\n  processIndex();\n}\nfunction structuredPatch(oldFileName, newFileName, oldStr, newStr, oldHeader, newHeader, options) {\n  if (!options) {\n    options = {};\n  }\n  if (typeof options.context === 'undefined') {\n    options.context = 4;\n  }\n  var diff = diffLines(oldStr, newStr, options);\n  if (!diff) {\n    return;\n  }\n  diff.push({\n    value: '',\n    lines: []\n  }); // Append an empty value to make cleanup easier\n\n  function contextLines(lines) {\n    return lines.map(function (entry) {\n      return ' ' + entry;\n    });\n  }\n  var hunks = [];\n  var oldRangeStart = 0,\n    newRangeStart = 0,\n    curRange = [],\n    oldLine = 1,\n    newLine = 1;\n  var _loop = function _loop(i) {\n    var current = diff[i],\n      lines = current.lines || current.value.replace(/\\n$/, '').split('\\n');\n    current.lines = lines;\n    if (current.added || current.removed) {\n      var _curRange;\n\n      // If we have previous context, start with that\n      if (!oldRangeStart) {\n        var prev = diff[i - 1];\n        oldRangeStart = oldLine;\n        newRangeStart = newLine;\n        if (prev) {\n          curRange = options.context > 0 ? contextLines(prev.lines.slice(-options.context)) : [];\n          oldRangeStart -= curRange.length;\n          newRangeStart -= curRange.length;\n        }\n      } // Output our changes\n\n      (_curRange = curRange).push.apply(_curRange, _toConsumableArray(lines.map(function (entry) {\n        return (current.added ? '+' : '-') + entry;\n      }))); // Track the updated file position\n\n      if (current.added) {\n        newLine += lines.length;\n      } else {\n        oldLine += lines.length;\n      }\n    } else {\n      // Identical context lines. Track line changes\n      if (oldRangeStart) {\n        // Close out any changes that have been output (or join overlapping)\n        if (lines.length <= options.context * 2 && i < diff.length - 2) {\n          var _curRange2;\n\n          // Overlapping\n          (_curRange2 = curRange).push.apply(_curRange2, _toConsumableArray(contextLines(lines)));\n        } else {\n          var _curRange3;\n\n          // end the range and output\n          var contextSize = Math.min(lines.length, options.context);\n          (_curRange3 = curRange).push.apply(_curRange3, _toConsumableArray(contextLines(lines.slice(0, contextSize))));\n          var hunk = {\n            oldStart: oldRangeStart,\n            oldLines: oldLine - oldRangeStart + contextSize,\n            newStart: newRangeStart,\n            newLines: newLine - newRangeStart + contextSize,\n            lines: curRange\n          };\n          if (i >= diff.length - 2 && lines.length <= options.context) {\n            // EOF is inside this hunk\n            var oldEOFNewline = /\\n$/.test(oldStr);\n            var newEOFNewline = /\\n$/.test(newStr);\n            var noNlBeforeAdds = lines.length == 0 && curRange.length > hunk.oldLines;\n            if (!oldEOFNewline && noNlBeforeAdds && oldStr.length > 0) {\n              // special case: old has no eol and no trailing context; no-nl can end up before adds\n              // however, if the old file is empty, do not output the no-nl line\n              curRange.splice(hunk.oldLines, 0, '\\\\ No newline at end of file');\n            }\n            if (!oldEOFNewline && !noNlBeforeAdds || !newEOFNewline) {\n              curRange.push('\\\\ No newline at end of file');\n            }\n          }\n          hunks.push(hunk);\n          oldRangeStart = 0;\n          newRangeStart = 0;\n          curRange = [];\n        }\n      }\n      oldLine += lines.length;\n      newLine += lines.length;\n    }\n  };\n  for (var i = 0; i < diff.length; i++) {\n    _loop(i);\n  }\n  return {\n    oldFileName: oldFileName,\n    newFileName: newFileName,\n    oldHeader: oldHeader,\n    newHeader: newHeader,\n    hunks: hunks\n  };\n}\nfunction formatPatch(diff) {\n  var ret = [];\n  if (diff.oldFileName == diff.newFileName) {\n    ret.push('Index: ' + diff.oldFileName);\n  }\n  ret.push('===================================================================');\n  ret.push('--- ' + diff.oldFileName + (typeof diff.oldHeader === 'undefined' ? '' : '\\t' + diff.oldHeader));\n  ret.push('+++ ' + diff.newFileName + (typeof diff.newHeader === 'undefined' ? '' : '\\t' + diff.newHeader));\n  for (var i = 0; i < diff.hunks.length; i++) {\n    var hunk = diff.hunks[i]; // Unified Diff Format quirk: If the chunk size is 0,\n    // the first number is one lower than one would expect.\n    // https://www.artima.com/weblogs/viewpost.jsp?thread=164293\n\n    if (hunk.oldLines === 0) {\n      hunk.oldStart -= 1;\n    }\n    if (hunk.newLines === 0) {\n      hunk.newStart -= 1;\n    }\n    ret.push('@@ -' + hunk.oldStart + ',' + hunk.oldLines + ' +' + hunk.newStart + ',' + hunk.newLines + ' @@');\n    ret.push.apply(ret, hunk.lines);\n  }\n  return ret.join('\\n') + '\\n';\n}\nfunction createTwoFilesPatch(oldFileName, newFileName, oldStr, newStr, oldHeader, newHeader, options) {\n  return formatPatch(structuredPatch(oldFileName, newFileName, oldStr, newStr, oldHeader, newHeader, options));\n}\nfunction createPatch(fileName, oldStr, newStr, oldHeader, newHeader, options) {\n  return createTwoFilesPatch(fileName, fileName, oldStr, newStr, oldHeader, newHeader, options);\n}\nfunction arrayEqual(a, b) {\n  if (a.length !== b.length) {\n    return false;\n  }\n  return arrayStartsWith(a, b);\n}\nfunction arrayStartsWith(array, start) {\n  if (start.length > array.length) {\n    return false;\n  }\n  for (var i = 0; i < start.length; i++) {\n    if (start[i] !== array[i]) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction calcLineCount(hunk) {\n  var _calcOldNewLineCount = calcOldNewLineCount(hunk.lines),\n    oldLines = _calcOldNewLineCount.oldLines,\n    newLines = _calcOldNewLineCount.newLines;\n  if (oldLines !== undefined) {\n    hunk.oldLines = oldLines;\n  } else {\n    delete hunk.oldLines;\n  }\n  if (newLines !== undefined) {\n    hunk.newLines = newLines;\n  } else {\n    delete hunk.newLines;\n  }\n}\nfunction merge(mine, theirs, base) {\n  mine = loadPatch(mine, base);\n  theirs = loadPatch(theirs, base);\n  var ret = {}; // For index we just let it pass through as it doesn't have any necessary meaning.\n  // Leaving sanity checks on this to the API consumer that may know more about the\n  // meaning in their own context.\n\n  if (mine.index || theirs.index) {\n    ret.index = mine.index || theirs.index;\n  }\n  if (mine.newFileName || theirs.newFileName) {\n    if (!fileNameChanged(mine)) {\n      // No header or no change in ours, use theirs (and ours if theirs does not exist)\n      ret.oldFileName = theirs.oldFileName || mine.oldFileName;\n      ret.newFileName = theirs.newFileName || mine.newFileName;\n      ret.oldHeader = theirs.oldHeader || mine.oldHeader;\n      ret.newHeader = theirs.newHeader || mine.newHeader;\n    } else if (!fileNameChanged(theirs)) {\n      // No header or no change in theirs, use ours\n      ret.oldFileName = mine.oldFileName;\n      ret.newFileName = mine.newFileName;\n      ret.oldHeader = mine.oldHeader;\n      ret.newHeader = mine.newHeader;\n    } else {\n      // Both changed... figure it out\n      ret.oldFileName = selectField(ret, mine.oldFileName, theirs.oldFileName);\n      ret.newFileName = selectField(ret, mine.newFileName, theirs.newFileName);\n      ret.oldHeader = selectField(ret, mine.oldHeader, theirs.oldHeader);\n      ret.newHeader = selectField(ret, mine.newHeader, theirs.newHeader);\n    }\n  }\n  ret.hunks = [];\n  var mineIndex = 0,\n    theirsIndex = 0,\n    mineOffset = 0,\n    theirsOffset = 0;\n  while (mineIndex < mine.hunks.length || theirsIndex < theirs.hunks.length) {\n    var mineCurrent = mine.hunks[mineIndex] || {\n        oldStart: Infinity\n      },\n      theirsCurrent = theirs.hunks[theirsIndex] || {\n        oldStart: Infinity\n      };\n    if (hunkBefore(mineCurrent, theirsCurrent)) {\n      // This patch does not overlap with any of the others, yay.\n      ret.hunks.push(cloneHunk(mineCurrent, mineOffset));\n      mineIndex++;\n      theirsOffset += mineCurrent.newLines - mineCurrent.oldLines;\n    } else if (hunkBefore(theirsCurrent, mineCurrent)) {\n      // This patch does not overlap with any of the others, yay.\n      ret.hunks.push(cloneHunk(theirsCurrent, theirsOffset));\n      theirsIndex++;\n      mineOffset += theirsCurrent.newLines - theirsCurrent.oldLines;\n    } else {\n      // Overlap, merge as best we can\n      var mergedHunk = {\n        oldStart: Math.min(mineCurrent.oldStart, theirsCurrent.oldStart),\n        oldLines: 0,\n        newStart: Math.min(mineCurrent.newStart + mineOffset, theirsCurrent.oldStart + theirsOffset),\n        newLines: 0,\n        lines: []\n      };\n      mergeLines(mergedHunk, mineCurrent.oldStart, mineCurrent.lines, theirsCurrent.oldStart, theirsCurrent.lines);\n      theirsIndex++;\n      mineIndex++;\n      ret.hunks.push(mergedHunk);\n    }\n  }\n  return ret;\n}\nfunction loadPatch(param, base) {\n  if (typeof param === 'string') {\n    if (/^@@/m.test(param) || /^Index:/m.test(param)) {\n      return parsePatch(param)[0];\n    }\n    if (!base) {\n      throw new Error('Must provide a base reference or pass in a patch');\n    }\n    return structuredPatch(undefined, undefined, base, param);\n  }\n  return param;\n}\nfunction fileNameChanged(patch) {\n  return patch.newFileName && patch.newFileName !== patch.oldFileName;\n}\nfunction selectField(index, mine, theirs) {\n  if (mine === theirs) {\n    return mine;\n  } else {\n    index.conflict = true;\n    return {\n      mine: mine,\n      theirs: theirs\n    };\n  }\n}\nfunction hunkBefore(test, check) {\n  return test.oldStart < check.oldStart && test.oldStart + test.oldLines < check.oldStart;\n}\nfunction cloneHunk(hunk, offset) {\n  return {\n    oldStart: hunk.oldStart,\n    oldLines: hunk.oldLines,\n    newStart: hunk.newStart + offset,\n    newLines: hunk.newLines,\n    lines: hunk.lines\n  };\n}\nfunction mergeLines(hunk, mineOffset, mineLines, theirOffset, theirLines) {\n  // This will generally result in a conflicted hunk, but there are cases where the context\n  // is the only overlap where we can successfully merge the content here.\n  var mine = {\n      offset: mineOffset,\n      lines: mineLines,\n      index: 0\n    },\n    their = {\n      offset: theirOffset,\n      lines: theirLines,\n      index: 0\n    }; // Handle any leading content\n\n  insertLeading(hunk, mine, their);\n  insertLeading(hunk, their, mine); // Now in the overlap content. Scan through and select the best changes from each.\n\n  while (mine.index < mine.lines.length && their.index < their.lines.length) {\n    var mineCurrent = mine.lines[mine.index],\n      theirCurrent = their.lines[their.index];\n    if ((mineCurrent[0] === '-' || mineCurrent[0] === '+') && (theirCurrent[0] === '-' || theirCurrent[0] === '+')) {\n      // Both modified ...\n      mutualChange(hunk, mine, their);\n    } else if (mineCurrent[0] === '+' && theirCurrent[0] === ' ') {\n      var _hunk$lines;\n\n      // Mine inserted\n      (_hunk$lines = hunk.lines).push.apply(_hunk$lines, _toConsumableArray(collectChange(mine)));\n    } else if (theirCurrent[0] === '+' && mineCurrent[0] === ' ') {\n      var _hunk$lines2;\n\n      // Theirs inserted\n      (_hunk$lines2 = hunk.lines).push.apply(_hunk$lines2, _toConsumableArray(collectChange(their)));\n    } else if (mineCurrent[0] === '-' && theirCurrent[0] === ' ') {\n      // Mine removed or edited\n      removal(hunk, mine, their);\n    } else if (theirCurrent[0] === '-' && mineCurrent[0] === ' ') {\n      // Their removed or edited\n      removal(hunk, their, mine, true);\n    } else if (mineCurrent === theirCurrent) {\n      // Context identity\n      hunk.lines.push(mineCurrent);\n      mine.index++;\n      their.index++;\n    } else {\n      // Context mismatch\n      conflict(hunk, collectChange(mine), collectChange(their));\n    }\n  } // Now push anything that may be remaining\n\n  insertTrailing(hunk, mine);\n  insertTrailing(hunk, their);\n  calcLineCount(hunk);\n}\nfunction mutualChange(hunk, mine, their) {\n  var myChanges = collectChange(mine),\n    theirChanges = collectChange(their);\n  if (allRemoves(myChanges) && allRemoves(theirChanges)) {\n    // Special case for remove changes that are supersets of one another\n    if (arrayStartsWith(myChanges, theirChanges) && skipRemoveSuperset(their, myChanges, myChanges.length - theirChanges.length)) {\n      var _hunk$lines3;\n      (_hunk$lines3 = hunk.lines).push.apply(_hunk$lines3, _toConsumableArray(myChanges));\n      return;\n    } else if (arrayStartsWith(theirChanges, myChanges) && skipRemoveSuperset(mine, theirChanges, theirChanges.length - myChanges.length)) {\n      var _hunk$lines4;\n      (_hunk$lines4 = hunk.lines).push.apply(_hunk$lines4, _toConsumableArray(theirChanges));\n      return;\n    }\n  } else if (arrayEqual(myChanges, theirChanges)) {\n    var _hunk$lines5;\n    (_hunk$lines5 = hunk.lines).push.apply(_hunk$lines5, _toConsumableArray(myChanges));\n    return;\n  }\n  conflict(hunk, myChanges, theirChanges);\n}\nfunction removal(hunk, mine, their, swap) {\n  var myChanges = collectChange(mine),\n    theirChanges = collectContext(their, myChanges);\n  if (theirChanges.merged) {\n    var _hunk$lines6;\n    (_hunk$lines6 = hunk.lines).push.apply(_hunk$lines6, _toConsumableArray(theirChanges.merged));\n  } else {\n    conflict(hunk, swap ? theirChanges : myChanges, swap ? myChanges : theirChanges);\n  }\n}\nfunction conflict(hunk, mine, their) {\n  hunk.conflict = true;\n  hunk.lines.push({\n    conflict: true,\n    mine: mine,\n    theirs: their\n  });\n}\nfunction insertLeading(hunk, insert, their) {\n  while (insert.offset < their.offset && insert.index < insert.lines.length) {\n    var line = insert.lines[insert.index++];\n    hunk.lines.push(line);\n    insert.offset++;\n  }\n}\nfunction insertTrailing(hunk, insert) {\n  while (insert.index < insert.lines.length) {\n    var line = insert.lines[insert.index++];\n    hunk.lines.push(line);\n  }\n}\nfunction collectChange(state) {\n  var ret = [],\n    operation = state.lines[state.index][0];\n  while (state.index < state.lines.length) {\n    var line = state.lines[state.index]; // Group additions that are immediately after subtractions and treat them as one \"atomic\" modify change.\n\n    if (operation === '-' && line[0] === '+') {\n      operation = '+';\n    }\n    if (operation === line[0]) {\n      ret.push(line);\n      state.index++;\n    } else {\n      break;\n    }\n  }\n  return ret;\n}\nfunction collectContext(state, matchChanges) {\n  var changes = [],\n    merged = [],\n    matchIndex = 0,\n    contextChanges = false,\n    conflicted = false;\n  while (matchIndex < matchChanges.length && state.index < state.lines.length) {\n    var change = state.lines[state.index],\n      match = matchChanges[matchIndex]; // Once we've hit our add, then we are done\n\n    if (match[0] === '+') {\n      break;\n    }\n    contextChanges = contextChanges || change[0] !== ' ';\n    merged.push(match);\n    matchIndex++; // Consume any additions in the other block as a conflict to attempt\n    // to pull in the remaining context after this\n\n    if (change[0] === '+') {\n      conflicted = true;\n      while (change[0] === '+') {\n        changes.push(change);\n        change = state.lines[++state.index];\n      }\n    }\n    if (match.substr(1) === change.substr(1)) {\n      changes.push(change);\n      state.index++;\n    } else {\n      conflicted = true;\n    }\n  }\n  if ((matchChanges[matchIndex] || '')[0] === '+' && contextChanges) {\n    conflicted = true;\n  }\n  if (conflicted) {\n    return changes;\n  }\n  while (matchIndex < matchChanges.length) {\n    merged.push(matchChanges[matchIndex++]);\n  }\n  return {\n    merged: merged,\n    changes: changes\n  };\n}\nfunction allRemoves(changes) {\n  return changes.reduce(function (prev, change) {\n    return prev && change[0] === '-';\n  }, true);\n}\nfunction skipRemoveSuperset(state, removeChanges, delta) {\n  for (var i = 0; i < delta; i++) {\n    var changeContent = removeChanges[removeChanges.length - delta + i].substr(1);\n    if (state.lines[state.index + i] !== ' ' + changeContent) {\n      return false;\n    }\n  }\n  state.index += delta;\n  return true;\n}\nfunction calcOldNewLineCount(lines) {\n  var oldLines = 0;\n  var newLines = 0;\n  lines.forEach(function (line) {\n    if (typeof line !== 'string') {\n      var myCount = calcOldNewLineCount(line.mine);\n      var theirCount = calcOldNewLineCount(line.theirs);\n      if (oldLines !== undefined) {\n        if (myCount.oldLines === theirCount.oldLines) {\n          oldLines += myCount.oldLines;\n        } else {\n          oldLines = undefined;\n        }\n      }\n      if (newLines !== undefined) {\n        if (myCount.newLines === theirCount.newLines) {\n          newLines += myCount.newLines;\n        } else {\n          newLines = undefined;\n        }\n      }\n    } else {\n      if (newLines !== undefined && (line[0] === '+' || line[0] === ' ')) {\n        newLines++;\n      }\n      if (oldLines !== undefined && (line[0] === '-' || line[0] === ' ')) {\n        oldLines++;\n      }\n    }\n  });\n  return {\n    oldLines: oldLines,\n    newLines: newLines\n  };\n}\n\n// See: http://code.google.com/p/google-diff-match-patch/wiki/API\nfunction convertChangesToDMP(changes) {\n  var ret = [],\n    change,\n    operation;\n  for (var i = 0; i < changes.length; i++) {\n    change = changes[i];\n    if (change.added) {\n      operation = 1;\n    } else if (change.removed) {\n      operation = -1;\n    } else {\n      operation = 0;\n    }\n    ret.push([operation, change.value]);\n  }\n  return ret;\n}\nfunction convertChangesToXML(changes) {\n  var ret = [];\n  for (var i = 0; i < changes.length; i++) {\n    var change = changes[i];\n    if (change.added) {\n      ret.push('<ins>');\n    } else if (change.removed) {\n      ret.push('<del>');\n    }\n    ret.push(escapeHTML(change.value));\n    if (change.added) {\n      ret.push('</ins>');\n    } else if (change.removed) {\n      ret.push('</del>');\n    }\n  }\n  return ret.join('');\n}\nfunction escapeHTML(s) {\n  var n = s;\n  n = n.replace(/&/g, '&amp;');\n  n = n.replace(/</g, '&lt;');\n  n = n.replace(/>/g, '&gt;');\n  n = n.replace(/\"/g, '&quot;');\n  return n;\n}\nexport { Diff, applyPatch, applyPatches, canonicalize, convertChangesToDMP, convertChangesToXML, createPatch, createTwoFilesPatch, diffArrays, diffChars, diffCss, diffJson, diffLines, diffSentences, diffTrimmedLines, diffWords, diffWordsWithSpace, merge, parsePatch, structuredPatch };", "map": {"version": 3, "names": ["Diff", "prototype", "diff", "oldString", "newString", "options", "arguments", "length", "undefined", "callback", "self", "done", "value", "setTimeout", "castInput", "removeEmpty", "tokenize", "newLen", "old<PERSON>en", "edit<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "Math", "min", "bestPath", "newPos", "components", "oldPos", "extractCommon", "join", "count", "execEditLength", "diagonalPath", "basePath", "addPath", "remove<PERSON>ath", "_old<PERSON><PERSON>", "canAdd", "canRemove", "<PERSON><PERSON><PERSON>", "pushComponent", "buildValues", "useLongestToken", "exec", "ret", "added", "removed", "last", "push", "commonCount", "equals", "left", "right", "comparator", "ignoreCase", "toLowerCase", "array", "i", "split", "chars", "componentPos", "componentLen", "component", "slice", "map", "oldValue", "tmp", "lastComponent", "pop", "path", "characterDiff", "diffChars", "oldStr", "newStr", "generateOptions", "defaults", "name", "hasOwnProperty", "extendedWordChars", "reWhitespace", "wordDiff", "ignoreWhitespace", "test", "tokens", "splice", "diffWords", "diffWordsWithSpace", "lineDiff", "retLines", "linesAndNewlines", "line", "newlineIsToken", "trim", "diffLines", "diffTrimmedLines", "sentenceDiff", "diffSentences", "cssDiff", "diffCss", "_typeof", "obj", "Symbol", "iterator", "constructor", "_toConsumableArray", "arr", "_arrayWithoutHoles", "_iterableToArray", "_unsupportedIterableToArray", "_nonIterableSpread", "Array", "isArray", "_arrayLikeToArray", "iter", "Object", "from", "o", "minLen", "n", "toString", "call", "len", "arr2", "TypeError", "objectPrototypeToString", "jsonDiff", "_this$options", "undefinedReplacement", "_this$options$stringi", "stringifyReplacer", "k", "v", "JSON", "stringify", "canonicalize", "replace", "diff<PERSON><PERSON>", "oldObj", "newObj", "stack", "replacementStack", "replacer", "key", "canonicalizedObj", "toJSON", "sortedKeys", "_key", "sort", "arrayDiff", "diffArrays", "oldArr", "newArr", "parsePatch", "uniDiff", "diffstr", "delimiters", "match", "list", "parseIndex", "index", "header", "parseFileHeader", "hunks", "_line", "parseHunk", "strict", "Error", "fileHeader", "keyPrefix", "data", "fileName", "substr", "chunkHeaderIndex", "chunkHeaderLine", "chunkHeader", "hunk", "oldStart", "oldLines", "newStart", "newLines", "lines", "linedelimiters", "addCount", "removeCount", "indexOf", "operation", "distanceIterator", "start", "minLine", "maxLine", "wantForward", "backwardExhausted", "forwardExhausted", "localOffset", "applyPatch", "source", "compareLine", "lineNumber", "patchContent", "errorCount", "fuzzFactor", "offset", "removeEOFNL", "addEOFNL", "hunkFits", "toPos", "j", "content", "diffOffset", "_i", "_hunk", "_toPos", "delimiter", "previousOperation", "_k", "applyPatches", "currentIndex", "processIndex", "complete", "loadFile", "err", "updatedContent", "patched", "structuredPatch", "oldFileName", "newFileName", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "context", "contextLines", "entry", "oldRangeStart", "newRangeStart", "cur<PERSON><PERSON><PERSON>", "oldLine", "newLine", "_loop", "current", "_cur<PERSON><PERSON>e", "prev", "apply", "_curRange2", "_curRange3", "contextSize", "oldEOFNewline", "newEOFNewline", "noNlBeforeAdds", "formatPatch", "createTwoFilesPatch", "createPatch", "arrayEqual", "a", "b", "arrayStartsWith", "calcLineCount", "_calcOldNewLineCount", "calcOldNewLineCount", "merge", "mine", "theirs", "base", "loadPatch", "fileNameChanged", "selectField", "mineIndex", "theirsIndex", "mineOffset", "theirsOffset", "mineCurrent", "Infinity", "theirsCurrent", "hunkBefore", "cloneHunk", "mergedHunk", "mergeLines", "param", "patch", "conflict", "check", "mineLines", "theirOffset", "theirLines", "their", "insertLeading", "theirCurrent", "mutualChange", "_hunk$lines", "collectChange", "_hunk$lines2", "removal", "insertTrailing", "myChanges", "theirChanges", "allRemoves", "skipRemoveSuperset", "_hunk$lines3", "_hunk$lines4", "_hunk$lines5", "swap", "collectContext", "merged", "_hunk$lines6", "insert", "state", "matchChanges", "changes", "matchIndex", "contextChanges", "conflicted", "change", "reduce", "removeChanges", "delta", "changeContent", "for<PERSON>ach", "myCount", "theirCount", "convertChangesToDMP", "convertChangesToXML", "escapeHTML", "s"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/diff/lib/index.mjs"], "sourcesContent": ["function Diff() {}\nDiff.prototype = {\n  diff: function diff(oldString, newString) {\n    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    var callback = options.callback;\n\n    if (typeof options === 'function') {\n      callback = options;\n      options = {};\n    }\n\n    this.options = options;\n    var self = this;\n\n    function done(value) {\n      if (callback) {\n        setTimeout(function () {\n          callback(undefined, value);\n        }, 0);\n        return true;\n      } else {\n        return value;\n      }\n    } // Allow subclasses to massage the input prior to running\n\n\n    oldString = this.castInput(oldString);\n    newString = this.castInput(newString);\n    oldString = this.removeEmpty(this.tokenize(oldString));\n    newString = this.removeEmpty(this.tokenize(newString));\n    var newLen = newString.length,\n        oldLen = oldString.length;\n    var editLength = 1;\n    var maxEditLength = newLen + oldLen;\n\n    if (options.maxEditLength) {\n      maxEditLength = Math.min(maxEditLength, options.maxEditLength);\n    }\n\n    var bestPath = [{\n      newPos: -1,\n      components: []\n    }]; // Seed editLength = 0, i.e. the content starts with the same values\n\n    var oldPos = this.extractCommon(bestPath[0], newString, oldString, 0);\n\n    if (bestPath[0].newPos + 1 >= newLen && oldPos + 1 >= oldLen) {\n      // Identity per the equality and tokenizer\n      return done([{\n        value: this.join(newString),\n        count: newString.length\n      }]);\n    } // Main worker method. checks all permutations of a given edit length for acceptance.\n\n\n    function execEditLength() {\n      for (var diagonalPath = -1 * editLength; diagonalPath <= editLength; diagonalPath += 2) {\n        var basePath = void 0;\n\n        var addPath = bestPath[diagonalPath - 1],\n            removePath = bestPath[diagonalPath + 1],\n            _oldPos = (removePath ? removePath.newPos : 0) - diagonalPath;\n\n        if (addPath) {\n          // No one else is going to attempt to use this value, clear it\n          bestPath[diagonalPath - 1] = undefined;\n        }\n\n        var canAdd = addPath && addPath.newPos + 1 < newLen,\n            canRemove = removePath && 0 <= _oldPos && _oldPos < oldLen;\n\n        if (!canAdd && !canRemove) {\n          // If this path is a terminal then prune\n          bestPath[diagonalPath] = undefined;\n          continue;\n        } // Select the diagonal that we want to branch from. We select the prior\n        // path whose position in the new string is the farthest from the origin\n        // and does not pass the bounds of the diff graph\n\n\n        if (!canAdd || canRemove && addPath.newPos < removePath.newPos) {\n          basePath = clonePath(removePath);\n          self.pushComponent(basePath.components, undefined, true);\n        } else {\n          basePath = addPath; // No need to clone, we've pulled it from the list\n\n          basePath.newPos++;\n          self.pushComponent(basePath.components, true, undefined);\n        }\n\n        _oldPos = self.extractCommon(basePath, newString, oldString, diagonalPath); // If we have hit the end of both strings, then we are done\n\n        if (basePath.newPos + 1 >= newLen && _oldPos + 1 >= oldLen) {\n          return done(buildValues(self, basePath.components, newString, oldString, self.useLongestToken));\n        } else {\n          // Otherwise track this path as a potential candidate and continue.\n          bestPath[diagonalPath] = basePath;\n        }\n      }\n\n      editLength++;\n    } // Performs the length of edit iteration. Is a bit fugly as this has to support the\n    // sync and async mode which is never fun. Loops over execEditLength until a value\n    // is produced, or until the edit length exceeds options.maxEditLength (if given),\n    // in which case it will return undefined.\n\n\n    if (callback) {\n      (function exec() {\n        setTimeout(function () {\n          if (editLength > maxEditLength) {\n            return callback();\n          }\n\n          if (!execEditLength()) {\n            exec();\n          }\n        }, 0);\n      })();\n    } else {\n      while (editLength <= maxEditLength) {\n        var ret = execEditLength();\n\n        if (ret) {\n          return ret;\n        }\n      }\n    }\n  },\n  pushComponent: function pushComponent(components, added, removed) {\n    var last = components[components.length - 1];\n\n    if (last && last.added === added && last.removed === removed) {\n      // We need to clone here as the component clone operation is just\n      // as shallow array clone\n      components[components.length - 1] = {\n        count: last.count + 1,\n        added: added,\n        removed: removed\n      };\n    } else {\n      components.push({\n        count: 1,\n        added: added,\n        removed: removed\n      });\n    }\n  },\n  extractCommon: function extractCommon(basePath, newString, oldString, diagonalPath) {\n    var newLen = newString.length,\n        oldLen = oldString.length,\n        newPos = basePath.newPos,\n        oldPos = newPos - diagonalPath,\n        commonCount = 0;\n\n    while (newPos + 1 < newLen && oldPos + 1 < oldLen && this.equals(newString[newPos + 1], oldString[oldPos + 1])) {\n      newPos++;\n      oldPos++;\n      commonCount++;\n    }\n\n    if (commonCount) {\n      basePath.components.push({\n        count: commonCount\n      });\n    }\n\n    basePath.newPos = newPos;\n    return oldPos;\n  },\n  equals: function equals(left, right) {\n    if (this.options.comparator) {\n      return this.options.comparator(left, right);\n    } else {\n      return left === right || this.options.ignoreCase && left.toLowerCase() === right.toLowerCase();\n    }\n  },\n  removeEmpty: function removeEmpty(array) {\n    var ret = [];\n\n    for (var i = 0; i < array.length; i++) {\n      if (array[i]) {\n        ret.push(array[i]);\n      }\n    }\n\n    return ret;\n  },\n  castInput: function castInput(value) {\n    return value;\n  },\n  tokenize: function tokenize(value) {\n    return value.split('');\n  },\n  join: function join(chars) {\n    return chars.join('');\n  }\n};\n\nfunction buildValues(diff, components, newString, oldString, useLongestToken) {\n  var componentPos = 0,\n      componentLen = components.length,\n      newPos = 0,\n      oldPos = 0;\n\n  for (; componentPos < componentLen; componentPos++) {\n    var component = components[componentPos];\n\n    if (!component.removed) {\n      if (!component.added && useLongestToken) {\n        var value = newString.slice(newPos, newPos + component.count);\n        value = value.map(function (value, i) {\n          var oldValue = oldString[oldPos + i];\n          return oldValue.length > value.length ? oldValue : value;\n        });\n        component.value = diff.join(value);\n      } else {\n        component.value = diff.join(newString.slice(newPos, newPos + component.count));\n      }\n\n      newPos += component.count; // Common case\n\n      if (!component.added) {\n        oldPos += component.count;\n      }\n    } else {\n      component.value = diff.join(oldString.slice(oldPos, oldPos + component.count));\n      oldPos += component.count; // Reverse add and remove so removes are output first to match common convention\n      // The diffing algorithm is tied to add then remove output and this is the simplest\n      // route to get the desired output with minimal overhead.\n\n      if (componentPos && components[componentPos - 1].added) {\n        var tmp = components[componentPos - 1];\n        components[componentPos - 1] = components[componentPos];\n        components[componentPos] = tmp;\n      }\n    }\n  } // Special case handle for when one terminal is ignored (i.e. whitespace).\n  // For this case we merge the terminal into the prior string and drop the change.\n  // This is only available for string mode.\n\n\n  var lastComponent = components[componentLen - 1];\n\n  if (componentLen > 1 && typeof lastComponent.value === 'string' && (lastComponent.added || lastComponent.removed) && diff.equals('', lastComponent.value)) {\n    components[componentLen - 2].value += lastComponent.value;\n    components.pop();\n  }\n\n  return components;\n}\n\nfunction clonePath(path) {\n  return {\n    newPos: path.newPos,\n    components: path.components.slice(0)\n  };\n}\n\nvar characterDiff = new Diff();\nfunction diffChars(oldStr, newStr, options) {\n  return characterDiff.diff(oldStr, newStr, options);\n}\n\nfunction generateOptions(options, defaults) {\n  if (typeof options === 'function') {\n    defaults.callback = options;\n  } else if (options) {\n    for (var name in options) {\n      /* istanbul ignore else */\n      if (options.hasOwnProperty(name)) {\n        defaults[name] = options[name];\n      }\n    }\n  }\n\n  return defaults;\n}\n\n//\n// Ranges and exceptions:\n// Latin-1 Supplement, 0080–00FF\n//  - U+00D7  × Multiplication sign\n//  - U+00F7  ÷ Division sign\n// Latin Extended-A, 0100–017F\n// Latin Extended-B, 0180–024F\n// IPA Extensions, 0250–02AF\n// Spacing Modifier Letters, 02B0–02FF\n//  - U+02C7  ˇ &#711;  Caron\n//  - U+02D8  ˘ &#728;  Breve\n//  - U+02D9  ˙ &#729;  Dot Above\n//  - U+02DA  ˚ &#730;  Ring Above\n//  - U+02DB  ˛ &#731;  Ogonek\n//  - U+02DC  ˜ &#732;  Small Tilde\n//  - U+02DD  ˝ &#733;  Double Acute Accent\n// Latin Extended Additional, 1E00–1EFF\n\nvar extendedWordChars = /^[A-Za-z\\xC0-\\u02C6\\u02C8-\\u02D7\\u02DE-\\u02FF\\u1E00-\\u1EFF]+$/;\nvar reWhitespace = /\\S/;\nvar wordDiff = new Diff();\n\nwordDiff.equals = function (left, right) {\n  if (this.options.ignoreCase) {\n    left = left.toLowerCase();\n    right = right.toLowerCase();\n  }\n\n  return left === right || this.options.ignoreWhitespace && !reWhitespace.test(left) && !reWhitespace.test(right);\n};\n\nwordDiff.tokenize = function (value) {\n  // All whitespace symbols except newline group into one token, each newline - in separate token\n  var tokens = value.split(/([^\\S\\r\\n]+|[()[\\]{}'\"\\r\\n]|\\b)/); // Join the boundary splits that we do not consider to be boundaries. This is primarily the extended Latin character set.\n\n  for (var i = 0; i < tokens.length - 1; i++) {\n    // If we have an empty string in the next field and we have only word chars before and after, merge\n    if (!tokens[i + 1] && tokens[i + 2] && extendedWordChars.test(tokens[i]) && extendedWordChars.test(tokens[i + 2])) {\n      tokens[i] += tokens[i + 2];\n      tokens.splice(i + 1, 2);\n      i--;\n    }\n  }\n\n  return tokens;\n};\n\nfunction diffWords(oldStr, newStr, options) {\n  options = generateOptions(options, {\n    ignoreWhitespace: true\n  });\n  return wordDiff.diff(oldStr, newStr, options);\n}\nfunction diffWordsWithSpace(oldStr, newStr, options) {\n  return wordDiff.diff(oldStr, newStr, options);\n}\n\nvar lineDiff = new Diff();\n\nlineDiff.tokenize = function (value) {\n  var retLines = [],\n      linesAndNewlines = value.split(/(\\n|\\r\\n)/); // Ignore the final empty token that occurs if the string ends with a new line\n\n  if (!linesAndNewlines[linesAndNewlines.length - 1]) {\n    linesAndNewlines.pop();\n  } // Merge the content and line separators into single tokens\n\n\n  for (var i = 0; i < linesAndNewlines.length; i++) {\n    var line = linesAndNewlines[i];\n\n    if (i % 2 && !this.options.newlineIsToken) {\n      retLines[retLines.length - 1] += line;\n    } else {\n      if (this.options.ignoreWhitespace) {\n        line = line.trim();\n      }\n\n      retLines.push(line);\n    }\n  }\n\n  return retLines;\n};\n\nfunction diffLines(oldStr, newStr, callback) {\n  return lineDiff.diff(oldStr, newStr, callback);\n}\nfunction diffTrimmedLines(oldStr, newStr, callback) {\n  var options = generateOptions(callback, {\n    ignoreWhitespace: true\n  });\n  return lineDiff.diff(oldStr, newStr, options);\n}\n\nvar sentenceDiff = new Diff();\n\nsentenceDiff.tokenize = function (value) {\n  return value.split(/(\\S.+?[.!?])(?=\\s+|$)/);\n};\n\nfunction diffSentences(oldStr, newStr, callback) {\n  return sentenceDiff.diff(oldStr, newStr, callback);\n}\n\nvar cssDiff = new Diff();\n\ncssDiff.tokenize = function (value) {\n  return value.split(/([{}:;,]|\\s+)/);\n};\n\nfunction diffCss(oldStr, newStr, callback) {\n  return cssDiff.diff(oldStr, newStr, callback);\n}\n\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n    _typeof = function (obj) {\n      return typeof obj;\n    };\n  } else {\n    _typeof = function (obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n  }\n\n  return _typeof(obj);\n}\n\nfunction _toConsumableArray(arr) {\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\n\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\n\nfunction _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && Symbol.iterator in Object(iter)) return Array.from(iter);\n}\n\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\n\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n\n  return arr2;\n}\n\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nvar objectPrototypeToString = Object.prototype.toString;\nvar jsonDiff = new Diff(); // Discriminate between two lines of pretty-printed, serialized JSON where one of them has a\n// dangling comma and the other doesn't. Turns out including the dangling comma yields the nicest output:\n\njsonDiff.useLongestToken = true;\njsonDiff.tokenize = lineDiff.tokenize;\n\njsonDiff.castInput = function (value) {\n  var _this$options = this.options,\n      undefinedReplacement = _this$options.undefinedReplacement,\n      _this$options$stringi = _this$options.stringifyReplacer,\n      stringifyReplacer = _this$options$stringi === void 0 ? function (k, v) {\n    return typeof v === 'undefined' ? undefinedReplacement : v;\n  } : _this$options$stringi;\n  return typeof value === 'string' ? value : JSON.stringify(canonicalize(value, null, null, stringifyReplacer), stringifyReplacer, '  ');\n};\n\njsonDiff.equals = function (left, right) {\n  return Diff.prototype.equals.call(jsonDiff, left.replace(/,([\\r\\n])/g, '$1'), right.replace(/,([\\r\\n])/g, '$1'));\n};\n\nfunction diffJson(oldObj, newObj, options) {\n  return jsonDiff.diff(oldObj, newObj, options);\n} // This function handles the presence of circular references by bailing out when encountering an\n// object that is already on the \"stack\" of items being processed. Accepts an optional replacer\n\nfunction canonicalize(obj, stack, replacementStack, replacer, key) {\n  stack = stack || [];\n  replacementStack = replacementStack || [];\n\n  if (replacer) {\n    obj = replacer(key, obj);\n  }\n\n  var i;\n\n  for (i = 0; i < stack.length; i += 1) {\n    if (stack[i] === obj) {\n      return replacementStack[i];\n    }\n  }\n\n  var canonicalizedObj;\n\n  if ('[object Array]' === objectPrototypeToString.call(obj)) {\n    stack.push(obj);\n    canonicalizedObj = new Array(obj.length);\n    replacementStack.push(canonicalizedObj);\n\n    for (i = 0; i < obj.length; i += 1) {\n      canonicalizedObj[i] = canonicalize(obj[i], stack, replacementStack, replacer, key);\n    }\n\n    stack.pop();\n    replacementStack.pop();\n    return canonicalizedObj;\n  }\n\n  if (obj && obj.toJSON) {\n    obj = obj.toJSON();\n  }\n\n  if (_typeof(obj) === 'object' && obj !== null) {\n    stack.push(obj);\n    canonicalizedObj = {};\n    replacementStack.push(canonicalizedObj);\n\n    var sortedKeys = [],\n        _key;\n\n    for (_key in obj) {\n      /* istanbul ignore else */\n      if (obj.hasOwnProperty(_key)) {\n        sortedKeys.push(_key);\n      }\n    }\n\n    sortedKeys.sort();\n\n    for (i = 0; i < sortedKeys.length; i += 1) {\n      _key = sortedKeys[i];\n      canonicalizedObj[_key] = canonicalize(obj[_key], stack, replacementStack, replacer, _key);\n    }\n\n    stack.pop();\n    replacementStack.pop();\n  } else {\n    canonicalizedObj = obj;\n  }\n\n  return canonicalizedObj;\n}\n\nvar arrayDiff = new Diff();\n\narrayDiff.tokenize = function (value) {\n  return value.slice();\n};\n\narrayDiff.join = arrayDiff.removeEmpty = function (value) {\n  return value;\n};\n\nfunction diffArrays(oldArr, newArr, callback) {\n  return arrayDiff.diff(oldArr, newArr, callback);\n}\n\nfunction parsePatch(uniDiff) {\n  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var diffstr = uniDiff.split(/\\r\\n|[\\n\\v\\f\\r\\x85]/),\n      delimiters = uniDiff.match(/\\r\\n|[\\n\\v\\f\\r\\x85]/g) || [],\n      list = [],\n      i = 0;\n\n  function parseIndex() {\n    var index = {};\n    list.push(index); // Parse diff metadata\n\n    while (i < diffstr.length) {\n      var line = diffstr[i]; // File header found, end parsing diff metadata\n\n      if (/^(\\-\\-\\-|\\+\\+\\+|@@)\\s/.test(line)) {\n        break;\n      } // Diff index\n\n\n      var header = /^(?:Index:|diff(?: -r \\w+)+)\\s+(.+?)\\s*$/.exec(line);\n\n      if (header) {\n        index.index = header[1];\n      }\n\n      i++;\n    } // Parse file headers if they are defined. Unified diff requires them, but\n    // there's no technical issues to have an isolated hunk without file header\n\n\n    parseFileHeader(index);\n    parseFileHeader(index); // Parse hunks\n\n    index.hunks = [];\n\n    while (i < diffstr.length) {\n      var _line = diffstr[i];\n\n      if (/^(Index:|diff|\\-\\-\\-|\\+\\+\\+)\\s/.test(_line)) {\n        break;\n      } else if (/^@@/.test(_line)) {\n        index.hunks.push(parseHunk());\n      } else if (_line && options.strict) {\n        // Ignore unexpected content unless in strict mode\n        throw new Error('Unknown line ' + (i + 1) + ' ' + JSON.stringify(_line));\n      } else {\n        i++;\n      }\n    }\n  } // Parses the --- and +++ headers, if none are found, no lines\n  // are consumed.\n\n\n  function parseFileHeader(index) {\n    var fileHeader = /^(---|\\+\\+\\+)\\s+(.*)$/.exec(diffstr[i]);\n\n    if (fileHeader) {\n      var keyPrefix = fileHeader[1] === '---' ? 'old' : 'new';\n      var data = fileHeader[2].split('\\t', 2);\n      var fileName = data[0].replace(/\\\\\\\\/g, '\\\\');\n\n      if (/^\".*\"$/.test(fileName)) {\n        fileName = fileName.substr(1, fileName.length - 2);\n      }\n\n      index[keyPrefix + 'FileName'] = fileName;\n      index[keyPrefix + 'Header'] = (data[1] || '').trim();\n      i++;\n    }\n  } // Parses a hunk\n  // This assumes that we are at the start of a hunk.\n\n\n  function parseHunk() {\n    var chunkHeaderIndex = i,\n        chunkHeaderLine = diffstr[i++],\n        chunkHeader = chunkHeaderLine.split(/@@ -(\\d+)(?:,(\\d+))? \\+(\\d+)(?:,(\\d+))? @@/);\n    var hunk = {\n      oldStart: +chunkHeader[1],\n      oldLines: typeof chunkHeader[2] === 'undefined' ? 1 : +chunkHeader[2],\n      newStart: +chunkHeader[3],\n      newLines: typeof chunkHeader[4] === 'undefined' ? 1 : +chunkHeader[4],\n      lines: [],\n      linedelimiters: []\n    }; // Unified Diff Format quirk: If the chunk size is 0,\n    // the first number is one lower than one would expect.\n    // https://www.artima.com/weblogs/viewpost.jsp?thread=164293\n\n    if (hunk.oldLines === 0) {\n      hunk.oldStart += 1;\n    }\n\n    if (hunk.newLines === 0) {\n      hunk.newStart += 1;\n    }\n\n    var addCount = 0,\n        removeCount = 0;\n\n    for (; i < diffstr.length; i++) {\n      // Lines starting with '---' could be mistaken for the \"remove line\" operation\n      // But they could be the header for the next file. Therefore prune such cases out.\n      if (diffstr[i].indexOf('--- ') === 0 && i + 2 < diffstr.length && diffstr[i + 1].indexOf('+++ ') === 0 && diffstr[i + 2].indexOf('@@') === 0) {\n        break;\n      }\n\n      var operation = diffstr[i].length == 0 && i != diffstr.length - 1 ? ' ' : diffstr[i][0];\n\n      if (operation === '+' || operation === '-' || operation === ' ' || operation === '\\\\') {\n        hunk.lines.push(diffstr[i]);\n        hunk.linedelimiters.push(delimiters[i] || '\\n');\n\n        if (operation === '+') {\n          addCount++;\n        } else if (operation === '-') {\n          removeCount++;\n        } else if (operation === ' ') {\n          addCount++;\n          removeCount++;\n        }\n      } else {\n        break;\n      }\n    } // Handle the empty block count case\n\n\n    if (!addCount && hunk.newLines === 1) {\n      hunk.newLines = 0;\n    }\n\n    if (!removeCount && hunk.oldLines === 1) {\n      hunk.oldLines = 0;\n    } // Perform optional sanity checking\n\n\n    if (options.strict) {\n      if (addCount !== hunk.newLines) {\n        throw new Error('Added line count did not match for hunk at line ' + (chunkHeaderIndex + 1));\n      }\n\n      if (removeCount !== hunk.oldLines) {\n        throw new Error('Removed line count did not match for hunk at line ' + (chunkHeaderIndex + 1));\n      }\n    }\n\n    return hunk;\n  }\n\n  while (i < diffstr.length) {\n    parseIndex();\n  }\n\n  return list;\n}\n\n// Iterator that traverses in the range of [min, max], stepping\n// by distance from a given start position. I.e. for [0, 4], with\n// start of 2, this will iterate 2, 3, 1, 4, 0.\nfunction distanceIterator (start, minLine, maxLine) {\n  var wantForward = true,\n      backwardExhausted = false,\n      forwardExhausted = false,\n      localOffset = 1;\n  return function iterator() {\n    if (wantForward && !forwardExhausted) {\n      if (backwardExhausted) {\n        localOffset++;\n      } else {\n        wantForward = false;\n      } // Check if trying to fit beyond text length, and if not, check it fits\n      // after offset location (or desired location on first iteration)\n\n\n      if (start + localOffset <= maxLine) {\n        return localOffset;\n      }\n\n      forwardExhausted = true;\n    }\n\n    if (!backwardExhausted) {\n      if (!forwardExhausted) {\n        wantForward = true;\n      } // Check if trying to fit before text beginning, and if not, check it fits\n      // before offset location\n\n\n      if (minLine <= start - localOffset) {\n        return -localOffset++;\n      }\n\n      backwardExhausted = true;\n      return iterator();\n    } // We tried to fit hunk before text beginning and beyond text length, then\n    // hunk can't fit on the text. Return undefined\n\n  };\n}\n\nfunction applyPatch(source, uniDiff) {\n  var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n\n  if (typeof uniDiff === 'string') {\n    uniDiff = parsePatch(uniDiff);\n  }\n\n  if (Array.isArray(uniDiff)) {\n    if (uniDiff.length > 1) {\n      throw new Error('applyPatch only works with a single input.');\n    }\n\n    uniDiff = uniDiff[0];\n  } // Apply the diff to the input\n\n\n  var lines = source.split(/\\r\\n|[\\n\\v\\f\\r\\x85]/),\n      delimiters = source.match(/\\r\\n|[\\n\\v\\f\\r\\x85]/g) || [],\n      hunks = uniDiff.hunks,\n      compareLine = options.compareLine || function (lineNumber, line, operation, patchContent) {\n    return line === patchContent;\n  },\n      errorCount = 0,\n      fuzzFactor = options.fuzzFactor || 0,\n      minLine = 0,\n      offset = 0,\n      removeEOFNL,\n      addEOFNL;\n  /**\n   * Checks if the hunk exactly fits on the provided location\n   */\n\n\n  function hunkFits(hunk, toPos) {\n    for (var j = 0; j < hunk.lines.length; j++) {\n      var line = hunk.lines[j],\n          operation = line.length > 0 ? line[0] : ' ',\n          content = line.length > 0 ? line.substr(1) : line;\n\n      if (operation === ' ' || operation === '-') {\n        // Context sanity check\n        if (!compareLine(toPos + 1, lines[toPos], operation, content)) {\n          errorCount++;\n\n          if (errorCount > fuzzFactor) {\n            return false;\n          }\n        }\n\n        toPos++;\n      }\n    }\n\n    return true;\n  } // Search best fit offsets for each hunk based on the previous ones\n\n\n  for (var i = 0; i < hunks.length; i++) {\n    var hunk = hunks[i],\n        maxLine = lines.length - hunk.oldLines,\n        localOffset = 0,\n        toPos = offset + hunk.oldStart - 1;\n    var iterator = distanceIterator(toPos, minLine, maxLine);\n\n    for (; localOffset !== undefined; localOffset = iterator()) {\n      if (hunkFits(hunk, toPos + localOffset)) {\n        hunk.offset = offset += localOffset;\n        break;\n      }\n    }\n\n    if (localOffset === undefined) {\n      return false;\n    } // Set lower text limit to end of the current hunk, so next ones don't try\n    // to fit over already patched text\n\n\n    minLine = hunk.offset + hunk.oldStart + hunk.oldLines;\n  } // Apply patch hunks\n\n\n  var diffOffset = 0;\n\n  for (var _i = 0; _i < hunks.length; _i++) {\n    var _hunk = hunks[_i],\n        _toPos = _hunk.oldStart + _hunk.offset + diffOffset - 1;\n\n    diffOffset += _hunk.newLines - _hunk.oldLines;\n\n    for (var j = 0; j < _hunk.lines.length; j++) {\n      var line = _hunk.lines[j],\n          operation = line.length > 0 ? line[0] : ' ',\n          content = line.length > 0 ? line.substr(1) : line,\n          delimiter = _hunk.linedelimiters[j];\n\n      if (operation === ' ') {\n        _toPos++;\n      } else if (operation === '-') {\n        lines.splice(_toPos, 1);\n        delimiters.splice(_toPos, 1);\n        /* istanbul ignore else */\n      } else if (operation === '+') {\n        lines.splice(_toPos, 0, content);\n        delimiters.splice(_toPos, 0, delimiter);\n        _toPos++;\n      } else if (operation === '\\\\') {\n        var previousOperation = _hunk.lines[j - 1] ? _hunk.lines[j - 1][0] : null;\n\n        if (previousOperation === '+') {\n          removeEOFNL = true;\n        } else if (previousOperation === '-') {\n          addEOFNL = true;\n        }\n      }\n    }\n  } // Handle EOFNL insertion/removal\n\n\n  if (removeEOFNL) {\n    while (!lines[lines.length - 1]) {\n      lines.pop();\n      delimiters.pop();\n    }\n  } else if (addEOFNL) {\n    lines.push('');\n    delimiters.push('\\n');\n  }\n\n  for (var _k = 0; _k < lines.length - 1; _k++) {\n    lines[_k] = lines[_k] + delimiters[_k];\n  }\n\n  return lines.join('');\n} // Wrapper that supports multiple file patches via callbacks.\n\nfunction applyPatches(uniDiff, options) {\n  if (typeof uniDiff === 'string') {\n    uniDiff = parsePatch(uniDiff);\n  }\n\n  var currentIndex = 0;\n\n  function processIndex() {\n    var index = uniDiff[currentIndex++];\n\n    if (!index) {\n      return options.complete();\n    }\n\n    options.loadFile(index, function (err, data) {\n      if (err) {\n        return options.complete(err);\n      }\n\n      var updatedContent = applyPatch(data, index, options);\n      options.patched(index, updatedContent, function (err) {\n        if (err) {\n          return options.complete(err);\n        }\n\n        processIndex();\n      });\n    });\n  }\n\n  processIndex();\n}\n\nfunction structuredPatch(oldFileName, newFileName, oldStr, newStr, oldHeader, newHeader, options) {\n  if (!options) {\n    options = {};\n  }\n\n  if (typeof options.context === 'undefined') {\n    options.context = 4;\n  }\n\n  var diff = diffLines(oldStr, newStr, options);\n\n  if (!diff) {\n    return;\n  }\n\n  diff.push({\n    value: '',\n    lines: []\n  }); // Append an empty value to make cleanup easier\n\n  function contextLines(lines) {\n    return lines.map(function (entry) {\n      return ' ' + entry;\n    });\n  }\n\n  var hunks = [];\n  var oldRangeStart = 0,\n      newRangeStart = 0,\n      curRange = [],\n      oldLine = 1,\n      newLine = 1;\n\n  var _loop = function _loop(i) {\n    var current = diff[i],\n        lines = current.lines || current.value.replace(/\\n$/, '').split('\\n');\n    current.lines = lines;\n\n    if (current.added || current.removed) {\n      var _curRange;\n\n      // If we have previous context, start with that\n      if (!oldRangeStart) {\n        var prev = diff[i - 1];\n        oldRangeStart = oldLine;\n        newRangeStart = newLine;\n\n        if (prev) {\n          curRange = options.context > 0 ? contextLines(prev.lines.slice(-options.context)) : [];\n          oldRangeStart -= curRange.length;\n          newRangeStart -= curRange.length;\n        }\n      } // Output our changes\n\n\n      (_curRange = curRange).push.apply(_curRange, _toConsumableArray(lines.map(function (entry) {\n        return (current.added ? '+' : '-') + entry;\n      }))); // Track the updated file position\n\n\n      if (current.added) {\n        newLine += lines.length;\n      } else {\n        oldLine += lines.length;\n      }\n    } else {\n      // Identical context lines. Track line changes\n      if (oldRangeStart) {\n        // Close out any changes that have been output (or join overlapping)\n        if (lines.length <= options.context * 2 && i < diff.length - 2) {\n          var _curRange2;\n\n          // Overlapping\n          (_curRange2 = curRange).push.apply(_curRange2, _toConsumableArray(contextLines(lines)));\n        } else {\n          var _curRange3;\n\n          // end the range and output\n          var contextSize = Math.min(lines.length, options.context);\n\n          (_curRange3 = curRange).push.apply(_curRange3, _toConsumableArray(contextLines(lines.slice(0, contextSize))));\n\n          var hunk = {\n            oldStart: oldRangeStart,\n            oldLines: oldLine - oldRangeStart + contextSize,\n            newStart: newRangeStart,\n            newLines: newLine - newRangeStart + contextSize,\n            lines: curRange\n          };\n\n          if (i >= diff.length - 2 && lines.length <= options.context) {\n            // EOF is inside this hunk\n            var oldEOFNewline = /\\n$/.test(oldStr);\n            var newEOFNewline = /\\n$/.test(newStr);\n            var noNlBeforeAdds = lines.length == 0 && curRange.length > hunk.oldLines;\n\n            if (!oldEOFNewline && noNlBeforeAdds && oldStr.length > 0) {\n              // special case: old has no eol and no trailing context; no-nl can end up before adds\n              // however, if the old file is empty, do not output the no-nl line\n              curRange.splice(hunk.oldLines, 0, '\\\\ No newline at end of file');\n            }\n\n            if (!oldEOFNewline && !noNlBeforeAdds || !newEOFNewline) {\n              curRange.push('\\\\ No newline at end of file');\n            }\n          }\n\n          hunks.push(hunk);\n          oldRangeStart = 0;\n          newRangeStart = 0;\n          curRange = [];\n        }\n      }\n\n      oldLine += lines.length;\n      newLine += lines.length;\n    }\n  };\n\n  for (var i = 0; i < diff.length; i++) {\n    _loop(i);\n  }\n\n  return {\n    oldFileName: oldFileName,\n    newFileName: newFileName,\n    oldHeader: oldHeader,\n    newHeader: newHeader,\n    hunks: hunks\n  };\n}\nfunction formatPatch(diff) {\n  var ret = [];\n\n  if (diff.oldFileName == diff.newFileName) {\n    ret.push('Index: ' + diff.oldFileName);\n  }\n\n  ret.push('===================================================================');\n  ret.push('--- ' + diff.oldFileName + (typeof diff.oldHeader === 'undefined' ? '' : '\\t' + diff.oldHeader));\n  ret.push('+++ ' + diff.newFileName + (typeof diff.newHeader === 'undefined' ? '' : '\\t' + diff.newHeader));\n\n  for (var i = 0; i < diff.hunks.length; i++) {\n    var hunk = diff.hunks[i]; // Unified Diff Format quirk: If the chunk size is 0,\n    // the first number is one lower than one would expect.\n    // https://www.artima.com/weblogs/viewpost.jsp?thread=164293\n\n    if (hunk.oldLines === 0) {\n      hunk.oldStart -= 1;\n    }\n\n    if (hunk.newLines === 0) {\n      hunk.newStart -= 1;\n    }\n\n    ret.push('@@ -' + hunk.oldStart + ',' + hunk.oldLines + ' +' + hunk.newStart + ',' + hunk.newLines + ' @@');\n    ret.push.apply(ret, hunk.lines);\n  }\n\n  return ret.join('\\n') + '\\n';\n}\nfunction createTwoFilesPatch(oldFileName, newFileName, oldStr, newStr, oldHeader, newHeader, options) {\n  return formatPatch(structuredPatch(oldFileName, newFileName, oldStr, newStr, oldHeader, newHeader, options));\n}\nfunction createPatch(fileName, oldStr, newStr, oldHeader, newHeader, options) {\n  return createTwoFilesPatch(fileName, fileName, oldStr, newStr, oldHeader, newHeader, options);\n}\n\nfunction arrayEqual(a, b) {\n  if (a.length !== b.length) {\n    return false;\n  }\n\n  return arrayStartsWith(a, b);\n}\nfunction arrayStartsWith(array, start) {\n  if (start.length > array.length) {\n    return false;\n  }\n\n  for (var i = 0; i < start.length; i++) {\n    if (start[i] !== array[i]) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\nfunction calcLineCount(hunk) {\n  var _calcOldNewLineCount = calcOldNewLineCount(hunk.lines),\n      oldLines = _calcOldNewLineCount.oldLines,\n      newLines = _calcOldNewLineCount.newLines;\n\n  if (oldLines !== undefined) {\n    hunk.oldLines = oldLines;\n  } else {\n    delete hunk.oldLines;\n  }\n\n  if (newLines !== undefined) {\n    hunk.newLines = newLines;\n  } else {\n    delete hunk.newLines;\n  }\n}\nfunction merge(mine, theirs, base) {\n  mine = loadPatch(mine, base);\n  theirs = loadPatch(theirs, base);\n  var ret = {}; // For index we just let it pass through as it doesn't have any necessary meaning.\n  // Leaving sanity checks on this to the API consumer that may know more about the\n  // meaning in their own context.\n\n  if (mine.index || theirs.index) {\n    ret.index = mine.index || theirs.index;\n  }\n\n  if (mine.newFileName || theirs.newFileName) {\n    if (!fileNameChanged(mine)) {\n      // No header or no change in ours, use theirs (and ours if theirs does not exist)\n      ret.oldFileName = theirs.oldFileName || mine.oldFileName;\n      ret.newFileName = theirs.newFileName || mine.newFileName;\n      ret.oldHeader = theirs.oldHeader || mine.oldHeader;\n      ret.newHeader = theirs.newHeader || mine.newHeader;\n    } else if (!fileNameChanged(theirs)) {\n      // No header or no change in theirs, use ours\n      ret.oldFileName = mine.oldFileName;\n      ret.newFileName = mine.newFileName;\n      ret.oldHeader = mine.oldHeader;\n      ret.newHeader = mine.newHeader;\n    } else {\n      // Both changed... figure it out\n      ret.oldFileName = selectField(ret, mine.oldFileName, theirs.oldFileName);\n      ret.newFileName = selectField(ret, mine.newFileName, theirs.newFileName);\n      ret.oldHeader = selectField(ret, mine.oldHeader, theirs.oldHeader);\n      ret.newHeader = selectField(ret, mine.newHeader, theirs.newHeader);\n    }\n  }\n\n  ret.hunks = [];\n  var mineIndex = 0,\n      theirsIndex = 0,\n      mineOffset = 0,\n      theirsOffset = 0;\n\n  while (mineIndex < mine.hunks.length || theirsIndex < theirs.hunks.length) {\n    var mineCurrent = mine.hunks[mineIndex] || {\n      oldStart: Infinity\n    },\n        theirsCurrent = theirs.hunks[theirsIndex] || {\n      oldStart: Infinity\n    };\n\n    if (hunkBefore(mineCurrent, theirsCurrent)) {\n      // This patch does not overlap with any of the others, yay.\n      ret.hunks.push(cloneHunk(mineCurrent, mineOffset));\n      mineIndex++;\n      theirsOffset += mineCurrent.newLines - mineCurrent.oldLines;\n    } else if (hunkBefore(theirsCurrent, mineCurrent)) {\n      // This patch does not overlap with any of the others, yay.\n      ret.hunks.push(cloneHunk(theirsCurrent, theirsOffset));\n      theirsIndex++;\n      mineOffset += theirsCurrent.newLines - theirsCurrent.oldLines;\n    } else {\n      // Overlap, merge as best we can\n      var mergedHunk = {\n        oldStart: Math.min(mineCurrent.oldStart, theirsCurrent.oldStart),\n        oldLines: 0,\n        newStart: Math.min(mineCurrent.newStart + mineOffset, theirsCurrent.oldStart + theirsOffset),\n        newLines: 0,\n        lines: []\n      };\n      mergeLines(mergedHunk, mineCurrent.oldStart, mineCurrent.lines, theirsCurrent.oldStart, theirsCurrent.lines);\n      theirsIndex++;\n      mineIndex++;\n      ret.hunks.push(mergedHunk);\n    }\n  }\n\n  return ret;\n}\n\nfunction loadPatch(param, base) {\n  if (typeof param === 'string') {\n    if (/^@@/m.test(param) || /^Index:/m.test(param)) {\n      return parsePatch(param)[0];\n    }\n\n    if (!base) {\n      throw new Error('Must provide a base reference or pass in a patch');\n    }\n\n    return structuredPatch(undefined, undefined, base, param);\n  }\n\n  return param;\n}\n\nfunction fileNameChanged(patch) {\n  return patch.newFileName && patch.newFileName !== patch.oldFileName;\n}\n\nfunction selectField(index, mine, theirs) {\n  if (mine === theirs) {\n    return mine;\n  } else {\n    index.conflict = true;\n    return {\n      mine: mine,\n      theirs: theirs\n    };\n  }\n}\n\nfunction hunkBefore(test, check) {\n  return test.oldStart < check.oldStart && test.oldStart + test.oldLines < check.oldStart;\n}\n\nfunction cloneHunk(hunk, offset) {\n  return {\n    oldStart: hunk.oldStart,\n    oldLines: hunk.oldLines,\n    newStart: hunk.newStart + offset,\n    newLines: hunk.newLines,\n    lines: hunk.lines\n  };\n}\n\nfunction mergeLines(hunk, mineOffset, mineLines, theirOffset, theirLines) {\n  // This will generally result in a conflicted hunk, but there are cases where the context\n  // is the only overlap where we can successfully merge the content here.\n  var mine = {\n    offset: mineOffset,\n    lines: mineLines,\n    index: 0\n  },\n      their = {\n    offset: theirOffset,\n    lines: theirLines,\n    index: 0\n  }; // Handle any leading content\n\n  insertLeading(hunk, mine, their);\n  insertLeading(hunk, their, mine); // Now in the overlap content. Scan through and select the best changes from each.\n\n  while (mine.index < mine.lines.length && their.index < their.lines.length) {\n    var mineCurrent = mine.lines[mine.index],\n        theirCurrent = their.lines[their.index];\n\n    if ((mineCurrent[0] === '-' || mineCurrent[0] === '+') && (theirCurrent[0] === '-' || theirCurrent[0] === '+')) {\n      // Both modified ...\n      mutualChange(hunk, mine, their);\n    } else if (mineCurrent[0] === '+' && theirCurrent[0] === ' ') {\n      var _hunk$lines;\n\n      // Mine inserted\n      (_hunk$lines = hunk.lines).push.apply(_hunk$lines, _toConsumableArray(collectChange(mine)));\n    } else if (theirCurrent[0] === '+' && mineCurrent[0] === ' ') {\n      var _hunk$lines2;\n\n      // Theirs inserted\n      (_hunk$lines2 = hunk.lines).push.apply(_hunk$lines2, _toConsumableArray(collectChange(their)));\n    } else if (mineCurrent[0] === '-' && theirCurrent[0] === ' ') {\n      // Mine removed or edited\n      removal(hunk, mine, their);\n    } else if (theirCurrent[0] === '-' && mineCurrent[0] === ' ') {\n      // Their removed or edited\n      removal(hunk, their, mine, true);\n    } else if (mineCurrent === theirCurrent) {\n      // Context identity\n      hunk.lines.push(mineCurrent);\n      mine.index++;\n      their.index++;\n    } else {\n      // Context mismatch\n      conflict(hunk, collectChange(mine), collectChange(their));\n    }\n  } // Now push anything that may be remaining\n\n\n  insertTrailing(hunk, mine);\n  insertTrailing(hunk, their);\n  calcLineCount(hunk);\n}\n\nfunction mutualChange(hunk, mine, their) {\n  var myChanges = collectChange(mine),\n      theirChanges = collectChange(their);\n\n  if (allRemoves(myChanges) && allRemoves(theirChanges)) {\n    // Special case for remove changes that are supersets of one another\n    if (arrayStartsWith(myChanges, theirChanges) && skipRemoveSuperset(their, myChanges, myChanges.length - theirChanges.length)) {\n      var _hunk$lines3;\n\n      (_hunk$lines3 = hunk.lines).push.apply(_hunk$lines3, _toConsumableArray(myChanges));\n\n      return;\n    } else if (arrayStartsWith(theirChanges, myChanges) && skipRemoveSuperset(mine, theirChanges, theirChanges.length - myChanges.length)) {\n      var _hunk$lines4;\n\n      (_hunk$lines4 = hunk.lines).push.apply(_hunk$lines4, _toConsumableArray(theirChanges));\n\n      return;\n    }\n  } else if (arrayEqual(myChanges, theirChanges)) {\n    var _hunk$lines5;\n\n    (_hunk$lines5 = hunk.lines).push.apply(_hunk$lines5, _toConsumableArray(myChanges));\n\n    return;\n  }\n\n  conflict(hunk, myChanges, theirChanges);\n}\n\nfunction removal(hunk, mine, their, swap) {\n  var myChanges = collectChange(mine),\n      theirChanges = collectContext(their, myChanges);\n\n  if (theirChanges.merged) {\n    var _hunk$lines6;\n\n    (_hunk$lines6 = hunk.lines).push.apply(_hunk$lines6, _toConsumableArray(theirChanges.merged));\n  } else {\n    conflict(hunk, swap ? theirChanges : myChanges, swap ? myChanges : theirChanges);\n  }\n}\n\nfunction conflict(hunk, mine, their) {\n  hunk.conflict = true;\n  hunk.lines.push({\n    conflict: true,\n    mine: mine,\n    theirs: their\n  });\n}\n\nfunction insertLeading(hunk, insert, their) {\n  while (insert.offset < their.offset && insert.index < insert.lines.length) {\n    var line = insert.lines[insert.index++];\n    hunk.lines.push(line);\n    insert.offset++;\n  }\n}\n\nfunction insertTrailing(hunk, insert) {\n  while (insert.index < insert.lines.length) {\n    var line = insert.lines[insert.index++];\n    hunk.lines.push(line);\n  }\n}\n\nfunction collectChange(state) {\n  var ret = [],\n      operation = state.lines[state.index][0];\n\n  while (state.index < state.lines.length) {\n    var line = state.lines[state.index]; // Group additions that are immediately after subtractions and treat them as one \"atomic\" modify change.\n\n    if (operation === '-' && line[0] === '+') {\n      operation = '+';\n    }\n\n    if (operation === line[0]) {\n      ret.push(line);\n      state.index++;\n    } else {\n      break;\n    }\n  }\n\n  return ret;\n}\n\nfunction collectContext(state, matchChanges) {\n  var changes = [],\n      merged = [],\n      matchIndex = 0,\n      contextChanges = false,\n      conflicted = false;\n\n  while (matchIndex < matchChanges.length && state.index < state.lines.length) {\n    var change = state.lines[state.index],\n        match = matchChanges[matchIndex]; // Once we've hit our add, then we are done\n\n    if (match[0] === '+') {\n      break;\n    }\n\n    contextChanges = contextChanges || change[0] !== ' ';\n    merged.push(match);\n    matchIndex++; // Consume any additions in the other block as a conflict to attempt\n    // to pull in the remaining context after this\n\n    if (change[0] === '+') {\n      conflicted = true;\n\n      while (change[0] === '+') {\n        changes.push(change);\n        change = state.lines[++state.index];\n      }\n    }\n\n    if (match.substr(1) === change.substr(1)) {\n      changes.push(change);\n      state.index++;\n    } else {\n      conflicted = true;\n    }\n  }\n\n  if ((matchChanges[matchIndex] || '')[0] === '+' && contextChanges) {\n    conflicted = true;\n  }\n\n  if (conflicted) {\n    return changes;\n  }\n\n  while (matchIndex < matchChanges.length) {\n    merged.push(matchChanges[matchIndex++]);\n  }\n\n  return {\n    merged: merged,\n    changes: changes\n  };\n}\n\nfunction allRemoves(changes) {\n  return changes.reduce(function (prev, change) {\n    return prev && change[0] === '-';\n  }, true);\n}\n\nfunction skipRemoveSuperset(state, removeChanges, delta) {\n  for (var i = 0; i < delta; i++) {\n    var changeContent = removeChanges[removeChanges.length - delta + i].substr(1);\n\n    if (state.lines[state.index + i] !== ' ' + changeContent) {\n      return false;\n    }\n  }\n\n  state.index += delta;\n  return true;\n}\n\nfunction calcOldNewLineCount(lines) {\n  var oldLines = 0;\n  var newLines = 0;\n  lines.forEach(function (line) {\n    if (typeof line !== 'string') {\n      var myCount = calcOldNewLineCount(line.mine);\n      var theirCount = calcOldNewLineCount(line.theirs);\n\n      if (oldLines !== undefined) {\n        if (myCount.oldLines === theirCount.oldLines) {\n          oldLines += myCount.oldLines;\n        } else {\n          oldLines = undefined;\n        }\n      }\n\n      if (newLines !== undefined) {\n        if (myCount.newLines === theirCount.newLines) {\n          newLines += myCount.newLines;\n        } else {\n          newLines = undefined;\n        }\n      }\n    } else {\n      if (newLines !== undefined && (line[0] === '+' || line[0] === ' ')) {\n        newLines++;\n      }\n\n      if (oldLines !== undefined && (line[0] === '-' || line[0] === ' ')) {\n        oldLines++;\n      }\n    }\n  });\n  return {\n    oldLines: oldLines,\n    newLines: newLines\n  };\n}\n\n// See: http://code.google.com/p/google-diff-match-patch/wiki/API\nfunction convertChangesToDMP(changes) {\n  var ret = [],\n      change,\n      operation;\n\n  for (var i = 0; i < changes.length; i++) {\n    change = changes[i];\n\n    if (change.added) {\n      operation = 1;\n    } else if (change.removed) {\n      operation = -1;\n    } else {\n      operation = 0;\n    }\n\n    ret.push([operation, change.value]);\n  }\n\n  return ret;\n}\n\nfunction convertChangesToXML(changes) {\n  var ret = [];\n\n  for (var i = 0; i < changes.length; i++) {\n    var change = changes[i];\n\n    if (change.added) {\n      ret.push('<ins>');\n    } else if (change.removed) {\n      ret.push('<del>');\n    }\n\n    ret.push(escapeHTML(change.value));\n\n    if (change.added) {\n      ret.push('</ins>');\n    } else if (change.removed) {\n      ret.push('</del>');\n    }\n  }\n\n  return ret.join('');\n}\n\nfunction escapeHTML(s) {\n  var n = s;\n  n = n.replace(/&/g, '&amp;');\n  n = n.replace(/</g, '&lt;');\n  n = n.replace(/>/g, '&gt;');\n  n = n.replace(/\"/g, '&quot;');\n  return n;\n}\n\nexport { Diff, applyPatch, applyPatches, canonicalize, convertChangesToDMP, convertChangesToXML, createPatch, createTwoFilesPatch, diffArrays, diffChars, diffCss, diffJson, diffLines, diffSentences, diffTrimmedLines, diffWords, diffWordsWithSpace, merge, parsePatch, structuredPatch };\n"], "mappings": "AAAA,SAASA,IAAIA,CAAA,EAAG,CAAC;AACjBA,IAAI,CAACC,SAAS,GAAG;EACfC,IAAI,EAAE,SAASA,IAAIA,CAACC,SAAS,EAAEC,SAAS,EAAE;IACxC,IAAIC,OAAO,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACpF,IAAIG,QAAQ,GAAGJ,OAAO,CAACI,QAAQ;IAE/B,IAAI,OAAOJ,OAAO,KAAK,UAAU,EAAE;MACjCI,QAAQ,GAAGJ,OAAO;MAClBA,OAAO,GAAG,CAAC,CAAC;IACd;IAEA,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAIK,IAAI,GAAG,IAAI;IAEf,SAASC,IAAIA,CAACC,KAAK,EAAE;MACnB,IAAIH,QAAQ,EAAE;QACZI,UAAU,CAAC,YAAY;UACrBJ,QAAQ,CAACD,SAAS,EAAEI,KAAK,CAAC;QAC5B,CAAC,EAAE,CAAC,CAAC;QACL,OAAO,IAAI;MACb,CAAC,MAAM;QACL,OAAOA,KAAK;MACd;IACF,CAAC,CAAC;;IAGFT,SAAS,GAAG,IAAI,CAACW,SAAS,CAACX,SAAS,CAAC;IACrCC,SAAS,GAAG,IAAI,CAACU,SAAS,CAACV,SAAS,CAAC;IACrCD,SAAS,GAAG,IAAI,CAACY,WAAW,CAAC,IAAI,CAACC,QAAQ,CAACb,SAAS,CAAC,CAAC;IACtDC,SAAS,GAAG,IAAI,CAACW,WAAW,CAAC,IAAI,CAACC,QAAQ,CAACZ,SAAS,CAAC,CAAC;IACtD,IAAIa,MAAM,GAAGb,SAAS,CAACG,MAAM;MACzBW,MAAM,GAAGf,SAAS,CAACI,MAAM;IAC7B,IAAIY,UAAU,GAAG,CAAC;IAClB,IAAIC,aAAa,GAAGH,MAAM,GAAGC,MAAM;IAEnC,IAAIb,OAAO,CAACe,aAAa,EAAE;MACzBA,aAAa,GAAGC,IAAI,CAACC,GAAG,CAACF,aAAa,EAAEf,OAAO,CAACe,aAAa,CAAC;IAChE;IAEA,IAAIG,QAAQ,GAAG,CAAC;MACdC,MAAM,EAAE,CAAC,CAAC;MACVC,UAAU,EAAE;IACd,CAAC,CAAC,CAAC,CAAC;;IAEJ,IAAIC,MAAM,GAAG,IAAI,CAACC,aAAa,CAACJ,QAAQ,CAAC,CAAC,CAAC,EAAEnB,SAAS,EAAED,SAAS,EAAE,CAAC,CAAC;IAErE,IAAIoB,QAAQ,CAAC,CAAC,CAAC,CAACC,MAAM,GAAG,CAAC,IAAIP,MAAM,IAAIS,MAAM,GAAG,CAAC,IAAIR,MAAM,EAAE;MAC5D;MACA,OAAOP,IAAI,CAAC,CAAC;QACXC,KAAK,EAAE,IAAI,CAACgB,IAAI,CAACxB,SAAS,CAAC;QAC3ByB,KAAK,EAAEzB,SAAS,CAACG;MACnB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;;IAGF,SAASuB,cAAcA,CAAA,EAAG;MACxB,KAAK,IAAIC,YAAY,GAAG,CAAC,CAAC,GAAGZ,UAAU,EAAEY,YAAY,IAAIZ,UAAU,EAAEY,YAAY,IAAI,CAAC,EAAE;QACtF,IAAIC,QAAQ,GAAG,KAAK,CAAC;QAErB,IAAIC,OAAO,GAAGV,QAAQ,CAACQ,YAAY,GAAG,CAAC,CAAC;UACpCG,UAAU,GAAGX,QAAQ,CAACQ,YAAY,GAAG,CAAC,CAAC;UACvCI,OAAO,GAAG,CAACD,UAAU,GAAGA,UAAU,CAACV,MAAM,GAAG,CAAC,IAAIO,YAAY;QAEjE,IAAIE,OAAO,EAAE;UACX;UACAV,QAAQ,CAACQ,YAAY,GAAG,CAAC,CAAC,GAAGvB,SAAS;QACxC;QAEA,IAAI4B,MAAM,GAAGH,OAAO,IAAIA,OAAO,CAACT,MAAM,GAAG,CAAC,GAAGP,MAAM;UAC/CoB,SAAS,GAAGH,UAAU,IAAI,CAAC,IAAIC,OAAO,IAAIA,OAAO,GAAGjB,MAAM;QAE9D,IAAI,CAACkB,MAAM,IAAI,CAACC,SAAS,EAAE;UACzB;UACAd,QAAQ,CAACQ,YAAY,CAAC,GAAGvB,SAAS;UAClC;QACF,CAAC,CAAC;QACF;QACA;;QAGA,IAAI,CAAC4B,MAAM,IAAIC,SAAS,IAAIJ,OAAO,CAACT,MAAM,GAAGU,UAAU,CAACV,MAAM,EAAE;UAC9DQ,QAAQ,GAAGM,SAAS,CAACJ,UAAU,CAAC;UAChCxB,IAAI,CAAC6B,aAAa,CAACP,QAAQ,CAACP,UAAU,EAAEjB,SAAS,EAAE,IAAI,CAAC;QAC1D,CAAC,MAAM;UACLwB,QAAQ,GAAGC,OAAO,CAAC,CAAC;;UAEpBD,QAAQ,CAACR,MAAM,EAAE;UACjBd,IAAI,CAAC6B,aAAa,CAACP,QAAQ,CAACP,UAAU,EAAE,IAAI,EAAEjB,SAAS,CAAC;QAC1D;QAEA2B,OAAO,GAAGzB,IAAI,CAACiB,aAAa,CAACK,QAAQ,EAAE5B,SAAS,EAAED,SAAS,EAAE4B,YAAY,CAAC,CAAC,CAAC;;QAE5E,IAAIC,QAAQ,CAACR,MAAM,GAAG,CAAC,IAAIP,MAAM,IAAIkB,OAAO,GAAG,CAAC,IAAIjB,MAAM,EAAE;UAC1D,OAAOP,IAAI,CAAC6B,WAAW,CAAC9B,IAAI,EAAEsB,QAAQ,CAACP,UAAU,EAAErB,SAAS,EAAED,SAAS,EAAEO,IAAI,CAAC+B,eAAe,CAAC,CAAC;QACjG,CAAC,MAAM;UACL;UACAlB,QAAQ,CAACQ,YAAY,CAAC,GAAGC,QAAQ;QACnC;MACF;MAEAb,UAAU,EAAE;IACd,CAAC,CAAC;IACF;IACA;IACA;;IAGA,IAAIV,QAAQ,EAAE;MACZ,CAAC,SAASiC,IAAIA,CAAA,EAAG;QACf7B,UAAU,CAAC,YAAY;UACrB,IAAIM,UAAU,GAAGC,aAAa,EAAE;YAC9B,OAAOX,QAAQ,EAAE;UACnB;UAEA,IAAI,CAACqB,cAAc,EAAE,EAAE;YACrBY,IAAI,EAAE;UACR;QACF,CAAC,EAAE,CAAC,CAAC;MACP,CAAC,GAAG;IACN,CAAC,MAAM;MACL,OAAOvB,UAAU,IAAIC,aAAa,EAAE;QAClC,IAAIuB,GAAG,GAAGb,cAAc,EAAE;QAE1B,IAAIa,GAAG,EAAE;UACP,OAAOA,GAAG;QACZ;MACF;IACF;EACF,CAAC;EACDJ,aAAa,EAAE,SAASA,aAAaA,CAACd,UAAU,EAAEmB,KAAK,EAAEC,OAAO,EAAE;IAChE,IAAIC,IAAI,GAAGrB,UAAU,CAACA,UAAU,CAAClB,MAAM,GAAG,CAAC,CAAC;IAE5C,IAAIuC,IAAI,IAAIA,IAAI,CAACF,KAAK,KAAKA,KAAK,IAAIE,IAAI,CAACD,OAAO,KAAKA,OAAO,EAAE;MAC5D;MACA;MACApB,UAAU,CAACA,UAAU,CAAClB,MAAM,GAAG,CAAC,CAAC,GAAG;QAClCsB,KAAK,EAAEiB,IAAI,CAACjB,KAAK,GAAG,CAAC;QACrBe,KAAK,EAAEA,KAAK;QACZC,OAAO,EAAEA;MACX,CAAC;IACH,CAAC,MAAM;MACLpB,UAAU,CAACsB,IAAI,CAAC;QACdlB,KAAK,EAAE,CAAC;QACRe,KAAK,EAAEA,KAAK;QACZC,OAAO,EAAEA;MACX,CAAC,CAAC;IACJ;EACF,CAAC;EACDlB,aAAa,EAAE,SAASA,aAAaA,CAACK,QAAQ,EAAE5B,SAAS,EAAED,SAAS,EAAE4B,YAAY,EAAE;IAClF,IAAId,MAAM,GAAGb,SAAS,CAACG,MAAM;MACzBW,MAAM,GAAGf,SAAS,CAACI,MAAM;MACzBiB,MAAM,GAAGQ,QAAQ,CAACR,MAAM;MACxBE,MAAM,GAAGF,MAAM,GAAGO,YAAY;MAC9BiB,WAAW,GAAG,CAAC;IAEnB,OAAOxB,MAAM,GAAG,CAAC,GAAGP,MAAM,IAAIS,MAAM,GAAG,CAAC,GAAGR,MAAM,IAAI,IAAI,CAAC+B,MAAM,CAAC7C,SAAS,CAACoB,MAAM,GAAG,CAAC,CAAC,EAAErB,SAAS,CAACuB,MAAM,GAAG,CAAC,CAAC,CAAC,EAAE;MAC9GF,MAAM,EAAE;MACRE,MAAM,EAAE;MACRsB,WAAW,EAAE;IACf;IAEA,IAAIA,WAAW,EAAE;MACfhB,QAAQ,CAACP,UAAU,CAACsB,IAAI,CAAC;QACvBlB,KAAK,EAAEmB;MACT,CAAC,CAAC;IACJ;IAEAhB,QAAQ,CAACR,MAAM,GAAGA,MAAM;IACxB,OAAOE,MAAM;EACf,CAAC;EACDuB,MAAM,EAAE,SAASA,MAAMA,CAACC,IAAI,EAAEC,KAAK,EAAE;IACnC,IAAI,IAAI,CAAC9C,OAAO,CAAC+C,UAAU,EAAE;MAC3B,OAAO,IAAI,CAAC/C,OAAO,CAAC+C,UAAU,CAACF,IAAI,EAAEC,KAAK,CAAC;IAC7C,CAAC,MAAM;MACL,OAAOD,IAAI,KAAKC,KAAK,IAAI,IAAI,CAAC9C,OAAO,CAACgD,UAAU,IAAIH,IAAI,CAACI,WAAW,EAAE,KAAKH,KAAK,CAACG,WAAW,EAAE;IAChG;EACF,CAAC;EACDvC,WAAW,EAAE,SAASA,WAAWA,CAACwC,KAAK,EAAE;IACvC,IAAIZ,GAAG,GAAG,EAAE;IAEZ,KAAK,IAAIa,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAAChD,MAAM,EAAEiD,CAAC,EAAE,EAAE;MACrC,IAAID,KAAK,CAACC,CAAC,CAAC,EAAE;QACZb,GAAG,CAACI,IAAI,CAACQ,KAAK,CAACC,CAAC,CAAC,CAAC;MACpB;IACF;IAEA,OAAOb,GAAG;EACZ,CAAC;EACD7B,SAAS,EAAE,SAASA,SAASA,CAACF,KAAK,EAAE;IACnC,OAAOA,KAAK;EACd,CAAC;EACDI,QAAQ,EAAE,SAASA,QAAQA,CAACJ,KAAK,EAAE;IACjC,OAAOA,KAAK,CAAC6C,KAAK,CAAC,EAAE,CAAC;EACxB,CAAC;EACD7B,IAAI,EAAE,SAASA,IAAIA,CAAC8B,KAAK,EAAE;IACzB,OAAOA,KAAK,CAAC9B,IAAI,CAAC,EAAE,CAAC;EACvB;AACF,CAAC;AAED,SAASY,WAAWA,CAACtC,IAAI,EAAEuB,UAAU,EAAErB,SAAS,EAAED,SAAS,EAAEsC,eAAe,EAAE;EAC5E,IAAIkB,YAAY,GAAG,CAAC;IAChBC,YAAY,GAAGnC,UAAU,CAAClB,MAAM;IAChCiB,MAAM,GAAG,CAAC;IACVE,MAAM,GAAG,CAAC;EAEd,OAAOiC,YAAY,GAAGC,YAAY,EAAED,YAAY,EAAE,EAAE;IAClD,IAAIE,SAAS,GAAGpC,UAAU,CAACkC,YAAY,CAAC;IAExC,IAAI,CAACE,SAAS,CAAChB,OAAO,EAAE;MACtB,IAAI,CAACgB,SAAS,CAACjB,KAAK,IAAIH,eAAe,EAAE;QACvC,IAAI7B,KAAK,GAAGR,SAAS,CAAC0D,KAAK,CAACtC,MAAM,EAAEA,MAAM,GAAGqC,SAAS,CAAChC,KAAK,CAAC;QAC7DjB,KAAK,GAAGA,KAAK,CAACmD,GAAG,CAAC,UAAUnD,KAAK,EAAE4C,CAAC,EAAE;UACpC,IAAIQ,QAAQ,GAAG7D,SAAS,CAACuB,MAAM,GAAG8B,CAAC,CAAC;UACpC,OAAOQ,QAAQ,CAACzD,MAAM,GAAGK,KAAK,CAACL,MAAM,GAAGyD,QAAQ,GAAGpD,KAAK;QAC1D,CAAC,CAAC;QACFiD,SAAS,CAACjD,KAAK,GAAGV,IAAI,CAAC0B,IAAI,CAAChB,KAAK,CAAC;MACpC,CAAC,MAAM;QACLiD,SAAS,CAACjD,KAAK,GAAGV,IAAI,CAAC0B,IAAI,CAACxB,SAAS,CAAC0D,KAAK,CAACtC,MAAM,EAAEA,MAAM,GAAGqC,SAAS,CAAChC,KAAK,CAAC,CAAC;MAChF;MAEAL,MAAM,IAAIqC,SAAS,CAAChC,KAAK,CAAC,CAAC;;MAE3B,IAAI,CAACgC,SAAS,CAACjB,KAAK,EAAE;QACpBlB,MAAM,IAAImC,SAAS,CAAChC,KAAK;MAC3B;IACF,CAAC,MAAM;MACLgC,SAAS,CAACjD,KAAK,GAAGV,IAAI,CAAC0B,IAAI,CAACzB,SAAS,CAAC2D,KAAK,CAACpC,MAAM,EAAEA,MAAM,GAAGmC,SAAS,CAAChC,KAAK,CAAC,CAAC;MAC9EH,MAAM,IAAImC,SAAS,CAAChC,KAAK,CAAC,CAAC;MAC3B;MACA;;MAEA,IAAI8B,YAAY,IAAIlC,UAAU,CAACkC,YAAY,GAAG,CAAC,CAAC,CAACf,KAAK,EAAE;QACtD,IAAIqB,GAAG,GAAGxC,UAAU,CAACkC,YAAY,GAAG,CAAC,CAAC;QACtClC,UAAU,CAACkC,YAAY,GAAG,CAAC,CAAC,GAAGlC,UAAU,CAACkC,YAAY,CAAC;QACvDlC,UAAU,CAACkC,YAAY,CAAC,GAAGM,GAAG;MAChC;IACF;EACF,CAAC,CAAC;EACF;EACA;;EAGA,IAAIC,aAAa,GAAGzC,UAAU,CAACmC,YAAY,GAAG,CAAC,CAAC;EAEhD,IAAIA,YAAY,GAAG,CAAC,IAAI,OAAOM,aAAa,CAACtD,KAAK,KAAK,QAAQ,KAAKsD,aAAa,CAACtB,KAAK,IAAIsB,aAAa,CAACrB,OAAO,CAAC,IAAI3C,IAAI,CAAC+C,MAAM,CAAC,EAAE,EAAEiB,aAAa,CAACtD,KAAK,CAAC,EAAE;IACzJa,UAAU,CAACmC,YAAY,GAAG,CAAC,CAAC,CAAChD,KAAK,IAAIsD,aAAa,CAACtD,KAAK;IACzDa,UAAU,CAAC0C,GAAG,EAAE;EAClB;EAEA,OAAO1C,UAAU;AACnB;AAEA,SAASa,SAASA,CAAC8B,IAAI,EAAE;EACvB,OAAO;IACL5C,MAAM,EAAE4C,IAAI,CAAC5C,MAAM;IACnBC,UAAU,EAAE2C,IAAI,CAAC3C,UAAU,CAACqC,KAAK,CAAC,CAAC;EACrC,CAAC;AACH;AAEA,IAAIO,aAAa,GAAG,IAAIrE,IAAI,EAAE;AAC9B,SAASsE,SAASA,CAACC,MAAM,EAAEC,MAAM,EAAEnE,OAAO,EAAE;EAC1C,OAAOgE,aAAa,CAACnE,IAAI,CAACqE,MAAM,EAAEC,MAAM,EAAEnE,OAAO,CAAC;AACpD;AAEA,SAASoE,eAAeA,CAACpE,OAAO,EAAEqE,QAAQ,EAAE;EAC1C,IAAI,OAAOrE,OAAO,KAAK,UAAU,EAAE;IACjCqE,QAAQ,CAACjE,QAAQ,GAAGJ,OAAO;EAC7B,CAAC,MAAM,IAAIA,OAAO,EAAE;IAClB,KAAK,IAAIsE,IAAI,IAAItE,OAAO,EAAE;MACxB;MACA,IAAIA,OAAO,CAACuE,cAAc,CAACD,IAAI,CAAC,EAAE;QAChCD,QAAQ,CAACC,IAAI,CAAC,GAAGtE,OAAO,CAACsE,IAAI,CAAC;MAChC;IACF;EACF;EAEA,OAAOD,QAAQ;AACjB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIG,iBAAiB,GAAG,+DAA+D;AACvF,IAAIC,YAAY,GAAG,IAAI;AACvB,IAAIC,QAAQ,GAAG,IAAI/E,IAAI,EAAE;AAEzB+E,QAAQ,CAAC9B,MAAM,GAAG,UAAUC,IAAI,EAAEC,KAAK,EAAE;EACvC,IAAI,IAAI,CAAC9C,OAAO,CAACgD,UAAU,EAAE;IAC3BH,IAAI,GAAGA,IAAI,CAACI,WAAW,EAAE;IACzBH,KAAK,GAAGA,KAAK,CAACG,WAAW,EAAE;EAC7B;EAEA,OAAOJ,IAAI,KAAKC,KAAK,IAAI,IAAI,CAAC9C,OAAO,CAAC2E,gBAAgB,IAAI,CAACF,YAAY,CAACG,IAAI,CAAC/B,IAAI,CAAC,IAAI,CAAC4B,YAAY,CAACG,IAAI,CAAC9B,KAAK,CAAC;AACjH,CAAC;AAED4B,QAAQ,CAAC/D,QAAQ,GAAG,UAAUJ,KAAK,EAAE;EACnC;EACA,IAAIsE,MAAM,GAAGtE,KAAK,CAAC6C,KAAK,CAAC,iCAAiC,CAAC,CAAC,CAAC;;EAE7D,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0B,MAAM,CAAC3E,MAAM,GAAG,CAAC,EAAEiD,CAAC,EAAE,EAAE;IAC1C;IACA,IAAI,CAAC0B,MAAM,CAAC1B,CAAC,GAAG,CAAC,CAAC,IAAI0B,MAAM,CAAC1B,CAAC,GAAG,CAAC,CAAC,IAAIqB,iBAAiB,CAACI,IAAI,CAACC,MAAM,CAAC1B,CAAC,CAAC,CAAC,IAAIqB,iBAAiB,CAACI,IAAI,CAACC,MAAM,CAAC1B,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;MACjH0B,MAAM,CAAC1B,CAAC,CAAC,IAAI0B,MAAM,CAAC1B,CAAC,GAAG,CAAC,CAAC;MAC1B0B,MAAM,CAACC,MAAM,CAAC3B,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;MACvBA,CAAC,EAAE;IACL;EACF;EAEA,OAAO0B,MAAM;AACf,CAAC;AAED,SAASE,SAASA,CAACb,MAAM,EAAEC,MAAM,EAAEnE,OAAO,EAAE;EAC1CA,OAAO,GAAGoE,eAAe,CAACpE,OAAO,EAAE;IACjC2E,gBAAgB,EAAE;EACpB,CAAC,CAAC;EACF,OAAOD,QAAQ,CAAC7E,IAAI,CAACqE,MAAM,EAAEC,MAAM,EAAEnE,OAAO,CAAC;AAC/C;AACA,SAASgF,kBAAkBA,CAACd,MAAM,EAAEC,MAAM,EAAEnE,OAAO,EAAE;EACnD,OAAO0E,QAAQ,CAAC7E,IAAI,CAACqE,MAAM,EAAEC,MAAM,EAAEnE,OAAO,CAAC;AAC/C;AAEA,IAAIiF,QAAQ,GAAG,IAAItF,IAAI,EAAE;AAEzBsF,QAAQ,CAACtE,QAAQ,GAAG,UAAUJ,KAAK,EAAE;EACnC,IAAI2E,QAAQ,GAAG,EAAE;IACbC,gBAAgB,GAAG5E,KAAK,CAAC6C,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC;;EAEjD,IAAI,CAAC+B,gBAAgB,CAACA,gBAAgB,CAACjF,MAAM,GAAG,CAAC,CAAC,EAAE;IAClDiF,gBAAgB,CAACrB,GAAG,EAAE;EACxB,CAAC,CAAC;;EAGF,KAAK,IAAIX,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgC,gBAAgB,CAACjF,MAAM,EAAEiD,CAAC,EAAE,EAAE;IAChD,IAAIiC,IAAI,GAAGD,gBAAgB,CAAChC,CAAC,CAAC;IAE9B,IAAIA,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAACnD,OAAO,CAACqF,cAAc,EAAE;MACzCH,QAAQ,CAACA,QAAQ,CAAChF,MAAM,GAAG,CAAC,CAAC,IAAIkF,IAAI;IACvC,CAAC,MAAM;MACL,IAAI,IAAI,CAACpF,OAAO,CAAC2E,gBAAgB,EAAE;QACjCS,IAAI,GAAGA,IAAI,CAACE,IAAI,EAAE;MACpB;MAEAJ,QAAQ,CAACxC,IAAI,CAAC0C,IAAI,CAAC;IACrB;EACF;EAEA,OAAOF,QAAQ;AACjB,CAAC;AAED,SAASK,SAASA,CAACrB,MAAM,EAAEC,MAAM,EAAE/D,QAAQ,EAAE;EAC3C,OAAO6E,QAAQ,CAACpF,IAAI,CAACqE,MAAM,EAAEC,MAAM,EAAE/D,QAAQ,CAAC;AAChD;AACA,SAASoF,gBAAgBA,CAACtB,MAAM,EAAEC,MAAM,EAAE/D,QAAQ,EAAE;EAClD,IAAIJ,OAAO,GAAGoE,eAAe,CAAChE,QAAQ,EAAE;IACtCuE,gBAAgB,EAAE;EACpB,CAAC,CAAC;EACF,OAAOM,QAAQ,CAACpF,IAAI,CAACqE,MAAM,EAAEC,MAAM,EAAEnE,OAAO,CAAC;AAC/C;AAEA,IAAIyF,YAAY,GAAG,IAAI9F,IAAI,EAAE;AAE7B8F,YAAY,CAAC9E,QAAQ,GAAG,UAAUJ,KAAK,EAAE;EACvC,OAAOA,KAAK,CAAC6C,KAAK,CAAC,uBAAuB,CAAC;AAC7C,CAAC;AAED,SAASsC,aAAaA,CAACxB,MAAM,EAAEC,MAAM,EAAE/D,QAAQ,EAAE;EAC/C,OAAOqF,YAAY,CAAC5F,IAAI,CAACqE,MAAM,EAAEC,MAAM,EAAE/D,QAAQ,CAAC;AACpD;AAEA,IAAIuF,OAAO,GAAG,IAAIhG,IAAI,EAAE;AAExBgG,OAAO,CAAChF,QAAQ,GAAG,UAAUJ,KAAK,EAAE;EAClC,OAAOA,KAAK,CAAC6C,KAAK,CAAC,eAAe,CAAC;AACrC,CAAC;AAED,SAASwC,OAAOA,CAAC1B,MAAM,EAAEC,MAAM,EAAE/D,QAAQ,EAAE;EACzC,OAAOuF,OAAO,CAAC9F,IAAI,CAACqE,MAAM,EAAEC,MAAM,EAAE/D,QAAQ,CAAC;AAC/C;AAEA,SAASyF,OAAOA,CAACC,GAAG,EAAE;EACpB,yBAAyB;;EAEzB,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAI,OAAOA,MAAM,CAACC,QAAQ,KAAK,QAAQ,EAAE;IACvEH,OAAO,GAAG,SAAAA,CAAUC,GAAG,EAAE;MACvB,OAAO,OAAOA,GAAG;IACnB,CAAC;EACH,CAAC,MAAM;IACLD,OAAO,GAAG,SAAAA,CAAUC,GAAG,EAAE;MACvB,OAAOA,GAAG,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAID,GAAG,CAACG,WAAW,KAAKF,MAAM,IAAID,GAAG,KAAKC,MAAM,CAACnG,SAAS,GAAG,QAAQ,GAAG,OAAOkG,GAAG;IAC9H,CAAC;EACH;EAEA,OAAOD,OAAO,CAACC,GAAG,CAAC;AACrB;AAEA,SAASI,kBAAkBA,CAACC,GAAG,EAAE;EAC/B,OAAOC,kBAAkB,CAACD,GAAG,CAAC,IAAIE,gBAAgB,CAACF,GAAG,CAAC,IAAIG,2BAA2B,CAACH,GAAG,CAAC,IAAII,kBAAkB,EAAE;AACrH;AAEA,SAASH,kBAAkBA,CAACD,GAAG,EAAE;EAC/B,IAAIK,KAAK,CAACC,OAAO,CAACN,GAAG,CAAC,EAAE,OAAOO,iBAAiB,CAACP,GAAG,CAAC;AACvD;AAEA,SAASE,gBAAgBA,CAACM,IAAI,EAAE;EAC9B,IAAI,OAAOZ,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,QAAQ,IAAIY,MAAM,CAACD,IAAI,CAAC,EAAE,OAAOH,KAAK,CAACK,IAAI,CAACF,IAAI,CAAC;AAC/F;AAEA,SAASL,2BAA2BA,CAACQ,CAAC,EAAEC,MAAM,EAAE;EAC9C,IAAI,CAACD,CAAC,EAAE;EACR,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE,OAAOJ,iBAAiB,CAACI,CAAC,EAAEC,MAAM,CAAC;EAC9D,IAAIC,CAAC,GAAGJ,MAAM,CAAChH,SAAS,CAACqH,QAAQ,CAACC,IAAI,CAACJ,CAAC,CAAC,CAACrD,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACtD,IAAIuD,CAAC,KAAK,QAAQ,IAAIF,CAAC,CAACb,WAAW,EAAEe,CAAC,GAAGF,CAAC,CAACb,WAAW,CAAC3B,IAAI;EAC3D,IAAI0C,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,KAAK,EAAE,OAAOR,KAAK,CAACK,IAAI,CAACC,CAAC,CAAC;EACpD,IAAIE,CAAC,KAAK,WAAW,IAAI,0CAA0C,CAACpC,IAAI,CAACoC,CAAC,CAAC,EAAE,OAAON,iBAAiB,CAACI,CAAC,EAAEC,MAAM,CAAC;AAClH;AAEA,SAASL,iBAAiBA,CAACP,GAAG,EAAEgB,GAAG,EAAE;EACnC,IAAIA,GAAG,IAAI,IAAI,IAAIA,GAAG,GAAGhB,GAAG,CAACjG,MAAM,EAAEiH,GAAG,GAAGhB,GAAG,CAACjG,MAAM;EAErD,KAAK,IAAIiD,CAAC,GAAG,CAAC,EAAEiE,IAAI,GAAG,IAAIZ,KAAK,CAACW,GAAG,CAAC,EAAEhE,CAAC,GAAGgE,GAAG,EAAEhE,CAAC,EAAE,EAAEiE,IAAI,CAACjE,CAAC,CAAC,GAAGgD,GAAG,CAAChD,CAAC,CAAC;EAErE,OAAOiE,IAAI;AACb;AAEA,SAASb,kBAAkBA,CAAA,EAAG;EAC5B,MAAM,IAAIc,SAAS,CAAC,sIAAsI,CAAC;AAC7J;AAEA,IAAIC,uBAAuB,GAAGV,MAAM,CAAChH,SAAS,CAACqH,QAAQ;AACvD,IAAIM,QAAQ,GAAG,IAAI5H,IAAI,EAAE,CAAC,CAAC;AAC3B;;AAEA4H,QAAQ,CAACnF,eAAe,GAAG,IAAI;AAC/BmF,QAAQ,CAAC5G,QAAQ,GAAGsE,QAAQ,CAACtE,QAAQ;AAErC4G,QAAQ,CAAC9G,SAAS,GAAG,UAAUF,KAAK,EAAE;EACpC,IAAIiH,aAAa,GAAG,IAAI,CAACxH,OAAO;IAC5ByH,oBAAoB,GAAGD,aAAa,CAACC,oBAAoB;IACzDC,qBAAqB,GAAGF,aAAa,CAACG,iBAAiB;IACvDA,iBAAiB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,UAAUE,CAAC,EAAEC,CAAC,EAAE;MACzE,OAAO,OAAOA,CAAC,KAAK,WAAW,GAAGJ,oBAAoB,GAAGI,CAAC;IAC5D,CAAC,GAAGH,qBAAqB;EACzB,OAAO,OAAOnH,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAGuH,IAAI,CAACC,SAAS,CAACC,YAAY,CAACzH,KAAK,EAAE,IAAI,EAAE,IAAI,EAAEoH,iBAAiB,CAAC,EAAEA,iBAAiB,EAAE,IAAI,CAAC;AACxI,CAAC;AAEDJ,QAAQ,CAAC3E,MAAM,GAAG,UAAUC,IAAI,EAAEC,KAAK,EAAE;EACvC,OAAOnD,IAAI,CAACC,SAAS,CAACgD,MAAM,CAACsE,IAAI,CAACK,QAAQ,EAAE1E,IAAI,CAACoF,OAAO,CAAC,YAAY,EAAE,IAAI,CAAC,EAAEnF,KAAK,CAACmF,OAAO,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;AAClH,CAAC;AAED,SAASC,QAAQA,CAACC,MAAM,EAAEC,MAAM,EAAEpI,OAAO,EAAE;EACzC,OAAOuH,QAAQ,CAAC1H,IAAI,CAACsI,MAAM,EAAEC,MAAM,EAAEpI,OAAO,CAAC;AAC/C,CAAC,CAAC;AACF;;AAEA,SAASgI,YAAYA,CAAClC,GAAG,EAAEuC,KAAK,EAAEC,gBAAgB,EAAEC,QAAQ,EAAEC,GAAG,EAAE;EACjEH,KAAK,GAAGA,KAAK,IAAI,EAAE;EACnBC,gBAAgB,GAAGA,gBAAgB,IAAI,EAAE;EAEzC,IAAIC,QAAQ,EAAE;IACZzC,GAAG,GAAGyC,QAAQ,CAACC,GAAG,EAAE1C,GAAG,CAAC;EAC1B;EAEA,IAAI3C,CAAC;EAEL,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkF,KAAK,CAACnI,MAAM,EAAEiD,CAAC,IAAI,CAAC,EAAE;IACpC,IAAIkF,KAAK,CAAClF,CAAC,CAAC,KAAK2C,GAAG,EAAE;MACpB,OAAOwC,gBAAgB,CAACnF,CAAC,CAAC;IAC5B;EACF;EAEA,IAAIsF,gBAAgB;EAEpB,IAAI,gBAAgB,KAAKnB,uBAAuB,CAACJ,IAAI,CAACpB,GAAG,CAAC,EAAE;IAC1DuC,KAAK,CAAC3F,IAAI,CAACoD,GAAG,CAAC;IACf2C,gBAAgB,GAAG,IAAIjC,KAAK,CAACV,GAAG,CAAC5F,MAAM,CAAC;IACxCoI,gBAAgB,CAAC5F,IAAI,CAAC+F,gBAAgB,CAAC;IAEvC,KAAKtF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2C,GAAG,CAAC5F,MAAM,EAAEiD,CAAC,IAAI,CAAC,EAAE;MAClCsF,gBAAgB,CAACtF,CAAC,CAAC,GAAG6E,YAAY,CAAClC,GAAG,CAAC3C,CAAC,CAAC,EAAEkF,KAAK,EAAEC,gBAAgB,EAAEC,QAAQ,EAAEC,GAAG,CAAC;IACpF;IAEAH,KAAK,CAACvE,GAAG,EAAE;IACXwE,gBAAgB,CAACxE,GAAG,EAAE;IACtB,OAAO2E,gBAAgB;EACzB;EAEA,IAAI3C,GAAG,IAAIA,GAAG,CAAC4C,MAAM,EAAE;IACrB5C,GAAG,GAAGA,GAAG,CAAC4C,MAAM,EAAE;EACpB;EAEA,IAAI7C,OAAO,CAACC,GAAG,CAAC,KAAK,QAAQ,IAAIA,GAAG,KAAK,IAAI,EAAE;IAC7CuC,KAAK,CAAC3F,IAAI,CAACoD,GAAG,CAAC;IACf2C,gBAAgB,GAAG,CAAC,CAAC;IACrBH,gBAAgB,CAAC5F,IAAI,CAAC+F,gBAAgB,CAAC;IAEvC,IAAIE,UAAU,GAAG,EAAE;MACfC,IAAI;IAER,KAAKA,IAAI,IAAI9C,GAAG,EAAE;MAChB;MACA,IAAIA,GAAG,CAACvB,cAAc,CAACqE,IAAI,CAAC,EAAE;QAC5BD,UAAU,CAACjG,IAAI,CAACkG,IAAI,CAAC;MACvB;IACF;IAEAD,UAAU,CAACE,IAAI,EAAE;IAEjB,KAAK1F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwF,UAAU,CAACzI,MAAM,EAAEiD,CAAC,IAAI,CAAC,EAAE;MACzCyF,IAAI,GAAGD,UAAU,CAACxF,CAAC,CAAC;MACpBsF,gBAAgB,CAACG,IAAI,CAAC,GAAGZ,YAAY,CAAClC,GAAG,CAAC8C,IAAI,CAAC,EAAEP,KAAK,EAAEC,gBAAgB,EAAEC,QAAQ,EAAEK,IAAI,CAAC;IAC3F;IAEAP,KAAK,CAACvE,GAAG,EAAE;IACXwE,gBAAgB,CAACxE,GAAG,EAAE;EACxB,CAAC,MAAM;IACL2E,gBAAgB,GAAG3C,GAAG;EACxB;EAEA,OAAO2C,gBAAgB;AACzB;AAEA,IAAIK,SAAS,GAAG,IAAInJ,IAAI,EAAE;AAE1BmJ,SAAS,CAACnI,QAAQ,GAAG,UAAUJ,KAAK,EAAE;EACpC,OAAOA,KAAK,CAACkD,KAAK,EAAE;AACtB,CAAC;AAEDqF,SAAS,CAACvH,IAAI,GAAGuH,SAAS,CAACpI,WAAW,GAAG,UAAUH,KAAK,EAAE;EACxD,OAAOA,KAAK;AACd,CAAC;AAED,SAASwI,UAAUA,CAACC,MAAM,EAAEC,MAAM,EAAE7I,QAAQ,EAAE;EAC5C,OAAO0I,SAAS,CAACjJ,IAAI,CAACmJ,MAAM,EAAEC,MAAM,EAAE7I,QAAQ,CAAC;AACjD;AAEA,SAAS8I,UAAUA,CAACC,OAAO,EAAE;EAC3B,IAAInJ,OAAO,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACpF,IAAImJ,OAAO,GAAGD,OAAO,CAAC/F,KAAK,CAAC,qBAAqB,CAAC;IAC9CiG,UAAU,GAAGF,OAAO,CAACG,KAAK,CAAC,sBAAsB,CAAC,IAAI,EAAE;IACxDC,IAAI,GAAG,EAAE;IACTpG,CAAC,GAAG,CAAC;EAET,SAASqG,UAAUA,CAAA,EAAG;IACpB,IAAIC,KAAK,GAAG,CAAC,CAAC;IACdF,IAAI,CAAC7G,IAAI,CAAC+G,KAAK,CAAC,CAAC,CAAC;;IAElB,OAAOtG,CAAC,GAAGiG,OAAO,CAAClJ,MAAM,EAAE;MACzB,IAAIkF,IAAI,GAAGgE,OAAO,CAACjG,CAAC,CAAC,CAAC,CAAC;;MAEvB,IAAI,uBAAuB,CAACyB,IAAI,CAACQ,IAAI,CAAC,EAAE;QACtC;MACF,CAAC,CAAC;;MAGF,IAAIsE,MAAM,GAAG,0CAA0C,CAACrH,IAAI,CAAC+C,IAAI,CAAC;MAElE,IAAIsE,MAAM,EAAE;QACVD,KAAK,CAACA,KAAK,GAAGC,MAAM,CAAC,CAAC,CAAC;MACzB;MAEAvG,CAAC,EAAE;IACL,CAAC,CAAC;IACF;;IAGAwG,eAAe,CAACF,KAAK,CAAC;IACtBE,eAAe,CAACF,KAAK,CAAC,CAAC,CAAC;;IAExBA,KAAK,CAACG,KAAK,GAAG,EAAE;IAEhB,OAAOzG,CAAC,GAAGiG,OAAO,CAAClJ,MAAM,EAAE;MACzB,IAAI2J,KAAK,GAAGT,OAAO,CAACjG,CAAC,CAAC;MAEtB,IAAI,gCAAgC,CAACyB,IAAI,CAACiF,KAAK,CAAC,EAAE;QAChD;MACF,CAAC,MAAM,IAAI,KAAK,CAACjF,IAAI,CAACiF,KAAK,CAAC,EAAE;QAC5BJ,KAAK,CAACG,KAAK,CAAClH,IAAI,CAACoH,SAAS,EAAE,CAAC;MAC/B,CAAC,MAAM,IAAID,KAAK,IAAI7J,OAAO,CAAC+J,MAAM,EAAE;QAClC;QACA,MAAM,IAAIC,KAAK,CAAC,eAAe,IAAI7G,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG2E,IAAI,CAACC,SAAS,CAAC8B,KAAK,CAAC,CAAC;MAC1E,CAAC,MAAM;QACL1G,CAAC,EAAE;MACL;IACF;EACF,CAAC,CAAC;EACF;;EAGA,SAASwG,eAAeA,CAACF,KAAK,EAAE;IAC9B,IAAIQ,UAAU,GAAG,uBAAuB,CAAC5H,IAAI,CAAC+G,OAAO,CAACjG,CAAC,CAAC,CAAC;IAEzD,IAAI8G,UAAU,EAAE;MACd,IAAIC,SAAS,GAAGD,UAAU,CAAC,CAAC,CAAC,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK;MACvD,IAAIE,IAAI,GAAGF,UAAU,CAAC,CAAC,CAAC,CAAC7G,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;MACvC,IAAIgH,QAAQ,GAAGD,IAAI,CAAC,CAAC,CAAC,CAAClC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC;MAE7C,IAAI,QAAQ,CAACrD,IAAI,CAACwF,QAAQ,CAAC,EAAE;QAC3BA,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAAC,CAAC,EAAED,QAAQ,CAAClK,MAAM,GAAG,CAAC,CAAC;MACpD;MAEAuJ,KAAK,CAACS,SAAS,GAAG,UAAU,CAAC,GAAGE,QAAQ;MACxCX,KAAK,CAACS,SAAS,GAAG,QAAQ,CAAC,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,EAAE7E,IAAI,EAAE;MACpDnC,CAAC,EAAE;IACL;EACF,CAAC,CAAC;EACF;;EAGA,SAAS2G,SAASA,CAAA,EAAG;IACnB,IAAIQ,gBAAgB,GAAGnH,CAAC;MACpBoH,eAAe,GAAGnB,OAAO,CAACjG,CAAC,EAAE,CAAC;MAC9BqH,WAAW,GAAGD,eAAe,CAACnH,KAAK,CAAC,4CAA4C,CAAC;IACrF,IAAIqH,IAAI,GAAG;MACTC,QAAQ,EAAE,CAACF,WAAW,CAAC,CAAC,CAAC;MACzBG,QAAQ,EAAE,OAAOH,WAAW,CAAC,CAAC,CAAC,KAAK,WAAW,GAAG,CAAC,GAAG,CAACA,WAAW,CAAC,CAAC,CAAC;MACrEI,QAAQ,EAAE,CAACJ,WAAW,CAAC,CAAC,CAAC;MACzBK,QAAQ,EAAE,OAAOL,WAAW,CAAC,CAAC,CAAC,KAAK,WAAW,GAAG,CAAC,GAAG,CAACA,WAAW,CAAC,CAAC,CAAC;MACrEM,KAAK,EAAE,EAAE;MACTC,cAAc,EAAE;IAClB,CAAC,CAAC,CAAC;IACH;IACA;;IAEA,IAAIN,IAAI,CAACE,QAAQ,KAAK,CAAC,EAAE;MACvBF,IAAI,CAACC,QAAQ,IAAI,CAAC;IACpB;IAEA,IAAID,IAAI,CAACI,QAAQ,KAAK,CAAC,EAAE;MACvBJ,IAAI,CAACG,QAAQ,IAAI,CAAC;IACpB;IAEA,IAAII,QAAQ,GAAG,CAAC;MACZC,WAAW,GAAG,CAAC;IAEnB,OAAO9H,CAAC,GAAGiG,OAAO,CAAClJ,MAAM,EAAEiD,CAAC,EAAE,EAAE;MAC9B;MACA;MACA,IAAIiG,OAAO,CAACjG,CAAC,CAAC,CAAC+H,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI/H,CAAC,GAAG,CAAC,GAAGiG,OAAO,CAAClJ,MAAM,IAAIkJ,OAAO,CAACjG,CAAC,GAAG,CAAC,CAAC,CAAC+H,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI9B,OAAO,CAACjG,CAAC,GAAG,CAAC,CAAC,CAAC+H,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;QAC5I;MACF;MAEA,IAAIC,SAAS,GAAG/B,OAAO,CAACjG,CAAC,CAAC,CAACjD,MAAM,IAAI,CAAC,IAAIiD,CAAC,IAAIiG,OAAO,CAAClJ,MAAM,GAAG,CAAC,GAAG,GAAG,GAAGkJ,OAAO,CAACjG,CAAC,CAAC,CAAC,CAAC,CAAC;MAEvF,IAAIgI,SAAS,KAAK,GAAG,IAAIA,SAAS,KAAK,GAAG,IAAIA,SAAS,KAAK,GAAG,IAAIA,SAAS,KAAK,IAAI,EAAE;QACrFV,IAAI,CAACK,KAAK,CAACpI,IAAI,CAAC0G,OAAO,CAACjG,CAAC,CAAC,CAAC;QAC3BsH,IAAI,CAACM,cAAc,CAACrI,IAAI,CAAC2G,UAAU,CAAClG,CAAC,CAAC,IAAI,IAAI,CAAC;QAE/C,IAAIgI,SAAS,KAAK,GAAG,EAAE;UACrBH,QAAQ,EAAE;QACZ,CAAC,MAAM,IAAIG,SAAS,KAAK,GAAG,EAAE;UAC5BF,WAAW,EAAE;QACf,CAAC,MAAM,IAAIE,SAAS,KAAK,GAAG,EAAE;UAC5BH,QAAQ,EAAE;UACVC,WAAW,EAAE;QACf;MACF,CAAC,MAAM;QACL;MACF;IACF,CAAC,CAAC;;IAGF,IAAI,CAACD,QAAQ,IAAIP,IAAI,CAACI,QAAQ,KAAK,CAAC,EAAE;MACpCJ,IAAI,CAACI,QAAQ,GAAG,CAAC;IACnB;IAEA,IAAI,CAACI,WAAW,IAAIR,IAAI,CAACE,QAAQ,KAAK,CAAC,EAAE;MACvCF,IAAI,CAACE,QAAQ,GAAG,CAAC;IACnB,CAAC,CAAC;;IAGF,IAAI3K,OAAO,CAAC+J,MAAM,EAAE;MAClB,IAAIiB,QAAQ,KAAKP,IAAI,CAACI,QAAQ,EAAE;QAC9B,MAAM,IAAIb,KAAK,CAAC,kDAAkD,IAAIM,gBAAgB,GAAG,CAAC,CAAC,CAAC;MAC9F;MAEA,IAAIW,WAAW,KAAKR,IAAI,CAACE,QAAQ,EAAE;QACjC,MAAM,IAAIX,KAAK,CAAC,oDAAoD,IAAIM,gBAAgB,GAAG,CAAC,CAAC,CAAC;MAChG;IACF;IAEA,OAAOG,IAAI;EACb;EAEA,OAAOtH,CAAC,GAAGiG,OAAO,CAAClJ,MAAM,EAAE;IACzBsJ,UAAU,EAAE;EACd;EAEA,OAAOD,IAAI;AACb;;AAEA;AACA;AACA;AACA,SAAS6B,gBAAgBA,CAAEC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAE;EAClD,IAAIC,WAAW,GAAG,IAAI;IAClBC,iBAAiB,GAAG,KAAK;IACzBC,gBAAgB,GAAG,KAAK;IACxBC,WAAW,GAAG,CAAC;EACnB,OAAO,SAAS3F,QAAQA,CAAA,EAAG;IACzB,IAAIwF,WAAW,IAAI,CAACE,gBAAgB,EAAE;MACpC,IAAID,iBAAiB,EAAE;QACrBE,WAAW,EAAE;MACf,CAAC,MAAM;QACLH,WAAW,GAAG,KAAK;MACrB,CAAC,CAAC;MACF;;MAGA,IAAIH,KAAK,GAAGM,WAAW,IAAIJ,OAAO,EAAE;QAClC,OAAOI,WAAW;MACpB;MAEAD,gBAAgB,GAAG,IAAI;IACzB;IAEA,IAAI,CAACD,iBAAiB,EAAE;MACtB,IAAI,CAACC,gBAAgB,EAAE;QACrBF,WAAW,GAAG,IAAI;MACpB,CAAC,CAAC;MACF;;MAGA,IAAIF,OAAO,IAAID,KAAK,GAAGM,WAAW,EAAE;QAClC,OAAO,CAACA,WAAW,EAAE;MACvB;MAEAF,iBAAiB,GAAG,IAAI;MACxB,OAAOzF,QAAQ,EAAE;IACnB,CAAC,CAAC;IACF;EAEF,CAAC;AACH;;AAEA,SAAS4F,UAAUA,CAACC,MAAM,EAAE1C,OAAO,EAAE;EACnC,IAAInJ,OAAO,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EAEpF,IAAI,OAAOkJ,OAAO,KAAK,QAAQ,EAAE;IAC/BA,OAAO,GAAGD,UAAU,CAACC,OAAO,CAAC;EAC/B;EAEA,IAAI3C,KAAK,CAACC,OAAO,CAAC0C,OAAO,CAAC,EAAE;IAC1B,IAAIA,OAAO,CAACjJ,MAAM,GAAG,CAAC,EAAE;MACtB,MAAM,IAAI8J,KAAK,CAAC,4CAA4C,CAAC;IAC/D;IAEAb,OAAO,GAAGA,OAAO,CAAC,CAAC,CAAC;EACtB,CAAC,CAAC;;EAGF,IAAI2B,KAAK,GAAGe,MAAM,CAACzI,KAAK,CAAC,qBAAqB,CAAC;IAC3CiG,UAAU,GAAGwC,MAAM,CAACvC,KAAK,CAAC,sBAAsB,CAAC,IAAI,EAAE;IACvDM,KAAK,GAAGT,OAAO,CAACS,KAAK;IACrBkC,WAAW,GAAG9L,OAAO,CAAC8L,WAAW,IAAI,UAAUC,UAAU,EAAE3G,IAAI,EAAE+F,SAAS,EAAEa,YAAY,EAAE;MAC5F,OAAO5G,IAAI,KAAK4G,YAAY;IAC9B,CAAC;IACGC,UAAU,GAAG,CAAC;IACdC,UAAU,GAAGlM,OAAO,CAACkM,UAAU,IAAI,CAAC;IACpCZ,OAAO,GAAG,CAAC;IACXa,MAAM,GAAG,CAAC;IACVC,WAAW;IACXC,QAAQ;EACZ;AACF;AACA;;EAGE,SAASC,QAAQA,CAAC7B,IAAI,EAAE8B,KAAK,EAAE;IAC7B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG/B,IAAI,CAACK,KAAK,CAAC5K,MAAM,EAAEsM,CAAC,EAAE,EAAE;MAC1C,IAAIpH,IAAI,GAAGqF,IAAI,CAACK,KAAK,CAAC0B,CAAC,CAAC;QACpBrB,SAAS,GAAG/F,IAAI,CAAClF,MAAM,GAAG,CAAC,GAAGkF,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG;QAC3CqH,OAAO,GAAGrH,IAAI,CAAClF,MAAM,GAAG,CAAC,GAAGkF,IAAI,CAACiF,MAAM,CAAC,CAAC,CAAC,GAAGjF,IAAI;MAErD,IAAI+F,SAAS,KAAK,GAAG,IAAIA,SAAS,KAAK,GAAG,EAAE;QAC1C;QACA,IAAI,CAACW,WAAW,CAACS,KAAK,GAAG,CAAC,EAAEzB,KAAK,CAACyB,KAAK,CAAC,EAAEpB,SAAS,EAAEsB,OAAO,CAAC,EAAE;UAC7DR,UAAU,EAAE;UAEZ,IAAIA,UAAU,GAAGC,UAAU,EAAE;YAC3B,OAAO,KAAK;UACd;QACF;QAEAK,KAAK,EAAE;MACT;IACF;IAEA,OAAO,IAAI;EACb,CAAC,CAAC;;EAGF,KAAK,IAAIpJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyG,KAAK,CAAC1J,MAAM,EAAEiD,CAAC,EAAE,EAAE;IACrC,IAAIsH,IAAI,GAAGb,KAAK,CAACzG,CAAC,CAAC;MACfoI,OAAO,GAAGT,KAAK,CAAC5K,MAAM,GAAGuK,IAAI,CAACE,QAAQ;MACtCgB,WAAW,GAAG,CAAC;MACfY,KAAK,GAAGJ,MAAM,GAAG1B,IAAI,CAACC,QAAQ,GAAG,CAAC;IACtC,IAAI1E,QAAQ,GAAGoF,gBAAgB,CAACmB,KAAK,EAAEjB,OAAO,EAAEC,OAAO,CAAC;IAExD,OAAOI,WAAW,KAAKxL,SAAS,EAAEwL,WAAW,GAAG3F,QAAQ,EAAE,EAAE;MAC1D,IAAIsG,QAAQ,CAAC7B,IAAI,EAAE8B,KAAK,GAAGZ,WAAW,CAAC,EAAE;QACvClB,IAAI,CAAC0B,MAAM,GAAGA,MAAM,IAAIR,WAAW;QACnC;MACF;IACF;IAEA,IAAIA,WAAW,KAAKxL,SAAS,EAAE;MAC7B,OAAO,KAAK;IACd,CAAC,CAAC;IACF;;IAGAmL,OAAO,GAAGb,IAAI,CAAC0B,MAAM,GAAG1B,IAAI,CAACC,QAAQ,GAAGD,IAAI,CAACE,QAAQ;EACvD,CAAC,CAAC;;EAGF,IAAI+B,UAAU,GAAG,CAAC;EAElB,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAG/C,KAAK,CAAC1J,MAAM,EAAEyM,EAAE,EAAE,EAAE;IACxC,IAAIC,KAAK,GAAGhD,KAAK,CAAC+C,EAAE,CAAC;MACjBE,MAAM,GAAGD,KAAK,CAAClC,QAAQ,GAAGkC,KAAK,CAACT,MAAM,GAAGO,UAAU,GAAG,CAAC;IAE3DA,UAAU,IAAIE,KAAK,CAAC/B,QAAQ,GAAG+B,KAAK,CAACjC,QAAQ;IAE7C,KAAK,IAAI6B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGI,KAAK,CAAC9B,KAAK,CAAC5K,MAAM,EAAEsM,CAAC,EAAE,EAAE;MAC3C,IAAIpH,IAAI,GAAGwH,KAAK,CAAC9B,KAAK,CAAC0B,CAAC,CAAC;QACrBrB,SAAS,GAAG/F,IAAI,CAAClF,MAAM,GAAG,CAAC,GAAGkF,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG;QAC3CqH,OAAO,GAAGrH,IAAI,CAAClF,MAAM,GAAG,CAAC,GAAGkF,IAAI,CAACiF,MAAM,CAAC,CAAC,CAAC,GAAGjF,IAAI;QACjD0H,SAAS,GAAGF,KAAK,CAAC7B,cAAc,CAACyB,CAAC,CAAC;MAEvC,IAAIrB,SAAS,KAAK,GAAG,EAAE;QACrB0B,MAAM,EAAE;MACV,CAAC,MAAM,IAAI1B,SAAS,KAAK,GAAG,EAAE;QAC5BL,KAAK,CAAChG,MAAM,CAAC+H,MAAM,EAAE,CAAC,CAAC;QACvBxD,UAAU,CAACvE,MAAM,CAAC+H,MAAM,EAAE,CAAC,CAAC;QAC5B;MACF,CAAC,MAAM,IAAI1B,SAAS,KAAK,GAAG,EAAE;QAC5BL,KAAK,CAAChG,MAAM,CAAC+H,MAAM,EAAE,CAAC,EAAEJ,OAAO,CAAC;QAChCpD,UAAU,CAACvE,MAAM,CAAC+H,MAAM,EAAE,CAAC,EAAEC,SAAS,CAAC;QACvCD,MAAM,EAAE;MACV,CAAC,MAAM,IAAI1B,SAAS,KAAK,IAAI,EAAE;QAC7B,IAAI4B,iBAAiB,GAAGH,KAAK,CAAC9B,KAAK,CAAC0B,CAAC,GAAG,CAAC,CAAC,GAAGI,KAAK,CAAC9B,KAAK,CAAC0B,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI;QAEzE,IAAIO,iBAAiB,KAAK,GAAG,EAAE;UAC7BX,WAAW,GAAG,IAAI;QACpB,CAAC,MAAM,IAAIW,iBAAiB,KAAK,GAAG,EAAE;UACpCV,QAAQ,GAAG,IAAI;QACjB;MACF;IACF;EACF,CAAC,CAAC;;EAGF,IAAID,WAAW,EAAE;IACf,OAAO,CAACtB,KAAK,CAACA,KAAK,CAAC5K,MAAM,GAAG,CAAC,CAAC,EAAE;MAC/B4K,KAAK,CAAChH,GAAG,EAAE;MACXuF,UAAU,CAACvF,GAAG,EAAE;IAClB;EACF,CAAC,MAAM,IAAIuI,QAAQ,EAAE;IACnBvB,KAAK,CAACpI,IAAI,CAAC,EAAE,CAAC;IACd2G,UAAU,CAAC3G,IAAI,CAAC,IAAI,CAAC;EACvB;EAEA,KAAK,IAAIsK,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGlC,KAAK,CAAC5K,MAAM,GAAG,CAAC,EAAE8M,EAAE,EAAE,EAAE;IAC5ClC,KAAK,CAACkC,EAAE,CAAC,GAAGlC,KAAK,CAACkC,EAAE,CAAC,GAAG3D,UAAU,CAAC2D,EAAE,CAAC;EACxC;EAEA,OAAOlC,KAAK,CAACvJ,IAAI,CAAC,EAAE,CAAC;AACvB,CAAC,CAAC;;AAEF,SAAS0L,YAAYA,CAAC9D,OAAO,EAAEnJ,OAAO,EAAE;EACtC,IAAI,OAAOmJ,OAAO,KAAK,QAAQ,EAAE;IAC/BA,OAAO,GAAGD,UAAU,CAACC,OAAO,CAAC;EAC/B;EAEA,IAAI+D,YAAY,GAAG,CAAC;EAEpB,SAASC,YAAYA,CAAA,EAAG;IACtB,IAAI1D,KAAK,GAAGN,OAAO,CAAC+D,YAAY,EAAE,CAAC;IAEnC,IAAI,CAACzD,KAAK,EAAE;MACV,OAAOzJ,OAAO,CAACoN,QAAQ,EAAE;IAC3B;IAEApN,OAAO,CAACqN,QAAQ,CAAC5D,KAAK,EAAE,UAAU6D,GAAG,EAAEnD,IAAI,EAAE;MAC3C,IAAImD,GAAG,EAAE;QACP,OAAOtN,OAAO,CAACoN,QAAQ,CAACE,GAAG,CAAC;MAC9B;MAEA,IAAIC,cAAc,GAAG3B,UAAU,CAACzB,IAAI,EAAEV,KAAK,EAAEzJ,OAAO,CAAC;MACrDA,OAAO,CAACwN,OAAO,CAAC/D,KAAK,EAAE8D,cAAc,EAAE,UAAUD,GAAG,EAAE;QACpD,IAAIA,GAAG,EAAE;UACP,OAAOtN,OAAO,CAACoN,QAAQ,CAACE,GAAG,CAAC;QAC9B;QAEAH,YAAY,EAAE;MAChB,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEAA,YAAY,EAAE;AAChB;AAEA,SAASM,eAAeA,CAACC,WAAW,EAAEC,WAAW,EAAEzJ,MAAM,EAAEC,MAAM,EAAEyJ,SAAS,EAAEC,SAAS,EAAE7N,OAAO,EAAE;EAChG,IAAI,CAACA,OAAO,EAAE;IACZA,OAAO,GAAG,CAAC,CAAC;EACd;EAEA,IAAI,OAAOA,OAAO,CAAC8N,OAAO,KAAK,WAAW,EAAE;IAC1C9N,OAAO,CAAC8N,OAAO,GAAG,CAAC;EACrB;EAEA,IAAIjO,IAAI,GAAG0F,SAAS,CAACrB,MAAM,EAAEC,MAAM,EAAEnE,OAAO,CAAC;EAE7C,IAAI,CAACH,IAAI,EAAE;IACT;EACF;EAEAA,IAAI,CAAC6C,IAAI,CAAC;IACRnC,KAAK,EAAE,EAAE;IACTuK,KAAK,EAAE;EACT,CAAC,CAAC,CAAC,CAAC;;EAEJ,SAASiD,YAAYA,CAACjD,KAAK,EAAE;IAC3B,OAAOA,KAAK,CAACpH,GAAG,CAAC,UAAUsK,KAAK,EAAE;MAChC,OAAO,GAAG,GAAGA,KAAK;IACpB,CAAC,CAAC;EACJ;EAEA,IAAIpE,KAAK,GAAG,EAAE;EACd,IAAIqE,aAAa,GAAG,CAAC;IACjBC,aAAa,GAAG,CAAC;IACjBC,QAAQ,GAAG,EAAE;IACbC,OAAO,GAAG,CAAC;IACXC,OAAO,GAAG,CAAC;EAEf,IAAIC,KAAK,GAAG,SAASA,KAAKA,CAACnL,CAAC,EAAE;IAC5B,IAAIoL,OAAO,GAAG1O,IAAI,CAACsD,CAAC,CAAC;MACjB2H,KAAK,GAAGyD,OAAO,CAACzD,KAAK,IAAIyD,OAAO,CAAChO,KAAK,CAAC0H,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC7E,KAAK,CAAC,IAAI,CAAC;IACzEmL,OAAO,CAACzD,KAAK,GAAGA,KAAK;IAErB,IAAIyD,OAAO,CAAChM,KAAK,IAAIgM,OAAO,CAAC/L,OAAO,EAAE;MACpC,IAAIgM,SAAS;;MAEb;MACA,IAAI,CAACP,aAAa,EAAE;QAClB,IAAIQ,IAAI,GAAG5O,IAAI,CAACsD,CAAC,GAAG,CAAC,CAAC;QACtB8K,aAAa,GAAGG,OAAO;QACvBF,aAAa,GAAGG,OAAO;QAEvB,IAAII,IAAI,EAAE;UACRN,QAAQ,GAAGnO,OAAO,CAAC8N,OAAO,GAAG,CAAC,GAAGC,YAAY,CAACU,IAAI,CAAC3D,KAAK,CAACrH,KAAK,CAAC,CAACzD,OAAO,CAAC8N,OAAO,CAAC,CAAC,GAAG,EAAE;UACtFG,aAAa,IAAIE,QAAQ,CAACjO,MAAM;UAChCgO,aAAa,IAAIC,QAAQ,CAACjO,MAAM;QAClC;MACF,CAAC,CAAC;;MAGF,CAACsO,SAAS,GAAGL,QAAQ,EAAEzL,IAAI,CAACgM,KAAK,CAACF,SAAS,EAAEtI,kBAAkB,CAAC4E,KAAK,CAACpH,GAAG,CAAC,UAAUsK,KAAK,EAAE;QACzF,OAAO,CAACO,OAAO,CAAChM,KAAK,GAAG,GAAG,GAAG,GAAG,IAAIyL,KAAK;MAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAGN,IAAIO,OAAO,CAAChM,KAAK,EAAE;QACjB8L,OAAO,IAAIvD,KAAK,CAAC5K,MAAM;MACzB,CAAC,MAAM;QACLkO,OAAO,IAAItD,KAAK,CAAC5K,MAAM;MACzB;IACF,CAAC,MAAM;MACL;MACA,IAAI+N,aAAa,EAAE;QACjB;QACA,IAAInD,KAAK,CAAC5K,MAAM,IAAIF,OAAO,CAAC8N,OAAO,GAAG,CAAC,IAAI3K,CAAC,GAAGtD,IAAI,CAACK,MAAM,GAAG,CAAC,EAAE;UAC9D,IAAIyO,UAAU;;UAEd;UACA,CAACA,UAAU,GAAGR,QAAQ,EAAEzL,IAAI,CAACgM,KAAK,CAACC,UAAU,EAAEzI,kBAAkB,CAAC6H,YAAY,CAACjD,KAAK,CAAC,CAAC,CAAC;QACzF,CAAC,MAAM;UACL,IAAI8D,UAAU;;UAEd;UACA,IAAIC,WAAW,GAAG7N,IAAI,CAACC,GAAG,CAAC6J,KAAK,CAAC5K,MAAM,EAAEF,OAAO,CAAC8N,OAAO,CAAC;UAEzD,CAACc,UAAU,GAAGT,QAAQ,EAAEzL,IAAI,CAACgM,KAAK,CAACE,UAAU,EAAE1I,kBAAkB,CAAC6H,YAAY,CAACjD,KAAK,CAACrH,KAAK,CAAC,CAAC,EAAEoL,WAAW,CAAC,CAAC,CAAC,CAAC;UAE7G,IAAIpE,IAAI,GAAG;YACTC,QAAQ,EAAEuD,aAAa;YACvBtD,QAAQ,EAAEyD,OAAO,GAAGH,aAAa,GAAGY,WAAW;YAC/CjE,QAAQ,EAAEsD,aAAa;YACvBrD,QAAQ,EAAEwD,OAAO,GAAGH,aAAa,GAAGW,WAAW;YAC/C/D,KAAK,EAAEqD;UACT,CAAC;UAED,IAAIhL,CAAC,IAAItD,IAAI,CAACK,MAAM,GAAG,CAAC,IAAI4K,KAAK,CAAC5K,MAAM,IAAIF,OAAO,CAAC8N,OAAO,EAAE;YAC3D;YACA,IAAIgB,aAAa,GAAG,KAAK,CAAClK,IAAI,CAACV,MAAM,CAAC;YACtC,IAAI6K,aAAa,GAAG,KAAK,CAACnK,IAAI,CAACT,MAAM,CAAC;YACtC,IAAI6K,cAAc,GAAGlE,KAAK,CAAC5K,MAAM,IAAI,CAAC,IAAIiO,QAAQ,CAACjO,MAAM,GAAGuK,IAAI,CAACE,QAAQ;YAEzE,IAAI,CAACmE,aAAa,IAAIE,cAAc,IAAI9K,MAAM,CAAChE,MAAM,GAAG,CAAC,EAAE;cACzD;cACA;cACAiO,QAAQ,CAACrJ,MAAM,CAAC2F,IAAI,CAACE,QAAQ,EAAE,CAAC,EAAE,8BAA8B,CAAC;YACnE;YAEA,IAAI,CAACmE,aAAa,IAAI,CAACE,cAAc,IAAI,CAACD,aAAa,EAAE;cACvDZ,QAAQ,CAACzL,IAAI,CAAC,8BAA8B,CAAC;YAC/C;UACF;UAEAkH,KAAK,CAAClH,IAAI,CAAC+H,IAAI,CAAC;UAChBwD,aAAa,GAAG,CAAC;UACjBC,aAAa,GAAG,CAAC;UACjBC,QAAQ,GAAG,EAAE;QACf;MACF;MAEAC,OAAO,IAAItD,KAAK,CAAC5K,MAAM;MACvBmO,OAAO,IAAIvD,KAAK,CAAC5K,MAAM;IACzB;EACF,CAAC;EAED,KAAK,IAAIiD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtD,IAAI,CAACK,MAAM,EAAEiD,CAAC,EAAE,EAAE;IACpCmL,KAAK,CAACnL,CAAC,CAAC;EACV;EAEA,OAAO;IACLuK,WAAW,EAAEA,WAAW;IACxBC,WAAW,EAAEA,WAAW;IACxBC,SAAS,EAAEA,SAAS;IACpBC,SAAS,EAAEA,SAAS;IACpBjE,KAAK,EAAEA;EACT,CAAC;AACH;AACA,SAASqF,WAAWA,CAACpP,IAAI,EAAE;EACzB,IAAIyC,GAAG,GAAG,EAAE;EAEZ,IAAIzC,IAAI,CAAC6N,WAAW,IAAI7N,IAAI,CAAC8N,WAAW,EAAE;IACxCrL,GAAG,CAACI,IAAI,CAAC,SAAS,GAAG7C,IAAI,CAAC6N,WAAW,CAAC;EACxC;EAEApL,GAAG,CAACI,IAAI,CAAC,qEAAqE,CAAC;EAC/EJ,GAAG,CAACI,IAAI,CAAC,MAAM,GAAG7C,IAAI,CAAC6N,WAAW,IAAI,OAAO7N,IAAI,CAAC+N,SAAS,KAAK,WAAW,GAAG,EAAE,GAAG,IAAI,GAAG/N,IAAI,CAAC+N,SAAS,CAAC,CAAC;EAC1GtL,GAAG,CAACI,IAAI,CAAC,MAAM,GAAG7C,IAAI,CAAC8N,WAAW,IAAI,OAAO9N,IAAI,CAACgO,SAAS,KAAK,WAAW,GAAG,EAAE,GAAG,IAAI,GAAGhO,IAAI,CAACgO,SAAS,CAAC,CAAC;EAE1G,KAAK,IAAI1K,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtD,IAAI,CAAC+J,KAAK,CAAC1J,MAAM,EAAEiD,CAAC,EAAE,EAAE;IAC1C,IAAIsH,IAAI,GAAG5K,IAAI,CAAC+J,KAAK,CAACzG,CAAC,CAAC,CAAC,CAAC;IAC1B;IACA;;IAEA,IAAIsH,IAAI,CAACE,QAAQ,KAAK,CAAC,EAAE;MACvBF,IAAI,CAACC,QAAQ,IAAI,CAAC;IACpB;IAEA,IAAID,IAAI,CAACI,QAAQ,KAAK,CAAC,EAAE;MACvBJ,IAAI,CAACG,QAAQ,IAAI,CAAC;IACpB;IAEAtI,GAAG,CAACI,IAAI,CAAC,MAAM,GAAG+H,IAAI,CAACC,QAAQ,GAAG,GAAG,GAAGD,IAAI,CAACE,QAAQ,GAAG,IAAI,GAAGF,IAAI,CAACG,QAAQ,GAAG,GAAG,GAAGH,IAAI,CAACI,QAAQ,GAAG,KAAK,CAAC;IAC3GvI,GAAG,CAACI,IAAI,CAACgM,KAAK,CAACpM,GAAG,EAAEmI,IAAI,CAACK,KAAK,CAAC;EACjC;EAEA,OAAOxI,GAAG,CAACf,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI;AAC9B;AACA,SAAS2N,mBAAmBA,CAACxB,WAAW,EAAEC,WAAW,EAAEzJ,MAAM,EAAEC,MAAM,EAAEyJ,SAAS,EAAEC,SAAS,EAAE7N,OAAO,EAAE;EACpG,OAAOiP,WAAW,CAACxB,eAAe,CAACC,WAAW,EAAEC,WAAW,EAAEzJ,MAAM,EAAEC,MAAM,EAAEyJ,SAAS,EAAEC,SAAS,EAAE7N,OAAO,CAAC,CAAC;AAC9G;AACA,SAASmP,WAAWA,CAAC/E,QAAQ,EAAElG,MAAM,EAAEC,MAAM,EAAEyJ,SAAS,EAAEC,SAAS,EAAE7N,OAAO,EAAE;EAC5E,OAAOkP,mBAAmB,CAAC9E,QAAQ,EAAEA,QAAQ,EAAElG,MAAM,EAAEC,MAAM,EAAEyJ,SAAS,EAAEC,SAAS,EAAE7N,OAAO,CAAC;AAC/F;AAEA,SAASoP,UAAUA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACxB,IAAID,CAAC,CAACnP,MAAM,KAAKoP,CAAC,CAACpP,MAAM,EAAE;IACzB,OAAO,KAAK;EACd;EAEA,OAAOqP,eAAe,CAACF,CAAC,EAAEC,CAAC,CAAC;AAC9B;AACA,SAASC,eAAeA,CAACrM,KAAK,EAAEmI,KAAK,EAAE;EACrC,IAAIA,KAAK,CAACnL,MAAM,GAAGgD,KAAK,CAAChD,MAAM,EAAE;IAC/B,OAAO,KAAK;EACd;EAEA,KAAK,IAAIiD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkI,KAAK,CAACnL,MAAM,EAAEiD,CAAC,EAAE,EAAE;IACrC,IAAIkI,KAAK,CAAClI,CAAC,CAAC,KAAKD,KAAK,CAACC,CAAC,CAAC,EAAE;MACzB,OAAO,KAAK;IACd;EACF;EAEA,OAAO,IAAI;AACb;AAEA,SAASqM,aAAaA,CAAC/E,IAAI,EAAE;EAC3B,IAAIgF,oBAAoB,GAAGC,mBAAmB,CAACjF,IAAI,CAACK,KAAK,CAAC;IACtDH,QAAQ,GAAG8E,oBAAoB,CAAC9E,QAAQ;IACxCE,QAAQ,GAAG4E,oBAAoB,CAAC5E,QAAQ;EAE5C,IAAIF,QAAQ,KAAKxK,SAAS,EAAE;IAC1BsK,IAAI,CAACE,QAAQ,GAAGA,QAAQ;EAC1B,CAAC,MAAM;IACL,OAAOF,IAAI,CAACE,QAAQ;EACtB;EAEA,IAAIE,QAAQ,KAAK1K,SAAS,EAAE;IAC1BsK,IAAI,CAACI,QAAQ,GAAGA,QAAQ;EAC1B,CAAC,MAAM;IACL,OAAOJ,IAAI,CAACI,QAAQ;EACtB;AACF;AACA,SAAS8E,KAAKA,CAACC,IAAI,EAAEC,MAAM,EAAEC,IAAI,EAAE;EACjCF,IAAI,GAAGG,SAAS,CAACH,IAAI,EAAEE,IAAI,CAAC;EAC5BD,MAAM,GAAGE,SAAS,CAACF,MAAM,EAAEC,IAAI,CAAC;EAChC,IAAIxN,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;EACd;EACA;;EAEA,IAAIsN,IAAI,CAACnG,KAAK,IAAIoG,MAAM,CAACpG,KAAK,EAAE;IAC9BnH,GAAG,CAACmH,KAAK,GAAGmG,IAAI,CAACnG,KAAK,IAAIoG,MAAM,CAACpG,KAAK;EACxC;EAEA,IAAImG,IAAI,CAACjC,WAAW,IAAIkC,MAAM,CAAClC,WAAW,EAAE;IAC1C,IAAI,CAACqC,eAAe,CAACJ,IAAI,CAAC,EAAE;MAC1B;MACAtN,GAAG,CAACoL,WAAW,GAAGmC,MAAM,CAACnC,WAAW,IAAIkC,IAAI,CAAClC,WAAW;MACxDpL,GAAG,CAACqL,WAAW,GAAGkC,MAAM,CAAClC,WAAW,IAAIiC,IAAI,CAACjC,WAAW;MACxDrL,GAAG,CAACsL,SAAS,GAAGiC,MAAM,CAACjC,SAAS,IAAIgC,IAAI,CAAChC,SAAS;MAClDtL,GAAG,CAACuL,SAAS,GAAGgC,MAAM,CAAChC,SAAS,IAAI+B,IAAI,CAAC/B,SAAS;IACpD,CAAC,MAAM,IAAI,CAACmC,eAAe,CAACH,MAAM,CAAC,EAAE;MACnC;MACAvN,GAAG,CAACoL,WAAW,GAAGkC,IAAI,CAAClC,WAAW;MAClCpL,GAAG,CAACqL,WAAW,GAAGiC,IAAI,CAACjC,WAAW;MAClCrL,GAAG,CAACsL,SAAS,GAAGgC,IAAI,CAAChC,SAAS;MAC9BtL,GAAG,CAACuL,SAAS,GAAG+B,IAAI,CAAC/B,SAAS;IAChC,CAAC,MAAM;MACL;MACAvL,GAAG,CAACoL,WAAW,GAAGuC,WAAW,CAAC3N,GAAG,EAAEsN,IAAI,CAAClC,WAAW,EAAEmC,MAAM,CAACnC,WAAW,CAAC;MACxEpL,GAAG,CAACqL,WAAW,GAAGsC,WAAW,CAAC3N,GAAG,EAAEsN,IAAI,CAACjC,WAAW,EAAEkC,MAAM,CAAClC,WAAW,CAAC;MACxErL,GAAG,CAACsL,SAAS,GAAGqC,WAAW,CAAC3N,GAAG,EAAEsN,IAAI,CAAChC,SAAS,EAAEiC,MAAM,CAACjC,SAAS,CAAC;MAClEtL,GAAG,CAACuL,SAAS,GAAGoC,WAAW,CAAC3N,GAAG,EAAEsN,IAAI,CAAC/B,SAAS,EAAEgC,MAAM,CAAChC,SAAS,CAAC;IACpE;EACF;EAEAvL,GAAG,CAACsH,KAAK,GAAG,EAAE;EACd,IAAIsG,SAAS,GAAG,CAAC;IACbC,WAAW,GAAG,CAAC;IACfC,UAAU,GAAG,CAAC;IACdC,YAAY,GAAG,CAAC;EAEpB,OAAOH,SAAS,GAAGN,IAAI,CAAChG,KAAK,CAAC1J,MAAM,IAAIiQ,WAAW,GAAGN,MAAM,CAACjG,KAAK,CAAC1J,MAAM,EAAE;IACzE,IAAIoQ,WAAW,GAAGV,IAAI,CAAChG,KAAK,CAACsG,SAAS,CAAC,IAAI;QACzCxF,QAAQ,EAAE6F;MACZ,CAAC;MACGC,aAAa,GAAGX,MAAM,CAACjG,KAAK,CAACuG,WAAW,CAAC,IAAI;QAC/CzF,QAAQ,EAAE6F;MACZ,CAAC;IAED,IAAIE,UAAU,CAACH,WAAW,EAAEE,aAAa,CAAC,EAAE;MAC1C;MACAlO,GAAG,CAACsH,KAAK,CAAClH,IAAI,CAACgO,SAAS,CAACJ,WAAW,EAAEF,UAAU,CAAC,CAAC;MAClDF,SAAS,EAAE;MACXG,YAAY,IAAIC,WAAW,CAACzF,QAAQ,GAAGyF,WAAW,CAAC3F,QAAQ;IAC7D,CAAC,MAAM,IAAI8F,UAAU,CAACD,aAAa,EAAEF,WAAW,CAAC,EAAE;MACjD;MACAhO,GAAG,CAACsH,KAAK,CAAClH,IAAI,CAACgO,SAAS,CAACF,aAAa,EAAEH,YAAY,CAAC,CAAC;MACtDF,WAAW,EAAE;MACbC,UAAU,IAAII,aAAa,CAAC3F,QAAQ,GAAG2F,aAAa,CAAC7F,QAAQ;IAC/D,CAAC,MAAM;MACL;MACA,IAAIgG,UAAU,GAAG;QACfjG,QAAQ,EAAE1J,IAAI,CAACC,GAAG,CAACqP,WAAW,CAAC5F,QAAQ,EAAE8F,aAAa,CAAC9F,QAAQ,CAAC;QAChEC,QAAQ,EAAE,CAAC;QACXC,QAAQ,EAAE5J,IAAI,CAACC,GAAG,CAACqP,WAAW,CAAC1F,QAAQ,GAAGwF,UAAU,EAAEI,aAAa,CAAC9F,QAAQ,GAAG2F,YAAY,CAAC;QAC5FxF,QAAQ,EAAE,CAAC;QACXC,KAAK,EAAE;MACT,CAAC;MACD8F,UAAU,CAACD,UAAU,EAAEL,WAAW,CAAC5F,QAAQ,EAAE4F,WAAW,CAACxF,KAAK,EAAE0F,aAAa,CAAC9F,QAAQ,EAAE8F,aAAa,CAAC1F,KAAK,CAAC;MAC5GqF,WAAW,EAAE;MACbD,SAAS,EAAE;MACX5N,GAAG,CAACsH,KAAK,CAAClH,IAAI,CAACiO,UAAU,CAAC;IAC5B;EACF;EAEA,OAAOrO,GAAG;AACZ;AAEA,SAASyN,SAASA,CAACc,KAAK,EAAEf,IAAI,EAAE;EAC9B,IAAI,OAAOe,KAAK,KAAK,QAAQ,EAAE;IAC7B,IAAI,MAAM,CAACjM,IAAI,CAACiM,KAAK,CAAC,IAAI,UAAU,CAACjM,IAAI,CAACiM,KAAK,CAAC,EAAE;MAChD,OAAO3H,UAAU,CAAC2H,KAAK,CAAC,CAAC,CAAC,CAAC;IAC7B;IAEA,IAAI,CAACf,IAAI,EAAE;MACT,MAAM,IAAI9F,KAAK,CAAC,kDAAkD,CAAC;IACrE;IAEA,OAAOyD,eAAe,CAACtN,SAAS,EAAEA,SAAS,EAAE2P,IAAI,EAAEe,KAAK,CAAC;EAC3D;EAEA,OAAOA,KAAK;AACd;AAEA,SAASb,eAAeA,CAACc,KAAK,EAAE;EAC9B,OAAOA,KAAK,CAACnD,WAAW,IAAImD,KAAK,CAACnD,WAAW,KAAKmD,KAAK,CAACpD,WAAW;AACrE;AAEA,SAASuC,WAAWA,CAACxG,KAAK,EAAEmG,IAAI,EAAEC,MAAM,EAAE;EACxC,IAAID,IAAI,KAAKC,MAAM,EAAE;IACnB,OAAOD,IAAI;EACb,CAAC,MAAM;IACLnG,KAAK,CAACsH,QAAQ,GAAG,IAAI;IACrB,OAAO;MACLnB,IAAI,EAAEA,IAAI;MACVC,MAAM,EAAEA;IACV,CAAC;EACH;AACF;AAEA,SAASY,UAAUA,CAAC7L,IAAI,EAAEoM,KAAK,EAAE;EAC/B,OAAOpM,IAAI,CAAC8F,QAAQ,GAAGsG,KAAK,CAACtG,QAAQ,IAAI9F,IAAI,CAAC8F,QAAQ,GAAG9F,IAAI,CAAC+F,QAAQ,GAAGqG,KAAK,CAACtG,QAAQ;AACzF;AAEA,SAASgG,SAASA,CAACjG,IAAI,EAAE0B,MAAM,EAAE;EAC/B,OAAO;IACLzB,QAAQ,EAAED,IAAI,CAACC,QAAQ;IACvBC,QAAQ,EAAEF,IAAI,CAACE,QAAQ;IACvBC,QAAQ,EAAEH,IAAI,CAACG,QAAQ,GAAGuB,MAAM;IAChCtB,QAAQ,EAAEJ,IAAI,CAACI,QAAQ;IACvBC,KAAK,EAAEL,IAAI,CAACK;EACd,CAAC;AACH;AAEA,SAAS8F,UAAUA,CAACnG,IAAI,EAAE2F,UAAU,EAAEa,SAAS,EAAEC,WAAW,EAAEC,UAAU,EAAE;EACxE;EACA;EACA,IAAIvB,IAAI,GAAG;MACTzD,MAAM,EAAEiE,UAAU;MAClBtF,KAAK,EAAEmG,SAAS;MAChBxH,KAAK,EAAE;IACT,CAAC;IACG2H,KAAK,GAAG;MACVjF,MAAM,EAAE+E,WAAW;MACnBpG,KAAK,EAAEqG,UAAU;MACjB1H,KAAK,EAAE;IACT,CAAC,CAAC,CAAC;;EAEH4H,aAAa,CAAC5G,IAAI,EAAEmF,IAAI,EAAEwB,KAAK,CAAC;EAChCC,aAAa,CAAC5G,IAAI,EAAE2G,KAAK,EAAExB,IAAI,CAAC,CAAC,CAAC;;EAElC,OAAOA,IAAI,CAACnG,KAAK,GAAGmG,IAAI,CAAC9E,KAAK,CAAC5K,MAAM,IAAIkR,KAAK,CAAC3H,KAAK,GAAG2H,KAAK,CAACtG,KAAK,CAAC5K,MAAM,EAAE;IACzE,IAAIoQ,WAAW,GAAGV,IAAI,CAAC9E,KAAK,CAAC8E,IAAI,CAACnG,KAAK,CAAC;MACpC6H,YAAY,GAAGF,KAAK,CAACtG,KAAK,CAACsG,KAAK,CAAC3H,KAAK,CAAC;IAE3C,IAAI,CAAC6G,WAAW,CAAC,CAAC,CAAC,KAAK,GAAG,IAAIA,WAAW,CAAC,CAAC,CAAC,KAAK,GAAG,MAAMgB,YAAY,CAAC,CAAC,CAAC,KAAK,GAAG,IAAIA,YAAY,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,EAAE;MAC9G;MACAC,YAAY,CAAC9G,IAAI,EAAEmF,IAAI,EAAEwB,KAAK,CAAC;IACjC,CAAC,MAAM,IAAId,WAAW,CAAC,CAAC,CAAC,KAAK,GAAG,IAAIgB,YAAY,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MAC5D,IAAIE,WAAW;;MAEf;MACA,CAACA,WAAW,GAAG/G,IAAI,CAACK,KAAK,EAAEpI,IAAI,CAACgM,KAAK,CAAC8C,WAAW,EAAEtL,kBAAkB,CAACuL,aAAa,CAAC7B,IAAI,CAAC,CAAC,CAAC;IAC7F,CAAC,MAAM,IAAI0B,YAAY,CAAC,CAAC,CAAC,KAAK,GAAG,IAAIhB,WAAW,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MAC5D,IAAIoB,YAAY;;MAEhB;MACA,CAACA,YAAY,GAAGjH,IAAI,CAACK,KAAK,EAAEpI,IAAI,CAACgM,KAAK,CAACgD,YAAY,EAAExL,kBAAkB,CAACuL,aAAa,CAACL,KAAK,CAAC,CAAC,CAAC;IAChG,CAAC,MAAM,IAAId,WAAW,CAAC,CAAC,CAAC,KAAK,GAAG,IAAIgB,YAAY,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MAC5D;MACAK,OAAO,CAAClH,IAAI,EAAEmF,IAAI,EAAEwB,KAAK,CAAC;IAC5B,CAAC,MAAM,IAAIE,YAAY,CAAC,CAAC,CAAC,KAAK,GAAG,IAAIhB,WAAW,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MAC5D;MACAqB,OAAO,CAAClH,IAAI,EAAE2G,KAAK,EAAExB,IAAI,EAAE,IAAI,CAAC;IAClC,CAAC,MAAM,IAAIU,WAAW,KAAKgB,YAAY,EAAE;MACvC;MACA7G,IAAI,CAACK,KAAK,CAACpI,IAAI,CAAC4N,WAAW,CAAC;MAC5BV,IAAI,CAACnG,KAAK,EAAE;MACZ2H,KAAK,CAAC3H,KAAK,EAAE;IACf,CAAC,MAAM;MACL;MACAsH,QAAQ,CAACtG,IAAI,EAAEgH,aAAa,CAAC7B,IAAI,CAAC,EAAE6B,aAAa,CAACL,KAAK,CAAC,CAAC;IAC3D;EACF,CAAC,CAAC;;EAGFQ,cAAc,CAACnH,IAAI,EAAEmF,IAAI,CAAC;EAC1BgC,cAAc,CAACnH,IAAI,EAAE2G,KAAK,CAAC;EAC3B5B,aAAa,CAAC/E,IAAI,CAAC;AACrB;AAEA,SAAS8G,YAAYA,CAAC9G,IAAI,EAAEmF,IAAI,EAAEwB,KAAK,EAAE;EACvC,IAAIS,SAAS,GAAGJ,aAAa,CAAC7B,IAAI,CAAC;IAC/BkC,YAAY,GAAGL,aAAa,CAACL,KAAK,CAAC;EAEvC,IAAIW,UAAU,CAACF,SAAS,CAAC,IAAIE,UAAU,CAACD,YAAY,CAAC,EAAE;IACrD;IACA,IAAIvC,eAAe,CAACsC,SAAS,EAAEC,YAAY,CAAC,IAAIE,kBAAkB,CAACZ,KAAK,EAAES,SAAS,EAAEA,SAAS,CAAC3R,MAAM,GAAG4R,YAAY,CAAC5R,MAAM,CAAC,EAAE;MAC5H,IAAI+R,YAAY;MAEhB,CAACA,YAAY,GAAGxH,IAAI,CAACK,KAAK,EAAEpI,IAAI,CAACgM,KAAK,CAACuD,YAAY,EAAE/L,kBAAkB,CAAC2L,SAAS,CAAC,CAAC;MAEnF;IACF,CAAC,MAAM,IAAItC,eAAe,CAACuC,YAAY,EAAED,SAAS,CAAC,IAAIG,kBAAkB,CAACpC,IAAI,EAAEkC,YAAY,EAAEA,YAAY,CAAC5R,MAAM,GAAG2R,SAAS,CAAC3R,MAAM,CAAC,EAAE;MACrI,IAAIgS,YAAY;MAEhB,CAACA,YAAY,GAAGzH,IAAI,CAACK,KAAK,EAAEpI,IAAI,CAACgM,KAAK,CAACwD,YAAY,EAAEhM,kBAAkB,CAAC4L,YAAY,CAAC,CAAC;MAEtF;IACF;EACF,CAAC,MAAM,IAAI1C,UAAU,CAACyC,SAAS,EAAEC,YAAY,CAAC,EAAE;IAC9C,IAAIK,YAAY;IAEhB,CAACA,YAAY,GAAG1H,IAAI,CAACK,KAAK,EAAEpI,IAAI,CAACgM,KAAK,CAACyD,YAAY,EAAEjM,kBAAkB,CAAC2L,SAAS,CAAC,CAAC;IAEnF;EACF;EAEAd,QAAQ,CAACtG,IAAI,EAAEoH,SAAS,EAAEC,YAAY,CAAC;AACzC;AAEA,SAASH,OAAOA,CAAClH,IAAI,EAAEmF,IAAI,EAAEwB,KAAK,EAAEgB,IAAI,EAAE;EACxC,IAAIP,SAAS,GAAGJ,aAAa,CAAC7B,IAAI,CAAC;IAC/BkC,YAAY,GAAGO,cAAc,CAACjB,KAAK,EAAES,SAAS,CAAC;EAEnD,IAAIC,YAAY,CAACQ,MAAM,EAAE;IACvB,IAAIC,YAAY;IAEhB,CAACA,YAAY,GAAG9H,IAAI,CAACK,KAAK,EAAEpI,IAAI,CAACgM,KAAK,CAAC6D,YAAY,EAAErM,kBAAkB,CAAC4L,YAAY,CAACQ,MAAM,CAAC,CAAC;EAC/F,CAAC,MAAM;IACLvB,QAAQ,CAACtG,IAAI,EAAE2H,IAAI,GAAGN,YAAY,GAAGD,SAAS,EAAEO,IAAI,GAAGP,SAAS,GAAGC,YAAY,CAAC;EAClF;AACF;AAEA,SAASf,QAAQA,CAACtG,IAAI,EAAEmF,IAAI,EAAEwB,KAAK,EAAE;EACnC3G,IAAI,CAACsG,QAAQ,GAAG,IAAI;EACpBtG,IAAI,CAACK,KAAK,CAACpI,IAAI,CAAC;IACdqO,QAAQ,EAAE,IAAI;IACdnB,IAAI,EAAEA,IAAI;IACVC,MAAM,EAAEuB;EACV,CAAC,CAAC;AACJ;AAEA,SAASC,aAAaA,CAAC5G,IAAI,EAAE+H,MAAM,EAAEpB,KAAK,EAAE;EAC1C,OAAOoB,MAAM,CAACrG,MAAM,GAAGiF,KAAK,CAACjF,MAAM,IAAIqG,MAAM,CAAC/I,KAAK,GAAG+I,MAAM,CAAC1H,KAAK,CAAC5K,MAAM,EAAE;IACzE,IAAIkF,IAAI,GAAGoN,MAAM,CAAC1H,KAAK,CAAC0H,MAAM,CAAC/I,KAAK,EAAE,CAAC;IACvCgB,IAAI,CAACK,KAAK,CAACpI,IAAI,CAAC0C,IAAI,CAAC;IACrBoN,MAAM,CAACrG,MAAM,EAAE;EACjB;AACF;AAEA,SAASyF,cAAcA,CAACnH,IAAI,EAAE+H,MAAM,EAAE;EACpC,OAAOA,MAAM,CAAC/I,KAAK,GAAG+I,MAAM,CAAC1H,KAAK,CAAC5K,MAAM,EAAE;IACzC,IAAIkF,IAAI,GAAGoN,MAAM,CAAC1H,KAAK,CAAC0H,MAAM,CAAC/I,KAAK,EAAE,CAAC;IACvCgB,IAAI,CAACK,KAAK,CAACpI,IAAI,CAAC0C,IAAI,CAAC;EACvB;AACF;AAEA,SAASqM,aAAaA,CAACgB,KAAK,EAAE;EAC5B,IAAInQ,GAAG,GAAG,EAAE;IACR6I,SAAS,GAAGsH,KAAK,CAAC3H,KAAK,CAAC2H,KAAK,CAAChJ,KAAK,CAAC,CAAC,CAAC,CAAC;EAE3C,OAAOgJ,KAAK,CAAChJ,KAAK,GAAGgJ,KAAK,CAAC3H,KAAK,CAAC5K,MAAM,EAAE;IACvC,IAAIkF,IAAI,GAAGqN,KAAK,CAAC3H,KAAK,CAAC2H,KAAK,CAAChJ,KAAK,CAAC,CAAC,CAAC;;IAErC,IAAI0B,SAAS,KAAK,GAAG,IAAI/F,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MACxC+F,SAAS,GAAG,GAAG;IACjB;IAEA,IAAIA,SAAS,KAAK/F,IAAI,CAAC,CAAC,CAAC,EAAE;MACzB9C,GAAG,CAACI,IAAI,CAAC0C,IAAI,CAAC;MACdqN,KAAK,CAAChJ,KAAK,EAAE;IACf,CAAC,MAAM;MACL;IACF;EACF;EAEA,OAAOnH,GAAG;AACZ;AAEA,SAAS+P,cAAcA,CAACI,KAAK,EAAEC,YAAY,EAAE;EAC3C,IAAIC,OAAO,GAAG,EAAE;IACZL,MAAM,GAAG,EAAE;IACXM,UAAU,GAAG,CAAC;IACdC,cAAc,GAAG,KAAK;IACtBC,UAAU,GAAG,KAAK;EAEtB,OAAOF,UAAU,GAAGF,YAAY,CAACxS,MAAM,IAAIuS,KAAK,CAAChJ,KAAK,GAAGgJ,KAAK,CAAC3H,KAAK,CAAC5K,MAAM,EAAE;IAC3E,IAAI6S,MAAM,GAAGN,KAAK,CAAC3H,KAAK,CAAC2H,KAAK,CAAChJ,KAAK,CAAC;MACjCH,KAAK,GAAGoJ,YAAY,CAACE,UAAU,CAAC,CAAC,CAAC;;IAEtC,IAAItJ,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MACpB;IACF;IAEAuJ,cAAc,GAAGA,cAAc,IAAIE,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG;IACpDT,MAAM,CAAC5P,IAAI,CAAC4G,KAAK,CAAC;IAClBsJ,UAAU,EAAE,CAAC,CAAC;IACd;;IAEA,IAAIG,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MACrBD,UAAU,GAAG,IAAI;MAEjB,OAAOC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QACxBJ,OAAO,CAACjQ,IAAI,CAACqQ,MAAM,CAAC;QACpBA,MAAM,GAAGN,KAAK,CAAC3H,KAAK,CAAC,EAAE2H,KAAK,CAAChJ,KAAK,CAAC;MACrC;IACF;IAEA,IAAIH,KAAK,CAACe,MAAM,CAAC,CAAC,CAAC,KAAK0I,MAAM,CAAC1I,MAAM,CAAC,CAAC,CAAC,EAAE;MACxCsI,OAAO,CAACjQ,IAAI,CAACqQ,MAAM,CAAC;MACpBN,KAAK,CAAChJ,KAAK,EAAE;IACf,CAAC,MAAM;MACLqJ,UAAU,GAAG,IAAI;IACnB;EACF;EAEA,IAAI,CAACJ,YAAY,CAACE,UAAU,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,KAAK,GAAG,IAAIC,cAAc,EAAE;IACjEC,UAAU,GAAG,IAAI;EACnB;EAEA,IAAIA,UAAU,EAAE;IACd,OAAOH,OAAO;EAChB;EAEA,OAAOC,UAAU,GAAGF,YAAY,CAACxS,MAAM,EAAE;IACvCoS,MAAM,CAAC5P,IAAI,CAACgQ,YAAY,CAACE,UAAU,EAAE,CAAC,CAAC;EACzC;EAEA,OAAO;IACLN,MAAM,EAAEA,MAAM;IACdK,OAAO,EAAEA;EACX,CAAC;AACH;AAEA,SAASZ,UAAUA,CAACY,OAAO,EAAE;EAC3B,OAAOA,OAAO,CAACK,MAAM,CAAC,UAAUvE,IAAI,EAAEsE,MAAM,EAAE;IAC5C,OAAOtE,IAAI,IAAIsE,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG;EAClC,CAAC,EAAE,IAAI,CAAC;AACV;AAEA,SAASf,kBAAkBA,CAACS,KAAK,EAAEQ,aAAa,EAAEC,KAAK,EAAE;EACvD,KAAK,IAAI/P,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+P,KAAK,EAAE/P,CAAC,EAAE,EAAE;IAC9B,IAAIgQ,aAAa,GAAGF,aAAa,CAACA,aAAa,CAAC/S,MAAM,GAAGgT,KAAK,GAAG/P,CAAC,CAAC,CAACkH,MAAM,CAAC,CAAC,CAAC;IAE7E,IAAIoI,KAAK,CAAC3H,KAAK,CAAC2H,KAAK,CAAChJ,KAAK,GAAGtG,CAAC,CAAC,KAAK,GAAG,GAAGgQ,aAAa,EAAE;MACxD,OAAO,KAAK;IACd;EACF;EAEAV,KAAK,CAAChJ,KAAK,IAAIyJ,KAAK;EACpB,OAAO,IAAI;AACb;AAEA,SAASxD,mBAAmBA,CAAC5E,KAAK,EAAE;EAClC,IAAIH,QAAQ,GAAG,CAAC;EAChB,IAAIE,QAAQ,GAAG,CAAC;EAChBC,KAAK,CAACsI,OAAO,CAAC,UAAUhO,IAAI,EAAE;IAC5B,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;MAC5B,IAAIiO,OAAO,GAAG3D,mBAAmB,CAACtK,IAAI,CAACwK,IAAI,CAAC;MAC5C,IAAI0D,UAAU,GAAG5D,mBAAmB,CAACtK,IAAI,CAACyK,MAAM,CAAC;MAEjD,IAAIlF,QAAQ,KAAKxK,SAAS,EAAE;QAC1B,IAAIkT,OAAO,CAAC1I,QAAQ,KAAK2I,UAAU,CAAC3I,QAAQ,EAAE;UAC5CA,QAAQ,IAAI0I,OAAO,CAAC1I,QAAQ;QAC9B,CAAC,MAAM;UACLA,QAAQ,GAAGxK,SAAS;QACtB;MACF;MAEA,IAAI0K,QAAQ,KAAK1K,SAAS,EAAE;QAC1B,IAAIkT,OAAO,CAACxI,QAAQ,KAAKyI,UAAU,CAACzI,QAAQ,EAAE;UAC5CA,QAAQ,IAAIwI,OAAO,CAACxI,QAAQ;QAC9B,CAAC,MAAM;UACLA,QAAQ,GAAG1K,SAAS;QACtB;MACF;IACF,CAAC,MAAM;MACL,IAAI0K,QAAQ,KAAK1K,SAAS,KAAKiF,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,IAAIA,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,EAAE;QAClEyF,QAAQ,EAAE;MACZ;MAEA,IAAIF,QAAQ,KAAKxK,SAAS,KAAKiF,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,IAAIA,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,EAAE;QAClEuF,QAAQ,EAAE;MACZ;IACF;EACF,CAAC,CAAC;EACF,OAAO;IACLA,QAAQ,EAAEA,QAAQ;IAClBE,QAAQ,EAAEA;EACZ,CAAC;AACH;;AAEA;AACA,SAAS0I,mBAAmBA,CAACZ,OAAO,EAAE;EACpC,IAAIrQ,GAAG,GAAG,EAAE;IACRyQ,MAAM;IACN5H,SAAS;EAEb,KAAK,IAAIhI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwP,OAAO,CAACzS,MAAM,EAAEiD,CAAC,EAAE,EAAE;IACvC4P,MAAM,GAAGJ,OAAO,CAACxP,CAAC,CAAC;IAEnB,IAAI4P,MAAM,CAACxQ,KAAK,EAAE;MAChB4I,SAAS,GAAG,CAAC;IACf,CAAC,MAAM,IAAI4H,MAAM,CAACvQ,OAAO,EAAE;MACzB2I,SAAS,GAAG,CAAC,CAAC;IAChB,CAAC,MAAM;MACLA,SAAS,GAAG,CAAC;IACf;IAEA7I,GAAG,CAACI,IAAI,CAAC,CAACyI,SAAS,EAAE4H,MAAM,CAACxS,KAAK,CAAC,CAAC;EACrC;EAEA,OAAO+B,GAAG;AACZ;AAEA,SAASkR,mBAAmBA,CAACb,OAAO,EAAE;EACpC,IAAIrQ,GAAG,GAAG,EAAE;EAEZ,KAAK,IAAIa,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwP,OAAO,CAACzS,MAAM,EAAEiD,CAAC,EAAE,EAAE;IACvC,IAAI4P,MAAM,GAAGJ,OAAO,CAACxP,CAAC,CAAC;IAEvB,IAAI4P,MAAM,CAACxQ,KAAK,EAAE;MAChBD,GAAG,CAACI,IAAI,CAAC,OAAO,CAAC;IACnB,CAAC,MAAM,IAAIqQ,MAAM,CAACvQ,OAAO,EAAE;MACzBF,GAAG,CAACI,IAAI,CAAC,OAAO,CAAC;IACnB;IAEAJ,GAAG,CAACI,IAAI,CAAC+Q,UAAU,CAACV,MAAM,CAACxS,KAAK,CAAC,CAAC;IAElC,IAAIwS,MAAM,CAACxQ,KAAK,EAAE;MAChBD,GAAG,CAACI,IAAI,CAAC,QAAQ,CAAC;IACpB,CAAC,MAAM,IAAIqQ,MAAM,CAACvQ,OAAO,EAAE;MACzBF,GAAG,CAACI,IAAI,CAAC,QAAQ,CAAC;IACpB;EACF;EAEA,OAAOJ,GAAG,CAACf,IAAI,CAAC,EAAE,CAAC;AACrB;AAEA,SAASkS,UAAUA,CAACC,CAAC,EAAE;EACrB,IAAI1M,CAAC,GAAG0M,CAAC;EACT1M,CAAC,GAAGA,CAAC,CAACiB,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC;EAC5BjB,CAAC,GAAGA,CAAC,CAACiB,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC;EAC3BjB,CAAC,GAAGA,CAAC,CAACiB,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC;EAC3BjB,CAAC,GAAGA,CAAC,CAACiB,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC;EAC7B,OAAOjB,CAAC;AACV;AAEA,SAASrH,IAAI,EAAEiM,UAAU,EAAEqB,YAAY,EAAEjF,YAAY,EAAEuL,mBAAmB,EAAEC,mBAAmB,EAAErE,WAAW,EAAED,mBAAmB,EAAEnG,UAAU,EAAE9E,SAAS,EAAE2B,OAAO,EAAEsC,QAAQ,EAAE3C,SAAS,EAAEG,aAAa,EAAEF,gBAAgB,EAAET,SAAS,EAAEC,kBAAkB,EAAE2K,KAAK,EAAEzG,UAAU,EAAEuE,eAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}