{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\");\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nvar _classCallCheck2 = _interopRequireDefault(require(\"@babel/runtime/helpers/classCallCheck\"));\nvar _createClass2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createClass\"));\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"@babel/runtime/helpers/possibleConstructorReturn\"));\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"@babel/runtime/helpers/getPrototypeOf\"));\nvar _assertThisInitialized2 = _interopRequireDefault(require(\"@babel/runtime/helpers/assertThisInitialized\"));\nvar _inherits2 = _interopRequireDefault(require(\"@babel/runtime/helpers/inherits\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _react = _interopRequireWildcard(require(\"react\"));\nvar _classnames = _interopRequireDefault(require(\"classnames\"));\nvar propTypes = {\n  actions: _propTypes[\"default\"].object,\n  player: _propTypes[\"default\"].object,\n  className: _propTypes[\"default\"].string\n};\nvar PlayToggle = /*#__PURE__*/\nfunction (_Component) {\n  (0, _inherits2[\"default\"])(PlayToggle, _Component);\n  function PlayToggle(props, context) {\n    var _this;\n    (0, _classCallCheck2[\"default\"])(this, PlayToggle);\n    _this = (0, _possibleConstructorReturn2[\"default\"])(this, (0, _getPrototypeOf2[\"default\"])(PlayToggle).call(this, props, context));\n    _this.handleClick = _this.handleClick.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    return _this;\n  }\n  (0, _createClass2[\"default\"])(PlayToggle, [{\n    key: \"handleClick\",\n    value: function handleClick() {\n      var _this$props = this.props,\n        actions = _this$props.actions,\n        player = _this$props.player;\n      if (player.paused) {\n        actions.play();\n      } else {\n        actions.pause();\n      }\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this2 = this;\n      var _this$props2 = this.props,\n        player = _this$props2.player,\n        className = _this$props2.className;\n      var controlText = player.paused ? 'Play' : 'Pause';\n      return _react[\"default\"].createElement(\"button\", {\n        ref: function ref(c) {\n          _this2.button = c;\n        },\n        className: (0, _classnames[\"default\"])(className, {\n          'video-react-play-control': true,\n          'video-react-control': true,\n          'video-react-button': true,\n          'video-react-paused': player.paused,\n          'video-react-playing': !player.paused\n        }),\n        type: \"button\",\n        tabIndex: \"0\",\n        onClick: this.handleClick\n      }, _react[\"default\"].createElement(\"span\", {\n        className: \"video-react-control-text\"\n      }, controlText));\n    }\n  }]);\n  return PlayToggle;\n}(_react.Component);\nexports[\"default\"] = PlayToggle;\nPlayToggle.propTypes = propTypes;\nPlayToggle.displayName = 'PlayToggle';", "map": {"version": 3, "names": ["_interopRequireWildcard", "require", "_interopRequireDefault", "Object", "defineProperty", "exports", "value", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_assertThisInitialized2", "_inherits2", "_propTypes", "_react", "_classnames", "propTypes", "actions", "object", "player", "className", "string", "PlayToggle", "_Component", "props", "context", "_this", "call", "handleClick", "bind", "key", "_this$props", "paused", "play", "pause", "render", "_this2", "_this$props2", "controlText", "createElement", "ref", "c", "button", "type", "tabIndex", "onClick", "Component", "displayName"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/video-react/lib/components/control-bar/PlayToggle.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\");\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _classCallCheck2 = _interopRequireDefault(require(\"@babel/runtime/helpers/classCallCheck\"));\n\nvar _createClass2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createClass\"));\n\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"@babel/runtime/helpers/possibleConstructorReturn\"));\n\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"@babel/runtime/helpers/getPrototypeOf\"));\n\nvar _assertThisInitialized2 = _interopRequireDefault(require(\"@babel/runtime/helpers/assertThisInitialized\"));\n\nvar _inherits2 = _interopRequireDefault(require(\"@babel/runtime/helpers/inherits\"));\n\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\n\nvar _react = _interopRequireWildcard(require(\"react\"));\n\nvar _classnames = _interopRequireDefault(require(\"classnames\"));\n\nvar propTypes = {\n  actions: _propTypes[\"default\"].object,\n  player: _propTypes[\"default\"].object,\n  className: _propTypes[\"default\"].string\n};\n\nvar PlayToggle =\n/*#__PURE__*/\nfunction (_Component) {\n  (0, _inherits2[\"default\"])(PlayToggle, _Component);\n\n  function PlayToggle(props, context) {\n    var _this;\n\n    (0, _classCallCheck2[\"default\"])(this, PlayToggle);\n    _this = (0, _possibleConstructorReturn2[\"default\"])(this, (0, _getPrototypeOf2[\"default\"])(PlayToggle).call(this, props, context));\n    _this.handleClick = _this.handleClick.bind((0, _assertThisInitialized2[\"default\"])(_this));\n    return _this;\n  }\n\n  (0, _createClass2[\"default\"])(PlayToggle, [{\n    key: \"handleClick\",\n    value: function handleClick() {\n      var _this$props = this.props,\n          actions = _this$props.actions,\n          player = _this$props.player;\n\n      if (player.paused) {\n        actions.play();\n      } else {\n        actions.pause();\n      }\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this2 = this;\n\n      var _this$props2 = this.props,\n          player = _this$props2.player,\n          className = _this$props2.className;\n      var controlText = player.paused ? 'Play' : 'Pause';\n      return _react[\"default\"].createElement(\"button\", {\n        ref: function ref(c) {\n          _this2.button = c;\n        },\n        className: (0, _classnames[\"default\"])(className, {\n          'video-react-play-control': true,\n          'video-react-control': true,\n          'video-react-button': true,\n          'video-react-paused': player.paused,\n          'video-react-playing': !player.paused\n        }),\n        type: \"button\",\n        tabIndex: \"0\",\n        onClick: this.handleClick\n      }, _react[\"default\"].createElement(\"span\", {\n        className: \"video-react-control-text\"\n      }, controlText));\n    }\n  }]);\n  return PlayToggle;\n}(_react.Component);\n\nexports[\"default\"] = PlayToggle;\nPlayToggle.propTypes = propTypes;\nPlayToggle.displayName = 'PlayToggle';"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,uBAAuB,GAAGC,OAAO,CAAC,+CAA+C,CAAC;AAEtF,IAAIC,sBAAsB,GAAGD,OAAO,CAAC,8CAA8C,CAAC;AAEpFE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;AAE3B,IAAIE,gBAAgB,GAAGL,sBAAsB,CAACD,OAAO,CAAC,uCAAuC,CAAC,CAAC;AAE/F,IAAIO,aAAa,GAAGN,sBAAsB,CAACD,OAAO,CAAC,oCAAoC,CAAC,CAAC;AAEzF,IAAIQ,2BAA2B,GAAGP,sBAAsB,CAACD,OAAO,CAAC,kDAAkD,CAAC,CAAC;AAErH,IAAIS,gBAAgB,GAAGR,sBAAsB,CAACD,OAAO,CAAC,uCAAuC,CAAC,CAAC;AAE/F,IAAIU,uBAAuB,GAAGT,sBAAsB,CAACD,OAAO,CAAC,8CAA8C,CAAC,CAAC;AAE7G,IAAIW,UAAU,GAAGV,sBAAsB,CAACD,OAAO,CAAC,iCAAiC,CAAC,CAAC;AAEnF,IAAIY,UAAU,GAAGX,sBAAsB,CAACD,OAAO,CAAC,YAAY,CAAC,CAAC;AAE9D,IAAIa,MAAM,GAAGd,uBAAuB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AAEtD,IAAIc,WAAW,GAAGb,sBAAsB,CAACD,OAAO,CAAC,YAAY,CAAC,CAAC;AAE/D,IAAIe,SAAS,GAAG;EACdC,OAAO,EAAEJ,UAAU,CAAC,SAAS,CAAC,CAACK,MAAM;EACrCC,MAAM,EAAEN,UAAU,CAAC,SAAS,CAAC,CAACK,MAAM;EACpCE,SAAS,EAAEP,UAAU,CAAC,SAAS,CAAC,CAACQ;AACnC,CAAC;AAED,IAAIC,UAAU,GACd;AACA,UAAUC,UAAU,EAAE;EACpB,CAAC,CAAC,EAAEX,UAAU,CAAC,SAAS,CAAC,EAAEU,UAAU,EAAEC,UAAU,CAAC;EAElD,SAASD,UAAUA,CAACE,KAAK,EAAEC,OAAO,EAAE;IAClC,IAAIC,KAAK;IAET,CAAC,CAAC,EAAEnB,gBAAgB,CAAC,SAAS,CAAC,EAAE,IAAI,EAAEe,UAAU,CAAC;IAClDI,KAAK,GAAG,CAAC,CAAC,EAAEjB,2BAA2B,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAEC,gBAAgB,CAAC,SAAS,CAAC,EAAEY,UAAU,CAAC,CAACK,IAAI,CAAC,IAAI,EAAEH,KAAK,EAAEC,OAAO,CAAC,CAAC;IAClIC,KAAK,CAACE,WAAW,GAAGF,KAAK,CAACE,WAAW,CAACC,IAAI,CAAC,CAAC,CAAC,EAAElB,uBAAuB,CAAC,SAAS,CAAC,EAAEe,KAAK,CAAC,CAAC;IAC1F,OAAOA,KAAK;EACd;EAEA,CAAC,CAAC,EAAElB,aAAa,CAAC,SAAS,CAAC,EAAEc,UAAU,EAAE,CAAC;IACzCQ,GAAG,EAAE,aAAa;IAClBxB,KAAK,EAAE,SAASsB,WAAWA,CAAA,EAAG;MAC5B,IAAIG,WAAW,GAAG,IAAI,CAACP,KAAK;QACxBP,OAAO,GAAGc,WAAW,CAACd,OAAO;QAC7BE,MAAM,GAAGY,WAAW,CAACZ,MAAM;MAE/B,IAAIA,MAAM,CAACa,MAAM,EAAE;QACjBf,OAAO,CAACgB,IAAI,EAAE;MAChB,CAAC,MAAM;QACLhB,OAAO,CAACiB,KAAK,EAAE;MACjB;IACF;EACF,CAAC,EAAE;IACDJ,GAAG,EAAE,QAAQ;IACbxB,KAAK,EAAE,SAAS6B,MAAMA,CAAA,EAAG;MACvB,IAAIC,MAAM,GAAG,IAAI;MAEjB,IAAIC,YAAY,GAAG,IAAI,CAACb,KAAK;QACzBL,MAAM,GAAGkB,YAAY,CAAClB,MAAM;QAC5BC,SAAS,GAAGiB,YAAY,CAACjB,SAAS;MACtC,IAAIkB,WAAW,GAAGnB,MAAM,CAACa,MAAM,GAAG,MAAM,GAAG,OAAO;MAClD,OAAOlB,MAAM,CAAC,SAAS,CAAC,CAACyB,aAAa,CAAC,QAAQ,EAAE;QAC/CC,GAAG,EAAE,SAASA,GAAGA,CAACC,CAAC,EAAE;UACnBL,MAAM,CAACM,MAAM,GAAGD,CAAC;QACnB,CAAC;QACDrB,SAAS,EAAE,CAAC,CAAC,EAAEL,WAAW,CAAC,SAAS,CAAC,EAAEK,SAAS,EAAE;UAChD,0BAA0B,EAAE,IAAI;UAChC,qBAAqB,EAAE,IAAI;UAC3B,oBAAoB,EAAE,IAAI;UAC1B,oBAAoB,EAAED,MAAM,CAACa,MAAM;UACnC,qBAAqB,EAAE,CAACb,MAAM,CAACa;QACjC,CAAC,CAAC;QACFW,IAAI,EAAE,QAAQ;QACdC,QAAQ,EAAE,GAAG;QACbC,OAAO,EAAE,IAAI,CAACjB;MAChB,CAAC,EAAEd,MAAM,CAAC,SAAS,CAAC,CAACyB,aAAa,CAAC,MAAM,EAAE;QACzCnB,SAAS,EAAE;MACb,CAAC,EAAEkB,WAAW,CAAC,CAAC;IAClB;EACF,CAAC,CAAC,CAAC;EACH,OAAOhB,UAAU;AACnB,CAAC,CAACR,MAAM,CAACgC,SAAS,CAAC;AAEnBzC,OAAO,CAAC,SAAS,CAAC,GAAGiB,UAAU;AAC/BA,UAAU,CAACN,SAAS,GAAGA,SAAS;AAChCM,UAAU,CAACyB,WAAW,GAAG,YAAY"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}