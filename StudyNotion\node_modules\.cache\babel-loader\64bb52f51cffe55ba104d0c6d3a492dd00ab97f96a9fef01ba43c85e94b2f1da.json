{"ast": null, "code": "import { getDocument } from 'ssr-window';\nimport { createElement, elementOffset, nextTick } from '../../shared/utils.js';\nimport createElementIfNotDefined from '../../shared/create-element-if-not-defined.js';\nexport default function Scrollbar(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on,\n    emit\n  } = _ref;\n  const document = getDocument();\n  let isTouched = false;\n  let timeout = null;\n  let dragTimeout = null;\n  let dragStartPos;\n  let dragSize;\n  let trackSize;\n  let divider;\n  extendParams({\n    scrollbar: {\n      el: null,\n      dragSize: 'auto',\n      hide: false,\n      draggable: false,\n      snapOnRelease: true,\n      lockClass: 'swiper-scrollbar-lock',\n      dragClass: 'swiper-scrollbar-drag',\n      scrollbarDisabledClass: 'swiper-scrollbar-disabled',\n      horizontalClass: `swiper-scrollbar-horizontal`,\n      verticalClass: `swiper-scrollbar-vertical`\n    }\n  });\n  swiper.scrollbar = {\n    el: null,\n    dragEl: null\n  };\n  function setTranslate() {\n    if (!swiper.params.scrollbar.el || !swiper.scrollbar.el) return;\n    const {\n      scrollbar,\n      rtlTranslate: rtl\n    } = swiper;\n    const {\n      dragEl,\n      el\n    } = scrollbar;\n    const params = swiper.params.scrollbar;\n    const progress = swiper.params.loop ? swiper.progressLoop : swiper.progress;\n    let newSize = dragSize;\n    let newPos = (trackSize - dragSize) * progress;\n    if (rtl) {\n      newPos = -newPos;\n      if (newPos > 0) {\n        newSize = dragSize - newPos;\n        newPos = 0;\n      } else if (-newPos + dragSize > trackSize) {\n        newSize = trackSize + newPos;\n      }\n    } else if (newPos < 0) {\n      newSize = dragSize + newPos;\n      newPos = 0;\n    } else if (newPos + dragSize > trackSize) {\n      newSize = trackSize - newPos;\n    }\n    if (swiper.isHorizontal()) {\n      dragEl.style.transform = `translate3d(${newPos}px, 0, 0)`;\n      dragEl.style.width = `${newSize}px`;\n    } else {\n      dragEl.style.transform = `translate3d(0px, ${newPos}px, 0)`;\n      dragEl.style.height = `${newSize}px`;\n    }\n    if (params.hide) {\n      clearTimeout(timeout);\n      el.style.opacity = 1;\n      timeout = setTimeout(() => {\n        el.style.opacity = 0;\n        el.style.transitionDuration = '400ms';\n      }, 1000);\n    }\n  }\n  function setTransition(duration) {\n    if (!swiper.params.scrollbar.el || !swiper.scrollbar.el) return;\n    swiper.scrollbar.dragEl.style.transitionDuration = `${duration}ms`;\n  }\n  function updateSize() {\n    if (!swiper.params.scrollbar.el || !swiper.scrollbar.el) return;\n    const {\n      scrollbar\n    } = swiper;\n    const {\n      dragEl,\n      el\n    } = scrollbar;\n    dragEl.style.width = '';\n    dragEl.style.height = '';\n    trackSize = swiper.isHorizontal() ? el.offsetWidth : el.offsetHeight;\n    divider = swiper.size / (swiper.virtualSize + swiper.params.slidesOffsetBefore - (swiper.params.centeredSlides ? swiper.snapGrid[0] : 0));\n    if (swiper.params.scrollbar.dragSize === 'auto') {\n      dragSize = trackSize * divider;\n    } else {\n      dragSize = parseInt(swiper.params.scrollbar.dragSize, 10);\n    }\n    if (swiper.isHorizontal()) {\n      dragEl.style.width = `${dragSize}px`;\n    } else {\n      dragEl.style.height = `${dragSize}px`;\n    }\n    if (divider >= 1) {\n      el.style.display = 'none';\n    } else {\n      el.style.display = '';\n    }\n    if (swiper.params.scrollbar.hide) {\n      el.style.opacity = 0;\n    }\n    if (swiper.params.watchOverflow && swiper.enabled) {\n      scrollbar.el.classList[swiper.isLocked ? 'add' : 'remove'](swiper.params.scrollbar.lockClass);\n    }\n  }\n  function getPointerPosition(e) {\n    return swiper.isHorizontal() ? e.clientX : e.clientY;\n  }\n  function setDragPosition(e) {\n    const {\n      scrollbar,\n      rtlTranslate: rtl\n    } = swiper;\n    const {\n      el\n    } = scrollbar;\n    let positionRatio;\n    positionRatio = (getPointerPosition(e) - elementOffset(el)[swiper.isHorizontal() ? 'left' : 'top'] - (dragStartPos !== null ? dragStartPos : dragSize / 2)) / (trackSize - dragSize);\n    positionRatio = Math.max(Math.min(positionRatio, 1), 0);\n    if (rtl) {\n      positionRatio = 1 - positionRatio;\n    }\n    const position = swiper.minTranslate() + (swiper.maxTranslate() - swiper.minTranslate()) * positionRatio;\n    swiper.updateProgress(position);\n    swiper.setTranslate(position);\n    swiper.updateActiveIndex();\n    swiper.updateSlidesClasses();\n  }\n  function onDragStart(e) {\n    const params = swiper.params.scrollbar;\n    const {\n      scrollbar,\n      wrapperEl\n    } = swiper;\n    const {\n      el,\n      dragEl\n    } = scrollbar;\n    isTouched = true;\n    dragStartPos = e.target === dragEl ? getPointerPosition(e) - e.target.getBoundingClientRect()[swiper.isHorizontal() ? 'left' : 'top'] : null;\n    e.preventDefault();\n    e.stopPropagation();\n    wrapperEl.style.transitionDuration = '100ms';\n    dragEl.style.transitionDuration = '100ms';\n    setDragPosition(e);\n    clearTimeout(dragTimeout);\n    el.style.transitionDuration = '0ms';\n    if (params.hide) {\n      el.style.opacity = 1;\n    }\n    if (swiper.params.cssMode) {\n      swiper.wrapperEl.style['scroll-snap-type'] = 'none';\n    }\n    emit('scrollbarDragStart', e);\n  }\n  function onDragMove(e) {\n    const {\n      scrollbar,\n      wrapperEl\n    } = swiper;\n    const {\n      el,\n      dragEl\n    } = scrollbar;\n    if (!isTouched) return;\n    if (e.preventDefault) e.preventDefault();else e.returnValue = false;\n    setDragPosition(e);\n    wrapperEl.style.transitionDuration = '0ms';\n    el.style.transitionDuration = '0ms';\n    dragEl.style.transitionDuration = '0ms';\n    emit('scrollbarDragMove', e);\n  }\n  function onDragEnd(e) {\n    const params = swiper.params.scrollbar;\n    const {\n      scrollbar,\n      wrapperEl\n    } = swiper;\n    const {\n      el\n    } = scrollbar;\n    if (!isTouched) return;\n    isTouched = false;\n    if (swiper.params.cssMode) {\n      swiper.wrapperEl.style['scroll-snap-type'] = '';\n      wrapperEl.style.transitionDuration = '';\n    }\n    if (params.hide) {\n      clearTimeout(dragTimeout);\n      dragTimeout = nextTick(() => {\n        el.style.opacity = 0;\n        el.style.transitionDuration = '400ms';\n      }, 1000);\n    }\n    emit('scrollbarDragEnd', e);\n    if (params.snapOnRelease) {\n      swiper.slideToClosest();\n    }\n  }\n  function events(method) {\n    const {\n      scrollbar,\n      params\n    } = swiper;\n    const el = scrollbar.el;\n    if (!el) return;\n    const target = el;\n    const activeListener = params.passiveListeners ? {\n      passive: false,\n      capture: false\n    } : false;\n    const passiveListener = params.passiveListeners ? {\n      passive: true,\n      capture: false\n    } : false;\n    if (!target) return;\n    const eventMethod = method === 'on' ? 'addEventListener' : 'removeEventListener';\n    target[eventMethod]('pointerdown', onDragStart, activeListener);\n    document[eventMethod]('pointermove', onDragMove, activeListener);\n    document[eventMethod]('pointerup', onDragEnd, passiveListener);\n  }\n  function enableDraggable() {\n    if (!swiper.params.scrollbar.el || !swiper.scrollbar.el) return;\n    events('on');\n  }\n  function disableDraggable() {\n    if (!swiper.params.scrollbar.el || !swiper.scrollbar.el) return;\n    events('off');\n  }\n  function init() {\n    const {\n      scrollbar,\n      el: swiperEl\n    } = swiper;\n    swiper.params.scrollbar = createElementIfNotDefined(swiper, swiper.originalParams.scrollbar, swiper.params.scrollbar, {\n      el: 'swiper-scrollbar'\n    });\n    const params = swiper.params.scrollbar;\n    if (!params.el) return;\n    let el;\n    if (typeof params.el === 'string' && swiper.isElement) {\n      el = swiper.el.shadowRoot.querySelector(params.el);\n    }\n    if (!el && typeof params.el === 'string') {\n      el = document.querySelectorAll(params.el);\n    } else if (!el) {\n      el = params.el;\n    }\n    if (swiper.params.uniqueNavElements && typeof params.el === 'string' && el.length > 1 && swiperEl.querySelectorAll(params.el).length === 1) {\n      el = swiperEl.querySelector(params.el);\n    }\n    if (el.length > 0) el = el[0];\n    el.classList.add(swiper.isHorizontal() ? params.horizontalClass : params.verticalClass);\n    let dragEl;\n    if (el) {\n      dragEl = el.querySelector(`.${swiper.params.scrollbar.dragClass}`);\n      if (!dragEl) {\n        dragEl = createElement('div', swiper.params.scrollbar.dragClass);\n        el.append(dragEl);\n      }\n    }\n    Object.assign(scrollbar, {\n      el,\n      dragEl\n    });\n    if (params.draggable) {\n      enableDraggable();\n    }\n    if (el) {\n      el.classList[swiper.enabled ? 'remove' : 'add'](swiper.params.scrollbar.lockClass);\n    }\n  }\n  function destroy() {\n    const params = swiper.params.scrollbar;\n    const el = swiper.scrollbar.el;\n    if (el) {\n      el.classList.remove(swiper.isHorizontal() ? params.horizontalClass : params.verticalClass);\n    }\n    disableDraggable();\n  }\n  on('init', () => {\n    if (swiper.params.scrollbar.enabled === false) {\n      // eslint-disable-next-line\n      disable();\n    } else {\n      init();\n      updateSize();\n      setTranslate();\n    }\n  });\n  on('update resize observerUpdate lock unlock', () => {\n    updateSize();\n  });\n  on('setTranslate', () => {\n    setTranslate();\n  });\n  on('setTransition', (_s, duration) => {\n    setTransition(duration);\n  });\n  on('enable disable', () => {\n    const {\n      el\n    } = swiper.scrollbar;\n    if (el) {\n      el.classList[swiper.enabled ? 'remove' : 'add'](swiper.params.scrollbar.lockClass);\n    }\n  });\n  on('destroy', () => {\n    destroy();\n  });\n  const enable = () => {\n    swiper.el.classList.remove(swiper.params.scrollbar.scrollbarDisabledClass);\n    if (swiper.scrollbar.el) {\n      swiper.scrollbar.el.classList.remove(swiper.params.scrollbar.scrollbarDisabledClass);\n    }\n    init();\n    updateSize();\n    setTranslate();\n  };\n  const disable = () => {\n    swiper.el.classList.add(swiper.params.scrollbar.scrollbarDisabledClass);\n    if (swiper.scrollbar.el) {\n      swiper.scrollbar.el.classList.add(swiper.params.scrollbar.scrollbarDisabledClass);\n    }\n    destroy();\n  };\n  Object.assign(swiper.scrollbar, {\n    enable,\n    disable,\n    updateSize,\n    setTranslate,\n    init,\n    destroy\n  });\n}", "map": {"version": 3, "names": ["getDocument", "createElement", "elementOffset", "nextTick", "createElementIfNotDefined", "Sc<PERSON><PERSON>", "_ref", "swiper", "extendParams", "on", "emit", "document", "isTouched", "timeout", "dragTimeout", "dragStartPos", "dragSize", "trackSize", "divider", "scrollbar", "el", "hide", "draggable", "snapOnRelease", "lockClass", "dragClass", "scrollbarDisabledClass", "horizontalClass", "verticalClass", "dragEl", "setTranslate", "params", "rtlTranslate", "rtl", "progress", "loop", "progressLoop", "newSize", "newPos", "isHorizontal", "style", "transform", "width", "height", "clearTimeout", "opacity", "setTimeout", "transitionDuration", "setTransition", "duration", "updateSize", "offsetWidth", "offsetHeight", "size", "virtualSize", "slidesOffsetBefore", "centeredSlides", "snapGrid", "parseInt", "display", "watchOverflow", "enabled", "classList", "isLocked", "getPointerPosition", "e", "clientX", "clientY", "setDragPosition", "positionRatio", "Math", "max", "min", "position", "minTranslate", "maxTranslate", "updateProgress", "updateActiveIndex", "updateSlidesClasses", "onDragStart", "wrapperEl", "target", "getBoundingClientRect", "preventDefault", "stopPropagation", "cssMode", "onDragMove", "returnValue", "onDragEnd", "slideToClosest", "events", "method", "activeListener", "passiveListeners", "passive", "capture", "passiveListener", "eventMethod", "enableDraggable", "disableDraggable", "init", "swiperEl", "originalParams", "isElement", "shadowRoot", "querySelector", "querySelectorAll", "uniqueNavElements", "length", "add", "append", "Object", "assign", "destroy", "remove", "disable", "_s", "enable"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/temp/StudyNotion/node_modules/swiper/modules/scrollbar/scrollbar.js"], "sourcesContent": ["import { getDocument } from 'ssr-window';\nimport { createElement, elementOffset, nextTick } from '../../shared/utils.js';\nimport createElementIfNotDefined from '../../shared/create-element-if-not-defined.js';\nexport default function Scrollbar({\n  swiper,\n  extendParams,\n  on,\n  emit\n}) {\n  const document = getDocument();\n  let isTouched = false;\n  let timeout = null;\n  let dragTimeout = null;\n  let dragStartPos;\n  let dragSize;\n  let trackSize;\n  let divider;\n  extendParams({\n    scrollbar: {\n      el: null,\n      dragSize: 'auto',\n      hide: false,\n      draggable: false,\n      snapOnRelease: true,\n      lockClass: 'swiper-scrollbar-lock',\n      dragClass: 'swiper-scrollbar-drag',\n      scrollbarDisabledClass: 'swiper-scrollbar-disabled',\n      horizontalClass: `swiper-scrollbar-horizontal`,\n      verticalClass: `swiper-scrollbar-vertical`\n    }\n  });\n  swiper.scrollbar = {\n    el: null,\n    dragEl: null\n  };\n  function setTranslate() {\n    if (!swiper.params.scrollbar.el || !swiper.scrollbar.el) return;\n    const {\n      scrollbar,\n      rtlTranslate: rtl\n    } = swiper;\n    const {\n      dragEl,\n      el\n    } = scrollbar;\n    const params = swiper.params.scrollbar;\n    const progress = swiper.params.loop ? swiper.progressLoop : swiper.progress;\n    let newSize = dragSize;\n    let newPos = (trackSize - dragSize) * progress;\n    if (rtl) {\n      newPos = -newPos;\n      if (newPos > 0) {\n        newSize = dragSize - newPos;\n        newPos = 0;\n      } else if (-newPos + dragSize > trackSize) {\n        newSize = trackSize + newPos;\n      }\n    } else if (newPos < 0) {\n      newSize = dragSize + newPos;\n      newPos = 0;\n    } else if (newPos + dragSize > trackSize) {\n      newSize = trackSize - newPos;\n    }\n    if (swiper.isHorizontal()) {\n      dragEl.style.transform = `translate3d(${newPos}px, 0, 0)`;\n      dragEl.style.width = `${newSize}px`;\n    } else {\n      dragEl.style.transform = `translate3d(0px, ${newPos}px, 0)`;\n      dragEl.style.height = `${newSize}px`;\n    }\n    if (params.hide) {\n      clearTimeout(timeout);\n      el.style.opacity = 1;\n      timeout = setTimeout(() => {\n        el.style.opacity = 0;\n        el.style.transitionDuration = '400ms';\n      }, 1000);\n    }\n  }\n  function setTransition(duration) {\n    if (!swiper.params.scrollbar.el || !swiper.scrollbar.el) return;\n    swiper.scrollbar.dragEl.style.transitionDuration = `${duration}ms`;\n  }\n  function updateSize() {\n    if (!swiper.params.scrollbar.el || !swiper.scrollbar.el) return;\n    const {\n      scrollbar\n    } = swiper;\n    const {\n      dragEl,\n      el\n    } = scrollbar;\n    dragEl.style.width = '';\n    dragEl.style.height = '';\n    trackSize = swiper.isHorizontal() ? el.offsetWidth : el.offsetHeight;\n    divider = swiper.size / (swiper.virtualSize + swiper.params.slidesOffsetBefore - (swiper.params.centeredSlides ? swiper.snapGrid[0] : 0));\n    if (swiper.params.scrollbar.dragSize === 'auto') {\n      dragSize = trackSize * divider;\n    } else {\n      dragSize = parseInt(swiper.params.scrollbar.dragSize, 10);\n    }\n    if (swiper.isHorizontal()) {\n      dragEl.style.width = `${dragSize}px`;\n    } else {\n      dragEl.style.height = `${dragSize}px`;\n    }\n    if (divider >= 1) {\n      el.style.display = 'none';\n    } else {\n      el.style.display = '';\n    }\n    if (swiper.params.scrollbar.hide) {\n      el.style.opacity = 0;\n    }\n    if (swiper.params.watchOverflow && swiper.enabled) {\n      scrollbar.el.classList[swiper.isLocked ? 'add' : 'remove'](swiper.params.scrollbar.lockClass);\n    }\n  }\n  function getPointerPosition(e) {\n    return swiper.isHorizontal() ? e.clientX : e.clientY;\n  }\n  function setDragPosition(e) {\n    const {\n      scrollbar,\n      rtlTranslate: rtl\n    } = swiper;\n    const {\n      el\n    } = scrollbar;\n    let positionRatio;\n    positionRatio = (getPointerPosition(e) - elementOffset(el)[swiper.isHorizontal() ? 'left' : 'top'] - (dragStartPos !== null ? dragStartPos : dragSize / 2)) / (trackSize - dragSize);\n    positionRatio = Math.max(Math.min(positionRatio, 1), 0);\n    if (rtl) {\n      positionRatio = 1 - positionRatio;\n    }\n    const position = swiper.minTranslate() + (swiper.maxTranslate() - swiper.minTranslate()) * positionRatio;\n    swiper.updateProgress(position);\n    swiper.setTranslate(position);\n    swiper.updateActiveIndex();\n    swiper.updateSlidesClasses();\n  }\n  function onDragStart(e) {\n    const params = swiper.params.scrollbar;\n    const {\n      scrollbar,\n      wrapperEl\n    } = swiper;\n    const {\n      el,\n      dragEl\n    } = scrollbar;\n    isTouched = true;\n    dragStartPos = e.target === dragEl ? getPointerPosition(e) - e.target.getBoundingClientRect()[swiper.isHorizontal() ? 'left' : 'top'] : null;\n    e.preventDefault();\n    e.stopPropagation();\n    wrapperEl.style.transitionDuration = '100ms';\n    dragEl.style.transitionDuration = '100ms';\n    setDragPosition(e);\n    clearTimeout(dragTimeout);\n    el.style.transitionDuration = '0ms';\n    if (params.hide) {\n      el.style.opacity = 1;\n    }\n    if (swiper.params.cssMode) {\n      swiper.wrapperEl.style['scroll-snap-type'] = 'none';\n    }\n    emit('scrollbarDragStart', e);\n  }\n  function onDragMove(e) {\n    const {\n      scrollbar,\n      wrapperEl\n    } = swiper;\n    const {\n      el,\n      dragEl\n    } = scrollbar;\n    if (!isTouched) return;\n    if (e.preventDefault) e.preventDefault();else e.returnValue = false;\n    setDragPosition(e);\n    wrapperEl.style.transitionDuration = '0ms';\n    el.style.transitionDuration = '0ms';\n    dragEl.style.transitionDuration = '0ms';\n    emit('scrollbarDragMove', e);\n  }\n  function onDragEnd(e) {\n    const params = swiper.params.scrollbar;\n    const {\n      scrollbar,\n      wrapperEl\n    } = swiper;\n    const {\n      el\n    } = scrollbar;\n    if (!isTouched) return;\n    isTouched = false;\n    if (swiper.params.cssMode) {\n      swiper.wrapperEl.style['scroll-snap-type'] = '';\n      wrapperEl.style.transitionDuration = '';\n    }\n    if (params.hide) {\n      clearTimeout(dragTimeout);\n      dragTimeout = nextTick(() => {\n        el.style.opacity = 0;\n        el.style.transitionDuration = '400ms';\n      }, 1000);\n    }\n    emit('scrollbarDragEnd', e);\n    if (params.snapOnRelease) {\n      swiper.slideToClosest();\n    }\n  }\n  function events(method) {\n    const {\n      scrollbar,\n      params\n    } = swiper;\n    const el = scrollbar.el;\n    if (!el) return;\n    const target = el;\n    const activeListener = params.passiveListeners ? {\n      passive: false,\n      capture: false\n    } : false;\n    const passiveListener = params.passiveListeners ? {\n      passive: true,\n      capture: false\n    } : false;\n    if (!target) return;\n    const eventMethod = method === 'on' ? 'addEventListener' : 'removeEventListener';\n    target[eventMethod]('pointerdown', onDragStart, activeListener);\n    document[eventMethod]('pointermove', onDragMove, activeListener);\n    document[eventMethod]('pointerup', onDragEnd, passiveListener);\n  }\n  function enableDraggable() {\n    if (!swiper.params.scrollbar.el || !swiper.scrollbar.el) return;\n    events('on');\n  }\n  function disableDraggable() {\n    if (!swiper.params.scrollbar.el || !swiper.scrollbar.el) return;\n    events('off');\n  }\n  function init() {\n    const {\n      scrollbar,\n      el: swiperEl\n    } = swiper;\n    swiper.params.scrollbar = createElementIfNotDefined(swiper, swiper.originalParams.scrollbar, swiper.params.scrollbar, {\n      el: 'swiper-scrollbar'\n    });\n    const params = swiper.params.scrollbar;\n    if (!params.el) return;\n    let el;\n    if (typeof params.el === 'string' && swiper.isElement) {\n      el = swiper.el.shadowRoot.querySelector(params.el);\n    }\n    if (!el && typeof params.el === 'string') {\n      el = document.querySelectorAll(params.el);\n    } else if (!el) {\n      el = params.el;\n    }\n    if (swiper.params.uniqueNavElements && typeof params.el === 'string' && el.length > 1 && swiperEl.querySelectorAll(params.el).length === 1) {\n      el = swiperEl.querySelector(params.el);\n    }\n    if (el.length > 0) el = el[0];\n    el.classList.add(swiper.isHorizontal() ? params.horizontalClass : params.verticalClass);\n    let dragEl;\n    if (el) {\n      dragEl = el.querySelector(`.${swiper.params.scrollbar.dragClass}`);\n      if (!dragEl) {\n        dragEl = createElement('div', swiper.params.scrollbar.dragClass);\n        el.append(dragEl);\n      }\n    }\n    Object.assign(scrollbar, {\n      el,\n      dragEl\n    });\n    if (params.draggable) {\n      enableDraggable();\n    }\n    if (el) {\n      el.classList[swiper.enabled ? 'remove' : 'add'](swiper.params.scrollbar.lockClass);\n    }\n  }\n  function destroy() {\n    const params = swiper.params.scrollbar;\n    const el = swiper.scrollbar.el;\n    if (el) {\n      el.classList.remove(swiper.isHorizontal() ? params.horizontalClass : params.verticalClass);\n    }\n    disableDraggable();\n  }\n  on('init', () => {\n    if (swiper.params.scrollbar.enabled === false) {\n      // eslint-disable-next-line\n      disable();\n    } else {\n      init();\n      updateSize();\n      setTranslate();\n    }\n  });\n  on('update resize observerUpdate lock unlock', () => {\n    updateSize();\n  });\n  on('setTranslate', () => {\n    setTranslate();\n  });\n  on('setTransition', (_s, duration) => {\n    setTransition(duration);\n  });\n  on('enable disable', () => {\n    const {\n      el\n    } = swiper.scrollbar;\n    if (el) {\n      el.classList[swiper.enabled ? 'remove' : 'add'](swiper.params.scrollbar.lockClass);\n    }\n  });\n  on('destroy', () => {\n    destroy();\n  });\n  const enable = () => {\n    swiper.el.classList.remove(swiper.params.scrollbar.scrollbarDisabledClass);\n    if (swiper.scrollbar.el) {\n      swiper.scrollbar.el.classList.remove(swiper.params.scrollbar.scrollbarDisabledClass);\n    }\n    init();\n    updateSize();\n    setTranslate();\n  };\n  const disable = () => {\n    swiper.el.classList.add(swiper.params.scrollbar.scrollbarDisabledClass);\n    if (swiper.scrollbar.el) {\n      swiper.scrollbar.el.classList.add(swiper.params.scrollbar.scrollbarDisabledClass);\n    }\n    destroy();\n  };\n  Object.assign(swiper.scrollbar, {\n    enable,\n    disable,\n    updateSize,\n    setTranslate,\n    init,\n    destroy\n  });\n}"], "mappings": "AAAA,SAASA,WAAW,QAAQ,YAAY;AACxC,SAASC,aAAa,EAAEC,aAAa,EAAEC,QAAQ,QAAQ,uBAAuB;AAC9E,OAAOC,yBAAyB,MAAM,+CAA+C;AACrF,eAAe,SAASC,SAASA,CAAAC,IAAA,EAK9B;EAAA,IAL+B;IAChCC,MAAM;IACNC,YAAY;IACZC,EAAE;IACFC;EACF,CAAC,GAAAJ,IAAA;EACC,MAAMK,QAAQ,GAAGX,WAAW,EAAE;EAC9B,IAAIY,SAAS,GAAG,KAAK;EACrB,IAAIC,OAAO,GAAG,IAAI;EAClB,IAAIC,WAAW,GAAG,IAAI;EACtB,IAAIC,YAAY;EAChB,IAAIC,QAAQ;EACZ,IAAIC,SAAS;EACb,IAAIC,OAAO;EACXV,YAAY,CAAC;IACXW,SAAS,EAAE;MACTC,EAAE,EAAE,IAAI;MACRJ,QAAQ,EAAE,MAAM;MAChBK,IAAI,EAAE,KAAK;MACXC,SAAS,EAAE,KAAK;MAChBC,aAAa,EAAE,IAAI;MACnBC,SAAS,EAAE,uBAAuB;MAClCC,SAAS,EAAE,uBAAuB;MAClCC,sBAAsB,EAAE,2BAA2B;MACnDC,eAAe,EAAG,6BAA4B;MAC9CC,aAAa,EAAG;IAClB;EACF,CAAC,CAAC;EACFrB,MAAM,CAACY,SAAS,GAAG;IACjBC,EAAE,EAAE,IAAI;IACRS,MAAM,EAAE;EACV,CAAC;EACD,SAASC,YAAYA,CAAA,EAAG;IACtB,IAAI,CAACvB,MAAM,CAACwB,MAAM,CAACZ,SAAS,CAACC,EAAE,IAAI,CAACb,MAAM,CAACY,SAAS,CAACC,EAAE,EAAE;IACzD,MAAM;MACJD,SAAS;MACTa,YAAY,EAAEC;IAChB,CAAC,GAAG1B,MAAM;IACV,MAAM;MACJsB,MAAM;MACNT;IACF,CAAC,GAAGD,SAAS;IACb,MAAMY,MAAM,GAAGxB,MAAM,CAACwB,MAAM,CAACZ,SAAS;IACtC,MAAMe,QAAQ,GAAG3B,MAAM,CAACwB,MAAM,CAACI,IAAI,GAAG5B,MAAM,CAAC6B,YAAY,GAAG7B,MAAM,CAAC2B,QAAQ;IAC3E,IAAIG,OAAO,GAAGrB,QAAQ;IACtB,IAAIsB,MAAM,GAAG,CAACrB,SAAS,GAAGD,QAAQ,IAAIkB,QAAQ;IAC9C,IAAID,GAAG,EAAE;MACPK,MAAM,GAAG,CAACA,MAAM;MAChB,IAAIA,MAAM,GAAG,CAAC,EAAE;QACdD,OAAO,GAAGrB,QAAQ,GAAGsB,MAAM;QAC3BA,MAAM,GAAG,CAAC;MACZ,CAAC,MAAM,IAAI,CAACA,MAAM,GAAGtB,QAAQ,GAAGC,SAAS,EAAE;QACzCoB,OAAO,GAAGpB,SAAS,GAAGqB,MAAM;MAC9B;IACF,CAAC,MAAM,IAAIA,MAAM,GAAG,CAAC,EAAE;MACrBD,OAAO,GAAGrB,QAAQ,GAAGsB,MAAM;MAC3BA,MAAM,GAAG,CAAC;IACZ,CAAC,MAAM,IAAIA,MAAM,GAAGtB,QAAQ,GAAGC,SAAS,EAAE;MACxCoB,OAAO,GAAGpB,SAAS,GAAGqB,MAAM;IAC9B;IACA,IAAI/B,MAAM,CAACgC,YAAY,EAAE,EAAE;MACzBV,MAAM,CAACW,KAAK,CAACC,SAAS,GAAI,eAAcH,MAAO,WAAU;MACzDT,MAAM,CAACW,KAAK,CAACE,KAAK,GAAI,GAAEL,OAAQ,IAAG;IACrC,CAAC,MAAM;MACLR,MAAM,CAACW,KAAK,CAACC,SAAS,GAAI,oBAAmBH,MAAO,QAAO;MAC3DT,MAAM,CAACW,KAAK,CAACG,MAAM,GAAI,GAAEN,OAAQ,IAAG;IACtC;IACA,IAAIN,MAAM,CAACV,IAAI,EAAE;MACfuB,YAAY,CAAC/B,OAAO,CAAC;MACrBO,EAAE,CAACoB,KAAK,CAACK,OAAO,GAAG,CAAC;MACpBhC,OAAO,GAAGiC,UAAU,CAAC,MAAM;QACzB1B,EAAE,CAACoB,KAAK,CAACK,OAAO,GAAG,CAAC;QACpBzB,EAAE,CAACoB,KAAK,CAACO,kBAAkB,GAAG,OAAO;MACvC,CAAC,EAAE,IAAI,CAAC;IACV;EACF;EACA,SAASC,aAAaA,CAACC,QAAQ,EAAE;IAC/B,IAAI,CAAC1C,MAAM,CAACwB,MAAM,CAACZ,SAAS,CAACC,EAAE,IAAI,CAACb,MAAM,CAACY,SAAS,CAACC,EAAE,EAAE;IACzDb,MAAM,CAACY,SAAS,CAACU,MAAM,CAACW,KAAK,CAACO,kBAAkB,GAAI,GAAEE,QAAS,IAAG;EACpE;EACA,SAASC,UAAUA,CAAA,EAAG;IACpB,IAAI,CAAC3C,MAAM,CAACwB,MAAM,CAACZ,SAAS,CAACC,EAAE,IAAI,CAACb,MAAM,CAACY,SAAS,CAACC,EAAE,EAAE;IACzD,MAAM;MACJD;IACF,CAAC,GAAGZ,MAAM;IACV,MAAM;MACJsB,MAAM;MACNT;IACF,CAAC,GAAGD,SAAS;IACbU,MAAM,CAACW,KAAK,CAACE,KAAK,GAAG,EAAE;IACvBb,MAAM,CAACW,KAAK,CAACG,MAAM,GAAG,EAAE;IACxB1B,SAAS,GAAGV,MAAM,CAACgC,YAAY,EAAE,GAAGnB,EAAE,CAAC+B,WAAW,GAAG/B,EAAE,CAACgC,YAAY;IACpElC,OAAO,GAAGX,MAAM,CAAC8C,IAAI,IAAI9C,MAAM,CAAC+C,WAAW,GAAG/C,MAAM,CAACwB,MAAM,CAACwB,kBAAkB,IAAIhD,MAAM,CAACwB,MAAM,CAACyB,cAAc,GAAGjD,MAAM,CAACkD,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IACzI,IAAIlD,MAAM,CAACwB,MAAM,CAACZ,SAAS,CAACH,QAAQ,KAAK,MAAM,EAAE;MAC/CA,QAAQ,GAAGC,SAAS,GAAGC,OAAO;IAChC,CAAC,MAAM;MACLF,QAAQ,GAAG0C,QAAQ,CAACnD,MAAM,CAACwB,MAAM,CAACZ,SAAS,CAACH,QAAQ,EAAE,EAAE,CAAC;IAC3D;IACA,IAAIT,MAAM,CAACgC,YAAY,EAAE,EAAE;MACzBV,MAAM,CAACW,KAAK,CAACE,KAAK,GAAI,GAAE1B,QAAS,IAAG;IACtC,CAAC,MAAM;MACLa,MAAM,CAACW,KAAK,CAACG,MAAM,GAAI,GAAE3B,QAAS,IAAG;IACvC;IACA,IAAIE,OAAO,IAAI,CAAC,EAAE;MAChBE,EAAE,CAACoB,KAAK,CAACmB,OAAO,GAAG,MAAM;IAC3B,CAAC,MAAM;MACLvC,EAAE,CAACoB,KAAK,CAACmB,OAAO,GAAG,EAAE;IACvB;IACA,IAAIpD,MAAM,CAACwB,MAAM,CAACZ,SAAS,CAACE,IAAI,EAAE;MAChCD,EAAE,CAACoB,KAAK,CAACK,OAAO,GAAG,CAAC;IACtB;IACA,IAAItC,MAAM,CAACwB,MAAM,CAAC6B,aAAa,IAAIrD,MAAM,CAACsD,OAAO,EAAE;MACjD1C,SAAS,CAACC,EAAE,CAAC0C,SAAS,CAACvD,MAAM,CAACwD,QAAQ,GAAG,KAAK,GAAG,QAAQ,CAAC,CAACxD,MAAM,CAACwB,MAAM,CAACZ,SAAS,CAACK,SAAS,CAAC;IAC/F;EACF;EACA,SAASwC,kBAAkBA,CAACC,CAAC,EAAE;IAC7B,OAAO1D,MAAM,CAACgC,YAAY,EAAE,GAAG0B,CAAC,CAACC,OAAO,GAAGD,CAAC,CAACE,OAAO;EACtD;EACA,SAASC,eAAeA,CAACH,CAAC,EAAE;IAC1B,MAAM;MACJ9C,SAAS;MACTa,YAAY,EAAEC;IAChB,CAAC,GAAG1B,MAAM;IACV,MAAM;MACJa;IACF,CAAC,GAAGD,SAAS;IACb,IAAIkD,aAAa;IACjBA,aAAa,GAAG,CAACL,kBAAkB,CAACC,CAAC,CAAC,GAAG/D,aAAa,CAACkB,EAAE,CAAC,CAACb,MAAM,CAACgC,YAAY,EAAE,GAAG,MAAM,GAAG,KAAK,CAAC,IAAIxB,YAAY,KAAK,IAAI,GAAGA,YAAY,GAAGC,QAAQ,GAAG,CAAC,CAAC,KAAKC,SAAS,GAAGD,QAAQ,CAAC;IACpLqD,aAAa,GAAGC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACH,aAAa,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACvD,IAAIpC,GAAG,EAAE;MACPoC,aAAa,GAAG,CAAC,GAAGA,aAAa;IACnC;IACA,MAAMI,QAAQ,GAAGlE,MAAM,CAACmE,YAAY,EAAE,GAAG,CAACnE,MAAM,CAACoE,YAAY,EAAE,GAAGpE,MAAM,CAACmE,YAAY,EAAE,IAAIL,aAAa;IACxG9D,MAAM,CAACqE,cAAc,CAACH,QAAQ,CAAC;IAC/BlE,MAAM,CAACuB,YAAY,CAAC2C,QAAQ,CAAC;IAC7BlE,MAAM,CAACsE,iBAAiB,EAAE;IAC1BtE,MAAM,CAACuE,mBAAmB,EAAE;EAC9B;EACA,SAASC,WAAWA,CAACd,CAAC,EAAE;IACtB,MAAMlC,MAAM,GAAGxB,MAAM,CAACwB,MAAM,CAACZ,SAAS;IACtC,MAAM;MACJA,SAAS;MACT6D;IACF,CAAC,GAAGzE,MAAM;IACV,MAAM;MACJa,EAAE;MACFS;IACF,CAAC,GAAGV,SAAS;IACbP,SAAS,GAAG,IAAI;IAChBG,YAAY,GAAGkD,CAAC,CAACgB,MAAM,KAAKpD,MAAM,GAAGmC,kBAAkB,CAACC,CAAC,CAAC,GAAGA,CAAC,CAACgB,MAAM,CAACC,qBAAqB,EAAE,CAAC3E,MAAM,CAACgC,YAAY,EAAE,GAAG,MAAM,GAAG,KAAK,CAAC,GAAG,IAAI;IAC5I0B,CAAC,CAACkB,cAAc,EAAE;IAClBlB,CAAC,CAACmB,eAAe,EAAE;IACnBJ,SAAS,CAACxC,KAAK,CAACO,kBAAkB,GAAG,OAAO;IAC5ClB,MAAM,CAACW,KAAK,CAACO,kBAAkB,GAAG,OAAO;IACzCqB,eAAe,CAACH,CAAC,CAAC;IAClBrB,YAAY,CAAC9B,WAAW,CAAC;IACzBM,EAAE,CAACoB,KAAK,CAACO,kBAAkB,GAAG,KAAK;IACnC,IAAIhB,MAAM,CAACV,IAAI,EAAE;MACfD,EAAE,CAACoB,KAAK,CAACK,OAAO,GAAG,CAAC;IACtB;IACA,IAAItC,MAAM,CAACwB,MAAM,CAACsD,OAAO,EAAE;MACzB9E,MAAM,CAACyE,SAAS,CAACxC,KAAK,CAAC,kBAAkB,CAAC,GAAG,MAAM;IACrD;IACA9B,IAAI,CAAC,oBAAoB,EAAEuD,CAAC,CAAC;EAC/B;EACA,SAASqB,UAAUA,CAACrB,CAAC,EAAE;IACrB,MAAM;MACJ9C,SAAS;MACT6D;IACF,CAAC,GAAGzE,MAAM;IACV,MAAM;MACJa,EAAE;MACFS;IACF,CAAC,GAAGV,SAAS;IACb,IAAI,CAACP,SAAS,EAAE;IAChB,IAAIqD,CAAC,CAACkB,cAAc,EAAElB,CAAC,CAACkB,cAAc,EAAE,CAAC,KAAKlB,CAAC,CAACsB,WAAW,GAAG,KAAK;IACnEnB,eAAe,CAACH,CAAC,CAAC;IAClBe,SAAS,CAACxC,KAAK,CAACO,kBAAkB,GAAG,KAAK;IAC1C3B,EAAE,CAACoB,KAAK,CAACO,kBAAkB,GAAG,KAAK;IACnClB,MAAM,CAACW,KAAK,CAACO,kBAAkB,GAAG,KAAK;IACvCrC,IAAI,CAAC,mBAAmB,EAAEuD,CAAC,CAAC;EAC9B;EACA,SAASuB,SAASA,CAACvB,CAAC,EAAE;IACpB,MAAMlC,MAAM,GAAGxB,MAAM,CAACwB,MAAM,CAACZ,SAAS;IACtC,MAAM;MACJA,SAAS;MACT6D;IACF,CAAC,GAAGzE,MAAM;IACV,MAAM;MACJa;IACF,CAAC,GAAGD,SAAS;IACb,IAAI,CAACP,SAAS,EAAE;IAChBA,SAAS,GAAG,KAAK;IACjB,IAAIL,MAAM,CAACwB,MAAM,CAACsD,OAAO,EAAE;MACzB9E,MAAM,CAACyE,SAAS,CAACxC,KAAK,CAAC,kBAAkB,CAAC,GAAG,EAAE;MAC/CwC,SAAS,CAACxC,KAAK,CAACO,kBAAkB,GAAG,EAAE;IACzC;IACA,IAAIhB,MAAM,CAACV,IAAI,EAAE;MACfuB,YAAY,CAAC9B,WAAW,CAAC;MACzBA,WAAW,GAAGX,QAAQ,CAAC,MAAM;QAC3BiB,EAAE,CAACoB,KAAK,CAACK,OAAO,GAAG,CAAC;QACpBzB,EAAE,CAACoB,KAAK,CAACO,kBAAkB,GAAG,OAAO;MACvC,CAAC,EAAE,IAAI,CAAC;IACV;IACArC,IAAI,CAAC,kBAAkB,EAAEuD,CAAC,CAAC;IAC3B,IAAIlC,MAAM,CAACR,aAAa,EAAE;MACxBhB,MAAM,CAACkF,cAAc,EAAE;IACzB;EACF;EACA,SAASC,MAAMA,CAACC,MAAM,EAAE;IACtB,MAAM;MACJxE,SAAS;MACTY;IACF,CAAC,GAAGxB,MAAM;IACV,MAAMa,EAAE,GAAGD,SAAS,CAACC,EAAE;IACvB,IAAI,CAACA,EAAE,EAAE;IACT,MAAM6D,MAAM,GAAG7D,EAAE;IACjB,MAAMwE,cAAc,GAAG7D,MAAM,CAAC8D,gBAAgB,GAAG;MAC/CC,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE;IACX,CAAC,GAAG,KAAK;IACT,MAAMC,eAAe,GAAGjE,MAAM,CAAC8D,gBAAgB,GAAG;MAChDC,OAAO,EAAE,IAAI;MACbC,OAAO,EAAE;IACX,CAAC,GAAG,KAAK;IACT,IAAI,CAACd,MAAM,EAAE;IACb,MAAMgB,WAAW,GAAGN,MAAM,KAAK,IAAI,GAAG,kBAAkB,GAAG,qBAAqB;IAChFV,MAAM,CAACgB,WAAW,CAAC,CAAC,aAAa,EAAElB,WAAW,EAAEa,cAAc,CAAC;IAC/DjF,QAAQ,CAACsF,WAAW,CAAC,CAAC,aAAa,EAAEX,UAAU,EAAEM,cAAc,CAAC;IAChEjF,QAAQ,CAACsF,WAAW,CAAC,CAAC,WAAW,EAAET,SAAS,EAAEQ,eAAe,CAAC;EAChE;EACA,SAASE,eAAeA,CAAA,EAAG;IACzB,IAAI,CAAC3F,MAAM,CAACwB,MAAM,CAACZ,SAAS,CAACC,EAAE,IAAI,CAACb,MAAM,CAACY,SAAS,CAACC,EAAE,EAAE;IACzDsE,MAAM,CAAC,IAAI,CAAC;EACd;EACA,SAASS,gBAAgBA,CAAA,EAAG;IAC1B,IAAI,CAAC5F,MAAM,CAACwB,MAAM,CAACZ,SAAS,CAACC,EAAE,IAAI,CAACb,MAAM,CAACY,SAAS,CAACC,EAAE,EAAE;IACzDsE,MAAM,CAAC,KAAK,CAAC;EACf;EACA,SAASU,IAAIA,CAAA,EAAG;IACd,MAAM;MACJjF,SAAS;MACTC,EAAE,EAAEiF;IACN,CAAC,GAAG9F,MAAM;IACVA,MAAM,CAACwB,MAAM,CAACZ,SAAS,GAAGf,yBAAyB,CAACG,MAAM,EAAEA,MAAM,CAAC+F,cAAc,CAACnF,SAAS,EAAEZ,MAAM,CAACwB,MAAM,CAACZ,SAAS,EAAE;MACpHC,EAAE,EAAE;IACN,CAAC,CAAC;IACF,MAAMW,MAAM,GAAGxB,MAAM,CAACwB,MAAM,CAACZ,SAAS;IACtC,IAAI,CAACY,MAAM,CAACX,EAAE,EAAE;IAChB,IAAIA,EAAE;IACN,IAAI,OAAOW,MAAM,CAACX,EAAE,KAAK,QAAQ,IAAIb,MAAM,CAACgG,SAAS,EAAE;MACrDnF,EAAE,GAAGb,MAAM,CAACa,EAAE,CAACoF,UAAU,CAACC,aAAa,CAAC1E,MAAM,CAACX,EAAE,CAAC;IACpD;IACA,IAAI,CAACA,EAAE,IAAI,OAAOW,MAAM,CAACX,EAAE,KAAK,QAAQ,EAAE;MACxCA,EAAE,GAAGT,QAAQ,CAAC+F,gBAAgB,CAAC3E,MAAM,CAACX,EAAE,CAAC;IAC3C,CAAC,MAAM,IAAI,CAACA,EAAE,EAAE;MACdA,EAAE,GAAGW,MAAM,CAACX,EAAE;IAChB;IACA,IAAIb,MAAM,CAACwB,MAAM,CAAC4E,iBAAiB,IAAI,OAAO5E,MAAM,CAACX,EAAE,KAAK,QAAQ,IAAIA,EAAE,CAACwF,MAAM,GAAG,CAAC,IAAIP,QAAQ,CAACK,gBAAgB,CAAC3E,MAAM,CAACX,EAAE,CAAC,CAACwF,MAAM,KAAK,CAAC,EAAE;MAC1IxF,EAAE,GAAGiF,QAAQ,CAACI,aAAa,CAAC1E,MAAM,CAACX,EAAE,CAAC;IACxC;IACA,IAAIA,EAAE,CAACwF,MAAM,GAAG,CAAC,EAAExF,EAAE,GAAGA,EAAE,CAAC,CAAC,CAAC;IAC7BA,EAAE,CAAC0C,SAAS,CAAC+C,GAAG,CAACtG,MAAM,CAACgC,YAAY,EAAE,GAAGR,MAAM,CAACJ,eAAe,GAAGI,MAAM,CAACH,aAAa,CAAC;IACvF,IAAIC,MAAM;IACV,IAAIT,EAAE,EAAE;MACNS,MAAM,GAAGT,EAAE,CAACqF,aAAa,CAAE,IAAGlG,MAAM,CAACwB,MAAM,CAACZ,SAAS,CAACM,SAAU,EAAC,CAAC;MAClE,IAAI,CAACI,MAAM,EAAE;QACXA,MAAM,GAAG5B,aAAa,CAAC,KAAK,EAAEM,MAAM,CAACwB,MAAM,CAACZ,SAAS,CAACM,SAAS,CAAC;QAChEL,EAAE,CAAC0F,MAAM,CAACjF,MAAM,CAAC;MACnB;IACF;IACAkF,MAAM,CAACC,MAAM,CAAC7F,SAAS,EAAE;MACvBC,EAAE;MACFS;IACF,CAAC,CAAC;IACF,IAAIE,MAAM,CAACT,SAAS,EAAE;MACpB4E,eAAe,EAAE;IACnB;IACA,IAAI9E,EAAE,EAAE;MACNA,EAAE,CAAC0C,SAAS,CAACvD,MAAM,CAACsD,OAAO,GAAG,QAAQ,GAAG,KAAK,CAAC,CAACtD,MAAM,CAACwB,MAAM,CAACZ,SAAS,CAACK,SAAS,CAAC;IACpF;EACF;EACA,SAASyF,OAAOA,CAAA,EAAG;IACjB,MAAMlF,MAAM,GAAGxB,MAAM,CAACwB,MAAM,CAACZ,SAAS;IACtC,MAAMC,EAAE,GAAGb,MAAM,CAACY,SAAS,CAACC,EAAE;IAC9B,IAAIA,EAAE,EAAE;MACNA,EAAE,CAAC0C,SAAS,CAACoD,MAAM,CAAC3G,MAAM,CAACgC,YAAY,EAAE,GAAGR,MAAM,CAACJ,eAAe,GAAGI,MAAM,CAACH,aAAa,CAAC;IAC5F;IACAuE,gBAAgB,EAAE;EACpB;EACA1F,EAAE,CAAC,MAAM,EAAE,MAAM;IACf,IAAIF,MAAM,CAACwB,MAAM,CAACZ,SAAS,CAAC0C,OAAO,KAAK,KAAK,EAAE;MAC7C;MACAsD,OAAO,EAAE;IACX,CAAC,MAAM;MACLf,IAAI,EAAE;MACNlD,UAAU,EAAE;MACZpB,YAAY,EAAE;IAChB;EACF,CAAC,CAAC;EACFrB,EAAE,CAAC,0CAA0C,EAAE,MAAM;IACnDyC,UAAU,EAAE;EACd,CAAC,CAAC;EACFzC,EAAE,CAAC,cAAc,EAAE,MAAM;IACvBqB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFrB,EAAE,CAAC,eAAe,EAAE,CAAC2G,EAAE,EAAEnE,QAAQ,KAAK;IACpCD,aAAa,CAACC,QAAQ,CAAC;EACzB,CAAC,CAAC;EACFxC,EAAE,CAAC,gBAAgB,EAAE,MAAM;IACzB,MAAM;MACJW;IACF,CAAC,GAAGb,MAAM,CAACY,SAAS;IACpB,IAAIC,EAAE,EAAE;MACNA,EAAE,CAAC0C,SAAS,CAACvD,MAAM,CAACsD,OAAO,GAAG,QAAQ,GAAG,KAAK,CAAC,CAACtD,MAAM,CAACwB,MAAM,CAACZ,SAAS,CAACK,SAAS,CAAC;IACpF;EACF,CAAC,CAAC;EACFf,EAAE,CAAC,SAAS,EAAE,MAAM;IAClBwG,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAMI,MAAM,GAAGA,CAAA,KAAM;IACnB9G,MAAM,CAACa,EAAE,CAAC0C,SAAS,CAACoD,MAAM,CAAC3G,MAAM,CAACwB,MAAM,CAACZ,SAAS,CAACO,sBAAsB,CAAC;IAC1E,IAAInB,MAAM,CAACY,SAAS,CAACC,EAAE,EAAE;MACvBb,MAAM,CAACY,SAAS,CAACC,EAAE,CAAC0C,SAAS,CAACoD,MAAM,CAAC3G,MAAM,CAACwB,MAAM,CAACZ,SAAS,CAACO,sBAAsB,CAAC;IACtF;IACA0E,IAAI,EAAE;IACNlD,UAAU,EAAE;IACZpB,YAAY,EAAE;EAChB,CAAC;EACD,MAAMqF,OAAO,GAAGA,CAAA,KAAM;IACpB5G,MAAM,CAACa,EAAE,CAAC0C,SAAS,CAAC+C,GAAG,CAACtG,MAAM,CAACwB,MAAM,CAACZ,SAAS,CAACO,sBAAsB,CAAC;IACvE,IAAInB,MAAM,CAACY,SAAS,CAACC,EAAE,EAAE;MACvBb,MAAM,CAACY,SAAS,CAACC,EAAE,CAAC0C,SAAS,CAAC+C,GAAG,CAACtG,MAAM,CAACwB,MAAM,CAACZ,SAAS,CAACO,sBAAsB,CAAC;IACnF;IACAuF,OAAO,EAAE;EACX,CAAC;EACDF,MAAM,CAACC,MAAM,CAACzG,MAAM,CAACY,SAAS,EAAE;IAC9BkG,MAAM;IACNF,OAAO;IACPjE,UAAU;IACVpB,YAAY;IACZsE,IAAI;IACJa;EACF,CAAC,CAAC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}